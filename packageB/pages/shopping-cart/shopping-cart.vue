<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar title="购物车">
			<view v-if="!loading" class="d-flex a-center ml-10 w-s-now font-30 text-3" @click="onSwitch">{{isShowEdit ? '编辑' : '完成'}}</view>
		</vh-navbar>
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in">
			<!-- 购物车列表 -->
			<view class="">
				<!-- 活动商品列表 -->
				<view v-if="activityList.length" class="pt-20" v-for="(outItem, outIndex) in activityList" :key="outIndex">
					<view class="list-con bg-ffffff ml-24 mr-24 ptb-40-plr-24 b-rad-10">
						<view class="d-flex j-sb a-center mb-36">
							<view class="d-flex a-center">
								<vh-check :checked="isSelectAllGoods('activity', outIndex)" @click="selectAll('activity', outIndex)" />
								<text class="ml-24 font-32 font-wei text-3">{{outItem.activity_name}}</text>
							</view>
						</view>
						
						<!-- 满减 -->
						<view v-if="outItem.reduction.length" class="d-flex j-sb a-center mb-36">
							<view class="ml-56 d-flex a-center">
								<text class="bg-fce4e3 b-rad-04 ptb-02-plr-12 font-24 text-e80404">满减</text>
								<!-- 活动有选中商品 -->
								<view v-if="outItem.select_money" class="w-400 ml-12 font-26 text-3">{{outItem.reduction_tips.replaceAll('.00', '')}}</view>
								<!-- 初始化显示 -->
								<view v-else class="w-400 ml-12 font-26 text-3">满{{`${outItem.reduction[0].fill.toFixed(2)}`.replace('.00', '')}}减{{`${outItem.reduction[0].decrease.toFixed(2)}`.replace('.00','')}}元</view>
							</view>
							
							<view class="d-flex a-center" @click="jump.h5Jump(outItem.activity_url, '', 1)">
								<text class="mr-04 font-24 text-3">去凑单</text>
								<u-icon name="arrow-right" :size="20" color="#333"></u-icon>
							</view>
						</view>
						
						<!-- 满赠 -->
						<view v-if="outItem.fullgift.length" class="d-flex j-sb a-center mb-36">
							<view class="ml-56 d-flex a-center">
								<text class="bg-fce4e3 b-rad-04 ptb-02-plr-12 font-24 text-e80404">满赠</text>
								<!-- 活动有选中商品 -->
								<view v-if="outItem.select_money" class="w-400 ml-12 font-26 text-3">{{outItem.fullgift_tips.replaceAll('.00', '')}}</view>
								<!-- 初始化显示 -->
								<view v-else class="w-400 ml-12 font-26 text-3">满{{`${outItem.fullgift[0].fill.toFixed(2)}`.replace('.00', '')}}赠{{`${outItem.fullgift[0].goods_name}`.replace('.00', '')}}</view>
							</view>
							
							<view class="d-flex a-center" @click="jump.h5Jump(outItem.activity_url, '', 1)">
								<text class="mr-04 font-24 text-3">去凑单</text>
								<u-icon name="arrow-right" :size="20" color="#333"></u-icon>
							</view>
						</view>
						
						<view class="list-item d-flex j-sb a-center mt-20" v-for="(item, index) in outItem.goods_info" :key="index">
							<vh-check :checked="activitySelectedList[outIndex].indexOf(item.id) > -1" @click="selectSingle(item, 'activity', outIndex)" />
							<view class="p-rela w-230 h-144 b-rad-08 o-hid ml-24" @click="handleGoodsJump(item.period)">
								<vh-image :loading-type="2" :src="item.banner_img" :width="230" :height="144" />
								<!-- <view v-if="item.is_support_reduction == 0" class="p-abso bottom-0 z-04 w-230 h-30 bg-000-000-000-035 d-flex j-center a-center font-18 text-ffffff">该商品不支持满减</view> -->
							</view>
				
							<view class="ml-16 flex-1" @click="handleGoodsJump(item.period)">
								<view class="d-flex a-center text-hidden-1 o-hid">
									<vh-channel-title-icon :channel="item.periods_type" :border-radius="4" padding="2rpx 6rpx" :font-size="18"/>
									<text class="ml-04 font-24 text-0 l-h-34">{{item.title}}</text>
								</view>
								<view class="mt-10">
									<text class="bg-eeeeee b-rad-06 mt-12 ptb-02-plr-12 font-20 text-9 l-h-28">{{item.package_name}}</text>
								</view>
								<view class="mt-28 d-flex j-sb a-center">
									<text v-if="item.is_hidden_price || [3, 4].includes(item.onsale_status)" class="font-28 font-wei text-e80404">价格保密</text>
									<text v-else class="font-28 font-wei text-e80404"><text class="font-16">¥</text>{{item.price}}</text>
									<view @click.stop>
										<u-number-box :value="item.available_stock > item.nums ? item.nums : item.available_stock" :input-width="52" :min="1" :max="item.available_stock" :input-height="40" :size="28" @change="changeCartNumbers($event, item.id, 'activity', outIndex)" />
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 普通商品列表 -->
				<view v-if="normalList.length" class="pt-20">
					<view class="list-con bg-ffffff ml-24 mr-24 ptb-40-plr-24 b-rad-10">
						<view class="d-flex j-sb a-center mb-40">
							<view class="d-flex a-center">
								<vh-check :checked="isSelectAllGoods('normal')" @click="selectAll('normal')" />
								<text class="ml-24 font-32 font-wei text-3">酒云商品</text>
							</view>
						</view>
						<view class="list-item d-flex j-sb a-center mt-20" v-for="(item, index) in normalList" :key="index">
							<vh-check :checked="normalSelectedList.indexOf(item.id) > -1" @click="selectSingle(item, 'normal')" />
							<view class="w-230 b-rad-08 o-hid ml-24" @click="handleGoodsJump(item.period)">
								<vh-image :loading-type="2" :src="item.banner_img" :height="144" />
							</view>
							<view class="ml-16 flex-1" @click="handleGoodsJump(item.period)">
								<view class="d-flex a-center text-hidden-1 o-hid">
									<vh-channel-title-icon :channel="item.periods_type" :border-radius="4" padding="2rpx 6rpx" :font-size="18"/>
									<text class="ml-04 font-24 text-0 l-h-34">{{item.title}}</text>
								</view>
								
								<view class="mt-10">
									<text class="bg-eeeeee b-rad-06 mt-12 ptb-02-plr-12 font-20 text-9 l-h-28">{{item.package_name}}</text>
								</view>
								<view class="mt-28 d-flex j-sb a-center">
									<text v-if="item.is_hidden_price || [3, 4].includes(item.onsale_status)" class="font-28 font-wei text-e80404">价格保密</text>
									<text v-else class="font-28 font-wei text-e80404"><text class="font-16">¥</text>{{item.price}}</text>
									<view @click.stop>
										<u-number-box :value="item.available_stock > item.nums ? item.nums : item.available_stock" :min="1" :max="item.available_stock" :input-width="52" :input-height="40" :size="28" @change="changeCartNumbers($event, item.id, 'normal')" />
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 失效商品列表 -->
				<view v-if="invalidList.length" class="pt-20">
					<view class="list-con bg-ffffff ml-24 mr-24 ptb-40-plr-24 b-rad-10">
						<view class="d-flex j-sb a-center mb-40">
							<view class="d-flex a-center">
								<image v-if="isShowEdit" class="w-32 h-32" src="https://images.vinehoo.com/vinehoomini/v3/shopping_cart/cir_dis.png" mode="aspectFill" />
								<vh-check v-else :checked="isSelectAllGoods('invalid')" @click="selectAll('invalid')" />
								<text class="ml-24 font-32 font-wei text-3">失效商品</text>
							</view>
						</view>
						
						<view class="list-item d-flex j-sb a-center mt-20" v-for="(item,index) in invalidList" :key="index">
							<image v-if="isShowEdit" class="w-32 h-32" src="https://images.vinehoo.com/vinehoomini/v3/shopping_cart/cir_dis.png" mode="aspectFill" />
							<vh-check v-else :checked="invalidSelectedList.indexOf(item.id) > -1" @click="selectSingle(item, 'invalid')" />
							<view class="w-230 op-040 b-rad-08 o-hid ml-24">
								<vh-image :loading-type="2" :src="item.banner_img" :height="144" />
							</view>
							<view class="ml-16 flex-1">
								<view class="font-24 text-6 l-h-34 o-hid text-hidden-1">{{item.title}}</view>
								<view class="mt-70 d-flex j-end a-center">
									<u-number-box :input-width="52" :input-height="40" :size="28" :disabled="true" />
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 购物车列表为空 -->
			<vh-empty v-if="activityList.length === 0 && normalList.length === 0 && invalidList.length === 0" :padding-top="40" :padding-bottom="100" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_cart.png" text="您的购物车空空如也" />
			
			<!-- 猜你喜欢列表 -->
			<view class="">
				<vh-split-line :padding-top="52" :padding-bottom="32" :margin-left="10" :margin-right="10" text="猜你喜欢" :font-bold="true" :font-size="36" text-color="#333333"
				:show-image="true" image-src="https://images.vinehoo.com/vinehoomini/v3/comm/guess_love.png" />
				<view :class="getBottomReductionAndFullGiftTipsList.length ? `pb-${ 124 + (getBottomReductionAndFullGiftTipsList.length * 76) }` : 'pb-124'">
					<vh-goods-recommend-list />
				</view>
			</view>
			
			<!-- 底部按钮 -->
			<view class="">
				<!-- （结算） -->
				<view v-show="isShowEdit" class="fade-in p-fixed bottom-0 z-999 w-p100">
					<view v-show="getBottomReductionAndFullGiftTipsList.length" class="">
						<view class="fade-in bg-fdfaea d-flex j-sb a-center ptb-18-plr-24" v-for="(item, index) in getBottomReductionAndFullGiftTipsList" :key="index">
							<view class="w-584 d-flex a-center">
								<view class="p-rela w-max-264 bg-f79101 d-flex j-center a-center b-rad-06 ptb-00-plr-12">
									<text class="font-22 font-wei text-ffffff text-hidden-2">{{item.name}}</text>
									<view class="p-abso right-n-12 w-20 h-20 b-rad-p50 bg-fdfaea"></view>
								</view>
								<view class="flex-1 ml-12 font-24 text-d87f19">
									<text class="text-hidden-2">{{item.information}}</text>
									<!-- <text class="font-wei">（差131元减20元(未对接)）</text> -->
								</view>
							</view>
							
							<view class="" @click="bottomGoAndCollectTheBill(item)">
								<text class="mr-08 font-24 font-wei text-d87f19">去凑单</text>
								<u-icon name="arrow-right" :size="20" color="#D87F19" />
							</view>
						</view>
					</view>
					
					<view class="h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-28 pr-24">
						<view class="d-flex a-center">
							<view class="d-flex a-center">
								<vh-check :checked="isSelectAllGoods('all')" @click="selectAll('all')"></vh-check>
								<text class="ml-08 font-32 text-3">全选</text>
							</view>
							
							<view class="ml-28">
								<view class="d-flex a-center">
									<text class="font-28 font-wei text-3">合计：</text>
									<text class="font-40 font-wei text-3"><text class="font-28">¥</text>{{settlementMoneyInfo.total_money ? settlementMoneyInfo.total_money : 0}}</text>
								</view>
								
								<view class="d-flex a-center font-20 text-e80404 l-h-28">优惠减:¥{{settlementMoneyInfo.money_off_value ? settlementMoneyInfo.money_off_value : 0}}</view>
							</view>
						</view>
						
						<view class="">
							<u-button :disabled="!canSubmit" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
							:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: !canSubmit ? '#FCE4E3' : '#E80404', border:'none'}" @click="settlement">结算{{ settlementQuantity }}</u-button>
						</view>
					</view>
				</view>
				
				<!--（删除） -->
				<view v-show="!isShowEdit" class="fade-in p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-32 pr-24">
					<view class="d-flex a-center">
						<vh-check :checked="isSelectAllGoods('all')" @click="selectAll('all')"></vh-check>
						<text class="ml-16 font-32 text-3">全选</text>
					</view>
					
					<view class="d-flex a-center">
						<view class="">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#999', backgroundColor: '#FFF', border:'2rpx solid #999'}"
							@click="deleteShoppingCartGoods(1)">清空失效商品</u-button>
						</view>
						<view class="ml-24">
							<u-button :disabled="!canSubmit" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
							:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#FFF', backgroundColor: !canSubmit ? '#FCE4E3' : '#E80404', border:'none'}"
							@click="deleteShoppingCartGoods(2)">删除</u-button>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 骨架屏 -->
		<vh-skeleton v-else :type="13" loading-color="#2E7BFF" />
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default{
	    name:"shopping-cart",
		
		data() {
			return {
				loading: true, //加载状态 true = 加载中、false = 结束加载
				isShowEdit: true,// 导航栏是否显示'编辑'
				activityList: [], //活动列表
				activitySelectedList: [], //选中id的闪购列表
				normalList: [], //普通商品列表
				normalSelectedList: [], //选中id的普通商品列表
				invalidList: [], //失效列表
				invalidSelectedList: [],
				bottomReductionAndFullGiftTipsList: [], //底部满减满赠tips（默认首次加载）
				settlementMoneyInfo: {}, //结算金额信息
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
			...mapState('shoppingCart', ['addShoppingCartStatus']),
			
			// 获取底部满减满赠tips列表
			getBottomReductionAndFullGiftTipsList() {
				if( this.settlementMoneyInfo?.full_discount?.length ) {
					return this.settlementMoneyInfo.full_discount
				}else if( this.bottomReductionAndFullGiftTipsList.length ) {
					return this.bottomReductionAndFullGiftTipsList
				}else{
					return []
				}
			},
			
			// 获取提交状态 0 = 闪购、1 = 秒发、4 = 闪购 + 秒发混合
			getSubmitType() {
				let status = 0
				if(this.activitySelectedList.length > 0 && this.normalSelectedList.length > 0){
					status = 4
				}else if(this.activitySelectedList.length > 0 && this.normalSelectedList.length == 0){
					status = 0
				}else if(this.normalSelectedList.length > 0 && this.activitySelectedList.length == 0){
					status = 1
				}
				return status
			},
			
			// 是否可以提交(结算、删除)
			canSubmit() {
				if( this.activitySelectedList.flat().length || this.normalSelectedList.length || this.invalidSelectedList.length ) {
					return true
				}
				return false
			},
			
			// 结算数量
			settlementQuantity({ activityList, normalList }) {
				const activityAllList = activityList.map( item => item.goods_info ).flat()
				const list = [...activityAllList, ...normalList].filter(item => item.checked)
				const sum = list.reduce((total, item) => total + (item.nums || 0), 0);
				return sum ? `（${sum}）` : ``
			}
		},
		
		onLoad() {
			this.system.setNavigationBarBlack()
			this.init()
			// 保证init执行一次
			this.modifyAddShoppingCartStatus(false)
		},

		onShow() {
			if (this.addShoppingCartStatus) {
				this.init()
			}
			this.modifyAddShoppingCartStatus(false)
		},
		
		methods: {
			// mapMutations辅助函数
			...mapMutations(['muOrderInfo']),
			...mapMutations('shoppingCart', ['modifyAddShoppingCartStatus']),
			
			// 初始化（获取购物车列表）
			async init() {
				this.settlementMoneyInfo = {}
				await Promise.all([ this.getShoppingCartList() ])
				// uni.stopPullDownRefresh() //停止下拉刷新
				this.loading = false
			},
			
			// 获取购物车列表
			async getShoppingCartList(){
				this.activitySelectedList = []
				this.normalSelectedList = []
				let res = await this.$u.api.shoppingCartList()
				let { activity = [], nomal = [], invalid = [], full_discount } = res?.data || {}
				activity.forEach( item => item.goods_info.forEach( item => item.checked = false ))
				nomal.forEach(item => item.checked = false )
				invalid.forEach(item => item.checked = false)
				this.activityList = activity //活动商品
				// this.activityList[0].reduction = [
                    // {fill: 80, decrease: 15},
                // ]
	
	            // this.activityList[0].fullgift = [
					// {
						  // "rule_id":1,
						  // "fill":1000,
						  // "goods_name":"[测试-尾货]【自然微橙 | 诠释风土与灵魂的纯粹】Intellego Wines Elementis (Chenin Blanc) 2020"
					// },
	            // ]
				this.normalList = nomal //普通商品
				this.invalidList = invalid //失效商品列表
				this.bottomReductionAndFullGiftTipsList = full_discount //底部满赠满减活动信息数组
				this.getActivityCount() // 获取活动数量
			},
			
			//  计算选中的总金额 type = 购物车类型（all = 所有、activity = 活动、normal = 酒云商品） , index = 活动的列表索引
			calculateSelectedTotalMoney( type, index ) {
				if( type == 'normal' ||  !this.activityList.length ) return
				this.activityList.map( (item, index) => { //先遍历活动列表数据
				    item.reduction_tips = '' //满减提示
					item.fullgift_tips = '' //满赠提示
				    item.select_money = 0 //先设置金额 = 0
					item.goods_info.map( v => { //再遍历活动列表下面的商品信息
						if( v.checked && v.is_support_reduction ) {
							item.select_money += v.nums * v.price
						}
					})
				})
				this.calculateReduction( type, index )
				this.calculateFullGift( type, index )
			},
			
			// 计算满减 计算选中的总金额 type = 购物车类型（all = 所有、activity = 活动、normal = 酒云商品） , index = 活动的列表索引
			calculateReduction( type, index ) {
				let { select_money, reduction_tips, reduction } = this.activityList[index]
				const reductionLen = reduction.length
				if( !reductionLen ) return
				if( reduction[0].stacking_numb !== undefined ) { //1.含有叠加次数
				    const { fill, decrease, stacking_numb } = reduction[0] //获取满减第一项
					if( reduction[0].stacking_numb > 0 ){ //叠加次数有限
						console.log('-----------------我是叠加次数有限满减')
						if( select_money >= stacking_numb * fill ) { // 第一种情况（列表选中的金额大于 满减条件金额 * 叠加次数 内容显示：已减xxx元）
							this.activityList[index].reduction_tips = `已减${(stacking_numb * decrease).toFixed(2)}元`
						}else{ //另外一种情况（列表选中的金额 < 满减条件金额 * 叠加次数 内容显示：已减xxx元（还差xxx再减xxx）
							if( select_money < fill ) { //第一种情况（列表选中的金额 < 满减条件金额）
								// this.activityList[index].reduction_tips = `已减0.00元（还差${(fill - select_money).toFixed(2)}再减${decrease.toFixed(2)}）`
								
								this.activityList[index].reduction_tips = `满${fill.toFixed(2)}减${decrease.toFixed(2)}元`
							}else{ //另一种情况（列表选中的金额 >= 满减条件金额）
								this.activityList[index].reduction_tips = `已减${ (Math.floor( select_money / fill) * decrease).toFixed(2) }元（还差${((Math.floor( select_money / fill ) + 1) * fill - select_money).toFixed(2)}再减${decrease.toFixed(2)}）`
							}
						}
					}else{ //叠加次数无限
						console.log('-----------------我是叠加次数无限满减')
					    this.activityList[index].reduction_tips = `已减${ (Math.floor( select_money / fill) * decrease).toFixed(2) }元（还差${((Math.floor( select_money / fill ) + 1) * fill - select_money).toFixed(2)}再减${decrease.toFixed(2)}）` 
					}
				}else{ // 3.阶梯满减次数
					 if( select_money < reduction[0].fill ) {  // 选中的金额比满减数组第一项都要小
						 console.log('----------------------我比数组第一项都要小')
						 const { fill, decrease } = reduction[0]
						 // this.activityList[index].reduction_tips = `还差${(fill - select_money).toFixed(2)}再减${decrease.toFixed(2)}`
						 
						 this.activityList[index].reduction_tips = `满${fill.toFixed(2)}减${decrease.toFixed(2)}元`
					 }else if(select_money >= reduction[reductionLen - 1].fill) { // 选中的金额比满减数组最后一项都要大
						 console.log('----------------------我比数组最后一项都要大')
						 const { fill, decrease } = reduction[reductionLen - 1]
						 this.activityList[index].reduction_tips = `已减${decrease.toFixed(2)}元`
					 }else{ // 选中的金额比满减数组第一项要大比最后一项要小
						let arrIndex = reduction.findIndex( item => item.fill > select_money )
						console.log('----------------------我比数组第一项要大比最后一项要小')
						console.log(arrIndex)
						const { fill, decrease } = reduction[arrIndex] //找到未到达的那一项
						const prevReduction = reduction[arrIndex - 1] //找到到达的那一项
						const { fill: prevFill, decrease: prevDecrease } = prevReduction
						// this.activityList[index].reduction_tips = `满${fill.toFixed(2)}减${decrease.toFixed(2)}（还差${(fill - prevFill).toFixed(2)}再减${(decrease - prevDecrease).toFixed(2)}）`
						
						this.activityList[index].reduction_tips = `已减${prevDecrease.toFixed(2)}元（还差${(fill - select_money).toFixed(2)}再减${(decrease - prevDecrease).toFixed(2)}）`
					 }
				 }
			},
			
			// 计算满赠 计算选中的总金额 type = 购物车类型（all = 所有、activity = 活动、normal = 酒云商品） , index = 活动的列表索引
			calculateFullGift( type, index ) {
				console.log('--------------------我是计算满赠')
				let { select_money, fullgift_tips, fullgift } = this.activityList[index]
				let fullgiftLength = fullgift.length
				if( !fullgiftLength ) return
				if( select_money < fullgift[0].fill ) { //选中的金额比满赠数组第一项都小
				    console.log('--------------------选中的金额比满赠第一项都小')
					let { fill, goods_name } = fullgift[0]
					this.activityList[index].fullgift_tips = `满${fill.toFixed(2)}赠${goods_name}`
				}else if (select_money >= fullgift[fullgiftLength - 1].fill ){ //选中的金额比满赠数组最后一项都大
				    console.log('--------------------选中的金额比满赠最后一项都大')
					let { fill, goods_name } = fullgift[fullgift.length - 1]
					this.activityList[index].fullgift_tips = `满${fill.toFixed(2)}赠${goods_name}`
				}else{ //选中的金额比满赠数组第一项要大比最后一项要小
					 console.log('--------------------选中的金额在第一项和最后一项之间')
					let arrIndex = fullgift.findIndex( item => item.fill > select_money )
					console.log(arrIndex)
					let { fill, goods_name } = fullgift[arrIndex]
					this.activityList[index].fullgift_tips = `满${fill.toFixed(2)}赠${goods_name}`
				}
				console.log('---------------------end')
			},
			
			// 重置选中的活动数
			getActivityCount() {
				this.activitySelectedList = []
				this.activityList.forEach(() => {
					this.activitySelectedList.push([])
				})
			},
			
			// 选中购物车商品金额计算
			async shoppingCartMoneyCalculate(){
				let activityCheckList = []
				this.activityList.forEach( v => { //选中的活动商品
					v.goods_info.forEach( k => { if(k.checked == true) activityCheckList.push(k) })
				})
				let normalCheckList = this.normalList.filter(v => { return v.checked == true }) //选中普通商品
				let allCheckedList = [...activityCheckList, ...normalCheckList] //所有的选中商品
				console.log(allCheckedList)
				
				if(allCheckedList.length > 0){
					let res = await this.$u.api.shoppingCartMoneyCalclute({items_info:JSON.stringify(allCheckedList)})
					this.settlementMoneyInfo = res.data
					// this.bottomReductionAndFullGiftTipsList = res.data.full_discount
				}else{
					this.settlementMoneyInfo = await {}
				}
			},
			
			// 判断是否全选所有商品 type = 商品类型、index = 操作的数组索引（双层数组需要传，单层数组不用传）
			isSelectAllGoods(type, index = 0){
				switch(type){
					case 'all': //全部商品
					let activityLength = 0 //活动商品长度
					let selectedActivityLength = 0 //默认选中的活动商品长度
					this.activityList.forEach( v => {
						v.goods_info.forEach( k => { activityLength ++ })
					})
					this.activitySelectedList.forEach( v => {
						v.forEach( k => { selectedActivityLength ++ })
					})
					if (!this.isShowEdit) {
						return (activityLength + this.normalList.length + this.invalidList.length === selectedActivityLength + this.normalSelectedList.length + this.invalidSelectedList.length) && (selectedActivityLength || this.normalSelectedList.length || this.invalidSelectedList.length)
					}
					return  (activityLength + this.normalList.length === selectedActivityLength + this.normalSelectedList.length) && (selectedActivityLength || this.normalSelectedList.length)
					case 'activity': //活动商品
					return this.activityList[index].goods_info.length === this.activitySelectedList[index].length
					
					case 'normal': //普通商品
					return this.normalList.length === this.normalSelectedList.length
					case 'invalid':
						return this.invalidList.length === this.invalidSelectedList.length
				}
			},
			
			// 全选/取消全选（活动商品、普通商品）type = 商品类型、index = 操作的数组索引（双层数组需要传，单层数组不用传）
			selectAll(type, index = 0){
				if( !this.normalList.length && !this.activityList.length && this.isShowEdit ) return
				console.log('全选/取消全选')
				console.log(type)
				console.log(index)
				switch(type){
					case 'all': //全部商品
					if(this.isSelectAllGoods('all')){
						// 活动商品取消全选
						this.activityList.forEach( v => {
							v.goods_info.forEach( k => { k.checked = false })
						})
						// 普通商品取消全选
						this.normalList.forEach( v => { v.checked = false })
						this.normalSelectedList = []
						if (!this.isShowEdit) {
							this.invalidList.forEach( v => { v.checked = false })
							this.invalidSelectedList = []
						}
						this.getActivityCount()
					}else{
						console.log('------------我是商品全选')
						// 活动商品全选
						this.getActivityCount()
						this.activityList.map( (item, index) => {
							item.goods_info.map( v => {
								v.checked = true
								this.activitySelectedList[index].push(v.id)
							})
						})
						
						//普通商品全选
						this.normalSelectedList = this.normalList.map( v => {
							v.checked = true
							return v.id
						})
						if (!this.isShowEdit) {
							this.invalidSelectedList = this.invalidList.map( v => {
								v.checked = true
								return v.id
							})
						}
					}
					break
					case 'activity': //活动商品
					if(this.isSelectAllGoods('activity', index)){
						console.log('---------------------我需要活动不全选')
						this.activityList[index].goods_info.forEach( v => { v.checked = false })
						this.activitySelectedList[index].splice(0, this.activitySelectedList[index].length);
					}else{
						console.log('---------------------我需要活动全选')
						let checkedList = this.activityList[index].goods_info.map( v => {
							v.checked = true
							return v.id
						})
						// this.getActivityCount()
						checkedList.forEach( v => { 
							if( this.activitySelectedList[index].indexOf(v) == -1 ) {
								this.activitySelectedList[index].push(v)
							}
						})
					}
					break
					case 'normal': //普通商品
					if(this.isSelectAllGoods('normal')){
						this.normalList.forEach( v => {
							v.checked = false
						})
						this.normalSelectedList = []
					}else{
						this.normalSelectedList = this.normalList.map( v => {
							v.checked = true
							return v.id
						})
					}
					break
					case 'invalid':
						if(this.isSelectAllGoods('invalid')){
							this.invalidList.forEach( v => {
								v.checked = false
							})
							this.invalidSelectedList = []
						}else{
							this.invalidSelectedList = this.invalidList.map( v => {
								v.checked = true
								return v.id
							})
						}
						break
				}
				this.calculateSelectedTotalMoney( type, index )
				this.shoppingCartMoneyCalculate()
			},
			
			// 选中单个/取消选中单个商品（活动商品、普通商品）item = 商品列表的每一项、type = 商品类型、index = 操作列表某一项（双层数组需要传，单层数组可以不传）
			selectSingle(item, type, index = 0){
				switch(type){
					case 'activity': //单个活动商品
					if(this.activitySelectedList[index].indexOf(item.id) > -1){
						this.activityList[index].goods_info.forEach( v => {
							if(v.id === item.id) v.checked = false
						})
						this.activitySelectedList[index].splice(this.activitySelectedList[index].indexOf(item.id), 1)
					}else{
						this.activityList[index].goods_info.forEach( v => {
							if(v.id === item.id) v.checked = true
						})
						this.activitySelectedList[index].push(item.id)
					}
					break
					case 'normal': //单个普通商品
					if(this.normalSelectedList.indexOf(item.id) > -1){
						this.normalList.forEach( v => {
							if(v.id === item.id) v.checked = false
						})
						this.normalSelectedList.splice(this.normalSelectedList.indexOf(item.id), 1)
					}else{
						this.normalList.forEach( v => {
							if(v.id === item.id) v.checked = true
						})
						this.normalSelectedList.push(item.id)
					}
					case 'invalid':
						if(this.invalidSelectedList.indexOf(item.id) > -1){
							this.invalidList.forEach( v => {
								if(v.id === item.id) v.checked = false
							})
							this.invalidSelectedList.splice(this.invalidSelectedList.indexOf(item.id), 1)
						}else{
							this.invalidList.forEach( v => {
								if(v.id === item.id) v.checked = true
							})
							this.invalidSelectedList.push(item.id)
						}
						break
				}
				this.calculateSelectedTotalMoney( type, index )
				this.shoppingCartMoneyCalculate()
			},
			
			// 购物车数量加减 e = 计步器值 、id = 购物车ID、type = 购物车类型、index = 操作列表某一项（双层数组需要传，单层数组可以不传）
			async changeCartNumbers( e, id, type, index = 0 ){
				if( e.value <= 0 ) return this.feedback.toast({ title: '商品数量必须大于0~'})
				try{
					console.log(id, e.value, type, index)
					await this.$u.api.changeShoppingCartNums({id, nums: e.value})
					switch(type) {
						case 'activity': //活动商品
						this.activityList[index].goods_info.forEach(v => { if (v.id == id) v.nums = e.value }) //活动数组
						this.calculateSelectedTotalMoney( type, index )
						break
						case 'normal': //普通商品（酒云商品）
						this.normalList.forEach(v => { if (v.id == id) v.nums = e.value }) //普通商品数组
						break
					}
					this.shoppingCartMoneyCalculate()
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 去凑单
			collectOrders() {
				switch(this.settlementMoneyInfo.gaps.type) {
					case 2:
					if (this.$app)
							wineYunJsBridge.openAppPage({
							client_path: { ios_path: 'goMain', android_path: 'goMain' },
							ad_path_param: [
								{
								ios_key: 'type',
								ios_val: '1',
								android_key: 'type',
								android_val: '1',
								},
							],
							})
						else this.jump.reLaunch(this.routeTable.pgFlashPurchase)
					 //跳转闪购
					break
					case 3:
					if (this.$app)
							wineYunJsBridge.openAppPage({
							client_path: { ios_path: 'goMain', android_path: 'goMain' },
							ad_path_param: [
								{
								ios_key: 'type',
								ios_val: '3',
								android_key: 'type',
								android_val: '3',
								},
							],
							})
						else this.jump.reLaunch(this.routeTable.pgMiaoFa)
					break
					default:
					 //首页
					 if (this.$app) wineYunJsBridge.openAppPage({ client_path: { ios_path: 'goMain', android_path: 'goMain' } })
          			else this.jump.reLaunch(this.$routeTable.pgIndex)//不确定的都跳转首页
				}
			},
			
			// 结算
			settlement(){
				let activityCheckList = []
				this.activityList.forEach( v => { //选中的活动商品
					v.goods_info.forEach( k => { if(k.checked == true) activityCheckList.push(k) })
				})
				let normalCheckList = this.normalList.filter(v => { return v.checked == true }) //选中普通商品
				let allCheckedList = [...activityCheckList, ...normalCheckList] //所有的选中商品
				console.log(allCheckedList)
				if(allCheckedList.length > 0) {
					let hasFlashGoods = allCheckedList.some( v => { return v.periods_type == 0 }) //是否含有闪购商品
					let hasSecondGoods = allCheckedList.some( v => { return v.periods_type == 1 }) //是否含有秒发商品
					let submitType = hasFlashGoods && !hasSecondGoods ? 0 : !hasFlashGoods && hasSecondGoods ? 1 : 4 //提交类型 0 = 闪购、1 = 秒发、4 = 闪购 + 秒发混合
					let orderGoodsInfo = { 
						is_cart: 1, 
						submit_type: submitType, 
						coupons_id: 0, 
						// is_group: 0, 
						special_type : 0, //特殊类型：0-普通 1-拼团 2-新人
						orderGoodsList: [],
					} //订单商品信息
					allCheckedList.forEach(v => {
						let item = {} //订单商品信息
						item.periods_type = v.periods_type // 订单提交类型：0-闪购 1-秒发 2-跨境 3-尾货...
						item.is_cart = 1 //是否来自购物车
						item.period = v.period //商品期数
						item.banner_img = v.banner_img //商品banner
						item.title = v.title //商品标题
						item.package_name = v.package_name //套餐名
						item.price = v.price //商品价格 
						item.package_id = v.package_id //套餐id
						item.nums = v.nums //购买数量
						item.express_type = 0 //express_type快递方式 1-中通 2-顺丰快递 3-顺丰冷链 5-京东快递
						item.predict_time = v.predict_shipment_time //预计发货时间
						item.goods_is_ts = v.goods_is_ts //是否支持暂存
						item.is_checked_ts = false //是否勾选暂存 false否、true是
						item.is_ts = 0 //是否勾选暂存：0否 1是
						item.is_cold_chain = v.is_cold_chain //是否冷链
						item.$is_checked_cold_chain = false //是否勾选冷链 false否、true是
						item.is_original_package = v.is_original_package === 1 ? v.is_original_package : 2 //是否原箱：0 = 不是原箱、1 = 是原箱发货（注意：这里的是否原箱是对于商品而言，订单则是否原箱：1-是 2-否）
						console.log(item)
						orderGoodsInfo.orderGoodsList.push(item)
					})
					this.muOrderInfo(orderGoodsInfo)
					uni.setStorageSync('nextorderGoodsInfo',orderGoodsInfo);
					this.jump.appAndMiniJump(0, this.routeTable.pBOrderConfirm, this.$vhFrom)
					// this.jump.navigateTo(this.routeTable.pBOrderConfirm)
				}else{
					this.feedback.toast({title:'请选择商品', icon:'error'})
				}
			},
		    
			// 删除购物车选中商品 type = 删除类型、1 = 清空失效商品、2 = 删除正常商品
			deleteShoppingCartGoods(type){
				let idArr = []
				type == 1 ? idArr = this.invalidList.map( v => { return v.id } ) : idArr = [...this.activitySelectedList, ...this.normalSelectedList, ...this.invalidSelectedList]
				console.log(idArr)
				if(idArr.length > 0){
					this.feedback.showModal({
						content: type === 1 ? '是否清空失效商品' : '删除之后数据无法恢复，确认删除吗？',
						confirm: async () => {
							try{
								await this.$u.api.shoppingCatDelete({ ids:idArr.join(',') })
								this.feedback.toast({ title: '删除成功', icon: 'success' })
								this.getShoppingCartList() //购物车列表
								this.settlementMoneyInfo = {} //重置
							}catch(e){
								this.feedback.toast({title: '删除失败，请重试！'})
							}
						}
					})
				}else{
					let tips = ''
					type == 1 ? tips = '暂无失效商品' : tips = '请选择删除项'
					this.feedback.toast({title: tips, icon: 'error'})
				}
			},
		    
			// 底部去凑单
			bottomGoAndCollectTheBill( item ) {
				console.log(item)
				switch( item.activity_range ) {
					case 2:
					if (this.$app)
							wineYunJsBridge.openAppPage({
							client_path: { ios_path: 'goMain', android_path: 'goMain' },
							ad_path_param: [
								{
								ios_key: 'type',
								ios_val: '1',
								android_key: 'type',
								android_val: '1',
								},
							],
							})
						else this.jump.reLaunch(this.routeTable.pgFlashPurchase)
					 //跳转闪购
					break
					case 3:
					if (this.$app)
							wineYunJsBridge.openAppPage({
							client_path: { ios_path: 'goMain', android_path: 'goMain' },
							ad_path_param: [
								{
								ios_key: 'type',
								ios_val: '3',
								android_key: 'type',
								android_val: '3',
								},
							],
							})
						else this.jump.reLaunch(this.routeTable.pgMiaoFa)
					break
					default:
					 //首页
					 if (this.$app) wineYunJsBridge.openAppPage({ client_path: { ios_path: 'goMain', android_path: 'goMain' } })
          			else this.jump.reLaunch(this.$routeTable.pgIndex)//不确定的都跳转首页
					
				}
			},

			handleGoodsJump(period) {
				// if (!this.isShowEdit) return
				this.jump.appAndMiniJump(1, `${this.routeTable.pgGoodsDetail}?id=${period}`, this.$vhFrom)
				// this.jump.navigateTo(`${this.routeTable.pgGoodsDetail}?id=${period}`)
			},
			onSwitch() {
				this.isShowEdit = !this.isShowEdit
				this.getActivityCount()
				const { normalSelectedList, invalidSelectedList } = this.$options.data()
				this.normalCheckList = normalSelectedList
				this.invalidSelectedList = invalidSelectedList
				this.activityList.forEach( v => {
					v.goods_info.forEach( k => { k.checked = false })
				})
				this.normalList.forEach( v => { v.checked = false })
				this.normalSelectedList = []
				this.invalidList.forEach( v => { v.checked = false })
				this.invalidSelectedList = []
			}
		},
		
		// onPullDownRefresh() {
		// 	this.init()
		// },
	}
</script>

<style scoped>
	.list-con>.list-item:first-child{
		margin-top: 0;
	}

	::v-deep .u-numberbox{
		border: 1px solid #EEEEEE;
		border-radius: 8rpx;
	}
	
	::v-deep .u-icon-minus, ::v-deep .u-icon-plus{
		width: 46rpx!important;
		background-color: #FFFFFF!important;
	}
	
	::v-deep .uicon-minus, ::v-deep .uicon-plus{
		font-size: 24rpx!important;
		color: #666!important;
	}
</style>

<style>
	@import "../../../common/css/page.css";
</style>
