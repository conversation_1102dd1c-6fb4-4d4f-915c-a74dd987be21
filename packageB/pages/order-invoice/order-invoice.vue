<template>
  <view class="content">
    <!-- 导航栏 -->
    <vh-navbar title="订单开票" :show-border="true">
      <view slot="right" class="d-flex a-center mr-24">
        <view class="font-24 text-6 l-h-34" @click="jump.appAndMiniJump(1, $routeTable.pBOrderInvoiceHistotyList, $vhFrom)"
          >开票历史</view
        >
        <view class="ml-16 mr-14 w-02 h-22 bg-999999"></view>
        <view class="flex-c-c" @click="showScreenPop = !showScreenPop">
          <view class="font-24 text-6 l-h-34">筛选</view>
          <image class="ml-04 w-20 h-20" :src="ossIcon(`/order_invoice/funnel${isFunnel ? '_h' : ''}_20.png`)"></image>
        </view>
      </view>
    </vh-navbar>

    <!-- tabs选项栏 -->
    <view class="p-stic z-978 b-sh-00041400-012" :style="{ top: system.navigationBarHeight() + 'px' }">
      <u-tabs
        :list="tabList"
        :current="currentTabs"
        :height="92"
        :font-size="28"
        inactive-color="#999"
        active-color="#E80404"
        :bar-width="36"
        :bar-height="8"
        :bar-style="{ background: 'linear-gradient(214deg, #FF8383 0%, #E70000 100%)' }"
        :is-scroll="false"
        @change="changeTabs"
      />
    </view>

    <!-- 订单列表 -->
    <view class="">
      <!-- 商品订单 -->
      <view v-if="currentTabs === 0" class="">
        <!-- 有数据 -->
        <view v-if="orderInvoiceList.length" class="fade-in pt-20 pb-124">
          <view class="d-flex a-center mb-20 mr-24" v-for="(item, index) in orderInvoiceList" :key="index">
            <view class="p-32" @click="selectSingle(item)">
              <vh-check
                :checked="orderInvoiceSelectedList.indexOf(item.id) > -1"
                @click="selectSingle(item)"
              ></vh-check>
            </view>
            <view class="bg-ffffff flex-1 b-rad-16 ptb-28-plr-24">
              <view class="d-flex j-sb bb-s-01-eeeeee pb-28">
                <text class="font-24 text-3 l-h-36">订单号：{{ item.sub_order_no }}</text>
                <text class="font-24 text-9">已完成</text>
              </view>

              <view class="d-flex mt-28">
                <view class="w-246 h-152 b-rad-06 o-hid">
                  <vh-image :loading-type="2" :src="item.banner_img" :height="152" />
                </view>

                <view class="flex-1 d-flex flex-column j-sb ml-12">
                  <view class="">
                    <view class="mb-06 font-24 text-0 l-h-34 o-hid text-hidden-2">{{ item.title }}</view>
                    <text class="bg-f5f5f5 b-rad-08 ptb-04-plr-18 font-22 text-9">{{ item.package_name }}</text>
                  </view>
                  <view class="d-flex j-sb a-center">
                    <text class="font-24 text-9">x{{ item.order_qty }}</text>
                    <text class="font-28 text-3 l-h-32"><text class="font-22">¥</text>{{ item.payment_amount }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <u-loadmore bg-color="#F5F5F5" :status="loadStatus"></u-loadmore>
        </view>

        <!-- 无数据 -->
        <view v-else class="fade-in h-vh-85 bg-ffffff o-hid">
          <vh-empty
            :padding-top="270"
            image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png"
            text="暂无开票订单"
          ></vh-empty>
        </view>
      </view>

      <!-- 酒会订单 -->
      <view v-if="currentTabs === 1" class="">
        <!-- 有数据 -->
        <view v-if="winePartyOrderInvoiceList.length" class="fade-in pt-20 pb-124">
          <view class="d-flex a-center mb-20 mr-24" v-for="(item, index) in winePartyOrderInvoiceList" :key="index">
            <view class="p-32" @click="selectSingle(item)">
              <vh-check
                :checked="winePartyorderInvoiceSelectedList.indexOf(item.party_id) > -1"
                @click="selectSingle(item)"
              ></vh-check>
            </view>
            <view class="bg-ffffff flex-1 b-rad-16 ptb-28-plr-24">
              <view class="d-flex j-sb bb-s-01-eeeeee pb-28">
                <text class="font-24 text-3 l-h-36">订单号：{{ item.main_order_no }}</text>
                <text class="font-24 text-9">{{ item.status === 0 ? '已过期' : '已使用' }}</text>
              </view>

              <view class="d-flex mt-28">
                <vh-image :loading-type="2" :src="item.thumb_image" :width="246" :height="152" :border-radius="6" />

                <view class="flex-1 d-flex flex-column j-sb ml-12">
                  <view class="">
                    <view class="mb-06 font-24 text-0 l-h-34 o-hid text-hidden-2">{{ item.party_name }}</view>
                    <text class="bg-f5f5f5 b-rad-08 ptb-04-plr-18 font-22 text-9">{{ item.package_name }}</text>
                  </view>
                  <view class="d-flex j-sb a-center">
                    <text class="font-24 text-9">x{{ item.order_qty }}</text>
                    <text class="font-28 text-3 l-h-32"><text class="font-22">¥</text>{{ item.payment_amount }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <u-loadmore bg-color="#F5F5F5" :status="loadStatus"></u-loadmore>
        </view>

        <!-- 无数据 -->
        <view v-else class="fade-in h-vh-85 bg-ffffff o-hid">
          <vh-empty
            :padding-top="270"
            image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png"
            text="暂无开票订单"
          ></vh-empty>
        </view>
      </view>
    </view>

    <!-- 底操作按钮 -->
    <view class="fade-in-up-medium p-fixed bottom-0 z-600 w-p100 bg-ffffff b-sh-00021200-022">
      <sp-invoice-company :list="invoiceDetailList" @openInvoiceDetail="showInvoiceDetailPop = true" />
      <view class="h-104 d-flex j-sb a-center pl-28 pr-24">
        <view class="d-flex a-center">
          <view class="d-flex a-center" @click="selectAll()">
            <vh-check :checked="isSelectAll()" :margin-top="10" @click="selectAll()" />
            <view class="ml-08 font-32 text-3">全选</view>
          </view>

          <view class="ml-28">
            <view class="d-flex a-center">
              <text class="font-28 font-wei text-3">合计：</text>
              <text class="font-40 font-wei text-e80404"><text class="font-28">¥</text>{{ totalMoney }}</text>
            </view>

            <view class="d-flex a-center font-20 text-3 l-h-28"
              >订单:
              {{
                currentTabs == 0 ? orderInvoiceSelectedList.length : winePartyorderInvoiceSelectedList.length
              }}个</view
            >
          </view>
        </view>

        <view class="">
          <u-button
            :disabled="!canNext"
            shape="circle"
            :hair-line="false"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{
              width: '208rpx',
              height: '64rpx',
              fontSize: '28rpx',
              fontWeight: 'bold',
              color: '#FFF',
              backgroundColor: !canNext ? '#FCE4E3' : '#E80404',
              border: 'none',
            }"
            @click="jump.appAndMiniJump(1, `${routeTable.pEInvoiceMangement}?comeFrom=1`, $vhFrom)"
            >下一步</u-button
          >
        </view>
      </view>
    </view>

    <!-- 弹框 -->
    <view class="">
      <!-- 筛选弹框 -->
      <u-popup
        v-model="showScreenPop"
        mode="top"
        height="710"
        :border-radius="20"
        :z-index="979"
        :custom-style="{ top: system.navigationBarHeight() + 'px' }"
      >
        <view class="p-rela h-p100 o-scr-y">
          <!-- 筛选内容 -->
          <view class="pt-36 pl-28 pr-28">
            <!-- 日期 -->
            <view class="">
              <view class="font-28 text-6 l-h-40">订单日期</view>
              <view class="d-flex j-sb a-center mt-20">
                <view
                  class="w-308 h-70 bg-f6f6f6 b-rad-36 d-flex j-center a-center"
                  :class="orderStartTime ? 'font-32 text-3' : 'font-28 text-cfcfcf'"
                  @click="showStartTimePicker = true"
                >
                  {{
                    orderStartTime ? `${orderStartTime.year}.${orderStartTime.month}.${orderStartTime.day}` : '起始日期'
                  }}
                </view>
                <view class="w-32 h-01 bg-999999" />
                <view
                  class="w-308 h-70 bg-f6f6f6 b-rad-36 d-flex j-center a-center"
                  :class="orderEndTime ? 'font-32 text-3' : 'font-28 text-cfcfcf'"
                  @click="showeEndTimePicker = true"
                >
                  {{ orderEndTime ? `${orderEndTime.year}.${orderEndTime.month}.${orderEndTime.day}` : '终止日期' }}
                </view>
              </view>
            </view>

            <!-- 可开票金额范围 -->
            <view class="mt-60">
              <view class="font-28 text-6 l-h-40">可开票金额范围</view>
              <view class="d-flex j-sb a-center mt-20">
                <view class="w-308 h-70 bg-f6f6f6 b-rad-36 d-flex j-center a-center text-cfcfcf">
                  <input
                    class="text-center font-32 text-3"
                    type="number"
                    v-model="minMoney"
                    placeholder="最低价"
                    placeholder-style="color:#cfcfcf;font-size:28rpx;"
                  />
                </view>
                <view class="w-32 h-01 bg-999999"></view>
                <view class="w-308 h-70 bg-f6f6f6 b-rad-36 d-flex j-center a-center text-cfcfcf">
                  <input
                    class="text-center font-32 text-3"
                    type="number"
                    v-model="maxMoney"
                    placeholder="最高价"
                    placeholder-style="color:#cfcfcf;font-size:28rpx;"
                  />
                </view>
              </view>
            </view>

            <!-- 订单类型 -->
            <view v-if="currentTabs === 0" class="mt-60">
              <view class="font-28 text-6 l-h-40">订单类型</view>
              <view class="d-flex j-sb a-center mt-20">
                <view
                  class="w-214 h-70 bg-f6f6f6 b-rad-36 d-flex j-center a-center font-28 font-wei text-3"
                  :class="orderType == item.type ? 'fade-in bg-fce0e0 text-e80404' : ''"
                  v-for="(item, index) in orderTypeList"
                  :key="index"
                  @click="changeOrderType(item.type)"
                  >{{ item.name }}</view
                >
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="p-fixed bottom-0 z-999 w-p100">
            <view class="h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center ptb-00-plr-36">
              <u-button
                shape="circle"
                :hair-line="false"
                :ripple="true"
                ripple-bg-color="#FFF"
                :custom-style="{
                  width: '308rpx',
                  height: '64rpx',
                  fontSize: '28rpx',
                  fontWeight: 'bold',
                  color: '#999',
                  backgroundColor: '#EEE',
                  border: 'none',
                }"
                @click="reset()"
                >重置</u-button
              >
              <u-button
                :disabled="!orderCanFilter"
                shape="circle"
                :hair-line="false"
                :ripple="true"
                ripple-bg-color="#FFF"
                :custom-style="{
                  width: '308rpx',
                  height: '64rpx',
                  fontSize: '28rpx',
                  fontWeight: 'bold',
                  color: '#FFF',
                  backgroundColor: !orderCanFilter ? '#FCE0E0' : '#E80404',
                  border: 'none',
                }"
                @click="confirm()"
                >确定</u-button
              >
            </view>
          </view>
        </view>
      </u-popup>

      <!-- 起始日期弹框 -->
      <u-picker v-model="showStartTimePicker" mode="time" confirm-color="#E80404" @confirm="confirmStartTime" />

      <!-- 终止日期弹框 -->
      <u-picker v-model="showeEndTimePicker" mode="time" confirm-color="#E80404" @confirm="confirmEndTime" />

      <!-- 开票选中所有发票弹框 -->
      <u-modal
        v-model="showSelectAllInvoiceMod"
        :show-title="false"
        content=""
        :width="520"
        :show-confirm-button="false"
        :show-cancel-button="true"
        cancel-text="知道了"
        :cancel-style="{ fontSize: '28rpx', color: '#999' }"
      >
        <view class="p-32">
          <view class="d-flex j-center a-center font-36 font-wei text-3">提示</view>
          <view class="d-flex j-center mt-30 l-h-44">
            <text class="font-28 text-3">
              当前已选中{{
                currentTabs === 0 ? orderInvoiceSelectedList.length : winePartyorderInvoiceSelectedList.length
              }}条{{ currentTabs === 0 ? '商品' : '酒会' }}订单
            </text>
          </view>
        </view>
      </u-modal>

      <!-- 发票明细弹框 -->
      <sp-invoice-detail-popup v-model="showInvoiceDetailPop" :list="invoiceDetailList" />
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'order-invoice',

  data() {
    return {
      // tabs
      tabList: [
        //状态栏选项
        { name: '商品订单' },
        { name: '酒会订单' },
      ],
      currentTabs: 0, //当前选中tabs

      // 筛选 + 开票
      orderTypeList: [
        { type: 0, name: '闪购' },
        { type: 1, name: '现货速发' },
        { type: 3, name: '尾货' },
      ], //订单类型列表
      orderStartTime: '', //订单开始时期
      orderEndTime: '', //订单结束时间
      minMoney: '', //最小金额
      maxMoney: '', //最大金额
      orderType: null, //订单类型 0 = 闪购、1 = 秒发、3 = 尾货
      orderInvoiceList: [], //订单开票列表
      orderInvoiceSelectedList: [], //选中的订单开票列表
      winePartyOrderInvoiceList: [], //酒会订单开票列表
      winePartyorderInvoiceSelectedList: [], //选中的酒会订单开票列表
      invoiceDetailList: [], //开票明细列表

      // 分页板块
      page: 1, //第几页
      limit: 10, //每页显示多少条
      totalPage: 1, //总页数
      loadStatus: 'loadmore', //加载状态
      totalMoney: '0.00', //合计金额

      // 弹框
      showScreenPop: false, //筛选弹框
      showStartTimePicker: false, //起始日期弹框
      showeEndTimePicker: false, //终止日期弹框
      showSelectAllInvoiceMod: false, //选中所有发票弹框
      showInvoiceDetailPop: false, //是否显示发票明细弹框
      isFunnel: false,
    }
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['invoiceInfo', 'routeTable']),

    // 获取导航栏高度
    getNavigationBarHeight() {
      return this.system.getSysInfo().statusBarHeight + 48
    },

    // 获取订单下边距
    getOrderInvoiceListPaddingBottom({ invoiceDetailList }) {
      const length = invoiceDetailList.length
      if (length) {
        if (length === 1) return 330
        return 370
      }
      return 124
    },

    // 订单是否可以过滤
    orderCanFilter() {
      // 三个条件
      // 1、选择了订单起始日期和订单终止日期且订单起始日期小于订单终止日期、且没有选择金额、且没有选择订单类型 true
      // 2、没有选择日期、且选择了订单金额最低价跟订单金额最高价订单金额最低价小于订单金额最高价、且没有选择订单类型 true
      // 3、没有选择日期、且没有选择金额、且选择了订单类型 true
      // 4、选择了订单起始日期和订单终止日期且订单起始日期小于订单终止日期、且选择了订单金额最低价跟订单金额最高价订单金额最低价小于订单金额最高价、且没有选择订单类型 true
      // 5、选择了订单起始日期和订单终止日期且订单起始日期小于订单终止日期、且没有选择金额、且选择了订单类型 true
      // 6、没有选择日期、且选择了订单金额最低价跟订单金额最高价订单金额最低价小于订单金额最高价、且选择了订单类型 true
      // 7、选择了订单起始日期和订单终止日期且订单起始日期小于订单终止日期、且选择了订单金额最低价跟订单金额最高价订单金额最低价小于订单金额最高价、且选择了订单类型 true
      let orderDateCorrect =
        this.orderStartTime && this.orderEndTime && this.orderStartTime.timestamp < this.orderEndTime.timestamp //订单日期校验正确
      let orderDateError = this.orderStartTime === '' && this.orderEndTime === '' //订单日期校验错误
      let orderMoneyCorrect = this.minMoney && this.maxMoney && Number(this.minMoney) < Number(this.maxMoney) //订单金额校验正确
      let orderMoneyError = this.minMoney === '' && this.maxMoney === '' //订单金额校验错误
      let orderTypeCorrect = this.orderType !== null //校验订单类型正确
      let orderTypeError = this.orderType === null //校验订单类型错误

      if (orderDateCorrect && orderMoneyError && orderTypeError) {
        //1
        return true
      } else if (orderDateError && orderMoneyCorrect && orderTypeError) {
        //2
        return true
      } else if (orderDateError && orderMoneyError && orderTypeCorrect) {
        //3
        return true
      } else if (orderDateCorrect && orderMoneyCorrect && orderTypeError) {
        //4
        return true
      } else if (orderDateCorrect && orderMoneyError && orderTypeCorrect) {
        //5
        return true
      } else if (orderDateError && orderMoneyCorrect && orderTypeCorrect) {
        //6
        return true
      } else if (orderDateCorrect && orderMoneyCorrect && orderTypeCorrect) {
        //7
        return true
      }
      return false
    },

    // 是否可以进行下一步
    canNext() {
      if (this.currentTabs === 0 && this.orderInvoiceSelectedList.length) {
        return true
      } else if (this.currentTabs === 1 && this.winePartyorderInvoiceSelectedList.length) {
        return true
      }
      return false
    },
  },

  onLoad() {
    this.system.setNavigationBarBlack()
    this.init()
  },

  onShow() {
    if(this.$vhFrom == 'next'){
				const InvoiceInfo = uni.getStorageSync('InvoiceInfo');
        this.muInvoiceInfo(InvoiceInfo)
			 	uni.removeStorageSync('InvoiceInfo');
    }
    this.openInvoice()
  },

  methods: {
    // Vuex mapMutations辅助函数
    ...mapMutations(['muInvoiceInfo']),

    // 初始化
    async init() {
      this.page = 1
      await this.getOrderInvoiceList()
      // await this.getWinePartyOrderInvoiceList()
      // await this.openInvoice()
    },

    // 切换状态栏
    changeTabs(index) {
      this.currentTabs = index
      this.reset()
    },

    // 获取订单开票列表类型 resetPage 是否需要重置page
    getOrderInvoiceListType(resetPage = 1) {
      this.feedback.loading()
      if (resetPage) {
        this.page = 1 //重置页面
        this.orderInvoiceSelectedList = [] //重置选中的订单选中列表
        this.winePartyorderInvoiceSelectedList = [] //重置选中的酒会订单列表
      }
      if (this.currentTabs === 0) {
        this.getOrderInvoiceList()
      } else {
        this.getWinePartyOrderInvoiceList()
      }
      this.getTotalMoney()
    },

    // 获取订单可开票列表
    async getOrderInvoiceList() {
      try {
        let data = {
          page: this.page,
          limit: this.limit,
        }
        // 传入起始日期终止日期
        if (this.orderStartTime && this.orderEndTime) {
          data.stime = `${this.orderStartTime.year}-${this.orderStartTime.month}-${this.orderStartTime.day} 00:00:00`
          data.etime = `${this.orderEndTime.year}-${this.orderEndTime.month}-${this.orderEndTime.day} 23:59:59`
        }
        // 传入最低价、最高价
        if (this.minMoney && this.maxMoney) {
          data.smoney = this.minMoney
          data.emoney = this.maxMoney
        }
        // 传入订单类型
        if (this.orderType !== null) {
          data.order_type = this.orderType
        }

        console.log('--------这是要上传的参数')
        console.log(data)
        let res = await this.$u.api.personalOrderInvoiceList(data)
        let { list, total } = res.data
        this.page == 1 ? (this.orderInvoiceList = list) : (this.orderInvoiceList = [...this.orderInvoiceList, ...list])
        this.totalPage = Math.ceil(total / this.limit)
        this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
        this.showScreenPop = false
        this.feedback.hideLoading()
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 获取酒会订单可开票列表
    async getWinePartyOrderInvoiceList() {
      try {
        let data = {
          type: 1, //列表类型 1我的开票订单（验证登录） 0 所有
          page: this.page,
          limit: this.limit,
        }

        // 传入起始日期终止日期
        if (this.orderStartTime && this.orderEndTime) {
          data.stime = `${this.orderStartTime.year}-${this.orderStartTime.month}-${this.orderStartTime.day} 00:00:00`
          data.etime = `${this.orderEndTime.year}-${this.orderEndTime.month}-${this.orderEndTime.day} 23:59:59`
        }

        // 传入最低价、最高价
        if (this.minMoney && this.maxMoney) {
          data.smoney = this.minMoney
          data.emoney = this.maxMoney
        }

        console.log('--------这是要上传的参数')
        console.log(data)
        let res = await this.$u.api.winePartyOrderInvoiceList(data)
        let { list, total } = res.data
        this.page == 1
          ? (this.winePartyOrderInvoiceList = list)
          : (this.winePartyOrderInvoiceList = [...this.winePartyOrderInvoiceList, ...list])
        this.totalPage = Math.ceil(total / this.limit)
        this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
        this.showScreenPop = false
        this.feedback.hideLoading()
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 开发票
    async openInvoice() {
      if (Object.keys(this.invoiceInfo).length) {
        try {
          let invoiceList = []
          if (this.currentTabs === 0) {
            this.orderInvoiceList.forEach((v) => {
              if (this.orderInvoiceSelectedList.indexOf(v.id) > -1) {
                invoiceList.push({ order_no: v.sub_order_no, associated_products: v.associated_products })
              }
            })
          } else {
            this.winePartyOrderInvoiceList.forEach((v) => {
              if (this.winePartyorderInvoiceSelectedList.indexOf(v.party_id) > -1) {
                invoiceList.push(v.main_order_no)
              }
            })
          }
          if (!invoiceList.length) return this.feedback.toast({ title: '请选择需要开票的发票~' })

          this.feedback.loading({ title: '开票中...' })
          // 接口需要的开票数据
          let data = {
            way: 0, //0:选择发票 1:临时提交发票抬头
            receipt_id: this.invoiceInfo.id, //发票抬头id way=0时必传
          }
          if (this.currentTabs === 0) {
            //商品订单开票
            data.branch = 0 // 场景 0:勾选开票 1:确认收货开票 2:工单开票 3:酒会开票
            data.orders = JSON.stringify(invoiceList) // 子订单json字符串 branch=0时必传
          } else {
            //酒会订单开票
            data.branch = 3 // 场景 0:勾选开票 1:确认收货开票 2:工单开票 3:酒会开票
            data.order_no = invoiceList.join(',') // 子订单号 branch等于1和2和3时必传 等于3时多个子订单逗号分隔
          }
          // let data = {
          // 	branch: 0,
          // 	orders: JSON.stringify(invoiceList),
          // 	way:0,
          // 	receipt_id: this.invoiceInfo.id
          // }
          console.log(data)
          // return
          let res = await this.$u.api.openInvoice(data)
          this.feedback.toast({ title: '开票成功~' })
          // 重置
          if (this.currentTabs === 0) {
            this.orderInvoiceSelectedList = []
          } else {
            this.winePartyorderInvoiceSelectedList = []
          }
          this.totalMoney = '0.00'
          this.muInvoiceInfo({})
          uni.removeStorageSync('InvoiceInfo');
          setTimeout(() => {
            this.getOrderInvoiceListType()
          }, 1500)
          console.log('--------------------我是开票的res')
          console.log(res)
        } catch (e) {
          console.log(e)
          setTimeout(() => {
            this.getOrderInvoiceListType()
          }, 1500)
          if (this.currentTabs === 0) {
            this.orderInvoiceSelectedList = []
          } else {
            this.winePartyorderInvoiceSelectedList = []
          }
          this.totalMoney = '0.00'
        }
      } else {
        console.log('bbb')
      }
    },

    // 选中单个/取消选中单个 item = 订单开票列表某一项
    selectSingle(item) {
      if (this.currentTabs === 0) {
        if (this.orderInvoiceSelectedList.indexOf(item.id) > -1) {
          this.orderInvoiceList.forEach((v) => {
            if (v.id === item.id) v.checked = false
          })
          this.orderInvoiceSelectedList.splice(this.orderInvoiceSelectedList.indexOf(item.id), 1)
        } else {
          this.orderInvoiceList.forEach((v) => {
            if (v.id === item.id) v.checked = true
          })
          this.orderInvoiceSelectedList.push(item.id)
        }
      } else {
        if (this.winePartyorderInvoiceSelectedList.indexOf(item.party_id) > -1) {
          this.winePartyOrderInvoiceList.forEach((v) => {
            if (v.party_id === item.party_id) v.checked = false
          })
          this.winePartyorderInvoiceSelectedList.splice(
            this.winePartyorderInvoiceSelectedList.indexOf(item.party_id),
            1
          )
        } else {
          this.winePartyOrderInvoiceList.forEach((v) => {
            if (v.party_id === item.party_id) v.checked = true
          })
          this.winePartyorderInvoiceSelectedList.push(item.party_id)
        }
      }
      this.getTotalMoney()
    },

    // 判断是否全选
    isSelectAll() {
      if (this.currentTabs === 0) {
        return (
          this.orderInvoiceList.length === this.orderInvoiceSelectedList.length && this.orderInvoiceSelectedList.length
        )
      } else {
        return (
          this.winePartyOrderInvoiceList.length === this.winePartyorderInvoiceSelectedList.length &&
          this.winePartyorderInvoiceSelectedList.length
        )
      }
    },

    // 全选/取消全选
    selectAll() {
      if (
        (!this.orderInvoiceList.length && this.currentTabs === 0) ||
        (!this.winePartyOrderInvoiceList.length && this.currentTabs === 1)
      )
        return
      if (this.currentTabs === 0) {
        if (this.isSelectAll()) {
          this.orderInvoiceList.forEach((v) => {
            v.checked = false
          })
          this.orderInvoiceSelectedList = []
        } else {
          this.orderInvoiceSelectedList = this.orderInvoiceList.map((v) => {
            v.checked = true
            return v.id
          })
          this.showSelectAllInvoiceMod = true
        }
      } else {
        if (this.isSelectAll()) {
          this.winePartyOrderInvoiceList.forEach((v) => {
            v.checked = false
          })
          this.winePartyorderInvoiceSelectedList = []
        } else {
          this.winePartyorderInvoiceSelectedList = this.winePartyOrderInvoiceList.map((v) => {
            v.checked = true
            return v.party_id
          })
          this.showSelectAllInvoiceMod = true
        }
      }
      this.getTotalMoney()
    },

    // 获取总金额
    getTotalMoney() {
      let total = 0
      if (this.currentTabs === 0) {
        this.orderInvoiceList.forEach((v) => {
          if (this.orderInvoiceSelectedList.indexOf(v.id) > -1) total += v.payment_amount
        })
        this.getInvoiceDetailList()
      } else {
        this.winePartyOrderInvoiceList.forEach((v) => {
          if (this.winePartyorderInvoiceSelectedList.indexOf(v.party_id) > -1) total += v.payment_amount
        })
      }
      this.totalMoney = total.toFixed(2)
    },

    // 获取发票明细列表
    async getInvoiceDetailList() {
      // this.invoiceDetailList = []
      const list = this.orderInvoiceList.filter((v) => v.checked)
      if (!list.length) return (this.invoiceDetailList = [])
      const items_info = list.map(({ period, package_id, payment_amount }) => ({
        period,
        package_id,
        goods_money: payment_amount,
      }))
      const { data } = await this.$u.api.orderInvoiceDetailList({ items_info })
      this.invoiceDetailList = data
    },

    // 确认开始时间 e = 开始时间信息
    confirmStartTime(e) {
      this.orderStartTime = e
    },

    // 确认结束时间 e = 结束时间信息
    confirmEndTime(e) {
      this.orderEndTime = e
    },

    // 重置
    reset() {
      this.orderStartTime = ''
      this.orderEndTime = ''
      this.minMoney = ''
      this.maxMoney = ''
      this.orderType = null
      this.isFunnel = false
      this.getOrderInvoiceListType()
    },

    // 确定
    confirm() {
      this.isFunnel = true
      this.getOrderInvoiceListType()
    },

    // 切换订单类型 type = 订单类型
    changeOrderType(type) {
      if (type === this.orderType) return (this.orderType = null)
      this.orderType = type
    },
  },

  onReachBottom() {
    console.log('--------------------------bbb')
    if (this.page == this.totalPage || !this.totalPage) return
    this.loadStatus = 'loading'
    this.page++
    this.getOrderInvoiceListType(0)
  },
}
</script>

<style>
@import '@/common/css/page.css';
</style>
