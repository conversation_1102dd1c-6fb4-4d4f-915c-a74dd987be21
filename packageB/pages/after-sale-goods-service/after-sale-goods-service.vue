<template>
	<view class="content h-vh-100 o-scr-y bg-f5f5f5 pb-124">
		<!-- 导航栏 -->
		<u-navbar back-icon-color="#333" title="选择服务" :title-bold="true" title-color="#333" />
		
		<!-- 退款商品 -->
		<view class="bg-ffffff b-rad-10 mt-20 mr-24 ml-24 ptb-32-plr-24">
			<view class="font-32 font-wei text-3">退款商品</view>
			
			<view class="mt-20 d-flex" v-for="(item, index) in afterSaleGoodsInfo.goodsInfo" :key="index">
				<OrderGoodsImage :orderType="afterSaleGoodsInfo.order_type" :goodsImg="item.goods_img" />
				
				<view class="flex-1 d-flex flex-column j-sb ml-12">
					<view class="">
						<OrderGoodsTitle :goodsInfo="item" />
						<OrderGoodsTag :orderType="afterSaleGoodsInfo.order_type" :auctionType="afterSaleGoodsInfo.auction_type" :goodsInfo="item" />
					</view>
					
					<view class="d-flex j-sb">
						<text class="font-22 text-6">x{{item.order_qty}}</text>
						<text class="font-28 text-3">¥{{item.package_price}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 服务类型（after_sale_status == 0 时代表该商品不能申请售后，展示不能申请售占位提示、after_sale_status == 1代表可以申请售后、展示服务类型） -->
		<view class="">
			<view v-if="afterSaleGoodsInfo.after_sale_status == 0" class="">
				<view class="d-flex j-center a-center pt-170">
					<view class="p-rela w-440 h-360">
						<image class="w-p100 h-p100" src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_after_sale.png" mode="aspectFill"></image>
						<view class="p-abso bottom-0 w-p100 d-flex j-center font-32 font-wei text-3">当前商品无法申请售后</view>
					</view>
				</view>
				<view class="d-flex j-center a-center ptb-24-plr-32 font-24 text-9 l-h-34">{{afterSaleGoodsInfo.forbidden_reason}}</view>
				<!-- <vh-empty :padding-top="168" bg-color="#F5F5F5" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png" text="当前商品无法申请售后~" /> -->
				
				<view class="d-flex j-center mt-230">
					<text class="font-24 text-9">如有疑问请</text>
					<text class="font-24 text-2e7bff" @click="system.customerService($vhFrom)">联系客服</text>
					<text class="font-24 text-9">，在线时间9:00-21:00。</text>
				</view>
			</view>
			<view v-else class="bg-ffffff b-rad-10 mt-20 mr-24 ml-24 pt-32 pr-24 pl-24">
				<view class="font-32 font-wei text-3">选择服务类型</view>
				
				<view class="mt-08">
					<view class="service-item d-flex j-sb a-center" v-for="(item,index) in serviceList" :key="index" @click="jump.redirectTo(`../after-sale-apply/after-sale-apply?serviceType=${item.type}`)">
						<image class="w-48 h-48" :src="item.icon" mode="aspectFill"></image>
						<view class="service-intro flex-1 ml-16 d-flex j-sb a-center ptb-32-plr-00 bb-s-01-eeeeee">
							<view class="">
								<view class="font-28 font-wei text-3 l-h-40">{{item.name}}</view>
								<view class="mt-06 font-24 text-9 l-h-34">{{item.intro}}</view>
							</view>
							<u-icon name="arrow-right" :size="20" color="#333" />
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default{
		name:"after-sale-goods-service",
		
		data(){
			return{
				serviceList:[
					{ 
						type: 1,
						icon: 'https://images.vinehoo.com/vinehoomini/v3/comm/mon_bla.png', 
						name: '仅退款',
						intro: '未收到货，或与酒云协商统一不退货仅退款',
					},
					{
						type: 2,
						icon: 'https://images.vinehoo.com/vinehoomini/v3/comm/ret_bla.png', 
						name: '退货退款',
						intro: '已收到货，需要退还收到的货物',
					},
					{
						type: 3,
						icon: 'https://images.vinehoo.com/vinehoomini/v3/comm/cha_bla.png', 
						name: '换货',
						intro: '已收到货，需要更换已收到的货物',
					}
				]
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['afterSaleGoodsInfo', 'routeTable']),
			hasAfterSaleGoodsInfo({ afterSaleGoodsInfo }) {
				return !!(typeof afterSaleGoodsInfo === 'object' && Object.keys(afterSaleGoodsInfo).length)
			}
		},
		
		onLoad(options) {
			const SaleGoodsInfo = uni.getStorageSync('SaleGoodsInfo');
			if(SaleGoodsInfo){
				this.muAfterSaleGoodsInfo(SaleGoodsInfo)
				uni.removeStorageSync('SaleGoodsInfo');
			}
			if (!this.hasAfterSaleGoodsInfo) {
				// this.jump.reLaunch(`${this.routeTable.pEMyOrder}`)
				this.jump.appAndMiniJump(1, `${this.routeTable.pEMyOrder}`, this.$vhFrom, 1)
				return
			}
			this.getServiceList()
		},
		
		methods: {
			...mapMutations(['muAfterSaleGoodsInfo']),
			// 获取服务列表
			getServiceList() {
				let { status, order_type } = this.afterSaleGoodsInfo
				console.log( '----------------------------我是status')
				console.log( status )
				if(status == 1 || status == 6) { //已支付、已暂存只有仅退款
					this.serviceList = this.serviceList.slice(0, 1)
				}else{
					if( order_type == 2 || order_type == 11 ) { // 跨境不允许换货（需求方 龙飞：2022-07-05）
						this.serviceList = this.serviceList.slice(0, 2)
					}
				}
			}
		}
	}
</script>

<style scoped>
	.service-item:nth-child(3) > .service-intro{
		border-bottom: 0 solid #eee;
	}
</style>
