<template>
	<view class="content h-vh-100 o-scr-y bg-f5f5f5 pb-124">
		<!-- 导航栏 -->
		<vh-navbar :title="serviceType == 3 ? '换货' : '申请售后'" />
		
		<!-- 退款商品 -->
		<view class="bg-ffffff b-rad-10 mt-20 mr-24 ml-24 ptb-32-plr-24">
			<view class="font-32 font-wei text-3">{{serviceType == 3 ? '换货商品' : '退款商品'}}</view>
			
			<view class="mt-20 d-flex" v-for="(item, index) in afterSaleGoodsInfo.goodsInfo" :key="index">
				<OrderGoodsImage :orderType="afterSaleGoodsInfo.order_type" :goodsImg="item.goods_img" />
				<view class="flex-1 d-flex flex-column j-sb ml-12">
					<view class="">
						<OrderGoodsTitle :goodsInfo="item" />
						<OrderGoodsTag :orderType="afterSaleGoodsInfo.order_type" :auctionType="afterSaleGoodsInfo.auction_type" :goodsInfo="item" />
					</view>
					
					<view class="d-flex j-sb">
						<text class="font-22 text-6">x{{item.order_qty}}</text>
						<text class="font-28 text-3">¥{{item.package_price}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 退款信息 -->
		<view class="bg-ffffff b-rad-10 mt-20 mr-24 ml-24 ptb-32-plr-24">
			<!-- 换货 -->
			<view v-if="serviceType == 3" class="">
				<view class="font-32 font-wei text-3">换货信息</view>
				
				<view class="d-flex j-sb a-center mt-40 pb-32 bb-s-01-eeeeee">
					<view class="font-28 font-wei text-3 l-h-40">换货原因</view>
					
					<view class="d-flex a-center" @click="showRefPop = true">
						<text class="mr-16 font-28 l-h-40" :class="refundReason == '' ? 'text-9' : 'text-3'">{{refundReason == '' ? '请选择' : refundReason}}</text>
						<u-icon name="arrow-right" :size="20" color="#333" />
					</view>
				</view>
				
				<view class="d-flex pt-32" :class="Object.keys(addressInfo).length ? '' : 'j-sb'">
					<view class="font-28 font-wei text-3 w-s-now">收货地址</view>
					
					<!-- 有收货地址 -->
					<view v-if="Object.keys(addressInfo).length" class="flex-1 d-flex j-sb a-center ml-28" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=2`)">
						<view class="w-486">
							<view class="d-flex a-center">
								<text class="font-28 font-wei text-3">{{addressInfo.consignee}}</text>
								<text class="ml-20 font-28 text-3">{{addressInfo.consignee_phone}}</text>
							</view>
							<view class="mt-08 font-24 text-9"> {{addressInfo.province_name}} {{addressInfo.city_name}} {{addressInfo.town_name}} {{addressInfo.address}}</view>
						</view>
						<u-icon name="arrow-right" :size="20" color="#333" />
					</view>
					
					<!-- 没有收货地址 -->
					<view v-else class="d-flex a-center" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=2`)">
						<text class="mr-16 font-28 text-9 l-h-40">请选择</text>
						<u-icon name="arrow-right" :size="20" color="#333" />
					</view>
				</view>
			</view>
			
			<!-- 退货、退货退款 -->
			<view v-else class="">
				<view class="font-32 font-wei text-3">退款信息</view>
				
				<view class="mt-40 pb-32 bb-s-01-eeeeee">
					<view class="d-flex j-sb a-center">
						<view class="font-28 font-wei text-3 l-h-40">货物状态</view>
						<!-- 货物状态为 （1、2）或者选择了退货退款或换货、就不允许选择货物状态弹框 -->
						<view v-if="cargoStatus == 1 || cargoStatus == 2 || serviceType == 2 || serviceType == 3" class="font-28 text-3 l-h-40">{{cargoStatusName}}</view>
						<view v-else class="d-flex a-center" @click="showRecPop = true">
							<text class="mr-16 font-28 l-h-40" :class="cargoStatusName == '' ? 'text-9' : 'text-3'">{{cargoStatusName == '' ? '请选择' : cargoStatusName}}</text>
							<u-icon name="arrow-right" :size="20" color="#333" />
						</view>
					</view>
					<view v-if="cargoStatus == 2" class="bg-f7f7f7 b-rad-04 mt-20 p-16">
						<view class="d-flex j-sb a-center">
							<text class="font-24 text-6">暂存时间</text>
							<text class="font-24 text-6">{{refundInfo.ts_day}}天</text>
						</view>
						
						<view class="d-flex j-sb a-center mt-20">
							<text class="font-24 text-6">暂存费用</text>
							<text class="font-24 text-6">¥{{refundInfo.ts_free}}</text>
						</view>
					</view>
				</view>
				
				<view class="d-flex j-sb a-center mt-40 pb-32 bb-s-01-eeeeee">
					<view class="font-28 font-wei text-3 l-h-40">退款原因</view>
					
					<view class="d-flex a-center" @click="showRefPop = true">
						<text class="mr-16 font-28 l-h-40" :class="refundReason == '' ? 'text-9' : 'text-3'">{{refundReason == '' ? '请选择' : refundReason}}</text>
						<u-icon name="arrow-right" :size="20" color="#333" />
					</view>
				</view>
				
				<view class="">
					<view class="d-flex a-center mt-36 font-28 font-wei text-3">申请金额</view>
					
					<view class="bg-f7f7f7 b-rad-04 mt-20 p-16">
						<view class="font-32 font-wei text-e80404">¥{{refundInfo.refund_money ? refundInfo.refund_money : '0.00'}}</view>
						<view class="mt-10 font-24 text-9">不可修改，最多¥{{refundInfo.refund_money}}，含运费¥{{refundInfo.express_fee}}</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 补充描述和凭证 -->
		<view class="bg-ffffff b-rad-10 mt-20 mr-24 ml-24 ptb-32-plr-24">
			<view class="">
				<text class="font-32 font-wei text-3">补充描述和凭证</text>
				<text class="font-28 text-9">（选填）</text>
			</view>
			
			<view class="bg-f7f7f7 mt-32 p-20">
				<view class="">
					<textarea v-model="describe" class="w-p100 h-150 font-28 text-3" placeholder="请输入补充描述" placeholder-style="color:#999;font-size:28rpx;"/>
				</view>
				
				<view class="mt-20">
					<vh-upload
						ref="uUpload" 
						:directory="'vinehoo/client/afterService/'" 
						:auto-upload="false" 
						:can-upload-video="true"
						:max-count="3" 
						@on-list-change="onListChange"
						@on-uploaded="onUploaded" />
				</view>
			</view>
		</view>
		
		<!-- 弹框 -->
		<view class="">
			<!-- 货物状态弹框 -->
			<u-popup v-model="showRecPop" mode="bottom" :border-radius="20">
				<view class="pt-40 pl-48 pr-48">
					<view class="d-flex j-center a-center font-36 font-wei text-3">货物状态</view>
					<view class="mt-20">
						<view class="d-flex j-sb a-center bb-s-01-eeeeee ptb-40-plr-00" v-for="(item, index) in cargoStatusList" :key="index" @click="selectCargoStatus(item, index)">
							<text class="font-28 font-wei text-3">{{item.name}}</text>
							<vh-check :width="36" :height="36" :checked="cargoStatusName == item.name"  @click="selectCargoStatus(item, index)"/>
						</view>
					</view>
				</view>
			</u-popup>
			
			<!-- 退款原因/换货原因弹框 -->
			<u-popup v-model="showRefPop" mode="bottom" :border-radius="20">
				<view class="pt-40 pl-48 pr-48">
					<view class="d-flex j-center a-center font-36 font-wei text-3">退款原因</view>
					<view class="mt-20">
						<view class="d-flex j-sb a-center bb-s-01-eeeeee ptb-40-plr-00" v-for="(item, index) in refundReasonList" :key="index" @click="selectRefundReason(item.content)">
							<text class="font-28 font-wei text-3">{{item.content}}</text>
							<vh-check :width="36" :height="36" :checked="refundReason == item.content" @click="selectRefundReason(item.content)"/>
						</view>
					</view>
				</view>
			</u-popup>
			
			<!-- 退换货物弹框 -->
			<!-- <u-popup v-model="showExcPop" mode="bottom" :border-radius="20">
				<view class="pt-40 pl-48 pr-48">
					<view class="d-flex j-center a-center font-36 font-wei text-3">换货原因</view>
					<view class="mt-20">
						<view class="d-flex j-sb a-center bb-s-01-eeeeee ptb-40-plr-00" v-for="(item, index) in exchangeReasonList" :key="index" @click="selectExchangeReason(item)">
							<text class="font-28 font-wei text-3">{{item}}</text>
							<vh-check :width="36" :height="36" :checked="refundReason == item" @click="selectExchangeReason(item)"/>
						</view>
					</view>
				</view>
			</u-popup> -->
			
			<!-- 提交成功弹框 -->
			<u-modal v-model="showsubmitSuccessPop" :show-title="false" content="" :width="490"
			 confirm-text="知道了" :confirm-style="{fontSize:'28rpx', color:'#999'}" @confirm="jump.redirectTo(`../after-sale-detail/after-sale-detail`)">
				<view class="pt-86 pb-64">
					<view class="d-flex j-center a-center">
						<image class="w-264 h-184" src="https://images.vinehoo.com/vinehoomini/v3/comm/succ_red.png" mode="aspectFill" />
					</view>
					
					<view class="d-flex flex-column j-center a-center mt-30 l-h-44">
						<text class="font-28 text-3">提交成功</text>
						<text class="font-28 text-3">我们将在48小时内处理</text>
					</view>
				</view>
			</u-modal>
		</view>
		
		<!-- 底部按钮 -->
		<view class="p-fixed bottom-0 w-p100 h-104 bg-ffffff d-flex j-center a-center b-sh-00021200-022">
			<u-button :disabled="!canSubmit" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
			:custom-style="{width:'646rpx', height:'80rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: !canSubmit ? '#FCE4E3' : '#E80404', border:'none' }" @click="submit">提交</u-button>
		</view>
	    
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		name: 'after-sale-apply',
		
		data(){
			return{
				serviceType: 1, //服务类型：1 = 仅退款、2 = 退款退货、3 = 换货
				addressInfo: {}, //地址信息
				showRecPop: false, //是否显示货物状态弹框
				showRefPop: false, //是否显示退款弹框
				showExcPop: false, //是否展示换货弹框
				showsubmitSuccessPop: false,//是否提交成功弹框
				cargoStatusList: [
					{
						type: 3,
						name: '未收到货',
					},
					{
						type: 4,
						name: '已收到货'
					}
				], //货物状态列表
				cargoStatusName: '', //货物状态名称
				cargoStatus: 0, //货物状态：1-未发货 2-暂存 3-未收到货/已拒收 4-已收到货
				refundReasonList: [], //退款原因列表
				refundReason: '', //仅退款、退货退款、换货原因
				refundInfo: {}, //退款信息
				// exchangeReasonList: ['污标漏夜', '破损', '发错货', '商品/赠品漏发', '与商家协商一致退款', '其他' ], //换货原因列表
				// exchangeReason: '', //换货原因
				describe:'', //描述
				uploadFileList: [], //上传完成的文件列表
				uploadImageStr:'', //补充描述图片，多张逗号分隔
				uploadVideoStr:'', //补充描述视频，多张逗号分隔
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable', 'afterSaleGoodsInfo', 'addressInfoState']),
			
			// 是否可以提交
			canSubmit() {
				if(this.serviceType == 3) { //换货
					if(this.refundReason && Object.keys(this.addressInfo).length) {
						return true
					}
					return false
				}else {
					if(this.cargoStatusName && this.refundReason) {
						return true
					}
					return false
				}
			},
			hasAfterSaleGoodsInfo({ afterSaleGoodsInfo }) {
				return !!(typeof afterSaleGoodsInfo === 'object' && Object.keys(afterSaleGoodsInfo).length)
			}
		},
		
		onLoad(options) {
			if (!this.hasAfterSaleGoodsInfo) {
				this.jump.reLaunch(`${this.routeTable.pEMyOrder}`)
				return
			}
			this.serviceType = parseInt(options.serviceType)
			this.init()
		},
		
		onShow() {
			this.getAddressList()
		},
		
		methods: {
			// 初始化
			async init() {
				this.getCargoStatus()
				await Promise.all( [this.getReasonList(), this.getAfterSaleRefundMoney() ])
			},
			
			// 获取货物状态
			getCargoStatus() { 
				const { status } = this.afterSaleGoodsInfo //售后商品信息
				if( this.serviceType == 1 ) { //仅退款
					switch( status ) {
						case 1: //未发货
						this.cargoStatus = 1
						this.cargoStatusName = '未发货'
						break;
						case 6: //暂存
						this.cargoStatus = 2
						this.cargoStatusName = '暂存'
					}
				}else { //退货退款/换货默认为已收到货（2022-07-08 需求方：龙飞）
					this.cargoStatus = 4
					this.cargoStatusName = '已收到货'
				}
			},
			
			// 获取退款原因/换货原因
			async getReasonList() {
				this.refundReason = '' //退换货原因
				this.refundReasonList = [] //退换货原因列表
				let type = ''
				if( this.serviceType == 1 ) { //仅退款
				    if( this.cargoStatus == 4 ) {
						// console.log('------------------------------我是已收到货')
						type = 7
					}else{
						// console.log('-------------------------我是未收到货')
						type = 8
					}
					// console.log( '-------------------------我是仅退款')
				}else if( this.serviceType == 2) { //退货退款
					// console.log( '-------------------------我是退货退款')
					type = 9
				}else{ //换货
					// console.log( '-------------------------我是换货')
					type = 10
				}
				
				let res = await this.$u.api.orderRelaText({ type })
				this.refundReasonList = res.data
			},
			
			// 获取售后订单可退金额
			async getAfterSaleRefundMoney() {
				let res = await this.$u.api.afterSaleRefundMoney({ sub_order_no: this.afterSaleGoodsInfo.order_no,  order_type: this.afterSaleGoodsInfo.order_type})
				this.refundInfo = res.data
			},
			
			// 获取收货地址列表
			async getAddressList() {
				if(this.serviceType == 3) { //换货
					if( Object.keys(this.addressInfoState).length ) { //如果用户从新选择了地址信息，就读选择的地址信息（从vuex取）
						this.addressInfo = this.addressInfoState
					}else{ //否则就从地址收货地址列表里面取数据，规则：有默认地址就取默认地址、没有默认地址就取数组第一项，否则就代表没有收货地址
						let res = await this.$u.api.addressList()
						if(res.data.list.length > 0) {
							for(let i = 0; i < res.data.list.length; i++) {
								if(res.data.list[i].is_default) {
									this.addressInfo = res.data.list[i]
									break
								}else{
									this.addressInfo = res.data.list[0]
								}
							}
						}else{
							this.addressInfo = {}
						}
					}
				}
			},
			
			// 选择货物状态 item = 货物状态列表每一项、index = 列表索引
			selectCargoStatus(item, index) {
				this.cargoStatusName = item.name
				this.cargoStatus = item.type
				this.getReasonList() //退换货原因列表
				this.showRecPop = false
			},
			
			// 选择退款/换货原因 content = 退款原因
			selectRefundReason( content ) {
				this.refundReason = content
				this.showRefPop = false
			},
			
			// 选择换货原因 item = 换货原因列表每一项
			// selectExchangeReason(item) {
			// 	this.exchangeReason = item
			// 	this.showExcPop = false
			// },
			
			// 申请售后
			async applyAfterSale() {
				this.feedback.loading({title:'提交中...'})
				let data = {} //申请售后需要上传的参数 
				data.sub_order_no = this.afterSaleGoodsInfo.order_no //子订单号
				data.order_type = this.afterSaleGoodsInfo.order_type, //订单类型：0-闪购 1-秒发 2-跨境 3-尾货 4-兔头实物
				data.type = this.serviceType //服务类型：1-仅退款 2-退款退货 3-换货
				data.replenish_imgs = this.uploadImageStr //补充描述图片，多张逗号分隔
				data.replenish_video = this.uploadVideoStr //补充描述短视频
				data.refund_reason = this.refundReason //退款/退货/换货原因
				data.replenish_content = this.describe //补充描述文字
				if(this.serviceType == 3){ //换货提交的数据
				    const { province_id, city_id, town_id, province_name, city_name, town_name, address, consignee, consignee_phone } = this.addressInfo //地址信息
					data.province_id = province_id //换货/补发省ID
					data.city_id = city_id //换货/补发市ID
					data.district_id = town_id //换货/补发区ID
					data.province_name = province_name //换货收货省
					data.city_name = city_name //换货收货市
					data.district_name = town_name //换货收货区
					data.address = address //换货收货详细地址
					data.consignee = consignee //换货/补发收货人姓名
					data.consignee_phone = consignee_phone //换货/补发收货人电话
				}else{ //仅退款、退货退款额外提交的数据
				    data.goods_status = this.cargoStatus //货物状态
					data.ts_time = this.refundInfo.ts_day //暂存时间(单位：天)
					data.ts_money = this.refundInfo.ts_free //暂存费用
				}
				console.log('------------------这是申请售后需要上传的数据')
				console.log(data)
				try {
					let res = await this.$u.api.applyAfterSale(data)
					this.showsubmitSuccessPop = true
				}catch(e) {
					console.log('------------------------我到了catch分支')
					console.log(e)
				}
				this.feedback.hideLoading()
			},
			
			// 上传列表发生改变
			onListChange(list) {
				console.log('-----------------------上传列表发生改变')
				this.uploadFileList = list
				this.handleUploadFiles(list)
			},
			
			// 所有上传成功
			onUploaded(list, index) {
				console.log('-------上传所有文件成功')
				this.handleUploadFiles(list)
				this.applyAfterSale()
			},
			
			// 处理上传的文件数据
			handleUploadFiles(list) {
				let imageList = list.filter(item => { return item.fileType == 'image' }).map(item=>{ return item.response })
				let videoList = list.filter(item => { return item.fileType == 'video' }).map(item=>{ return item.response })
				this.uploadImageStr = imageList.join(',')
				this.uploadVideoStr = videoList.join(',')
			},
			
			// 提交
			submit() {
				if(this.uploadFileList.length) {
					this.feedback.loading({title:'上传中...'})
					this.$refs.uUpload.upload();
				}else{
					this.applyAfterSale()
				}
			}
		}
	}
</script>

<style scoped></style>
