<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in bg-f5f5f5 pb-104">
			<!-- 导航栏 -->
			<u-navbar back-icon-color="#FFF" title="订单详情" :title-bold="true" title-color="#FFF" :background="{ background: navBackgroundColor }" />
			
			<!-- banner -->
			<image class="p-abso top-0 w-p100 h-400" src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ban.png" mode="widthFix" />
			
			<!-- 状态（待付款、待收货...） -->
			<view class="p-rela z-1 mt-n-20 ml-48 mr-48">
				<!-- 待发货 -->
				<view v-if="rabbitOrderInfo.status == 1" class="d-flex j-sb a-center">
					<view class="">
						<view class="font-36 font-wei text-ffffff">待发货</view>
						<view class="mt-08 font-28 text-ffffff">买家已付款，等待商家发货</view>
					</view>
					<image class="w-128 h-128" src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_be_ship.png" mode="aspectFill" />
				</view>
				
				<!-- 待收货 -->
				<view v-if="rabbitOrderInfo.status == 2" class="d-flex j-sb a-center">
					<view class="">
						<view class="font-36 font-wei text-ffffff">待收货</view>
						<view class="mt-08 font-28 text-ffffff">自动收货时间：{{rabbitOrderInfo.receipt_time}}</view>
					</view>
					<image class="w-128 h-128" src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_be_rec.png" mode="aspectFill" />
				</view>
				
				<!-- 已完成 -->
				<view v-if="rabbitOrderInfo.status == 3" class="d-flex j-sb a-center">
					<view class="">
						<view class="font-36 font-wei text-ffffff">已完成</view>
						<view class="mt-08 font-28 text-ffffff">订单已完成，感谢您对酒云网的支持！</view>
					</view>
					<image class="w-128 h-128" src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_comp.png" mode="aspectFill" />
				</view>
			</view>
			
			<!-- 物流状态 + 收货地址 -->
			<view class="mt-24 ml-24 mr-24">
				<!-- 物流状态 -->
				<view v-if="logisticInfo.traces && logisticInfo.traces.length" class="p-rela z-01 bg-ffffff b-rad-10 pr-24 pl-24" @click="viewLogistics()">
					<view class="d-flex j-sb a-center bb-s-01-eeeeee pt-32 pb-32">
						<view class="d-flex">
							<image class="w-44 h-44 mt-04" src="https://images.vinehoo.com/vinehoomini/v3/order_detail/rec_bla.png" mode="widthFix"></image>
							<view class="w-540 ml-16">
								<view class="font-32 font-wei text-3 text-hidden-3">{{logisticInfo.traces[0].context}}</view>
								<view class="mt-12 ml-10 font-24 text-9 l-h-34">{{logisticInfo.traces[0].ftime}}</view>
							</view>
						</view>
						<u-icon name="arrow-right" :size="24" color="#333"></u-icon>
					</view>
				</view>
				
				<!-- 收货地址 -->
				<view class="p-rela z-01 bg-ffffff b-rad-10 d-flex j-sb a-center pt-32 pr-24 pb-32 pl-24">
					<view class="d-flex">
						<image class="w-44 h-44 mt-04" src="https://images.vinehoo.com/vinehoomini/v3/order_detail/add_bla.png" mode="widthFix"></image>
						<view class="w-540 ml-16">
							<view class="">
								<text class="mr-36 font-32 font-wei text-3">{{rabbitOrderInfo.consignee}}</text>
								<text class="font-28 text-3">{{rabbitOrderInfo.consignee_phone}}</text>
							</view>
							
							<view class="mt-12 font-24 text-9 l-h-34">{{rabbitOrderInfo.province_name}} {{rabbitOrderInfo.city_name}} {{rabbitOrderInfo.district_name}} {{rabbitOrderInfo.address}}</view>
						</view>
					</view>
				</view>
			</view>
	 
			<!-- 兔头商品列表 -->
			<view class="p-rela z-01">
				<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24 pt-32 pr-20 pb-32 pl-28" v-for="(item, index) in rabbitOrderInfo.goodsInfo" :key="index" @click.stop="jump.redirectTo(`${routeTable.pBRabbitHeadGoodsDetail}?id=${item.period}`)">
					<view class="d-flex j-sb">
						<vh-image :src="item.goods_img" :width="246" :height="152" :border-radius="10" />
					
						<view class="flex-1 d-flex flex-column j-sb ml-12">
							<view class="">
								<view class="text-hidden-2">
									<vh-channel-title-icon :channel="item.periods_type" padding="2rpx 8rpx" :font-size="20"/>
									<text class="ml-12 font-24 text-0 l-h-34">{{item.goods_title}}</text>
								</view>
								<view class="mt-08">
									<text class="bg-f5f5f5 ptb-02-plr-12 b-rad-04 font-20 text-9">{{item.package_name}}</text>
								</view>
							</view>
							<view class="">
								<view class="mt-04 d-flex j-sb">
									<view class="font-22 text-6">x{{item.order_qty}}</view>
									<view class="d-flex a-center">
										<text class="font-28 font-wei text-e80404">{{item.package_price}}</text>
										<image class="w-28 h-28 ml-04" src="http://images.vinehoo.com/vinehoomini/v3/comm/rab_gold.png" mode="aspectFill" />
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 订单明细 -->
			<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<text class="font-28 font-wei text-3">订单编号</text>
					<text class="font-24 text-3" @click.stop="copy.copyText( rabbitOrderInfo.order_no )">{{rabbitOrderInfo.order_no}}</text>
				</view>

				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<text class="font-28 font-wei text-3">配送方式</text>
					<text class="font-24 text-3">默认配送</text>
				</view>

				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<text class="font-28 font-wei text-3">支付方式</text>
					<text class="font-24 text-3">兔头兑换</text>
				</view>

				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32">
					<text class="font-28 font-wei text-3">预计发货时间</text>
					<text class="font-24 text-3">{{rabbitOrderInfo.predict_time}}前</text>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<text class="font-28 font-wei text-3">下单时间</text>
					<text class="font-24 text-3">{{rabbitOrderInfo.created_time}}</text>
				</view>
			</view>
			
			<!-- 金额明细 -->
			<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="font-28 font-wei text-3">商品金额</view>
					<view class="">
						<text class="font-28 font-wei text-3">{{rabbitOrderInfo.payment_amount}}</text>
						<image class="ml-06 w-28 h-28" src="http://images.vinehoo.com/vinehoomini/v3/comm/rab_gold.png" mode="aspectFill" />
					</view>
				</view>
				<view class="d-flex j-end a-center ml-24 mr-24 pt-32 pb-32">
					<text class="font-28 text-3">共1件</text>
					<text class="ml-10 font-28 font-wei text-3">实付款：</text>
					<text class="font-32 font-wei text-e80404">{{rabbitOrderInfo.payment_amount}}</text>
					<image class="ml-06 w-28 h-28" src="http://images.vinehoo.com/vinehoomini/v3/comm/rab_gold.png" mode="aspectFill" />
				</view>
			</view>
		
			<!-- 猜你喜欢 -->
			<view class="">
				<!-- 分割线 -->
				<vh-split-line :padding-top="52" :padding-bottom="32" :margin-left="10" :margin-right="10" text="猜你喜欢" :font-bold="true" :font-size="36" text-color="#333333"
				:show-image="true" image-src="https://images.vinehoo.com/vinehoomini/v3/comm/guess_love.png" />
					   
				<!-- 猜你喜欢列表 -->
				<vh-goods-recommend-list />
			</view>
			
			<!-- 底部按钮 -->
			<view v-if="rabbitOrderInfo.status == 2 || rabbitOrderInfo.status == 3" class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022">
				<view v-if="rabbitOrderInfo.status == 2" class="h-104 d-flex j-center a-center">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
					@click="confirmReceipt">确认收货</u-button>
				</view>
				<view v-if="rabbitOrderInfo.status == 3" class=" h-104 d-flex j-end a-center pr-24">
					<view class="">
						<u-button shape="circle" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#999', border:'2rpx solid #999'}"
						@click="deleteOrder">删除订单</u-button>
					</view>
				</view>
			</view>
		</view>
	   
		<!-- 骨架屏 -->
	    <view v-else class="fade-in">
			<u-navbar back-icon-color="#FFF" title="订单详情" title-size="36" :title-bold="true" title-color="#FFF" :background="{ background: '#E80404' }" />
			<vh-skeleton :type="3" loading-color="#E80404" />
	    </view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		name: 'rabbit-head-order-detail',
		
		data() {
			return {
				loading: true, //加载状态 true = 加载中、false = 结束加载
				navBackgroundColor:'rgba(224, 20, 31, 0)', //导航栏背景
				orderNo:'', //订单编号
				rabbitOrderInfo: {}, //兔头订单信息
				logisticInfo:{}, //物流信息
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable', 'logisticsInfo']),
		},
		
		onLoad(options) {
			this.orderNo = options.orderNo
			this.init()
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muLogisticsInfo']),
			
			// 初始化
			async init() {
				await this.getOrderDetail()
				await this.getLogisticsDetail()
				this.loading = false
			},
			
			// 获取订单详情
			async getOrderDetail(){
				let res = await this.$u.api.orderDetail({order_no: this.orderNo})
				this.rabbitOrderInfo = res.data
			},
			
			// 查询物流轨迹
			async getLogisticsDetail() {
				const {order_no, express_type, express_number} = this.rabbitOrderInfo
				if( express_number ) {
					let res = await this.$u.api.logisticsDetails({
						logisticCode: express_number,
						expressType: express_type
					})
					this.logisticInfo = res.data
				}
			},
			
			// 查看物流
			viewLogistics() {
				let { goodsInfo, express_type, express_number } = this.rabbitOrderInfo
				this.muLogisticsInfo({ image: goodsInfo[0].goods_img, expressType: express_type, logisticCode: express_number })
				this.jump.navigateTo(`/packageB/pages/logistics-detail/logistics-detail`)
			},
			
			
			// 删除订单 
			deleteOrder(){
				this.feedback.showModal({
					confirm: async () => {
						try{
							await this.$u.api.cancelDeleteOrder({ type: 2, order_no: this.rabbitOrderInfo.order_no })
							this.feedback.toast({ title: '删除成功', icon: 'success' })
							setTimeout(() => { this.jump.navigateBack() }, 1500)
						}catch(e){
							
						}
					}
				})
			},
			
			// 确认收货
			async confirmReceipt() {
				console.log('======================我是确认收货')
				let data = {}
				data.sub_order_no_str = this.rabbitOrderInfo.order_no
				console.log(data)
				let res = await this.$u.api.orderReceipt(data)
				this.feedback.toast({ title: '确认成功', icon: 'success' })
				this.getOrderDetail(this.orderNo)
			},
			
			
		},
		
		onPageScroll(res) {
			res.scrollTop <= 100 ? 
			this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop/100})` : 
			this.navBackgroundColor = `rgba(224, 20, 31, 1)`
		}
	}
</script>

<style scoped></style>
