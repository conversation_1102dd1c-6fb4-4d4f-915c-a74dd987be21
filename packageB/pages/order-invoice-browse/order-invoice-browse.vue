<template>
	<view class="content">
		<vh-navbar :title="`发票预览`">
			<image v-if="invoiceImage" slot="right" class="fade-in p-24 w-44 h-44" :src="ossIcon(`/order-invoice-browse/save.png`)" 
			@click="image.saveImageToPhotosAlbum(invoiceImage)" />
		</vh-navbar>
		
		<view class="flex-c-c pt-230 pb-40" @click="image.previewImage([invoiceImage])">
			<vh-image :src="invoiceImage" :loadingType="1" :width="686" mode="widthFix"/>
		</view>
	</view>
</template>

<script>
	export default {
		data: () => ({
			invoiceImage: '',
		}),
		onLoad({ invoice_code, url }) {
			if( invoice_code && url ) {
				const deUrl = decodeURIComponent(url)
				this.load( invoice_code, deUrl )
			}
		},
		methods: {
			async load(invoice_code, url) {
				this.feedback.loading()
				const res = await this.$u.api.invoiceImage({ invoice_code, url })
				this.invoiceImage = res?.data?.url || ''
			}
		}
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>
