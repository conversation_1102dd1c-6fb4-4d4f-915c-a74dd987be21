<template>
	<view class="content">
		<vh-navbar title="开票历史" :show-border="true" />
		<view class="p-stic z-998 h-92 bg-ffffff flex-s-c bt-s-01-eeeeee pl-24" 
		:class="{ 'b-sh-00041400-012' : !popupVisible }" 
		:style="{top: navHeight + 'px'}">
			<view class="flex-c-c mr-84" v-for="(item, index) in MInvoiceHistoryFilterTypeList" :key="index"
			@click="onChangeFilterType(item)">
				<view class="font-28 text-6 mr-10" :class="{ 'text-3 font-wei' : filterType === item.value }">{{ item.text }}</view>
				<u-icon 
					:name="`arrow-${ filterType === item.value && popupVisible ? 'up' : 'down' }-fill`" 
					:size="12" 
					:color="`#${filterType === item.value ? '666' : '999'}`"
				/>
			</view>
		</view>
		
		<!-- 历史发票列表 -->
		<InvoiceHistoryList 
			:list="list" 
			:reachBottomLoadStatus="reachBottomLoadStatus"
		 />
		
		<!-- 类型 -->
		<!-- 发票类型 -->
		<InvoiceHistoryTypePopup
		  v-model="allTypeVisible"
		  :popupTop="popupTop"
		  :invoiceType="query.genre"
		  @click="onChangeInvoiceType"
		/>
		<!-- 日期 -->
		<InvoiceHistoryDatePopup 
			v-model="dateVisible"
			:range="generateTenYearsData"
			:popupTop="popupTop"
			:default-selector="[2, 2]" 
			@confirm="confirmDate" 
		/>
	
	</view>
</template>

<script>
	import { MInvoiceHistoryFilterTypeList, MInvoiceHistoryInvoiceStatusInfo } from '@/common/js/mapper/invoiceHistory/mapper'
	import { MInvoiceHistoryFilterType, MInvoiceHistoryType } from '@/common/js/mapper/invoiceHistory/model'
	import listMixin from '@/common/js/mixins/listMixin'
	export default {
		mixins: [listMixin],
		data: () => ({
			MInvoiceHistoryFilterTypeList,
			filterType: null,
			MInvoiceHistoryInvoiceStatusInfo,
			allTypeVisible: false,
			dateVisible: false,
			query: {
				genre: 0,
				start_time: '',
				end_time: ''
			}
		}),
		computed: {
			navHeight() {
				return this.system.navigationBarHeight() 
			},
			popupTop({ navHeight }) {
				return navHeight + uni.upx2px(90)
			},
			popupVisible({ allTypeVisible, dateVisible }) {
				return allTypeVisible || dateVisible
			},
			generateTenYearsData() {
				const currentYear = new Date().getFullYear()
				const startYear = currentYear - 9
				const yearArray = Array.from({ length: 10 }, (_, index) => startYear + index)
				const monthArray = Array.from({ length: 12 }, (_, index) => (index + 1 ))
				monthArray.unshift('全部')
				return [ yearArray, monthArray ]
			}
		},
		onLoad() {
			this.login.isLoginV3( this.$vhFrom ).then(isLogin => {
				if (isLogin) this.load()
			})
		},
		onShow() {
			this.changeBodyClassList()
		},
		methods: {
			async load(query) {
				const res = await this.$u.api.invoiceHistoryList(query)
				const { list = [] } = res?.data || {}
				const combList = list.map( item => Object.assign({}, MInvoiceHistoryInvoiceStatusInfo[item.status], item ))
				this.list = query.page === 1 ? combList : this.list.concat(combList)
				this.changeBodyClassList()
				return res
			},
			changeBodyClassList () {
			  this.$nextTick(() => {
			    document?.body?.classList?.[this.list.length ? 'add' : 'remove']('bg-f5f5f5')
			  })
			},
			onChangeFilterType({ value }) {
				const { allTypeVisible, dateVisible } = this.$options.data()
				this.filterType = value
				if( value === MInvoiceHistoryFilterType.All ) {
					this.dateVisible = dateVisible
					this.allTypeVisible = !this.allTypeVisible
				}else if( value === MInvoiceHistoryFilterType.InvoiceTime ) {
					this.allTypeVisible = allTypeVisible
					this.dateVisible = !this.dateVisible
				}
			},
			onChangeInvoiceType( genre ) {
				this.query.genre = genre
				this.allTypeVisible = false
				this.reload()
			},
			confirmDate(e1, e2) {
				const date = `${e1}-${e2}`
				if( e2 === '全部' ) {
					this.query.start_time = this.date.getFirstDayOfMonth(`${e1}-01`)
					this.query.end_time = `${e1}-12-31 23:59:59`
				} else {
					this.query.start_time = this.date.getFirstDayOfMonth(date)
					this.query.end_time = this.date.getLastDayOfMonth(date)
				}
				this.dateVisible = false
				this.reload()
			}
		},
		onPullDownRefresh () {
			this.pullDownRefresh()
		},
		onReachBottom () {
			this.reachBottomLoad()
		}
	}
</script>
