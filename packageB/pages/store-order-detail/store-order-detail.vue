<template>
	<view class="content">
		<!-- 数据加载完成 -->
		<view class="fade-in pb-104">
			<!-- 导航栏 -->
			<vh-navbar back-icon-color="#FFF" title="门店订单详情" title-color="#FFF" :background="{ background: navBackgroundColor }" />
			
			<!-- banner -->
			<image class="p-abso top-0 w-p100 h-400" src="https://images.vinehoo.com/vinehoomini/v3/order_detail/ban.png" mode="widthFix" />
			
			<!-- 状态（待付款、待收货...） -->
			<view class="p-rela z-1 mt-n-20 ml-48 mr-48">
				<!-- 已完成 -->
				<view class="d-flex j-sb a-center">
					<view class="">
						<view v-if="storeOrderDetail.refund_status" class="font-36 font-wei text-ffffff">{{refundStatusList[storeOrderDetail.status]}}</view>
						<view v-else class="font-36 font-wei text-ffffff">{{orderStatusList[storeOrderDetail.status].name}}</view>
						<view class="mt-08 font-28 text-ffffff">下单时间：{{storeOrderDetail.create_time}}</view>
					</view>
					<view v-if="storeOrderDetail.status" class="">
						<image class="w-128 h-128" :src="`https://images.vinehoo.com/vinehoomini/v3/order_detail/${orderStatusList[storeOrderDetail.status].img}.png`" mode="aspectFill" />
					</view>
				</view>
			</view>
			
			<!-- 收货地址 -->
			<view class="p-rela z-01 bg-ffffff b-rad-10 mt-24 ml-24 mr-24 ptb-00-plr-24">
				<view class="d-flex ptb-32-plr-00">
					<image class="w-44 h-44 mt-04" src="https://images.vinehoo.com/vinehoomini/v3/order_detail/add_bla.png" mode="widthFix" />
					
					<!-- 收货人信息 -->
					<view v-if="storeOrderDetail.send_type == 1" class="w-580 ml-16">
						<view class="">
							<text class="mr-36 font-32 font-wei text-3">{{storeOrderDetail.consignee}}</text>
							<text class="font-28 text-3">{{storeOrderDetail.cellphone}}</text>
						</view>
						<view class="mt-12 font-24 text-3 l-h-34">{{storeOrderDetail.ship_addr}}</view>
					</view>
					
					<!-- 门店信息 -->
					<view v-else class="w-580 ml-16">
						<view class="font-32 font-wei text-3">{{storeOrderDetail.store_name}}</view>
						<view class="mt-12 font-24 text-3 l-h-34">{{storeOrderDetail.address}}</view>
					</view>
				</view>
			</view>
			
			<!-- 门店商品列表 -->
			<view class="p-rela z-01 bg-ffffff b-rad-10 mt-20 ml-24 mr-24 pt-24">
				<view class=" pr-20 pb-24 pl-28" v-for="(item, index) in storeOrderDetail.goods_info" :key="index">
					<view class="d-flex j-sb">
						<vh-image :loading-type="2" :src="item.goods_images" :width="152" :height="152" :border-radius="10" mode="aspectFit"/>
					
						<view class="flex-1 d-flex flex-column j-sb ml-12">
							<view class="">
								<view class="font-24 text-0 l-h-34 text-hidden-2">{{item.goods_name}}</view>
								<view class="mt-08">
									<text class="bg-f5f5f5 ptb-02-plr-12 b-rad-04 font-20 text-9">{{item.c_name}}</text>
								</view>
							</view>
							<view class="font-22 text-6">x{{item.pay_number}}</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 订单明细 -->
			<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<text class="font-28 font-wei text-3">订单编号</text>
					<text class="font-24 text-3">{{ storeOrderDetail.status == 0 ? storeOrderDetail.mainorderno : storeOrderDetail.orderno}}</text>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee" v-if="storeOrderDetail.express_no">
					<text class="font-28 font-wei text-3">物流单号</text>
					<text class="font-24 text-3">{{storeOrderDetail.express_no}}</text>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<text class="font-28 font-wei text-3">支付方式</text>
					<text class="font-24 text-3">{{ storeOrderDetail.pay_type == 1 ? '微信支付' : '线下支付' }}</text>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<text class="font-28 font-wei text-3">配送方式</text>
					<text class="font-24 text-3">{{storeOrderDetail.send_type == 1 ? '物流配送' : storeOrderDetail.send_type == 2 ? '门店自提' : '门店场饮'}}</text>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee" v-if="storeOrderDetail.consignee">
					<text class="font-28 font-wei text-3">姓名</text>
					<text class="font-24 text-3">storeOrderDetail.consignee</text>
				</view>
			</view>
			
			<!-- 金额明细 -->
			<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="font-28 font-wei text-3">订单金额</view>
					<view class="font-28 font-wei text-e80404">¥{{storeOrderDetail.status == 0 ? storeOrderDetail.pay_money : storeOrderDetail.order_money}}</view>
				</view>
				<view v-if="storeOrderDetail.send_type == 3" class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32">
					<view class="font-28 font-wei text-3">堂饮费</view>
					<view class="font-28 font-wei text-e80404">¥{{storeOrderDetail.service_charge}}</view>
				</view>
				<view v-if="storeOrderDetail.send_type == 1" class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32">
					<view class="font-28 font-wei text-3">运费</view>
					<view class="font-28 font-wei text-e80404">¥0.00</view>
				</view>
			</view>
		
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		name: 'store-order-detail',
		
		data() {
			return {
				orderStatusList:[{ name:'待支付', img:'ord_be_pad' }, { name:'已支付', img:'ord_be_ship' }, { name:'已发货', img:'ord_be_rec' }, { name:'已完成', img:'ord_comp' }], //订单状态列表
				refundStatusList:['', '退款中', '退款成功', '退款失败'], //退款状态列表
				navBackgroundColor:'rgba(224, 20, 31, 0)', //导航栏背景
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['storeOrderDetail'])
		},

		onPageScroll(res) {
			res.scrollTop <= 100 ? 
			this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop/100})` : 
			this.navBackgroundColor = `rgba(224, 20, 31, 1)`
		}
	}
</script>

<style>
	@import "../../../common/css/page.css";
</style>
