<template>
  <view
    class="content"
    :class="loading || showDisModPop || showTempStorPop || showCouPop || showInvHeadPop ? 'h-vh-100 o-hid' : ''"
  >
    <!-- 导航栏 -->
    <vh-navbar title="确认订单" />

    <!-- 数据加载完成 -->
    <view v-if="!loading" class="fade-in bg-f5f5f5 pt-20 pb-148">
      <!-- 收货地址（有数据） -->
      <view
        v-if="addressInfo.id"
        class="bg-ffffff b-rad-10 d-flex j-sb a-center ml-24 mr-24 ptb-32-plr-24"
        @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=1`)"
      >
        <view class="w-500">
          <view class="d-flex a-center">
            <text class="font-32 font-wei text-3">{{ addressInfo.consignee }}</text>
            <text class="ml-14 font-28 text-9">{{ addressInfo.consignee_phone }}</text>
          </view>
          <view class="mt-12">
            <text
              v-if="addressInfo.is_default"
              class="bg-ff0013 b-rad-04 ptb-02-plr-08 text-ffffff font-20 font-wei l-h-28"
              >默认</text
            >
            <text
              v-if="addressInfo.label"
              class="bg-2e7bff b-rad-04 ptb-02-plr-16 text-ffffff font-20 font-wei l-h-28"
              :class="addressInfo.is_default ? 'ml-10' : ''"
              >{{ addressInfo.label }}</text
            >
            <text class="font-24 text-3 l-h-34" :class="addressInfo.is_default || addressInfo.label ? 'ml-10' : ''"
              >{{ addressInfo.province_name }} {{ addressInfo.city_name }} {{ addressInfo.town_name }}
              {{ addressInfo.address }}</text
            >
          </view>
        </view>

        <u-icon name="arrow-right" :size="24" color="#333"></u-icon>
      </view>

      <!-- 收货地址（无数据） -->
      <!-- jump.appAndMiniJump(1, `${routeTable.pEAddressManagement}?comeFrom=1`, $vhFrom) -->
      <view
        v-else
        class="fade-in p-rela h-188 b-rad-10 d-flex j-center a-center ml-24 mr-24 o-hid"
        @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=1`)"
      >
        <image
          class="p-abso z-01 w-702 h-188"
          src="http://images.vinehoo.com/vinehoomini/v3/order-confirm/add_bg.png"
          mode="aspectFill"
        ></image>
        <view class="p-rela z-02 d-flex flex-column j-center a-center">
          <image
            class="w-84 h-84"
            src="http://images.vinehoo.com/vinehoomini/v3/order-confirm/add_ico.png"
            mode="aspectFill"
          ></image>
          <text class="mt-06 font-30 text-3">新建收货地址</text>
        </view>
      </view>

      <!-- 跨境身份信息（身份证号、真实姓名） -->
      <view v-if="orderGoodsInfo.submit_type == 2" class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24 ptb-00-plr-24">
        <view class="bb-s-01-eeeeee pt-32 pb-20" @click="showCrossBorderRealInfoMod = true">
          <view class="d-flex j-sb">
            <view class="font-28 font-wei text-3">{{
              realInfoCorrect ? realName + ' ' + idCard : '请填写实名信息'
            }}</view>
            <u-icon name="arrow-right" :size="24" color="#333" />
          </view>
          <view class="mt-04 font-22 text-ff9127 l-h-32">*根据海关提出，购买跨境商品需要提交实名信息</view>
        </view>

        <view class="pt-20 pb-24 font-24 text-9">
          <text>本信息仅用于海关，我们将严格保密，查看</text>
          <text class="text-734cd2" @click="showCrossBorderNoticePop = true">《跨境购买须知》</text>
        </view>
      </view>

      <!-- 订单商品列表 -->
      <block v-for="(item, index) in orderGoodsInfo.orderGoodsList" :key="index">
        <view v-if="!item.is_add_purchase" class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24 ptb-32-plr-24">
          <vh-channel-title-icon
            :channel="item.periods_type"
            :show-title="true"
            :margin-left="12"
            :padding="'0 10rpx'"
            :fontSize="22"
            :plate="'orderConfirm'"
          />

          <view class="mt-28 d-flex">
            <view class="w-246 h-152 b-rad-06 o-hid">
              <vh-image :loading-type="2" :src="item.banner_img" :height="152" />
            </view>

            <view class="flex-1 d-flex flex-column j-sb ml-12">
              <view class="">
                <view class="font-24 text-0 l-h-34 text-hidden-2">{{ item.title }}</view>
                <view class="mt-08 font-20 text-9 l-h-28 text-hidden-1">{{ item.package_name }}</view>
              </view>
              <view class="d-flex j-sb a-center mt-08">
                <text class="font-28 text-e80404 l-h-40">¥{{ item.price }}</text>
                <text class="font-24 text-6">x{{ item.nums }}</text>
              </view>
            </view>
          </view>

          <view class="mt-32 d-flex j-sb a-center">
            <view class="font-28 font-wei text-3 l-h-40">配送</view>
            <view v-if="item.periods_type != MCommodityPeriodsType.MerchantSeconds" class="font-24 text-3 l-h-36">{{
              item.sel_dis_info.express_name
            }}</view>
            <view v-else class="d-flex a-center" @click="openDisModPop(item)">
              <!-- <text class="font-24 l-h-36" :class="item.sel_dis_info.express_fee == '0.00' ? 'text-3' : 'text-e80404'">{{item.sel_dis_info.express_name}}｜{{ item.sel_dis_info.express_fee == 0 ? '免运费' : '¥' + item.sel_dis_info.express_fee }}</text> -->
              <text class="font-24 text-3 l-h-36">{{ item.sel_dis_info.express_name }}</text>
              <view class="ml-12">
                <u-icon name="arrow-right" :size="24" color="#333"></u-icon>
              </view>
            </view>
          </view>
          <view v-if="item.goods_is_ts == 1" class="mt-30 d-flex j-sb a-center">
            <view class="d-flex a-center" @click="jump.jumpH5Agreement(agreementPrefix + '/service_TS')">
              <view class="font-28 font-wei text-3 l-h-40">是否暂存</view>
              <view class="ml-08">
                <u-icon name="question-circle" :size="28" color="#CDCDCD" />
              </view>
            </view>

            <u-switch
              v-model="item.is_checked_ts"
              :size="28"
              inactive-color="#E0E1E0"
              active-color="#E80404"
              @change="changeSwitchStatus($event, index)"
            />
          </view>

          <!-- 是否冷链 -->
          <view
            v-if="
              item.periods_type != MCommodityPeriodsType.MerchantSeconds &&
              item.dis_list.length > 1 &&
              item.dis_list.some((item) => item.express_type === MorderConfirmExpressType.ColdChain)
            "
            class="mt-20 flex-sb-c"
          >
            <view class="font-28 font-wei text-3 l-h-40">{{ priceInfo.express_title }}</view>
            <u-switch
              v-model="item.$is_checked_cold_chain"
              :size="28"
              inactive-color="#E0E1E0"
              active-color="#E80404"
              @change="changeColdChainSwitchStatus($event, index)"
            />
          </view>
          <view class="mt-08 font-24 text-6">预计发货时间：{{ item.predict_time }}</view>
          <view class="mt-08 font-24 text-6" v-if="item.is_ts && item.ts_time && item.goods_is_ts"
            >暂存至 {{ item.ts_time }}</view
          >
        </view>
      </block>

      <!-- 顺收购 -->
      <OrderConfirmAddPurchase
        v-if="purchasedList.length && orderGoodsInfo.submit_type !== 2"
        :list="purchasedList"
        @click="onChangeAddPurchasedItem"
      />

      <!-- 商品金额明细 -->
      <view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
        <view class="ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee font-28 text-9">温馨提示：不支持7天无理由退货</view>

        <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
          <text class="font-28 font-wei text-3">商品金额</text>
          <text class="font-28 font-wei text-3">¥{{ priceInfo.total_money ? priceInfo.total_money : '0.00' }}</text>
        </view>

        <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
          <text class="font-28 font-wei text-3">运费</text>
          <text class="font-24 font-wei text-3">¥{{ priceInfo.express_fee ? priceInfo.express_fee : '0.00' }}</text>
        </view>

        <!-- 跨境商品也需要显示平台优惠、满减、优惠券 2022-07-08 需求方：龙飞）-->
        <view class="">
          <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
            <text class="font-28 font-wei text-3">平台优惠</text>
            <text class="font-24 font-wei text-e80404"
              >-¥{{ priceInfo.preferential_reduction ? priceInfo.preferential_reduction : '0.00' }}</text
            >
          </view>

          <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
            <text class="font-28 font-wei text-3">满减</text>
            <text class="font-24 font-wei text-e80404"
              >-¥{{ priceInfo.money_off_value ? priceInfo.money_off_value : '0.00' }}</text
            >
          </view>

          <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee" @click="openCouponPop">
            <view class="">
              <text class="font-28 font-wei text-3">优惠券</text>
              <text
                v-if="recommendedCouponId && recommendedCouponId == canUseSelCouId"
                class="b-s-01-ff6f6f b-rad-06 ml-08 ptb-02-plr-08 font-20 text-e80404"
                >已选推荐优惠</text
              >
            </view>

            <view class="d-flex a-center">
              <view class="font-24 font-wei text-e80404"
                >-¥{{ priceInfo.coupon_value ? priceInfo.coupon_value : '0.00' }}</view
              >
              <view class="ml-08">
                <u-icon name="arrow-right" :size="24" color="#666"></u-icon>
              </view>
            </view>
          </view>
        </view>

        <view class="d-flex j-end a-center ml-24 mr-24 pt-32 pb-32">
          <text class="font-28 font-wei text-3">合计：</text>
          <text class="font-32 font-wei text-e80404">¥{{ priceInfo.payment_amount }}</text>
        </view>
      </view>

      <!-- 发票 （跨境商品不支持发票 2022-07-08 需求方：龙飞） -->
      <view v-if="orderGoodsInfo.submit_type !== 2" class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
        <!-- 发票 -->
        <view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee" @click="openShowPop">
          <view class="font-28 font-wei text-3">发票</view>
          <view class="d-flex a-center">
            <view class="w-472 text-right font-24 text-hidden-1" :class="invoiceSelId ? 'text-3' : 'text-9'">{{
              invoiceSelId ? invoiceInfo.invoice_name : '不开发票'
            }}</view>
            <view class="ml-16">
              <u-icon name="arrow-right" :size="24" color="#666" />
            </view>
          </view>
        </view>

        <!-- 开票公司 -->
        <sp-invoice-company @openInvoiceDetail="showInvoiceDetailPop = true" :list="invoiceDetailList" />

        <!-- 发票须知 -->
        <view class="ml-24 mr-24 pt-32 pb-32">
          <view class="font-28 font-wei text-3">发票须知</view>

          <view class="bg-f6f6f6 b-rad-10 mt-20 p-24">
            <view class="font-24 text-6">1、开票金额为用户实际支付金额</view>
            <view class="font-24 text-6">2、电子发票会在确认收货后发送到您的邮箱中</view>
          </view>
        </view>
      </view>

      <!-- 联系客服 -->
      <view class="d-flex j-center mt-44">
        <text class="font-24 text-9">如有特殊需求，请</text>
        <text class="font-24 text-2e7bff" @click="system.customerService($vhFrom)">联系客服</text>
        <text class="font-24 text-9">。</text>
      </view>

      <!-- 结算按钮 -->
      <view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-32 pr-24">
        <view class="d-flex a-center">
          <text class="font-28 font-wei text-3">{{ specialType === 4 ? '订金' : '合计' }}：</text>
          <text class="font-40 font-wei text-e80404"><text class="font-28">¥</text>{{ priceInfo.payment_amount }}</text>
        </view>

        <view class="">
          <u-button
            :disabled="!loadingUpdateDataEnd"
            shape="circle"
            :hair-line="false"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{
              width: '208rpx',
              height: '64rpx',
              fontSize: '28rpx',
              fontWeight: 'bold',
              color: '#FFF',
              backgroundColor: !loadingUpdateDataEnd ? '#FCE4E3' : '#E80404',
              border: 'none',
            }"
            @click="createOrder"
            >结算{{ settlementQuantity }}</u-button
          >
        </view>
      </view>

      <!-- 弹框 -->
      <view class="">
        <!-- 跨境商品购买须知弹框 -->
        <u-popup
          v-model="showCrossBorderNoticePop"
          :z-index="10076"
          mode="center"
          :mask-close-able="false"
          height="80%"
          :border-radius="10"
        >
          <view class="w-582 h-p100 d-flex flex-column">
            <view class="ptb-00-plr-40 pt-40 o-hid-y">
              <scroll-view scroll-y="true" class="h-p100">
                <view class="d-flex j-center a-center font-32 font-wei text-3">跨境商品购买须知</view>
                <view class="pt-32 pb-32 l-h-50 font-28 text-3">
                  <view v-for="(notice, noticeIndex) in crossBorderNotices" :key="noticeIndex">
                    <view class="font-30">{{ notice.title }}</view>
                    <view v-for="(desc, descIndex) in notice.list" :key="descIndex">{{ desc }}</view>
                  </view>
                </view>
              </scroll-view>
            </view>
            <view class="d-flex j-center pt-20 pb-20 b-sh-00021200-022">
              <u-button
                shape="circle"
                :hair-line="false"
                :ripple="true"
                ripple-bg-color="#FFF"
                :custom-style="{
                  width: '440rpx',
                  height: '64rpx',
                  fontSize: '28rpx',
                  fontWeight: 'bold',
                  color: '#FFF',
                  backgroundColor: '#E80404',
                  border: 'none',
                }"
                :throttleTime="0"
                @click="showCrossBorderNoticePop = false"
                >我知道了</u-button
              >
            </view>
          </view>
        </u-popup>

        <!-- 跨境实名信息弹框 -->
        <u-modal
          v-model="showCrossBorderRealInfoMod"
          :show-title="false"
          content=""
          :width="606"
          :show-cancel-button="true"
          cancel-text="取消"
          :cancel-style="{ fontSize: '32rpx', color: '#999' }"
          confirm-text="保存"
          :confirm-style="{ fontSize: '32rpx', fontWeight: 'bold', color: '#CA101A' }"
          @cancel="cancelRealInfo"
          @confirm="confirmRealInfo"
        >
          <view class="">
            <view class="bb-s-01-eeeeee pt-48 pb-40 text-center">
              <view class="font-32 font-wei text-3">填写实名信息</view>
              <view class="mt-24 ptb-00-plr-60 font-26 text-ff9127"
                >*根据海关规定，您需要填写与付款账号一致的实名信息</view
              >
            </view>

            <view class="p-60">
              <view class="d-flex j-sb a-center">
                <text class="font-30 text-3">姓名</text>
                <input
                  class="w-330 bg-fafafa b-rad-04 ptb-10-plr-24 font-28 text-3"
                  v-model="realName"
                  type="text"
                  placeholder="付款人真实姓名"
                  placeholder-style="color:#999;font-size:28rpx;"
                />
              </view>

              <view class="d-flex j-sb a-center mt-40">
                <text class="font-30 text-3">身份证</text>
                <input
                  class="w-330 bg-fafafa b-rad-04 ptb-10-plr-24 font-28 text-3"
                  v-model="idCard"
                  type="idcard"
                  placeholder="付款人真实身份证号"
                  placeholder-style="color:#999;font-size:28rpx;"
                />
              </view>
            </view>
          </view>
        </u-modal>

        <!-- 配送方式 -->
        <u-popup v-model="showDisModPop" mode="bottom" :border-radius="20">
          <view class="pl-40 pr-40">
            <view class="mt-40 text-center font-36 font-wei text-0">配送方式</view>
            <view
              class="d-flex j-sb a-center pt-48 pb-48 bb-s-01-eeeeee"
              v-for="(item, index) in distributeTypeList"
              :key="index"
              @click="changeDisMod(item)"
            >
              <text class="font-32 text-3 l-h-36">{{ item.express_name }}</text>
              <!-- <text class="font-32">{{item.express_name}}｜<text :class="item.express_fee == '0.00' ? 'text-3' : 'text-e80404'">{{item.express_fee == '0.00' ? '免运费' : '补贴价¥' + item.express_fee}}</text></text> -->
              <vh-check :checked="item.is_selected == 1" @click="changeDisMod(item)"></vh-check>
            </view>
          </view>
        </u-popup>

        <!-- 暂存弹框（版本2） -->
        <view
          class="tran-2 p-fixed top-0 right-0 bottom-0 left-0 op-000"
          :class="{ 'op-100': showTempStorPop }"
          hover-stop-propagation
          :style="[TempMaskStyle]"
          @click="isAccTemAgree(0)"
        >
          <view class="h-p100 d-flex j-center a-center">
            <view class="p-rela w-582 bg-ffffff b-rad-10 o-hid" @click.stop>
              <view class="d-flex j-center mt-40 font-32 font-wei text-3">酒云网暂存服务协议</view>
              <view class="mt-32 mr-40 ml-46 pb-156">
                <scroll-view
                  id="temp-scroll-con"
                  style="max-height: 788rpx"
                  scroll-y="true"
                  :show-scrollbar="false"
                  @scrolltolower="scrollReadComplete"
                >
                  <view id="temp-text-con" class="font-28 text-6 l-h-40">
                    <u-parse :html="tempStorText" :show-with-animation="true" @linkpress="linkpressJump" />
                  </view>
                </scroll-view>
              </view>
              <view
                v-if="tempStorText"
                class="p-abso bottom-0 z-100 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-center a-center"
              >
                <view v-if="isReadTempStorageComplete" class="fade-in">
                  <u-button
                    shape="circle"
                    :hair-line="false"
                    :ripple="true"
                    ripple-bg-color="#FFF"
                    :custom-style="{
                      width: '440rpx',
                      height: '64rpx',
                      fontSize: '28rpx',
                      color: '#FFF',
                      backgroundColor: '#E80404',
                      border: 'none',
                    }"
                    @click.stop="isAccTemAgree(1)"
                    >同意本条款，接受并暂存</u-button
                  >
                </view>
                <view
                  v-else
                  class="fade-in w-440 h-64 bg-dddddd b-rad-32 d-flex j-center a-center font-28 text-ffffff"
                  @click.stop="isAccTemAgree(0)"
                  >请上滑看完本条款再同意</view
                >
              </view>
            </view>
          </view>
        </view>

        <!-- 冷链弹框 -->
        <OrderConfirmColdChainMask
          ref="childRef"
          :show="showColdChainPop"
          :text="coldChainText"
          :title="clodTitle"
          :readComplete="isReadColdChainComplete"
          @agree="isAccColdAgree"
          @scrolltolower="isReadColdChainComplete = true"
        />

        <OrderConfirmCouponPopup
          v-model="showCouPop"
          :canUseCouList="canUseCouList"
          :canNotUseCouList="canNotUseCouList"
          :canUseSelCouId="canUseSelCouId"
          @changeCouId="changeCouId"
        ></OrderConfirmCouponPopup>

        <InvoiceRadioGroupPopup
          ref="invoiceRadioGroupPopupRef"
          v-model="showInvHeadPop"
          :invoiceId="invoiceSelId"
          @change="onChangeInvoiceInfo"
          @find="onFindInvoiceInfo"
        />

        <!-- 发票明细弹框 -->
        <sp-invoice-detail-popup v-model="showInvoiceDetailPop" :list="invoiceDetailList" />

        <OrderConfirmDepositPromptMask :show="showDepositPromptMask" @close="showDepositPromptMask = false" />

        <!-- 支付方式弹出层 -->
        <u-popup
          v-model="paymentPopupShow"
          mode="bottom"
          border-radius="20"
          :safe-area-inset-bottom="true"
        >
          <view class="payment-popup">
            <!-- 金额显示 -->
            <view class="popup-amount">
              <text class="amount-symbol">¥</text>
              <text class="amount-value">{{ priceInfo.payment_amount || '0.00' }}</text>
            </view>

            <!-- 支付方式列表 -->
            <view class="popup-pay-list">
              <!-- 储值账户支付 -->
              <view
                class="popup-pay-item"
                :class="{
                  'selected': balanceSelected,
                  'disabled': balanceDisabled
                }"
                @click="handleBalanceSelect"
              >
                <view class="pay-item-left">
                  <view class="pay-icon balance-icon">
                    <text class="icon-text">储</text>
                  </view>
                  <view class="pay-info">
                    <text class="pay-name">储值账户支付</text>
                    <text class="pay-desc">（余额 ¥{{ totalBalance.toFixed(2) }}）</text>
                  </view>
                </view>
                <view class="pay-item-right">
                  <image
                    :src="ossIcon(`${balanceSelected ? '/payment/cir_sel_h_36.png' : '/payment/cir_sel_40.png'}`)"
                    :class="balanceSelected ? 'w-36 h-36' : 'w-40 h-40'"
                  />
                </view>
              </view>

              <!-- 华为支付 -->
              <view
                class="popup-pay-item"
                :class="{ 'selected': huaweiSelected }"
                @click="handleHuaweiSelect"
              >
                <view class="pay-item-left">
                  <view class="pay-icon huawei-icon">
                    <text class="icon-text">华</text>
                  </view>
                  <text class="pay-name">华为支付</text>
                </view>
                <view class="pay-item-right">
                  <image
                    :src="ossIcon(`${huaweiSelected ? '/payment/cir_sel_h_36.png' : '/payment/cir_sel_40.png'}`)"
                    :class="huaweiSelected ? 'w-36 h-36' : 'w-40 h-40'"
                  />
                </view>
              </view>
            </view>

            <!-- 立即支付按钮 -->

            <view class="flex-c-c mt-32">
			<u-button shape="circle" :loading="paying" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
			:custom-style="{width:'678rpx', height:'72rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="confirmPayment">{{ paying ? '支付中...' : '支付' }}</u-button>
		</view>
          </view>
        </u-popup>
      </view>
      <u-select
        :mask-close-able="false"
        v-model="tsConfirm"
        mode="mutil-column-auto"
        :list="tsTimeList"
        @cancel="cancelTsTime"
        @confirm="confirmTsTime"
        :default-value="defaultTsValue"
        title="请选择暂存时间"
      ></u-select>
    </view>

    <!-- 骨架屏 -->
    <vh-skeleton v-else :type="2" loading-color="#2E7BFF" />
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import crossBorderNotices from '@/common/js/utils/crossBorderNotices'
import { MorderConfirmExpressType } from '@/common/js/mapper/order/model.js'
import { MCommodityPeriodsType } from '@/common/js/mapper/commodity/model.js'
export default {
  name: 'order-confirm',

  data() {
    return {
      pickerParams: {
        year: true,
        month: true,
        day: true,
        minDate: new Date(new Date().setDate(new Date().getDate() + 1)).getTime(),
        maxDate: null,
      },
      paying: false, //是否处于支付中
      MorderConfirmExpressType,
      MCommodityPeriodsType,
      crossBorderNotices,
      tsConfirm: false,
      defaultTsValue: [], // 添加默认值数据
      loading: true, //加载状态 true = 加载中、false = 结束加载
      loadingUpdateDataEnd: false, //加载更新数据是否结束 （选择地址...）
      addressInfo: {}, //地址信息
      orderGoodsIndex: 0, //订单商品索引
      hasGotPriceInfo: 0, //是否获取过价格信息
      priceInfo: {}, //金额信息
      idCard: '', //身份证号
      realName: '', //真实姓名
      showCrossBorderNoticePop: false, //跨境商品购买须知弹框
      showCrossBorderRealInfoMod: false, //跨境实名信息弹框
      showDisModPop: false, //是否展示配送方式弹框
      distributeSelGoodsInfo: {}, //选中的配送商品信息
      distributeTypeList: [], //配送方式列表
      showTempStorPop: false, //是否展示暂存弹框
      hasGotTempText: false, //是否获取过暂存文本
      tempStorText: '', //暂存文本
      isReadTempStorageComplete: false, //是否阅读完暂存
      showCouPop: true, //是否展示优惠券弹框
      canUseCouList: [], //可以使用的优惠券列表
      couponType: 0, //优惠券类型
      canUseSelCouId: 0, //选中的优惠券Id 默认为0
      invoiceDetailList: [], //开票明细列表
      showInvoiceDetailPop: false, //是否显示发票明细弹框
      recommendedCouponId: 0, //推荐优惠券id
      isUseCou: 1, //是否使用优惠券：0-否 1-是
      canNotUseCouList: [], //不可使用的优惠券列表
      showInvHeadPop: false, //是否展示发票抬头弹框
      invoiceList: [], //订单可开票列表
      invoiceInfo: {}, //订单发票信息
      invoiceSelId: 0, //选中的发票id

      // 顺手购板块
      purchasedList: [],
      defaultSubmitType: 0,

      // 冷链板块
      showColdChainPop: false, //是否展示冷链
      hasGotColdChainText: false, //是否获取过冷链文本
      coldChainText: '', //暂存文本
      clodTitle:'',//标题
      isReadColdChainComplete: false, //是否阅读完冷链

      // 订金板块
      showDepositPromptMask: false,
      tsTimeList: [], // 可选时间列表

      // 支付弹出层相关
      paymentPopupShow: false, // 支付弹出层显示状态
      balanceSelected: false, // 储值账户是否选中
      huaweiSelected: true, // 华为支付是否选中（默认选中）
      balanceInfo: {
        recharge_balance: 0,
        bonus_balance: 0
      },
      lastOrderResult: null, // 保存最后的订单结果
    }
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable', 'orderGoodsInfo', 'addressInfoState', 'agreementPrefix']),

    // 获取提交类型
    getSubmitType({ orderGoodsInfo: { submit_type } }) {
      return submit_type
    },

    // 显示跨境实名信息
    realInfoCorrect() {
      if (this.$u.trim(this.realName, 'all') !== '' && this.$u.test.idCard(this.idCard)) {
        return true
      }
      return false
    },

    // 是否含有商家秒发商品
    hasBusGoods() {
      const { orderGoodsList } = this.orderGoodsInfo //拿到订单商品信息
      const hasBusGoods = orderGoodsList.some((v) => v.periods_type === 9) //是否含有商家秒发商品
      if (hasBusGoods) {
        return true
      }
      return false
    },

    // 支持加购商品
    supportAddPurchasedGoods({ hasBusGoods, orderGoodsInfo }) {
      const { special_type = 0, submit_type } = orderGoodsInfo
      if (special_type === 0 && [0, 1, 4].includes(submit_type)) return true
      return false
    },

    // 暂存遮罩层样式
    TempMaskStyle() {
      let style = {}
      style.backgroundColor = 'rgba(0, 0, 0, 0.6)'
      if (this.showTempStorPop) style.zIndex = this.$u.zIndex.mask
      else style.zIndex = -1
      style.transition = `all 0.3s ease-in-out`
      return style
    },
    hasOrderGoodsInfo({ orderGoodsInfo }) {
      return !!(typeof this.orderGoodsInfo === 'object' && orderGoodsInfo?.orderGoodsList?.length)
    },
    specialType({ orderGoodsInfo: { special_type } }) {
      return special_type
    },
    payPlate({ specialType }) {
      if (specialType === 4) return 6
      return 0
    },
    settlementQuantity({ orderGoodsInfo }) {
      const sum = orderGoodsInfo.orderGoodsList.reduce((total, item) => total + (item.nums || 0), 0)
      return sum ? `（${sum}）` : ``
    },

    // 支付弹出层相关计算属性
    // 计算总余额
    totalBalance() {
      return this.balanceInfo.recharge_balance + this.balanceInfo.bonus_balance;
    },
    // 储值账户是否禁用
    balanceDisabled() {
      return this.totalBalance <= 0;
    }
  },

  onLoad() {
    const orderInfo = uni.getStorageSync('nextorderGoodsInfo')
    if (orderInfo) {
      this.muOrderInfo(orderInfo)
      uni.removeStorageSync('nextorderGoodsInfo')
    }

    console.log('orderGoodsInfo', JSON.stringify(this.orderGoodsInfo))
    if (!this.hasOrderGoodsInfo) return
    this.system.setNavigationBarBlack()
    this.init()
  },

  onShow() {
    if (!this.hasOrderGoodsInfo) {
      this.jump.reLaunch('/')
      return
    }
    this.loadingUpdateDataEnd = false
    this.getAddressList()
  },

  methods: {
    // Vuex mapMutations辅助函数
    ...mapMutations(['muPayInfo', 'muOrderInfo']),
    openCouponPop() {
      this.showCouPop = true
      this.getOrderCoupon()
    },
    // 初始化请求（接口聚合）
    async init() {
      this.getRealInfo()
      this.getDefaultPredictTime()
      this.defaultSubmitType = this.orderGoodsInfo.submit_type
      await Promise.all([this.getAddPurchasedList()])
    },

    // 获取真实信息
    getRealInfo() {
      if (this.getSubmitType === 2) {
        const realInfo = uni.getStorageSync('realInfo') || {}
        if (Object.keys(realInfo).length) {
          const { realName, idCard } = realInfo
          this.realName = realName
          this.idCard = idCard
        }
      }
    },

    // 获取默认预计发货时间
    getDefaultPredictTime() {
      this.orderGoodsInfo.orderGoodsList.map((item) => {
        item.default_predict_time = item.predict_time
        return item
      })
    },
    openShowPop() {
      this.showInvHeadPop = true
      this.$refs?.invoiceRadioGroupPopupRef?.load()
    },
    // 获取收货地址列表
    async getAddressList() {
      if (Object.keys(this.addressInfoState).length) {
        //如果用户从新选择了地址信息，就读选择的地址信息（从vuex取）
        this.addressInfo = this.addressInfoState
      } else {
        //否则就从地址收货地列表里面取数据，规则：有默认地址就取默认地址、没有默认地址就取数组第一项，否则就代表没有收货地址
        let res = await this.$u.api.addressList()
        if (res.data.list.length > 0) {
          for (let i = 0; i < res.data.list.length; i++) {
            if (res.data.list[i].is_default) {
              this.addressInfo = res.data.list[i]
              break
            } else {
              this.addressInfo = res.data.list[0]
            }
          }
        } else {
          this.addressInfo = {}
        }
      }
      this.getPrice()
    },

    // 跨境真实信息板块
    // 取消真实信息
    cancelRealInfo() {
      console.log('-----------我是取消真实信息')
      if (!this.realInfoCorrect) {
        this.realName = ''
        this.idCard = ''
      }
    },

    // 确认真实信息
    confirmRealInfo() {
      console.log('-----------我是确认真实信息')
      if (this.$u.trim(this.realName, 'all') == '') {
        this.showCrossBorderRealInfoMod = true
        return this.feedback.toast({ title: '请输入真实姓名~' })
      } else if (this.$u.trim(this.idCard, 'all') == '') {
        this.showCrossBorderRealInfoMod = true
        return this.feedback.toast({ title: '请输入身份证号~' })
      } else if (!this.$u.test.idCard(this.idCard)) {
        this.showCrossBorderRealInfoMod = true
        return this.feedback.toast({ title: '身份证格式有误~' })
      }
      uni.setStorage({
        key: 'realInfo',
        data: { realName: this.realName, idCard: this.idCard },
        success: () => {
          this.feedback.toast({ title: '保存成功', icon: 'success', duration: 1000 })
          this.showCrossBorderRealInfoMod = false
        },
      })
    },

    // 打开配送方式弹框 item = 订单商品列表每一项
    openDisModPop(item) {
      this.distributeTypeList = item.dis_list
      this.distributeSelGoodsInfo = item
      this.showDisModPop = true
    },

    // 改变开关状态 event = 原生改变状态事件、index = 列表索引
    async changeSwitchStatus(event, index) {
      try {
        const { orderGoodsList } = this.orderGoodsInfo
        // 请求暂存文本协议
        if (!this.hasGotTempText) {
          this.feedback.loading()
          let res = await this.$u.api.orderRelaText({ type: 3 })
          this.tempStorText = res.data[0].content
          this.hasGotTempText = true
        }
        if (event) {
          this.showTempStorPop = true
        } else {
          orderGoodsList[index].is_ts = 0
          orderGoodsList[index].predict_time = orderGoodsList[index].default_predict_time
          this.getPrice()
        }
        this.orderGoodsIndex = index

        uni
          .createSelectorQuery()
          .in(this)
          .select('#temp-text-con')
          .boundingClientRect((res2) => {
            uni.upx2px(788) < res2.height
              ? (this.isReadTempStorageComplete = false)
              : (this.isReadTempStorageComplete = true)
          })
          .exec()
      } catch (e) {
        this.feedback.toast({ title: '出了点小意外~' })
      }
    },

    // 改变冷链开关状态
    async changeColdChainSwitchStatus(event, index) {
      try {
        // 请求暂存文本协议
        if (!this.hasGotColdChainText) {
          this.feedback.loading()
          let res = await this.$u.api.orderRelaText({ type: 16, is_cross: this.getSubmitType === 2 ? 1 : 0 })
          this.coldChainText = res.data[0].content
          this.clodTitle = res.data[0].title
          this.hasGotColdChainText = true
        }
        if (event) {
          this.showColdChainPop = true
        } else {
          const { orderGoodsList } = this.orderGoodsInfo
          orderGoodsList[index].$is_checked_cold_chain = event
          orderGoodsList[index].express_type = MorderConfirmExpressType.Default
          orderGoodsList[index].predict_time = orderGoodsList[index].default_predict_time
          this.getPrice()
        }
        this.orderGoodsIndex = index
        if (!this.isReadColdChainComplete) {
          this.$nextTick(() => {
            const childRef = this?.$refs?.childRef
            uni
              .createSelectorQuery()
              .in(childRef)
              .select('#cold-text-con')
              .boundingClientRect((res2) => {
                uni.upx2px(788) < res2.height
                  ? (this.isReadColdChainComplete = false)
                  : (this.isReadColdChainComplete = true)
              })
              .exec()
          })
        }
      } catch (e) {
        this.feedback.toast({ title: '出了点小意外~' })
      }
    },

    // 链接跳转
    linkpressJump(e) {
      e.ignore()
      const trimHref = this.$u.trim(e.href, 'all')
      this.jump.jumpH5Agreement(trimHref)
    },

    // 修改配送方式 item = 配送方式的每一项
    changeDisMod(item) {
      console.log('changeDisMod')
      console.log(item)
      const { period, periods_type, package_id } = this.distributeSelGoodsInfo //拿到商品信息（订单商品列表某一项）
      this.distributeTypeList.forEach((v) => {
        //更改快递方式列表选中状态
        v.express_name == item.express_name ? (v.is_selected = 1) : (v.is_selected = 0)
      })
      this.orderGoodsInfo.orderGoodsList.map((v) => {
        //更改快递方式
        if (v.period == period && v.package_id == package_id) {
          v.express_type = item.express_type
          v.sel_dis_info = item

          // 2022-09-14:冷链订单-除跨境外的订单如果选择冷链发货，发货时间需要提醒到周三和周六 需求方：龙飞
          if (periods_type !== 2 && item.express_type === 3) {
            v.predict_time = item.predict_time
          } else {
            v.predict_time = v.default_predict_time //用默认的预计发货时间
          }
        }
        return v
      })
      this.showDisModPop = false

      this.getPrice()
    },

    // 获取优惠券列表
    async getOrderCoupon() {
      // 2022-07-27 所有板块都需要请求优惠券列表 需求方：龙飞
      let itemList = []
      this.orderGoodsInfo.orderGoodsList.forEach((v) => {
        let item = {}
        item.id = v.period
        item.package_id = v.package_id
        item.nums = v.nums
        item.price = v.price
        // item.type = v.periods_type == 0 ? 1002 : v.periods_type == 1 ? 1003 : 0
        itemList.push(item)
      })
      console.log(itemList)
      try {
        let res = await this.$u.api.getCouponListByOrder({
          type: 1000,
          items_info: itemList,
          total_discount: this.priceInfo.total_discount,
        })
        // this.canUseCouList = [...res.data.in, ...res.data.in, ...res.data.in]
        // // res.data.notIn
        // this.canNotUseCouList = [...res.data.in, ...res.data.in, ...res.data.in]
        this.canUseCouList = res.data.in
        this.canNotUseCouList = res.data.notIn
        console.log(res)
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 改变选中的优惠券id item = 优惠券列表每一项
    changeCouId(item) {
      let { id, coupon_type } = item
      this.couponType = coupon_type
      if (this.canUseSelCouId == id) {
        this.canUseSelCouId = 0
        this.isUseCou = 0
      } else {
        this.canUseSelCouId = id
        this.isUseCou = 1
      }
      // this.canUseSelCouId == id ? this.canUseSelCouId = 0 : this.canUseSelCouId = id
      this.getPrice()
    },

    // 获取金额
    async getPrice() {
      let { submit_type, orderGoodsList, special_type } = this.orderGoodsInfo //订单信息
      let { province_id, city_id, town_id } = this.addressInfo //地址信息
      let data = {
        submit_type, //订单提交类型：0-闪购 1-秒发 2-跨境 3-尾货 4-闪购+秒发混合
        items_info: orderGoodsList.map((item) => ({
          ...item,
          predict_time: item.default_predict_time,
        })), //商品信息
        special_type, //特殊类型：0-普通 1-拼团 2-新人
        is_use_coupon: this.isUseCou, //是否使用优惠券：0-否 1-是
        province_id, //省id
        city_id, //市id
        district_id: town_id, //区id
      }
      if (this.couponType == 3001) {
        //快递优惠券
        data.express_coupon_id = this.canUseSelCouId //快递优惠券ID
        data.is_use_coupon = 0 //是否使用优惠券：0-否 1-是
      } else {
        //普通优惠券
        data.coupon_id = this.canUseSelCouId //普通优惠券ID
        data.is_use_coupon = this.isUseCou //是否使用优惠券：0-否 1-是
      }

      try {
        if (this.hasGotPriceInfo) this.feedback.loading()
        let res = await this.$u.api.calculateOrderMoney(data)
        this.priceInfo = res.data
        if (!this.hasGotPriceInfo) {
          const { coupon_id, is_tip, tip_title, tip_msg } = this.priceInfo
          this.canUseSelCouId = coupon_id
          this.recommendedCouponId = coupon_id
          console.log('canUseSelCouId', this.canUseSelCouId)
          if (this.specialType === 4) {
            this.showDepositPromptMask = true
          } else {
            if (is_tip) {
              //是否需要弹窗提示：0-否 1-是
              this.feedback.showModal({ title: tip_title, content: tip_msg })
            }
          }
        }
        this.changeDefaultOrderGoodsInfoFromPriceInfo()
        // await this.getInvoiceDetailList()
        await Promise.all([
          this.getInvoiceDetailList(),
          // this.getOrderCoupon(),
        ])
        this.hasGotPriceInfo = 1
        this.loading = false
        this.loadingUpdateDataEnd = true
        this.showCouPop = false
        return res
      } catch (e) {
        return e
        // setTimeout(()=> {
        // 	this.jump.navigateBack()
        // }, 1500)
        //TODO handle the exception
      }
    },

    // 从计算金额获取数据修改默认数据
    changeDefaultOrderGoodsInfoFromPriceInfo() {
      for (let i of this.orderGoodsInfo.orderGoodsList) {
        for (let j of this.priceInfo.items_info) {
          if (i.period == j.period && i.package_id == j.package_id) {
            let selDisList = []
            selDisList = j.express_info.filter((v) => {
              return v.is_selected == 1
            })
            i.express_type = selDisList[0].express_type
            i.dis_list = j.express_info
            i.sel_dis_info = selDisList[0]
            i.goods_is_ts = j.goods_is_ts //需要拿到计算金额返回的是否支持暂存同步
            i.goods_total_money = j.goods_money // 商品价格（单价 * 数量）
            // 2022-09-14:冷链订单-除跨境外的订单如果选择冷链发货，发货时间需要提醒到周三和周六 需求：龙飞
            if (selDisList.length && selDisList.length === 1) {
              if (selDisList[0].express_type === 3) {
                i.predict_time = selDisList[0].predict_time
              }
            }
            break
          } else {
            i.sel_dis_info = {}
          }
        }
      }
    },

    // 获取发票列表
    async getInvoiceList() {
      let res = await this.$u.api.invoiceList()
      let { list } = res.data
      console.log('-=============================================')
      console.log(res)
      if (list.length) {
        this.invoiceList = list
      } else {
        // this.jump.navigateTo(`/packageE/pages/invoice-head-add/invoice-head-add`)
      }
    },

    // 获取发票明细列表
    async getInvoiceDetailList() {
      // if( this.invoiceSelId ) {
      const { orderGoodsList, submit_type } = this.orderGoodsInfo //订单信息
      if (submit_type === 2) return
      const items_info = orderGoodsList.map(({ period, package_id, goods_total_money }) => ({
        period,
        package_id,
        goods_money: goods_total_money,
      }))
      const { data } = await this.$u.api.orderInvoiceDetailList({ items_info })
      this.invoiceDetailList = data
      // }
    },

    // 监听滚动阅读完暂存协议
    scrollReadComplete() {
      this.isReadTempStorageComplete = true
    },

    // 是否接受暂存协议 type = 类型 0 = 未点击同意本条款（遮罩、叉叉、未滚动到底的）、1 = 点击了同意本条款
    isAccTemAgree(type) {
      const { orderGoodsList } = this.orderGoodsInfo
      let index = this.orderGoodsIndex
      let status = this.isReadTempStorageComplete && type == 1
      orderGoodsList[index].is_ts = status ? 1 : 0
      orderGoodsList[index].is_checked_ts = status
      this.showTempStorPop = false
      if (status) {
        this.getPrice().then((res) => {
          if (res.data.items_info[index].is_open_ts_confirm && res.data.items_info[index].ts_time) {
            // 生成可选时间列表
            const minDate = new Date(res.data.items_info[index].begin_storage_time)
            const maxDate = new Date(res.data.items_info[index].ts_time)
            this.tsTimeList = this.generateTsTimeList(minDate, maxDate)

            // 设置默认选中最晚时间
            const maxYear = maxDate.getFullYear()
            const maxMonth = maxDate.getMonth() + 1
            const maxDay = maxDate.getDate()

            // 在列表中找到对应的索引
            const yearIndex = this.tsTimeList.findIndex((item) => item.value === maxYear)
            const monthIndex = this.tsTimeList[yearIndex]?.children.findIndex((item) => item.value === maxMonth)
            const dayIndex = this.tsTimeList[yearIndex]?.children[monthIndex]?.children.findIndex(
              (item) => item.value === maxDay
            )

            // 如果找到了对应的索引，设置默认值
            if (yearIndex !== -1 && monthIndex !== -1 && dayIndex !== -1) {
              this.defaultTsValue = [yearIndex, monthIndex, dayIndex]
              this.$nextTick(() => {
                this.tsConfirm = true
              })
            } else {
              this.tsConfirm = true
            }
          }
          const { error_code } = res?.data || {}
          if (error_code === 50004) {
            orderGoodsList[index].is_ts = 0
            orderGoodsList[index].is_checked_ts = false
          }
        })
      }
    },
    cancelTsTime() {
      // 如果是取消,取消暂存
      const { orderGoodsList } = this.orderGoodsInfo
      let index = this.orderGoodsIndex
      orderGoodsList[index].is_ts = 0
      orderGoodsList[index].is_checked_ts = false
      orderGoodsList[index].predict_time = orderGoodsList[index].default_predict_time
      this.getPrice()
      return
    },
    confirmTsTime(val) {
      if (!val || !val.length) return

      const [year, month, day] = val.map((item) => item.value)
      const selectedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`

      // 验证选择时间是否在允许范围内
      const selectedTime = new Date(selectedDate).getTime()
      const minDate = new Date(this.priceInfo.items_info[this.orderGoodsIndex].begin_storage_time)
      const maxDate = new Date(this.priceInfo.items_info[this.orderGoodsIndex].ts_time)

      // 将时间设置为当天的开始时间（00:00:00）以进行比较
      minDate.setHours(0, 0, 0, 0)
      const selectedDate0 = new Date(selectedDate)
      selectedDate0.setHours(0, 0, 0, 0)

      if (selectedDate0.getTime() < minDate.getTime()) {
        this.feedback.toast({
          title: '暂存发货时间需大于当前时间',
          duration: 2000,
        })
        return
      }

      if (selectedTime > maxDate) {
        this.feedback.toast({
          title: '暂存发货时间不能超过限定时间',
          duration: 2000,
        })
        return
      }

      this.orderGoodsInfo.orderGoodsList[this.orderGoodsIndex].ts_time = selectedDate
    },

    // 是否接受冷链协议
    isAccColdAgree(type) {
      const { orderGoodsList } = this.orderGoodsInfo
      let index = this.orderGoodsIndex
      let status = this.isReadColdChainComplete && type == 1
      orderGoodsList[index].$is_checked_cold_chain = status
      this.showColdChainPop = false
      if (status) {
        const coldChainInfo = orderGoodsList[index]?.dis_list.find(
          (item) => item.express_type === MorderConfirmExpressType.ColdChain
        )
        if (coldChainInfo) {
          orderGoodsList[index].express_type = MorderConfirmExpressType.ColdChain
          orderGoodsList[index].sel_dis_info = coldChainInfo
          this.getPrice().then((res) => {
            const { error_code } = res?.data || {}
            if (error_code === 50004) {
              orderGoodsList[index].is_ts = 0
              orderGoodsList[index].is_checked_ts = false
              this.getPrice()
            }
          })
        }
      }
    },

    onChangeInvoiceInfo(item) {
      if (this.invoiceSelId == item.id) {
        this.invoiceSelId = 0
        this.invoiceInfo = {}
      } else {
        this.invoiceSelId = item.id
        this.invoiceInfo = item
      }
      this.showInvHeadPop = false
      this.getInvoiceDetailList()
    },
    onFindInvoiceInfo(item) {
      if (item) {
        this.invoiceSelId = item.id
        this.invoiceInfo = item
      } else {
        this.invoiceSelId = 0
        this.invoiceInfo = {}
      }
    },

    //  顺手购板块
    async getAddPurchasedList() {
      if (this.specialType === 4) return
      if (this.supportAddPurchasedGoods) {
        // { is_ap:1, is_vp:1, use_flash: 1, use_leftover: 1, use_second: 1 }
        const data = { is_ap: 1, is_vp: 1 }
        if (this.defaultSubmitType === 0) {
          data.use_flash = 1
        } else if (this.defaultSubmitType === 1) {
          data.use_second = 1
        } else if (this.defaultSubmitType === 4) {
          data.use_flash = 1
          data.use_second = 1
        } else {
          data.use_flash = 0
          data.use_second = 0
        }
        data.package_id = this.orderGoodsInfo.orderGoodsList.map((item) => item.package_id).join()
        const res = await this.$u.api.orderAddPurchase(data)
        const list = res?.data || []
        const handleList = list.map((item) => {
          const {
            // periods_type,
            // period,
            // package_id,
            // package_name,
            package_price: price,
            predict_shipment_time: predict_time,
            ap_product_img: banner_img,
            limit: nums,
            ap_title: title,
            is_support_ts: goods_is_ts,
          } = item
          return Object.assign(
            {},
            {
              ...item,
              // periods_type,
              // period,
              // package_id,
              // package_name,
              price,
              predict_time,
              banner_img,
              nums,
              title,
              goods_is_ts,
            },
            {
              $checked: false,
              is_cart: this.orderGoodsInfo.is_cart,
              express_type: 0,
              is_checked_ts: false,
              is_ts: 0,
              is_cold_chain: 0,
              is_add_purchase: 1,
              marketing_attribute: '0',
            }
          )
        })
        this.purchasedList = handleList
      }
    },

    onChangeAddPurchasedItem(item) {
      this.feedback.loading()
      this.purchasedList.forEach((item1) => {
        if (item.periods === item1.periods && item.package_id === item1.package_id) item1.$checked = !item1.$checked
        else item1.$checked = false
      })
      const purchasedInfo = this.purchasedList.find((item) => item.$checked)
      if (purchasedInfo) {
        const orderGoodsList = this.orderGoodsInfo.orderGoodsList
        const orderGoodsListLen = orderGoodsList.length
        const purchaseGoods = orderGoodsList[orderGoodsListLen - 1]
        if (purchaseGoods.is_add_purchase) {
          console.log('---替换')
          this.orderGoodsInfo.orderGoodsList.splice(-1, 1, purchasedInfo)
          // this.orderGoodsInfo.orderGoodsList[orderGoodsListLen - 1] = purchasedInfo
        } else {
          console.log('---追加')
          this.orderGoodsInfo.orderGoodsList.push(purchasedInfo)
        }
        this.orderGoodsInfo.submit_type = 4
      } else {
        console.log('---删除')
        this.orderGoodsInfo.orderGoodsList.pop()
        this.orderGoodsInfo.submit_type = this.defaultSubmitType
        this.canUseSelCouId = 0
      }
      this.canUseSelCouId = 0
      this.getPrice().then(() => {
        const { coupon_id } = this.priceInfo
        this.canUseSelCouId = coupon_id
        this.recommendedCouponId = coupon_id
        this.feedback.hideLoading()
      })
    },

    // 下单（创建订单）
    async createOrder() {
      if (!this.addressInfo.id) return this.feedback.toast({ title: '请选择收货地址~' })
      if (this.getSubmitType === 2 && !this.realInfoCorrect) {
        this.showCrossBorderRealInfoMod = true
        this.feedback.toast({ title: '请填写有效的实名信息~' })
        return
      }
      const { submit_type, orderGoodsList, is_cart, special_type, group_id } = this.orderGoodsInfo //订单信息
      const { province_id, city_id, town_id, address, consignee, consignee_phone } = this.addressInfo //地址信息
      let data = {
        submit_type, //订单提交类型：0-闪购 1-秒发 2-跨境 3-尾货 4-闪购+秒发混合
        order_from: 2, //来源: 0-IOS 1-Android 2-H5 3-PC 4-Wxapp
        items_info: orderGoodsList, //商品信息
        // coupon_id: this.canUseSelCouId, //优惠券ID
        province_id, //省id
        city_id, //市id
        district_id: town_id, //区id
        address, //详细地址
        consignee, //收货人姓名
        consignee_phone, //收货人电话号码
        invoice_progress: 0, //发票是否开票 0不开票 1开票
        is_cart, //是否购物车购买：0否 1是
        id_card_no: this.idCard, //身份证号
        realname: this.realName, //真实姓名
        special_type, //特殊类型：0-普通 1-拼团 2-新人
        group_id, //拼团id
      }
      if (this.invoiceSelId) {
        //需要开发票
        data.invoice_progress = 1
        data.invoice_id = this.invoiceSelId
      }

      if (this.couponType == 3001) {
        //快递优惠券
        data.express_coupon_id = this.canUseSelCouId //快递优惠券ID
      } else {
        //普通优惠券
        data.coupon_id = this.canUseSelCouId //普通优惠券ID
      }
      if (this.orderGoodsInfo.oneSource) {
        data.oneSource = this.orderGoodsInfo.oneSource
      }
      console.log('--------------------------这是下单需要的参数')
      console.log(data)

      this.feedback.loading({ title: '结算中...' })

      try {
        let res = await this.$u.api.createOrder(data)
        let payInfo = {
          payPlate: this.payPlate, //支付板块 0 = 普通商品，1 = 酒会，2 = 门店
          payment_amount: this.priceInfo.payment_amount, //支付金额
          payment_method: 4, //支付方式: 0:支付宝APP 1:支付宝H5 2:微信h5 3:微信APP 4:微信小程序 5:PC扫码
          order_type: 2, //订单类型(来源)：1:v3支付 2:v3酒云 20:门店 30:老外卖酒 40:nft
          is_cross: submit_type == 2 ? 1 : 0, //是否跨境 0:非跨境 1:跨境 不传默认非跨境
          ...res.data, //下单信息
        }
        uni.removeStorageSync('source') //下单成功后需要清除来源标识
        this.muPayInfo(payInfo)
        // this.jump.redirectTo(this.routeTable.pBPayment)
        if (this.$vhFrom == 'next') {
          console.log('version-------',this.$vhVersion);
          
          // $vhVersion
          // uni.setStorageSync('nextpayInfo',payInfo);
          if (this.compareVersions( this.$vhVersion, '9.4.2') >= 0) {
            if (submit_type == 2) {
            uni.setStorageSync('nextpayInfo', payInfo)
            this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1)
          } else {
            // 保存订单结果供弹出层使用
            this.lastOrderResult = res.data;
            // 使用弹出层选择支付方式
            this.feedback.hideLoading();
            this.showPaymentPopup();
            return; // 不继续执行后续逻辑，等待用户选择支付方式
          }
          } else {
            if (submit_type == 2) {
            uni.setStorageSync('nextpayInfo', payInfo)
            this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1)
          } else {
            if (special_type == 4) {
              const { main_order_no } = res.data
              const params = {
                client_path: {
                  android_path: 'toPay',
                },
                ad_path_param: [
                  { android_key: 'main_order_no', android_val: `${main_order_no}` },
                  { android_key: 'payOkUrl', android_val: `${this.routeTable.pBOrderDeposit}` },
                ],
              }

              wineYunJsBridge.openAppPage(params)
            } else {
              this.jump.pullAppPay(this.$vhFrom, res.data)
            }
          }
          }
          
        } else {
          this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1)
        }
      } catch (e) {
        console.log(e)
        // setTimeout(() => {
        // 	this.jump.navigateBack()
        // }, 1500)
      }
    },

    // 生成可选时间列表
    generateTsTimeList(minDate, maxDate) {
      const start = new Date(minDate)
      const end = new Date(maxDate)

      // 设置时间为当天的开始时间（00:00:00）
      start.setHours(0, 0, 0, 0)
      end.setHours(0, 0, 0, 0)

      const result = []

      // 生成年份
      for (let y = start.getFullYear(); y <= end.getFullYear(); y++) {
        const yearItem = {
          value: y,
          label: `${y}年`,
          children: [],
        }

        // 生成月份
        for (let m = 1; m <= 12; m++) {
          const monthItem = {
            value: m,
            label: `${m}月`,
            children: [],
          }

          // 生成日期
          const daysInMonth = new Date(y, m, 0).getDate()
          for (let d = 1; d <= daysInMonth; d++) {
            // 检查日期是否在允许范围内
            const currentDate = new Date(y, m - 1, d)
            currentDate.setHours(0, 0, 0, 0)
            if (currentDate.getTime() >= start.getTime() && currentDate.getTime() <= end.getTime()) {
              monthItem.children.push({
                value: d,
                label: `${d}日`,
              })
            }
          }

          // 只添加有效日期的月份
          if (monthItem.children.length > 0) {
            yearItem.children.push(monthItem)
          }
        }

        // 只添加有效月份的年份
        if (yearItem.children.length > 0) {
          result.push(yearItem)
        }
      }

      return result
    },

    // 支付弹出层相关方法
    // 显示支付弹出层
    async showPaymentPopup() {
      // 先查询余额信息
      await this.getBalanceInfo();

      // 根据余额情况初始化选择状态
      const paymentAmount = parseFloat(this.priceInfo.payment_amount);

      if (this.totalBalance <= 0) {
        // 余额为0时，储值账户禁用，默认选择华为支付
        this.balanceSelected = false;
        this.huaweiSelected = true;
      } else if (this.totalBalance >= paymentAmount) {
        // 余额大于订单金额时，两个选项为单选，默认选择华为支付
        this.balanceSelected = false;
        this.huaweiSelected = true;
      } else {
        // 余额小于订单金额时，两个选项为多选，默认选择华为支付
        this.balanceSelected = false;
        this.huaweiSelected = true;
      }

      this.paymentPopupShow = true;
    },

    // 获取余额信息
    async getBalanceInfo() {
      try {
        // 实际API调用
        let res = await this.$u.api.myCurrentBalance()
        const data = res.data;
        this.balanceInfo = data;

        console.log('余额信息:', this.balanceInfo);
      } catch(e) {
        console.error('获取余额信息失败', e);
        // 如果API调用失败，使用模拟数据
        this.balanceInfo = {
          recharge_balance: 29.50,
          bonus_balance: 0
        };
      }
    },

    // 处理储值账户选择
    handleBalanceSelect() {
      if (this.balanceDisabled) {
        this.feedback.toast({ title: '账户余额为0，无法使用余额支付' });
        return;
      }

      const paymentAmount = parseFloat(this.priceInfo.payment_amount);

      if (this.totalBalance >= paymentAmount) {
        // 余额充足时，单选逻辑
        this.balanceSelected = !this.balanceSelected;
        if (this.balanceSelected) {
          this.huaweiSelected = false;
        }
      } else {
        // 余额不足时，多选逻辑
        this.balanceSelected = !this.balanceSelected;
      }
    },

    // 处理华为支付选择
    handleHuaweiSelect() {
      const paymentAmount = parseFloat(this.priceInfo.payment_amount);

      if (this.totalBalance >= paymentAmount) {
        // 余额充足时，单选逻辑
        this.huaweiSelected = !this.huaweiSelected;
        if (this.huaweiSelected) {
          this.balanceSelected = false;
        }
      } else {
        // 余额不足时，多选逻辑
        this.huaweiSelected = !this.huaweiSelected;
      }
    },

    // 确认支付
    async confirmPayment() {

      // 检查是否选择了支付方式
      if (!this.balanceSelected && !this.huaweiSelected) {
        this.feedback.toast({ title: '请选择支付方式' });
        return;
      }

      // 关闭弹出层
     

      // 如果选择了余额支付，先处理余额支付
      if (this.balanceSelected) {
        await this.handleBalancePayment();
      } else {
        // 没有选择余额支付，继续原有的支付流程
        const { special_type } = this.orderGoodsInfo;

        if (special_type == 4) {
          const { main_order_no } = this.lastOrderResult;
          const params = {
            client_path: {
              android_path: 'toPay',
            },
            ad_path_param: [
              { android_key: 'main_order_no', android_val: `${main_order_no}` },
              { android_key: 'payOkUrl', android_val: `${this.routeTable.pBOrderDeposit}` },
            ],
          }

          wineYunJsBridge.openAppPage(params)
        } else {
          this.jump.pullAppPay(this.$vhFrom, this.lastOrderResult)
        }
        this.paymentPopupShow = false;
      }
    },

    // 处理余额支付
    async handleBalancePayment() {
      // 计算余额支付金额
      const paymentAmount = parseFloat(this.priceInfo.payment_amount);
        const balanceAmount = Math.min(this.totalBalance, paymentAmount);

        // 调用余额支付接口
        const balancePayParams = {
          main_order_no: this.lastOrderResult.main_order_no,
          balance_amount: balanceAmount
        };

        const balanceRes = await this.$u.api.balancePay(balancePayParams);
        const { recharge_balance, bonus_balance, pending_amount, is_paid } = balanceRes.data;

        // 更新余额信息
        this.balanceInfo.recharge_balance = recharge_balance;
        this.balanceInfo.bonus_balance = bonus_balance;

        this.feedback.hideLoading();

        if (is_paid) {
          this.paymentPopupShow = false;
          // 全额支付成功，跳转到我的订单页
          this.feedback.toast({ title: '支付成功', icon: 'success' });
          if (special_type == 4) {
            this.jump.appAndMiniJump(0, `${this.routeTable.pBOrderDeposit}`, this.$vhFrom, 1)
          } else {
            this.jump.appAndMiniJump(0, `${this.routeTable.pEMyOrder}?status=1`, this.$vhFrom, 1)
          }
         
        } else {

          // 如果同时选择了华为支付，继续处理剩余金额
          if (this.huaweiSelected) {
            // 更新支付金额为剩余金额
            const { special_type } = this.orderGoodsInfo;
            console.log('special_type-----',special_type);
            
                if (special_type == 4) {
                  const { main_order_no } = this.lastOrderResult;
                  const params = {
                    client_path: {
                      android_path: 'toPay',
                    },
                    ad_path_param: [
                      { android_key: 'main_order_no', android_val: `${main_order_no}` },
                      { android_key: 'payOkUrl', android_val: `${this.routeTable.pBOrderDeposit}` },
                    ],
                  }

                  wineYunJsBridge.openAppPage(params)
                } else {
                  this.jump.pullAppPay(this.$vhFrom, this.lastOrderResult)
                }
            this.paymentPopupShow = false;
          } else {
            // 只选择了余额支付但余额不足，提示用户
            this.feedback.toast({ title: '余额不足，请选择其他支付方式' });
          }
        }
    },
    compareVersions(v1, v2) {
      console.warn(v1, v2)
      const v1Parts = v1.split('.').map(Number)
      const v2Parts = v2.split('.').map(Number)

      for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
        const v1Part = v1Parts[i] || 0
        const v2Part = v2Parts[i] || 0

        if (v1Part > v2Part) return 1
        if (v1Part < v2Part) return -1
      }

      return 0
    },
  },
  watch: {
    addressInfo: {
      handler(newVal) {
        if (newVal && this.orderGoodsInfo && this.orderGoodsInfo.orderGoodsList) {
          this.orderGoodsInfo.orderGoodsList.forEach((item) => {
            if (item.goods_is_ts === 1) {
              item.is_checked_ts = 0
              item.ts_time = ''
              item.is_ts = 0
            }
          })
        }
      },
      deep: true,
    },
  },
}
</script>

<style scoped>
/* 支付弹出层样式 */
.payment-popup {
  padding: 40rpx 32rpx 32rpx;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
}

.popup-amount {
  text-align: center;
  margin-bottom: 40rpx;
  padding-top: 20rpx;
}

.amount-symbol {
  font-size: 28rpx;
  color: #E53E3E;
  font-weight: 400;
}

.amount-value {
  font-size: 48rpx;
  color: #E53E3E;
  font-weight: 600;
  margin-left: 4rpx;
}

.popup-pay-list {
  margin-bottom: 40rpx;
}

.popup-pay-item {
  display: flex;
  align-items: center;
  justify-content: space-between;

  height: 80rpx;
}

.popup-pay-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.pay-item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.pay-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

.balance-icon {
  background: #FF6B6B;
}

.huawei-icon {
  background: #FF6600;
}

.icon-text {
  font-size: 20rpx;
  color: #FFFFFF;
  font-weight: bold;
}

.pay-info {
  display: flex;

}

.pay-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 400;
  line-height: 40rpx;
}

.pay-desc {
  font-size: 24rpx;
  color: #999999;
  line-height: 32rpx;
  margin-top: 2rpx;
}

.pay-item-right {
  margin-left: 20rpx;
}

/* 选择按钮图片样式 */
.w-36 {
  width: 36rpx;
}

.h-36 {
  height: 36rpx;
}

.w-40 {
  width: 40rpx;
}

.h-40 {
  height: 40rpx;
}

.popup-pay-button {
  width: 100%;
  height: 80rpx;
  background: #E53E3E;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #FFFFFF;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.popup-pay-button:active {
  background: #C53030;
}
</style>
