<template>
	<view class="content bg-f5f5f5 h-vh-100 o-scr-y">
		<!-- 导航栏 -->
		<view v-if="from == ''" class="bb-s-01-eeeeee">
			<u-navbar back-icon-color="#333" title="兑换成功" title-size="36" :title-bold="true" title-color="#333" />
		</view>
		
		<!-- 兑换成功 -->
		<view class="h-748 bg-ffffff">
			<view class="d-flex flex-column a-center">
				<image class="w-266 h-184 mt-158" src="https://images.vinehoo.com/vinehoomini/v3/comm/succ_red.png" mode="aspectFill" />
				<view class="mt-44 font-36 font-wei text-3">兑换成功</view>
			</view>
			
			<view class="mt-120 ml-94 mr-94">
				<view class="d-flex j-sb a-center">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#999', backgroundColor: '#FFF', border:'2rpx solid #999'}"
					@click="viewOrder()">查看订单</u-button>
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
					@click="returnIndex()">返回首页</u-button>
				</view>
			</view>
		</view>
	    
		<!-- 热门推荐 -->
		<view class="">
			<vh-split-line :padding-top="52" :padding-bottom="32" :margin-left="10" :margin-right="10" text="热门推荐" 
			:font-bold="true" :font-size="36" text-color="#333333" :show-image="true" image-src="https://images.vinehoo.com/vinehoomini/v3/comm/fire.png" />
			<vh-goods-recommend-list />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				from:'', //从哪个端进入 1 = 安卓、2 = ios"、3 = pc
			}
		},
		
		computed: {
			// 获取状态栏高度
			navigationBarHeight() {
				return this.system.getSysInfo().statusBarHeight + 48
			}
		},
		
		onLoad(options) {
			if(options.from){
				this.from = options.from 
			}
		},
		
		methods: {
			// 查看订单
			viewOrder() {
				if(this.comes.isFromApp(this.from))
					wineYunJsBridge.openAppPage({ 
						client_path: { "ios_path":"MyOrderViewController", "android_path":"com.stg.rouge.activity.MyOrderActivity" }
					});
				else
					this.jump.redirectTo(`/packageE/pages/my-order/my-order`)
			},
			
			// 返回首页
			returnIndex() {
				if(this.comes.isFromApp(this.from))
					wineYunJsBridge.openAppPage({ 
						client_path: { "ios_path":"goMain", "android_path":"goMain" } ,
					});
				else
					this.jump.reLaunch(`/pages/index/index`)
			}
		}
	}
</script>

<style scoped>
</style>
