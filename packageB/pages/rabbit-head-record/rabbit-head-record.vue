<template>
	<view class="content">
		<!-- 导航栏 -->
		<view v-if="from == '' || from == 'next'" class="bb-s-01-eeeeee">
			<u-navbar back-icon-color="#333" title="兔头记录" title-size="36" :title-bold="true" title-color="#333" />
		</view>
		
		<!-- 兔头数量 -->
		<view class="d-flex j-center a-center mt-24">
			<view class="p-rela w-686 h-240 d-flex flex-column j-center a-center">
				<image class="p-abso z-01 w-686 h-240" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_rec_bg.png" mode="aspectFill" />
				<view class="p-rela z-03 font-84 font-wei text-ffffff l-h-84">{{rabbitNum}}</view>
				<view class="p-rela z-03 d-flex j-center a-center mt-20">
					<image class="w-18 h-18" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png" mode="widthFix" />
					<text class="ml-06 mr-06 font-24 text-ffffff">我的兔头</text>
					<image class="w-18 h-18" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png" mode="widthFix" />
				</view>
			</view>
		</view>
		
		<!-- tabs切换栏 -->
		<view class="mt-20 bb-s-01-eeeeee">
			<u-tabs :is-scroll="false" :list="tabsList" :current="current" bar-width="36" bar-height="8" font-size="28" inactive-color="#999" active-color="#333"
			:bar-style="{background:'linear-gradient(214deg, #FF8383 0%, #E70000 100%)'}" @change="changeTabs" />
		</view>
		
		<view class="">
			<!-- 兔头记录有数据 -->
			<view v-if="rabbitRecordList.length" class="pt-24">
				<view class="d-flex j-sb a-center bb-s-01-eeeeee ml-32 mr-32 pt-32 pb-32" v-for="(item, index) in rabbitRecordList" :key="index">
					<view class="">
						<view class="">
							<text class="font-30 text-3 l-h-40">{{item.change}}</text>
							<text class="ml-20 font-24 text-9">{{item.reason ? item.reason : ''}}</text>
						</view>
						<view class="mt-10 font-24 text-9">{{item.created_time}}</view>
					</view>
					
					<view class="d-flex a-center">
						<text class="font-32" :class="item.change_type == 1 ? 'text-e80404' : 'text-3'"><text class="font-28">{{item.change_type == 1 ? '+' : '-'}}</text>{{item.rabbit}}</text>
						<image class="w-26 h-28 ml-10" :src="`https://images.vinehoo.com/vinehoomini/v3/comm/rab_${ item.change_type == 1 ? 'gold' : 'gray'}.png`" mode="widthFix" />
					</view>
				</view>
				
				<view class="ptb-24-plr-16">
					<u-loadmore :status="loadStatus" />
				</view>
			</view>
			
			<!-- 兔头记录数据为空 -->
			<view class="fade-in" v-if="rabbitRecordList.length == 0">
				<vh-empty :padding-top="52" :padding-bottom="100" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_goods.png" text="暂无兑换记录~" />
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name:"rabbit-head-record",
		data(){
			return{
				from: '', //从哪个端进入 1 = 安卓、2 = ios"
				rabbitNum:0, //兔头数量
				tabsList: [
					{ name: '全部' }, 
					{ name: '获取记录' },
					{ name: '兑换记录' }
				],
				current: 0,//tabs当前索引
				rabbitRecordList:[], //兔头兑换记录列表
				page: 1, //第几页
				limit: 10, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //底部加载状态
			}
		},
		
		onLoad(options) {
			if(options.from) this.from = options.from
			this.init()
		},
		
		methods:{
			// 初始化
			async init() {
				await Promise.all([this.getRabbitNum(),this.getRabbitExchangeRecord()])
			},
			
			// 获取兔头数
			async getRabbitNum() {
				let res = await this.$u.api.userSpecifiedData({field: 'rabbit'})
				this.rabbitNum = res.data.rabbit
			},
			
			// 获取兔头兑换记录
			async getRabbitExchangeRecord() {
				let res = await this.$u.api.rabbitExchangeRecord({ page: this.page, limit: this.limit, type: this.current })
				this.page == 1 ? this.rabbitRecordList = res.data.list : this.rabbitRecordList = [...this.rabbitRecordList, ...res.data.list]
				this.totalPage = Math.ceil(res.data.total / this.limit)
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				console.log(res)
			},
			
			// 切换tabs状态栏
			changeTabs(index) {
				this.current = index;
				this.page = 1
				this.getRabbitExchangeRecord()
			},
		},
		
		onReachBottom() {
			if (this.page == this.totalPage) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getRabbitExchangeRecord()
		}
	}
</script>

<style scoped>
</style>
