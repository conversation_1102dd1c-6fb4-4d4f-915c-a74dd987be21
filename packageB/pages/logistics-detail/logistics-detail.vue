<template>
  <view class="content bg-f5f5f5">
    <!-- 导航栏 -->
    <!-- <u-navbar back-icon-color="#333" title="物流详情" title-size="36" :title-bold="true" title-color="#333"></u-navbar> -->
    <vh-navbar title="物流详情"> </vh-navbar>
    <!-- 物流轨迹 -->
    <view class="">
      <!-- 拥有物流轨迹信息 -->
      <view v-if="logisticData.traces && logisticData.traces.length" class="">
        <!-- 订单状态 -->
        <view class="pt-24">
          <view class="bg-ffffff ml-24 mr-24 b-rad-10">
            <view class="d-flex a-center pt-24 pb-24 ml-24 mr-24 bb-s-01-f8f8f8">
              <vh-image :loading-type="2" :src="logisticsInfo.image" :width="50" :height="50" :border-radius="4" />
              <text v-if="logisticData.state_text" class="ml-20 font-28 text-3 font-wei l-h-40">{{
                logisticData.state_text
              }}</text>
              <text v-else-if="logisticsInfo.status" class="ml-20 font-28 text-3 font-wei l-h-40">
                订单{{ logisticsInfo.status == 2 ? '待收货' : '已完成' }}
              </text>
              <text v-else class="ml-20 font-28 text-3 font-wei l-h-40"> 暂无订单状态信息 </text>
            </view>

            <view
              v-if="logisticData.logistic_code"
              class="d-flex a-center pt-32 pb-32 ml-24 mr-24 bb-s-01-f8f8f8"
              @click="copy.copyText(logisticData.logistic_code)"
            >
              <text class="font-28 text-3 font-wei l-h-40">物流单号</text>
              <text class="ml-40 font-24 text-3 l-h-36">{{ logisticData.logistic_code }}</text>
              <text class="bg-f5f5f5 ml-12 ptb-02-plr-12 b-rad-14 font-16 text-3">复制</text>
            </view>

            <view class="d-flex a-center pt-32 pb-32 ml-24 mr-24">
              <text class="font-28 text-3 font-wei l-h-40">预计送达</text>
              <text class="ml-40 font-24 text-3 l-h-36">{{
                logisticData.arrival_time ? logisticData.arrival_time : '暂无信息'
              }}</text>
            </view>
          </view>
        </view>

        <!-- 时间轴 -->
        <view class="pb-72">
          <view class="bg-ffffff mt-20 ml-24 mr-24 b-rad-10 ptb-32-plr-24 d-flex a-center">
            <view class="text-ffffff">-</view>
            <view class="">
              <u-time-line>
                <!-- <view class="">
									<u-time-line-item nodeTop="2">
										<template v-slot:node>
											<view class="bg-fce4e3 w-26 h-26 b-rad-p50 d-flex j-center a-center">
												<view class="w-14 h-14 bg-e80404 b-rad-p50"></view>
											</view>
										</template>
										<template v-slot:content>
											<view class="ml-12">
												<view class="text-e80404">
													<text class="font-28 font-wei l-h-40">已签收</text>
													<text class="ml-18 font-24 l-h-40">07-30 18:00</text>
												</view>
												<view class="mt-10 font-24 text-3 l-h-40">您的快件已在由快递驿站代部，感谢使用京东物流，期待再次为您服务</view>
											</view>
										</template>
									</u-time-line-item>
								</view>
								<view class="">
									<u-time-line-item>
										<template v-slot:content>
											<view class="ml-12">
												<view class="text-9">
													<text class="font-28 font-wei l-h-40">派送中</text>
													<text class="ml-18 font-24 l-h-40">07-30 18:00</text>
												</view>
												<view class="mt-10 font-24 text-9 l-h-40">您的快件已在由快递驿站代部，感谢使用京东物流，期待再次为您服务</view>
											</view>
										</template>
									</u-time-line-item>
								</view>
								<view class="">
									<u-time-line-item>
										<template v-slot:content>
											<view class="ml-12">
												<view class="text-9">
													<text class="font-28 font-wei l-h-40">运输中</text>
													<text class="ml-18 font-24 l-h-40">07-30 18:00</text>
												</view>
												<view class="mt-10 font-24 text-9 l-h-40">您的快件已在由快递驿站代部，感谢使用京东物流，期待再次为您服务</view>
											</view>
										</template>
									</u-time-line-item>
								</view>
								<view class="">
									<u-time-line-item>
										<template v-slot:content>
											<view class="ml-12">
												<view class="text-9">
													<text class="font-24 l-h-40">07-30 18:00</text>
												</view>
												<view class="mt-10 font-24 text-9 l-h-40">您的快件已在由快递驿站代部，感谢使用京东物流，期待再次为您服务</view>
											</view>
										</template>
									</u-time-line-item>
								</view>
								<view class="">
									<u-time-line-item>
										<template v-slot:content>
											<view class="ml-12">
												<view class="text-9">
													<text class="font-24 l-h-40">07-30 18:00</text>
												</view>
												<view class="mt-10 font-24 text-9 l-h-40">您的快件已在由快递驿站代部，感谢使用京东物流，期待再次为您服务</view>
											</view>
										</template>
									</u-time-line-item>
								</view>
								<view class="">
									<u-time-line-item>
										<template v-slot:content>
											<view class="ml-12">
												<view class="text-9">
													<text class="font-28 font-wei l-h-40">已揽件</text>
													<text class="ml-18 font-24 l-h-40">07-30 18:00</text>
												</view>
												<view class="mt-10 font-24 text-9 l-h-40">您的快件已由【南通川姜营业部】揽收成功</view>
											</view>
										</template>
									</u-time-line-item>
								</view>
								<view class="">
									<u-time-line-item>
										<template v-slot:content>
											<view class="ml-12">
												<view class="text-9">
													<text class="font-28 font-wei l-h-40">已发货</text>
													<text class="ml-18 font-24 l-h-40">07-30 18:00</text>
												</view>
												<view class="mt-10 font-24 text-9 l-h-40">快递正在等待揽收</view>
											</view>
										</template>
									</u-time-line-item>
								</view>
								<view class="">
									<u-time-line-item>
										<template v-slot:content>
											<view class="ml-12">
												<view class="text-9">
													<text class="font-28 font-wei l-h-40">已下单</text>
													<text class="ml-18 font-24 l-h-40">07-30 18:00</text>
												</view>
												<view class="mt-10 font-24 text-9 l-h-40">商品已下单</view>
											</view>
										</template>
									</u-time-line-item>
								</view> -->

                <view class="" v-for="(item, index) in logisticData.traces" :key="index">
                  <u-time-line-item nodeTop="10">
                    <template v-slot:node>
                      <view v-if="index == 0" class="w-26 h-26 bg-fce4e3 d-flex j-center a-center b-rad-p50">
                        <view class="w-14 h-14 bg-e80404 b-rad-p50" />
                      </view>

                      <view v-else class="w-14 h-14 bg-cccccc b-rad-p50" />
                    </template>
                    <template v-slot:content>
                      <view class="ml-12">
                        <view :class="index == 0 ? 'text-e80404' : 'text-9'">
                          <!-- <text class="font-28 font-wei l-h-40">已签收</text> -->
                          <text class="font-24 l-h-40">{{ item.ftime }}</text>
                        </view>
                        <view class="mt-10 font-24 l-h-40" :class="index == 0 ? 'text-3' : 'text-9'">{{
                          item.context
                        }}</view>
                      </view>
                    </template>
                  </u-time-line-item>
                </view>
              </u-time-line>
            </view>
          </view>
        </view>
      </view>

      <!-- 暂无物流轨迹信息 -->
      <vh-empty
        v-else
        :padding-top="260"
        :padding-bottom="820"
        image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_log.png"
        :text-bottom="0"
        text="暂无物流信息~"
      />
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'logistics-detail',

  data() {
    return {
      logisticData: {}, //物流数据
    }
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['logisticsInfo']),

    hasLogisticsInfo({ logisticsInfo }) {
      return !!(logisticsInfo && Object.keys(logisticsInfo).length)
    },
  },

  onLoad(option) {
    if (!this.hasLogisticsInfo) return
    console.log(option.info)
    if (option.info && option.info !== '{}') {
      this.logisticData = JSON.parse(option.info)
    } else {
      this.getLogisticsDetails()
    }
  },

  onShow() {
    // if (!this.hasLogisticsInfo&&this.logisticData=={}) {
    //   this.jump.reLaunch('/')
    //   return
    // }
  },

  methods: {
    // 获取物流详情
    async getLogisticsDetails() {
      const { image, logisticCode, expressType } = this.logisticsInfo
      let res = await this.$u.api.logisticsDetails({
        logisticCode,
        expressType,
      })
      this.logisticData = res.data
    },
  },
}
</script>

<style>
@import '../../../common/css/page.css';
</style>
