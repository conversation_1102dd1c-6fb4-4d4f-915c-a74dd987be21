<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar  title="门店购物车">
			<view class="d-flex a-center ml-10 w-s-now font-30 text-3" @click="isShowEdit = !isShowEdit">{{isShowEdit ? '编辑' : '结算'}}</view>
		</vh-navbar>
		
		<!-- 购物车列表 -->
		<view class="">
			<!-- 普通商品列表 -->
			<view v-if="storeGoodsList.length" class="pt-20">
				<view class="list-con bg-ffffff ml-24 mr-24 ptb-40-plr-24 b-rad-10">
					<view class="d-flex j-sb a-center mb-40">
						<view class="d-flex a-center">
							<vh-check :checked="isSelectAllGoods()" @click="selectAll()" />
							<text class="ml-24 font-32 font-wei text-3">门店商品</text>
						</view>
					</view>
					<view class="list-item d-flex j-sb a-center mt-20" v-for="(item, index) in storeGoodsList" :key="index">
						<vh-check :checked="storeGoodsSelectedList.indexOf(item.id) > -1" @click="selectSingle(item)" />
						<view class="ml-24">
							<vh-image :loading-type="2" :src="item.goods_images" :width="144" :height="144" :border-radius="8" mode="aspectFit"/>
						</view>
						<view class="ml-16 flex-1">
							<view class="d-flex a-center text-hidden-1 o-hid">
								<text class="ml-04 font-24 text-0 l-h-34">{{item.goods_name}}</text>
							</view>
							
							<view class="mt-10">
								<text class="bg-eeeeee b-rad-06 mt-12 ptb-02-plr-12 font-20 text-9 l-h-28">{{item.c_name}}</text>
							</view>
							<view class="mt-28 d-flex j-sb a-center">
								<text class="font-28 font-wei text-e80404"><text class="font-16">¥</text>{{item.price}}</text>
								<u-number-box :value="item.nums" :min="1" :max="item.maxstock" :input-width="52" :input-height="40" :size="28" @change="changeCartNumbers($event, item.id)" />
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 购物车列表为空 -->
		<view v-if="storeGoodsList.length === 0" class="bg-ffffff">
			<vh-empty :padding-top="40" :padding-bottom="100" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_cart.png" text="您的购物车空空如也" />
		</view>
		
		<!-- 猜你喜欢列表 -->
		<view class="">
			<vh-split-line :padding-top="52" :padding-bottom="32" :margin-left="10" :margin-right="10" text="猜你喜欢" :font-bold="true" :font-size="36" text-color="#333333"
			:show-image="true" image-src="https://images.vinehoo.com/vinehoomini/v3/comm/guess_love.png" />
			<view :class="isSelectedGoods ? 'pb-180' : 'pb-104'">
				<vh-goods-recommend-list />
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="">
			<!-- （结算） -->
			<view v-show="isShowEdit" class="fade-in-up-medium p-fixed bottom-0 z-999 w-p100">
				<view class="h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-28 pr-24">
					<view class="d-flex a-center">
						<view class="d-flex a-center">
							<vh-check :checked="isSelectAllGoods()" @click="selectAll()"></vh-check>
							<text class="ml-08 font-32 text-3">全选</text>
						</view>
						
						<view class="ml-28 d-flex a-center">
							<text class="font-28 font-wei text-3">合计：</text>
							<text class="font-40 font-wei text-e80404"><text class="font-28">¥</text>{{ settlementPrice }}</text>
						</view>
					</view>
					
					<!-- 配置了杯卖商品（杯卖商品只允许场饮） -->
					<view v-if="isCup">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'300rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" 
						@click="settlement(3)">门店场饮</u-button>
					</view>
					
					<!-- 没有配置杯卖商品 -->
					<view v-else>
						<!-- 门店配置了自提跟场饮 -->
						<view v-if="hasSelfMention && hasFieldDrink" class="d-flex a-center">
							<u-button :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'150rpx', height:'64rpx', fontSize:'26rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#FF9127', border:'none', borderRadius:'32rpx 0 0 32rpx'}" 
							@click="settlement(2)">打包外带</u-button>
							<u-button :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'150rpx', height:'64rpx', fontSize:'26rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none', borderRadius:'0 32rpx 32rpx 0'}" 
							@click="settlement(3)">门店场饮</u-button>
						</view>
						
						<!-- 门店只配置了外带 -->
						<view v-if="hasSelfMention && !hasFieldDrink">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'300rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#FF9127', border:'none'}" 
							@click="settlement(2)">打包外带</u-button>
						</view>
						
						<!-- 门店只配置了场饮或者商品中包含杯买商品 -->
						<view v-if="!hasSelfMention && hasFieldDrink" class="">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'300rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" 
							@click="settlement(3)">门店场饮</u-button>
						</view>
					</view>
				</view>
			</view>
			
			<!--（删除） -->
			<view v-show="!isShowEdit" class="fade-in-up-medium p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-32 pr-24">
				<view class="d-flex a-center">
					<vh-check :checked="isSelectAllGoods()" @click="selectAll()"></vh-check>
					<text class="ml-16 font-32 text-3">全选</text>
				</view>
				
				<view class="d-flex a-center">
					<view class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
						@click="deleteShoppingCartGoods()">删除</u-button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
	    name:"shopping-cart",
		
		data() {
			return {
				isShowEdit: true,// 导航栏是否显示'编辑'
				storeGoodsList: [], //普通商品列表
				storeGoodsSelectedList: [], //选中id的普通商品列表
				showDisdetPop: false, //是否展示优惠明细
				settlementPrice:0, //结算金额
				hasSelfMention: 0, //是否拥有门店自提
				hasFieldDrink: 0, //是否拥有门店场饮
				isCup: false, //是否含有杯卖商品
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['storeInfo', 'storeOrderInfo']),
			
			// 是否含有杯卖商品
			judgeIsCup() {
				for( let item of this.storeGoodsList ){
					if( item.is_cup ) {
						this.isCup = true
						break
					}
				}
			},
			
			// 是否有选中的商品
			isSelectedGoods() {
				if( this.storeGoodsSelectedList.length > 0 ) {
					return true
				}else {
					return false
				}
			},
			
			// 判断物流类型
			judgeDistributionType() {
				console.log('----------------------我是门店信息')
				console.log(this.storeInfo)
				this.storeInfo.purchase_mode_list.forEach( v => {
					if( v.id == 2) {
						this.hasSelfMention = 1
					}
					if( v.id == 3) {
						this.hasFieldDrink = 1
					}
				})
			},
		},
		
		onLoad() {
			this.system.setNavigationBarBlack()
			this.getStoreShoppingCartList()
		},
		
		methods: {
			// mapMutations辅助函数
			...mapMutations(['muStoreOrderInfo']),
			
			// 获取门店购物车列表
			async getStoreShoppingCartList() {
				let res = await this.$u.api.storeShoppingCartList({ sid: this.storeInfo.id })
				this.storeGoodsList = res.data.filter( v => { return v.is_shelf === 1 })
				// this.storeGoodsList[0].is_cup = 1
			},

			// 选中购物车商品金额计算
			async shoppingCartMoneyCalculate(){
				console.log('----------我是计算门店商品计算金额')
				let total = 0;
				this.storeGoodsList.forEach( v => {
					if (this.storeGoodsSelectedList.indexOf(v.id) > -1) {
						total += v.price * 100 * v.nums
					}
				})
				this.settlementPrice = ( total / 100 ).toFixed(2);
			},
			
			// 判断是否全选所有商品
			isSelectAllGoods(){
				return this.storeGoodsList.length === this.storeGoodsSelectedList.length
			},
			
			// 全选/取消全选 
			selectAll(){
				if(this.isSelectAllGoods()){
					this.storeGoodsList.forEach( v => {
						v.checked = false
					})
					this.storeGoodsSelectedList = []
				}else{
					this.storeGoodsSelectedList = this.storeGoodsList.map( v => {
						v.checked = true
						return v.id
					})
				}
				this.shoppingCartMoneyCalculate()
			},
			
			// 选中单个/取消选中单个商品（活动商品、普通商品）item = 商品列表的每一项
			selectSingle(item) {
				if(this.storeGoodsSelectedList.indexOf(item.id) > -1){
					this.storeGoodsList.forEach( v => {
						if(v.id === item.id) v.checked = false
					})
					this.storeGoodsSelectedList.splice(this.storeGoodsSelectedList.indexOf(item.id), 1)
				}else{
					this.storeGoodsList.forEach( v => {
						if(v.id === item.id) v.checked = true
					})
					this.storeGoodsSelectedList.push(item.id)
				}
				this.shoppingCartMoneyCalculate()
			},
			
			// 购物车数量加减 e = 计步器值 、id = 购物车ID、type = 购物车类型、index = 操作列表某一项（双层数组需要传，单层数组可以不传）
			async changeCartNumbers( e, id ){
				console.log( id, e.value )
				try{
					await this.$u.api.storeShoppingCartChangeNum({cart_id: id, nums: e.value})
					this.storeGoodsList.forEach(v => { if (v.id == id) v.nums = e.value }) //普通商品数组
					this.shoppingCartMoneyCalculate()
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 结算 type 配送方式 2 = 打包外带、3 = 门店场饮
			settlement( type ){
				console.log('------------我是结算')
				let storeGoodsCheckList = this.storeGoodsList.filter(v => { return v.checked == true }) //选中普通商品
				console.log(storeGoodsCheckList)
				if(storeGoodsCheckList.length > 0) {
					let storeOrderInfo = {  
						order_source: 1, //订单来源 1：购物车 2：立即购买
						distribution_id: type, //配送id
						storeOrderGoodsList: [], //门店订单商品信息} //订单商品信息
					}
					storeGoodsCheckList.forEach( v => {
						let item = {} //订单商品信息
						item.goods_name = v.goods_name //商品名称
						item.goods_image = v.goods_images //商品图片
						item.goods_id = v.goods_id //商品id
						item.pack_id = v.pack_id //套餐id
						item.c_name = v.c_name //套餐名称
						item.nums = v.nums //购买数量
						item.is_cup = v.is_cup //是否为杯卖订单 0 = 非杯卖商品 1 = 杯卖商品
						storeOrderInfo.storeOrderGoodsList.push(item) //追加商品
					})
					this.muStoreOrderInfo(storeOrderInfo)
					this.jump.redirectTo(`/packageB/pages/store-order-confirm/store-order-confirm`)
				}else{
					this.feedback.toast({title:'请选择商品', icon:'error'})
				}
			},
		    
			// 删除购物车选中商品 
			deleteShoppingCartGoods(){
				let list = this.storeGoodsSelectedList
				if( list.length > 0 ) {
					this.feedback.showModal({
						content:`确认将这${ list.length }个宝贝删除？`,
						confirm: async () => {
							try{
								await this.$u.api.storeShoppingCartDel({ cart_ids: list.join(',') })
								this.feedback.toast({ title: '删除成功', icon: 'success' })
								this.getStoreShoppingCartList()
							}catch(e){
								this.feedback.toast({title: '删除失败，请重试！'})
							}
						}
					})
				}else{
					this.feedback.toast({title: '请选择删除项', icon: 'error'})
				}
			},
		}
	}
</script>

<style scoped>
	.list-con>.list-item:first-child{
		margin-top: 0;
	}

	::v-deep .u-numberbox{
		border: 1px solid #EEEEEE;
		border-radius: 8rpx;
	}
	
	::v-deep .u-icon-minus, ::v-deep .u-icon-plus{
		width: 46rpx!important;
		background-color: #FFFFFF!important;
	}
	
	::v-deep .uicon-minus, ::v-deep .uicon-plus{
		font-size: 24rpx!important;
		color: #666!important;
	}
</style>

<style>
	@import "../../../common/css/page.css";
</style>
