<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar back-icon-color="#FFF" :background="{background: '#E80404'}" title="申请退款" title-color="#FFF" />
		
		<!-- 用户退款输入框 -->
		<view class="p-24">
			<textarea class="w-654 h-320 bg-f7f7f7 b-rad-10 p-24" v-model="refund" placeholder="请输入退款理由" />
		</view>
		
		<!-- 底部按钮 -->
		<view class="p-fixed bottom-0 z-999 bg-ffffff w-p100 h-104 d-flex j-center a-center b-sh-00021200-022">
			<u-button :disabled="!canSubmit" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
			:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: !canSubmit ? '#FCE4E3' : '#E80404', border:'none'}"
			@click="sureRefund">确认</u-button>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		name:'store-order-refund',
		
		data() {
			return {
				refund:'',//退款理由
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['storeOrderDetail']),
			
			// 是否可以提交
			canSubmit() {
				if(this.$u.trim(this.refund, 'all') == '') {
					return false
				}
				return true
			}
		},
		
		onLoad() {
			this.feedback.toast({ title: '您的退款如果通过审核，微信支付将在通过后1-3个工作日原路退还。', duration:4000})
		},
		
		methods: {
			// 确认退款
			async sureRefund(){
				try{
					let { orderno, status } = this.storeOrderDetail
					let res = await this.$u.api.storeOrderRefund({orderno, refund_note: this.refund })
					this.feedback.toast({ title: '申请成功~', icon:'success '})
					setTimeout(()=>{ this.jump.navigateBack() }, 1500 )
				}catch(e){
					//TODO handle the exception
				}
			}
		}
	}
</script>

<style scoped></style>