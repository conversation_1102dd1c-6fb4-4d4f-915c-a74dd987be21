<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar title="门店订单开票" :show-border="true">
			<view class="d-flex a-center" @click="showStoreCanInvoiceListPop = true">
				<text class="font-28 font-wei text-3 w-s-now">门店</text>
				<view class="ml-06">
					<u-icon name="arrow-down-fill" :size="16" color="#E80404" />
				</view>
			</view>
			<view slot="right" class="d-flex a-center mr-24" @click="showScreenPop = !showScreenPop">
				<image class="w-44 h-44" src="https://images.vinehoo.com/vinehoomini/v3/order_invoice/funnel.png" mode="aspectFill" />
			</view>
		</vh-navbar>
		
		<!-- 订单列表 -->
		<view class="">
			<!-- 有数据 -->
			<view v-if="orderInvoiceList.length" class="fade-in pt-20 pb-124">
				<view class="d-flex a-center mb-20 mr-24 ml-30" v-for="(item, index) in orderInvoiceList" :key="index" @click="selectSingle(item)">
					<vh-check :checked="orderInvoiceSelectedList.indexOf(item.id) > -1" @click.stop="selectSingle(item)"></vh-check>
					<view class="bg-ffffff flex-1 b-rad-16 ml-30 ptb-28-plr-24">
						<view class="d-flex j-sb bb-s-01-eeeeee pb-28">
							<text class="font-24 text-3 l-h-36">订单号：{{item.orderno}}</text>
							<text class="font-24 text-9">已完成</text>
						</view>
						
						<!-- <view class="d-flex mt-28">
							<view class="w-246 h-152 b-rad-06 o-hid">
								<vh-image :loading-type="2" :src="item.banner_img" :height="152" />
							</view>
							
							<view class="flex-1 d-flex flex-column j-sb ml-12">
								<view class="">
									<view class="mb-06 font-24 text-0 l-h-34 o-hid text-hidden-2">{{item.goodsname}}</view>
									<text class="bg-f5f5f5 b-rad-08 ptb-04-plr-18 font-22 text-9">{{item.c_name}}</text>
								</view>
								<view class="d-flex j-sb a-center">
									<text class="font-24 text-9">x{{item.pay_number}}</text>
									<text class="font-28 text-3 l-h-32"><text class="font-22">¥</text>{{item.pay_money}}</text>
								</view>
							</view>
						</view> -->
						<view class="mt-28 font-28 font-wei text-3"><text class="text-e80404">门店</text>：{{storeCanInvoiceInfo.store_name}}</view>
						<view class="mt-10">
							<view class="">
								<view class="mb-10 font-24 text-0 l-h-34 o-hid text-hidden-2">{{item.goodsname}}</view>
								<text class="bg-f5f5f5 b-rad-08 ptb-04-plr-18 font-22 text-9">{{item.c_name}}</text>
							</view>
							<view class="d-flex j-sb a-center mt-20">
								<text class="font-24 text-9">x{{item.pay_number}}</text>
								<text class="font-28 text-3 l-h-32"><text class="font-22">¥</text>{{item.pay_money}}</text>
							</view>
						</view>
					</view>
				</view>
				
				<u-loadmore :status="loadStatus" />
			</view>
			
			<!-- 无数据 -->
			<vh-empty v-else :padding-top="270" :padding-bottom="720" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png" text="暂无门店开票订单~" />
		</view>
	
	    <!-- 弹框 -->
	    <view class="">
	    	<!-- 已消费且支持开票的门店列表弹框 -->
	    	<u-popup v-model="showStoreCanInvoiceListPop" mode="bottom" :border-radius="20">
	    		<view class="pt-40 pl-48 pr-48">
	    			<view class="d-flex j-center a-center font-36 font-wei text-3">已消费且支持开票的门店列表</view>
	    			<view class="mt-20">
	    				<view class="d-flex j-sb a-center bb-s-01-eeeeee ptb-40-plr-00" v-for="(item, index) in storeCanInvoiceList" :key="item.id" @click="selectStore( item, index )">
	    					<text class="font-28 font-wei text-3">{{item.store_name}}</text>
	    					<vh-check :width="36" :height="36" :checked="sid == item.id"  @click="selectStore( item, index )"/>
	    				</view>
	    			</view>
	    		</view>
	    	</u-popup>
	    	
	    	<!-- 筛选弹框 -->
	    	<u-popup v-model="showScreenPop" mode="top" :height="710" :border-radius="20" :z-index="979" :custom-style="{top: system.navigationBarHeight() + 'px'}">
	    		<view class="p-rela h-p100 o-scr-y">
	    			<!-- 筛选内容 -->
	    			<view class="pt-36 pl-28 pr-28">
	    				<!-- 日期 -->
	    				<view class="">
	    					<view class="font-28 text-6 l-h-40">订单日期</view>
	    					<view class="d-flex j-sb a-center mt-20">
	    						<view class="w-308 h-70 bg-f6f6f6 b-rad-36 d-flex j-center a-center" :class="orderStartTime ? 'font-32 text-3' :'font-28 text-cfcfcf'" @click="showStartTimePicker = true">
	    							{{orderStartTime ? `${orderStartTime.year}.${orderStartTime.month}.${orderStartTime.day}` :'起始日期'}}
	    						</view>
	    						<view class="w-32 h-01 bg-999999" />
	    						<view class="w-308 h-70 bg-f6f6f6 b-rad-36 d-flex j-center a-center" :class="orderEndTime ? 'font-32 text-3' :'font-28 text-cfcfcf'" @click="showeEndTimePicker = true">
	    							{{orderEndTime ? `${orderEndTime.year}.${orderEndTime.month}.${orderEndTime.day}` :'终止日期'}}
	    						</view>
	    					</view>
	    				</view>
	    				
	    				<!-- 可开票金额范围 -->
	    				<view class="mt-60">
	    					<view class="font-28 text-6 l-h-40">可开票金额范围</view>
	    					<view class="d-flex j-sb a-center mt-20">
	    						<view class="w-308 h-70 bg-f6f6f6 b-rad-36 d-flex j-center a-center text-cfcfcf">
	    							<input class="text-center font-32 text-3" type="number" v-model="minMoney" placeholder="最低价" placeholder-style="color:#cfcfcf;font-size:28rpx;"/>
	    						</view>
	    						<view class="w-32 h-01 bg-999999"></view>
	    						<view class="w-308 h-70 bg-f6f6f6 b-rad-36 d-flex j-center a-center text-cfcfcf">
	    							<input class="text-center font-32 text-3" type="number" v-model="maxMoney" placeholder="最高价" placeholder-style="color:#cfcfcf;font-size:28rpx;"/>
	    						</view>
	    					</view>
	    				</view>
	    			</view>
	    			
	    			<!-- 操作按钮 -->
	    			<view class="p-fixed bottom-0 z-999 w-p100">
	    				<view class="h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center ptb-00-plr-36">
	    					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
	    					:custom-style="{width:'308rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#999', backgroundColor: '#EEE', border:'none'}"
	    					@click="reset()">重置</u-button>
	    					<u-button :disabled="!orderCanFilter" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
	    					:custom-style="{width:'308rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: !orderCanFilter ? '#FCE0E0': '#E80404', border:'none'}"
	    					@click="confirm()">确定</u-button>
	    				</view>
	    			</view>
	    		</view>
	    	</u-popup>
	        
	    	<!-- 起始日期弹框 -->
	    	<u-picker v-model="showStartTimePicker" mode="time" confirm-color="#E80404" @confirm="confirmStartTime" />
	    	
	    	<!-- 终止日期弹框 -->
	    	<u-picker v-model="showeEndTimePicker" mode="time" confirm-color="#E80404" @confirm="confirmEndTime" />
	    </view> 
		
		<!-- 底操作按钮 -->
		<view class="fade-in-up-medium p-fixed bottom-0 z-600 w-p100">
			<view class="h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-28 pr-24">
				<view class="d-flex a-center">
					<view class="d-flex a-center">
						<vh-check :checked="isSelectAll()" :margin-top="10" @click="selectAll()"/>
						<view class="ml-08 font-32 text-3">全选</view>
					</view>
					
					<view class="ml-28">
						<view class="d-flex a-center">
							<text class="font-28 font-wei text-3">合计：</text>
							<text class="font-40 font-wei text-e80404"><text class="font-28">¥</text>{{totalMoney}}</text>
						</view>
						
						<view class="d-flex a-center font-20 text-3 l-h-28">订单: {{orderInvoiceSelectedList.length}}个</view>
					</view>
				</view>
				
				<u-button :disabled="!canNext" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
				:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: !canNext ? '#FCE4E3' : '#E80404', border:'none'}"
				 @click="jump.navigateTo(`/packageE/pages/invoice-mangement/invoice-mangement?comeFrom=2`)">下一步</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default {
		name:'order-invoice',  
		
		data() {
			return {
				sid:'', //门店id
				showStoreCanInvoiceListPop:false, //是否显示门店可开发票列表
				storeCanInvoiceList:[], //门店可开发票列表（ 用户消费国，且支持开票的门店 ）
				storeCanInvoiceInfo:{}, //门店可开票信息
				showScreenPop: false, //筛选弹框
				showStartTimePicker: false, //起始日期弹框
				showeEndTimePicker: false, //终止日期弹框
				// orderTypeList: [{ type:0, name:'闪购' }, {type:1, name:'秒发'}, {type:3, name:'尾货'}], //订单类型列表
				orderStartTime: '', //订单开始时期
				orderEndTime: '', //订单结束时间
				minMoney: '', //最小金额
				maxMoney: '', //最大金额
				// orderType: null, //订单类型 0 = 闪购、1 = 秒发、3 = 尾货
				hasGetOrderInvoiceList: 0, //是否已经请求过订单开票列表
				orderInvoiceList: [], //订单开票列表
				orderInvoiceSelectedList:[], //选中的订单开票列表
				page: 1, //第几页
				limit: 10, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //加载状态
				totalMoney:0, //合计金额
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['invoiceInfo']),
			
			// 订单是否可以过滤
			orderCanFilter() {
				// 三个条件
				// 1、选择了订单起始日期和订单终止日期且订单起始日期小于订单终止日期且没有选择金额 true
				// 2、选择了订单金额最低价跟订单金额最高价订单金额最低价小于订单金额最高价且没有选择日期 true
				// 3、订单起始日期小于订单终止日期且订单金额最低价小于订单金额最高价 true
				if( this.orderStartTime && this.orderEndTime && this.orderStartTime.timestamp < this.orderEndTime.timestamp && this.minMoney === '' && this.maxMoney === ''){
					return true
				}else if(this.minMoney && this.maxMoney && Number(this.minMoney) < Number(this.maxMoney) && this.orderStartTime === '' && this.orderEndTime === ''){
					return true
				}else if((this.orderStartTime && this.orderEndTime && this.orderStartTime.timestamp < this.orderEndTime.timestamp) && (this.minMoney && this.maxMoney && Number(this.minMoney) < Number(this.maxMoney))) {
					return true
				}
				return false
			},
		    
			// 是否可以进行下一步
			canNext() {
				if(this.orderInvoiceSelectedList == 0) {
					return false
				}
				return true
			}
		},
		
		onLoad() {
			this.system.setNavigationBarBlack()
		},
		
		onShow() {
			this.init()
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muInvoiceInfo']),
			
			// 初始化
			async init() {
				// await this.getOrderInvoiceList()
				// await this.openInvoice()
				await this.getStoreCanInvoiceList()
				await Promise.all([this.getOrderInvoiceList(), this.openInvoice()])
			},
			
			// 获取门店可开票订单列表
			async getStoreCanInvoiceList() {
				let res = await this.$u.api.storeCanInvoiceList()
				let { list } = res.data
				this.storeCanInvoiceList = list
				this.storeCanInvoiceInfo = list[0]
				this.sid = list[0].id
			},
			
			// 选择门店 item = 门店列表每一项
			selectStore( item, index ) {
				this.page = 1
				this.sid = item.id
				this.storeCanInvoiceInfo = this.storeCanInvoiceList[index] //选中可开票门店
				this.orderInvoiceSelectedList = [] //重置选中项
				this.getOrderInvoiceList()
			},
			
			// 获取订单可开票列表
			async getOrderInvoiceList() {
				if( this.hasGetOrderInvoiceList ) this.feedback.loading()
				let data = {
					page: this.page,
					limit: this.limit,
					sid: this.sid
				}
				// 传入起始日期终止日期
				if( this.orderStartTime && this.orderEndTime ){
					data.stime = `${this.orderStartTime.year}-${this.orderStartTime.month}-${this.orderStartTime.day} 00:00:00`
					data.etime = `${this.orderEndTime.year}-${this.orderEndTime.month}-${this.orderEndTime.day} 00:00:00`
				}
				// 传入最低价、最高价
				if( this.minMoney && this.maxMoney ) {
					data.smoney = this.minMoney
					data.emoney = this.maxMoney
				}
				// 传入订单类型
				// if(this.orderType !== null) {
				// 	data.order_type = this.orderType
				// }
				
				console.log('--------这是要上传的参数')
				console.log(data)
				let res = await this.$u.api.storeOrderInvoiceList(data)
				let { list, total } = res.data
				this.hasGetOrderInvoiceList = 1
				this.feedback.hideLoading()
				this.page == 1 ? this.orderInvoiceList = list : this.orderInvoiceList = [ ...this.orderInvoiceList, ...list ]
				this.totalPage = Math.ceil(total / this.limit)
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				this.showScreenPop = false
				this.showStoreCanInvoiceListPop = false
			},
			
			// 开发票
			async openInvoice() {
				if( Object.keys(this.invoiceInfo).length ) {
					console.log('-----------------------------发票信息')
					console.log(this.invoiceInfo)
					try{
						let { id, type_id, telephone, email, invoice_name, taxpayer } = this.invoiceInfo
						let data = {} //需要上传的数据
						data.orders = this.orderInvoiceSelectedList.join(',') //多个订单号 逗号分隔
						data.way = 1//0:选择发票 1:临时提交发票抬头
						// data.receipt_id = id //发票抬头id way=0时必传
						data.type_id = type_id //发票类型 1 个人 2 公司 way=1时必传
						data.telephone = telephone //手机号码 way=1时必传
						data.email = email //邮箱 way=1时必传
						data.invoice_name = invoice_name //发票抬头 way=1时必传
						data.taxpayer = taxpayer //纳税人识别号 way=1时必传
						console.log('-------------------我是开发票的参数')
						console.log(data)
						let res = await this.$u.api.storeOrderOpenInvoice(data)
						// if(res.error_code == 0) {
							this.feedback.toast({title: '开票成功！'})
							// 重置
							this.orderInvoiceSelectedList = []
							this.totalMoney = 0
							this.muInvoiceInfo({})
							uni.removeStorageSync('InvoiceInfo');
							setTimeout(() => {this.getOrderInvoiceList()}, 1500)
						// }
						console.log('--------------------我是开票的res')
						console.log(res)
					}catch(e) {
						console.log(e)
					}
				}else{
					console.log('bbb')
				}
			},
			
			// 选中单个/取消选中单个 item = 订单开票列表某一项
			selectSingle(item) {
				if(this.orderInvoiceSelectedList.indexOf(item.id) > -1){
					this.orderInvoiceList.forEach( v => { if(v.id === item.id) v.checked = false })
					this.orderInvoiceSelectedList.splice(this.orderInvoiceSelectedList.indexOf(item.id), 1)
				}else{
					this.orderInvoiceList.forEach( v => { if(v.id === item.id) v.checked = true })
					this.orderInvoiceSelectedList.push(item.id)
				}
				this.getTotalMoney()
			},
			
			// 判断是否全选
			isSelectAll(){
				return this.orderInvoiceList.length === this.orderInvoiceSelectedList.length && this.orderInvoiceSelectedList.length
			},
			
			// 全选/取消全选
			selectAll(){
				if(this.isSelectAll()){
					this.orderInvoiceList.forEach( v => { v.checked = false })
					this.orderInvoiceSelectedList = []
				}else{
					this.orderInvoiceSelectedList = this.orderInvoiceList.map( v => {
						v.checked = true
						return v.id
					})
				}
				this.getTotalMoney()
			},
			
			// 获取总金额
			getTotalMoney() {
				let total = 0;
				this.orderInvoiceList.forEach( v => {
					 if(this.orderInvoiceSelectedList.indexOf(v.id) > -1) total += parseFloat(v.pay_money)
				})
				this.totalMoney = total
			},
			
			// 确认开始时间 e = 开始时间信息
			confirmStartTime(e) {
				this.orderStartTime = e
			},
			
			// 确认结束时间 e = 结束时间信息
			confirmEndTime(e) {
				this.orderEndTime = e
			},
			
			// 重置
			reset() {
				this.page = 1
				this.orderStartTime = ''
				this.orderEndTime = ''
				this.minMoney = ''
				this.maxMoney = ''
				// this.orderType = null
				this.getOrderInvoiceList()
			},
			
			// 确定
			confirm() {
				this.getOrderInvoiceList()
			},
	
		},
		
		onReachBottom() {
			if (this.page == this.totalPage || this.totalPage == 0 ) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getOrderInvoiceList()
		}
        
	}
</script>

<style>
	@import "../../../common/css/page.css";
</style>
