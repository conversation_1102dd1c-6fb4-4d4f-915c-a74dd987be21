<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar back-icon-color="#FFF" :background="{background: '#E80404'}" title="门店订单" title-color="#FFF" />
		
		<!-- tabs选项栏 -->
		<view class="p-stic z-980 bt-s-01-eeeeee" :style="{top: system.navigationBarHeight() + 'px'}">
			<u-tabs :list="tabList" :current="currentTabs" :height="92" :font-size="28" inactive-color="#333" active-color="#E80404"
			 :bar-width="36" :bar-height="8" :bar-style="{background:'linear-gradient(214deg, #FF8383 0%, #E70000 100%)'}" :is-scroll="false" @change="changeTabs" />
		</view>
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in">
			<!-- 门店订单列表 -->
			<view class="">
				<!-- 有门店订单列表 -->
				<view v-if="storeOrderList.length" class="ptb-20-plr-00">
					<view class="bg-ffffff b-rad-16 mr-24 mb-20 ml-24 pr-20 pb-32 pl-20" v-for="(item, index) in storeOrderList" :key="item.id" @click="openStoreOrderDetail(item)">
						<!-- 订单信息 -->
						<view class="">
							<view class="d-flex j-sb a-center bb-s-01-eeeeee mb-32 ptb-32-plr-00">
								<view class="d-flex a-center">
									<view class="bg-li-21 b-tr-16-bl-16 ptb-04-plr-16 font-20 text-ffffff">{{item.send_type == 1 ? '物流配送' : item.send_type == 2 ? '门店自提' : '门店场饮'}}</view>
									<view class="w-max-430 ml-06 font-28 font-wei text-3 text-hidden-1">{{item.store_name}}</view>
								</view>
								<view class="">
									<view v-if="item.refund_status == 1" class="font-24 text-e80404">退款中</view>
									<view v-else-if="item.refund_status == 2" class="font-24 text-e80404">退款成功</view>
									<view v-else-if="item.refund_status == 3 && item.invoice_speed !== 1" class="font-24 text-e80404">退款失败</view>
									<view v-else-if="item.invoice_speed == 1 && item.status == 3" class="font-24 text-666">已开票</view>
									<view v-else class="font-24 text-e80404" v-for="(inItem, inIndex) in tabList" :key="inIndex"><text v-if="inIndex == item.status">{{inItem.name}}</text></view>
								</view>
							</view>
							
							<view class="d-flex mb-20" v-for="(inItem, inIndex) in item.goods_info" :key="inIndex">
								<vh-image :loading-type="2" :src="inItem.goods_images" :width="136" :height="136" :border-radius="6" mode="aspectFit"/>
								<view class="flex-1 d-flex flex-column j-sb ml-16">
									<view class="font-26 text-3 text-hidden-2 l-h-36">{{inItem.goods_name}}</view>
									<view class="d-flex j-sb">
										<text class="bg-eeeeee ptb-02-plr-12 b-rad-06 font-20 text-9">{{inItem.c_name}}</text>
										<text class="font-24 text-9">x{{inItem.pay_number}}</text>
									</view>
								</view>
							</view>
							
							<view class="d-flex j-sb mt-32">
								<view class="font-24 text-9 l-h-34">订单号：{{item.status == 0 ? item.mainorderno : item.orderno}}</view>
								<view class="ml-20 text-3">
									<text class="font-18">实付款：</text>
									<text class="font-22 font-wei">¥</text>
									<text class="font-28 font-wei">{{item.status == 0 ? item.pay_money : item.order_money}}</text>
								</view>
							</view>
						</view>
						
						<!-- 操作按钮 -->
						<view class="d-flex j-end a-center mt-32">
							<!-- 线下支付核销码 -->
							<view v-if="item.status == 0 && item.pay_type == 2">
								<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'54rpx', fontSize:'24rpx' ,fontWeight:'500', color:'#FFF', backgroundColor: '#E80404', border:'none'}" 
								@click.stop="getWriteOffCode(item)">出示核验码</u-button>
							</view>
							
							<!-- 立即支付（线上支付） -->
							<view v-if="item.status == 0 && item.pay_type == 1" class="ml-20">
								<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'54rpx', fontSize:'24rpx' ,fontWeight:'500', color:'#E80404', backgroundColor: '#fff', border:'1rpx solid #E80404'}" 
								@click.stop="payNow(item)">立即支付</u-button>
							</view>
							
							<!-- 申请退款 -->
							<view v-if="item.status != 0 && item.status != 3 && item.refund_status == 0 && item.is_cup == 0" class="ml-20">
								<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'54rpx', fontSize:'24rpx' ,fontWeight:'500', color:'#E80404', backgroundColor: '#fff', border:'1rpx solid #E80404'}" 
								@click.stop="refundMoney(item)">申请退款</u-button>
							</view>
							
							<!-- 展示核销码 -->
							<view v-if="item.status == 1 && item.send_type != 1 && (item.refund_status == 0 || item.refund_status == 3) && item.pay_type == 1" class="ml-20">
								<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'54rpx', fontSize:'24rpx' ,fontWeight:'500', color:'#E80404', backgroundColor: '#fff', border:'1rpx solid #E80404'}" 
								@click.stop="getWriteOffCode(item)">展示核销码</u-button>
							</view>
							
							<!-- 确认收货 -->
							<view v-if="item.status == 2 && item.send_type == 1 && item.pay_type == 1" class="ml-20">
								<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'54rpx', fontSize:'24rpx' ,fontWeight:'500', color:'#E80404', backgroundColor: '#fff', border:'1rpx solid #E80404'}" 
								@click.stop="confirmReceipt(item)">确认收货</u-button>
							</view>
						</view>
					</view>
					
					<u-loadmore :status="loadStatus" />
				</view>
                
				<!-- 暂无门店订单 -->
				<vh-empty v-else :padding-top="300" :padding-bottom="680" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png" text="暂无门店订单~" />
			</view>
		    
			<!-- 门店开发票按钮 -->
			<view v-if="canInvoice && currentTabs == 3" class="fade-in p-fixed z-100 bottom-148 right-32 w-86 h-86 bg-ffffff d-flex j-center a-center b-sh-00021200-022 b-rad-p50 font-24 text-3" @click="showInvoiceMask = true">发票</view>
			
			<!-- 弹框 -->
			<view class="">
				<!-- 门店开发票弹框 -->
				<u-mask :show="showInvoiceMask" @click="showInvoiceMask = false">
					<view class="p-rela h-vh-100 o-hid">
						<view class="p-abso bottom-160 right-140 bg-ffffff b-rad-10 w-200 h-224">
							<view class="h-112 d-flex j-center a-center bb-s-01-eeeeee font-28 font-wei text-3" @click.stop="jump.navigateTo( routeTable.pBStoreOrderInvoice )">开发票</view>
							<view class="p-rela h-112 d-flex j-center a-center" @click.stop="jump.navigateTo( routeTable.pBStoreHistoryInvoice )">
								<view class="triangle p-abso right-n-10 bottom-28"></view>
								<text class="font-28 font-wei text-3">历史发票</text>
							</view>
						</view>
					</view>
				</u-mask>
			    
				<!-- 核销码 -->
				<u-mask :show="showCodeMask" @click="showCodeMask = false">
					<view class="h-p100 d-flex j-center a-center">
						<view class="code-bg w-530 h-750">
							<view class="w-530 h-580 d-flex flex-column j-center a-center">
								<vh-image :loading-type="2" :src="writeOffCode" :width="300" :height="300" :duration="0"/>
								<text class="mt-40 font-36 font-wei text-3">{{storeOrderInfo.status == 0 ? storeOrderInfo.mainorderno : storeOrderInfo.orderno}}</text>
							</view>
							<view class="w-530 h-150 d-flex j-center a-center font-40 font-wei text-3">未使用</view>
						</view>
					</view>
				</u-mask>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		name:'store-order',
		
		data() {
			return{
				loading: false, //数据是否加载完成
				tabList: [ //状态栏选项
					{ name:'待支付' },
					{ name:'已支付' },
					{ name:'已发货' },
					{ name:'已完成' }
				],
				currentTabs: 0, //当前选中tabs
				canInvoice: false, //是否可以开票
				hasGotOrderList: 0, //是否请求过订单列表
				storeOrderList: [], //门店订单列表
				storeOrderInfo:{}, //门店订单信息
				showCodeMask: false, //门店核销码
				writeOffCode:'', //核销码
				showInvoiceMask: false, //是否显示开发票遮罩层
				page: 1, //当前页
				limit: 10, //每页限制多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //加载状态
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable', 'storeOrderDetail']),
		},
		
		onShow() {
			this.init()
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muStoreOrderDetail']),
			
			// 初始化
			async init() {
				this.page = 1
				try {
					await Promise.all([ this.getStoreOrderList(), this.getStoreCanInvoiceList() ])
					this.loading = false
				}catch(e) {
					
				}
			},
			
			// 切换状态栏
			changeTabs(index) {
				this.currentTabs = index
				this.page = 1
				this.getStoreOrderList()
			},
			
			// 获取门店订单列表
			async getStoreOrderList() {
				if( this.hasGotOrderList ) this.feedback.loading()
				let data = {}
				data.page = this.page //当前页
				data.limit = this.limit //每页限制多少条
				data.status = this.currentTabs //当前选中tabs
				let res = await this.$u.api.storeOrderList(data) 
				let { list, total } = res.data
				this.page == 1 ? this.storeOrderList = list : this.storeOrderList = [...this.storeOrderList, ...list]
				this.totalPage = Math.ceil( total / this.limit )
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				this.hasGotOrderList = 1
				this.feedback.hideLoading()
				uni.stopPullDownRefresh() //停止下拉刷新
				if(this.currentTabs === 0 && this.storeOrderList.length > 0 && this.storeOrderList[0].pay_type === 2){ //线下支付·
					this.getWriteOffCode(this.storeOrderList[0])
				}
			},
			
			// 获取门店可开票订单列表
			async getStoreCanInvoiceList() {
				let res = await this.$u.api.storeCanInvoiceList()
				if( res.data.list.length > 0) {
					this.canInvoice = true
				}
				console.log(res)
			},
			
			// 立即支付 item = 门店订单每一项
			payNow( item ) {
				this.feedback.loading({title: '拉取支付中...', icon: 'success'})
				uni.getStorage({ //获取用户登录信息
					key: 'loginInfo',
					success: async s => {
						try {
							const { mainorderno, sid, pay_method } = item //订单列表每一项信息
							let payParams = {} //支付信息
							if( pay_method == 0 ) { //门店微信支付
								console.log('---------------我是门店微信支付')
								let res = await this.$u.api.storePay({ orderno: mainorderno , sid, openid: s.data.openid })
								payParams = res.data //微信支付信息
							}else{ //银联支付
							    console.log('---------------我是银联支付')
								let res = await this.$u.api.payMethod({ main_order_no: mainorderno, payment_method: 4 , order_type:20, is_cross:0, open_id: s.data.openid })
								payParams = res.data.mini_pay_info //微信支付信息
							}
							this.requestPayment(payParams) //拉起支付
						}catch(e) {
							// this.feedback.toast({ title: '支付出现异常' })
						}
					},
					fail:() => {
						console.log('---获取用户信息失败')
					}
				})
			},
			
			// 拉起支付 payParams = 支付参数
			requestPayment( payParams ) {
				wx.requestPayment({
					timeStamp: payParams.timeStamp,
					nonceStr: payParams.nonceStr,
					package: payParams.package,
					signType: payParams.signType,
					paySign: payParams.paySign,
					success: suc => {
						this.feedback.toast({ title: '支付成功~', icon:'success' })
						this.page = 1
						this.currentTabs = 1
						this.getStoreOrderList()
					},
					fail: err => {
						if( err.errMsg == 'requestPayment:fail cancel' ) {
							this.feedback.toast({ title:'您取消了支付~' })
						}else {
							this.feedback.toast({ title: err.errMsg })
						}
					},
					complete: com => {
						this.feedback.hideLoading()
					}
				})
			},
			
			// 打开门店订单详情 item = 订单列表某一项
			openStoreOrderDetail( item ) {
				this.muStoreOrderDetail(item)
				this.jump.navigateTo(`/packageB/pages/store-order-detail/store-order-detail`)
			},
			
			// 申请退款 item = 订单列表某一项
			refundMoney( item ){
				console.log(item)
				this.muStoreOrderDetail(item)
				this.jump.navigateTo(`/packageB/pages/store-order-refund/store-order-refund`)
			},
			
			// 获取核销码 item = 订单列表某一项
			async getWriteOffCode( item ) {
				try{
					this.storeOrderInfo = item
					this.showCodeMask = true
					let res = await this.$u.api.writeOffCode({code: item.status == 0 ? item.mainorderno : item.orderno})
					this.writeOffCode = res.data
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 确认收货 item = 确认收货
			async confirmReceipt( item ) {
				console.log(item)
				try{
					await this.$u.api.storeOrderConfirmReceipt({ orderno: item.orderno })
					this.page = 1
					this.getStoreOrderList()
					this.feedback.toast({ title:'收货成功~', icon:'success' })
				}catch(e){
					//TODO handle the exception
				}
				
			},
		},
		
		onPullDownRefresh() {
			this.page = 1
			this.getStoreOrderList()
		},
		
		onReachBottom() {
			if ( this.page == this.totalPage || this.totalPage == 0) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getStoreOrderList()
		}
	}
</script>

<style scoped>
	.triangle{
		border-left: 20rpx solid transparent;
		border-right: 20rpx solid #ffffff;
		border-bottom: 20rpx solid transparent;
		transform: rotate(45deg);
	}
	.code-bg {
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/wine_party_order_detail/code_bg.png);
		background-size: cover;
	}
</style>

<style>
	@import "../../../common/css/page.css";
</style>
