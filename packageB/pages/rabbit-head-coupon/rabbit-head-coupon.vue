<template>
	<view class="content">
		<!-- 导航栏 -->
		<view v-if="from ==''" class="">
			<vh-navbar title="兔头商店" />
		</view>
		
		<!-- 背景 -->
		<view class="p-rela w-p100 h-vh-100">
			<image class="p-abso w-p100 h-p100" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_cou_bann.png" mode="widthFix" />
			
			<!-- 兔头优惠券 -->
			<view class="p-rela z-1 pt-100 d-flex j-center a-center">
				<view class="cou-bg w-718 h-1050">
					<view v-if="hasExchanged" class="d-flex flex-column">
						<view class="d-flex j-center a-center mt-102">
							<image class="w-26 h-26" src="https://images.vinehoo.com/vinehoomini/v3/comm/cir_sel.png" mode="widthFix"></image>
							<text class="ml-10 font-36 font-wei text-3">兑换成功</text>
						</view>
						
						<view class="mt-40 d-flex j-center a-center">
							<view class="cou-mon w-600 h-318 d-flex j-center a-center">
								<view class="font-180 font-wei text-e80404">{{exchangeSuccInfo.price}}</view>
								<view class="bg-e80404 w-01 h-124 ml-28 mr-34"></view>
								<view class="font-56 font-wei text-e80404">
									<view class="l-h-48">RMB</view>
									<view class="mt-20 l-h-48">优惠券</view>
								</view>
							</view>
						</view>
						
						<view class="mt-22 text-center font-28 text-9">使用有效期至 <text class="font-wei ml-06">{{exchangeSuccInfo.expire_time}}</text></view>
						
						<view class="mt-130 ml-58 mr-54">
							<view class="d-flex j-sb a-center pb-32 bb-s-01-eeeeee">
								<view class="font-30 font-wei text-3">兑换条件</view>
								
								<view class="d-flex a-center">
									<image class="w-32 h-34" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png" mode="widthFix"></image>
									<text class="ml-10 font-36 text-e80404">{{exchangeSuccInfo.rabbit_price}}</text>
								</view>
							</view>
							
							<view class="d-flex j-sb a-center pt-32 pb-32 bb-s-01-eeeeee">
								<text class="font-30 font-wei text-3">兑换单号</text>
								<text class="font-24 text-3">{{exchangeSuccInfo.exchange}}</text>
							</view>
							
							<view class="d-flex j-sb a-center pt-32 pb-32">
								<text class="font-30 font-wei text-3">兑换时间</text>
								<text class="font-24 text-3">{{exchangeSuccInfo.exchange_time}}</text>
							</view>
						</view>
					</view>
				    
					<view v-else class="d-flex flex-column a-center">
						<view class="h-650 d-flex flex-column j-center a-center ptb-00-plr-48">
							<view class="mt-50 text-center font-36 font-wei text-3 text-hidden-2">{{rabbitCoupon.title}}</view>
							
							<view class="mt-40 text-center">
								<text class="font-60 font-wei text-e80404">¥</text>
								<text class="ml-10 font-240 font-wei text-e80404 l-h-240">{{rabbitCoupon.price}}</text>
							</view>
							
							<view class="mt-10 text-center font-30 font-wei text-6 text-hidden-2">{{rabbitCoupon.brief}}</view>
							<template v-if="rabbitCoupon.coupon_info">
							<view v-if="rabbitCoupon.coupon_info.validity_days" class="mt-40 text-center font-28 text-9">注：兑换后{{rabbitCoupon.coupon_info.validity_days}}日内有效，请及时使用。</view>
							<view v-else class="mt-40 text-center font-28 text-9">注：兑换后{{rabbitCoupon.coupon_info.effected_time}} 至 {{rabbitCoupon.coupon_info.invalidate_time}}有效</view>
							</template>
						</view>
						
						<view class="w-p100 pt-40 pl-54 pr-52">
							<view class="d-flex j-sb a-center pb-32 bb-s-01-eeeeee">
								<view class="font-30 font-wei text-3">兑换条件</view>
								
								<view class="d-flex a-center">
									<image class="w-32 h-34" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png" mode="widthFix"></image>
									<text class="ml-10 font-36 text-e80404">{{rabbitCoupon.rabbit_price}}</text>
								</view>
							</view>
							
							<view class="mt-32">
								<view class="font-30 font-wei text-3">商品详情</view>
								<view class="mt-24 font-28 text-9 l-h-40 text-hidden-3" v-html="rabbitCoupon.detail">
									
								</view>
								<!-- <view class="mt-24 font-28 text-9 l-h-40">优惠券不折现、不找零、不可叠加使用，单张订单限使用一张优惠券，全场可用，跨境除外，
								优惠券有效期{{rabbitCoupon.coupon_info.validity_days ? `${rabbitCoupon.coupon_info.validity_days}天` : ` ${rabbitCoupon.coupon_info.effected_time} 至 ${rabbitCoupon.coupon_info.invalidate_time}`}}。</view> -->
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部按钮 -->
			<view v-if="!hasExchanged" class="d-flex j-center a-center mt-50">
				<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
				:custom-style="{width:'646rpx', height:'80rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
				@click="exchangeNow">立即兑换</u-button>
			</view>
			<view v-else class="d-flex j-center a-center mt-50">
				<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
				:custom-style="{width:'646rpx', height:'80rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
				@click="onJump">去使用</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		name: 'rabbit-head-coupon',
		
		data(){
			return{
				from:'', //从哪个端进入 1 = 安卓、2 = ios"
				rabbitCouponId:'', //兔头优惠券id
				jumpGoodsId: '',
				rabbitCoupon:{}, //兔头优惠券信息
				hasExchanged: 0, //是否兑换过
				exchangeSuccInfo:{}, //兑换成功信息
			}
		},
		
		onLoad(options) {
			this.rabbitCouponId = parseInt(options.id)
			this.jumpGoodsId = options.jumpGoodsId
			if(options.from){
				this.from = options.from
				this.muFrom(this.from) //保存来自哪个状态（解决用户在安卓、ios端登录失效的问题）
			}
			this.getRabbitCouponDetail()
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muFrom']),
			
			async getRabbitCouponDetail() {
				let res = await this.$u.api.rabbitCouponDetail({id: this.rabbitCouponId})
				console.log(res)
				this.rabbitCoupon = res.data
			},
			
			// 立即兑换
			exchangeNow() {
				console.log('----------------我是立即兑换')
				let { rabbit_price, price } = this.rabbitCoupon
				this.feedback.showModal({
					content:`您确认花费${rabbit_price}兔头去兑换${price}元优惠券吗？`,
					confirm: async () => {
						console.log('-------您点击了确定')
						console.log(this.rabbitCoupon.id)
						this.feedback.loading({ title:'兑换中' })
						try {
							let res = await this.$u.api.rabbitExchangeCoupon({ id: this.rabbitCoupon.id })
							console.log(res)
							this.exchangeSuccInfo = res.data
							this.hasExchanged = 1
							this.feedback.hideLoading()
						}catch(e) {
							// this.feedback.toast({ title: '兑换异常，请重试!'})
						}
					},
					cancel: () => {
						console.log('-------您点击了取消')
					}
				})
			},
			onJump () {
				if (this.jumpGoodsId) {
					const delta = 2
					const pageLen = this.pages.getPageLength()
					if (pageLen >= delta) {
						const page = getCurrentPages()[pageLen - delta - 1]
						const fullPath = page?.$page?.fullPath || ''
						if (fullPath.includes(this.$routeTable.pgGoodsDetail)) {
							this.jump.navigateBack(delta)
							return
						}
					}
					this.jump.appAndMiniJump( 2, `${this.$routeTable.pgGoodsDetail}?id=${this.jumpGoodsId}`, this.$vhFrom )
				} else {
					this.$u.api.userSpecifiedData({ field: 'home_select' }).then(res => {
						const { data: { home_select = '1' }} = res
						let routeTableKey = 'pgIndex'
						let type = '0'
						if (home_select === '0') {
							if (this.$app) {
								type = '3'
							} else {
								routeTableKey = 'pgMiaoFa'
							}
						}
						if (this.$app) {
							wineYunJsBridge.openAppPage({
								client_path: { ios_path: 'goMain', android_path: 'goMain' },
								ad_path_param: [
									{ ios_key: 'type', ios_val: type },
									{ android_key: 'type', android_val: type },
								]
							})
						} else {
							this.jump.switchTab(this.$routeTable[routeTableKey])
						}
					})
				}
			}
		}
	}
</script>

<style scoped>
	.cou-bg{
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/mine/rab_cou.png);
		background-size: cover;
	}
	.cou-mon{
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/mine/rab_cou_mon.png);
		background-size: cover;
	}
</style>
