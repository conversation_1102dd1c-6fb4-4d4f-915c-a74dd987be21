<template>
	<view class="content">
		<vh-navbar back-icon-color="#FFF" :background="{ background: '#E80404' }" />
		<view class="p-abso z-0 top-0 w-p100 h-250 bg-e80404" />
		<view v-if="!loading" class="pb-20">
			<view class="p-rela z-1000 flex-c-c mt-n-20">
				<image class="w-32 h-32" :src="ossIcon(MInvoiceHistoryInvoiceStatusInfo[detail.status].$statusIcon)" />
				<text class="ml-10 font-36 font-wei text-ffffff">{{ MInvoiceHistoryInvoiceStatusInfo[detail.status].$statusText }}</text>
			</view>
			<view class="p-rela z-01 mt-40 ptb-00-plr-24">
				<view class="flex-c-c-c">
					<view class="p-rela w-p100 h-192 flex-s-c-c">
						<image class="p-abso top-0 left-0 w-p100 h-192" :src="ossIcon(`/order_invoice_history_detail/head.png`)"></image>
						<view class="p-rela z-01 mt-64 font-30 font-wei text-954538">增值税{{ MInvoiceHistoryTypeInfo[detail.genre] }}</view>
					</view>
					<view :style="{ width: `calc( 100% - 16rpx )` }">
						<view class="bg-ffffff ptb-00-plr-32 mt-n-02 pt-02 pb-18">
							<view class="bb-d-01-eeeeee pb-34">
								<view v-for="(item, index) in MInvoiceHistoryDetailReceiptList" :key="index">
									<view v-if="receipt[item.key]" class="flex-sb-c mt-24 font-26 text-6 l-h-36"
									:class="{ 'mt-24' : index != 0 }">
										<text class="w-s-now">{{ item.info.name }}</text>
										<view class="w-max-510 font-wei text-3">{{ receipt[item.key] }}</view>
									</view>
								</view>
							</view>
							<view class="pt-28"
							:class="{'bb-d-01-eeeeee pb-34': MInvoiceHistoryInvoiceStatus.Success === detail.status }">
								<view class="flex-sb-c font-26 text-6 l-h-36">
									<text class="w-s-now">开票金额</text>
									<view class="w-max-510 font-wei text-e80404">¥{{ detail.invoice_price }}</view>
								</view>
								
								<view class="flex-sb-c mt-24 font-26 text-6 l-h-36">
									<text class="w-s-now">{{ MInvoiceHistoryInvoiceStatus.Pending === detail.status ? '申请时间' : '开票时间' }}</text>
									<view class="w-max-510 font-wei text-3">{{ detail.create_time }}</view>
								</view>
							</view>
							<view v-if="MInvoiceHistoryInvoiceStatus.Success === detail.status" class="pt-28">
								<view class="flex-sb-c l-h-36 font-26 text-6">
									<text>查看发票</text>
									<view class="" @click="onJumpInvoiceBrowse">
										<text class="mr-10 ml-20 font-wei text-3">发票预览</text>
										<u-icon :name="'arrow-right'" :size="12" />
									</view>
								</view>
							</view>
						</view>
						
						<image class="w-p100 h-40" :src="ossIcon(`/order_invoice_history_detail/footer.png`)" mode=""></image>
					</view>
					</view>
				</view>
				<InvoiceHistoryDetailOrder v-if="detail.orders && detail.orders.length" :list="detail.orders" />
			</view>
		</view>
	</view>
</template>

<script>
	import { MInvoiceHistoryInvoiceStatusInfo, MInvoiceHistoryTypeInfo, MInvoiceHistoryDetailReceiptList } from '@/common/js/mapper/invoiceHistory/mapper'
	import { MInvoiceHistoryInvoiceStatus } from '@/common/js/mapper/invoiceHistory/model'
	export default {
		data: () => ({
			loading: true,
			invoiceCode: '',
			detail: {},
			receipt: {},
			MInvoiceHistoryInvoiceStatus,
			MInvoiceHistoryInvoiceStatusInfo,
			MInvoiceHistoryTypeInfo,
			MInvoiceHistoryDetailReceiptList
		}),
		onLoad({ invoice_code }) {
			this.load(invoice_code).then(() => {
				this.invoiceCode = invoice_code
				this.loading = false
			})
		},
		methods: {
			async load( invoice_code ) {
				this.feedback.loading()
				const res = await this.$u.api.invoiceHistoryDetail({ invoice_code })
				this.detail = res?.data || {}
				this.receipt = this.detail?.receipt || {}
			},
			onJumpInvoiceBrowse() {
				if( this.invoiceCode && this.detail.pdf_url ) {
					this.jump.navigateTo(`${this.$routeTable.pBOrderInvoiceBrowse}?invoice_code=${this.invoiceCode}&url=${encodeURIComponent(this.detail.pdf_url)}`)
				}else{
					this.feedback.toast({ title: '未获取到发票地址，暂不支持预览' })
				}
			}
		}
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>