<template>
  <view class="content" :class="loading || showRuleMask ? 'h-vh-100 o-hid' : ''">
    <!-- 导航栏 -->
    <view class="">
      <view v-if="from == ''" class="">
        <u-navbar :is-back="false" :background="{ background: navBackgroundColor }">
          <image
            class="ml-24 w-44 h-44"
            src="https://images.vinehoo.com/vinehoomini/v3/pay_success/back_arr.png"
            mode="aspectFill"
            @click="showLeaveModal(0)"
          />
        </u-navbar>
      </view>
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: navBackgroundColor }">
          <view :style="{ height: appStatusBarHeight + 'px' }"></view>
          <view class="h-px-46 d-flex j-sb a-center">
            <image
              class="ml-24 w-44 h-44"
              src="https://images.vinehoo.com/vinehoomini/v3/pay_success/back_arr.png"
              mode="aspectFill"
              @click="showLeaveModal(0)"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 数据加载完成 -->
    <view v-if="!loading" class="bg-f5f5f5">
      <!-- banner -->
      <view class="p-abso top-0 w-p100 h-820">
        <image
          class="w-p100 h-820"
          src="https://images.vinehoo.com/vinehoomini/v3/pay_success/banner.png"
          mode="widthFix"
        />
      </view>

      <!--支付成功 -->
      <view
        class="p-rela z-02 d-flex flex-column j-center a-center"
        :style="{ paddingTop: from ? appStatusBarHeight + 30 + 'px' : '-20rpx' }"
      >
        <image
          class="w-264 h-184"
          src="https://images.vinehoo.com/vinehoomini/v3/pay_success/pay_succ.png"
          mode="aspectFill"
        />
        <view class="mt-20 font-36 font-wei text-ffffff">支付成功</view>
        <view v-if="isColl" class="mt-10">
          <view v-if="isCollSucc === 3" class="font-28 text-ffffff">拼团失败，钱款将按照原支付账户退回。</view>
          <view v-else-if="isCollSucc === 2" class="font-28 text-ffffff">恭喜您！已拼团成功啦！</view>
          <view v-else-if="isCollSucc === 1" class="d-flex flex-column j-center a-center">
            <view class="font-28 text-ffffff">
              <text>还差</text>
              <text class="text-ffc900">{{ CollNumber }}人</text>
              <text>，赶快邀请好友来拼团吧</text>
            </view>
            <view class="mt-14 font-24 text-ffbbbb">拼团规则：好友拼团·人满发货·人不满退款</view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="p-rela z-02 ml-72 mr-72" :class="isColl ? 'mt-58' : 'mt-106'">
        <view v-if="isColl && isCollSucc === 1" class="d-flex j-sb a-center">
          <u-button
            shape="circle"
            :hair-line="false"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{
              width: '278rpx',
              height: '64rpx',
              fontSize: '28rpx',
              fontWeight: 'bold',
              color: '#FFF',
              backgroundColor: '#D60D0D',
              border: '2rpx solid #FFF',
            }"
            @click="showLeaveModal(1)"
            >查看拼团详情</u-button
          >
          <u-button
            shape="circle"
            :hair-line="false"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{
              width: '278rpx',
              height: '64rpx',
              fontSize: '28rpx',
              fontWeight: 'bold',
              color: '#D60D0D',
              backgroundColor: '#FFF',
              border: 'none',
            }"
            @click="inviteGroup"
            >邀请好友拼团</u-button
          >
        </view>
        <view v-else class="d-flex j-sb a-center">
          <u-button
            shape="circle"
            :hair-line="false"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{
              width: '278rpx',
              height: '64rpx',
              fontSize: '28rpx',
              fontWeight: 'bold',
              color: '#FFF',
              backgroundColor: '#D60D0D',
              border: '2rpx solid #FFF',
            }"
            @click="showLeaveModal(1)"
            >查看我的订单</u-button
          >
          <view
            class="w-278 h-64 bg-ffffff d-flex j-center a-center b-rad-32 font-28 font-wei text-e80404"
            @click="showLeaveModal(2)"
            >返回首页</view
          >
          <!-- <u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
                        :custom-style="{width:'278rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#D60D0D', backgroundColor: '#FFF', border:'none'}" @click="showLeaveModal(2)">返回首页</u-button> -->
        </view>
      </view>

      <!-- 抽奖板块 -->
      <view class="p-rela z-02 bg-ffffff b-rad-20 mr-24 ml-24 mt-60 p-32">
        <view class="d-flex j-center a-center font-32 font-wei text-8e591f">下单抽抽乐</view>
        <view class="p-abso top-38 right-32 font-24 text-9" @click="showRuleMask = true">规则</view>

        <view class="luck-draw p-rela h-560 d-flex j-center a-center flex-wrap mt-32">
          <!-- 抽奖宫格 -->
          <view
            class="luck-draw-item p-abso w-198 h-172 bg-fde8c7 d-flex flex-column j-center a-center b-rad-10"
            :class="!canLuckDraw ? 'bg-f4f4f4' : currentIndex == index + 1 && luckyDrawing ? 'bg-ffd761' : 'bg-fde8c7'"
            v-for="(item, index) in luckyDrawList"
            :key="index"
          >
            <image
              class="w-60 h-60"
              :class="canLuckDraw ? '' : 'fil-gray100-opa40'"
              :src="item.image"
              mode="aspectFill"
            />
            <text class="mt-12 font-22 font-wei" :class="canLuckDraw ? 'text-c87924' : 'text-9'">{{ item.name }}</text>
            <text v-if="item.sub_name" class="font-22 font-wei" :class="canLuckDraw ? 'text-c87924' : 'text-9'">{{
              item.sub_name
            }}</text>
          </view>

          <!-- 抽奖按钮 -->
          <view class="p-rela w-198 h-172" @click="$u.throttle(luckDraw, 3000)">
            <image
              class="w-198 h-172"
              :src="ossIcon(`/pay_success/click_${luckDrawImgSuffArr[canLuckDraw]}.png`)"
              mode="aspectFill"
            />
            <view
              class="p-abso right-n-52 top-n-44 w-156 h-80 flex-c-c pb-10 font-24 text-e00701"
              :style="{ backgroundImage: `url(${ossIcon(`/pay_success/draw_count.png`)})`, backgroundSize: 'cover' }"
            >
              剩余{{ drawCount }}次
            </view>
          </view>
        </view>

        <!-- <view v-if="true" class="w-p100">
                        <view v-if="false" class="h-92 bg-feefd7 d-flex j-center a-center b-rad-10 mt-36 ptb-00-plr-24 font-28 text-c87924">{{prizeInfo.msg}}</view>
                        <view v-else class="h-92 w-p100 bg-feefd7 d-flex j-sb a-center b-rad-10 mt-36 ptb-00-plr-24">
                            <view class="font-28 text-c87924">恭喜您获得3兔头</view>
                            <u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
                            :custom-style="{width:'146rpx', height:'44rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="toExchange">{{ prizeInfo.type == 1 ? '去兑换' : '去使用' }}</u-button>
                        </view>
                    </view> -->

        <view v-if="Object.keys(prizeInfo).length" class="">
          <view
            v-if="prizeInfo.type == 0"
            class="h-92 bg-feefd7 d-flex j-center a-center b-rad-10 mt-36 ptb-00-plr-24 font-28 text-c87924"
            >{{ prizeInfo.msg }}</view
          >
          <view v-else class="h-92 bg-feefd7 d-flex j-sb a-center b-rad-10 mt-36 ptb-00-plr-24">
            <view class="font-28 text-c87924">{{ prizeInfo.msg }}</view>
            <view class="flex-1 d-flex j-end">
              <view class="">
                <u-button
                  shape="circle"
                  :hair-line="false"
                  :ripple="true"
                  ripple-bg-color="#FFF"
                  :custom-style="{
                    width: '146rpx',
                    height: '44rpx',
                    fontSize: '28rpx',
                    color: '#FFF',
                    backgroundColor: '#E80404',
                    border: 'none',
                  }"
                  @click="toExchange"
                  >{{ prizeInfo.type == 1 ? '去兑换' : '去使用' }}</u-button
                >
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 猜你喜欢 -->
      <view class="">
        <vh-split-line
          :padding-top="52"
          :padding-bottom="30"
          :margin-left="10"
          :margin-right="10"
          text="猜你喜欢"
          :font-bold="true"
          :font-size="36"
          text-color="#333333"
          :show-image="true"
          image-src="https://images.vinehoo.com/vinehoomini/v3/comm/guess_love.png"
        />
        <vh-goods-recommend-list :custom-click="true" @click="openGoodsDetail($event, 5)" />
      </view>

      <!-- 弹框 -->
      <view class="">
        <!-- 抽奖成功弹框 -->
        <u-modal
          v-model="showDrawSuccMod"
          :show-title="false"
          content=""
          :width="490"
          confirm-text="知道了"
          :confirm-style="{ fontSize: '28rpx', color: '#999' }"
          @confirm="iKnow"
        >
          <view class="pt-86 pb-64">
            <view class="d-flex j-center a-center">
              <image
                class="w-264 h-184"
                src="https://images.vinehoo.com/vinehoomini/v3/comm/succ_red.png"
                mode="aspectFill"
              />
            </view>

            <view class="d-flex flex-column j-center a-center mt-30 l-h-44">
              <view class="font-28 text-3">中奖提示</view>
              <view class="pl-24 pr-24 text-center font-28 text-3">{{ prizeContent }}</view>
            </view>
          </view>
        </u-modal>

        <!-- 规则弹框 -->
        <u-mask :show="showRuleMask" :zoom="false">
          <view class="h-p100 d-flex j-center a-center">
            <view class="p-rela w-582 h-988 bg-ffffff d-flex flex-column a-center b-rad-10">
              <view class="d-flex j-center mt-40 font-32 font-wei text-3">规则</view>

              <view class="w-502 h-730 mt-24">
                <scroll-view class="h-p100" scroll-y="true">
                  <view class="font-30 font-wei text-3">抽奖规则：</view>
                  <view class="font-28 mt-20 text-6 l-h-50"
                    >1.用户通过手机酒云网APP(APP版本需在8.30及以上)或酒云网微信小程序，购买符合要求的普通实物商品（不含酒会、课程、兔头商店商品等），下单并支付成功后将会获得一次抽取机会，未抽奖即离开该页面视为放弃，后续不可补抽；</view
                  >
                  <view class="font-28 text-6 l-h-50"
                    >2.任何参与者通过不正当手段（包括但不限于侵犯其他人合法权益、作弊、干扰系统、实施网络攻击、批量注册账户、用机器注册账户等方式）获得本活动奖品，酒云网有权撤销奖品；</view
                  >
                  <view class="font-28 text-6 l-h-50"
                    >3.若用户曾经存在，出现或经酒云网合理怀疑或判定的不当行为，用户将可能面临无法使用优惠权益；</view
                  >
                  <view class="font-28 text-6 l-h-50"
                    >4.如遇不可抗力（包括但不限于活动存在大面积作弊行为、活动遭受严重网络攻击或因系统故障导致活动资格大批量出错，活动不能正常进行时），酒云网可取消、修改或暂停本活动。</view
                  >

                  <view class="mt-24 font-30 font-wei text-3">奖品使用规则：</view>
                  <view class="font-28 mt-20 text-6 l-h-50"
                    >1.优惠券可在“我的”优惠券中查看，具体使用规则和有效时间详见券面信息；</view
                  >
                  <view class="font-28 text-6 l-h-50"
                    >2.兔头在“我的”-兔头商店-兔头记录中查看，可在兔头商店中进行使用。</view
                  >
                </scroll-view>
              </view>

              <view class="p-abso bottom-0 w-p100 h-104 d-flex j-center a-center b-sh-00021200-022">
                <u-button
                  shape="circle"
                  :hair-line="false"
                  :ripple="true"
                  ripple-bg-color="#FFF"
                  :custom-style="{
                    width: '440rpx',
                    height: '64rpx',
                    fontSize: '28rpx',
                    color: '#FFF',
                    backgroundColor: '#E80404',
                    border: 'none',
                  }"
                  @click.stop="showRuleMask = false"
                  >我知道了</u-button
                >
              </view>
            </view>
          </view>
        </u-mask>

        <!-- 离开弹框 -->
        <u-modal
          v-model="showLeaveMod"
          :width="504"
          :show-title="false"
          :show-cancel-button="true"
          cancel-text="残忍离开"
          confirm-text="继续抽奖"
          :cancel-style="{ fontSize: '28rpx', color: '#999' }"
          :confirm-style="{ fontSize: '28rpx', color: '#E80404' }"
          :content-style="{ fontSize: '28rpx', fontWeight: 'bold', color: '#333' }"
          @cancel="cruelLeave"
          @confirm="continueToDraw"
        >
          <view class="ptb-60-plr-00">
            <view class="text-center">离开将会失去本次抽奖机会哦~</view>
            <view class="text-center">确认离开吗？</view>
          </view>
        </u-modal>

        <!-- 首三单弹框 -->
        <TopThreeOrderMask
          v-if="topThreeOrderInfo.is_show"
          :show="topThreeOrderMaskVisible"
          :info="topThreeOrderInfo"
          @jump="topThreeJump"
          @close="topThreeOrderMaskVisible = false"
        />
      </view>
    </view>

    <!-- 骨架屏 -->
    <view v-else class="fade-in" :style="{ paddingTop: from ? appStatusBarHeight + 'px' : '' }">
      <vh-skeleton :type="8" />
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
export default {
  name: 'pay-success',

  data() {
    return {
      // 页面数据
      share_image: '',
      main_title: '',
      CollPeriod: 0,
      group_id: 0,
      from: '', //从哪个端进入 1 = 安卓、2 = ios"
      plate: '', //板块
      loading: true, //加载状态 true = 加载中、false = 结束加载
      appStatusBarHeight: '', //状态栏高度
      navBackgroundColor: 'rgba(224, 20, 31, 0)', //导航栏背景
      scrollTop: 0, //距离顶部距离
      isColl: false, //是否拼团
      isCollSucc: 1, //是否拼团成功
      eventType: 0, //点击事件的类型 0 = 左上角返回、1 = 查看我的订单、2 = 返回首页、3 = 查看拼团详情、4 = 邀请好友拼团

      // 抽奖
      luckyDrawList: [], //抽奖列表
      canLuckDraw: 1, //是否可以抽奖 0 = 不可以抽奖、1 = 可以抽奖
      luckDrawImgSuffArr: ['gra', 'ora'], //抽奖图片后缀 gra = 灰色、ora = 橙色
      currentIndex: 1, //当前位置 (从1开始)
      priceIndex: 0, //后台返回的中奖位置
      count: 8, // 总共有多少个位置
      timer: 0, // 每次转动定时器
      speed: 200, // 初始转动速度
      times: 0, // 转动次数
      cycle: 60, // 转动基本次数：即至少需要转动多少次再进入抽奖环节
      prize: -1, // 中奖位置
      luckyDrawing: false, //抽奖进行中
      prizeContent: '', //中奖内容
      originPrizeInfo: {}, //原始抽奖数据
      prizeInfo: {}, //中奖信息
      goodsInfo: {}, //商品信息
      drawCount: 0, //抽奖次数

      // 弹框
      showDrawSuccMod: false, //是否展示抽奖成功的弹框
      showRuleMask: false, //是否显示规则弹框
      showLeaveMod: false, //是否展示离开抽奖弹框

      topThreeOrderMaskVisible: true,
      topThreeOrderInfo: {},
    }
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable']),
  },

  onLoad(options) {
    console.log('options.from', options.from);
    
    this.from = options.from || ''
    this.plate = options.plate || ''
    this.appStatusBarHeight = parseInt(options.statusBarHeight)
    this.searchIsColl()
    this.initOnLoad()
  },

  methods: {
    async searchIsColl() {
      const res = await this.$u.api.lastOrderDetail({})
      if (res.data.special_type === 1) {
        this.isColl = true
        this.CollNumber = res.data.group_last_num
        this.group_id = res.data.group_id
        this.CollPeriod = res.data.period
        this.main_title = res.data.main_title
        this.share_image = res.data.share_image
        if (!Number(this.CollNumber)) {
          this.isCollSucc = 2
        } else {
          this.isCollSucc = res.data.group_status
        }
      }
    },
    // 初始化
    async initOnLoad() {
      try {
        const isLogin = await this.login.isLoginV3(this.$vhFrom)
        if (isLogin) {
          await Promise.all([this.getLuckyDrawList(), this.getLuckyDrawCount(), this.getTopThreeOrderNums()])
          this.loading = false
        }
      } catch (e) {
        //TODO handle the exception
        setTimeout(() => {
          if (this.comes.isFromApp(this.from)) {
            if(this.from == 'next'){
                this.jump.appAndMiniJump(0,this.routeTable.pEMyOrder, this.$vhFrom, 1)
              } else {
                wineYunJsBridge.openAppPage({
                client_path: {
                  ios_path: 'MyOrderViewController',
                  android_path: 'com.stg.rouge.activity.MyOrderActivity',
                },
              })
              }
          } else {
            this.jump.redirectTo(this.routeTable.pEMyOrder)
          }
        }, 1500)
      }
    },

    // 获取抽奖列表
    async getLuckyDrawList() {
      let res = await this.$u.api.payLuckyDrawList()
      res.data.list.map((v, i) => {
        if (v.name.includes('指定酒款')) {
          v.sub_name = v.name.split('指定酒款')[1]
          v.name = '指定酒款'
        }
        if (v.name.includes('满300减15')) {
          v.sub_name = v.name.split('满300减15')[1]
          v.name = '满300减15'
        }
      })
      this.luckyDrawList = res.data.list
    },

    // 获取抽奖次数
    async getLuckyDrawCount() {
      try {
        const { data } = await this.$u.api.payLuckyDrawCount()
        this.drawCount = data
        if (this.drawCount > 0) {
          this.canLuckDraw = 1
        } else {
          this.canLuckDraw = 0
        }
        this.appGoBack()
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 获取三单次数
    async getTopThreeOrderNums() {
      if (this.plate === 'orderDetail') return
      const res = await this.$u.api.topThreeOrderNums()
      this.topThreeOrderInfo = res?.data || {}
    },

    // app返回（系统原生返回按钮、侧滑返回）
    appGoBack() {
      if (this.comes.isFromApp(this.from)) {
        if (this.times != 0) return this.feedback.toast({ title: '正在抽奖中，请稍后' }) //抽奖中不能重复抽奖
        window.interceptBack = () => {
          this.eventType = 1
          this.canLuckDraw ? (this.showLeaveMod = true) : this.cruelLeave()
        }
      }
    },

    // 显示弹框 type = 事件类型
    showLeaveModal(type) {
      if (this.times != 0) return this.feedback.toast({ title: '正在抽奖中，请稍后' }) //抽奖中不能重复抽奖
      this.eventType = type
      this.canLuckDraw ? (this.showLeaveMod = true) : this.cruelLeave()
    },

    // 残忍离开
    cruelLeave() {
      switch (this.eventType) {
        case 0:
        case 1:
          if (this.comes.isFromApp(this.from)) {
            if (this.plate === 'orderDetail') {
              //订单详情（App）
              this.$customBack()
            } else {
              if(this.from == 'next'){
                this.jump.appAndMiniJump(0,this.routeTable.pEMyOrder, this.$vhFrom, 1)
              } else {
                wineYunJsBridge.openAppPage({
                client_path: {
                  ios_path: 'MyOrderViewController',
                  android_path: 'com.stg.rouge.activity.MyOrderActivity',
                },
              })
              }
            }
          } else {
            if (this.plate === 'orderDetail') {
              //订单详情（H5）
              this.jump.navigateBack()
            } else {
              this.jump.redirectTo(this.routeTable.pEMyOrder)
            }
          }
          break
        case 2:
          if (this.comes.isFromApp(this.from))
            wineYunJsBridge.openAppPage({
              client_path: { ios_path: 'goMain', android_path: 'goMain' },
            })
          else this.jump.reLaunch(`/pages/index/index`)
          break
        case 3:
          console.log('查看拼团详情')
          break
        case 4:
          let path = `${this.$routeTable.pgGoodsDetail}?id=${this.CollPeriod}&groupId=${this.group_id}`
          this.jump.appShare({
            title: this.main_title,
            // des: `【帮我拼】还差${this.CollNumber}人`,
            img: this.share_image,
            path,
          })
          break
        case 5:
          console.log('跳转商品详情')
          this.jump.appAndMiniJump(1, `/pages/goods-detail/goods-detail?id=${this.goodsInfo.id}`, this.from, 1)
          break
      }
    },
    inviteGroup() {
      if (this.from) {
        let path = `${this.$routeTable.pgGoodsDetail}?id=${this.CollPeriod}&groupId=${this.group_id}`
        this.jump.appShare({
          title: this.main_title,
          // des: `【帮我拼】还差${this.CollNumber}人`,
          img: this.share_image,
          path,
        })
      } else {
        this.feedback.toast({ title: '请前往APP或小程序查看此功能~' })
      }
    },

    // 继续抽奖
    continueToDraw() {
      console.log('----------我是继续抽奖')
    },

    // 抽奖
    luckDraw() {
      if (this.canLuckDraw == 0) return this.feedback.toast({ title: '亲，您的抽奖机会已经用完' }) //已经抽过
      if (this.times != 0) return this.feedback.toast({ title: '正在抽奖中，请勿重复点击' }) //抽奖中不能重复抽奖
      this.luckyDrawing = true
      this.startDraw()
    },

    // 开始抽奖
    async startDraw() {
      try {
        let data = {}
        let res = await this.$u.api.payLuckyDraw() //请求中奖接口
        this.luckyDrawList.forEach((item, index) => {
          if (item.id === res.data.id) this.priceIndex = index + 1
        })
        this.originPrizeInfo = res.data //抽奖信息
        this.prizeContent = res.data.msg //中奖弹框
        this.startRoll()
      } catch (e) {
        this.getLuckyDrawCount()
      }
    },

    // 开始转动
    async startRoll() {
      this.times += 1 // 转动次数
      this.oneRoll() // 转动过程调用的每一次转动方法，这里是第一次调用初始化
      // 如果当前转动次数达到要求 && 目前转到的位置是中奖位置
      if (this.times > this.cycle + 10 && this.prize === this.currentIndex) {
        clearTimeout(this.timer) // 清除转动定时器，停止转动
        this.prize = -1
        this.priceIndex = 0
        this.times = 0
        this.speed = 200
        setTimeout((res) => {
          // this.canLuckDraw = 0
          this.showDrawSuccMod = true
        }, 500)
      } else {
        if (this.times < this.cycle) {
          this.speed -= 10 // 加快转动速度
        } else if (this.times === this.cycle) {
          //中奖位置
          this.prize = this.priceIndex //中奖位置,可由后台返回
          if (this.prize > 8) {
            this.prize = 8
          }
        } else if (
          this.times > this.cycle + 10 &&
          ((this.prize === 0 && this.currentIndex === 8) || this.prize === this.currentIndex + 1)
        ) {
          this.speed += 110
        } else {
          this.speed += 20
        }
        if (this.speed < 40) {
          this.speed = 40
        }
        this.timer = setTimeout(this.startRoll, this.speed)
      }
    },

    // 每一次转动
    oneRoll() {
      let currentIndex = this.currentIndex // 当前转动到哪个位置
      const count = this.count // 总共有多少个位置
      currentIndex += 1
      if (currentIndex > count) {
        currentIndex = 1
      }
      this.currentIndex = currentIndex
    },

    // 我知道了
    iKnow() {
      ;(this.prizeInfo = this.originPrizeInfo), //中奖信息
        this.getLuckyDrawCount()
    },

    // 去兑换
    toExchange() {
      console.log('------------------------我是去兑换')
      const { period = 0 } = this.prizeInfo
      switch (
        this.prizeInfo.type // 0：未中奖，1：兔头，2：优惠券
      ) {
        case 1:
          console.log('------------------我是去兔头商店')
          this.jump.appAndMiniJump(0, this.routeTable.pBRabbitHeadShop, this.from, 1)
          break
        case 2:
          if (period) {
            console.log('------------------period', period)
            this.jump.appAndMiniJump(0, `${this.routeTable.pgGoodsDetail}?id=${period}`, this.$vhFrom)
          } else {
            if (this.comes.isFromApp(this.from))
              wineYunJsBridge.openAppPage({
                client_path: { ios_path: 'goMain', android_path: 'goMain' },
              })
            else this.jump.reLaunch(this.routeTable.pgIndex)
          }
          break
      }
    },

    // 打开商品详情
    openGoodsDetail(item, type) {
      if (this.times != 0) return this.feedback.toast({ title: '正在抽奖中，请稍后' }) //抽奖中不能重复抽奖
      this.eventType = type
      this.goodsInfo = item
      this.canLuckDraw ? (this.showLeaveMod = true) : this.cruelLeave()
    },

    // 首三单跳转
    topThreeJump() {
      this.feedback.loading()
      this.$u.api.userSpecifiedData({ field: 'home_select' }).then((res) => {
        const {
          data: { home_select = '1' },
        } = res
        if (home_select === '0') {
          //秒发
          if (this.$app)
            wineYunJsBridge.openAppPage({
              client_path: { ios_path: 'goMain', android_path: 'goMain' },
              ad_path_param: [
                {
                  ios_key: 'type',
                  ios_val: '3',
                  android_key: 'type',
                  android_val: '3',
                },
              ],
            })
          else this.jump.reLaunch(this.routeTable.pgMiaoFa)
        } else {
          //首页
          if (this.$app) wineYunJsBridge.openAppPage({ client_path: { ios_path: 'goMain', android_path: 'goMain' } })
          else this.jump.reLaunch(this.$routeTable.pgIndex)
        }
      })
    },
  },

  onPageScroll(res) {
    this.scrollTop = res.scrollTop
    res.scrollTop <= 100
      ? (this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop / 100})`)
      : (this.navBackgroundColor = `rgba(224, 20, 31, 1)`)
  },
}
</script>

<style scoped>
/* 九宫格抽奖滚动板块定位(八块) */
.luck-draw .luck-draw-item:nth-child(1) {
  left: 0;
  top: 0;
}
.luck-draw .luck-draw-item:nth-child(2) {
  left: 220rpx;
  top: 0;
}
.luck-draw .luck-draw-item:nth-child(3) {
  right: 0;
  top: 0;
}
.luck-draw .luck-draw-item:nth-child(4) {
  right: 0;
  top: 194rpx;
}
.luck-draw .luck-draw-item:nth-child(5) {
  right: 0;
  bottom: 0;
}
.luck-draw .luck-draw-item:nth-child(6) {
  right: 220rpx;
  bottom: 0;
}
.luck-draw .luck-draw-item:nth-child(7) {
  left: 0;
  bottom: 0;
}
.luck-draw .luck-draw-item:nth-child(8) {
  left: 0;
  top: 194rpx;
}
</style>
