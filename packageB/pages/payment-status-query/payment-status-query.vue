<template>
	<view class="content">
		<!-- 收银台支付信息 -->
		<view v-if="payStatus != 0" class="">
			<!-- 支付商品信息 -->
			<view class="bg-ffffff p-24">
				<view class="" @click="copy.copyText(mainOrderNo)">
					<text class="font-28 text-3">主订单编号：</text>
					<text class="font-28 text-6">{{mainOrderNo}}</text>
					<text class="bg-f5f5f5 ml-12 ptb-02-plr-12 font-22 text-6">复制</text>
				</view>
			</view>
			
			<!-- 支付状态 -->
			<view class="bg-ffffff d-flex flex-column j-center a-center mt-24 p-24">
				<view v-if="payStatus == 1" class="">
					<view class="mb-30 font-28 text-center text-e80404">订单支付取消或订单异常</view>
					<!-- <view class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'650rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="rePay(payInfo)">重新支付</u-button>
					</view> -->
				</view>
				
				<view v-if="payStatus == 2" class="d-flex flex-column j-center">
					<view class="mb-30 font-28 text-center text-e80404">订单支付成功~</view>
					<image class="w-264 h-184" src="https://images.vinehoo.com/vinehoomini/v3/comm/succ_red.png" mode="aspectFill"></image>
				</view>
				
				<view class="mt-p30">
					<u-button :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'650rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#1AAD19', border:'none'}" @click="returnIndex">返回商家</u-button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name:'payment-status-query',
		
		data(){
			return{
				mainOrderNo:'', //订单号
				paymentMethod: '',//支付方式
				payPlate:'', //支付板块
				payStatus: '', //支付状态 1 = 订单支付取消或订单异常、2 = 支付成功
			}
		},
		
		
		onLoad( options ) {
			if( options.mainOrderNo && options.paymentMethod && options.payPlate ) {
				let { mainOrderNo, paymentMethod, payPlate } = options
				this.mainOrderNo = mainOrderNo
				this.paymentMethod = paymentMethod
				this.payPlate = payPlate
				this.getQueryUmsPayOrderStatus( mainOrderNo, paymentMethod, payPlate )
			}else{
				this.feedback.toast({title:'参数有误~',icon:'error'})
			}
		},
		
		methods:{
			// 查询订单状态 mainOrderNo = 主订单号， paymentMethod = 支付方式、payPlate = 支付板块
			async getQueryUmsPayOrderStatus( mainOrderNo, paymentMethod, payPlate ) {
				this.feedback.loading({ title: '查询支付状态中...' })
				try{
					let res = await this.$u.api.queryUmsPayOrderStatus({ main_order_no: paymentMethod+mainOrderNo, payment_method: paymentMethod })
					let { status } = res.data
					this.feedback.hideLoading()
					this.feedback.toast({ title: '查询成功~' })
					if( status == 'TRADE_SUCCESS' ) {
						this.payStatus = 2
						// if( payPlate == 1 ) { //酒会
						// 	this.jump.redirectTo(`/packageD/pages/wine-party-order-list/wine-party-order-list`)
						// }else { //普通商品
						// 	this.jump.redirectTo(`/packageB/pages/pay-success/pay-success`)
						// }
					}else{
						this.payStatus = 1
						// if( payPlate == 1 ) { //酒会
						// 	this.jump.redirectTo(`/packageD/pages/wine-party-order-list/wine-party-order-list`)
						// }else { //普通商品
						// 	this.jump.redirectTo(`/packageE/pages/my-order/my-order`)
						// }
					}
				}catch(e){
					//TODO handle the exception
				}
			},
			returnIndex() {
				if(this.comes.isFromApp(this.$vhFrom))
					wineYunJsBridge.openAppPage({ 
						client_path: { "ios_path":"goMain", "android_path":"goMain" } ,
					});
				else
					this.jump.reLaunch(`/pages/index/index`)
			}
		}
		
	}
</script>

<style scoped></style>