<template>
	<view class="">
		<vh-navbar :title="`已缴纳订金`" :show-border="true" />
		<view v-if="!loading" class="fade-in">
			<view v-if="list.length > 0" class="pb-20">
				<view class="bg-ffffff b-rad-16 mt-20 mr-24 mb-20 ml-24 pl-24 pr-24" v-for="(item, index) in list" :key="item.order_no" 
				@click="jump.appAndMiniJump(0, `${$routeTable.pBOrderDepositDetail}?orderNo=${item.order_no}`, $vhFrom)">
					<OrderListStatusTextDeposit :item="item" />
					<OrderListGoodsItemDeposit :item="item" />
					<view v-if="item.status !== 3" class="ptb-28-plr-00">
						<view v-if="[1].includes(item.status)" class="flex-sb-c">
							<view class="p-rela d-flex a-center">
								<view class="p-abso flex-s-c ptb-20-plr-00 w-120 font-24 text-6" @click.stop="onApplyRefund(item)">申请退款</view>
							</view>
							<view v-if="item.countdown" class="flex-e-c">
								<vh-count-down
									:show-days="true" 
									:showHours="true" 
									dayColor="#E80404" 
									:hasDayMarginRight="false" 
									:timestamp="item.countdown" 
									separator="zh" 
									bg-color="transparent" 
									color="#E80404"
									separator-color="#E80404"
									:allFontBold="true"
								    @end="item.countdown = 0" 
								/>
								<text class="font-24 font-wei text-e80404">后<text class="ml-10 text-3">付尾款</text></text>
							</view>
							<view v-if="item.countdown === 0" class="flex-e-c">
								<view class="">
									<u-button
										shape="circle" 
										:hair-line="false" 
										:ripple="true" 
										ripple-bg-color="#FFF"
										:custom-style="{width:'148rpx', height:'52rpx', fontSize:'24rpx', color:'#FFF', backgroundColor: '#e80404', border:'none'}" 
										@click="jump.appAndMiniJump(1, `${$routeTable.pgGoodsDetail}?id=${item.goodsInfo[0].period}`, $vhFrom, 1)">支付尾款
									</u-button>
								</view>
							</view>
						</view>
						<view v-if="[4, 8].includes(item.status)" class="flex-e-c font-24 text-6">已失效</view>
					</view>
				</view>
				<u-loadmore :status="reachBottomLoadStatus" />
			</view>
			<view v-if="list.length == 0">
				<vh-empty 
					:padding-top="52" 
					:padding-bottom="100" 
					:image-src="ossIcon(`/empty/emp_order.png`)" 
					text="暂无已缴纳订金" 
				/>
				
				<vh-split-line 
					:padding-top="52" 
					:padding-bottom="32" 
					:margin-left="10" 
					:margin-right="10" 
					text="猜你喜欢" 
					:font-bold="true" 
					:font-size="36" 
					text-color="#333333"
					:show-image="true" 
					:image-src="ossIcon(`/comm/guess_love.png`)" 
				/>
				
				<vh-goods-recommend-list :jumpType="1" />
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	import listMixin from '@/common/js/mixins/listMixin'
	export default {
		mixins: [listMixin],
		data: () => ({}),
		methods: {
			...mapMutations(['muPayInfo']),
			async load(query) {
				 const res = await this.$u.api.depositOrderList(query)
				 const { list = [] } = res?.data || {}
				 this.list = query.page === 1 ? list : this.list.concat(list)
				 return res
			},
			onApplyRefund (item) {
				this.feedback.showModal({
					title: '',
					content: '确认是否申请退还订金，退还后您将无法享受尾款折扣。',
					confirm: () => {
						this.feedback.loading({ title: '' })
						const { order_no, goodsInfo } = item
						const params = {
							sub_order_no: order_no,
							order_type: goodsInfo[0].periods_type,
							type: 1,
							goods_status: 1,
							refund_reason: '订金退款'
						}
						this.$u.api.applyAfterSale(params).then(() => {
							this.reload().finally(() => {
								this.feedback.toast()
							})
						})
					}
				})
			}
		},
		onShow () {
		  this.login.isLoginV3(this.$vhFrom).then(isLogin => {
		    if (!isLogin) return
		    this.load().finally(() => {
				this.loading = false
			})
		  })
		},
	    onPullDownRefresh() {
	    	this.pullDownRefresh()
	    },
	    onReachBottom () {
	    	this.reachBottomLoad()
	    }
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>