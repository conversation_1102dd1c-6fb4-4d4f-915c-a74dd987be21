<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar back-icon-color="#FFF" :background="{background: '#E80404'}" title="订单确认" title-color="#FFF" />
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in pb-124">
			<!-- 地址信息 -->
			<view v-if="disTypeId == 1 " class="mt-20">
				<view v-if="addressInfo.id" class="bg-ffffff b-rad-10 d-flex j-sb a-center ml-24 mr-24 ptb-32-plr-24" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=4`)">
					<view class="w-500">
						<view class="d-flex a-center">
							<text class="font-32 font-wei text-3">{{addressInfo.consignee}}</text>
							<text class="ml-14 font-28 text-9">{{addressInfo.consignee_phone}}</text>
						</view>
						<view class="mt-12">
							<text v-if="addressInfo.is_default" class="bg-ff0013 b-rad-04 ptb-02-plr-08 text-ffffff font-20 font-wei l-h-28">默认</text>
							<text v-if="addressInfo.label" class="bg-2e7bff b-rad-04 ptb-02-plr-16 text-ffffff font-20 font-wei l-h-28" :class="addressInfo.is_default ? 'ml-10' : ''">{{addressInfo.label}}</text>
							<text class="font-24 text-3 l-h-34" :class="addressInfo.is_default || addressInfo.label ? 'ml-10' : ''">{{addressInfo.province_name}} {{addressInfo.city_name}} {{addressInfo.town_name}} {{addressInfo.address}}</text>
						</view>
					</view>
						
					<u-icon name="arrow-right" :size="24" color="#333"></u-icon>
				</view>
				
				<!-- 收货地址（无数据） -->
				<view v-else class="p-rela h-188 b-rad-10 d-flex j-center a-center ml-24 mr-24 o-hid" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=4`)">
					<image class="p-abso z-01 w-702 h-188" src="http://images.vinehoo.com/vinehoomini/v3/order-confirm/add_bg.png" mode="aspectFill"></image>
					<view class="p-rela z-02 d-flex flex-column j-center a-center">
						<image class="w-84 h-84" src="http://images.vinehoo.com/vinehoomini/v3/order-confirm/add_ico.png" mode="aspectFill"></image>
						<text class="mt-06 font-30 text-3">新建收货地址</text>
					</view>
				</view>
			</view>
			
			<!-- 门店信息 -->
			<view class="p-rela bg-ffffff b-rad-10 o-hid mtb-20-mlr-24 ptb-00-plr-20">
				<image v-if="disTypeId" class="p-abso top-0 right-0 w-148 h-48" :src="`${osip}/store_order_confirm/${storeIconList[disTypeId]}.png`" mode="aspectFill"></image>
				<view class="bb-s-01-eeeeee pt-30 pb-24">
					<view class="d-flex">
						<image class="w-28 h-28" :src="`${osip}/store_order_confirm/add.png`" mode="widthFix" />
						<view class="ml-20">
							<view class="w-500 font-28 font-wei text-3">{{storeInfo.store_name}}</view>
							<view class="mt-10 font-24 text-6">{{storeInfo.address}}</view>
						</view>
					</view>
				</view>
				
				<view class="d-flex a-center bb-s-01-eeeeee ptb-24-plr-00">
					<image class="w-28 h-28" :src="`${osip}/store_order_confirm/user.png`" mode="widthFix" />
					<input class="flex-1 ml-20 font-28 text-3" type="number" v-model="phone" :maxlength="11" />
				</view>
			</view>
			
			<!-- 门店订单商品列表 -->
			<view class="bg-ffffff b-rad-10 mr-24 mb-20 ml-24 ptb-00-plr-20">
				<view class="d-flex bb-s-01-eeeeee ptb-20-plr-00" v-for="(item, index) in storeOrderInfo.storeOrderGoodsList" :key="index">
					<vh-image :loading-type="2" :src="item.goods_image" :width="160" :height="160" :border-radius="6" mode="aspectFit"/>
					
					<view class="flex-1 d-flex flex-column j-sb ml-20">
						<view class="font-28 text-0 text-hidden-2 l-h-40">{{item.goods_name}}</view>
						
						<view class="d-flex j-sb">
							<text class="bg-eeeeee ptb-06-plr-22 b-rad-08 font-20 text-6">{{item.c_name}}</text>
							<text class="font-24 text-6">x{{item.nums}}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 配送方式 -->
			<view class="bg-ffffff d-flex j-sb a-center b-rad-10 mr-24 mb-20 ml-24 ptb-30-plr-20" @click="showDisPop = true">
				<view class="font-28 font-wei text-3">配送方式</view>
				
				<view class="d-flex a-center">
					<text class="mr-10 font-24 text-6">{{disTypeName}}</text>
					<u-icon name="arrow-right" :size="16" color="#666" />
				</view>
			</view>
			
			<!-- 金额明细 -->
			<view class="bg-ffffff b-rad-10 mr-24 ml-24">
				<view class="d-flex j-sb a-center bb-s-01-eeeeee ptb-30-plr-20">
					<text class="font-28 font-wei text-3">商品金额</text>
					<text class="font-28 font-wei text-e80404">¥{{priceInfo.totalMoney}}</text>
				</view>
				
				<view class="d-flex j-sb a-center bb-s-01-eeeeee ptb-30-plr-20" v-if="disTypeId == 3">
					<text class="font-28 font-wei text-3">堂饮费</text>
					<text class="font-28 font-wei text-e80404">¥{{priceInfo.service_charge}}</text>
				</view>
				
				<view class="d-flex j-end a-center ptb-30-plr-20">
					<text class="font-28 font-wei text-3">合计：</text>
					<text class="font-28 font-wei text-e80404">¥{{priceInfo.orderMoney}}</text>
				</view>
			</view>
			
			<!-- 底部操作按钮 -->
			<view class="p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff d-flex j-sb a-center b-sh-00021200-022 ptb-00-plr-24">
				<!-- 合计 -->
				<view class="d-flex a-center">
					<text class="font-24 text-3">合计：</text>
					<text class="font-40 font-wei text-e80404"><text class="font-28">¥</text>{{priceInfo.orderMoney}}</text>
				</view>
				
				<!-- 按钮 -->
				<view class="d-flex a-center">
					<view class="">
						<u-button :disabled="!canCreateOrder" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: !canCreateOrder ? '#ACCCF7' : '#1678ff', border:'none'}" 
						@click="createOrder(2)">吧台支付</u-button>
					</view>
					<view class="ml-12">
						<u-button :disabled="!canCreateOrder" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: !canCreateOrder ? '#C5F3C5' : '#1AAD19', border:'none'}" 
						@click="createOrder(1)">微信支付</u-button>
					</view>
				</view>
			</view>
		    
			<!-- 弹框 -->
			<view class="">
				<!-- 配送方式方式弹框 -->
				<u-popup v-model="showDisPop" mode="bottom" :border-radius="20">
					<view class="pt-40 pl-48 pr-48">
						<view class="d-flex j-center a-center font-36 font-wei text-3">配送方式</view>
						<view class="mt-20">
							<view class="d-flex j-sb a-center bb-s-01-eeeeee ptb-40-plr-00" v-for="(item, index) in disTypeList" :key="item.id" @click="selectDisType(item)">
								<text class="font-28 font-wei text-3">{{item.name}}</text>
								<vh-check :width="36" :height="36" :checked="false"  @click=""/>
							</view>
						</view>
					</view>
				</u-popup>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		name:'store-order-confirm',
		
		data() {
			return {
				osip: 'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				loading: true, //加载状态 true = 加载中、false = 结束加载
				addressInfo: {}, //地址信息
				storeIconList: ['','dis', 'pick', 'drink'], //门店icon列表
				phone:'', //手机号
				isCup: false,//判断是否为杯卖商品
				disTypeList: [], //配送方式列表
				disTypeId: 0,//配送类型 1=物流配送 2=门店自提 3=门店场饮
				disTypeName: '请选择',///配送方式名
				priceInfo: {}, //金额信息
				
				// 弹框
				showDisPop: false, //物流信息弹框
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable', 'storeTableInfo', 'storeInfo', 'storeOrderInfo', 'addressInfoState']),
			
			// 是否可以创建订单
			canCreateOrder() {
				// if(this.phone === ''){
				// 	return uni.showToast({title:'手机号码不能为空',icon:'none',mask:true})
				// }else if(this.phone.length != 11 || !/^\d+$/.test(this.phone)){
				// 	return uni.showToast({title:'手机号不合法',icon:'none',mask:true})
				// }else if( !this.addressInfo.id && this.disTypeId === 1){
				// 	return uni.showToast({title:'请选择您的收货地址',icon:'none',mask:true})
				// }
				
				if(this.phone === ''){
					return false
				}else if(this.phone.length != 11 || !/^\d+$/.test(this.phone)){
					return false
				}else if( !this.addressInfo.id && this.disTypeId === 1){
					return false
				}
				
				return true
			}
		},
		
		onLoad() {
			this.init()
		},
		
		onShow() {
			this.getAddressList()
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muPayInfo']),
			
			// 初始化
			init() {
				uni.getStorage({
					key:'loginInfo',
					success: res => {
						console.log(res)
						this.phone = res.data.telephone
						this.disTypeList = this.storeInfo.purchase_mode_list
						this.disTypeId = this.storeOrderInfo.distribution_id
						switch(this.disTypeId) {
							case 2:
							this.disTypeName = '门店自提'
							break
							case 3:
							this.disTypeName = '门店场饮'
							break
						}
						this.judgeIsCup()
						this.getStoreOrderPrice()
					}
				})
			},
			
			// 判断是否含有杯卖商品
			judgeIsCup() {
				for( let item of this.storeOrderInfo.storeOrderGoodsList ){
					if( item.is_cup ) {
						this.isCup = true
						break
					}
				}
				
				if(this.isCup){
					this.disTypeList = this.storeInfo.purchase_mode_list.filter(v => {return v.id == '3'} )
				}
			},
			
			
			// 获取收货地址列表
			async getAddressList() {
				if( Object.keys(this.addressInfoState).length ) { //如果用户从新选择了地址信息，就读选择的地址信息（从vuex取）
					this.addressInfo = this.addressInfoState
				}else{ //否则就从地址收货地址列表里面取数据，规则：有默认地址就取默认地址、没有默认地址就取数组第一项，否则就代表没有收货地址
					let res = await this.$u.api.addressList()
					if(res.data.list.length > 0) {
						for(let i = 0; i < res.data.list.length; i++) {
							if(res.data.list[i].is_default) {
								this.addressInfo = res.data.list[i]
								break
							}else{
								this.addressInfo = res.data.list[0]
							}
						}
					}else{
						this.addressInfo = {}
					}
				}
				// let res = await this.$u.api.addressList()
				// if(res.data.list.length > 0) {
				// 	for(let i = 0; i < res.data.list.length; i++) {
				// 		res.data.list[i].is_default ? this.addressInfo = res.data.list[i] : this.addressInfo = res.data.list[0]
				// 	}
				// }else{
				// 	this.addressInfo = {}
				// }
				this.getStoreOrderPrice()
			},
			
			// 获取订单金额
			async getStoreOrderPrice() {
				console.log('--------获取门店订单金额')
				try{
					let { storeOrderGoodsList } = this.storeOrderInfo //门店订单信息
					let data = {}
					data.sid = this.storeInfo.id //门店id
					data.send_type = this.disTypeId //配送方式 1快递2自提3现饮
					data.goodsInfo = storeOrderGoodsList //商品信息
					let res = await this.$u.api.storeOrderPrice(data)
					this.priceInfo = res.data
					this.loading = false
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 选择物流方式
			selectDisType( item ) {
				this.disTypeId = parseInt(item.id)
				this.disTypeName = item.name
				this.showDisPop = false
			},
			
			// 创建订单 支付类型（1微信支付，2线下支付（ 吧台支付，支付宝支付 ））
			async createOrder( type ) {
				let { order_source, storeOrderGoodsList } = this.storeOrderInfo //门店订单信息
				let data = {}
				data.pay_type = type //支付类型（1微信，2线下支付）
				data.sid = this.storeInfo.id //门店ID
				if( this.storeTableInfo.tid ) data.tid = this.storeTableInfo.tid //桌面ID
				if(this.disTypeId === 1) { //物流配送时需要上传的参数
				    let { province_name, city_name, town_name, address, consignee } = this.addressInfo //地址信息
					data.ship_addr = `${province_name} ${city_name} ${town_name} ${address}`
					data.consignee = consignee
				}
				data.cellphone = this.phone //联系电话
				data.is_invoice = ''//是否开票 1:开票 2：不开票
				data.send_type = this.disTypeId //配送方式 1快递 2自提 3现饮
				data.order_source = order_source //订单来源 1：购物车 2：立即购买
				data.goodsInfo = storeOrderGoodsList //商品信息（二维数组）
				console.log('--------------------------------门店创建订单需要上传的参数')
				console.log(data)
				
				try{
					let res = await this.$u.api.storeCreateOrder(data)
					console.log(res)
					if( type === 1 ) {
						let { orderno, countdown } = res.data //创建订单返回信息
						let payInfo = {
							payPlate: 2, //支付板块 0 = 普通商品，1 = 酒会，2 = 门店
							payment_amount: this.priceInfo.orderMoney, //支付金额
							// payment_method:4, //支付方式: 0:支付宝APP 1:支付宝H5 2:微信h5 3:微信APP 4:微信小程序 5:PC扫码
							order_type:20, //订单类型(来源)：1:v3支付 2:v3酒云 20:门店 30:老外卖酒 40:nft
							is_cross: 0, //是否跨境 0:非跨境 1:跨境 不传默认非跨境
							main_order_no: orderno, //主订单号
							countdown, //倒计时
						}
						this.muPayInfo(payInfo)
						this.jump.redirectTo('/packageB/pages/payment/payment')
					}else {
						this.jump.redirectTo(`/packageB/pages/store-order/store-order`)
					} 
				}catch(e){
					console.log(e)
					console.log('------------------我进入了异常分支')
					this.jump.navigateBack()
				}
			}
		}
	}
</script>

<style>
	@import "../../../common/css/page.css";
</style>