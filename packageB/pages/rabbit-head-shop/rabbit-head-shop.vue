<template>
	<view class="content">
		<!-- 导航栏 -->
		<view class="">
			<!-- 微信小程序、h5导航栏 -->
			<vh-navbar v-if="from == ''" back-icon-color="#FFF" title="兔头商店" title-color="#FFF" :background="{ background: navBackgroundColor}" />
	
			<!-- app（安卓、ios） -->
			<view v-else>
				<view class="p-fixed z-980 top-0 w-p100" :style="{ background: navBackgroundColor }">
					<view :style="{ height: $appStatusBarHeight + 'px'}" />
					<view class="p-rela h-px-48 d-flex j-center a-center">
						<view class="p-abso left-24 h-p100 d-flex a-center" @click="jumpBack()">
							<u-icon name="nav-back" color="#FFF" :size="44" />
						</view>
						<view class="font-36 font-wei text-ffffff">兔头商店</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in">
			<!-- banner -->
			<view class="p-abso top-0 z-02 w-p100 h-400">
				<image class="w-p100 h-460" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_res_ban.png" mode="widthFix" />
			</view>
			
			<!-- 白背景 -->
			<view class="p-abso z-01 w-p100 h-434 bg-ffffff" :class="capsuleList.length ? 'top-430' : 'top-410'"></view>
			
			<!-- 兔头数量 -->
			<view class="p-rela z-03 d-flex flex-column j-center a-center">
				<view class="font-84 font-wei text-ffffff" :style="{ paddingTop: $vhFrom == '' ? 0 : parseInt($appStatusBarHeight) + 48 + 'px' }">{{rabbitNumber}}</view>
				<view class="d-flex j-center a-center mt-20">
					<image class="w-18 h-18" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png" mode="widthFix" />
					<text class="ml-06 mr-06 font-24 text-ffffff">我的兔头</text>
					<image class="w-18 h-18" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png" mode="widthFix" />
				</view>
			</view>
			
			<!-- 兔头记录、赚取兔头 -->
			<view class="p-rela z-03 bg-ffffff b-rad-10 d-flex j-sb a-center b-sh-00042604-007 mt-44 mr-32 mb-36 ml-32 ptb-24-plr-56">
				<view class="d-flex a-center" @click="rabbitRecord">
					<image class="w-90 h-90" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_rec.png" mode="widthFix" />
					<text class="ml-12 font-32 font-wei text-e04040">兔头记录</text>
				</view>
				<view class="w-01 h-60 bg-e1e1e1 t-sc-x-h-1" />
				<view class="d-flex a-center" @click="earnRabbit">
					<image class="w-90 h-90" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_get.png" mode="widthFix" />
					<text class="ml-12 font-32 font-wei text-d79c36">赚取兔头</text>
				</view>
			</view>
			
			<!-- 胶囊-->
			<view v-if="capsuleList.length" class="p-rela z-03 mt-40 mr-32 mb-36 ml-32">
				<vh-swiper :list="capsuleList" mode="rect" :loadingType="10" :height="178" bg-color="transparent" @click="jump.pubConfJump($event, $vhFrom)"/>
			</view>
			
			<!-- tabs切换栏 -->
			<view class="rabbit-head-shop__tabs p-stic z-980 bb-s-01-f7f7f7" :style="{top: $vhFrom == '' ? navigationBarHeight + 'px' : parseInt($appStatusBarHeight) + 48 + 'px' }">
				<u-tabs ref="tabs" :list="tabsList" :current="current" bar-width="36" bar-height="8" font-size="28" inactive-color="#999" active-color="#333"
				:bar-style="{background: '#e80404'}" @change="changeTabs" />
			</view>
			
			<!-- 兔头商品 -->
			<view v-if="current == 0" class="fade-in p-rela z-03">
				<!-- 兔头商品列表（有数据 ）-->
				<view v-if="goodsList.length" class="bg-f7f7f7 pt-40 pb-20 pl-32 pr-32">
					<view class="d-flex flex-wrap j-sb">
						<view class="p-rela bg-ffffff w-334 b-rad-10 o-hid mb-18" v-for="(item, index) in goodsList" :key="index" 
						@click="jump.appAndMiniJump(1, `/packageB/pages/rabbit-head-goods-detail/rabbit-head-goods-detail?id=${item.id}`, $vhFrom)">
							<vh-image :src="item.banner_img" :height="208" />
								
							<view class="h-180 d-flex flex-column j-sb pt-20 pb-22 pl-16 pr-16">
								<view class="">
									<view class="font-24 font-wei text-3 l-h-30 text-hidden-2">{{item.title}}</view>
									<view class="mt-04 font-20 text-9 l-h-34 text-hidden-1">{{item.brief}}</view>
								</view>
								<view class="d-flex j-sb a-center">
									<view class="d-flex a-center">
										<text class="font-28 font-wei text-e80404 l-h-30">{{item.rabbit}}</text>
										<image class="w-32 h-34 ml-04" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png" mode="widthFix" />
									</view>
									
									<view class="">
										<text class="font-24 text-9">已兑</text>
										<!-- 已兑 = 已购 + 马甲 -->
										<text class="font-24 text-6">{{item.purchased}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					
					<u-loadmore :status="loadStatus" />
				</view>
				
				<!-- 兔头商品列表（无数据） -->
				<vh-empty v-else :padding-top="52" :padding-bottom="400" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_goods.png" text="亲，暂无商品哦~" :text-bottom="0" />
			</view>
			
			<!-- 优惠券列表 -->
			<view v-if="current == 1" class="fade-in p-rela z-03">
				<!-- 优惠券列表（有数据） -->
				<view v-if="couponList.length" class="bg-f7f7f7 d-flex flex-column j-center a-center pt-40 pb-20">
					<view class="p-rela w-686 h-184 d-flex j-sb a-center mb-20" v-for="(item, index) in couponList" :key="index">
						<image class="p-abso w-686 h-184" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_cou_bg.png" mode="aspectFill" />
						<view class="p-rela z-02 d-flex a-center">
							<view class="w-156 d-flex j-center">
								<text class="font-76 font-wei text-e80404"><text class="font-24">¥</text>{{item.price}}</text>
							</view>
							
							<view class="w-324">
								<view class="font-36 font-wei text-3 text-hidden-1 l-h-40">{{item.title}}</view>
								<view class="font-22 text-9 text-hidden-1 l-h-36">{{item.brief}}</view>
								<view class="mt-08 font-24 l-h-36">
									<text class="text-9">已兑</text>
									<text class="text-6">{{item.purchased}}</text>
								</view>
							</view>
						</view>
						<view class="p-rela z-02 w-202 d-flex flex-column a-center">
							<view class="d-flex a-center">
								<image class="w-32 h-34" :src="ossIcon(`/mine/rab_coi_gold.png`)" mode="widthFix" />
								<text class="ml-10 font-36 text-e80404">{{item.rabbit_price}}</text>
							</view>
							<view class="mt-10">
								<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
								:custom-style="{width:'144rpx', height:'58rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}" 
								@click="toExchange(item)">去兑换</u-button>
							</view>
						</view>
					</view>
					
					<u-loadmore :status="loadStatus" />
				</view>
			    
				<!-- 优惠券列表（无数据） -->
				<vh-empty v-else :padding-top="52" :padding-bottom="400" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_cou.png" text="亲，暂无优惠券哦~" :text-bottom="0" />
			</view>
		</view>
		
		<!-- 骨架屏 -->
		<vh-skeleton v-else :type="10000" loading-mode="flower" />
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default{
		name:"rabbit-head-shop",
		
		data(){
			return{
				loading: true, //加载状态 true = 加载中、false = 结束加载
				pageLength: 0, //页面栈长度
				from: '', //从哪个端进入 1 = 安卓、2 = ios"
				appStatusBarHeight:'', //状态栏高度
				navBackgroundColor:'rgba(224, 20, 31, 1)', //导航栏背景
				hasGotRabbitCombine: 0, //是否已经请求过兔头聚合接口 0 = 未请求过、1 = 请求过
				rabbitInfo: {}, //兔头信息
				rabbitNumber: '', //兔头数量
				capsuleList: [], //胶囊列表
				goodsList: [], //商品列表
				couponList: [], //优惠券列表
				current: 0,//tabs当前索引
				tabsList: [{ name: '商品' }, { name: '优惠券' }], //tabs切换栏
				page: 1, //第几页
				limit: 10, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //底部加载状态
				loadType: '', //加载方式
				jumpGoodsId: '',
				jumpRabbitHeadCoupon: false,
			}
		},
		
		computed: {
			
			// 获取状态栏高度
			navigationBarHeight() {
				return this.system.getSysInfo().statusBarHeight + 47
			},
			
			// 获取客户端
			getClient() {
				let client = null
				if(this.$vhFrom == '1') {
					client = 1
				}else if( this.$vhFrom == '2') {
					client = 0
				} 
				else if( this.$vhFrom == 'next') {
					client = 5
				}
				else {
					client = 2 //小程序
				}
				
				return client
			}
		},
		
		onLoad(options) {
			this.pageLength = this.pages.getPageLength() //计算页面栈长度
			if(options.from && options.statusBarHeight){
				this.from = options.from
				this.appStatusBarHeight = options.statusBarHeight //app状态栏高度
				this.muFrom(this.from) //保存来自哪个状态（解决用户在安卓、ios端登录失效的问题）
			}
			if( options.loadType ) this.loadType = options.loadType
			if (options.jumpGoodsId) this.jumpGoodsId = options.jumpGoodsId
		},
		
		onShow() {
			this.login.isLoginV2(this.$vhFrom).then(() => {
				this.init()
			})
			// if(this.login.isLogin(this.from)){
			// 	this.init()
			// }
			// if (this.$app && this.jumpGoodsId && this.jumpRabbitHeadCoupon) {
			// 	this.$customBack()
			// }
		},
		
		methods:{
			// Vuex mapMutations辅助函数
			...mapMutations(['muRabbitNum', 'muFrom']),
			
			// 初始化
			async init() {
				try {
					this.page = 1
					await this.getRabbitCombine()
					uni.stopPullDownRefresh() //停止下拉刷新
					this.loading = false
				}catch(e) {
					this.goBack()
				}
			},
			
			// 发生错误返回首页
			goBack() {
				setTimeout(() => {
					if(this.comes.isFromApp(this.$vhFrom)){ //判断是否从App过来 1 = 安卓 2 = ios
						wineYunJsBridge.openAppPage({
							client_path: { "ios_path":"goBack", "android_path":"goBack" }
						})
					}else{//h5端
						this.jump.navigateBack()
					}
				}, 1500)
			},
			
			// 返回首页 1.当酒闻详情页面没有暴露成首页的时候，默认回到上一个页面，2.当暴露成首页的时候回到首页
			jumpBack() {
				if(this.pageLength <= 1 && this.$vhFrom == '') { //当酒闻详情页被暴露成首页（分享）
				    console.log('-----------------------------------------------我的页面栈 <= 1')
					this.jump.reLaunch('/pages/index/index')
				}else{ //当商品详情页没有被暴露成首页
					if(this.comes.isFromApp(this.$vhFrom)) { //判断是否从App过来 1 = 安卓 2 = ios
						wineYunJsBridge.openAppPage({
							client_path: { "ios_path":"goBack", "android_path":"goBack" }
						});
					}
					else //h5端、微信小程序
						this.jump.navigateBack()
				}
			},
			
			// 获取兔头商城聚合接口
			async getRabbitCombine() {
				let res = await this.$u.api.rabbitCombine({
					tab: 'home', //切换栏 home：首页；commodities：兔头商品；coupon：兔头优惠券
					page: this.page ,//页数
					limit: this.limit ,//每页限制多少
					client: this.getClient //胶囊客户端 0 iOS 1 安卓 2 小程序 3 h5 4 pc（在请求home的时候 必须传，单独请求商品和优惠券的时候 不需要）
				})
				this.rabbitInfo = res.data
				if( !this.hasGotRabbitCombine ) this.navBackgroundColor = 'rgba(224, 20, 31, 0)' //导航栏背景
				const { rabbitNumber, capsule, commodities } = res.data //解构聚合数据
				this.rabbitNumber = rabbitNumber ? rabbitNumber : 0, //兔头数量
				this.muRabbitNum(this.rabbitNumber) // vuex变更兔头数量
				this.capsuleList = capsule.list //胶囊列表
				this.goodsList = commodities.list //商品列表
				this.totalPage = Math.ceil(commodities.total / this.limit) //兔头商品总页数
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore' //兔头商品加载状态
				this.hasGotRabbitCombine = 1
				if( this.loadType === 'coupon' ) this.changeTabs(1)
			},
			
			// 切换tabs状态栏
			changeTabs(index) {
				this.feedback.loading()
				this.page = 1
				this.totalPage = 1
				this.current = index
				this.current == 0 ? this.getRabbitGoods() : this.getCouonList()
			},
			
			// 获取兔头商品数据
			async getRabbitGoods() {
				let res = await this.$u.api.rabbitCombine({tab: 'commodities', page: this.page, limit: this.limit })
				const { list, total } = res.data //解构商品聚合信息
				this.handlePage('goodsList', list, total) //处理分页
			},
			
			// 获取优惠券数据
			async getCouonList() {
				let res = await this.$u.api.rabbitCombine({tab: 'coupon', page: this.page, limit: this.limit })
				const { list, total } = res.data //解构优惠券聚合信息
				this.handlePage('couponList', list, total) //处理分页
			},
			
			// 处理分页信息 listName = 列表名称（goodsList = 兔头商品、couponList = 优惠券）、resList = 接口返回的列表数据、total = 总条数
			handlePage(listName, resList , total) {
				this.page == 1 ? this[listName] = resList : this[listName] = [...this[listName], ...resList]
				this.totalPage = Math.ceil(total / this.limit)
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				this.feedback.hideLoading()
			},
			
			// 兔头记录
			rabbitRecord() {
				if(this.login.isLogin(this.$vhFrom)){
					this.jump.appAndMiniJump( 1, `/packageB/pages/rabbit-head-record/rabbit-head-record`, this.$vhFrom )
				}
			},
			
			// 赚取兔头
			earnRabbit() {
				if(this.login.isLogin(this.$vhFrom)) {
					if(this.comes.isFromApp(this.$vhFrom)){
						if(this.$vhFrom == 'next'){
							this.jump.appAndMiniJump(0, `/packageE/pages/daily-tasks/daily-tasks`,this.$vhFrom, 0, true)
						} else{
							wineYunJsBridge.openAppPage({
							client_path: { "ios_path":"DailyCheckViewController", "android_path":"com.stg.rouge.activity.EveryTaskCenterActivity" },
							ad_path_param: [
								{ 
									"ios_key":"login", "ios_val":"1",  
									"android_key":"login", "android_val":"1" 
								}
							]
						});
						}
						
					}else{
						this.jump.navigateTo(`/packageE/pages/daily-tasks/daily-tasks`)
					}
				}
			},
			
			// 去兑换
			toExchange(item) {
				console.log(item)
				if(this.login.isLogin(this.$vhFrom)){
					this.jumpRabbitHeadCoupon = true
					if (this.from === '') {
						this.jump.navigateTo(`/packageB/pages/rabbit-head-coupon/rabbit-head-coupon?id=${item.id}&jumpGoodsId=${this.jumpGoodsId}`)
					} else {
						this.jump.appAndMiniJump( 2, `/packageB/pages/rabbit-head-coupon/rabbit-head-coupon?id=${item.id}&jumpGoodsId=${this.jumpGoodsId}`, this.from )
					}
				}
			}
		},
		
		onPullDownRefresh() {
			this.page = 1
			this.init()
			if( this.current == 1 ) this.getCouonList()
		},
		
		onPageScroll(res) {
			res.scrollTop <= 100 ? this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop/100})` : 
			this.navBackgroundColor = `rgba(224, 20, 31, 1)`
		},
		
		onReachBottom() {
			if ( this.page == this.totalPage || this.totalPage == 0 ) return
			this.loadStatus = 'loading'
			this.page ++
			this.current == 0 ? this.getRabbitGoods() : this.getCouonList()
		}
	}
</script>

<style>
	@import "../../../common/css/page.css";
</style>

<style lang="scss" scoped>
	.rabbit-head-shop {
		&__tabs {
      ::v-deep {
        .u-tab {
          &-item {
            font-weight: 600 !important;
            vertical-align: top;
          }

          &-bar {
            bottom: auto;
            background: linear-gradient(214deg, #FF8383 0%, #E70000 100%);
          }
        }
      }
    }
	}
</style>