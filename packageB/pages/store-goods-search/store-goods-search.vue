<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar back-icon-color="#FFF" :background="{ background: '#E80404'}">
			<view class="d-flex a-center pr-24">
				<view class="bg-ffffff d-flex j-sb a-center b-rad-40 pl-26 pr-26">
					<view class="p-rela w-310 h-68 d-flex a-center">
						<image class="w-44 h-44" :src="`${osip}/comm/ser_gray.png`" mode="aspectFill" />
						<input class="w-200 h-p100 ml-10 font-28 text-3" type="text" v-model="keyword" @confirm="init"/>
						<view class="p-abso right-0 top-0 w-40 h-p100 d-flex j-center a-center" @click="keyword = ''">
							<image class="w-40 h-40" :src="`${osip}/comm/del_gray.png`" mode="aspectFill" />
						</view>
					</view>
				</view>
				<view class="ml-30 font-28 font-wei text-ffffff w-s-now" @click="init">搜索</view>
			</view>
		</vh-navbar>
		
		<!-- 门店商品列表 -->
		<view class="">
			<!-- 有门店商品列表 -->
			<view v-if="goodsList.length" class="ptb-20-plr-00">
				<view class="bg-ffffff d-flex mb-20 ml-20 mr-20 p-24 b-rad-10" v-for="(item, index) in goodsList" :key="item.id" @click="jump.navigateTo(`${routeTable.pgStoreGoodsDetail}?id=${item.id}`)">
					<vh-image :loading-type="2" :src="item.goods_images" :width="200" :height="200" :border-radius="10" mode="aspectFit" />
					<view class="flex-1 d-flex flex-column j-sb ml-20">
						<view class="">
							<view class="font-28 text-0 text-hidden-2 l-h-40">{{item.goods_name}}</view>
							<view class="mt-10 font-24 text-6 text-hidden-1 l-h-36">{{item.brief}}</view>
						</view>
						
						<view class="d-flex j-sb a-center">
							<text class="font-32 font-wei text-ff0013"><text class="font-24">¥</text>499.8</text>
							<text class="ml-04 font-20 text-9">已售<text class="text-ff0013">{{item.soldnum}}</text>份</text>
						</view>
					</view>
				</view>
				<u-loadmore :status="loadStatus" />
			</view>
			
			<!-- 没有门店商品列表 -->
			<vh-empty v-else :padding-top="400" :padding-bottom="680" :image-src="`${osip}/empty/emp_goods.png`" text="暂无搜索数据~" />
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		data() {
			return {
				osip:'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				loading: true, //加载状态 true = 加载中、false = 结束加载
				sid:'', //门店id
				hasGotGoodsList:0, //是否获取过门店搜索列表
				goodsList:[], //门店商品列表
				keyword: '', //搜索文本
				canSearch: 0, //是否可以点击搜索
				page: 1, //第几页
				limit: 10, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //底部加载状态
			}
		},
		
		onLoad(options) {
			this.sid = parseInt(options.sid)
			this.getStoreGoodsList()
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
		
		methods: {
			// 初始化
			async init() {
				this.page = 1
				await this.getStoreGoodsList()
			},
			
			// 获取门店商品接口
			async getStoreGoodsList() {
				if( this.hasGotGoodsList ) this.feedback.loading()
				let data = {} //接口需要的数据
				data.sid = this.sid //门店id
				data.goods_name = this.keyword, // 关键字
				data.page = this.page //第几页
				data.limit = this.limit //每页多少条
				let res = await this.$u.api.storeGoodsList(data) //门店商品列表
				let { list, total } = res.data
				this.hasGotGoodsList = 1
				this.page == 1 ? this.goodsList = list : this.goodsList = [...this.goodsList, ...list]
				this.totalPage = Math.ceil( total / this.limit )
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				this.feedback.hideLoading()
				this.loading = false
			}
		},
		
		onReachBottom() {
		   if (this.page === this.totalPage || this.totalPage == 0) return;
		   this.loadStatus = 'loading'
		   this.page ++
		   this.init()
		}
	}
</script>

<style>
	@import "../../../common/css/page.css";
</style>