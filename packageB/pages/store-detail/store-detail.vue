<template>
	<view class="content">
		<!-- 导航栏 -->
		<u-navbar back-icon-color="#FFF" :background="{background: '#E80404'}" title="门店详情" title-size="36" :title-bold="true" title-color="#FFF" />
		
		<!-- 轮播图 -->
		<view class="">
			<vh-swiper :list="storeInfo.store_img" :loading-type="2" :height="500"/>
		</view>
		
		<!-- 详情 -->
		<view class="p-rela z-100 bg-ffffff b-tl-tr-rad-20 mt-n-40 pr-32 pb-48 pl-32">
			<view class="bb-s-01-eeeeee ptb-48-plr-00 font-36 font-wei text-3">{{storeInfo.store_name}}</view>
			
			<view class="bb-s-01-eeeeee d-flex ptb-30-plr-00">
				<image class="w-28 h-28 mt-04" :src="`${osip}/store_detail/time.png`" mode="widthFix" />
				
				<view class="flex-1 ml-10">
					<view class="font-28 text-3">营业时间</view>
					<view class="mt-20 font-24 text-6">{{storeInfo.start_time}} - {{storeInfo.end_time}}</view>
				</view>
			</view>
			
			<view class="bb-s-01-eeeeee d-flex ptb-30-plr-00">
				<image class="w-28 h-28 mt-04" :src="`${osip}/store_detail/address.png`" mode="widthFix" />
				
				<view class="flex-1 ml-10">
					<view class="font-28 text-3">门店地址</view>
					<view class="mt-20 font-24 text-6">{{storeInfo.address}}</view>
				</view>
			</view>
			
			<view class="bb-s-01-eeeeee d-flex ptb-30-plr-00">
				<image class="w-28 h-28 mt-04" :src="`${osip}/store_detail/phone.png`" mode="widthFix" />
				
				<view class="flex-1 ml-10">
					<view class="font-28 text-3">联系电话</view>
					<view class="mt-20 font-24 text-6">{{storeInfo.store_phone}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	export default {
		name:'store-detail',
		
		data() {
			return {
				osip:'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				// swiperList: [], //轮播图
				// storeInfo:{}, //门店信息
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['storeInfo']),
		},
		
		// onLoad() {
		// 	this.getStoreDetail()
		// },
		
		// methods: {
		// 	// 获取门店信息
		// 	async getStoreDetail() {
		// 		let res = await this.$u.api.storeInfo({ sid:1 })
		// 		this.swiperList = res.data.store_img
		// 		this.storeInfo = res.data
		// 		console.log(res)
		// 	}
		// }
	}
</script>

<style scoped>
</style>