<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<view v-if="!loading" class="fade-in bg-f5f5f5 pb-104">
			<vh-navbar back-icon-color="#FFF" title="订单详情" title-color="#FFF" :background="{ background: navBackgroundColor }" />
			<image class="p-abso z-0 top-0 w-p100 h-400" :src="ossIcon(`/order_detail/ban.png`)" />
			<view class="p-rela z-01 mt-n-20 ml-48 mr-48">
				<view class="">
					<view v-if="[1].includes(orderInfo.status)" class="d-flex j-sb a-center">
						<view class="">
							<view class="font-36 font-wei text-ffffff">已支付</view>
							<view class="mt-08">
								<view v-if="orderInfo.countdown" class="flex-c-c">
									<vh-count-down
										:show-days="true" 
										:showHours="true" 
										dayColor="#FFF" 
										:hasDayMarginRight="false" 
										:timestamp="orderInfo.countdown" 
										separator="zh" 
										bg-color="transparent" 
										separator-color="#fff"
									    @end="orderInfo.countdown = 0" 
									/>
									<text class="font-24 text-ffffff">后<text class="ml-10">付尾款</text></text>
								</view>
								<view v-else class="font-28 text-ffffff">需付尾款：¥{{ orderInfo.balance_payment }}</view>
							</view>
						</view>
						<image class="w-128 h-128" :src="ossIcon(`/order_detail/ord_be_pad.png`)" />
					</view>
					
					<view v-if="[3].includes(orderInfo.status)" class="d-flex j-sb a-center">
						<view class="">
							<view class="font-36 font-wei text-ffffff">已结清</view>
							<view class="mt-08 font-28 text-ffffff">订单已完成，感谢您对酒云网的支持！</view>
						</view>
						<image class="w-128 h-128" :src="ossIcon(`/order_detail/ord_comp.png`)" />
					</view>
					
					<view v-if="[8].includes(orderInfo.status)" class="d-flex j-sb a-center">
						<view class="">
							<view class="font-36 font-wei text-ffffff">已退款</view>
							<view class="mt-08 font-28 text-ffffff">{{ orderInfo.refund_end_time }}</view>
						</view>
						<image class="w-128 h-128" :src="ossIcon(`/order_detail/ord_clo.png`)" />
					</view>
				</view>
			</view>
			<view class="p-rela z-01 bg-ffffff b-rad-10 mt-64 ml-24 mr-24 ptb-00-plr-24">
				<view class="d-flex ptb-32-plr-00">
					<image class="w-44 h-44 mt-04" :src="ossIcon(`/order_detail/add_bla.png`)" mode="widthFix" />
					<view class="w-540 ml-16">
						<view class="">
							<text class="mr-36 font-32 font-wei text-3">{{orderInfo.consignee}}</text>
							<text class="font-28 text-3">{{orderInfo.consignee_phone}}</text>
						</view>
						<view class="mt-12 font-24 text-3 l-h-34">{{orderInfo.province_name}} {{orderInfo.city_name}} {{orderInfo.district_name}} {{orderInfo.address}}</view>
					</view>
				</view>
			</view>
			
			<OrderDetailDepositGoods :orderInfo="orderInfo" />
			<OrderDetailDepositDetails :orderInfo="orderInfo" />
			
			<vh-split-line 
				:padding-top="52" 
				:padding-bottom="32" 
				:margin-left="10" 
				:margin-right="10" 
				text="猜你喜欢" 
				:font-bold="true" 
				:font-size="36" 
				text-color="#333"
				:show-image="true" 
				:image-src="ossIcon(`/comm/guess_love.png`)" 
			/>
			<vh-goods-recommend-list :jumpType="1" />
			<view v-if="[1].includes(orderInfo.status) && orderInfo.countdown === 0" class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff flex-e-c b-sh-00021200-022 pr-24">
				<view class="">
					<u-button
						shape="circle" 
						:hair-line="false"
						:ripple="true" 
						ripple-bg-color="#FFF" 
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" 
						@click="jump.appAndMiniJump(1, `${$routeTable.pgGoodsDetail}?id=${orderInfo.goodsInfo[0].period}`, $vhFrom, 1)">
						支付尾款
					</u-button>
				</view>
			</view>
		</view>
		<view v-else class="fade-in">
			<u-navbar back-icon-color="#FFF" title="订单详情" title-size="36" :title-bold="true" title-color="#FFF" :background="{ background: '#E80404' }" />
			<vh-skeleton :type="3" loading-color="#E80404" />
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		data: () => ({
			loading: true,
			navBackgroundColor:'rgba(224, 20, 31, 0)', 
			orderNo:'', 
			orderInfo: {}, 
		}),
		computed: { ...mapState(['routeTable']) },
		onLoad(options) {
			this.orderNo = options.orderNo
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
			  if (!isLogin) return
			  this.init().finally(() => {
			  	this.loading = false
			  }) 
			})
		},
		methods:{
			...mapMutations(['muPayInfo']),
			async init() {
				await this.getOrderDetail()
			},
			async getOrderDetail(){
				let res = await this.$u.api.depositOrderDetail({ order_no: this.orderNo })
				this.orderInfo = res?.data || {}
			},
		},
		onPageScroll(res) {
			res.scrollTop <= 100 ? 
			this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop/100})` : 
			this.navBackgroundColor = `rgba(224, 20, 31, 1)`
		}
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>
