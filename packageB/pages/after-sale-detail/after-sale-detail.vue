<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in bg-f5f5f5 pb-124">
			<!-- banner -->
			<view class="p-abso top-0 w-p100 h-374">
				<image class="w-p100 h-400" :src="`${osip}/after_sale_detail/ban.png`" mode="widthFix" />
			</view>
					
			<!-- 导航栏 -->
			<vh-navbar title="售后详情">
				<view class="d-flex a-center" @click="system.customerService($vhFrom)">
					<image class="w-56 h-56" :src="`${osip}/after_sale_detail/ser.png`" mode="aspectFill"/>
				</view>
			</vh-navbar>
			<!-- <u-navbar back-icon-color="#FFF" title="售后详情" :title-bold="true" title-color="#FFF" :background="{ background: navBackgroundColor }">
				<view class="d-flex a-center" @click="system.customerService()">
					<image class="w-56 h-56" :src="`${osip}/after_sale_detail/ser.png`" mode="aspectFill"/>
				</view>
			</u-navbar> -->
					
			<!-- 售后状态 -->
			<view class="">
				<view class="p-rela z-1 mt-n-10 ml-48 mr-48">
					<!-- 等待商家处理 √-->
					<view v-if="afterSaleInfo.status == 1" class="d-flex j-sb a-center">
						<view class="">
							<view class="font-36 font-wei text-ffffff">{{afterSaleInfo.status_msg}}</view>
							<view class="mt-08 d-flex">
								<view class="font-28 text-ffffff">还剩</view>
								<vh-count-down :timestamp="afterSaleInfo.countdown" bg-color="transparent" separator="zh" day-color="#FFF" :has-day-margin-right="false" :font-size="28" :separator-size="28" separator-color="#FFF" @end="afterSaleInfo.countdown = 0"/>
							</view>
						</view>
						<image class="w-128 h-128" :src="`${osip}/after_sale_detail/aft_sale_pro_ing.png`" mode="aspectFill" />
					</view>
					
					<!-- 商家同意退货退款、换货并填写物流单号 √-->
					<view v-if="afterSaleInfo.status == 2" class="d-flex j-sb a-center">
						<view v-if="returnMethodName && returnMethod == 1" class="">
							<view class="font-36 font-wei text-ffffff">请退货并填写物流单号</view>
							<view class="mt-08 d-flex">
								<view class="font-28 text-ffffff">还剩</view>
								<vh-count-down :timestamp="afterSaleInfo.countdown" bg-color="transparent" separator="zh" 
								:show-days="false" :font-size="28" :separator-size="28" separator-color="#FFF" @end="afterSaleInfo.countdown = 0"/>
							</view>
						</view>
						<view v-else class="">
							<view class="font-36 font-wei text-ffffff">{{afterSaleInfo.status_msg}}</view>
							<view class="mt-08 font-28 text-ffffff">{{afterSaleInfo.handle_time}}</view>
						</view>
						<image class="w-128 h-128" :src="`${osip}/after_sale_detail/aft_sale_pro_ing.png`" mode="aspectFill" />
					</view>
					
					<!-- 等待商家验收 √-->
					<view v-if="afterSaleInfo.status == 3" class="d-flex j-sb a-center">
						<view class="">
							<view class="font-36 font-wei text-ffffff">{{afterSaleInfo.status_msg}}</view>
							<view class="mt-08 d-flex">
								<view class="font-28 text-ffffff">还剩</view>
								<vh-count-down :timestamp="afterSaleInfo.countdown" bg-color="transparent" separator="zh" 
								:has-day-margin-right="false" day-color="#FFF" :font-size="28" :separator-size="28" separator-color="#FFF" @end="afterSaleInfo.countdown = 0"/>
							</view>
						</view>
						<image class="w-128 h-128" :src="`${osip}/after_sale_detail/aft_sale_pro_ing.png`" mode="aspectFill" />
					</view>
					
					<!-- 商家退款中、商家已签收 √-->
					<view v-if="afterSaleInfo.status == 4" class="d-flex j-sb a-center">
						<view class="">
							<view class="font-36 font-wei text-ffffff">{{afterSaleInfo.status_msg}}</view>
							<view class="mt-08 d-flex">
								<view class="font-28 text-ffffff">还剩</view>
								<vh-count-down :timestamp="afterSaleInfo.countdown" bg-color="transparent" separator="zh" 
								:has-day-margin-right="false" day-color="#FFF" :font-size="28" :separator-size="28" separator-color="#FFF" @end="afterSaleInfo.countdown = 0"/>
							</view>
						</view>
						<image class="w-128 h-128" :src="`${osip}/after_sale_detail/aft_sale_pro_ing.png`" mode="aspectFill" />
					</view>
					
					<!-- 商家已签收 （2022-05-14暂时不用） -->
					<!-- <view v-if="afterSaleInfo.status == 5" class="d-flex j-sb a-center">
						<view class="">
							<view class="font-36 font-wei text-ffffff">{{afterSaleInfo.status_msg}}</view>
							<view class="mt-08 font-28 text-ffffff">{{afterSaleInfo.acceptance_time}}</view>
						</view>
						<image class="w-128 h-128" :src="`${osip}/after_sale_detail/aft_sale_pro_ing.png`" mode="aspectFill" />
					</view> -->
					
					<!-- 退款成功、已完成 √-->
					<view v-if="afterSaleInfo.status == 8" class="d-flex j-sb a-center">
						<view class="">
							<view class="font-36 font-wei text-ffffff">{{afterSaleInfo.status_msg}}</view>
							<view class="mt-08 font-28 text-ffffff">{{afterSaleInfo.handle_time}}</view>
						</view>
						<image class="w-128 h-128" :src="`${osip}/after_sale_detail/aft_sale_comp.png`" mode="aspectFill" />
					</view>
					
					<!-- 已完成 -->
					<view v-if="afterSaleInfo.status == 6 && (afterSaleInfo.type == 2 || afterSaleInfo.type == 3)" class="d-flex j-sb a-center">
						<view class="">
							<view class="font-36 font-wei text-ffffff">已完成</view>
							<view class="mt-08 font-28 text-ffffff">{{afterSaleInfo.refund_time}}</view>
						</view>
						<image class="w-128 h-128" :src="`${osip}/after_sale_detail/aft_sale_comp.png`" mode="aspectFill" />
					</view>
					
					<!-- 拒绝售后 √ -->
					<view v-if="afterSaleInfo.status == 9" class="d-flex j-sb a-center">
						<view class="">
							<view class="font-36 font-wei text-ffffff">{{afterSaleInfo.status_msg}}</view>
							<view class="mt-08 font-28 text-ffffff">{{afterSaleInfo.handle_time}}</view>
						</view>
						<image class="w-128 h-128" :src="`${osip}/after_sale_detail/aft_sale_clo.png`" mode="aspectFill" />
					</view>
					
				</view>
				
				<view class="p-rela z-1 bg-ffffff b-rad-10 mt-20 mr-24 ml-24 ptb-32-plr-24">
					<!-- 等待商家处理 √-->
					<view v-if="afterSaleInfo.status == 1" class="">
						<view class="font-28 font-wei text-3">{{afterSaleInfo.countdown == 0 ? afterSaleInfo.countdown_title : afterSaleInfo.title}}</view>
						<view class="mt-10 font-24 text-9 l-h-34" v-html="afterSaleInfo.description" />
					</view>
					
					<!-- 商家同意退货退款、换货并填写物流单号 √-->
					<view v-if="afterSaleInfo.status == 2">
						<view class="d-flex j-sb a-center" :class="returnMethodName ? 'pb-32' : ''" @click="showReturnMethodPop = true">
							<view class="font-28 font-wei text-3 l-h-40">选择退货方式</view>
							<view class="d-flex a-center">
								<text class="mr-16 font-28 l-h-40" :class="returnMethodName == '' ? 'text-9' : 'text-3'">{{returnMethodName == '' ? '请选择' : returnMethodName}}</text>
								<image :src="`${osip}/after_sale_detail/arrow_right_12x24.png`" class="w-12 h-24" />
							</view>
						</view>
						
						<view v-if="returnMethodName && returnMethod == 1" class="fade-in pt-32 d-flex flex-column bt-s-01-f8f8f8 font-28 text-3 l-h-48">
							<view class="d-flex j-sb a-center">
								<view class="font-30 text-3">快递公司</view>
								<view class="w-502 h-60 bg-fafafa d-flex j-sb a-center b-rad-04 ml-32" @click="showLogisticsCompanyPop = true">
									<!-- <input v-model="logisticsCompany" class="w-400 font-28 text-3 ml-26" type="text" placeholder="请选择物流公司" placeholder-style="color:#999;font-size:24rpx;"/> -->
									<view class="w-400 ml-26 text-hidden-1" :class="logisticsCompany ? 'font-28 text-3' : 'font-24 text-9'">{{logisticsCompany}}</view>
									<view class="w-60 d-flex j-center">
										<u-icon class="" name="arrow-down" :size="28" color="#333" />
									</view>
								</view>
							</view>
						
							<view class="d-flex j-sb a-center mt-20">
								<view class="font-30 text-3">物流单号</view>
								<view class="w-502 h-60 bg-fafafa d-flex j-sb a-center b-rad-04 ml-32">
									<input v-model="logisticsOrderNo" class="w-400 font-28 text-3 ml-26" placeholder="请填写物流单号" placeholder-style="color:#999;font-size:24rpx;"/>
								</view>
							</view>
						</view>

						<view v-if="returnMethodName && returnMethod === 2" class="fade-in pt-32 bt-s-02-f8f8f8">
							<view class="d-flex j-sb a-center" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=5`)">
								<view class="font-30 text-3 l-h-50">取件地址</view>
								<view class="w-502 d-flex j-end a-center">
									<view class="w-max-474">
										<view class="font-28 text-3 l-h-40">{{ afterSaleInfo.pick_up_province_name }} {{ afterSaleInfo.pick_up_city_name }} {{ afterSaleInfo.pick_up_district_name }} {{ afterSaleInfo.pick_up_address }}</view>
										<view class="font-24 text-9 l-h-34 text-right">{{ afterSaleInfo.pick_up_consignee }} {{ afterSaleInfo.pick_up_consignee_phone }}</view>
									</view>
									<image :src="`${osip}/after_sale_detail/arrow_right_12x24.png`" class="w-12 h-24 ml-16" />
								</view>
							</view>
						
							<view class="d-flex j-sb a-center mt-32" @click="openPickUpTimePopup">
								<view class="font-30 text-3 l-h-50">上门时间</view>
								<view v-if="pickUpDateList.length" class="d-flex a-center">
									<text class="font-28" :class="findPickUpDate ? 'text-3' : 'text-9'">{{ findPickUpDate ? `${findPickUpDate.day} ${findPickUpDate.timeRange}` : '请选择' }}</text>
									<image :src="`${osip}/after_sale_detail/arrow_right_12x24.png`" class="w-12 h-24 ml-16" />
								</view>
								<view v-else class="font-28 text-9">该取件地址暂无上门时间</view>
							</view>
						</view>
						
					</view>
					
					<!-- 等待商家验收 √-->
					<view v-if="afterSaleInfo.status == 3" class="">
						<view class="font-28 font-wei text-3">{{afterSaleInfo.countdown == 0 ? afterSaleInfo.countdown_title : afterSaleInfo.title}}</view>
						<view class="mt-10 font-24 text-9 l-h-34" v-html="afterSaleInfo.description" />
					</view>
					
					<!-- 商家退款中、商家已签收 √-->
					<view v-if="afterSaleInfo.status == 4" class="font-28 font-wei text-3">{{afterSaleInfo.countdown == 0 ? afterSaleInfo.countdown_title : afterSaleInfo.title}}</view>
					
					<!-- 商家已签收 （2022-05-14暂时不用）-->
					<!-- <view v-if="afterSaleInfo.status == 5" class="d-flex j-sb a-center">
						<view class="font-28 font-wei text-3">{{afterSaleInfo.title}}</view>
						<view class="font-24 text-ff9127">查看换货订单</view>
					</view> -->
					
					<!-- 退款成功 √-->
					<view v-if="afterSaleInfo.status == 8 && (afterSaleInfo.type == 1 || afterSaleInfo.type == 2)" class="d-flex j-sb a-center">
						<view class="d-flex a-center">
							<text class="font-32 font-wei text-3">退款总金额</text>
							<text class="ml-12 font-32 font-wei text-e80404">¥{{afterSaleInfo.return_money}}</text>
						</view>
						
						<view class="font-20 text-ff9127">已退回您的支付账户</view>
					</view>
					
					<!-- 已完成 √-->
					<view v-if="afterSaleInfo.status == 8 && afterSaleInfo.type == 3" class="d-flex j-sb a-center">
						<view class="font-28 font-wei text-3">{{afterSaleInfo.title}}</view>
						<view class="font-20 text-ff9127" @click="jump.navigateTo(`${routeTable.pBOrderDetail}?orderNo=${afterSaleInfo.exchange_order_status == 0 ? afterSaleInfo.exchange_order_main : afterSaleInfo.exchange_order_no}`)">查看换货订单</view>
					</view>
					
					<!-- 拒绝售后 √-->
					<view v-if="afterSaleInfo.status == 9" class="">
						<view class="font-28 font-wei text-3">{{afterSaleInfo.title}}</view>
						<view class="mt-10 font-24 text-9 l-h-34" v-html="afterSaleInfo.description" />
					</view>
				</view>
			</view>
			 
			<!-- 退款商品 -->
			<view class="bg-ffffff b-rad-10 mt-20 mr-24 ml-24 ptb-32-plr-24">
				<view class="font-32 font-wei text-3">{{afterSaleInfo.type == 3 ? '换货商品' : '退款商品'}} </view>
				
				<view class="mt-20 d-flex" v-for="(item, index) in afterSaleGoodsInfo.goodsInfo" :key="index">
					<OrderGoodsImage :orderType="afterSaleGoodsInfo.order_type" :goodsImg="item.goods_img" />
					<view class="flex-1 d-flex flex-column j-sb ml-12">
						<view class="">
							<OrderGoodsTitle :goodsInfo="item" />
							<OrderGoodsTag :orderType="afterSaleGoodsInfo.order_type" :auctionType="afterSaleGoodsInfo.auction_type" :goodsInfo="item" />
						</view>
						
						<view class="d-flex j-sb">
							<text class="font-22 text-6">x{{item.order_qty}}</text>
							<text class="font-28 text-3">¥{{item.package_price}}</text>
						</view>
					</view>
				</view>
			</view>
					
			<!-- 退款信息 -->
			<view class="bg-ffffff b-rad-10 mt-20 mr-24 ml-24 pt-32 pr-24 pl-24">
				<view v-if="afterSaleInfo.type == 3" class="">
					<view class="font-32 font-wei text-3">换货信息</view>

					<view class="d-flex j-sb a-center ptb-32-plr-00 bb-s-01-eeeeee">
						<text class="font-28 font-wei text-3">换货原因</text>
						<text class="font-24 text-3">{{afterSaleInfo.refund_reason}}</text>
					</view>
					
					<view class="d-flex j-sb ptb-32-plr-00 bb-s-01-eeeeee">
						<view class="font-28 font-wei text-3 w-s-now">收货地址</view>
						
						<view class="flex-1 d-flex a-center ml-28">
							<view class="">
								<view class="d-flex a-center">
									<text class="font-28 font-wei text-3">{{afterSaleInfo.consignee}}</text>
									<text class="ml-20 font-28 text-3">{{afterSaleInfo.consignee_phone}}</text>
								</view>
								
								<view class="mt-08 font-24 text-9">{{afterSaleInfo.province_name}} {{afterSaleInfo.city_name}} {{afterSaleInfo.district_name}} {{afterSaleInfo.address}}</view>
							</view>
						</view>
					</view>
				</view>
				<view v-else class="">
					<view class="font-32 font-wei text-3">退款信息</view>
					
					<view class="d-flex j-sb a-center ptb-32-plr-00 bb-s-01-eeeeee">
						<text class="font-28 font-wei text-3">退款原因</text>
						<text class="font-24 text-3">{{afterSaleInfo.refund_reason}}</text>
					</view>
					
					<view class="d-flex j-sb a-center ptb-32-plr-00 bb-s-01-eeeeee">
						<text class="font-28 font-wei text-3">退款金额</text>
						<text class="font-24 text-3">¥{{afterSaleInfo.return_money}}</text>
					</view>
				</view>
				<view class="d-flex j-sb a-center ptb-32-plr-00">
					<text class="font-28 font-wei text-3">申请时间</text>
					<text class="font-24 text-3">{{afterSaleInfo.created_time}}</text>
				</view>
				<view class="d-flex j-sb a-center ptb-32-plr-00">
					<text class="font-28 font-wei text-3">订单编号</text>
					<text class="font-24 text-3">{{afterSaleInfo.refund_order_no}}</text>
				</view>
			</view>
					
			 <!-- 分割线 -->
			 <vh-split-line :padding-top="52" :padding-bottom="32" :margin-left="10" :margin-right="10" text="猜你喜欢" :font-bold="true" :font-size="36" text-color="#333333"
			 :show-image="true" :image-src="`${osip}/comm/guess_love.png`" />
			
			<!-- 猜你喜欢列表 -->
			<vh-goods-recommend-list :outer-padding-bottom="0"/>
			
			<!-- 弹框 -->
			<view class="">
				   <!-- 撤销申请弹框 -->
				   <u-modal v-model="showRevokeApplyModal" :show-title="false" :show-cancel-button="true" :cancel-style="{fontSize:'28rpx', color:'#999999'}"  confirm-text="确定" :confirm-style="{fontSize:'28rpx', color:'#CA101A'}">
					  <view class="ptb-44-plr-00 text-center font-28 text-3 l-h-48">
						<view class="">您将撤销本次申请，如果问题未解决，</view>
						<view class="">你还可以再次发起。确定继续吗？</view>
					  </view>
				   </u-modal>
					
				   <!-- 填写物流信息弹框 -->
				   <!-- <u-modal v-model="showLogisticsInformationModal" :width="606" :show-title="false" :show-cancel-button="true" :cancel-style="{fontSize:'28rpx', color:'#999999'}"  confirm-text="确定" :confirm-style="{fontSize:'28rpx', color:'#CA101A'}">
						<view class="bb-s-01-eeeeee ptb-40-plr-00 text-center font-32 font-wei text-0">填写物流信息</view>
						  <view class="ptb-60-plr-00 d-flex flex-column j-center a-center font-28 text-3 l-h-48">
							   <view class="d-flex a-center">
									<view class="font-30 text-3">快递公司</view>
									<view class="w-330 h-60 bg-fafafa d-flex j-center a-center ml-32">
										<input class="w-278 font-28 text-3" type="text" />
									</view>
								</view>
							
								<view class="d-flex a-center mt-40">
									<view class="font-30 text-3">物流单号</view>
									<view class="w-330 h-60 bg-fafafa d-flex j-center a-center ml-32">
										<input class="w-278 font-28 text-3" type="number" />
									</view>
								</view>
						  </view>
					</u-modal> -->
		 
				   <!-- 退货方式 -->
				   <u-popup v-model="showReturnMethodPop" mode="bottom" :border-radius="20" @close="returnMethodName = returnMethod === 2 ? '上门取件' : '自行寄回'">
						<view class="pt-36 pl-48 pr-48">
							<view class="d-flex j-center a-center font-36 font-wei text-3">退货方式</view>
							
							<view class="pt-60 pb-40 bb-s-01-eeeeee" @click="returnMethod = 2">
								<view class="d-flex j-sb a-center">
									<view class="font-28 font-wei text-3">上门取件</view>
									<vh-check :width="36" :height="36" :checked="returnMethod == 2"  @click="returnMethod = 2"/>
								</view>
								<view class="mt-10 font-24" :class="returnMethod == 2 ? 'text-ff9127' : 'text-9'">
									{{returnMethod == 2 ? '请选择上门取件时间，我们将为您上门取件，可随时修改或取消时间' : '选择上门取件时间后，我们将为您预约上门取件'}}
								</view>
								<view v-if="returnMethod == 2" class="d-flex j-sb a-center mt-24">
									<template v-if="pickUpDateList.length">
										<view class="d-flex">
											<view
												v-for="(item, index) in pickUpDateList.slice(0, pickUpDateShowNum)" :key="index"
												class="w-260 h-94 d-flex flex-column j-center a-center b-rad-10 font-28 bg-f7f7f7 text-9 l-h-40 mr-20"
												:class="pickUpDateId === item.pickUpDateId ? 'b-s-02-ff9127 text-ff9127' : ''"
												@click="pickUpDateId = item.pickUpDateId"
											>
												<text>{{ item.timeRange }}</text>
												<text>{{ item.day }}</text>
											</view>
										</view>
										<view v-if="pickUpDateList.length > pickUpDateShowNum" class="d-flex a-center" @click="openPickUpTimePopup">
											<text class="font-28 text-9">更多</text>
											<image :src="`${osip}/after_sale_detail/arrow_right_12x20.png`" class="w-12 h-20 ml-10" />
										</view>
									</template>
									<view v-else class="font-28 text-9">该取件地址暂无上门时间</view>
								</view>
							</view>
							<view class="pt-36 pb-40" @click="returnMethod = 1">
								<view class="d-flex j-sb a-center">
									<view class="font-28 font-wei text-3">自行寄回</view>
									<vh-check :width="36" :height="36" :checked="returnMethod == 1"  @click="returnMethod = 1"/>
								</view>
								<view class="mt-14 font-24 text-9">放弃丢损必赔保障服务，运费自行支付/垫付</view>
								<view v-if="returnMethod == 1" class="fade-in bg-f7f7f7 b-rad-10 mt-24 p-20 font-24 text-9">
									<view class="l-h-34">售后地址：{{afterSaleInfo.return_address}}</view>
									<view class="l-h-34">{{afterSaleInfo.return_contacts}} {{afterSaleInfo.return_phone}}</view>
								</view>
							</view>
						</view>
				   </u-popup>
		        
					<!-- 选择物流公司公司弹框 -->
					<u-popup v-model="showLogisticsCompanyPop" mode="bottom" height="500" :border-radius="20">
						<view class="pl-48 pr-48">
							<view class="p-stic top-0 bg-ffffff d-flex j-center a-center pt-40 font-36 font-wei text-3">快递公司</view>
							<view class="mt-20">
								<view class="d-flex j-sb a-center bb-s-01-eeeeee ptb-40-plr-00" v-for="(item, index) in logisticsCompanyList" :key="item.company_id" @click="selectLogisticsCompany(item)">
									<text class="font-28 font-wei text-3">{{item.company}}</text>
									<vh-check :width="36" :height="36" :checked="logisticsCompanyId == item.company_id" @click="selectLogisticsCompany(item)"/>
								</view>
							</view>
						</view>
					</u-popup>

					<u-popup v-model="pickUpTimePopupVisible" mode="bottom" height="614" :border-radius="20">
						<view class="d-flex j-center a-center h-122 font-wei-600 font-36 text-3">选择上门时间段</view>
						<view class="h-388">
							<picker-view :value="pickUpDatePicker" @change="handlePickerChange" class="h-p100">
								<picker-view-column>
									<view class="d-flex j-center a-center ptb-00-plr-08 font-32 text-3" v-for="(day, index) in pickUpDayList" :key="index">
										<view class="o-hid w-s-now t-o-ell">{{ day }}</view>
									</view>
								</picker-view-column>
								<picker-view-column>
									<view class="d-flex j-center a-center ptb-00-plr-08 font-32 text-3" v-for="(time, index) in pickUpTimeList" :key="index">
										<view class="o-hid w-s-now t-o-ell">{{ time }}</view>
									</view>
								</picker-view-column>
							</picker-view>
						</view>
						<view class="d-flex j-center a-center h-104 b-sh-00021200-022" @click="updatePickUpDateId">
							<button class="d-flex j-center a-center w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32">{{ pickUpPickerBtnText }}</button>
						</view>
					</u-popup>
			</view>
			
			<!--底部按钮 -->
			<view class="">
				<!--  售后进度 = 待用户填写物流信息（ status == 2 ） && 服务类型 = 退货退款/换货（ type == 2 || type == 3） -->
				<view v-if=" (afterSaleInfo.status == 2 && (afterSaleInfo.type == 2 || afterSaleInfo.type == 3))" class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-end a-center pl-32 pr-24">
					<!-- <view v-if="false" class="d-flex a-center">
						<view class="">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#999', backgroundColor: '#FFF', border:'2rpx solid #999'}"
							@click="">撤销申请</u-button>
						</view>
						<view v-if="false" class="ml-24">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
							:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
							@click="">修改申请</u-button>
						</view>
						
						<view v-if="true" class="ml-24">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
							:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
							@click="showLogisticsInformationModal = true">我已寄出</u-button>
						</view>
					</view> -->
					
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#FFF', backgroundColor: !canSubmit ? '#FCE4E3' : '#E80404', border:'none'}"
					:disabled="!canSubmit"
					@click="fillReturnInfo">提交</u-button>
				</view>
			    
				<!-- 拒绝售后（ status == 9 ） -->
				<view v-if="afterSaleInfo.status == 9" class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-end a-center pl-32 pr-24">
					<view class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', color:'#666', border:'1rpx solid #666'}" 
						@click="applyAfterSale()">申请售后</u-button>
					</view>
				</view>

				<view v-if="afterSaleInfo.status === 1" class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-end a-center pl-32 pr-24">
					<view class="">
						<u-button
							shape="circle"
							:hair-line="false"
							:ripple="true"
							ripple-bg-color="#FFF"
							:custom-style="{ width:'208rpx', height:'64rpx', fontSize:'28rpx', color:'#666', border:'1rpx solid #666' }" 
							@click="onCancelAfterSale"
						>撤销申请</u-button>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 骨架屏 -->
		<view v-else class="fade-in">
			<u-navbar back-icon-color="#FFF" title="售后详情" title-size="36" :title-bold="true" title-color="#FFF" :background="{ background: '#E80404' }" />
			<vh-skeleton :type="3" loading-color="#E80404" />
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default{
		name:"after-sale-detail",
		
		data(){
			return{
				osip:'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				loading: true, //加载状态 true = 加载中、false = 结束加载
				navBackgroundColor:'rgba(224, 20, 31, 0)', //导航栏背景
				afterSaleInfo:{}, //售后信息
				countdownTimer: null, //定时器
				showRevokeApplyModal: false, //是否显示撤销申请弹框
				// showLogisticsInformationModal: false, //是否展示物流信息弹框
				showReturnMethodPop: false, //是否显示退货方式弹框
				showLogisticsCompanyPop: false, //是否显示快递公司弹框
				returnMethodName:'', //退还方式名称
				returnMethod: 1, //退换邮寄方式：1-自行寄回 2-上门取件
				pickUpTimePeriod: 1,//上门取件时间段：1-上午(9:00-13:00) 2-下午(13:00-19:00)
				logisticsCompanyList:[], //物流公司列表
				logisticsCompany: '', //物流公司
				logisticsCompanyId:'', //物流公司id
				logisticsOrderNo: '', //物流单号
				pickUpDateId: '',
				pickUpDate: [],
				pickUpDateList: [],
				pickUpDateShowNum: 2,
				pickUpTimePopupVisible: false,
				pickUpDatePicker: [0, 0],
				pickUpDateListLoading: false
			}
		},
		
		onLoad() {
			const SaleGoodsInfo = uni.getStorageSync('SaleGoodsInfo');
			if(SaleGoodsInfo){
				this.muAfterSaleGoodsInfo(SaleGoodsInfo)
				uni.removeStorageSync('SaleGoodsInfo');
			}
			console.log('afterSaleGoodsInfo', this.afterSaleGoodsInfo)
			if (!this.hasAfterSaleGoodsInfo) {
				this.jump.reLaunch(`${this.routeTable.pEMyOrder}?status=6`)
				return
			}
			this.init()
		},

		onShow () {
			if (this.returnMethodName && Object.keys(this.addressInfoState).length) {
				console.log('addressInfoState', this.addressInfoState)
				const { province_id, province_name, city_id, city_name, town_id, town_name, address, consignee, consignee_phone } = this.addressInfoState
				this.afterSaleInfo = Object.assign({}, this.afterSaleInfo, {
					pick_up_province_id: province_id,
					pick_up_province_name: province_name,
					pick_up_city_id: city_id,
					pick_up_city_name: city_name,
					pick_up_district_id: town_id,
					pick_up_district_name: town_name,
					pick_up_address: address,
					pick_up_consignee: consignee,
					pick_up_consignee_phone: consignee_phone
				})
				this.getPickUpDateList()
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable', 'afterSaleGoodsInfo', 'addressInfoState']),
			
			// 是否可以提交
			canSubmit() {
				if(this.returnMethod == 2) {
					if(this.returnMethodName && this.pickUpDateId && !this.pickUpDateListLoading) {
						return true
					}
					return false
				}else {
					if(this.returnMethodName && this.logisticsCompany && this.logisticsOrderNo) {
						return true
					}
					return false
				}
			},
			hasAfterSaleGoodsInfo({ afterSaleGoodsInfo }) {
				return !!(typeof afterSaleGoodsInfo === 'object' && Object.keys(afterSaleGoodsInfo).length)
			},
			findPickUpDate ({ pickUpDateId, pickUpDateList }) {
				return pickUpDateList.find(item => item.pickUpDateId === pickUpDateId)
			},
			pickUpDayList ({ pickUpDate }) {
				return pickUpDate.map(item => item.day)
			},
			pickUpTimeList ({ pickUpDate, pickUpDatePicker }) {
				const findItem = pickUpDate[pickUpDatePicker[0]]
				return findItem ? findItem.timeList.map(item => item.timeRange) : []
			},
			pickUpPickerBtnText ({ pickUpDayList, pickUpTimeList, pickUpDatePicker }) {
				const [dayIndex, timeIndex] = pickUpDatePicker
				return `${pickUpDayList[dayIndex]} ${pickUpTimeList[timeIndex]}`
			}
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muAfterSaleGoodsInfo']),
			
			// 初始化 (售后详情、快递公司列表)
			async init() {
				// Promise.all([this.getAfterSaleDetail(), this.getLogisticsCompanyList()])
				await this.getAfterSaleDetail()
				await this.getLogisticsCompanyList()
				this.getPickUpDateList()
				this.loading = false
			},
			
			// 获取申请售后详情
			async getAfterSaleDetail() {
				let res = await this.$u.api.afterSaleDetail({ sub_order_no: this.afterSaleGoodsInfo.order_no })
				this.afterSaleInfo = res.data
				this.startCountDown() //定时器
				// this.afterSaleInfo.countdown = 10
				this.loading = false
			},
			
			// 获取快递公司列表
			async getLogisticsCompanyList() {
				if(this.afterSaleInfo.status == 2) {
					let res = await this.$u.api.logisticsCompanyList()
					let { total, list } = res.data
					this.logisticsCompanyList = list
					this.logisticsCompany = list[0].company
					this.logisticsCompanyId = list[0].company_id
				}
			},

			async getPickUpDateList () {
				if(this.afterSaleInfo.status === 2) {
					this.pickUpDateListLoading = true
					const { gd_id, pick_up_province_name, pick_up_city_name, pick_up_district_name, pick_up_address } = this.afterSaleInfo
					const params = {
						gd_id,
						pick_up_province_name,
						pick_up_city_name,
						pick_up_district_name,
						pick_up_address
					}
					const res = await this.$u.api.getPickUpDateList(params)
					console.log('res', res)
					const list = res?.data?.list || []
					this.pickUpDate = list.filter(item => item.timeList.length)
					this.pickUpDateList = list.map(item => {
						const { day } = item
						return item.timeList.map(({ startTime, endTime, timeRange }) => ({
							pickUpDateId: `${day}&${timeRange}`,
							timeRange,
							day,
							startTime,
							endTime
						}))
					}).flat()
					if (!this.pickUpDateList.some(item => item.pickUpDateId === this.pickUpDateId)) {
						this.pickUpDateId = ''
					}
					this.pickUpDateListLoading = false
				}
			},
			
			// 选择物流公司 item = 物流公司列表每一项
		    selectLogisticsCompany( item ) {
				this.logisticsCompany = item.company //物流公司
				this.logisticsCompanyId = item.company_id //物流公司id
				this.showLogisticsCompanyPop = false
			},
			
			// 填写物流单号倒计时
			startCountDown() {
				if(this.afterSaleInfo.status == 2) {
					this.clearTimer()
					if(this.afterSaleInfo.countdown <= 0) return
					this.countdownTimer = setInterval(() => {
						this.afterSaleInfo.countdown --
						if(this.afterSaleInfo.countdown < 0){
							this.clearTimer()
						}
					}, 1000)
				}
			},
			
			// 清空定时器
			clearTimer() {
				console.log('----------我是清除定时器')
				if(this.countdownTimer) {
					clearInterval(this.countdownTimer)
					this.countdownTimer = null
					this.afterSaleInfo.countdown = 0
				}
			},
			
			// 选择退款方式 type = 退换邮寄方式：1-自行寄回 2-上门取件
			selectReturnMethod(type) {
				this.returnMethod = type
				type == 2 ? this.returnMethodName = '上门取件' : this.returnMethodName = '自行寄回'
			},
		    
			// 退换货填写退货信息
			async fillReturnInfo() {
				if (this.returnMethod === 2 && !this.pickUpDateId) {
					this.feedback.toast({ title:'请选择上门取件时间' })
					return
				}
				if(this.$u.trim(this.returnMethodName, 'all') == '') return this.feedback.toast({ title:'请选择退货方式' }) //未选择退货方式
				if(this.$u.trim(this.logisticsCompany, 'all') == '' && this.returnMethod == 1) return this.feedback.toast({ title:'请填写物流公司' }) //未填写物流公司
				if(this.$u.trim(this.logisticsOrderNo, 'all') == '' && this.returnMethod == 1) return this.feedback.toast({ title:'请填写物流单号' }) //未填写物流单号
				const { refund_order_no, type, province_id, city_id, district_id, province_name, city_name, district_name, address, consignee, consignee_phone} = this.afterSaleInfo //售后详情
				let data = {} //接口需要上传的数据
				data.sub_order_no = refund_order_no //子订单号
				data.return_method = this.returnMethod //退换邮寄方式：1-自行寄回 2-上门取件
				if (this.returnMethod === 2) {
					const { day, startTime, endTime } = this.pickUpDateList.find(item => item.pickUpDateId === this.pickUpDateId)
					data.pick_up_start_time = `${day} ${startTime}`
					data.pick_up_end_time = `${day} ${endTime}`
					const { pick_up_province_id, pick_up_province_name, pick_up_city_id, pick_up_city_name, pick_up_district_id, pick_up_district_name, pick_up_address, pick_up_consignee_phone, pick_up_consignee } = this.afterSaleInfo
					data.pick_up_province_id = pick_up_province_id
					data.pick_up_province_name = pick_up_province_name
					data.pick_up_city_id = pick_up_city_id
					data.pick_up_city_name = pick_up_city_name
					data.pick_up_district_id = pick_up_district_id
					data.pick_up_district_name = pick_up_district_name
					data.pick_up_address = pick_up_address
					data.pick_up_consignee_phone = pick_up_consignee_phone
					data.pick_up_consignee = pick_up_consignee
				}
				data.return_logisticsid = this.logisticsCompanyId //退货物流公司ID（自行寄回必传）
				data.return_logistics = this.logisticsCompany //退货物流公司
				data.return_waybill_no = this.logisticsOrderNo //退款订单号
				
				if( type == 3 ) { //换货
					data.province_id = province_id //收货人省ID
					data.city_id = city_id //收货人市ID
					data.district_id = district_id //收货人区县ID
					data.province_name = province_name //换货收货省
					data.city_name = city_name //换货收货市
					data.district_name = district_name //换货收货区
					data.address = address //收货人详细地址
					data.consignee = consignee //收货人
					data.consignee_phone = consignee_phone //收货人电话
				}
				console.log('-----------------------------我是需要上传的数据')
				console.log(data)
				try{
					let res = await this.$u.api.returnAndexchangeFillInfo(data)
					console.log(res)
					this.feedback.toast({title:'提交成功~', icon:'success'})
					setTimeout(() => {
						this.getAfterSaleDetail()
					}, 1500)
				}catch(e) {
					// this.feedback.toast({title:'提交失败，请重试'})
				}
			},
		    
			// 申请售后/售后详情
			applyAfterSale(){
				const { is_application, not_apply_reason } = this.afterSaleInfo //售后详情信息
				if( is_application ) {
					this.afterSaleGoodsInfo.after_sale_status = 1
				}else{
					this.afterSaleGoodsInfo.after_sale_status = 0
					this.afterSaleGoodsInfo.forbidden_reason = not_apply_reason
				}
				this.muAfterSaleGoodsInfo( this.afterSaleGoodsInfo )
				this.jump.redirectTo( this.routeTable.pBAfterSaleGoodsService )
			},

			openPickUpTimePopup () {
				if (!this.pickUpDateList.length) return
				if (this.pickUpDateId) {
					const [day, time] = this.pickUpDateId.split('&')
					const dayIndex = this.pickUpDayList.findIndex(item => item === day)
					const timeIndex = this.pickUpTimeList.findIndex(item => item === time)
					this.pickUpDatePicker = [dayIndex, timeIndex]
				}
				this.pickUpTimePopupVisible = true
			},
			handlePickerChange (e) {
				this.pickUpDatePicker = e.detail.value
			},
			updatePickUpDateId () {
				const [dayIndex, timeIndex] = this.pickUpDatePicker
				this.pickUpDateId = `${this.pickUpDayList[dayIndex]}&${this.pickUpTimeList[timeIndex]}`
				this.pickUpTimePopupVisible = false
				this.showReturnMethodPop = false
			},
			
			onCancelAfterSale () {
				this.feedback.showModal({
					title: '撤销申请',
					content: '您将撤销本次申请，如果问题未解决，您还可以再次发起，确定继续吗？',
					confirm: () => {
						this.$u.api.cancelAfterSale({ sub_order_no: this.afterSaleGoodsInfo.order_no }).then(() => {
							this.feedback.toast({ title: '撤销成功~', icon: 'success' })
							const timer = setTimeout(() => {
								this.jump.jumpPrePage(this.$vhFrom)
								timer && clearTimeout(timer)
							}, 1000)
						})
					}
				})
			}
		},
		
		onUnload() {
			console.log('--------onUnload')
			this.clearTimer()
		},
		
		beforeDestroy() {
			console.log('--------beforeDestroy')
			// this.clearTimer()
		},
		
		onPageScroll(res) {
			res.scrollTop <= 100 ? 
			this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop/100})` : 
			this.navBackgroundColor = `rgba(224, 20, 31, 1)`
		}
	}
</script>

<style>
	@import "../../../common/css/page.css";
</style>

