<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar back-icon-color="#FFF" :background="{background: '#E80404'}" title="门店历史发票" title-color="#FFF" />
		
		<!-- 发票列表 -->
		<view class="">
			<view v-if="historyInvoicelist.length > 0" class="fade-in mt-20">
				<view class="bg-ffffff b-rad-16 mr-24 mb-20 ml-24 ptb-00-plr-24" v-for="(item, index) in historyInvoicelist" :key="index">
					<view class="d-flex bb-s-01-eeeeee pt-30 pb-30">
						<vh-image :loading-type="2" :src="item.goods_images" :width="152" :height="152" :border-radius="6" mode="aspectFit"/>
						
						<view class="flex-1 d-flex flex-column j-sb ml-12">
							<view class="">
								<view class="font-24 text-0 l-h-34 text-hidden-2">{{item.goodsname}}</view>
								<view class="mt-08">
									<text class="bg-f5f5f5 b-rad-08 ptb-04-plr-16 font-22 text-9">{{item.c_name}}</text>
								</view>
							</view>
							
							<view class="d-flex j-sb a-center l-h-34">
								<text class="font-24 text-9">x{{item.pay_number}}</text>
								<text class="font-32 text-3"><text class="font-22">¥</text>{{item.order_money}}</text>
							</view>
						</view>
					</view>
					
					<view v-if="item.receipt_info.receipt_record.type_id != 1" class="d-flex j-sb a-center bb-s-01-eeeeee ptb-30-plr-00">
						<text class="font-24 font-wei text-3">公司名称</text>
						<text class="font-24 text-3">{{item.receipt_info.receipt_record.invoice_name}}</text>
					</view>
					
					<view v-if="item.receipt_info.receipt_record.type_id != 1" class="d-flex j-sb a-center bb-s-01-eeeeee ptb-30-plr-00">
						<text class="font-24 font-wei text-3">单位税号</text>
						<text class="font-24 text-3">{{item.receipt_info.receipt_record.taxpayer}}</text>
					</view>
					
					<view class="d-flex j-sb a-center bb-s-01-eeeeee ptb-30-plr-00">
						<text class="font-24 font-wei text-3">邮箱</text>
						<text class="font-24 text-3">{{item.receipt_info.receipt_record.email}}</text>
					</view>
					
					<view class="d-flex j-sb a-center ptb-30-plr-00">
						<text class="font-24 font-wei text-3">手机</text>
						<text class="font-24 text-3">{{item.receipt_info.receipt_record.telephone}}</text>
					</view>
				</view>
				
				<u-loadmore :status="loadStatus" :margin-top="20" :margin-bottom="20"/>
			</view>
			
			<!-- 暂无门店订单 -->
			<vh-empty v-else :padding-top="300" :padding-bottom="780" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png" text="暂无历史发票~" />
		</view>
	</view>
</template>

<script>
	export default {
		name:'store-history-invoice',
		
		data() {
			return {
				loading: false, //数据是否加载完成
				historyInvoicelist:[], //历史开票列表、
				page: 1, //当前页
				limit: 10, //每页限制多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //加载状态
			}
		},
		
		onLoad() {
			this.init()
		},
		
		methods:{
			// 初始化
			async init() {
				try {
					await Promise.all([ this.getHistoryInvoicelist() ])
					this.loading = false
				}catch(e) {
					
				}
			},
			
			// 获取门店可开票订单列表
			async getHistoryInvoicelist() {
				let data = {}
				data.page = this.page //当前页
				data.limit = this.limit //每页限制多少条
				data.status = 3 //订单状态
				data.invoice_speed = 1
				let res = await this.$u.api.storeOrderList(data) 
				let { list, total } = res.data
				this.page == 1 ? this.historyInvoicelist = list : this.historyInvoicelist = [...this.historyInvoicelist, ...list]
				this.totalPage = Math.ceil( total / this.limit )
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
			},
		},
		
		onReachBottom() {
			if ( this.page == this.totalPage || this.totalPage == 0) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getHistoryInvoicelist()
		}
	}
</script>

<style>
	@import "../../../common/css/page.css";
</style>