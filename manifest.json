{
    "name" : "",
    "appid" : "__UNI__E0D005C",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {},
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {},
            /* SDK配置 */
            "sdkConfigs" : {}
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx3e0b582d1f902659",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "postcss" : true,
            "minified" : true
        },
        "usingComponents" : true,
        "optimization" : {
            "subPackages" : true
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "酒云网将获取您的地理位置"
            }
        },
        "plugins" : {
            "live-player-plugin" : {
                "version" : "1.3.5",
                "provider" : "wx2b03c6e691cd7370"
            }
        }
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "h5" : {
        "router" : {
            "mode" : "history"
        },
        "devServer" : {
            "proxy" : {
                "/api" : {
                    "target" : "https://h5.vinehoo.com/api",
                    "pathRewrite" : {
                        "^/api" : ""
                    }
                },
                "/html-statics" : {
                    "target" : "https://h5.vinehoo.com/html-statics",
                    "pathRewrite" : {
                        "^/html-statics" : ""
                    }
                }
            }
        },
        "template" : "index.html",
        "optimization" : {
            "treeShaking" : {
                "enable" : false
            }
        },
        "publicPath" : "https://h5.vinehoo.com/",
        "sdkConfigs" : {
            "maps" : {
                "amap" : {
                    "key" : "e304a90799e55d29cebce5c39f63b453",
                    "securityJsCode" : "c9f89fcf39fea0217e728afd982a98c5",
                    "serviceHost" : ""
                }
            }
        }
    }
}
