<template>
  <view class="p-40 font-30 text-3 l-h-50">
    <view>为向用户提供注册/登录、个性推荐、信息发布、互动交流、搜索、活动参与等丰富多样的服务，我们会根据用户使用，服务时收到的告知或作出的授权、用户服务协议及隐私政策约定、法律法规的相关规定收集并使用用户的个人信息。</view>
    <view class="mt-10">如果你仅希望使用酒云网为你提供的基本功能模式，包括浏览功能，可选择进入基本功能模式。在基本功能模式下，酒云网仅为保障软件及服务的安全运行，依据法律法规的相关规定收集你的个人信息，不收集你的其他个人信息。为保证最小必要地收集你的个人信息,你仅能在浏览模式(即未登录状态下)使用，且在使用基本功能模式时，你不能使用注册/登录、内容推荐、信息发布、互动交流、活动参与等丰富多样的服务，在进入基本功能模式前参加的活动奖励也会停止计算，但你可以在退出基本功能模式后继续根据活动规则参与活动或领取已获得的奖励。</view>
    <view class="d-flex j-sb a-center mt-20">
      <text class="font-36 font-wei text-3">基本功能模式</text>
      <u-switch v-model="status" @change="onChange" />
    </view>
  </view>
</template>

<script>
export default {
  data: () => ({
    from: '',
    status: false
  }),
  methods: {
    onChange () {
      if (this.status) {
        this.jump.appAndMiniJump(2, `/packageA/pages/more-than-wine/more-than-wine?id=26`, this.from)
      }
    }
  },
  onLoad (options) {
    if (options.from) this.from = options.from
    uni.setNavigationBarTitle({
      title: '基本功能模式'
    })
  },
  onShow () {
    this.status = false
  }
}
</script>