<template>
  <view class="content">
    <!-- 导航栏 -->
    <vh-navbar v-if="from == ''" :title="title"> </vh-navbar>
    <view class="share" v-if="from == '1' || from == '2'">
      <view @click="onAppShare" class="share_btn">分享</view>
    </view>
    <view class="share" v-else>
      <view @click="CopyLink" class="share_btn h5-btn-share">分享</view>
    </view>
    <!-- 商品数据加载完成 -->
    <view v-if="!loading" class="">
      <!-- 商品瀑布流列表（有数据） -->
      <view class="fade-in ptb-20-plr-24" :style="[outerEmpConStyle]" v-if="goodsList.length">
        <u-waterfall v-model="goodsList" ref="uWaterfall" :add-time="4">
          <template v-slot:left="{ leftList }">
            <view
              class="p-rela bg-ffffff w-346 b-rad-10 o-hid t-trans-3d-1 mb-10"
              v-for="(item, index) in leftList"
              :key="index"
              @click="jump.appAndMiniJump(1, `/pages/goods-detail/goods-detail?id=${item.id}`, from)"
            >
              <vh-image :src="item.banner_img" :height="214" />
              <view class="pt-20 pb-22 pl-16 pr-16">
                <view class="font-24 font-wei text-3 text-hidden-2 l-h-30">{{ item.title }}</view>
                <view class="font-24 text-9 l-h-34 text-hidden-1 l-h-34">{{ item.brief }}</view>

                <view class="mt-14 d-flex j-sb a-center">
                  <text
                    v-if="item.is_hidden_price || [3, 4].includes(item.onsale_status)"
                    class="font-28 font-wei text-e80404 l-h-44"
                    >价格保密</text
                  >
                  <text v-else class="font-36 font-wei text-e80404 l-h-44"
                    ><text class="font-24">¥</text>{{ item.price }}</text
                  >
                  <text class="font-18 text-9 l-h-36" style="font-size: 12px"
                    >已售<text class="text-e80404">{{ item.purchased + item.vest_purchased }}</text></text
                  >
                </view>
              </view>
            </view>
          </template>
          <template v-slot:right="{ rightList }">
            <view
              class="p-rela bg-ffffff w-346 b-rad-10 o-hid t-trans-3d-1 mb-10"
              v-for="(item, index) in rightList"
              :key="index"
              @click="jump.appAndMiniJump(1, `/pages/goods-detail/goods-detail?id=${item.id}`, from)"
            >
              <vh-image :src="item.banner_img" :height="214" />
              <view class="pt-20 pb-22 pl-16 pr-16" :class="index == rightList.length - 1 ? 'pb-24' : ''">
                <view class="font-24 font-wei text-3 text-hidden-2 l-h-30">{{ item.title }}</view>
                <view class="font-24 text-9 l-h-34 text-hidden-1 l-h-34">{{ item.brief }}</view>

                <view class="mt-14 d-flex j-sb a-center">
                  <text
                    v-if="item.is_hidden_price || [3, 4].includes(item.onsale_status)"
                    class="font-28 font-wei text-e80404 l-h-44"
                    >价格保密</text
                  >
                  <text v-else class="font-36 font-wei text-e80404 l-h-44"
                    ><text class="font-24">¥</text>{{ item.price }}</text
                  >
                  <text class="font-18 text-9 l-h-36" style="font-size: 12px"
                    >已售<text class="text-e80404">{{ item.purchased + item.vest_purchased }}</text></text
                  >
                </view>
              </view>
            </view>
          </template>
        </u-waterfall>
        <u-loadmore bg-color="#F5F5F5" :status="loadStatus" />
      </view>

      <!-- 商品列表（无数据） -->
      <view class="bg-ffffff" v-else>
        <vh-empty
          :padding-top="300"
          :padding-bottom="100"
          image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_goods.png"
          text="亲亲，暂无商品哟~"
          :text-bottom="0"
        />
      </view>

      <view v-if="basicFun" class="p-fixed bottom-0 w-p100 h-104 bg-ffffff d-flex j-center a-center z-9999">
        <u-button
          :hair-line="false"
          :ripple="true"
          ripple-bg-color="#FFF"
          :custom-style="{
            width: '100%',
            height: '104rpx',
            fontSize: '28rpx',
            fontWeight: '500',
            color: '#FFF',
            backgroundColor: '#E80404',
            border: 'none',
          }"
          @click="jump.jumpAppWelcome(from)"
          >退出基本功能模式，开启完整版酒云网</u-button
        >
      </view>
    </view>

    <!-- 加载状态 -->
    <vh-skeleton v-else :has-navigation-bar="true" bg-color="#F5F5F5" :show-loading="false" />
  </view>
</template>

<script>
export default {
  name: 'more-than-wine',

  data() {
    return {
      loading: true, //加载状态
      shareInfo: {
        main_title: '',
        share_image: '',
        share_url: '',
        sub_title: '',
      },
      title: '', //标题
      from: '', //从哪个端进入 1 = 安卓、2 = ios"、3 = pc
      cardId: '', //卡片id
      goodsList: [], //商品列表
      page: 1, //第几页
      limit: 10, //每页显示多少条
      totalPage: 1, //总页数
      loadStatus: 'nomore', //加载状态
      basicFun: 0,
    }
  },

  onLoad(options) {
    this.cardId = parseInt(options.id)
    if (options.from) {
      this.from = options.from
      if (options.basicFun) this.basicFun = +options.basicFun
    }
    this.getMoreThanWineList().then(() => {
      uni.setNavigationBarTitle({
        title: this.title,
      })
    })
  },
  computed: {
    // 外层缺省页容器样式
    outerEmpConStyle() {
      return {
        paddingBottom: this.from == 'next' ? '50rpx' : '24rpx',
      }
    },
  },
  methods: {
    CopyLink() {
      // 获取当前页面路径
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]

      let currentUrl = ''
      currentUrl = window.location.origin

      let url = currentUrl + '/' + currentPage.route + '?id=' + this.cardId
      if (this.from) {
        url = url + '&from=' + this.from
      }
      if (this.basicFun) {
        url = url + '&basicFun=' + this.basicFun
      }

      // 复制链接到剪贴板
      uni.setClipboardData({
        data: url,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success',
            duration: 2000,
          })
        },
      })
    },
    // 获取更多酒款列表
    async getMoreThanWineList() {
      let data = {}
      ;(data.cid = this.cardId), //卡片id
        (data.type = 1) //类型 1商品 2直播
      ;(data.page = this.page), //当前页码
        (data.limit = this.limit) //页码大小
      let res = await this.$u.api.moreThanWineList(data)
      let { list = [], total, title } = res.data
      this.shareInfo = res.data.card_info
      list.forEach((item) => {
        const { banner_img } = item
        item.banner_img = Array.isArray(banner_img) ? banner_img[0] : banner_img
      })
      this.title = title
      this.page == 1 ? (this.goodsList = list) : (this.goodsList = [...this.goodsList, ...list])
      this.totalPage = Math.ceil(total / this.limit)
      this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
      this.loading = false
      console.log(res)
    },
    onAppShare() {
      let path = `${this.$routeTable.pAMoreThanWine}?id=${this.cardId}`
      this.jump.appShare({
        des: this.shareInfo.sub_title,
        img: this.shareInfo.share_image,
        title: this.shareInfo.main_title,
        path,
      })
    },
  },

  onReachBottom() {
    if (this.page == this.totalPage || this.totalPage == 0) return
    this.loadStatus = 'loading'
    this.page++
    this.getMoreThanWineList()
  },
}
</script>

<style>
@import '../../../common/css/page.css';

.share {
  position: relative;
  z-index: 1000;
}

.share .share_btn {
  position: fixed;
  right: 0;
  top: 60rpx;
  background-color: rgba(0, 0, 0, 0.5);
  width: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  opacity: 0.9;
  color: #fff;
  padding-left: 4rpx;
  height: 40rpx;
  border-radius: 20px 0 0 20px;
  background-size: 100% 100%;
}

.share .share_btn.h5-btn-share {
  top: 110rpx !important;
}
</style>
