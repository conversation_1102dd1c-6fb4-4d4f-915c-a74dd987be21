<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar v-if="from == ''" back-icon-color="#FFF" title="快报详情"  :background="{ background: navBackgroundColor }" title-color="#FFF"/>
		
		<!-- 数据加载完后 -->
		<view v-if="!loading" class="d-flex j-center">
			<!-- banner -->
			<image class="p-abso top-0 w-p100" :src="`${osip}/news_detail/bg.png`" mode="widthFix" />
			
			<!-- 快报内容 -->
			<view class="p-rela w-684 h-1214 p-rela d-flex j-center mt-52">
				<image class="p-abso w-684 h-1214" :src="`${osip}/news_detail/news_bg.png`" mode="aspectFill" />
				
				<view class="p-rela z-02 w-684 h-1080 pl-100 pr-72 o-scr-y">
					<!-- 快报标题 -->
					<view class="d-flex j-center a-center mt-80">
						<view class="w-336 font-42 text-center text-3 font-wei">{{newsInfo.title}}</view>
					</view>
					
					<!-- 快报内容 -->
					<view class="mt-80 w-b-b-w font-28 text-3 l-h-52">
						<u-parse :html="newsInfo.context" :show-with-animation="true" />
					</view>
					
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:'news-detail', 
		
		data() {
			return{
				osip:'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				loading: true, //是否加载完成
				from: '', //从哪个端进入 1 = 安卓、2 = ios"
				navBackgroundColor: 'rgba(224, 20, 31, 0)', //导航栏背景
				newsId:'', //快报id
				newsInfo:{}, //快报详情
			}
		},
		
		onLoad(options) {
			console.log(options)
			this.newsId = parseInt(options.id)
			this.from = options.from
			this.getNewsDetail()
		},
		
		methods:{
			// 获取快报详情
			async getNewsDetail(){
				let res = await this.$u.api.newsDetail({ id: this.newsId })
				this.newsInfo = res.data
				this.loading = false
			}
		},
		
		onPageScroll(res) {
			if(res.scrollTop <= 100) {
				this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop/100})`
			}else{
				this.navBackgroundColor = `rgba(224, 20, 31, 1)`
			}
		},
	}
</script>
