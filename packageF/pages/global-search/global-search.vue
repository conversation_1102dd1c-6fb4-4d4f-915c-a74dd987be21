<template>
  <view>
    <!-- 导航栏 -->
    <vh-navbar height="46" :customBack="customBack">
      <view class="d-flex a-center w-p100">
        <view class="p-rela flex-1 flex-c-c h-68 pl-26 pr-26 bg-f7f7f7 b-rad-40">
          <image class="w-36 h-36" :src="ossIcon('/comm/search_36.png')" />
          <input
            class="flex-1 ml-08 h-p100 font-28 text-3"
            type="text"
            v-model="keyword"
            :placeholder="keywordPlaceHolder"
            placeholder-style="color:#999;font-size:28rpx;"
            @input="changeInputValue"
            @confirm="search"
          />
          <image v-if="keyword" class="p-08 w-40 h-40" :src="ossIcon('/comm/del_gray.png')" @click="clearSearchText" />
        </view>
        <view class="flex-c-c w-116 font-28 font-wei-500 text-6" @click="search">搜索</view>
      </view>
    </vh-navbar>

    <!-- 数据内容 -->
    <view v-if="!loading" class="fade-in" id="outer-content">
      <!-- 历史搜索 + 热门推荐 -->
      <view v-if="canSearch == 0" class="fade-in pl-32 pr-32">
        <view class="pt-52">
          <view v-if="hisSearchList.length" class="mb-36">
            <view class="d-flex j-sb a-center mb-32">
              <text class="font-32 font-wei l-h-44">历史搜索</text>
              <image
                v-if="hisSearchList.length"
                class="w-26 h-26"
                src="https://images.vinehoo.com/vinehoomini/v3/comm/tra_gray.png"
                mode="aspectFill"
                @click="delHisSearchRecord"
              />
            </view>
            <view class="d-flex flex-wrap a-center">
              <view
                class="w-max-660 bg-f5f5f5 b-rad-26 mr-20 mb-24 ptb-02-plr-24 font-24 text-6 l-h-40 text-hidden-1"
                v-for="(item, index) in hisSearchList"
                :key="index"
                @click="hisAndHotSearch(item)"
                >{{ item }}</view
              >
            </view>
          </view>

          <view v-if="hotSearchList.length">
            <view class="d-flex j-sb a-center mb-32 font-32 font-wei l-h-44">热门推荐</view>
            <view class="d-flex flex-wrap a-center">
              <view
                class="d-flex a-center b-rad-26 mr-20 mb-24 ptb-02-plr-24"
                :class="item.is_hot ? 'bg-fce0e0' : 'bg-f5f5f5'"
                v-for="(item, index) in hotSearchList"
                :key="index"
                @click="hisAndHotSearch(item.keyword)"
              >
                <image
                  v-if="item.is_hot"
                  class="w-28 h-28 mr-04"
                  src="https://images.vinehoo.com/vinehoomini/v3/comm/fire.png"
                  mode="widthFix"
                />
                <view
                  class="font-24 l-h-40 text-hidden-1"
                  :class="item.is_hot ? 'w-max-590 text-e80404' : 'w-max-660 text-6'"
                  >{{ item.keyword }}</view
                >
              </view>
            </view>
          </view>
        </view>
      </view>

      <!--(选项栏 + 列表) 商品、产区、酒款... -->
      <view v-if="canSearch == 1" class="fade-in p-rela">
        <!--(选项栏) 商品、产区、酒款... -->
        <view class="gsearch__tabs p-stic z-980" style="top: 46px">
          <u-tabs
            :list="tabList"
            :current="currentTabIndex"
            :height="92"
            :font-size="28"
            inactive-color="#333"
            active-color="#E80404"
            :gutter="40"
            :bar-width="36"
            :bar-height="8"
            :bar-style="{ background: '#E80404' }"
            @change="onTabChange"
          ></u-tabs>
        </view>

        <!--列表 商品、产区、酒款...有数据 -->
        <view v-if="globalSearchList.length" id="search-list-con" class="pb-20">
          <view v-if="fromPlate === 'index' || fromPlate === 'commodity'" class="pt-20">
            <!-- 商品 -->
            <view v-if="currentTabType === 'goods'" class="ml-24 mr-24">
              <view
                class="bg-ffffff b-rad-10 mb-20 p-24 d-flex"
                v-for="(item, index) in globalSearchList"
                :key="index"
                @click="jump.navigateTo(`${routeTable.pgGoodsDetail}?id=${item.id}&from_search=${keyword}`)"
              >
                <view class="w-290 b-rad-06 o-hid">
                  <vh-image :loading-type="2" :src="item.banner_img" :height="180" />
                </view>

                <view class="flex-1 d-flex flex-column j-sb ml-18">
                  <view class="">
                    <view class="text-hidden-2">
                      <vh-channel-title-icon
                        :channel="item.periods_type"
                        :border-radius="4"
                        padding="2rpx 8rpx"
                        :font-size="20"
                        :font-bold="false"
                      />
                      <text class="ml-04 font-24 font-wei text-3 l-h-34">{{ item.title }}</text>
                    </view>
                    <view class="mt-08 font-22 text-9 l-h-32 text-hidden-1">{{ item.brief }}</view>
                  </view>

                  <view class="d-flex j-sb a-center mt-28">
                    <view
                      v-if="item.is_hidden_price == 1 || [3, 4].includes(item.onsale_status)"
                      class="font-28 font-wei text-e80404 l-h-26"
                      >价格保密</view
                    >
                    <text v-else class="font-28 text-e80404 l-h-26"
                      ><text class="font-18">¥</text>{{ item.price }}</text
                    >

                    <!-- 2022-07-19 秒发列表只显示已售 需求方：杨文科 -->
                    <view v-if="!item.is_deposit" class="">
                      <text v-if="item.periods_type == 1" class="font-22 text-9 l-h-32"
                        >已售<text class="text-e80404">{{ item.purchased + item.vest_purchased }}</text></text
                      >
                      <text v-else class="font-22 text-9 l-h-32"
                        >已售<text class="text-e80404">{{ item.purchased + item.vest_purchased }} </text>/限量<text
                          class="text-e80404"
                          >{{ item.limit_number }}</text
                        >{{ item.quota_rule && item.quota_rule.quota_number == '9999' ? '' : '/限购' }}
                        <text class="text-e80404">{{
                          item.quota_rule && item.quota_rule.quota_number == '9999'
                            ? ''
                            : item.quota_rule && item.quota_rule.quota_number
                        }}</text></text
                      >
                    </view>
                    <!-- <text class="font-22 text-9 l-h-32">已售{{item.purchased + item.vest_purchased}}/限量{{item.limit_number}}/限购{{item.quota_rule.quota_number}}</text> -->
                  </view>
                </view>
              </view>
            </view>

            <!-- 产区 -->
            <!-- <view v-if="currentTab == 1" class="ml-24 mr-24">
							<view class="bg-ffffff b-rad-10 mb-20 p-24 d-flex j-sb a-center" v-for="(item,index) in globalSearchList" :key="index">
								<view class="d-flex">
									<view class="w-100 b-rad-06 o-hid">
										<vh-image :loading-type="2" :src="item.banner_img" :height="100" />
									</view>
									
									<view class="flex-1 ml-16">
										<view class="font-28 font-wei text-3 l-h-40 text-hidden-1">{{item.regions_name_cn}}</view>
										<view class="font-26 text-9 l-h-36 text-hidden-1">{{item.regions_name_en}}</view>
									</view>
								</view>
							</view>
						</view> -->

            <!-- 酒款 -->
            <!-- <view v-if="currentTab == 2" class="ml-24 mr-24">
							<view class="bg-ffffff b-rad-10 mb-20 p-24 d-flex" v-for="(item,index) in globalSearchList" :key="index">
								<view class="w-124 b-rad-06 o-hid">
									<vh-image :loading-type="2" :src="item.master_image" :height="180" />
								</view>
								
								<view class="flex-1 ml-16">
									<view class="font-28 font-wei text-3 l-h-40 o-hid text-hidden-1">{{item.winename}}</view>
									<view class="mt-08 font-26 text-3 l-h-36 o-hid text-hidden-1">{{item.wename}}</view>
									<view class="mt-24 font-26 text-9 l-h-36 o-hid text-hidden-1">{{item.country_name_cn}}·{{item.winery_name_cn}}</view>
								</view>
							</view>
						</view> -->

            <view v-if="currentTabType === 'auction' && !waterfallLoading" class="ptb-00-plr-24">
              <AuctionWGoodsList :list="globalSearchList" :addTime="50" />
            </view>

            <!-- 酒闻 -->
            <view v-if="currentTabType === 'news'" class="ml-24 mr-24">
              <view
                class="bg-ffffff b-rad-10 mb-20 p-24 d-flex"
                v-for="(item, index) in globalSearchList"
                :key="index"
                @click="jump.navigateTo(`/packageD/pages/wine-smell-detail/wine-smell-detail?id=${item.id}`)"
              >
                <view class="w-186 b-rad-06 o-hid">
                  <vh-image :loading-type="2" :src="item.img" :height="186" />
                </view>

                <view class="flex-1 d-flex flex-column j-sb ml-20">
                  <view class="">
                    <view class="font-28 font-wei text-3 l-h-40 o-hid text-hidden-2">{{ item.title }}</view>
                    <view class="mt-16 font-24 text-9 l-h-34 o-hid text-hidden-1">{{ item.abst }}</view>
                  </view>

                  <view class="d-flex j-end a-center">
                    <view class="d-flex a-center">
                      <image
                        class="w-30 h-22"
                        src="https://images.vinehoo.com/vinehoomini/v3/comm/view.png"
                        mode="aspectFill"
                      />
                      <text class="ml-06 font-24 text-9">{{ item.viewnums | numToThousands }}</text>
                    </view>

                    <view class="ml-60 d-flex a-center">
                      <image
                        class="w-22 h-22"
                        src="https://images.vinehoo.com/vinehoomini/v3/comm/comm.png"
                        mode="aspectFill"
                      />
                      <text class="ml-06 font-24 text-9">{{ item.commentnums | numToThousands }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 酒会 -->
            <view v-if="currentTabType === 'wineParty'" class="ml-24 mr-24">
              <view
                class="bg-ffffff b-rad-10 o-hid mb-20"
                v-for="(item, index) in globalSearchList"
                :key="index"
                @click="jump.navigateTo(`/packageD/pages/wine-party-detail/wine-party-detail?id=${item.id}`)"
              >
                <vh-image :src="item.thumb_image" :height="356" />

                <view class="p-24">
                  <view class="d-flex a-center">
                    <!-- <text class="bg-li-1 ptb-02-plr-10 b-rad-04 font-24 text-ffffff w-s-now">官方</text> -->
                    <text class="ml-10 font-28 font-wei text-3 l-h-40 text-hidden-1">{{ item.title }}</text>
                  </view>

                  <view class="d-flex a-center mt-20">
                    <image
                      class="w-30 h-30"
                      src="https://images.vinehoo.com/vinehoomini/v3/comm/add_red.png"
                      mode="aspectFill"
                    />
                    <text class="ml-10 font-24 text-6 l-h-34">{{ item.activity_time }}</text>
                  </view>

                  <view class="d-flex a-center mt-12">
                    <image
                      class="w-30 h-30"
                      src="https://images.vinehoo.com/vinehoomini/v3/comm/tim_red.png"
                      mode="aspectFill"
                    />
                    <text class="ml-10 font-24 text-6 l-h-34 text-hidden-1"
                      >{{ item.province_name }}{{ item.city_name }}{{ item.district_name }}{{ item.address }}</text
                    >
                  </view>

                  <view class="d-flex j-sb a-center mt-20">
                    <view class="">
                      <text class="font-24 text-e80404">¥</text>
                      <text class="ml-06 font-44 font-wei text-e80404">{{ item.money }}</text>
                      <text class="ml-06 font-28 text-9">起</text>
                    </view>

                    <view class="">
                      <text class="font-28 text-6">剩余名额</text>
                      <text class="font-28 font-wei text-e80404">{{ item.last_num }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 葡萄 -->
            <!-- <view v-if="currentTab == 5" class="ml-24 mr-24">
							<view class="bg-ffffff b-rad-10 mb-20 p-24 d-flex j-sb a-center" v-for="(item,index) in globalSearchList" :key="index">
								<view class="d-flex">
									<view class="w-100 b-rad-06 o-hid">
										<vh-image :loading-type="2" :src="item.image" :height="100" />
									</view>
									<view class="flex-1 ml-16">
										<view class="font-28 font-wei text-3 l-h-40 text-hidden-1">{{item.gname_cn}}</view>
										<view class="mt-04 font-26 text-9 l-h-36 text-hidden-1">{{item.gname_en}}</view>
									</view>
								</view>
							</view>
						</view> -->
          </view>

          <view v-if="fromPlate === 'bbs'" class="">
            <!-- 帖子 -->
            <view v-show="currentTabIndex == 0">
              <postItemList v-if="currentTabIndex == 0" :list="globalSearchList" :isFollowList="true"></postItemList>
            </view>

            <!-- 用户 -->
            <view v-show="currentTabIndex == 1" class="ml-24 pt-20 mr-24">
              <view
                class="bg-ffffff b-rad-10 d-flex a-center j-sb mb-20 p-24"
                v-for="(item, index) in globalSearchList"
                :key="index"
              >
                <view class="d-flex" @click="goPersonCenter(item)">
                  <view class="p-rela w-98 h-98">
                    <image class="w-98 h-98 b-rad-p50" :src="item.avatar_image" mode="aspectFill"></image>
                  </view>

                  <view class="ml-20">
                    <view class="d-flex a-center">
                      <text class="font-32 font-wei text-2d2d2d l-h-44">{{ item.nickname }}</text>

                      <view
                        v-if="item.type != 2"
                        style="line-height: 34rpx"
                        class="font-22 text-center b-rad-26 text-ffffff ml-10 w-80 h-34 bg-ff9300"
                        >LV.{{ item.user_level }}</view
                      >
                      <view
                        v-else
                        style="line-height: 34rpx"
                        class="font-22 text-center b-rad-10 text-ffffff ml-10 w-80 h-34 bg-e80404"
                        >官方</view
                      >
                    </view>

                    <view
                      v-if="item.certified_info"
                      class="w-178 h-36 bg-fce0e0 d-flex j-center a-center mt-10 b-rad-22"
                    >
                      <text class="mr-06 font-18 font-wei text-e80404 l-h-26">WSET高级认证</text>
                      <u-icon name="arrow-right" :size="20" color="#E80404"></u-icon>
                    </view>
                    <view v-else class="w-120 h-36 bg-e5e5e5 d-flex j-center a-center mt-10 b-rad-22">
                      <text class="mr-06 font-18 font-wei text-999999 l-h-26">未认证</text>
                    </view>
                  </view>
                </view>
                <view
                  :class="[item.is_follow ? 'text-3 b-s-02-d8d8d8' : 'text-2e7bff bg-e2ebfa']"
                  class="w-100 mt-06 ptb-04-plr-00 font-24 text-center text-9 l-h-34 b-rad-26"
                  @tap="handleFollow(item, index)"
                  >{{ item.is_follow ? '已关注' : '+关注' }}</view
                >
              </view>
            </view>

            <!-- 酒评 -->
            <view v-show="currentTabIndex == 2">
              <postItemList v-if="currentTabIndex == 2" :list="globalSearchList" :isFollowList="true"></postItemList>
            </view>
          </view>

          <view v-if="fromPlate === 'miaofa' && !waterfallLoading" class="pt-20 ptb-00-plr-24">
            <VhWaterfall v-model="globalSearchList" :itemStyle="wfitemStyle"></VhWaterfall>
          </view>
          <view v-if="fromPlate === 'miaofaHair' && !waterfallLoading" class="pt-20 ptb-00-plr-24">
            <VhWaterfall v-model="globalSearchList" :itemStyle="wfitemStyle"></VhWaterfall>
          </view>

          <u-loadmore class="pt-20" :status="reachBottomLoadStatus" />
        </view>

        <!-- 列表无数据 -->
        <view v-else>
          <!-- 暂无数据列表 -->
          <vh-empty
            :padding-top="60"
            :padding-bottom="190"
            :image-src="ossIcon('/empty/emp_goods.png')"
            text="无搜索结果，请试试其他关键词"
            :text-bottom="0"
          />

          <!-- 热门推荐列表 - 非社区搜索时显示 -->
          <view v-if="fromPlate !== 'bbs'" class="bg-f5f5f5">
            <vh-split-line
              :line-width="0"
              :padding-top="52"
              :padding-bottom="32"
              text="猜你搜索"
              :font-bold="true"
              :font-size="36"
              text-color="#333333"
            />
            <vh-goods-recommend-list />
          </view>
        </view>
        <GlobalSearchFeedback
          ref="globalSearchFeedbackRef"
          v-model="globalSearchFeedbackVisible"
          :keywords="keyword"
        ></GlobalSearchFeedback>
      </view>
    </view>

    <!-- 骨架屏 -->
    <vh-skeleton v-else :type="7" bg-color="#FFF" />
  </view>
</template>

<script>
import { mapState } from 'vuex'
import listMixin from '@/common/js/mixins/listMixin'
import {
  MMiaofaSearchType,
  MSecondsWfitemType,
  MSWfitemTypeToIncParamsKey,
  MSWfitemIncRes,
} from '@/common/js/utils/mapperModel'
import { MMiaofaSearchTypeText } from '@/common/js/utils/mapper'
import postItemList from '@/pages/community/postList.vue'
const screenWidth = uni.getSystemInfoSync()?.screenWidth || 0
const $isBigScreen = screenWidth >= 390
console.log('$isBigScreen', screenWidth, $isBigScreen)
const WfitemWidth = 344
const WfitemCustom = {
  $reportDisabled: true,
  $wfitemWidth: WfitemWidth,
  $imgIsResize: true,
  $isBigScreen,
}
export default {
  name: 'global-search',
  mixins: [listMixin],
  components: {
    postItemList,
  },
  data() {
    return {
      wfitemStyle: { width: WfitemWidth, marginRight: 14, marginBottom: 16 },
      loading: true, //加载状态 true = 加载中、false = 结束加载
      type: 0, //类型 0 = 首页、1 = 闪购、2 = 秒发、3 = 社区、4 = 秒发分类
      keyword: '', //搜索文本
      isNewYear: false,
      keywordPlaceHolder: '', //搜索文本占位符
      canSearch: 0, //是否可以点击搜索
      vidList: [
        //短视频列表
        {
          imgUrl: 'https://h5.vinehoo.com/images/nothing/error_img.jpg',
          title: '名家精选大师酒会，带你品味不一样的红酒，感受不一样的艺术新品位 ',
          topic: '#旅行家',
        },
        {
          imgUrl: 'https://h5.vinehoo.com/images/nothing/error_img.jpg',
          title: '女孩与艺术 ',
          topic: '#女孩与艺术',
        },
        {
          imgUrl: 'https://h5.vinehoo.com/images/nothing/error_img.jpg',
          title: '诗与酒，品味与品 ',
          topic: '#红酒',
        },
        {
          imgUrl: 'https://h5.vinehoo.com/images/nothing/error_img.jpg',
          title: '黑白艺术大片果然是高级经典的顶流！',
          topic: '#艺写真',
        },
        {
          imgUrl: 'https://h5.vinehoo.com/images/nothing/error_img.jpg',
          title: '生活不必太过计较 ',
          topic: '#旅行家',
        },
      ],
      hisSearchList: [], //历史搜索列表
      hotSearchList: [], //热门搜索列表
      globalSearchList: [], //搜索到的列表数据
      waterfallLoading: true,
      globalSearchFeedbackVisible: false,
      searchFeedbackTimer: null,
    }
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable']),

    //来自哪个板块 index = 首页、commodity = 秒发闪购、bbs = 社区
    fromPlate() {
      let fromPlate = ''
      switch (this.type) {
        case 0:
          fromPlate = 'index'
          break
        case 1:
          fromPlate = 'commodity'
          break
        case 2:
          fromPlate = 'miaofa'
          break
        case 3:
          fromPlate = 'bbs'
          break
        case 4:
          fromPlate = 'miaofaHair'
          break
      }
      return fromPlate
    },

    // 频道Tabs
    channelTabs() {
      let map = new Map([
        [
          'index',
          [
            { name: '商品', type: 'goods' },
            // { name: '拍卖', type: 'auction' },
            // { name: '产区', type:'region' },
            // { name: '酒款', type:'wine' },
            { name: '酒闻', type: 'news' },
            { name: '酒会', type: 'wineParty' },
            // { name: '葡萄', type:'grape' } ,
          ],
        ], //首页
        ['commodity', [{ name: '商品', type: 'goods' }]], //商品（闪购、秒发...）
        [
          'bbs',
          [
            { name: '帖子', type: 'post' },
            { name: '用户', type: 'user' },
            { name: '酒评', type: 'wineEvaluation' },
          ],
        ], //社区
        ['miaofa', MMiaofaSearchTypeText.map(({ value, text }) => ({ type: value, name: text }))],
        [
          'miaofaHair',
          MMiaofaSearchTypeText.filter(({ value }) => [MMiaofaSearchType.Goods].includes(value)).map(
            ({ value, text }) => ({ type: value, name: text })
          ),
        ],
      ])
      return map
    },

    //历史搜索类型 indexHisSearchList = 首页搜索、flashPurchaseHisSearchList = 闪购历史搜索列表、secondHairHisSearchList = 秒发历史搜索列表、communityHisSearchList = 社区历史搜索
    hisSearchType() {
      let search = ''
      switch (this.type) {
        case 0:
          search = 'indexHisSearchList'
          break
        case 1:
          search = 'flashPurchaseHisSearchList'
          break
        case 2:
        case 4:
          search = 'secondHairHisSearchList'
          break
        case 3:
          search = 'communityHisSearchList'
          break
      }
      return search
    },
    tabList({ fromPlate, channelTabs }) {
      return channelTabs.get(fromPlate)
    },
    currentTabType({ currentTabIndex, tabList }) {
      return tabList[currentTabIndex].type
    },
    customBack({ globalSearchFeedbackVisible }) {
      if (globalSearchFeedbackVisible) {
        return () => {
          globalSearchFeedbackVisible && this.$refs?.globalSearchFeedbackRef?.add()
          if (this.pageLength <= 1) {
            uni.reLaunch({
              url: '/pages/index/index',
            })
          } else {
            uni.navigateBack()
          }
        }
      }
      return null
    },
  },

  watch: {
    canSearch() {
      this.changeBodyClassList()
      if (!this.canSearch) {
        this.globalSearchFeedbackVisible && this.$refs?.globalSearchFeedbackRef?.add()
      }
    },
  },

  onLoad(options) {
    this.type = parseInt(options.type)
    this.secondConfig()
    this.init().finally(() => {
      this.loading = false
    })
  },

  onShow() {
    this.changeBodyClassList()
  },

  methods: {
    async handleFollow(item, index) {
      // 如果是取消关注，先出确认框
      if (item.is_follow) {
        uni.showModal({
          title: '提示',
          content: '确定取消关注该用户吗？',
          success: async (res) => {
            if (res.confirm) {
              await this.doFollow(item, index)
            }
          },
        })
      } else {
        // 如果是关注操作，直接执行
        await this.doFollow(item, index)
      }
    },
    goPersonCenter(item) {
      this.jump.appAndMiniJump(1, `/packageC/pages/my-post/my-post?uid=${item.uid}`, this.$vhFrom)
    },
    async secondConfig() {
      const res = await this.$u.api.secondConfig()
      if (res.data.isopen) {
        this.isNewYear = true
      }
    },
    // 执行关注/取消关注的具体操作
    async doFollow(item, index) {
      try {
        const params = {
          operate_uid: item.uid,
          status: item.is_follow ? 2 : 1,
        }

        const res = await this.$u.api.focusUser(params)
        console.log('关接口返回:', res)

        if (res.error_code == 0) {
          this.$u.toast(item.is_follow ? '取消关注成功' : '关注成功')
          this.globalSearchList[index].is_follow = !this.globalSearchList[index].is_follow
        }
      } catch (error) {
        console.error('关注操作错误:', error)
      }
    },
    // 初始化(获取搜索关键字、获取热门搜索)
    async init() {
      this.getHisSearchList()
      await Promise.all([this.getChannelKeyword(), this.getHotSearchList()])
    },

    // 获取搜索关键字
    async getChannelKeyword() {
      try {
        const params = { type: this.type === 4 ? 2 : this.type }
        let res = await this.$u.api.channelKeyword(params)
        this.keywordPlaceHolder = res.data.keyword
      } catch (e) {}
    },

    // 获取热门搜索列表
    async getHotSearchList() {
      try {
        const params = { type: this.type === 4 ? 2 : this.type }
        let res = await this.$u.api.hotSearch(params)
        this.hotSearchList = res.data.list
      } catch (e) {}
    },

    // 获取历史搜索列表
    getHisSearchList() {
      this.hisSearchList = uni.getStorageSync(this.hisSearchType) || []
    },

    // 保存历史搜索记录
    saveHisSearchRecord() {
      if (this.hisSearchList.indexOf(this.keyword) == -1 && this.$u.trim(this.keyword, 'all') !== '') {
        this.hisSearchList.unshift(this.keyword)
        if (this.hisSearchList.length >= 10) {
          this.hisSearchList.pop()
        }
        uni.setStorageSync(this.hisSearchType, this.hisSearchList)
      }
    },

    // 删除历史记录
    delHisSearchRecord() {
      console.log('----------我是删除按钮')
      this.feedback.showModal({
        content: '确认清空历史搜索记录吗？',
        confirm: () => {
          this.hisSearchList = []
          uni.setStorage({
            key: this.hisSearchType,
            data: this.hisSearchList,
            success: (res) => {
              console.log('success')
            },
          })
        },
      })
    },

    // 历史搜索和热门搜索 keyword = 关键字
    hisAndHotSearch(keyword) {
      this.keyword = keyword
      this.search()
    },

    // 获取全局搜索列表
    async load(query, currentTabIndex) {
      const { page, limit } = query
      if (page === 1) {
        this.searchFeedbackTimer && clearTimeout(this.searchFeedbackTimer)
        this.globalSearchFeedbackVisible = false
      }
      if (['miaofa', 'miaofaHair'].includes(this.fromPlate)) {
        const params = { keywords: this.keyword, type: this.tabList[currentTabIndex].type, page, limit }
        const res = await this.$u.api.secondSearch(params)
        const { list = [] } = res?.data || {}
        const refList = list.map((item) => ({
          type: item.genre || item.type,
          ...item[item.genre || item.type],
          ...WfitemCustom,
        }))
        if (this.tabList[currentTabIndex].type === 0) {
          await this.loadIncrementData(refList, query)
        }
        refList.forEach((item) => {
          if (item.type === MSecondsWfitemType.Goods && !item.left_top_label.length) {
            if (item.marketing_attribute.includes('1')) {
              item.left_top_label = [{ title: '拼团', $clazz: 'text-ffffff bg-ff9127 b-rad-16' }]
            } else {
              const mapper = {
                0: {
                  title: '闪购',
                  $clazz: 'text-ffffff bg-e80404 b-rad-16',
                },
                1: {
                  title: this.isNewYear ? '年货节' : '现货速发',
                  $clazz: 'text-ffffff bg-ff9127 b-rad-16',
                },
                2: {
                  title: '跨境',
                  $clazz: 'text-ffffff bg-734cd2 b-rad-16',
                },
                3: {
                  title: '尾货',
                  $clazz: 'text-ffffff bg-da9840 b-rad-16',
                },
              }
              const mapperItem = mapper[item.periods_type]
              item.left_top_label = mapperItem ? [mapperItem] : []
            }
          }
        })
        this.globalSearchList = page === 1 ? refList : this.globalSearchList.concat(refList)
        this.searchFeedback()
        return res
      }
      const search_type = this.tabList[currentTabIndex].type
      let data = { page, limit, search_type }
      data.channel = this.fromPlate
      data.keywords = this.keyword
      let res = await this.$u.api.globalSearch(data)
      res.data.list.forEach((item) => {
        if (search_type === 'auction') {
          item.$isShowPageviews = true
        }
      })
      page == 1
        ? (this.globalSearchList = res.data.list)
        : (this.globalSearchList = [...this.globalSearchList, ...res.data.list])
      this.searchFeedback()
      return res
    },
    async loadIncrementData(list = [], query) {
      if (!list.length) return
      const group = list.reduce((prev, curr) => {
        if (prev[curr.type]) {
          prev[curr.type].push(curr)
          return prev
        } else {
          prev[curr.type] = [curr]
          return prev
        }
      }, {})
      const params = {}
      Object.keys(group).forEach((groupKey) => {
        const key = MSWfitemTypeToIncParamsKey[groupKey]
        if (key) params[key] = group[groupKey].map(({ id }) => id).join()
      })
      params.periods_params =
        group[MSecondsWfitemType.Goods]?.map(({ id, periods_type, price }) => ({
          period: id,
          period_type: periods_type,
          price: +price,
        })) || []
      const incrementRes = await this.$u.api.getSecondRecommendIncrement(params)
      const incrementData = incrementRes.data
      Object.keys(incrementData).forEach((key) => {
        const incrementDataList = incrementData[key]
        const obj = MSWfitemIncRes[key]
        if (incrementDataList && obj) {
          incrementDataList.forEach((data) => {
            const findItem = list.find((item) => item.id === data[obj.id])
            if (findItem) {
              const target = {}
              Object.entries(obj)
                .filter((arr) => !['type', 'id'].includes(arr[0]))
                .forEach((arr) => {
                  target[arr[1]] = data[arr[0]]
                })
              Object.assign(findItem, target)
            }
          })
        }
      })
      const goodsDataList = incrementData.goods_data
      if (goodsDataList.length) {
        group[MSecondsWfitemType.Goods]?.forEach((goods) => {
          const findItem = goodsDataList.find((item) => item.period === goods.id)
          if (findItem) {
            const {
              label: { left_top_label = [], product_label = [], top_label = [] },
              price,
            } = findItem
            Object.assign(goods, { left_top_label, product_label, top_label, price })
          }
        })
      }
    },

    // 改变输入值
    changeInputValue(e) {
      if (this.keyword) return
      this.canSearch = 0
    },

    // 清空搜索内容
    clearSearchText() {
      this.keyword = ''
      this.canSearch = 0
    },

    // 搜索
    async search() {
      // if(this.$u.trim(this.keyword, 'all') == '') return this.feedback.toast({title:'请输入关键字！'})
      if (this.$u.trim(this.keyword, 'all') == '') this.keyword = this.keywordPlaceHolder
      if (!this.keyword) return
      this.saveHisSearchRecord()
      this.waterfallLoading = true
      const preReachBottomLoadStatus = this.reachBottomLoadStatus
      this.reachBottomLoadStatus = 'loading'
      this.reload()
        .then(() => {
          this.canSearch = 1
          this.onChangeToScroll()
        })
        .catch(() => {
          this.reachBottomLoadStatus = preReachBottomLoadStatus
        })
        .finally(() => {
          this.waterfallLoading = false
        })
    },
    changeBodyClassList() {
      // this.$nextTick(() => {
      // 	const classList = document?.body?.classList
      // 	if (classList) classList[this.canSearch ? 'add' : 'remove']('bg-f5f5f5')
      // })
    },
    onTabChange(index) {
      this.waterfallLoading = true
      this.changeTabIndex(index).finally(() => {
        this.waterfallLoading = false
      })
    },
    searchFeedback() {
      if (this.globalSearchFeedbackVisible || !this.globalSearchList.length) return
      this.searchFeedbackTimer && clearTimeout(this.searchFeedbackTimer)
      this.searchFeedbackTimer = setTimeout(() => {
        const params = { user_unique_code: uni.getStorageSync('uniqueId') }
        this.$u.api.getSearchFeedbackCount(params).then((res) => {
          const { is_pop_up = false } = res?.data || {}
          this.globalSearchFeedbackVisible = is_pop_up
        })
      }, 2000)
    },
  },

  onReachBottom() {
    this.reachBottomLoad()
  },
}
</script>

<style lang="scss" scoped>
.gsearch {
  &__tabs {
    ::v-deep {
      .u-tab {
        &-item {
          font-weight: 600 !important;
          vertical-align: top;
        }

        &-bar {
          bottom: auto;
        }
      }
    }
  }
}
</style>
