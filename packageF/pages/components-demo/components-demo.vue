<template>
	<view class="mt-120">
		<OrderConfirmDepositPromptMask :show="showMask"/>
		<!-- <NewPeopleIndexMask :show="showMask" @close="showMask=false"/> -->
		<!-- <NewPeopleGift :list="newPeopleGoodsList" :info="newPeopleCouponPackage"/> -->
		<!-- <NewPeopleFloatingFrame :tabbarHeight="tabbarHeight"/> -->
		<!-- <NewPeopleFirstOrderCouponMask :show="showMask" @close="showMask = false"/> -->
		<!-- <SecWaterFallProGoods /> -->
		<!-- <SecWaterFallAdGuessLike /> -->
		<!-- <SecWaterFallAdInterested /> -->
		<!-- <SecWaterFallContWineComment /> -->
		<!-- <SecWaterFallFirstItem /> -->
		<!-- <SecIconReduce /> -->
		<!-- <SecWaterFallContWineNews /> -->
		<!-- <SecWaterFallProAuctionGoods /> -->
		<!-- <SecWaterFallProWineParty /> -->
		<!-- <SecGoodsDetailCouponPopup v-model="secCouponPopupVisible" /> -->
		<!-- <SecGoodsDetailCouponNewPopup v-model="secCouponPopupVisible" /> -->
		<!-- <SecGoodsDetailCouponReceive /> -->
		<!-- <SecGoodsDetailCouponNewReceive /> -->
		<!-- <SecStoreRangeMask @cancel="showMask = false" :show="showMask" /> -->
	</view>
	
</template>

<script>
	import newPeopleMixin from '@/common/js/mixins/newPeopleMixin'
	export default {
		mixins: [newPeopleMixin],
		data: () => ({
			secCouponPopupVisible: true,
			showMask: true,
			
		}),
		
		methods: {
			
		}
	}
</script>

<style scoped></style>

