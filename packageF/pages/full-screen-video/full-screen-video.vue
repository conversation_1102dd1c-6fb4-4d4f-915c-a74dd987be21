<template>
	<view class="content">
		<!-- 导航栏 -->
		<u-navbar back-icon-color="#FFF" :background="{ background: '#000' }" />
		
		<!-- 全屏视频播放器 -->
		<view class="p-rela bg-333333" :style="[fullVideoContainerStyle]">
			<video class="w-p100 h-p100" :src="videoLink" :autoplay="true" object-fit="contain" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				videoLink:'', //视频链接
			}
		},
		
		computed: {
			// 获取状态栏高度
			statusBarHeight(){
				return this.system.getSysInfo().statusBarHeight
			},
			
			// 获取全屏视频播放容器高度
			fullVideoContainerStyle() {
				return { height: `calc(100vh - ${this.statusBarHeight + 48}px)` }
			},
		},
		
		onLoad(options) {
			console.log(options)
			this.videoLink = options.videoLink
		}
	}
</script>

<style scoped></style>
