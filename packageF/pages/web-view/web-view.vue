<template>
	<view class="content">
		<!-- web-view(小程序嵌套的h5页面) -->
		<web-view :src="paramUrl" />
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	import guid from '@/common/js/fun/guid.js'
	export default {
		name: 'web-view', 
		
		data() {
			return {
				url:'', //原始跳转地址
				paramUrl:'', //携带参数的url
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
		
		onLoad(options) {
			this.spliceUrlParameter(options)
		},
		
		methods: {
			// 拼接url参数 options = 页面参数
			spliceUrlParameter(options) {
				let url = decodeURIComponent(options.url)
				this.url = url //原始url（页面传递过来）
				let loginInfo = uni.getStorageSync('loginInfo') || '' //判断有无用户登录信息
				let uniqueId = uni.getStorageSync('uniqueId') || '' //判断有无用全局唯一id
				let uid = loginInfo.uid || uniqueId || guid() //有uis时传入用户真实uid，没有uid时传入全局唯一id
				
				if( url.includes('?') && url.includes('=') ) { //url参数上面已经包含 ?xxx=xxx
					url = url + `&uid=${uid}&from=4`
				} else {
					url = url + `?uid=${uid}&from=4`
				}
				this.paramUrl = url //处理后的参数url（携带用户id跟from）
			}
		},
		
		// 分享给好友
		onShareAppMessage(res) {
			let path = `${this.routeTable.pFWebView}?url=${encodeURIComponent(this.url)}`
			console.log(path)
			return {
			  title: '酒云网 与百万发烧友一起淘酒',
			  path,
			}
		},
		
		// 分享到朋友圈
		onShareTimeline(res) {
			let path = `${this.routeTable.pFWebView}?url=${encodeURIComponent(this.url)}`
			console.log(path)
			return {
			  title: '酒云网 与百万发烧友一起淘酒',
			  path,
			}
		},
		
	}
</script>

<style scoped></style>
