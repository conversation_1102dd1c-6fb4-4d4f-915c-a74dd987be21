<template>
	<view class="content">
		<!-- 无网络 -->
		<view class="d-flex flex-column j-center a-center mt-p40">
			<view class="p-rela w-440 h-360">
				<image class="w-p100 h-p100" src="../../../static/emp_net.png" mode="aspectFill"></image>
				<view class="p-abso bottom-0 w-p100 d-flex j-center font-28 text-9">哎呀，网络出错了</view>
			</view>
			
			<view class="mt-44">
				<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
				:custom-style="{width:'268rpx', height:'64rpx', fontSize:'28rpx', color:'#999', backgroundColor: '#EEE', border:'none'}" @click="refresh">刷新</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name:'no-network',
		
		data(){
			return {
				
			}
		},
		
		onLoad() {
			let pages = getCurrentPages() //当前页面栈
			let prevPage = pages[pages.length - 2] //上一页面
			console.log(pages)
			console.log(pages.length)
			console.log(prevPage)
		},
		
		methods: {
			// 刷新
			refresh() {
				uni.getNetworkType({
					success: res => {
						if(res.networkType == 'none'){
							return this.feedback.toast({ title:'请打开网络连接' })
						}
						this.jumpOperation()
					}
				})
			},
			
			// 跳转操作
			jumpOperation() {
				let pages = getCurrentPages() //当前页面栈
				let prevPage = pages[pages.length - 2] //上一页面
				let fullPath = prevPage.$page.fullPath //页面完整路径
				if (fullPath.includes('/pages/index/index') || 
				fullPath.includes('/pages/flash-purchase/flash-purchase') || 
				fullPath.includes('/pages/community/community') || 
				fullPath.includes('/pages/miaofa/miaofa') || 
				fullPath.includes('/pages/mine/mine') || pages.length === 2){ // 判断页面为tabbar页面、或者页面被分享出去当成首页进入
				    console.log('reLaunch')
					this.jump.reLaunch(fullPath.replace('/pages','..'))
				}else {
					console.log('navigateBack')
					this.jump.navigateBack()
				}
			}
		}
	}
</script>

<style scoped></style>
