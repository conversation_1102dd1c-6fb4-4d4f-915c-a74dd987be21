<template>
	<view class="content">
		<!-- 导航栏 -->
		<view class="">
			<view v-if="!from" class="">
				<vh-navbar title="大转盘" title-size="36" :title-bold="true" title-color="#333">
					<!-- <view class="h-p100 d-flex a-center ml-24" @click="goBack()">
						<u-icon name="nav-back" color="#333" :size="44" />
					</view> -->
				</vh-navbar>
			</view>
			<view v-else class="">
				<view class="p-fixed z-980 top-0 w-p100 bg-ffffff">
					<view :style="{ height: appStatusBarHeight + 'px'}" />
					<view class="p-rela h-px-48 d-flex j-center a-center">
						<view class="p-abso left-24 h-p100 d-flex a-center" @click="goBack()">
							<u-icon name="nav-back" color="#333" :size="44" />
						</view>
						<view class="font-36 font-wei text-3">大转盘</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in"
			:style="{ paddingTop: from == '' ? 0 : parseInt(appStatusBarHeight) + 48 + 'px' }">
			<!-- banner -->
			<view class="p-abso top-0 w-p100 h-1830">
				<image class="w-p100 h-p100" src="https://images.vinehoo.com/vinehoomini/v3/large_turntable/rab_bg.jpg"
					mode="aspectFill" />
			</view>
			<view
				class="p-abso top-120 right-0 bg-000-000-000-030 w-86 h-38 b-tl-bl-rad-200 font-24 text-ffffff text-center"
				@click="newcomerShare">
				分享
			</view>
			<!-- 标题 -->
			<view class="p-rela z-02 d-flex j-center mt-84">
				<image class="w-442 h-210" src="https://images.vinehoo.com/vinehoomini/v3/large_turntable/new_title.png"
					mode="aspectFill" />
			</view>


			<!-- 抽奖板块 -->
			<view class="luck-draw-bg p-rela z-02 w-750 h-854 mt-n-80">
				<!-- 抽奖转盘 -->
				<view class="p-abso bottom-222 w-p100 d-flex j-center">
					<view class="luck-draw p-rela z-04 d-flex j-center a-center w-510 h-462">
						<!-- 抽奖宫格 -->
						<view class="luck-draw-item p-abso w-170 h-152 d-flex flex-column j-center a-center"
							:class="currentIndex == index + 1 ? 'sel-luck-draw-item' : ''"
							v-for="(item, index) in luckyDrawList" :key="index">
							<image class="w-44 h-44" :src="item.image" mode="aspectFill" />
							<text class="mt-06 font-20 text-d2773f">{{item.name}}</text>
							<text v-if="item.sub_name" class="font-20 text-d2773f">{{item.sub_name}}</text>
						</view>

						<!-- 抽奖按钮 -->
						<!-- $u.throttle(btnAClick, 500) -->
						<!-- luckDraw -->
						<view class="w-170 h-152" :class="luckyDrawing ? 'drawing-btn fil-gray100-opa100' : 'draw-btn'"
							@click="$u.throttle(luckDraw, 3000)" />
					</view>
				</view>

				<!-- 剩余抽奖记录 -->
				<view class="p-abso bottom-140 w-p100 d-flex j-center">
					<view class="d-flex j-center a-center w-300 h-50">
						<text class="font-22 text-ffffff">今日免费抽奖次数：</text>
						<text class="font-24 font-wei text-ffd23e">{{luckyDrawInfo.usable_count || 0}}</text>
					</view>
				</view>
			</view>

			<!-- 按钮 -->
			<view class="p-rela z-02 d-flex j-sb a-center mt-n-70 ptb-00-plr-42">
				<!-- {{format.numberFormat(userInfo.rabbit)}} -->
				<!-- luckyDrawInfo.rabbit -->
				<view class="btn-bg w-312 h-102 pt-20 text-center font-30 font-wei text-ffffff">当前兔头：{{getRabbitNum}}
				</view>
				<view class="btn-bg w-312 h-102 pt-20 text-center font-30 font-wei text-ffffff"
					@click="jumpPriceRecord">中奖记录（{{luckyDrawInfo.share_draw_count || 0}}）</view>
			</view>

			<!-- 说明 -->
			<view class="tips-bg p-rela z-02 w-750 h-360 d-flex j-center mt-44">
				<view class="w-630 h-316 d-flex flex-column j-center mt-44 pl-36">
					<view class="d-flex">
						<view class="w-08 h-08 bg-b44b33 b-rad-p50 mt-10" />
						<view class="w-max-552 ml-10 font-24 text-6 l-h-34">每日首次抽奖免费，之后10兔头即可参与抽奖1次。</view>
					</view>
					<view class="d-flex mt-10">
						<view class="w-08 h-08 bg-b44b33 b-rad-p50 mt-10" />
						<view class="w-max-552 ml-10 font-24 text-6 l-h-34">分享抽奖链接即可增加一次抽奖机会，每天最多可分享5次</view>
					</view>
					<view v-if="from === '2'" class="d-flex">
						<view class="w-08 h-08 bg-b44b33 b-rad-p50 mt-10" />
						<view class="w-max-552 ml-10 font-24 text-6 l-h-34">在此活动中Apple不是赞助者，也没有以任何形式参与活动。</view>
					</view>
					<!-- <view class="d-flex mt-10">
						<view class="w-08 h-08 bg-b44b33 b-rad-p50 mt-10" />
						<view class="w-max-552 ml-10 font-24 text-6 l-h-34">本活动不限时间，不限次数，通过酒云网每日任务赚取兔头，即可参与抽奖！奖品包括兔头、优惠券、酒云网联名上官红葡萄酒1瓶、飞天茅台1瓶等。</view>
					</view> -->
				</view>
			</view>

			<!-- 最终解释权 -->
			<view class="p-rela z-02 mt-48 text-center font-24 text-ffd6d3">本活动最终解释权归酒云网所有</view>

			<!-- 弹框 -->
			<view class="">
				<!-- 抽奖成功弹框 -->
				<u-modal v-model="showDrawSuccMod" :show-title="false" content="" :width="490" show-cancel-button
					:show-confirm-button="winPrize.type !== 0 ? true : false" cancel-text="知道了"
					:cancel-style="{fontSize:'28rpx', color:'#999'}" @cancel="knowIt()"
					:confirm-text="winPrize.type === 1 ? '前往兔头商店' : '去使用'"
					:confirm-style="{fontSize:'28rpx', color:'#E80404'}" @confirm="jumpWinPrize()">
					<view class="pt-86 pb-64">
						<view class="d-flex j-center a-center">
							<image class="w-264 h-184" src="https://images.vinehoo.com/vinehoomini/v3/comm/succ_red.png"
								mode="aspectFill" />
						</view>

						<view class="d-flex flex-column j-center a-center mt-30 l-h-44">
							<view class="font-28 text-3">中奖提示</view>
							<view class="pl-24 pr-24 text-center font-28 text-3">{{winPrize.msg}}</view>
						</view>
					</view>
				</u-modal>
			</view>
		</view>

		<!-- 骨架屏 -->
		<view v-else class="fade-in">
			<vh-skeleton :type="10000" loading-mode="flower" bg-color="#f5f5f5" />
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'

	export default {
		name: 'large-turntable',

		data() {
			return {
				// 页面数据
				from: '', //从哪个端进入 1 = 安卓、2 = ios"
				loading: true, //加载状态 true = 加载中、false = 结束加载
				appStatusBarHeight: '', //状态栏高度

				// 抽奖
				luckyDrawInfo: {}, //抽奖信息
				luckyDrawList: [], //抽奖列表
				currentIndex: 1, //当前位置 (从1开始)
				priceIndex: 0, //后台返回的中奖位置
				count: 8, // 总共有多少个位置
				timer: 0, // 每次转动定时器
				speed: 200, // 初始转动速度
				times: 0, // 转动次数
				cycle: 60, // 转动基本次数：即至少需要转动多少次再进入抽奖环节
				prize: -1, // 中奖位置
				luckyDrawing: false, //抽奖进行中
				// prizeContent:'', //中奖内容
				winPrize: {}, //根据中奖id查找的中奖信息

				// 弹框
				showDrawSuccMod: false, //是否显示中奖弹框
			}
		},

		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),

			// 获取兔头数量
			getRabbitNum() {
				if (this.luckyDrawInfo.rabbit) return this.format.numberFormat(this.luckyDrawInfo.rabbit)
				return 0
			}
		},


		onLoad(options) {
			this.from = options.from
			this.appStatusBarHeight = options.statusBarHeight
			this.system.setNavigationBarBlack()
			// this.init()
			this.appGoBack()
		},
		onShow() {
			this.init()
		},
		onPullDownRefresh () {
			this.init()
		},
		methods: {
			// 初始化（抽奖信息、获取抽奖列表）
			async init() {
				await this.getLuckyDrawList()
			 	const isLogin = await this.login.isLoginV3(this.from, 0)
				if(isLogin) {
					await  this.getLuckyDrawInfo()
				}
				this.loading = false
				uni.stopPullDownRefresh()
			},

			// 获取抽奖列表
			async getLuckyDrawList() {
				let res = await this.$u.api.getNewRabbitRotaryDraw()
				res.data.list.map((v, i) => {
					if (v.name.includes('指定酒款')) {
						v.sub_name = v.name.split('指定酒款')[1];
						v.name = '指定酒款'
					}
					if (v.name.includes('满300减15')) {
						v.sub_name = v.name.split('满300减15')[1];
						v.name = '满300减15'
					}
				})
				this.luckyDrawList = res.data.list
			},

			// 获取抽奖信息
			async getLuckyDrawInfo() {
				let res = await this.$u.api.getNewcomerLotteryInfo()
				this.luckyDrawInfo = res.data
				// this.luckyDrawInfo.rabbit = 9
			},

			// app返回（系统原生返回按钮、侧滑返回）
			appGoBack() {
				if (this.comes.isFromApp(this.from)) {
					window.interceptBack = () => {
						if (this.times != 0 || this.luckyDrawing) return this.feedback.toast({
							title: '正在抽奖中，请稍后'
						}) //抽奖中不能重复抽奖
						wineYunJsBridge.openAppPage({
							client_path: {
								"ios_path": "goBack",
								"android_path": "goBack"
							}
						});
					}
				}
			},

			// 返回（返回上一页）
			goBack() {
				if (this.times != 0 || this.luckyDrawing) return this.feedback.toast({
					title: '正在抽奖中，请稍后'
				}) //抽奖中不能重复抽奖
				console.log('-----------------------我是兔头数')
				console.log(this.luckyDrawInfo.rabbit)
				if (this.comes.isFromApp(this.from))
					wineYunJsBridge.openAppPage({
						client_path: {
							"ios_path": "goBack",
							"android_path": "goBack"
						}
					});
				else
					this.jump.navigateBack()
			},

			// 抽奖
			async luckDraw() {
				const isLogin = await this.login.isLoginV3(this.from)
				if(isLogin){
					if (this.luckyDrawInfo.rabbit < 10 && this.luckyDrawInfo.usable_count == 0) return this.feedback.toast({
						title: '兔头不足~',
						icon: 'error'
					}) //抽奖中不能重复抽奖
					if (this.times != 0 || this.luckyDrawing) return this.feedback.toast({
						title: '正在抽奖中，请勿重复点击'
					}) //抽奖中不能重复抽奖
					this.luckyDrawing = true
					this.startDraw()
				}
				
			},

			// 开始抽奖
			async startDraw() {
				try {
					let data = {}
					if (this.luckyDrawInfo.usable_count == 0) data.is_rabbit = 1
					let res = await this.$u.api.newRabbitLuckDraw(data) //请求中奖接口
					this.luckyDrawList.forEach((item, index) => {
						if (item.id === res.data.id) this.priceIndex = index + 1
					})

					console.log('----------------------------------------我是中奖信息')
					console.log(res)
					// this.prizeContent = res.data.msg //中奖弹框
					this.winPrize = res.data
					this.startRoll();
					// console.log('----------------------------------------我是中奖位置')
					// console.log(this.priceIndex)
					// this.prize = this.priceIndex; //中奖位置,可由后台返回 
					// this.prizeContent = res.data.msg //中奖弹框
					// if (this.prize > 8) {
					// 	console.log('------------------------------------我的中奖位置大于8')
					// 	this.prize = 8
					// }
					// setTimeout(() => { this.getLuckyDrawInfo() }, 500)
				} catch (e) {
					console.log('--------------我进入了抽奖异常的分支')
					this.luckyDrawing = false

				}
			},

			// 开始转动
			async startRoll() {
				this.times += 1 // 转动次数
				this.oneRoll() // 转动过程调用的每一次转动方法，这里是第一次调用初始化 
				// 如果当前转动次数达到要求 && 目前转到的位置是中奖位置
				if (this.times > this.cycle + 10 && this.prize === this.currentIndex) {
					clearTimeout(this.timer) // 清除转动定时器，停止转动
					this.prize = -1
					this.priceIndex = 0
					this.times = 0
					this.speed = 200
					setTimeout(res => {
						this.showDrawSuccMod = true
					}, 1000)
				} else {
					if (this.times < this.cycle) {
						this.speed -= 10 // 加快转动速度
					} else if (this.times === this.cycle) { //中奖位置
						this.prize = this.priceIndex; //中奖位置,可由后台返回
						if (this.prize > 8) {
							console.log('------------------------------------我的中奖位置大于8')
							this.prize = 8
						}

						// try{
						// 	let data = {}
						// 	if(this.luckyDrawInfo.usable_count == 0) data.is_rabbit = 1
						// 	let res = await this.$u.api.largeTurntableLuckyDraw(data) //请求中奖接口
						// 	this.luckyDrawList.forEach((item, index) => { if(item.id === res.data.id) this.priceIndex = index + 1})
						// 	console.log('----------------------------------------我是中奖信息')
						// 	console.log(res)
						// 	console.log('----------------------------------------我是中奖位置')
						// 	console.log(this.priceIndex)
						// 	this.prize = this.priceIndex; //中奖位置,可由后台返回 
						// 	this.prizeContent = res.data.msg //中奖弹框
						// 	if (this.prize > 8) {
						// 		console.log('------------------------------------我的中奖位置大于8')
						// 		this.prize = 8
						// 	}
						//     // setTimeout(() => { this.getLuckyDrawInfo() }, 500)
						// }catch(e) {
						// 	console.log('--------------我进入了抽奖异常的分支')

						// }

					} else if (this.times > this.cycle + 10 && ((this.prize === 0 && this.currentIndex === 8) || this
							.prize === this.currentIndex + 1)) {
						this.speed += 110
					} else {
						this.speed += 20
					}
					if (this.speed < 40) {
						this.speed = 40
					}
					this.timer = setTimeout(this.startRoll, this.speed)
				}
			},

			// 每一次转动
			oneRoll() {
				let currentIndex = this.currentIndex // 当前转动到哪个位置
				const count = this.count // 总共有多少个位置
				currentIndex += 1
				if (currentIndex > count) {
					currentIndex = 1
				}
				this.currentIndex = currentIndex
			},

			// 跳转中奖记录
			jumpPriceRecord() {
				if (this.times != 0 || this.luckyDrawing) return this.feedback.toast({
					title: '正在抽奖中，请稍后'
				}) //抽奖中不能重复抽奖
				this.jump.appAndMiniJump(2, `${this.routeTable.pGLargeTurntablePrizeRecord}`, this.from)
			},
			//跳转中奖后对应的奖品页面
			async jumpWinPrize() {
				this.luckyDrawing = false
				this.getLuckyDrawInfo()
				const {
					type,
					prize = ""
				} = this.winPrize
				switch (type) {
					case 1:
						this.jump.appAndMiniJump(0, `${this.routeTable.pBRabbitHeadShop}`, this.from)
						break
					case 2:
						let res = await this.$u.api.couponDetail({
							coupon_id: prize.split(',')[0]
						})
						const {
							coupon_type, relation_id
						} = res.data
						// 1007指定商品立减券,1008指定商品满减券
						if ([1007, 1008].includes(coupon_type)) {
							this.jump.appAndMiniJump(1, `${this.routeTable.pgGoodsDetail}?id=${relation_id}`, this
								.from)
						} else {
							this.jump.appAndMiniJump(0, this.routeTable.pECouponList, this.from)
						}
						break
				}
			},
			// 知道了
			knowIt() {
				this.luckyDrawing = false,
					this.getLuckyDrawInfo()
			},
			//新人分享增加抽奖次数
			async newcomerShare() {
				this.feedback.toast({
					title: '请点击右上角分享'
				})
				let res = await this.$u.api.newcomerShare()
			}
		}
	}
</script>

<style scoped>
	.luck-draw-bg {
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/draw_box.png);
		background-size: cover;
	}

	/* 九宫格抽奖滚动板块定位(八块) */
	.luck-draw .luck-draw-item {
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/usel.png);
		background-size: cover;
	}

	.luck-draw .sel-luck-draw-item {
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/sel.png);
		background-size: cover;
	}

	.luck-draw .luck-draw-item:nth-child(1) {
		left: 0;
		top: 0;
	}

	.luck-draw .luck-draw-item:nth-child(2) {
		left: 170rpx;
		top: 0;
	}

	.luck-draw .luck-draw-item:nth-child(3) {
		right: 0;
		top: 0;
	}

	.luck-draw .luck-draw-item:nth-child(4) {
		right: 0;
		top: 154rpx;
	}

	.luck-draw .luck-draw-item:nth-child(5) {
		right: 0;
		bottom: 0;
	}

	.luck-draw .luck-draw-item:nth-child(6) {
		right: 170rpx;
		bottom: 0;
	}

	.luck-draw .luck-draw-item:nth-child(7) {
		left: 0;
		bottom: 0;
	}

	.luck-draw .luck-draw-item:nth-child(8) {
		left: 0;
		top: 154rpx;
	}

	.draw-btn {
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/draw1.png);
		background-size: cover;
	}

	.drawing-btn {
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/draw.png);
		background-size: cover;
	}


	.btn-bg {
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/rab_but.png);
		background-size: cover;
	}

	.tips-bg {
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/rab_bg.png);
		background-size: cover;
	}
</style>