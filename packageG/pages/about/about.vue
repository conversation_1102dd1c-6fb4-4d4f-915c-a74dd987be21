<template>
  <view style="min-height: 100vh; padding-bottom: 168rpx;" class="bg-f5f5f5">
    <vh-navbar title="关于酒云网" height="46" :customBack="customBack">
      <image
        v-if="$vhFrom!='next'"
        slot="right"
        :src="ossIcon('/about/share_44.png')"
        class="p-24 w-44 h-44"
        @click="onShare"
      />
    </vh-navbar>
    <view v-if="$vhFrom != 'next'" class="flex-sb-c flex-column mtb-00-mlr-auto mt-90 ptb-30-plr-00 w-634 h-650 b-rad-20 bg-eeeeee">
      <view class="flex-c-c flex-column">
        <image :src="ossIcon('/about/logo_302_94.png')" class="w-302 h-94" />
        <view class="mt-08 font-24 text-9">{{ versionDesc }}</view>
        <image :src="ossIcon('/about/download_314.png')" class="mt-60 w-314 h-314" />
      </view>
      <view class="font-28 text-3">扫描二维码，您的朋友也可下载酒云网客户端</view>
    </view>
    <view class="mt-80 bg-fcfcfc b-rad-10">
      <view v-if="$vhFrom != 'next'" class="flex-sb-c ptb-00-plr-52 h-98" @click="onCheckUpdate">
        <text class="font-wei-500 font-32 text-3">检测更新</text>
        <image :src="ossIcon('/about/arrow_r_12_20.png')" class="w-12 h-20" />
      </view>
      <view class="mtb-00-mlr-auto w-654 h-02 bg-eeeeee"></view>
      <view class="flex-sb-c ptb-00-plr-52 h-98" @click="onCheckPrivacy">
        <text class="font-wei-500 font-32 text-3">隐私政策</text>
        <image :src="ossIcon('/about/arrow_r_12_20.png')" class="w-12 h-20" />
      </view>
    </view>
    <view class="p-fixed left-0 bottom-0 flex-c-c flex-column w-p100 h-168 text-9 text-center">
      <view class="font-28" @click="copy.appCopy(icpNum)">ICP备案号: {{ icpNum }}</view>
      <view class="font-28">Copyright@2004-{{ new Date().getFullYear() }}</view>
      <view class="font-26">酒云网版权所有</view>
    </view>
  </view>
</template>

<script>
export default {
  data: () => ({
    icpNum: '渝ICP备2021013074号-11A'
  }),
  computed: {
    customBack () {
      if (this.$app) {
        return () => {
          wineYunJsBridge.openAppPage({
            client_path: { "ios_path":"goBack", "android_path":"goBack" }
          })
        }
      }
      return null
    },
    versionDesc ({ $android, $ios, $app, $vhVersion, $harmonyos }) {
      if ($app) {
        if ($android) return `For Android V${$vhVersion}`
        if ($ios) return `For IOS V${$vhVersion}`
        if($harmonyos) return `For HarmonyOS V${$vhVersion}`
      }
      return `For Android V11.3.4 build9847`
    }
  },
  methods: {
    onShare () {
      this.jump.appShare({ dataType: 1, path: '/agreement/downLoadApp' })
    },
    onCheckUpdate () {
      this.jump.checkAppUpdate(this.$vhFrom)
    },
    onCheckPrivacy () {
      this.jump.appAndMiniJump(2, '/agreement/PrivacyPolicy', this.$vhFrom)
    }
  }
}
</script>