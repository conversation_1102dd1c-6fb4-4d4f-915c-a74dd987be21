<template>
	<view class="content">
		<image class="w-p100" :src="ossIcon(`/auction_share/bg.jpg`)" mode="widthFix"/>
		<button class="share" @click="feedback.toast({ title: isWxEnv ? '请点击右上角...分享' : '请在微信浏览器中打开' })">分享</button>
		<button class="auction" @click="jump.reLaunch(routeTable.pgIndex)">去拍卖</button>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	export default {
		data: () => ({
			 isWxEnv: false, // 是否为微信环境
		}),
		computed: {
		  ...mapState(['routeTable']),
		},
		onLoad() {
			this.init()
		},
		methods: {
			// 初始化
			init() {
				// console.log('this.param.isMomentsParam()', this.param.isMomentsParam())
				// console.log('window.location.href', window.location.href)
				// console.log("location.href.split('#')[0]", location.href.split('#')[0])
				const ua = window.navigator.userAgent.toLowerCase()
				this.isWxEnv = /micromessenger/.test(ua)
				if (this.isWxEnv) {
				  const friends = {
					  title: '春日微醺 专场拍卖',
					  desc: '酒云网春日快拍活动浪漫邀约！快来酒云拍卖将春日美景填满酒杯！',
				  }
				  const moments = {
					  title: '酒云网春日美酒专场拍卖！微醺美酒等你捡漏~',
					  desc: '',
				  }
				  const link = {
					  link: window.location.href,
					  imgUrl: this.ossIcon(`/auction_share/share.jpg`)
				  }
				  this.share.h5ShareWeixin({ ...friends, ...link }, { ...moments, ...link })
				}
			}
		},
	}
</script>

<style lang="scss" scoped>
	.share, .auction {
		position: fixed;
		top: 40rpx;
		right: -24rpx;
		@include size(150rpx, 52rpx);
		@include flex-row(flex-start);
		background-color: rgba(0, 0, 0, .5);
		border-radius: 25rpx;
		 @include font(500, 24rpx, #fff);
	}
	.share {
		padding-left: 40rpx;
	}
	
	.auction {
		top: 120rpx;
	}
</style>