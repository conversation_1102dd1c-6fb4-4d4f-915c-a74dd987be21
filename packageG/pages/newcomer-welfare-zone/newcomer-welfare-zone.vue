<template>
	<view class="newcomer">
		<u-navbar back-icon-color="#FFF" :background="{ background: 'transparent' }" :is-fixed="false" />

		<view>
			<view class="newcomer__title">新人福利专区</view>
			<view class="newcomer__subtitle">- {{ totalPrice }}元超值大礼包 -</view>
			<view class="newcomer__ticket" @click="jumpLoginPage">
				<view class="newcomer__tbg"></view>
				<view class="newcomer__tcontent">
					<scroll-view :scroll-x="true" class="newcomer__tcview">
						<view v-for="(item, index) in couponList" :key="index" class="newcomer__tcitem">
							<view v-if="item" class="newcomer__tcicontent">
								<text class="newcomer__tciprice"><text>¥</text>{{ item.coupon_face_value }}</text>
								<text class="newcomer__tcitext">无门槛优惠券</text>
							</view>
						</view>
					</scroll-view>
					<view v-if="received" class="newcomer__tctext" :class="clazz">
						<template v-if="countdownStr">距失效<text>{{ countdownStr }}</text></template>
						<template v-else>已失效</template>
					</view>
					<view v-else class="newcomer__tctext" :class="clazz">新人专享超值礼包，每人限领一次</view>
				</view>
				<image :src="receiveIcon" class="newcomer__icon" :class="clazz" />
			</view>
		</view>

		<view v-show="showNotice" class="newcomer__notice">
			<template v-if="received">
				已领取<text>{{ totalPrice }}元</text>新人礼包，
				<template v-if="countdownStr">距失效<text>{{ countdownStr }}</text></template>
				<template v-else>已失效</template>
			</template>
			<template v-else>
				您有<text>{{ totalPrice }}元</text>新人礼包，点击领取 >>
			</template>
		</view>

		<newcomer-goods-list :list="firstList" :from="from" />

		<newcomer-goods-list :type="2" :list="secondList" :from="from" />

		<newcomer-goods-list :type="3" :list="thirdList" :from="from" />
	</view>
</template>

<script>
	export default{
		name:"newcomer-welfare-zone",
		data: () => ({
			received: false,
			targetScrollTop: 0,
			showNotice: false,
			timer: null,
			countdownStr: '',
			countdownSeconds: 0,
			totalPrice: '',
			couponList: [],
			list: [],
			from: ''
		}),
		computed: {
			clazz ({ received }) {
				return {
					'is-receive': received
				}
			},
			receiveIcon ({ received }) {
				return received ? '/static/newcomer/icon_get1.png' : 'https://images.vinehoo.com/vinehoomini/v3/newborn_zone/new_zone_get.png'
			},
			firstList ({ list }) {
				return list.filter(item => item.module === 1)
			},
			secondList ({ list }) {
				return list.filter(item => item.module === 2)
			},
			thirdList ({ list }) {
				return list.filter(item => item.module === 3)
			}
		},
		methods: {
			load () {
				Promise.all([
					this.$u.api.getCouponDetail({ id: 1 }),
					this.$u.api.getNewcomerGoodsList()
				]).then(res => {
					console.log('res', res)
					const { coupon_package_value = '', package_details = [] } = res[0].data
					this.totalPrice = coupon_package_value
					const len = package_details.length
					const fillList = new Array(len < 4 ? 4 - len : 1).fill('')
					this.couponList = [
						...package_details,
						...fillList
					]
					const list = res[1]?.data?.list || []
					list.forEach(item => {
						const { banner_img = '', newcomer_price = '' } = item
						item.custom_banner_img = banner_img[0]
						const newcomer_price_json = JSON.parse(newcomer_price || '{}')
						item.custom_newcomer_price = newcomer_price_json[0].price
					})
					this.list = list
				})
			},
			statrCountdown () {
				this.formatCountdownStr(this.countdownSeconds)
				this.timer = setInterval(() => {
					if (this.countdownSeconds <= 0) {
						this.clearTimer()
						this.countdownStr = ''
					} else {
						this.countdownSeconds--
						this.formatCountdownStr(this.countdownSeconds)
					}
				}, 1000)
			},
			formatCountdownStr (seconds) {
				let [day, hour, minute, second] = [0, 0, 0, 0];
				day = Math.floor(seconds / (60 * 60 * 24))
				hour = Math.floor(seconds / (60 * 60)) - day * 24
				minute = Math.floor(seconds / 60) - hour * 60 - day * 24 * 60
				second = Math.floor(seconds) - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60
				day = day < 10 ? '0' + day : day
				hour = hour < 10 ? '0' + hour : hour
				minute = minute < 10 ? '0' + minute : minute
				second = second < 10 ? '0' + second : second
				this.countdownStr = `${day}天${hour}:${minute}:${second}`
			},
			clearTimer () {
				if(this.timer) {
					clearInterval(this.timer)
					this.timer = null
				}
			},
			jumpLoginPage () {
				if (this.received) return
				if (['1', '2'].includes(this.from)) {
					wineYunJsBridge.openAppPage({
						client_path: { "ios_path":"login", "android_path":"login" }
					})
				} else {
					this.jump.navigateTo('/pages/login/login')
				}
			}
		},
		onReady () {
			const query = uni.createSelectorQuery().in(this)
			query.select('.newcomer__ticket').boundingClientRect(data => {
				const { height, top } = data
				this.targetScrollTop = height + top
			}).exec()
		},
		onLoad (options) {
			if(options.from){
				this.from = options.from
			}
		},
		onShow () {
			const { uid = '', token = '', created_time = '' } = uni.getStorageSync('loginInfo') || {}
			if (uid && token) {
				this.received = true
				const createdTimeSeconds = new Date(created_time).getTime()
				const overdueSeconds = createdTimeSeconds + 7 * 24 * 60 * 60 * 1000
				const currSeconds = Date.now()
				if (currSeconds < overdueSeconds) {
					this.countdownSeconds = Math.ceil((overdueSeconds - currSeconds) / 1000)
					this.statrCountdown()
				}
			}
			this.load()
		},
		onPageScroll (res) {
			this.showNotice = res.scrollTop > this.targetScrollTop
		},
		beforeDestroy () {
			this.clearTimer()
		}
	}
</script>

<style lang="scss" scoped>
	.newcomer {
		padding: 0 0 60rpx 0;
		background-image: url('/static/newcomer/bg.png');
		background-size: contain;

		&__title {
			background: linear-gradient(180deg, #FFFFFF 0%, #FFCD90 100%);
			-webkit-background-clip: text;
			@include font(600, 68rpx, transparent);
			line-height: 96rpx;
			text-align: center;
		}

		&__subtitle {
			@include font(400, 28rpx, #FFD8AB);
			line-height: 40rpx;
			text-align: center;
		}

		&__ticket {
			position: relative;
			margin: 32rpx 0 0 52rpx;
			width: 662rpx;
		}

		&__tbg {
			margin: 0 0 0 46rpx;
			@include size(582rpx, 204rpx);
			background: #E67A36;
			border-radius: 20rpx;
		}

		&__tcontent {
			position: absolute;
			top: 8rpx;
			@include size(628rpx, 186rpx);
		}

		&__tcview {
			white-space: nowrap;
			box-shadow: 0px 2rpx 2rpx 2rpx rgba(184,98,21,0.43);
		}

		&__tcitem {
			display: inline-block;
			vertical-align: top;
			@include size(160rpx, 186rpx);
			background-image: url('/static/newcomer/card.png');
			background-size: 160rpx 186rpx;

			&:first-of-type {
				width: 164rpx;
				background-image: url('/static/newcomer/card1.png');
				background-size: 164rpx 186rpx;

				.newcomer__tcicontent {
					margin-left: 6rpx;
					width: 154rpx;
				}
			}
		}

		&__tcicontent {
			position: relative;
			margin-top: 6rpx;
			@include size(164rpx, 118rpx);
			@include flex-col(flex-start);
		}

		&__tciprice {
			margin-top: 10rpx;
			background: linear-gradient(214deg, #FF6161 0%, #E70000 100%);
			-webkit-background-clip: text;
			@include font(600, 58rpx, transparent);
			line-height: 82rpx;

			text {
				margin-right: 4rpx;
				font-size: 20rpx;
				line-height: 28rpx;
			}
		}

		&__tcitext {
			position: absolute;
			bottom: 10rpx;
			@include font(400, 16rpx, #C87B47);
			line-height: 22px;
		}

		&__tctext {
			position: absolute;
			left: 0;
			bottom: 6rpx;
			@include flex-row;
			@include size(496rpx, 62rpx);
			@include font(400, 24rpx, #C1721E);
			line-height: 34px;

			&.is-receive {
				width: 494rpx;
			}

			text {
				@include font(500, 32rpx, #E80404);
				line-height: 44px;
			}
		}

		&__icon {
			position: absolute;
			right: 0;
			top: 0;
			@include size(166rpx, 232rpx);

			&.is-receive {
				top: -4rpx;
				@include size(168rpx, 236rpx);
			}
		}

		&__notice {
			position: fixed;
			top: 0;
			z-index: 999;
			@include flex-row;
			@include size(100%, 80rpx);
			@include font(400, 28rpx, #A54504);
			line-height: 40rpx;
			background: #FFEBD7;
			box-shadow: 0 2rpx 4rpx 0 rgba(98,0,0,0.18);

			text {
				color: rgba(232, 4, 4, 1);
			}
		}

		.ngoods-list {
			margin-top: 60rpx;
		}
	}
</style>
