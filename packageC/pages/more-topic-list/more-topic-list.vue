<template>
  <view class="content">
    <!-- 导航栏 -->
    <vh-navbar back-icon-color="#333" title="更多话题" title-color="#333" />
    <!-- 搜索框 -->
    <view class="pl-32 pr-32">
      <view class="d-flex j-sb a-center mt-16">
        <view class="w-600 h-80 bg-f7f7f7 d-flex j-sb a-center b-rad-40 pl-26 pr-32">
          <view class="d-flex a-center h-p100">
            <image
              class="w-44 h-44"
              src="https://images.vinehoo.com/vinehoomini/v3/comm/ser_gray.png"
              mode="aspectFill"
            ></image>
            <input
              class="w-428 h-p100 ml-10 font-28 text-3"
              type="text"
              v-model="toplicText"
              placeholder="大家都在搜"
              placeholder-style="font-size:28rpx;color:#999"
              @confirm="handleSearch"
            />
          </view>
          <image
            v-if="toplicText.length"
            class="w-40 h-40"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/del_gray.png"
            mode="aspectFill"
            @click="toplicText = ''"
          ></image>
        </view>
        <view class="font-28 font-wei text-6" @click="handleSearch">搜索</view>
      </view>

      <!-- 话题列表 -->
      <view
        class="pt-32 pb-32 bb-s-01-eeeeee"
        v-for="(item, index) in topicList"
        :key="item.id"
        @click="goToTopicDetail(item.id)"
      >
        <view class="d-flex a-center j-sb">
          <!-- 第1名 -->
          <view class="d-flex a-center">
            <view v-if="index === 0" class="bg-e80404 text-ffffff ptb-01-plr-12 b-rad-06 font-24">{{ index + 1 }}</view>
            <!-- 第2名 -->
            <view v-else-if="index === 1" class="bg-ff9127 text-ffffff ptb-01-plr-12 b-rad-06 font-24">{{
              index + 1
            }}</view>
            <!-- 第3名 -->
            <view v-else-if="index === 2" class="bg-f7ec74 text-ffffff ptb-01-plr-12 b-rad-06 font-24">{{
              index + 1
            }}</view>
            <!-- 其他名次 -->
            <view v-else class="bg-eeeeee text-6 ptb-01-plr-12 b-rad-06 font-24">{{ index + 1 }}</view>

            <view class="ml-20 w-max-488 font-30 font-wei text-3 o-hid text-hidden-1">#{{ item.title }}#</view>
            <view class="ml-20 font-24 text-9">{{ formatReadNum(item.posts_read_num) }}</view>
          </view>

          <view>
            <image
              v-if="item.ishot"
              class="ml-20 w-44 h-26"
              src="https://images.vinehoo.com/vinehoomini/v3/comm/hot.png"
              mode="aspectFill"
            ></image>
            <image
              v-if="item.isrecommend"
              class="ml-20 w-44 h-26"
              src="https://images.vinehoo.com/vinehoomini/v3/comm/new.png"
              mode="aspectFill"
            ></image>
          </view>
        </view>

        <view class="mt-20 font-24 text-9 o-hid text-hidden-1">{{ item.shortdesc }}</view>
      </view>

      <!-- 在话题列表最后添加 -->
      <view class="pt-32 pb-32 text-center font-24 text-9" v-if="topicList.length > 0">
        {{ loading ? '加载中...' : finished ? '没有更多了' : '' }}
      </view>
      <view class="pt-32 pb-32 text-center font-28 text-9" v-if="topicList.length === 0"> 暂无数据 </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'more-toplic-list',
  computed: {
    ...mapState(['routeTable']),
  },
  data() {
    return {
      toplicText: '', // 话题内容
      topicList: [], // 话题列表
      page: 1, // 当前页码
      limit: 200, // 每页条数
      loading: false, // 加载状态
      finished: false, // 是否加载完成
    }
  },
  onLoad() {
    this.getTopicList()
  },
  methods: {
    async getTopicList(type = 'init') {
      if (this.loading) return
      this.loading = true

      try {
        const params = {
          type: 1,
          page: this.page,
          limit: this.limit,
          title: this.toplicText,
        }
        const res = await this.$u.api.getTopicList(params)
        if (res.error_code === 0) {
          if (type === 'init') {
            this.topicList = res.data.list
          } else {
            // 加载更多时追加数据
            this.topicList = [...this.topicList, ...res.data.list]
          }
          // 判断是否加载完成
          if (res.data.list.length < this.limit) {
            this.finished = true
          }
        } else {
          this.$u.toast(res.error_msg || '获取话题列表失败')
        }
      } catch (error) {
        console.error('获取话题列表出错:', error)
      } finally {
        this.loading = false
      }
    },
    goToTopicDetail(id) {
      this.$u.route({
        url: this.routeTable.pCTopicDetail,
        params: { id },
      })
    },
    formatReadNum(num) {
      return num > 10000 ? (num / 10000).toFixed(1) + 'w' : num
    },
    async handleSearch() {
      this.page = 1
      this.finished = false
      this.getTopicList('init')
    },
  },
  onReachBottom() {
    if (!this.loading && !this.finished) {
      this.page++
      this.getTopicList('load')
    }
  },
}
</script>

<style scoped></style>
