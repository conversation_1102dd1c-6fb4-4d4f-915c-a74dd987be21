<template>
  <view class="content">
    <!-- banner -->
    <view class="p-abso z-n-01 w-p100">
      <image
        class="w-p100 h-410"
        src="https://images.vinehoo.com/vinehoomini/v3/comm/post_bg.png"
        mode="widthFix"
      ></image>
    </view>

    <!-- 导航栏 -->
    <view class="">
      <vh-navbar :background="{ background: navBackgroundColor }" back-icon-color="#fff" />
    </view>

    <!-- 用户信息 -->
    <view class="">
      <view class="d-flex j-sb pl-40 pr-40 a-center">
        <view class="d-flex">
          <view class="p-rela w-98 h-98 bg-255-255-255-059 d-flex j-center a-center b-rad-p50">
            <vh-image
              :src="userInfo.avatar_image"
              :loading-type="5"
              :width="90"
              :height="90"
              shape="circle"
              :duration="50"
            />
            <image
              v-if="userInfo.certified_info"
              :src="ossIcon('/comm/certified_24_26.png')"
              class="p-abso bottom-n-02 right-0 w-38 h-40"
            />
          </view>

          <view class="ml-20">
            <view class="">
              <view class="d-flex a-center">
                <view class="w-max-290 font-32 text-ffffff l-h-44 text-hidden">{{
                  userInfo.nickname ? userInfo.nickname : ''
                }}</view>
                <!-- 根据用户类型显示不同标签 -->
                <view
                  v-if="userInfo.type != 2"
                  class="flex-shrink flex-c-c ml-12 w-80 h-32 font-wei-600 font-22 text-ffffff bg-ff9127 b-rad-18"
                  @click.stop="jump.loginNavigateTo(routeTable.pEMyGrade)"
                >
                  <!-- :class="sysPlatformAndroid ? 'top-n-02' : ''" -->
                  <text class="p-rela">LV.{{ userInfo.user_level ? userInfo.user_level : 0 }}</text>
                </view>
                <!-- 官方标签 -->
                <view
                  v-else
                  class="flex-shrink flex-c-c ml-12 w-80 h-32 font-wei-600 font-22 text-ffffff bg-e80404 b-rad-10"
                  >官方</view
                >
              </view>

              <!-- 修改认证信息部分 -->
              <view class="mt-10 d-flex a-center">
                <!-- 如果是自己的账户 -->
                <view
                  v-if="is_self"
                  class="bg-255-255-255-030 pl-18 pr-18 h-36 d-flex j-center a-center b-rad-22"
                  @click.stop="
                    jump.loginNavigateTo(
                      userInfo.certified_info ? routeTable.pECertificationDetail : routeTable.pECertificationApply
                    )
                  "
                >
                  <text class="mr-10 font-18 text-ffffff text-center font-wei-500">{{
                    userInfo.certified_info ? userInfo.certified_info : '申请认证'
                  }}</text>
                  <image
                    v-if="!userInfo.certified_info"
                    :src="ossIcon('/mine/arrow_r_8_16.png')"
                    class="w-08 h-16"
                  ></image>
                </view>

                <!-- 如果是其他用户的账户 -->
                <view v-else class="bg-255-255-255-030 pl-18 pr-18 h-36 d-flex j-center a-center b-rad-22">
                  <text class="font-18 text-ffffff text-center font-wei-500">{{
                    userInfo.certified_info ? userInfo.certified_info : '未认证'
                  }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 发布按钮只在自己的主页显示 -->
        <view class="" v-if="is_self">
          <u-button
            shape="circle"
            :hair-line="false"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{
              width: '100rpx',
              height: '40rpx',
              fontSize: '24rpx',
              color: '#E80404',
              backgroundColor: '#FFFFFF',
              border: 'none',
            }"
            @click="openPublishPopup"
            >发布</u-button
          >
        </view>

        <!-- 在非自己的主页显示关注按钮 -->
        <view v-else>
          <view
            :class="[is_attention ? 'bg-255-255-255-030 text-ffffff ' : 'text-e80404 bg-ffffff']"
            class="w-100 mt-06 ptb-04-plr-00 font-24 text-center text-9 l-h-34 b-rad-26"
            @tap="handleFollow"
            >{{ is_attention ? '已关注' : '+关注' }}</view
          >
        </view>
      </view>

      <view class="bg-d80707 d-flex j-sb ptb-48-plr-80">
        <view class="d-flex flex-column j-center a-center">
          <text class="font-32 font-wei text-ffffff l-h-44">{{ total_diggnums }}</text>
          <text class="mt-06 font-24 text-f4cfd1 l-h-34">点赞</text>
        </view>

        <view class="d-flex flex-column j-center a-center" @click="goFollow">
          <text class="font-32 font-wei text-ffffff l-h-44">{{
            userInfo.attention_nums ? userInfo.attention_nums : 0
          }}</text>
          <text class="mt-06 font-24 text-f4cfd1 l-h-34">关注</text>
        </view>

        <view class="d-flex flex-column j-center a-center" @click="goFans">
          <text class="font-32 font-wei text-ffffff l-h-44">{{ userInfo.fan_nums ? userInfo.fan_nums : 0 }}</text>
          <text class="mt-06 font-24 text-f4cfd1 l-h-34">粉丝</text>
        </view>
      </view>
    </view>

    <!-- tabs切换栏 -->
    <view class="">
      <u-tabs
        ref="uTabs"
        :list="tabsList"
        :current="currentTabs"
        :height="100"
        :font-size="36"
        inactive-color="#999"
        active-color="#E80404"
        :bar-width="36"
        :bar-height="8"
        :bar-style="{ background: 'linear-gradient(214deg, #FF8383 0%, #E70000 100%)' }"
        :is-scroll="false"
        @change="changeTabs"
      ></u-tabs>
    </view>

    <!-- 视频列表 帖子列表 -->
    <view
      class="pt-10 pb-20"
      :class="{ 'bg-f5f5f5': (currentTabs == 0 && postList.length > 0) || (currentTabs == 1 && wineList.length > 0) }"
    >
      <!-- 视频 -->
      <view v-show="currentTabs == 0" class="">
        <!-- 帖子列表组件 -->
        <template v-if="postList.length > 0">
          <postItemList :list="postList" :myPostSelf="is_self" :isFollowList="true"></postItemList>
        </template>
        <vh-empty
          v-else
          bgColor="transparent"
          :padding-top="100"
          :padding-bottom="100"
          :image-src="ossIcon('/empty/emp_goods.png')"
          text="暂无数据"
          :text-bottom="0"
        />
      </view>

      <!-- 酒评列表 -->
      <view v-show="currentTabs == 1" class="">
        <template v-if="wineList.length > 0">
          <postItemList :list="wineList" :myPostSelf="is_self" :isFollowList="true"></postItemList>
        </template>
        <vh-empty
          v-else
          bgColor="transparent"
          :padding-top="100"
          :padding-bottom="100"
          :image-src="ossIcon('/empty/emp_goods.png')"
          text="暂无数据"
          :text-bottom="0"
        />
      </view>
    </view>

    <!-- 添加发布弹窗 -->
    <view class="popup-mask" v-if="showPublishPopup" @click="closePublishPopup"></view>
    <view class="popup-content" :class="{ 'popup-show': showPublishPopup }">
      <view class="popup-inner">
        <view class="popup-item" @tap="handleWineReviewClick">
          <image
            class="popup-image"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/post-wine-talk-banner-x2.png"
            mode="aspectFill"
          />
        </view>
        <view class="popup-item" @tap="handlePostClick">
          <image
            class="popup-image"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/post-list-banner-x2.png"
            mode="aspectFill"
          />
        </view>
      </view>
      <view class="popup-button" @tap="closePublishPopup">
        <image
          class="button-image"
          src="https://images.vinehoo.com/vinehoomini/v3/comm/com-close.png"
          mode="aspectFit"
        />
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import postItemList from '../../../pages/community/postList.vue'

export default {
  name: 'my-post',
  components: {
    postItemList,
  },

  data() {
    return {
      userInfo: {
        rabbit: 0, //当前兔头
        rabbit_available: 0, //未领取兔头
        coupon_totals: 0, //总优惠券
        conpon_expirings: 0, //即将过期的优惠券
        auction_credit_score: 100, //信用值
      }, //用户数据
      navBackgroundColor: 'rgba(224, 20, 31, 0)', //导航栏背景
      tabsList: [
        //tabs列表
        { name: '帖子' },
        { name: '酒评' },
      ],
      currentTabs: 0, //tabs当前选中页
      showPostOpt: false, //帖子操作弹出层
      uid: '', // 添加 uid 字段
      is_self: false, // 添加是否是自己的标识
      is_attention: false, // 添加关注状态
      showPublishPopup: false, // 控制发布弹窗的显示
      postList: [], // 帖子列表
      wineList: [], // 酒评列表
      page: 1,
      loading: false,
      hasMore: true,
      winePage: 1,
      total_diggnums: 0,
      wineLoading: false,
      wineHasMore: true,
    }
  },
  computed: {
    ...mapState(['routeTable', 'ossPrefix']), // 移除 userInfo
  },
  methods: {
    // tabs切换
    changeTabs(index) {
      this.currentTabs = index
      if (index === 1) {
        // 切换到酒评列表
        this.winePage = 1
        this.wineList = []
        this.wineHasMore = true
        this.getWineList()
      } else {
        // 切换到帖子列表
        this.page = 1
        this.postList = []
        this.hasMore = true
        this.getPostList()
      }
    },

    // 是否展示分享弹出层
    showPost() {
      this.showPostOpt = true
    },

    // 添加获取用户信息的方法
    async getUserInfo(uid) {
      try {
        // 调用获取用户信息的接口
        const res = await this.$u.api.getUserInfo({ operate_uid: uid })
        if (res.error_code === 0) {
          this.userInfo = res.data
          this.is_attention = res.data.is_follow // 设置关注状态
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    // 处理关注/取消关注
    async handleFollow() {
      // 如果是取消关注，先出确认框
      if (this.is_attention) {
        uni.showModal({
          title: '提示',
          content: '确定取消关注该用户吗？',
          success: async (res) => {
            if (res.confirm) {
              await this.doFollow()
            }
          },
        })
      } else {
        // 如果是关注操作，直接执行
        await this.doFollow()
      }
    },

    // 执行关注/取消关注的具体操作
    async doFollow() {
      try {
        const params = {
          operate_uid: this.uid,
          status: this.is_attention ? 2 : 1,
        }

        const res = await this.$u.api.focusUser(params)

        if (res.error_code === 0) {
          this.$u.toast(this.is_attention ? '取消关注成功' : '关注成功')

          // 更新关注状态
          this.is_attention = !this.is_attention

          // 发出全局事件，通知其他列表更新
          uni.$emit('updateFollowStatus', {
            uid: this.uid,
            status: this.is_attention ? 1 : 0,
          })
        }
      } catch (error) {
        console.error('关注操作错误:', error)
      }
    },

    // 打开发布弹窗
    openPublishPopup() {
      this.showPublishPopup = true
    },

    // 关闭发布弹窗
    closePublishPopup() {
      this.showPublishPopup = false
    },

    // 处理发帖点击
    handlePostClick() {
      this.jump.loginNavigateTo(this.$routeTable.pCSendPost)
      this.closePublishPopup()
    },

    // 处理写酒评点击
    handleWineReviewClick() {
      this.jump.loginNavigateTo(this.$routeTable.pCWineComment)
      this.closePublishPopup()
    },

    // 获取帖子列表
    async getPostList(isLoadMore = false) {
      if (this.loading || !this.hasMore) return

      try {
        this.loading = true
        // 根据是否是自己的主页调用不同接口
        const api = this.is_self ? 'selfPostsList' : 'postsList'
        const params = {
          page: this.page,
          limit: 10,
        }

        // 如果是查看他人主页,需要传other_uid
        if (!this.is_self) {
          params.other_uid = this.uid
          params.type = 1
        }

        const res = await this.$u.api[api](params)

        if (res.error_code === 0) {
          const list = res.data.list || []
          this.total_diggnums = res.data.total_diggnums
          if (isLoadMore) {
            this.postList = [...this.postList, ...list]
          } else {
            this.postList = list
          }

          this.hasMore = list.length === 10
        }
      } catch (error) {
        console.error('获取帖子列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    goFollow() {
      if (this.is_self) {
        this.jump.appAndMiniJump(1, `/packageE/pages/my-attention-list/my-attention-list`, this.$vhFrom, 0, true)
      }
    },
    goFans() {
      if (this.is_self) {
        this.jump.appAndMiniJump(1, `/packageE/pages/my-fans-list/my-fans-list`, this.$vhFrom, 0, true)
      }
    },
    // 获取酒评列表
    async getWineList(isLoadMore = false) {
      if (this.wineLoading || !this.wineHasMore) return

      try {
        this.wineLoading = true

        // 根据是否是自己的主页调用不同接口和参数
        const api = this.is_self ? 'myWineEvaluationList' : 'wineEvaluationList'
        const params = {
          page: this.winePage,
          limit: 10,
        }

        // 如果不是自己的主页,需要传additional参数
        if (!this.is_self) {
          params.other_uid = this.uid
          params.type = 1
        }

        const res = await this.$u.api[api](params)

        if (res.error_code === 0) {
          let list = res.data.list || []

          // 酒评数据适配
          list = list.map((item) => ({
            ...item,
            type_data: item.type_data || '',
            content: item.wine_evaluation || item.content,
            diggnums: item.diggnums || item.likenums,
            viewnums: item.viewnums || 0,
            is_digg: item.is_digg || 0,
            video_url: '',
            cover_img: '',
          }))

          if (isLoadMore) {
            this.wineList = [...this.wineList, ...list]
          } else {
            this.wineList = list
          }

          this.wineHasMore = list.length === 10
        }
      } catch (error) {
        console.error('获取酒评列表失败:', error)
      } finally {
        this.wineLoading = false
      }
    },

    // 下拉刷新
    onPullDownRefresh() {
      if (this.currentTabs === 1) {
        this.winePage = 1
        this.wineList = []
        this.wineHasMore = true
        this.getWineList().then(() => {
          uni.stopPullDownRefresh()
        })
      } else {
        this.page = 1
        this.postList = []
        this.hasMore = true
        this.getPostList().then(() => {
          uni.stopPullDownRefresh()
        })
      }
    },

    // 触底加载更多
    onReachBottom() {
      if (this.currentTabs === 1) {
        if (this.wineHasMore) {
          this.winePage++
          this.getWineList(true)
        }
      } else {
        if (this.hasMore) {
          this.page++
          this.getPostList(true)
        }
      }
    },
  },

  onPageScroll(res) {
    if (res.scrollTop <= 100) {
      this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop / 100})`
    } else {
      this.navBackgroundColor = `rgba(224, 20, 31, 1)`
    }
  },

  onLoad(options) {
    if (options.uid) {
      this.uid = options.uid

      // 获取当前登录用户的uid
      const loginInfo = uni.getStorageSync('loginInfo')
      const currentUid = loginInfo ? loginInfo.uid : ''

      // 判断是否是自己的主页
      this.is_self = Number(currentUid) === Number(options.uid)

      // 根据 uid 获取用户信息
      this.getUserInfo(options.uid)

      // 初始化加载帖子列表
      this.getPostList()
    }
  },
  onShow() {
    // 初始化加载帖子列表
    this.changeTabs(Number(this.currentTabs))
  },
}
</script>

<style scoped>
.swiper-con {
  height: calc(100vh - 520rpx);
}

/* 弹窗相关样式 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998;
}

.popup-content {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  height: 600rpx;
  z-index: 9999;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

.popup-content.popup-show {
  transform: translateY(0);
}

.popup-inner {
  flex: 1;
  padding: 60rpx 52rpx 0 52rpx;
  overflow-y: auto;
}

.popup-button {
  height: 112rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 100rpx;
}

.button-image {
  width: 112rpx;
  height: 112rpx;
}

.popup-item {
  width: 100%;
  height: 136rpx;
  margin-bottom: 24rpx;
  position: relative;
  z-index: 1;
}

.popup-image {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}
</style>
