<template>
  <view class="content">
    <!-- 导航栏 -->
    <view class="bb-s-01-eeeeee">
      <vh-navbar back-icon-color="#333" title="发帖" title-color="#333" />
    </view>

    <!-- 发帖内容 -->
    <view class="p-24">
      <!-- 发帖文本框 -->
      <view class="">
        <textarea
          :maxlength="-1"
          class="w-654 h-320 bg-f7f7f7 b-rad-08 p-24 font-28 text-3"
          v-model="postCont"
          placeholder="来吧，尽情发挥吧…"
          placeholder-style="color:#999;font-size:28rpx;"
        />
      </view>

      <!-- 更多话题 -->
      <view class="bg-f7f7f7 d-flex flex-wrap a-center b-rad-08 mt-20 p-24">
        <!-- 显示选中的话题 -->
        <text
          v-for="(topic, index) in selectedTopics"
          :key="index"
          class="bg-e2ebfa text-2e7bff b-rad-26 mr-24 mb-24 ptb-08-plr-20 font-24 font-wei l-h-40"
          @click="removeTopic(index)"
          >#{{ topic.title }}#</text
        >

        <view class="w-148 h-50 d-flex j-center a-center b-rad-26 b-s-01-e7e7e7 mb-24">
          <text @click="jump.loginNavigateTo(routeTable.PCTopicSelectMore)" class="font-24 font-wei text-3">#话题</text>
          <u-icon name="arrow-right" :size="20" color="#999"></u-icon>
        </view>
      </view>

      <!-- 添加图片、添加视频 -->
      <view class="mt-20">
        <vh-community-upload
          ref="uUpload"
          :directory="'vinehoo/client/community/'"
          :auto-upload="false"
          :max-count="9"
          :width="222"
          :height="222"
          :border-radius="6"
          :background-color="'#f7f7f7'"
          :icon="{
            src: 'https://images.vinehoo.com/vinehoomini/v3/comm/cam_red.png',
            width: 76,
            height: 60,
          }"
          :text="{
            content: '添加图片',
            style: 'margin-top: 22rpx; font-size: 28rpx; color: #999; line-height: 40rpx;',
          }"
          @on-list-change="onListChange"
          @on-uploaded="onUploaded"
        />
      </view>

      <!-- 修改协议显示部分 -->
      <view class="mt-100">
        <!-- 只在用户未同意过协议时显示 -->
        <view v-if="!hasAgreedBefore" class="j-center bg-ffffff d-flex a-center">
          <image
            class="w-26 h-26"
            :src="
              isRead
                ? 'https://images.vinehoo.com/vinehoomini/v3/comm/cir_sel.png'
                : 'https://images.vinehoo.com/vinehoomini/v3/comm/cir_unsel.png'
            "
            mode="widthFix"
            @click="isRead = !isRead"
          ></image>
          <text @click="isRead = !isRead" class="ml-10 font-24 text-9">阅读并接受</text>
          <text class="font-24 text-2b8cf7" @tap="goToContentRules">《酒云网内容发布规则》</text>
        </view>
        <view class="bg-ffffff cer-btn-con d-flex j-center mt-24 pb-62">
          <view v-if="canPublish">
            <u-button
              shape="circle"
              :ripple="true"
              ripple-bg-color="#ffffff"
              :custom-style="{
                width: '646rpx',
                fontSize: '28rpx',
                color: '#fff',
                backgroundColor: '#E80404',
                border: 'none',
              }"
              @click="handlePublish"
              >发布</u-button
            >
          </view>
          <view v-else class="bg-dddddd w-646 h-80 d-flex j-center a-center font-28 text-ffffff font-wei-500 b-rad-40"
            >发布</view
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'send-post',
  computed: {
    ...mapState(['routeTable']),

    // 判断发布按钮是否可用
    canPublish() {
      // 检查是否同意协议
      const agreementChecked = this.isRead || this.hasAgreedBefore
      // 检查内容是否为空（去除空格和换行）
      const hasContent = this.postCont.trim().replace(/[\r\n]/g, '').length > 0

      return agreementChecked && hasContent
    },
  },
  data() {
    return {
      postCont: '', //发帖内容
      isRead: false, //是否阅读协议
      hasAgreedBefore: false, // 新增：是否之前同意过协议
      selectedTopics: [], // 选中的话题列表
      uploadFileList: [], // 上传文件列表
      uploadImageStr: '', // 上传的图片字符串
      isEdit: false, // 是否是编辑模式
      postId: '', // 帖子ID
    }
  },

  onLoad(options) {
    // 检查用户是否之前同意过协议
    try {
      const hasAgreed = uni.getStorageSync('post_agreement_accepted')
      if (hasAgreed) {
        this.isRead = true
        this.hasAgreedBefore = true
      }

      // 处理编辑模式
      if (options.postData) {
        const postData = JSON.parse(decodeURIComponent(options.postData))
        this.isEdit = true
        this.postId = postData.id
        this.postCont = postData.content

        // 处理话题数据
        if (postData.topics && postData.topics.length) {
          this.selectedTopics = postData.topics.map((topic) => ({
            id: topic.id,
            title: topic.title,
          }))
        }

        // 处理图片数据
        if (postData.type_data) {
          const images = postData.type_data.split(',')
          this.uploadImageStr = postData.type_data
          this.uploadFileList = images.map((url) => ({
            url,
            status: 'success',
            response: url,
          }))
        }
      }
      // 处理传入的话题数据
      else if (options.topicData) {
        const topicData = JSON.parse(decodeURIComponent(options.topicData))
        // 将话题添加到已选话题列表
        this.selectedTopics = [
          {
            id: topicData.id,
            title: topicData.title,
          },
        ]
      }
    } catch (e) {
      console.error('获取协议同意状态或话题数据失败:', e)
    }
  },

  onShow() {
    // 页面显示时检查是否有新选中的话题
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    if (currentPage.$vm.selectedTopics) {
      this.selectedTopics = currentPage.$vm.selectedTopics
    }
  },

  methods: {
    // 上列表发生改变
    onListChange(list) {
      console.log('上传列表发生改变')
      this.uploadFileList = list
    },

    // 所有图片上传成功
    onUploaded(list) {
      console.log('上传所有文件成功')
      this.feedback.toast({ title: '所有图片上传成功~' })
      this.uploadImageStr = list.map(({ response }) => response).join()
      this.createPost()
    },

    // 验证内容是否为空
    validateContent() {
      // 去除首尾空格和所有换行符后检查是否为空
      const trimmedContent = this.postCont.trim().replace(/[\r\n]/g, '')
      if (!trimmedContent) {
        this.feedback.toast({ title: '请输入帖子内容' })
        return false
      }
      return true
    },

    // 发布按钮点击
    handlePublish() {
      // 先验证内容
      if (!this.validateContent()) {
        return
      }

      // 如果用户同意发布，保存状态
      if (this.isRead) {
        try {
          uni.setStorageSync('post_agreement_accepted', true)
        } catch (e) {
          console.error('保存协议同意状态失败:', e)
        }
      }

      // 如果有图片则先上传
      if (this.uploadFileList.length) {
        this.$refs.uUpload.upload()
      } else {
        this.createPost()
      }
    },

    // 创建帖子
    async createPost() {
      // 再次验证内容以防异步操作期间内容被清空）
      if (!this.validateContent()) {
        return
      }

      try {
        let params = {
          // 发送前过滤首尾空格和多余的换行符
          content: this.postCont.trim().replace(/[\r\n]+/g, '\n'),
          topicid: this.selectedTopics.length ? this.selectedTopics.map((topic) => topic.id).join(',') : '',
          is_best: 0,
          istop: 0,
          type: 0,
          type_data: this.uploadImageStr || '',
        }
        let editParams = {}
        // 如果是编辑模式，添加帖子ID
        if (this.isEdit) {
          editParams = {
            topicid: params.topicid,
            type_data: params.type_data,
            content: params.content,
            refuse_pid: this.postId,
          }
        }
        //         {
        //   "record_id": 0,
        //   "topicid": "7",
        //   "type_data": "",
        //   "video_url": "",
        //   "content": "好123133",
        //   "refuse_pid": 37
        // }

        const res = await this.$u.api.createPost(this.isEdit ? editParams : params)

        if (res.error_code === 0) {
          this.feedback.toast({ title: this.isEdit ? '更新成功' : '发布成功' })
          // 清空已选话题和发帖内容
          this.selectedTopics = []
          this.postCont = ''
          this.uploadFileList = []
          this.uploadImageStr = ''

          // 延迟500ms后返回上一页,给用户看到提示
          setTimeout(() => {
            this.jump.jumpPrePage(this.$vhFrom)
          }, 500)
        } else {
          this.feedback.toast({ title: res.error_msg || (this.isEdit ? '更新失败' : '发布失败') })
        }
      } catch (error) {
        console.error(this.isEdit ? '更新帖子失败:' : '发布帖子失败:', error)
        this.feedback.toast({ title: (this.isEdit ? '更新' : '发布') + '失败,请稍后重试' })
      }
    },

    // 跳转到发布规则页面
    goToContentRules() {
      const url = `${this.$store.state.agreementPrefix}/ContentPubRules`
      this.jump.jumpH5Agreement(url, this.$vhFrom)
    },

    // 移除选中的话题
    removeTopic(index) {
      this.selectedTopics.splice(index, 1)
    },
  },
}
</script>

<style scoped></style>
