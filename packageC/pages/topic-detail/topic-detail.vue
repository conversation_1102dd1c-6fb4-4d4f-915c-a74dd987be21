<template>
  <view class="content bg-f5f5f5 pb-20">
    <!-- 导航栏 -->
    <vh-navbar back-icon-color="#333" title="话题详情" title-color="#333" />

    <!-- banner -->
    <view class="p-abso w-p100 h-256">
      <image
        class="w-p100 h-304"
        src="https://images.vinehoo.com/vinehoomini/v3/community/top_det_ban.png"
        mode="aspectFill"
      ></image>
    </view>

    <!-- 话题详情 -->
    <view class="p-rela z-01 pt-40">
      <view class="bg-ffffff b-rad-20 ml-24 mr-24 pt-32 pr-24 pb-24 pl-24 h-min-240">
        <view class="">
          <image
            class="w-40 h-36"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/top_ico_blu.png"
            mode="aspectFill"
          ></image>
          <text class="ml-10 font-40 font-wei text-3">{{ topicDetail.title }}</text>
        </view>

        <view class="mt-20 font-24 text-9 l-h-40">
          No.{{ topicDetail.rank }} 话题榜 ｜ {{ topicDetail.posts_read_num }}阅读
        </view>

        <view class="mt-20 font-24 text-6 l-h-40 o-hid text-hidden-2">
          {{ topicDetail.shortdesc }}
        </view>
      </view>
    </view>

    <!-- 帖子列表 -->
    <postItemList :isFollowList="false" :list="postList"></postItemList>

    <!-- 添加底部占位，避免评论框遮挡内容 -->
    <view class="h-120"></view>

    <!-- 参与讨论 -->
    <view
      class="p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff d-flex j-center a-center b-sh-00021200-022"
      @click="goToSendPost"
    >
      <view class="w-702 h-80 bg-f5f5f5 d-flex a-center b-rad-40 pl-24">
        <image
          class="w-34 h-34"
          src="https://images.vinehoo.com/vinehoomini/v3/comm/wri_gra.png"
          mode="aspectFill"
        ></image>
        <text class="ml-10 font-28 text-9">参与讨论…</text>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import postItemList from '@/pages/community/postList.vue'

export default {
  computed: {
    ...mapState(['routeTable', 'ossPrefix']), // 添加ossPrefix
  },
  components: {
    postItemList,
  },
  name: 'topic-detail',
  data() {
    return {
      topicId: '',
      topicDetail: {
        title: '',
        shortdesc: '',
        image: '',
        ishot: 0,
        total_posts_num: 0,
        posts_read_num: 0,
        rank: 0,
      },
      postList: [], // 子列表
      lastLoginInfo: null,
      page: 1, // 当前页码
      isLoadMore: false, // 是否正在加载更多
      hasMore: true, // 是否还有更多数据
    }
  },

  onLoad(options) {
    this.login.isLoginV3(this.$vhFrom, 0).then(async (isLogin) => {
      this.lastLoginInfo = isLogin
    })

    if (options.id) {
      this.topicId = options.id
      this.getTopicDetail()
      this.getTopicPosts() // 获取帖子列表
    }
  },

  onShow() {
    this.login.isLoginV3(this.$vhFrom, 0).then(async (isLogin) => {
      const currentLoginInfo = isLogin
      if (this.lastLoginInfo !== currentLoginInfo) {
        console.log('登录状态发生变化，刷新数据')
        window.location.reload()

        // 更新存储的登录信息
        this.lastLoginInfo = currentLoginInfo

        // 重新获取数据
        if (this.topicId) {
          this.page = 1
          this.postList = []
          this.hasMore = true
          this.getTopicDetail()
          this.getTopicPosts()
        }
      }
    })
    // 获取当前登录信息

    // 比较登录信息是否发生变化

    // 监听关注状态更新事件
    uni.$on('updateFollowStatus', ({ uid, status }) => {
      // 更新列表中对应用户的关注状态
      this.postList = this.postList.map((post) => {
        if (post.userinfo?.uid === uid) {
          return {
            ...post,
            is_attention: status,
          }
        }
        return post
      })
    })
  },

  onHide() {
    // 取消监听
    uni.$off('updateFollowStatus')
  },

  // 添加上拉加载更多方法
  onReachBottom() {
    if (this.hasMore) {
      this.page++
      this.getTopicPosts(true)
    }
  },

  methods: {
    async getTopicDetail() {
      try {
        const res = await this.$u.api.getTopicDetail({
          id: this.topicId,
        })
        if (res.error_code === 0) {
          this.topicDetail = res.data
        } else {
          this.$u.toast(res.error_msg || '获取话题详情失败')
        }
      } catch (error) {
        console.error('获取话题详情失败:', error)
      }
    },

    // 获取话题相关的帖子列表
    async getTopicPosts(isLoadMore = false) {
      if (this.isLoadMore || !this.hasMore) return

      try {
        this.isLoadMore = true
        const res = await this.$u.api.topicForList({
          topic_id: this.topicId,
          page: this.page,
          limit: 15,
        })

        if (res.error_code === 0) {
          let list = res.data.list || []

          // 适配数据结构
          list = list.map((item) => ({
            ...item,
            type_data: item.data_image, // 将data_image改为type_data
            content: item.data_content, // 将data_content改为content
            userinfo: {
              ...item.user_info,
              avatar_image: item.user_info.avatar_image.startsWith('http')
                ? item.user_info.avatar_image
                : this.ossPrefix + item.user_info.avatar_image, // 拼接头像域名
            },
            is_attention: item.user_info.status, // 使用user_info.status作为关注状态
          }))

          // 在获取数据的地方做适配
          list = list.map((item) => {
            if (item.vine) {
              // 将vine重命名为wine_data
              item.wine_data = item.vine
            }
            return item
          })

          if (isLoadMore) {
            // 加载更多,追加数据
            this.postList = [...this.postList, ...list]
          } else {
            // 首次加载,直接赋值
            this.postList = list
          }

          // 获取评论数量和点赞状态
          await this.getPostsOtherData(list)

          // 判断是否还有更多数据
          this.hasMore = list.length === 15
        } else {
          this.$u.toast(res.error_msg || '获取帖子列表失败')
        }
      } catch (error) {
        console.error('获取帖子列表失败:', error)
      } finally {
        this.isLoadMore = false
      }
    },

    // 添加获取评论数量和点赞状态的方法
    async getPostsOtherData(list) {
      if (!list || list.length === 0) return

      try {
        const params = {
          data: list.map((item) => ({
            id: item.id,
            source: item.source || (item.wine_data ? 6 : 2),
          })),
          start_index: 0,
          end_index: 0,
        }

        if (!params.data[0].id) {
          return
        }

        const res = await this.$u.api.getOtherData(params)
        if (res.error_code === 0 && res.data.list) {
          const updatedList = [...this.postList]

          res.data.list.forEach((newData) => {
            const index = updatedList.findIndex(
              (item) => item.id === newData.id && (item.source || (item.wine_data ? 6 : 2)) === newData.source
            )

            if (index !== -1) {
              updatedList[index] = {
                ...updatedList[index],
                commentnums: newData.commentnums,
                is_digg: newData.is_digg,
              }
            }
          })

          this.postList = updatedList
        }
      } catch (error) {
        console.error('获取评论数量和点赞状态失败:', error)
      }
    },

    // 修改跳转到发帖页面的方法
    async goToSendPost() {
      // 准备话题数据
      const topicData = {
        id: this.topicId,
        title: this.topicDetail.title,
        allowEmoticonOnly: true, // 添加允许只发表情包的标记
      }
      this.login.isLoginV3(this.$vhFrom, 1).then((isLogin) => {
        if (isLogin) {
          this.jump.appAndMiniJump(
            1,
            `${this.routeTable.pCSendPost}?topicData=${encodeURIComponent(JSON.stringify(topicData))}`,
            this.$vhFrom
          )
        }
      })
      // 带上话题数据跳转到发帖页
    },

    // 添加下拉刷新生命周期函数
    onPullDownRefresh() {
      // 重置页码
      this.page = 1
      // 重置加载更多状态
      this.hasMore = true

      // 并发请求话题详情和帖子列表
      Promise.all([this.getTopicDetail(), this.getTopicPosts()]).finally(() => {
        // 停止下拉刷新动画
        uni.stopPullDownRefresh()
      })
    },
  },
}
</script>

<style scoped></style>
