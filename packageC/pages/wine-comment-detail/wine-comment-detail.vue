<template>
  <view class="content">
    <!-- 添加下拉刷新组件 -->

    <!-- 导航栏 - 根据source显示不同标题 -->
    <vh-navbar :title="source === 6 ? '酒评详情' : '帖子详情'" show-border />

    <!-- 帖子详情 -->
    <view v-if="!loading && Object.keys(wineCommentDetail).length" class="pt-40 pr-32 pl-32 pb-32">
      <!-- 帖子信息 -->

      <view class="">
        <view class="d-flex">
          <!-- 用户头像 -->
          <view class="p-rela w-88 h-88" @click="goPersonCenter(userInfo)">
            <vh-image :src="userInfo.avatar_image" :loading-type="5" :width="88" :height="88" :shape="'circle'" />
            <image
              v-if="userInfo.certified_info"
              class="p-abso right-0 bottom-n-02 w-24 h-26"
              :src="`${osip}/comm/certified_24_26.png`"
            />
          </view>

          <!-- 用户信息 -->
          <view class="ml-10 flex-sb-c w-p100">
            <view class="d-flex a-center">
              <text class="font-28 font-wei text-2d2d2d l-h-40">{{ userInfo.nickname }}</text>
              <view
                v-if="userInfo.type != 2"
                class="font-22 d-flex j-center a-center b-rad-26 text-ffffff ml-10 w-80 h-34 bg-ff9300"
                >LV.{{ userInfo.user_level }}</view
              >
              <view v-else class="font-22 d-flex j-center a-center b-rad-10 text-ffffff ml-10 w-80 h-34 bg-e80404"
                >官方</view
              >
            </view>

            <!-- 关注按钮 -->
            <view
              v-if="userInfo.uid != currentUid"
              :class="[userInfo.is_follow ? 'text-3 b-s-02-d8d8d8' : 'text-2e7bff bg-e2ebfa']"
              class="w-100 mt-06 ptb-04-plr-00 font-24 text-center text-9 l-h-34 b-rad-26"
              @tap="handleFollow"
              >{{ userInfo.is_follow ? '已关注' : '+关注' }}</view
            >

            <!-- 重新编辑按钮 - 优化样式 -->
            <view
              v-if="source === 6 && userInfo.uid == currentUid && fromWineComment"
              class="mt-06 ptb-04-plr-00 font-24 text-center l-h-34 b-rad-26 edit-btn"
              @tap="handleEdit"
              >重新编辑</view
            >
          </view>
        </view>

        <view v-if="wineCommentDetail.topics.length" class="d-flex flex-wrap">
          <view
            v-for="item in wineCommentDetail.topics"
            :key="item.id"
            class="d-flex a-center b-rad-28 mt-20 mr-24 h-40 ptb-00-plr-20 font-24 bg-e2ebfa text-2e7bff"
            @click="goToTopicDetail(item)"
            >#{{ item.title }}#</view
          >
        </view>

        <view class="mt-32 font-30 text-6 l-h-50">
          <text space="emsp" decode="true">{{ wineCommentDetail.wine_evaluation || wineCommentDetail.content }}</text>
        </view>

        <view v-if="imgList.length" class="d-flex flex-wrap">
          <vh-image
            v-for="(item, index) in imgList"
            :key="index"
            :src="item"
            :loading-type="4"
            :width="imgList.length === 1 ? 686 : 220"
            :height="imgList.length === 1 ? singleImageHeight : 220"
            borderRadius="20rpx"
            class="mt-32"
            :class="[imgList.length === 1 ? '' : index % 3 === 2 ? 'mr-0' : 'mr-12']"
            @click="onPreviewImage(index)"
          />
        </view>
      </view>

      <!-- 酒评信息 -->
      <view v-if="source === 6 && wineCommentDetail.comment_type === 1" class="bg-f7f7f7 b-rad-20 mt-24 ptb-00-plr-24">
        <view
          v-if="wineCommentDetail.grade"
          class="d-flex j-sb a-center ptb-20-plr-00"
          :class="{ 'bb-s-02-eeeeee': wineCommentDetail.comment_type !== 2 }"
        >
          <vh-wine-comment-public-title :src="`${osip}/wine-comment/icon_grade.png`" title="评分" />
          <view class="d-flex a-center">
            <u-rate
              v-model="wineCommentDetail.grade"
              :count="5"
              :min-count="1"
              :size="26"
              :gutter="8"
              inactive-color="#D8D8D8"
              active-color="#E80404"
              inactive-icon="star-fill"
              disabled
            />
            <text class="font-28 text-3 ml-06">{{ wineCommentDetail.grade }}分</text>
          </view>
        </view>

        <template v-if="wineCommentDetail.comment_type !== 2">
          <!-- 观色 -->
          <view class="d-flex j-sb a-center ptb-20-plr-00 bb-s-02-eeeeee">
            <vh-wine-comment-public-title :src="`${osip}/wine-comment/icon_guanse.png`" title="观色" />
            <view class="d-flex a-center">
              <view class="mr-10 font-28 text-3">{{ wineCommentDetail.wine_color }}</view>
              <image class="w-40 h-20" :src="wineCommentDetail.wine_color_image" />
            </view>
          </view>

          <!-- 闻香 -->
          <view class="d-flex j-sb a-center ptb-20-plr-00 bb-s-02-eeeeee">
            <vh-wine-comment-public-title :src="`${osip}/wine-comment/icon_wenxiang.png`" title="闻香" />
            <view class="w-max-502 font-28 text-3">{{ wineCommentDetail.smell_aroma }}</view>
          </view>

          <!-- 品味 -->
          <view class="pt-20">
            <vh-wine-comment-public-title :src="`${osip}/wine-comment/icon_pinwei.png`" title="品味" />

            <view class="d-flex j-sb ptb-20-plr-00" v-for="(item, index) in wineCommentDetail.taste" :key="index">
              <view class="font-24 text-3 l-h-34">{{ item.name }}</view>
              <view class="w-502">
                <vh-wine-comment-slider
                  :value="item.value ? `${item.value}%` : '30rpx'"
                  :width="502"
                  :height="18"
                  :range-list="item.rang"
                  :pointWidth="0"
                  :pointHeight="0"
                  disabled
                />
              </view>
            </view>
          </view>
        </template>
      </view>

      <!-- 关联酒款 -->
      <view v-if="source === 6 && goodsInfo" class="mt-32">
        <vh-wine-comment-public-title :src="`${osip}/wine-comment/icon_relevance.png`" title="关联酒款" />
        <view
          class="d-flex b-sh-00001002-009 b-rad-10 mt-24 p-24"
          @click="jump.appAndMiniJump(1, `${routeTable.pgGoodsDetail}?id=${wineCommentDetail.period}`, $vhFrom)"
        >
          <vh-image :src="goodsInfo.banner_img[0]" :loading-type="2" :width="294" :height="184" />
          <view class="flex-1 d-flex flex-column j-sb ml-20">
            <view class="">
              <view class="font-26 text-3 text-hidden-2">{{ goodsInfo.title }}</view>
              <view class="mt-04 font-20 text-9 text-hidden-1">{{ goodsInfo.brief }}</view>
            </view>

            <view class="d-flex j-sb a-center">
              <view>
                <text v-if="isHiddenPrice || !isActiveStatus" class="font-30 text-3">价格保密</text>
                <template v-else>
                  <text v-if="goodsPrice" class="font-24 text-e80404">¥</text>
                  <text class="font-36 text-e80404">{{ goodsPrice }}</text>
                </template>
              </view>

              <u-button
                :hair-line="false"
                :ripple="true"
                @click="jump.appAndMiniJump(1, `${routeTable.pgGoodsDetail}?id=${wineCommentDetail.period}`, $vhFrom)"
                ripple-bg-color="#FFF"
                :custom-style="{
                  width: '96rpx',
                  height: '40rpx',
                  fontSize: '24rpx',
                  color: '#FFF',
                  backgroundColor: '#E80404',
                  border: 'none',
                  borderRadius: '6rpx',
                }"
                >{{ isActiveStatus ? '去购买' : '去查看' }}</u-button
              >
            </view>
          </view>
        </view>
      </view>
      <view class="mt-24 font-20 text-9">发布于{{ wineCommentDetail.format_created_time }}</view>

      <view class="pt-40 pr-32 pb-120">
        <view class="font-32 font-wei text-3 l-h-50">评论（{{ wineCommentDetail.commentnums }}）</view>

        <view v-if="commentList.length > 0">
          <view v-for="item in commentList" :key="item.id" class="bb-s-01-eeeeee pt-32 pb-32">
            <view class="">
              <view class="d-flex">
                <view class="p-rela w-72 h-72">
                  <image
                    class="w-72 h-72 b-rad-p50"
                    @click="goPersonCenter(item)"
                    :src="item.user.avatar_image"
                    mode="aspectFill"
                  ></image>
                  <image
                    v-if="item.user.certified_info"
                    class="p-abso right-0 bottom-0 w-24 h-26"
                    :src="`${osip}/comm/lv_gold.png`"
                    mode="aspectFill"
                  ></image>
                </view>

                <view class="ml-10 w-p100">
                  <view class="d-flex a-center">
                    <text class="font-28 font-wei text-9">{{ item.user.nickname }}</text>
                    <VhIconGrade :grade="item.user.user_level" />
                  </view>

                  <view class="mt-16">
                    <view class="">
                      <view class="comment-content">
                        <text
                          class="d-block font-28 text-3"
                          :class="{ 'text-hidden-2': !isContentExpanded(item.id) }"
                          @click="toggleContent(item.id)"
                          >{{ item.content }}</text
                        >
                        <!-- 只有内容超过两行时才显示展开/收起按钮 -->
                        <text
                          v-if="item.content.length > 50"
                          class="expand-btn font-24 text-2e7bff ml-10"
                          @click="toggleContent(item.id)"
                          >{{ isContentExpanded(item.id) ? '收起' : '展开' }}</text
                        >
                      </view>
                      <image
                        v-if="item.type_data"
                        :src="ossIcon(`/emoticon/${getEmojiMap.get(item.type_data)}.gif`)"
                        class="w-80 h-80"
                        mode="aspectFit"
                      />
                    </view>
                  </view>

                  <view class="d-flex j-sb a-center mt-20">
                    <view class="font-24 text-9 l-h-40">{{ item.created_time }}</view>
                    <view class="d-flex j-end">
                      <view class="d-flex a-center" @click="handleLike(item)">
                        <image
                          class="w-26 h-26"
                          :src="`${osip}/comm/${item.is_digg ? 'zan_active' : 'zan_none'}.png`"
                          mode="aspectFill"
                        ></image>
                        <text class="ml-06 font-28 text-9 l-h-40">{{ item.likenums || item.diggnums }}</text>
                      </view>

                      <view class="d-flex a-center ml-60" @click="showReplyInput(item)">
                        <image class="w-26 h-26" :src="`${osip}/comm/comm.png`" mode="aspectFill"></image>
                        <text class="ml-06 font-28 text-9 l-h-40">回复</text>
                      </view>
                    </view>
                  </view>

                  <!-- 回复列表 -->
                  <view v-if="item.reply && item.reply.length" class="bg-f8f8f8 b-rad-08 mt-20 p-24">
                    <!-- 始终显示前2条回复 -->
                    <view
                      class="mb-16"
                      v-for="(reply, index) in expandedCommentId === item.id ? item.reply : item.reply.slice(0, 2)"
                      :key="reply.id"
                      @click="showReplyInput(item, reply)"
                    >
                      <view class="d-flex flex-wrap">
                        <text
                          class="font-28 text-9"
                          v-if="
                            Number(reply.p_uid ? reply.p_uid : reply.reply_uid) !==
                            Number(reply.userInfo ? reply.userInfo.uid : reply.user.uid)
                          "
                          >{{ reply.userInfo ? reply.userInfo.nickname : reply.user.nickname
                          }}<text class="triangle"></text>{{ reply.reply_user.nickname }}：</text
                        >
                        <text class="font-28 text-9" v-else
                          >{{ reply.userInfo ? reply.userInfo.nickname : reply.user.nickname }}：</text
                        >
                        <view class="font-28 text-3">{{ reply.content }}</view>
                        <image
                          v-if="reply.type_data"
                          :src="ossIcon(`/emoticon/${getEmojiMap.get(reply.type_data)}.gif`)"
                          class="w-40 h-40"
                          mode="aspectFit"
                        />
                      </view>
                    </view>

                    <!-- 超过2条显示展开/收起 -->
                    <view v-if="item.reply.length > 2" class="mt-12 d-flex a-center" @click="toggleComment(item.id)">
                      <text class="mr-10 font-28 text-9">
                        {{ expandedCommentId === item.id ? '收起' : `共${item.reply.length}条回复` }}
                      </text>
                      <u-icon
                        :name="expandedCommentId === item.id ? 'arrow-up' : 'arrow-right'"
                        :size="20"
                        color="#999"
                      ></u-icon>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <vh-empty
          v-else
          bgColor="transparent"
          :padding-top="100"
          :padding-bottom="100"
          :image-src="ossIcon('/empty/emp_goods.png')"
          text="暂无数据"
          :text-bottom="0"
        />
      </view>

      <view
        style="left: 0"
        class="p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff d-flex j-sb a-center b-sh-00021200-022 pl-24 pr-24"
      >
        <view
          :class="source === 6 ? 'w-p80' : 'w-p90'"
          class="h-80 bg-f7f7f7 d-flex a-center b-rad-40 pl-24 font-28 text-9 l-h-40"
          @click="showCommentInput"
          >说点什么…</view
        >

        <!-- 添加收藏图标 -->
        <view class="d-flex a-center">
          <image
            v-if="source === 6"
            class="w-44 h-44 mr-24"
            :src="`${osip}/comm/${wineCommentDetail.is_collection ? 'collect-icon-active' : 'collect-icon'}.png`"
            mode="aspectFill"
            @click="handleCollect"
          ></image>

          <image
            class="w-44 h-44"
            :src="`${osip}/comm/${wineCommentDetail.is_digg ? 'zan_active' : 'zan_none'}.png`"
            mode="aspectFill"
            @click="handleMainLike"
          ></image>
        </view>
      </view>
    </view>

    <!-- 回复输入框弹窗 -->
    <u-popup v-model="showComment" mode="bottom" border-radius="20">
      <vh-comment
        :plate="0"
        :placeholder="'说点什么…'"
        :protocol-list="protocolList"
        @on-comment-add="handleCommentAdd"
        @on-img-send="handleImgSend"
      />
    </u-popup>
  </view>
</template>

<script>
import { OSIP } from '@/common/js/fun/constant.js'
import { mapState } from 'vuex'
import VhComment from '@/components/vh-comment/vh-comment.vue'
import emoj from '@/common/js/data/rabbitEmoji.js'

export default {
  name: 'wineCommentDetail',

  components: {
    VhComment,
  },

  data() {
    return {
      osip: OSIP,
      loading: true,
      id: '',
      currentUid: '',
      wineCommentDetail: {},
      topicList: [],
      goodsInfo: null,
      goodsPrice: '',
      isHiddenPrice: false,
      isActiveStatus: false,
      source: '', // 添加source字段
      showAllReplies: false, // 控制是否展开有回复
      commentReplies: [],
      isExpanded: false,
      commentContent: '一瓶酒云网回来的年份香槟，还是值得一试的，该品牌的年份香槟我都很喜欢，200…111',
      commentList: [], // 评论列表
      commentTotal: 0, // 评论总数
      commentPage: 1, // 当前页码
      commentLimit: 10, // 每页条数
      protocolList: [],
      commentLoading: false, // 加载状态
      expandedCommentId: '', // 当前展开的论ID
      replyInfo: {
        show: false,
        commentId: '', // 要回复的评论ID
        firstId: '', // 一级评论ID
        replyUid: '', // 被回复用户ID
        placeholder: '说点什么...',
        content: '',
      },
      showComment: false, // 控制评论框显示
      commentImgReg: '', // 表情包图片正则
      isRefreshing: false, // 控制下拉刷新状态
      fromWineComment: false, // 新增: 是否从酒评列表页进入
      expandedContentIds: [], // 存储已展开的评论ID
      collectLoading: false,
      singleImageHeight: 400, // 默认高度
      lastLoginInfo: null, // 添加lastLoginInfo变量
    }
  },

  watch: {
    imgList: {
      immediate: true,
      handler(newVal) {
        if (newVal.length === 1) {
          this.calculateImageHeight(newVal[0])
        }
      },
    },
  },

  computed: {
    ...mapState(['routeTable']),
    userInfo({ wineCommentDetail }) {
      return wineCommentDetail?.userinfo || {}
    },
    imgList({ wineCommentDetail }) {
      const type_data = wineCommentDetail?.type_data || ''
      return type_data ? type_data?.split(',') : []
    },
    contentLength() {
      return this.commentContent.length
    },
    // 获取兔头表情包映射
    getEmojiMap() {
      return emoj
    },
    canEdit() {
      return this.source === 6 && this.userInfo.uid === this.$store.state.userInfo.uid
    },
    // 计算总评论数（包括回复）
    totalCommentCount() {
      let total = this.commentTotal // 一级评论数

      // 加上所有回复数
      if (this.commentList && this.commentList.length) {
        total += this.commentList.reduce((sum, comment) => {
          return sum + (comment.reply?.length || 0)
        }, 0)
      }

      return total
    },
  },

  methods: {
    goPersonCenter(item) {
      console.log(item)
      this.jump.appAndMiniJump(1, `/packageC/pages/my-post/my-post?uid=${item.uid}`, this.$vhFrom, 0, true)
    },
    async getWineCommentDetail() {
      const res = await this.$u.api.getWineCommentDetail({ id: this.id })
      console.log('getWineCommentDetail', res)
      this.wineCommentDetail = res?.data || {}
    },

    async getGoodsDetail() {
      const res = await this.$u.api.goodsDetailJson({ t: 1, isJson: true, id: this.wineCommentDetail.period })
      this.goodsInfo = res?.data || null
      if (this.goodsInfo) {
        this.getPackageDetail()
      }
    },

    async getPackageDetail() {
      const { periods_type } = this.goodsInfo
      const params = { period: this.wineCommentDetail.period, periods_type }
      const res =
        periods_type == 9 ? await this.$u.api.vmallPackageDetail(params) : await this.$u.api.packageDetail(params)
      const { packageList, is_hidden_price, onsale_status } = res.data
      const filteredPackageList = packageList.filter((v) => v.is_hidden == 0 && +v.price > 0)
      this.goodsPrice = is_hidden_price ? '价格保密' : filteredPackageList[0]?.price || ''
      this.isHiddenPrice = is_hidden_price
      this.isActiveStatus = ![3, 4].includes(onsale_status)
    },
    onPreviewImage(index) {
      uni.previewImage({
        current: index,
        indicator: true,
        urls: this.imgList,
      })
    },
    toggleExpand() {
      this.isExpanded = !this.isExpanded
    },
    async getCommentList() {
      if (this.commentLoading) return
      this.commentLoading = true

      try {
        const params = {
          page: this.commentPage,
          limit: this.commentLimit,
        }

        let res
        if (this.source === 6) {
          params.wine_evaluation_id = this.id
          res = await this.$u.api.commentList(params)
        } else {
          params.posts_id = this.id
          res = await this.$u.api.postsCommentList(params)
        }

        const list = res.data.list
        this.commentList = this.commentPage === 1 ? list : [...this.commentList, ...list]
        this.commentTotal = res.data.total
      } catch (e) {
        console.error(e)
      } finally {
        this.commentLoading = false
      }
    },
    // 展开/收起评论
    toggleComment(commentId) {
      this.expandedCommentId = this.expandedCommentId === commentId ? '' : commentId
    },
    checkAgreement() {
      uni.getStorage({
        key: 'protocolList',
        success: (res) => {
          this.protocolList = res.data
        },
        fail: async () => {
          try {
            let res = await this.$u.api.userSpecifiedData({ field: 'protocol' })
            this.protocolList = res.data.protocol
            uni.setStorageSync('protocolList', [...this.protocolList])
          } catch (e) {
            //TODO handle the exception
          }
        },
        complete: () => {
          this.showComment = true
        },
      })
    },

    // 显示回复框
    showReplyInput(comment, replyInfo = null) {
      this.replyInfo = {
        show: true,
        commentId: comment.id,
        firstId: replyInfo ? comment.first_id || comment.id : comment.id,
        replyUid: replyInfo ? replyInfo.uid || replyInfo.user?.uid : comment.uid || comment.user?.uid,
        placeholder: replyInfo ? `回复 ${replyInfo.userInfo?.nickname || replyInfo.user?.nickname}` : '说点什么...',
        content: '',
      }
      // 直接显示评论框
      this.checkAgreement()
    },

    // 提交回复
    async submitReply() {
      if (!this.replyInfo.content.trim()) {
        return this.$u.toast('请输入回复内容')
      }

      const params = {
        content: this.replyInfo.content,
      }

      if (this.source === 6) {
        // 酒评回复
        params.wine_evaluation_id = this.id
        if (this.replyInfo.firstId) {
          params.first_id = this.replyInfo.firstId
          params.reply_comment_id = this.replyInfo.commentId
          params.reply_uid = this.replyInfo.replyUid
        }
        await this.$u.api.wineMakeComment(params)
      } else {
        // 帖子回复
        params.posts_id = this.id
        if (this.replyInfo.firstId) {
          params.first_id = this.replyInfo.firstId
          params.reply_comment_id = this.replyInfo.commentId
          params.reply_uid = this.replyInfo.replyUid
        }
        await this.$u.api.postMakeComment(params)
      }

      this.$u.toast('回复成功')
      this.replyInfo.show = false
      this.replyInfo.content = ''
      this.wineCommentDetail.commentnums++

      // 重新加载评论列表
      this.commentPage = 1
      this.commentList = []
      await this.getCommentList()
    },

    // 显示评论框
    showCommentInput() {
      this.checkAgreement()
    },
    // 处理主贴点赞
    async handleMainLike() {
      // 如果已经点赞,则不执行任何操作
      if (this.wineCommentDetail.is_digg) {
        return
      }

      const params = {
        id: this.id,
        type: 0, // 0表示主贴点赞
        action: 0, // 只保留点赞功能,固定为0
      }

      // 根据source调用不同接口
      if (this.source === 6) {
        await this.$u.api.wineEvaluationLike(params)
      } else {
        await this.$u.api.postsLike(params)
      }

      // 新点赞状态
      this.$set(this.wineCommentDetail, 'is_digg', true)

      // 更新赞数,只增加不减少
      const currentLikeNum = this.wineCommentDetail.likenums || this.wineCommentDetail.diggnums || 0
      if ('likenums' in this.wineCommentDetail) {
        this.$set(this.wineCommentDetail, 'likenums', currentLikeNum + 1)
      }
      if ('diggnums' in this.wineCommentDetail) {
        this.$set(this.wineCommentDetail, 'diggnums', currentLikeNum + 1)
      }
    },
    // 处理评论提交
    async handleCommentAdd(content) {
      try {
        // 过滤内容中的回车和首尾空格
        const filteredContent = content.replace(/[\r\n]/g, '').trim()

        // // 如果滤后内容为空,提示用户
        // if (!filteredContent) {
        //   return this.$u.toast('请输入评论内容')
        // }

        // await this.$u.api.msgSecCheck({
        //   content: filteredContent,
        //   scene: 2,
        // })

        this.feedback.loading({ title: '评论中...' })

        const params = {
          content: filteredContent,
          type_data: this.commentImgReg, // 直接使用表情包数据,已经含[]
        }

        if (this.source === 6) {
          params.wine_evaluation_id = this.id
          if (this.replyInfo.firstId) {
            params.first_id = this.replyInfo.firstId
            params.reply_comment_id = this.replyInfo.commentId
            params.reply_uid = this.replyInfo.replyUid
          }
          await this.$u.api.wineMakeComment(params)
        } else {
          params.posts_id = this.id
          if (this.replyInfo.firstId) {
            params.first_id = this.replyInfo.firstId
            params.reply_comment_id = this.replyInfo.commentId
            params.reply_uid = this.replyInfo.replyUid
          }
          await this.$u.api.postMakeComment(params)
        }
        this.wineCommentDetail.commentnums++
        this.$u.toast('评论成功')
        this.showComment = false
        this.commentImgReg = '' // 清空表情数据

        // 重置复信息
        this.replyInfo = {
          show: false,
          commentId: '',
          firstId: '',
          replyUid: '',
          placeholder: '说点什么...',
          content: '',
        }

        // 重新加载评论列表
        this.commentPage = 1
        this.commentList = []
        await this.getCommentList()
      } catch (e) {
        console.error(e)
        this.commentImgReg = '' // 发送失败也要清空表情数据
        this.$u.toast(e.message || '评论失败')
      }
    },

    // 处理表情发送
    handleImgSend(emoReg) {
      // 保存表情包正则,用于发送评论时带上
      this.commentImgReg = emoReg
    },

    async handleLike(item) {
      // 如果已经点赞,则不执行任何操作
      if (item.is_digg) {
        return
      }

      const params = {
        id: item.id,
        type: 1, // 评论点赞
        action: 0, // 只保留点赞功能,固为0
      }

      // 根据source调用不同的点赞接口
      if (this.source === 6) {
        await this.$u.api.wineEvaluationLike(params)
      } else {
        await this.$u.api.postsLike(params)
      }

      // 更新点赞状态
      this.$set(item, 'is_digg', true)

      // 更点赞数,只增加不减少
      const currentLikeNum = item.likenums || item.diggnums || 0
      if ('likenums' in item) {
        this.$set(item, 'likenums', currentLikeNum + 1)
      }
      if ('diggnums' in item) {
        this.$set(item, 'diggnums', currentLikeNum + 1)
      }
    },

    // 获取分享信息
    async getShareInfo() {
      try {
        const title =
          this.source === 6
            ? (this.wineCommentDetail.wine_evaluation || '').slice(0, 30)
            : (this.wineCommentDetail.content || '').slice(0, 30)

        return {
          title: title || (this.source === 6 ? '酒评详情' : '帖子详情'),
          imageUrl: this.imgList.length > 0 ? this.imgList[0] : '',
          path: `/packageC/pages/wine-comment-detail/wine-comment-detail?id=${this.id}&source=${this.source}`,
        }
      } catch (e) {
        console.error('获取分享信息失败:', e)
        return {
          title: this.source === 6 ? '酒评详情' : '帖子详情',
          path: `/packageC/pages/wine-comment-detail/wine-comment-detail?id=${this.id}&source=${this.source}`,
        }
      }
    },
    handleEdit() {
      // 准备编辑数据
      const editData = {
        id: this.id,
        orderNo: this.wineCommentDetail.main_order_no,
        grade: this.wineCommentDetail.grade,
        wineColor: this.wineCommentDetail.wine_color,
        wineColorImage: this.wineCommentDetail.wine_color_image,
        fragrance: this.wineCommentDetail.smell_aroma,
        content: this.wineCommentDetail.wine_evaluation || this.wineCommentDetail.content,
        // 转换图片格式以匹配上传组期望的格式
        images: this.imgList.map((url) => ({
          url,
          path: url,
          status: 'success',
          progress: 100,
          error: false,
          file: { path: url },
        })),
        topics: this.wineCommentDetail.topics?.map((t) => t.id) || [],
        taste: this.wineCommentDetail.taste,
      }

      // 跳转到编辑页面时添加 fromDetail 参数
      this.jump.navigateTo(
        `${this.routeTable.pCWineCommentSend}?editData=${encodeURIComponent(JSON.stringify(editData))}&fromDetail=true`
      )
    },

    // 处理关注/取消关注
    async handleFollow() {
      // 如果是取消关注，出确认框
      if (this.userInfo.is_follow) {
        uni.showModal({
          title: '提示',
          content: '确定取消关注该用户吗？',
          success: async (res) => {
            if (res.confirm) {
              await this.doFollow()
            }
          },
        })
      } else {
        // 如果是关注操作，直接执行
        await this.doFollow()
      }
    },

    // 执行关注/取消关注的具体操作
    async doFollow() {
      const params = {
        operate_uid: this.userInfo.uid,
        status: this.userInfo.is_follow ? 2 : 1,
      }

      const res = await this.$u.api.focusUser(params)

      if (res.error_code == 0) {
        this.$u.toast(this.userInfo.is_follow ? '取消关注成功' : '关注成功')

        // 更新关注状态
        this.$set(this.userInfo, 'is_follow', !this.userInfo.is_follow)

        // 发出全局事件，通知其他列表更新
        uni.$emit('updateFollowStatus', {
          uid: this.userInfo.uid,
          status: this.userInfo.is_follow ? 1 : 0,
        })
      } else {
        this.$u.toast(res.error_msg || '操作失败')
      }
    },

    // 刷新数据的方法
    async refreshData() {
      // 重置所有相关数据
      this.commentPage = 1
      this.commentList = []
      this.commentTotal = 0

      // 并行请求主要数据
      await Promise.all([
        // 获取详情
        this.source === 6
          ? this.$u.api.getWineCommentDetail({ id: this.id }).then((res) => {
              this.wineCommentDetail = res?.data || {}
            })
          : this.$u.api.postsDetail({ id: this.id }).then((res) => {
              this.wineCommentDetail = res?.data || {}
            }),

        // 获取评论列表
        this.getCommentList(),
      ])

      // 如果是酒评且有商品信息,刷新商品信息
      if (this.source === 6 && this.wineCommentDetail.period) {
        await this.getGoodsDetail()
      }
    },

    // 处理下拉刷新
    async onRefresh() {
      if (this.isRefreshing) return

      this.isRefreshing = true
      await this.refreshData()
      this.isRefreshing = false
    },

    // 切换评论内容的展开/收起状态
    toggleContent(commentId) {
      const index = this.expandedContentIds.indexOf(commentId)
      if (index > -1) {
        this.expandedContentIds.splice(index, 1)
      } else {
        this.expandedContentIds.push(commentId)
      }
    },

    // 判断评论是否展开
    isContentExpanded(commentId) {
      return this.expandedContentIds.includes(commentId)
    },

    async handleCollect() {
      if (this.collectLoading) return

      try {
        this.collectLoading = true
        const params = {
          wine_id: this.wineCommentDetail.id,
          status: this.wineCommentDetail.is_collection ? 0 : 1,
        }

        const res = await this.$u.api.collection(params)
        if (res.error_code === 0) {
          this.$set(this.wineCommentDetail, 'is_collection', !this.wineCommentDetail.is_collection)
          uni.showToast({
            title: this.wineCommentDetail.is_collection ? '收藏成功' : '取消收藏',
            icon: 'none',
          })
        }
      } catch (e) {
        // uni.showToast({
        //   title: '操作失败，请稍后重试',
        //   icon: 'none',
        // })
      } finally {
        this.collectLoading = false
      }
    },

    goToTopicDetail(topic) {
      this.jump.appAndMiniJump(1, `/packageC/pages/topic-detail/topic-detail?id=${topic.id}`, this.$vhFrom)
    },

    // 计算单张图片的高度
    calculateImageHeight(imageUrl) {
      uni.getImageInfo({
        src: imageUrl,
        success: (res) => {
          // 根据图片原始宽高比计算显示高度
          // 686是容器宽度
          const scale = 686 / res.width
          this.singleImageHeight = Math.floor(res.height * scale)
        },
        fail: () => {
          // 如果获取失败，使用默认高度
          this.singleImageHeight = 400
        },
      })
    },

    onShow() {
      this.login.isLoginV3(this.$vhFrom, 0).then(async (isLogin) => {
        const currentLoginInfo = isLogin
        if (this.lastLoginInfo !== null && this.lastLoginInfo !== currentLoginInfo) {
          // 只在登录状态发生变化时刷新数据
          await this.refreshData()
        }
        // 更新存储的登录信息
        this.lastLoginInfo = currentLoginInfo
      })

      // 监听关注状态更新事件
      uni.$on('updateFollowStatus', ({ uid, status }) => {
        // 更新列表中对应用户的关注状态
        this.commentList = this.commentList.map((comment) => {
          if (comment.user?.uid === uid) {
            return {
              ...comment,
              is_attention: status,
            }
          }
          return comment
        })
      })
    },
  },

  onLoad(options) {
    const { id, source, from } = options
    this.id = id
    this.source = Number(source)
    this.fromWineComment = from === 'wine-comment'

    // 添加页面显示的事件监听
    uni.$on('wine-comment-updated', () => {
      this.refreshData()
    })

    // 根据source类型调不同的接口
    const api =
      this.source === 6
        ? this.$u.api
            .getWineCommentDetail({ id: this.id })
            .then((res) => {
              this.wineCommentDetail = res?.data || {}
              if (this.source === 6) {
                this.getGoodsDetail()
              }
            })
            .finally(() => {
              this.loading = false
            })
        : this.$u.api
            .postsDetail({ id: this.id })
            .then((res) => {
              this.wineCommentDetail = res?.data || {}
              if (this.source === 6) {
                this.getGoodsDetail()
              }
            })
            .finally(() => {
              this.loading = false
            })
    this.getCommentList()
  },

  onReachBottom() {
    if (this.commentList.length < this.commentTotal) {
      this.commentPage++
      this.getCommentList()
    }
  },

  // 添加分享到聊天
  onShareAppMessage() {
    return this.getShareInfo()
  },

  // 添加分享到朋友圈
  onShareTimeline() {
    return this.getShareInfo()
  },

  created() {
    // 在组件创建时获取storage中的uid
    const loginInfo = uni.getStorageSync('loginInfo')
    this.currentUid = loginInfo ? loginInfo.uid : ''
  },

  // 添加 onUnload 生命周期,清理事件监听
  onUnload() {
    uni.$off('wine-comment-updated')
  },
  onPullDownRefresh() {
    this.refreshData().then(() => {
      uni.stopPullDownRefresh()
    })
  },
}
</script>

<style lang="scss" scoped>
.content {
  .scroll-view {
    height: 100%;
  }

  ::v-deep {
    .slider {
      &__tbar {
        background: linear-gradient(90deg, #fcdede 0%, #e80404 100%) !important;
      }

      &__bottom {
        margin-top: 6rpx;
      }
    }
  }
  .text-hidden-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }

  .comment-content {
    position: relative;
    display: inline;

    .text-hidden-2 {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
  }

  .expand-btn {
    cursor: pointer;
    display: inline-block;

    &:active {
      opacity: 0.8;
    }
  }
}
.edit-btn {
  color: #fff;
  padding: 4rpx 10rpx;
  background: #e80404;
  border: 1rpx solid #e80404;

  &:active {
    opacity: 0.81;
  }
}
.triangle {
  display: inline-block;
  width: 0;
  height: 0;
  border-top: 7rpx solid transparent;
  border-bottom: 7rpx solid transparent;
  border-left: 14rpx solid #999999;
  margin: 0 8rpx;
  vertical-align: middle;
}
</style>
