<template>
	<view class="content">
		<!-- 直播已结束 -->
		<view class="">
			<!-- 导航栏 -->
			<view class="">
				<u-navbar back-icon-color="#FFF" :background="{ backgroundColor: '#000' }"></u-navbar>
			</view>
			
			<!-- 直播结束信息 -->
			<view class="h-vh-100 pt-238 bg-999999">
				<view class="d-flex j-center a-center font-52 font-wei text-ff9127">直播已结束</view>
				<view class="d-flex j-center a-center mt-24 font-28 text-ffffff l-h-40">直播时长  120:25</view>
				
				<view class="d-flex j-center a-center mt-76">
					<view class="d-flex flex-column j-center a-center">
						<text class="font-32 text-ffffff l-h-40">1148</text>
						<text class="mt-24 font-24 text-ffffff l-h-40">观看人数</text>
					</view>
					
					<view class="w-02 h-80 bg-ffffff ml-100 mr-100">
						
					</view>
					
					<view class="d-flex flex-column j-center a-center">
						<text class="font-32 text-ffffff l-h-40">215</text>
						<text class="mt-24 font-24 text-ffffff l-h-40">互动消息</text>
					</view>
				</view>
				
				<view class="d-flex j-center a-center mt-152 font-24 text-ffffff">分享至</view>
				
				<view class="d-flex j-sa a-center mt-40">
					<button class="sha-weixin-btn w-40 h-40"></button>
					<button class="sha-fri-btn w-40 h-40"></button>
				</view>
				
				<view class="d-flex flex-column j-center a-center mt-230">
					<view class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'540rpx', height:'80rpx', fontSize:'32rpx' ,fontWeight:'500', color:'#FFF', backgroundImage: 'linear-gradient(214deg, #FF9144 0%, #F35D00 100%)', border:'none'}">回看直播</u-button>
					</view>
					
					<view class="mt-24">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'540rpx', height:'80rpx', fontSize:'32rpx' ,fontWeight:'500', color:'#FF9127', backgroundColor: '#FFF', border:'none'}"
						@click="showGoodsPop=true">购买酒款</u-button>
					</view>
				</view>
				
				
			</view>
		</view>
		
		<!-- 酒款商品弹出层 -->
		<view class="">
			<u-popup v-model="showGoodsPop" mode="bottom" :border-radius="20">
				<view class="d-flex j-center a-center bb-s-01-eeeeee pt-40 pb-30 font-32 font-wei text-0">全部商品</view>
				
				<scroll-view class="h-max-1000" scroll-y="true">
					<view class="pt-28 pr-24 pl-24">
						<view class="d-flex mb-24" v-for="(item,index) in 6" :key="index">
							<view class="">
								<image class="w-288 h-180 b-rad-10" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_rec_bg.png" mode="aspectFill"></image>
							</view>
							
							<view class="flex-1 ml-20">
								<view class="h-80 font-28 text-0 l-h-40 text-hidden-2 o-hid">名家精选 Decug Barbi Villa 
arbiOrvieto Classico 2016六的文档无</view>
								
								<view class="d-flex j-sb a-end mt-50">
									<view>
										<text class="font-36 text-e80404"><text class="font-24">¥</text>499.8</text>
									</view>
									
									<view>
										<text class="font-24 text-9 l-h-34">已售<text class="text-e04040">21</text>份/限量<text class="text-3">38</text>份</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</u-popup>
		</view>
	</view>
</template>

<script>
	export default{
		name:"live-broadcast",
		data(){
			return{
				showGoodsPop:true,//是否展示
			}
		}
	}
</script>

<style scoped>
	.sha-weixin-btn{
		background: url(https://images.vinehoo.com/vinehoomini/v3/live_broadcast/sha_weixin_whi.png) center center no-repeat;
		background-size: contain;
	}
	.sha-fri-btn{
		background: url(https://images.vinehoo.com/vinehoomini/v3/live_broadcast/sha_fir_whi.png) center center no-repeat;
		background-size: contain;
	}
</style>
