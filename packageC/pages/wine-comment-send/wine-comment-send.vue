<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar title="写酒评"/>
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" id="outer-content" class="fade-in">
			<!-- 提示 -->
			<view class="d-flex j-center a-center pt-32 pb-24">
				<view class="w-60 h-01 bg-ff9127"></view>
				<view class="ml-10 mr-10 font-24 text-ff9127">客观真实的酒评可以帮助更多兔友哦</view>
				<view class="w-60 h-01 bg-ff9127"></view>
			</view>
			
			<!-- 编辑项 -->
			<view class="mtb-00-mlr-24 pb-124">
				<!-- 商品信息 -->
				<view class="bg-ffffff d-flex b-rad-20 bb-d-01-eeeeee p-24">
					<vh-image :src="wineCommentInfo.banner_img" :loading-type="2" :width="110" :height="80" :border-radius="6" />
					<view class="flex-1 d-flex flex-column j-sb ml-20">
						<view class="font-24 text-6 text-hidden-1">{{ wineCommentInfo.title }}</view>
						<view class="d-flex j-sb a-center">
							<text class="bg-f5f5f5 b-rad-04 ptb-02-plr-12 font-20 text-9">{{ wineCommentInfo.package_name }}</text>
							<text class="font-24 text-6">¥{{ wineCommentInfo.package_price }}</text>
						</view>
					</view>
				</view>

				<view style="border-top: 2rpx dashed #eee;" class="w-654 mtb-00-mlr-auto"></view>
				
				<!-- 观色、闻香、配餐...-->
				<view class="bg-ffffff b-rad-20 ptb-00-plr-24">
					<view class="d-flex j-sb a-start ptb-24-plr-00">
						<view class="d-flex a-center p-rela">
							<text class="font-18 text-e80404 p-abso t-trans-x-m100 pr-04">*</text>
							<text class="font-28 text-3">评分</text>
						</view>
						<view class="d-flex j-sb a-center w-524">
							<u-rate v-model="params.grade" :count="5" :min-count="1" :size="40" :gutter="24" inactive-color="#D8D8D8" active-color="#E80404" inactive-icon="star-fill" />
							<text v-if="params.grade" class="font-28 text-3">{{ params.grade }}分</text>
						</view>
					</view>

					<template v-if="wineCommentInfo.comment_type === 1">
						<!-- 观色 -->
						<view class="d-flex j-sb a-center ptb-24-plr-00 bt-s-02-eeeeee" @click="showWineColorPicker = true">
							<view class="d-flex a-center p-rela">
								<text class="font-18 text-e80404 p-abso t-trans-x-m100 pr-04">*</text>
								<text class="font-28 text-3">观色</text>
							</view>
							
							<view class="d-flex a-center">
								<view class="font-28 l-h-40" :class="wineColor ? 'text-3' : 'text-9'">{{ wineColor ? wineColor : '请选择' }}</view>
								<image v-if="wineColorImage" class="w-40 h-20 b-rad-10 ml-10" :src="wineColorImage" />
								<image :src="`${osip}/after_sale_detail/arrow_right_12x20.png`" class="w-12 h-20 ml-10">
							</view>
						</view>
						
						<!-- 闻香 -->
						<view class="d-flex j-sb a-center ptb-24-plr-00 bt-s-02-eeeeee" @click="showFragrancePop = true">
							<view class="d-flex a-center p-rela">
								<text class="font-18 text-e80404 p-abso t-trans-x-m100 pr-04">*</text>
								<text class="font-28 text-3">闻香</text>
							</view>
							
							<view class="d-flex a-center">
								<view class="font-28 l-h-40 w-max-470" :class="fragrance ? 'text-3' : 'text-9'">{{ fragrance ? fragrance : '请选择' }}</view>
								<image :src="`${osip}/after_sale_detail/arrow_right_12x20.png`" class="w-12 h-20 ml-10">
							</view>
						</view>

						<!-- 配餐 -->
						<!-- <view class="d-flex j-sb a-center ptb-24-plr-00 bb-d-01-eeeeee">
							<view class="d-flex a-center ml-n-06">
								<text class="font-18 text-e80404">*</text>
								<text class="ml-04 font-28 text-3">配餐</text>
							</view>
							<view class="d-flex a-center" @click="showCateringPop = true">
								<view class="mr-16 font-28" :class="catering ? 'text-3' : 'text-9'">{{ catering ? catering : '请选择' }}</view>
								<u-icon name="arrow-right" :size="20" color="#333" />
							</view>
						</view> -->
						
						<!-- 酒体 -->
						<view class="bt-s-02-eeeeee">
							<view class="d-flex j-sb a-start" v-for="(item, index) in tasteList" :key="index">
								<view class="d-flex a-center p-rela pt-24">
									<text class="font-18 text-e80404 p-abso t-trans-x-m100 pr-04">*</text>
									<text class="font-28 text-3">{{ item.name }}</text>
								</view>
								
								<view class="w-500" :class="index + 1 < tasteListLength ? 'bb-s-02-eeeeee' : ''">
									<view v-if="item.id === tanninId" class="d-flex a-center" :class="params.is_tannin ? 'pt-24' : 'ptb-24-plr-00'">
										<view class="d-flex a-center mr-50" v-for="tanninRadio in [1, 0]" :key="tanninRadio" @click="params.is_tannin = tanninRadio">
											<image class="w-32 h-34" :src="`${osip}/wine-comment/radio${params.is_tannin === tanninRadio ? '_h' : ''}_32_34.png`"  />
											<text class="ml-16 font-20 text-6">{{ tanninRadio ? '有' : '无'}}</text>
										</view>
									</view>
									
									<view v-if="item.id !== tanninId || params.is_tannin" class="pt-32 pb-24" :class="item.id === tanninId ? 'pt-28' : ''">
										<vh-wine-comment-slider v-model="item.progress" :height="20" :range-list="item.list" :pointWidth="24" :pointHeight="24" />
									</view>
								</view>
							</view>
						</view>
					</template>
				</view>
				<view style="border-top: 2rpx dashed #D8D8D8;" class="w-654 mtb-00-mlr-auto"></view>

				<!-- 饮酒心得 -->
				<view id="weTextarea" class="bg-ffffff b-rad-20 p-24">
					<view style="padding-bottom: 22rpx;" class="font-24 text-6 bb-s-02-eeeeee">
						<template v-if="wineEvaluationLength < wineEvaluationMinCount || !uploadFileList.length">
							<text v-if="wineEvaluationLength" class="d-flex a-center">
								<template v-if="wineEvaluationMinCount > wineEvaluationLength">还差<text style="font-family: PingFangSC-Semibold;" class="font-28 text-e80404">{{ wineEvaluationMinCount - wineEvaluationLength }}</text>字，并</template>带图评论，即可得<text style="font-family: PingFangSC-Semibold;" class="font-28 text-e80404">{{ rabbitHeadCount }}</text>兔头哦
							</text>
							<text v-else>每日首次提交<text style="font-family: PingFangSC-Semibold;" class="font-28 text-e80404">{{ wineEvaluationMinCount }}</text>字以上带图评论，可得<text style="font-family: PingFangSC-Semibold;" class="font-28 text-e80404">{{ rabbitHeadCount }}</text>兔头！</text>
						</template>
						<text v-else class="text-e80404">恭喜！提交后，审核通过可得 {{ rabbitHeadCount }} 兔头！</text>
					</view>

					<!-- 心得 -->
					<view class="p-rela">
						<view v-if="isShowPlaceholder && !params.wine_evaluation" class="p-abso top-24 d-flex a-center">
							<image :src="`${osip}/wine-comment/icon_edit.png`" class="mr-10 w-26 h-26" />
							<text class="font-28 text-9">分享你的饮酒心得到社区吧～</text>
						</view>
						<textarea class="w-654 h-200 b-rad-10 font-28 text-3 l-h-40 ptb-24-plr-00" v-model="params.wine_evaluation" @focus="onWeTextareaFocus" @blur="isShowPlaceholder = true" />
					</view>
					
					<!-- 上传 -->
					<vh-community-upload
						ref="uUpload" 
                        :fileList="uploadFileList"
						:directory="'vinehoo/client/wineComment/'" 
						:auto-upload="false" 
						:max-count="9" 
						@on-list-change="onListChange"
						@on-uploaded="onUploaded" 
					/>

					<topic-checkbox-group v-model="checkedTopicIdList" :topicList="topicList" showMore />
				</view>
			</view>
			
			<!-- 底部按钮 -->
			<view class="p-fixed bottom-0 w-p100 h-104 bg-ffffff d-flex j-center a-center b-sh-00021200-022 z-9999">
				<u-button
				:disabled="!canSubmit"
				shape="circle" 
				:hair-line="false" 
				:ripple="true" 
				ripple-bg-color="#FFF"
				:custom-style="{ width:'646rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'500', color:'#FFF', backgroundColor: !canSubmit ? '#FCE4E3' : '#E80404', border:'none', borderRadius: '32rpx' }"
				@click="submit">提交</u-button>
			</view>
			
			<!-- 弹框 -->
			<view class="">
				<!-- 酒体颜色 -->
				<vh-wine-comment-picker
					v-model="showWineColorPicker"
					title="酒体颜色"
					mode="wineColor"
					:range="wineColorList" 
					range-key="name"
					:default-selector="[0]"
					@confirm="confirm($event, 'wineColor')" 
				/>
				
				<!-- 香气特征 -->
				<vh-wine-comment-popup-multi-params 
					v-model="showFragrancePop"
					:params-list="fragranceList"
					:default-ids-str="fragranceIds"
					@confirm="confirm($event, 'fragrance')" 
				/>
				
				<!-- 饮酒心得弹框 -->
				<u-modal 
					v-model="showExperienceMod" 
					:show-title="false" 
					content="" 
					:width="540" 
					:show-confirm-button="true" 
					:show-cancel-button="true" 
					cancel-text="仍要发布" 
					confirm-text="继续编辑"
					:cancel-style="{fontSize:'30rpx', color:'#999'}"
					:confirm-style="{fontSize:'30rpx', color:'#2E7BFF'}"
					@cancel="publishAnyway"
				>
					<view class="ptb-40-plr-60 text-center font-30 font-wei text-3 l-h-42">
						<template v-if="wineEvaluationMinCount > wineEvaluationLength">还差{{ wineEvaluationMinCount - wineEvaluationLength }}字，并</template>带图评论，即可得{{ rabbitHeadCount }}兔头哦！
					</view>
				</u-modal>
				
				<!-- 配餐 -->
				<!-- <vh-popup-multi-params
					v-model="showCateringPop"
					:type="2"
					title="配餐"
					:params-list="cateringList"
					@confirm="confirm($event, 'catering')" 
				/> -->
			</view>
		</view>
	</view>
</template>

<script>
	import { OSIP } from '@/common/js/fun/constant.js'
	import { mapState, mapMutations } from 'vuex'
	const TASTE_MAPPER = {
		sweetness: 189,
		acidity: 188,
		tannin_content: 190,
		wine_body: 191,
		aftertaste: 192
	}

	export default {
		name: 'wine-comment-send', //发酒评
		
		data() {
			return {
				osip: OSIP, //oss静态图片前缀
				loading: true, //加载中
				wineEvaluationMinCount: 30,
				rabbitHeadCount: 20,
				tanninId: TASTE_MAPPER.tannin_content,
				params: {
					main_order_no: '',
					period: '',
					period_title: '',
					wine_color: '',
					wine_color_image: '',
					smell_aroma: '',
					sweetness: '',
					acidity: '',
					is_tannin: 0,
					tannin_content: '',
					wine_body: '',
					aftertaste: '',
					wine_evaluation: '',
					type_data: '',
					topic_id: '',
					comment_type: 0,
					grade: 0
				},
				
				// 酒体颜色板块
				wineTypeId: '', //酒款类型id
				wineColor: '', //酒体颜色
				wineColorId: '', //酒体颜色id
				wineColorImage: '', //酒体颜色图片
				wineColorList: [], //酒体颜色列表
				
				// 香气板块
				fragranceList: [], //香气列表
				fragrance:'', //香气
				fragranceIds: '', //香气ids
				
				// 品味
				tasteList: [], //品味列表
				taste: '', //品味儿
				tasteIds: '', //品味id
				tannin: 0, //是否含有单宁
				
				// 配餐板块
				// cateringList: [], //配餐列表
				// catering: '', //请选择配餐
				// cateringIds: '', //配餐id字符串
				
				uploadFileList: [], //上传完成的文件列表
				uploadImageStr:'', //补充描述图片，多张逗号分隔

				topicList: [],
				checkedTopicIdList: [],
				
				// 框板块
				showWineColorPicker: false, //是否显示酒体颜色
				showFragrancePop: false, //是否显示闻香弹框
				showExperienceMod: false, //是否显示心得弹框
				// showCateringPop: false, //是否显示配餐弹框

				isPublishAnyaway: false,
				orderNo: '',
				wineCommentInfo: {},
				isShowPlaceholder: true,
				isEdit: false, // 是否是编辑模式
				editData: null, // 编辑数据
			}
		},
		
		computed: {
			...mapState(['routeTable']),
			...mapState('topic', ['checkedTopicList', 'defaultWineCommentTopicIds', 'wineCommentTopicList', 'wineCommentTopicIdList']),
			wineEvaluationLength ({ params }) {
				const str = params.wine_evaluation.replaceAll(' ', '')
				let count = 0
				for (const letter of str) { count++ }
				return count
			},
			tasteListLength ({ tasteList }) {
				return tasteList.length
			},
			// 是否可以提交
			canSubmit({ wineCommentInfo, wineTypeId, wineColorId, fragranceIds, wineEvaluationLength, params }) {
				if (wineCommentInfo.comment_type === 1) {
					if (wineTypeId && wineColorId && fragranceIds && wineEvaluationLength && params.grade) return true
				} else {
					if (wineEvaluationLength && params.grade) return true
				}
				return false
			}
		},
		
		onLoad (options) {
			const { orderNo = '', editData = '', fromDetail = false } = options || {}
			this.orderNo = orderNo
			this.fromDetail = fromDetail
			
			// 处理编���数据
			if(editData) {
				this.isEdit = true
				this.editData = JSON.parse(decodeURIComponent(editData))
              
                
                
                
					
				// 初始化编辑数据
				this.initEditData()
			}

			const reqs = this.defaultWineCommentTopicIds.map(id => {
				return this.$u.api.getCommunityTopicDetail({ id }).then(res => res?.data || {}).catch(() => ({ id, status: 0 }))
			})
			Promise.all(reqs).then(resList => {
				const wineCommentTopicList = resList.filter(({ status }) => status)
				this.UPDATE_WINE_COMMENT_TOPIC_LIST(wineCommentTopicList)
			}).finally(() => {
				this.UPDATE_CHECKED_TOPIC_LIST([...this.wineCommentTopicList])
				this.checkedTopicIdList = this.checkedTopicList.map(({ id }) => id)
				this.init()
			})
		},

		onShow () {
			// 更多话题返回需要执行
			const pages = getCurrentPages()
			const currentPage = pages[pages.length - 1]
			
			// 如果有从话题选择页返回的数据
			if (currentPage.$vm.selectedTopics) {
				const selectedTopics = currentPage.$vm.selectedTopics
				
				// 更新选中的话题ID列表
				this.checkedTopicIdList = selectedTopics.map(({ id }) => id)
				
				// 更新 store 中的选中话题列表
				this.UPDATE_CHECKED_TOPIC_LIST(selectedTopics)
				
				// 更新本地话题列表
				const filteredCheckedTopicList = selectedTopics.filter(checkedItem => 
					!this.topicList.some(item => item.id === checkedItem.id)
				)
				this.topicList = [...filteredCheckedTopicList, ...this.topicList]
				
				// 清除临时数据
				currentPage.$vm.selectedTopics = null
			}
		},
		
		methods: {
			...mapMutations('topic', ['UPDATE_CHECKED_TOPIC_LIST', 'UPDATE_WINE_COMMENT_TOPIC_LIST']),
			// 初始化
			async init() {
				await Promise.all([ this.getWineParams(), this.getCommunityTopicList(), this.getOrderDetail(), this.initRabbitHeadCount() ])
				this.loading = false
                if (this.isEdit && this.editData) {
					this.initEditBaseData() // 先初始化基础数据
					this.matchWineColorAndFragranceIds() // 再进行ID匹配
					this.initTasteData() // 初始化品味数据
				}
				
				this.loading = false
			},
			matchWineColorAndFragranceIds(){
                if (this.wineColor && this.wineColorList.length) {
					// wineColorList 结构是 [[类型列表], [颜色列表]]
					const colorList = this.wineColorList[1][0] || [] // 获取第一个类型的颜色列表
                    console.warn(colorList)
					const colorMatch = colorList.find(item => item.cate_name === this.wineColor)
                    console.warn(colorMatch)
					if (colorMatch) {
						this.wineColorId = colorMatch.asid
						// 找到对应的酒款类型ID
						const typeList = this.wineColorList[0]
						const typeMatch = typeList.find(type => {
							const typeColors = this.wineColorList[1][typeList.indexOf(type)] || []
							return typeColors.some(color => color.asid === this.wineColorId)
						})
						if (typeMatch) {
							this.wineTypeId = typeMatch.id
						}
					}
				}

				// 匹配香气特征IDs
                
				if (this.fragrance && this.fragranceList.length) {
					const fragranceNames = this.fragrance.split(' ')
					const matchedIds = []
                    console.log(fragranceNames)
                    console.log(this.fragranceList)
					fragranceNames.forEach(name => {
						this.fragranceList.forEach(category => {
							const match = category.list.find(item => item.cate_name === name)
                            console.log(match)
							if (match) {
								matchedIds.push(match.asid)
							}
						})
					})
					
					if (matchedIds.length) {
						this.fragranceIds = matchedIds.join(',')
					}
				}
            },
			// 获取酒款
			async getWineParams() {
				let res = await this.$u.api.wineParams()
				let { asc: { smell, taste , Catering, readyd_rink_param, vision } } = res.data
				this.multiParams = res.data //所有参数
				let wineTypeList = vision.map(({ id, name }) => ({ id, name })) //拿酒款类型
				let wineColorList = vision.map(({ list }) =>  list) //拿到酒款颜色
				this.wineColorList = [ wineTypeList, wineColorList ] //酒款颜色列表
				this.fragranceList = smell //气味
				this.tasteList = Object.keys(TASTE_MAPPER).map(key => {
					const tasteId = TASTE_MAPPER[key]
					const findItem = taste.find(({ id }) => tasteId === id)
					if (!findItem) return null
					const { id, list, name } = findItem
					return { id, list, name, progress: 0, show: id == this.tanninId ? false : true, key }
				})
				// this.cateringList = Catering //配餐
			},

			async getCommunityTopicList () {
				const params = {
					type: 1,
					status: 1,
					ishot: 1,
					page: 1,
					limit: 5
				}
				const res = await this.$u.api.getCommunityTopicList(params)
				const list = res?.data?.list || []
				this.topicList = [...this.wineCommentTopicList, ...list.filter(item => !this.defaultWineCommentTopicIds.includes(item.id))]
			},

			async getOrderDetail () {
				try {
					if (this.orderNo) {
						const res = await this.$u.api.orderDetail({ order_no: this.orderNo })
						const orderDetail = res.data
						const { order_no, goodsInfo, comment_type } = orderDetail
						const { period, goods_title, goods_img, package_name, package_price } = goodsInfo[0] || {}
						this.wineCommentInfo = {
							sub_order_no: order_no,
							period,
							title: goods_title,
							banner_img: goods_img,
							package_name,
							package_price,
							comment_type
						}
					}
				} catch (err) {
				}
			},

			async initRabbitHeadCount () {
				const res = await this.$u.api.getWineCommentRHCount()
				this.rabbitHeadCount = res.data.wine_rabbit
			},
			
			// 品味滑动结束
			tasteSlideEnd(e, index) {
				console.log( e ) 
				console.log( index )
			},
			
			// 确认选择 e = 事件、type = 类型
			confirm(e, type) {
				switch( type ) {
					// 观色（颜色）
					case 'wineColor':
					console.log('wineColor')
					const { id } = e.wineType //拿到酒款类型
					const { asid, cate_name, cate_image } = e.wineColor //拿到酒款颜色
					this.wineTypeId = id 
					this.wineColorId = asid
					this.wineColor = cate_name
					this.wineColorImage = cate_image
					break
					// 香味
					case 'fragrance':
					console.log('fragrance')
					this.fragrance = e.names
					this.fragranceIds = e.ids
                    console.log(this.fragranceIds)
					break
					// 配餐
					// case 'catering':
					// console.log('catering')
					// this.catering = e.names
					// this.cateringIds = e.ids
					// break
				}
				console.log('wine-comment-send')
				console.log( e )
			},
			
			// 上传列表发生改变
			onListChange(list) {
				console.log('-----------------------上传列表发生改变')
                console.log(list)
				this.uploadFileList = list
                // 当图片列表发生变化时，同步更新 type_data
                this.params.type_data = list && list.length ? list.map(item => {
                    if (item.response) {
                        return item.response
                    }
                    if (item.url) {
                        const vinehooIndex = item.url.indexOf('/vinehoo/')
                        if (vinehooIndex !== -1) {
                            return item.url.substring(vinehooIndex)
                        }
                        return item.url
                    }
                    return ''
                }).filter(Boolean).join() : ''
			},
			
			// 所有图片上传成功
			// 修改 onUploaded 方法
            onUploaded(list, index) {
                console.log('-------上传所有文件成功')
                console.log(list)
                this.feedback.toast({ title: '所有图片上传成功~'})
                    
                // 统一处理图片URL格式
                this.params.type_data = list && list.length ? list.map(item => {
                    // 如果有response直接使用
                    if (item.response) {
                        return item.response
                    }
                    // 处理只有url的情况
                    if (item.url) {
                        // 找到 /vinehoo/ 的位置
                        const vinehooIndex = item.url.indexOf('/vinehoo/')
                        if (vinehooIndex !== -1) {
                            // 截取 /vinehoo/ 及其后面的部分
                            return item.url.substring(vinehooIndex)
                        }
                    }
                    return ''
                }).filter(Boolean).join() : ''

                // 保存当前选中的话题状态，而不是使用默认话题
                const currentTopicIds = [...this.checkedTopicIdList]
                const currentTopics = this.topicList.filter(topic => currentTopicIds.includes(topic.id))
                
                // 更新 store 中的选中话题，确保使用当前选中的话题而不是默认话题
                this.UPDATE_CHECKED_TOPIC_LIST(currentTopics)
                
                this.create().catch(() => {
                    // 如果创建失败，保持当前话题选择状态
                    this.checkedTopicIdList = currentTopicIds
                    this.UPDATE_CHECKED_TOPIC_LIST(currentTopics)
                })
            },
			
			// 仍要发布
			publishAnyway( e ) {
				console.log(' publishAnyway ')
				this.isPublishAnyaway = true
				this.submit()
			},
			
			// 提交
			submit() {
				const uploadFileListLength = this.uploadFileList.length
				if (!this.isPublishAnyaway && (this.wineEvaluationLength < this.wineEvaluationMinCount || !uploadFileListLength)) {
					this.showExperienceMod = true
					return
				}

				// 保存当前选中的话题状态
				const currentTopicIds = [...this.checkedTopicIdList]
				const currentTopics = this.topicList.filter(topic => currentTopicIds.includes(topic.id))
				
				// 更新 store 中的选中话题
				this.UPDATE_CHECKED_TOPIC_LIST(currentTopics)
				
				if (uploadFileListLength) {
					this.$refs.uUpload.upload()
				} else {
					this.create().catch(() => {
						// 如果创建失败，保持当前话题选择状态
						this.checkedTopicIdList = currentTopicIds
						this.UPDATE_CHECKED_TOPIC_LIST(currentTopics)
					})
				}
			},

			async create () {
                const data = {
                    content: this.params.wine_evaluation,
                    scene: 2,
                }
                try {
                    // const res = await this.$u.api.msgSecCheck(data)
                    const { sub_order_no, period, title, comment_type } = this.wineCommentInfo
                    
                    // 构建基础参数
                    const params = {
                        main_order_no: sub_order_no,
                        period,
                        period_title: title,
                        wine_color: this.wineColor,
                        wine_color_image: this.wineColorImage,
                        smell_aroma: this.fragrance,
                        topic_id: this.checkedTopicIdList.join(),
                        comment_type,
                        wine_evaluation: this.params.wine_evaluation,
                        type_data: this.params.type_data,
                        grade: this.params.grade
                    }
                    console.log(this.params.type_data)
                    // 添加品味相关参数
                    this.tasteList.forEach(({ key, progress }) => {
                        if (key === 'tannin_content') {
                            params.is_tannin = progress > 0 ? 1 : 0
                        }
                        params[key] = progress
                    })

                    // 如果是编辑模式,添加id参数
                    if (this.isEdit && this.editData) {
                        params.id = this.editData.id
                        // 调用编辑接口
                        const res = await this.$u.api.upEva(params)
                        
                        // 触发更新事件
                        uni.$emit('wine-comment-updated')

                        // 根据来源决定跳转方式
                        if (this.fromDetail) {
                            // 如果是从详情页来的,用 navigateBack
                            uni.navigateBack()
                        } else {
                            // 否则重向到详情页
                            this.jump.redirectTo(`${this.routeTable.pCWineCommentDetail}?id=${res.data}&source=6&from=wine-comment`)
                        }
                    } else {
                        // 调用创建接口
                        const res = await this.$u.api.createWineComment(params)
                        this.jump.redirectTo(`${this.routeTable.pCWineCommentDetail}?id=${res.data}&source=6`)
                    }
                } catch (e) {
                    console.log(e)
                }
			},

			onWeTextareaFocus () {
				this.isShowPlaceholder = false
				uni.createSelectorQuery().in(this).select('#weTextarea').boundingClientRect(data=>{
					uni.createSelectorQuery().in(this).select('#outer-content').boundingClientRect(res=>{
						uni.pageScrollTo({
							scrollTop: data.top - res.top,
							duration: 0
						})
					}).exec()
				}).exec()
			},

			initEditData() {
				const { 
					id,
					orderNo,
					grade,
					wineColor,
					wineColorImage,
					fragrance,
					content,
					images,
					topics,
					taste
				} = this.editData

				// 设���基础数据
				this.params.grade = grade
				this.params.wine_evaluation = content
				this.wineColor = wineColor
				this.wineColorImage = wineColorImage
				this.fragrance = fragrance
                // this.wineTypeId
                // this.wineColorId
                // this.fragranceIds
           
                
				// 设置话题
				if(topics && topics.length) {
					this.checkedTopicIdList = topics
				}

				// 设置图片
				if(images && images.length) {
					this.params.type_data = images.join(',')
					this.uploadFileList = images.map(url => ({
						url,
						status: 'success',
						response: url
					}))
				}

				// 设置品味数据
				if(taste && taste.length) {
					taste.forEach(item => {
						const target = this.tasteList.find(t => t.name === item.name)
						if(target) {
							target.progress = parseInt(item.value)
							if(target.id === this.tanninId) {
								this.params.is_tannin = target.progress > 0 ? 1 : 0
							}
						}
					})
				}

				// 设置订单信息
				if(orderNo) {
					this.orderNo = orderNo
				}

				// 匹配酒体颜色ID
                
			},

			// 新增 initTasteData 方法
			initTasteData() {
				const { taste } = this.editData
				if(taste && taste.length && this.tasteList.length) {
					taste.forEach(item => {
						const target = this.tasteList.find(t => t.name === item.name)
						if(target) {
							target.progress = parseInt(item.value)
							if(target.id === this.tanninId) {
								this.params.is_tannin = target.progress > 0 ? 1 : 0
							}
							// 设置对应的参数值
							if(target.key) {
								this.params[target.key] = target.progress +'%'
							}
						}
					})
				}
			},

			// 修改 initEditBaseData 方法，移除品味数据的初始化
			initEditBaseData() {
				const { 
					id,
					orderNo,
					grade,
					wineColor,
					wineColorImage,
					fragrance,
					content,
					images,
					topics
				} = this.editData

				// 设置基础数据
				this.params.grade = grade
				this.params.wine_evaluation = content
				
				// 修改观色相关赋值
				this.params.wine_color = wineColor
				this.params.wine_color_image = wineColorImage
				this.wineColor = wineColor  // 用于显示
				this.wineColorImage = wineColorImage  // 用于显示
				
				this.fragrance = fragrance
				
				// 设置话题
				if(topics && topics.length) {
					this.checkedTopicIdList = topics
				}

				// 设置图片
				if(images && images.length) {
					this.params.type_data = images.join(',')
					this.uploadFileList = images.map(url => ({
						url:url.url,
						status: 'success',
						response: url
					}))
				}

				// 设置订单信息
				if(orderNo) {
					this.orderNo = orderNo
				}
			}
			
		}
	}
</script>

<style>
	@import "../../../common/css/page.css";
</style>