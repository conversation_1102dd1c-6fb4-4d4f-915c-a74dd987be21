<template>
	<view class="content pl-24 pr-24">
		<!-- 导航栏 -->
		<u-navbar back-icon-color="#333" title="用户评论" title-size="36" :title-bold="true" title-color="#333"></u-navbar>
		
		<!-- 用户评论框 -->
		<view class="mt-24">
			<textarea class="w-654 h-270 bg-f7f7f7 b-rad-08 p-24 font-28 text-3" :maxlength="159" v-modal="commContent" 
			placeholder="说点什么…最多可输入159字" placeholder-style="color:#999;font-size:28rpx;" />
		</view>
		
		<!-- 上传图片 -->
		<view class="mt-32">
			<view class="w-222 h-222 bg-f7f7f7 d-flex flex-column j-center a-center b-rad-06">
				<image class="w-76 h-60" src="https://images.vinehoo.com/vinehoomini/v3/comm/cam_red.png" mode="aspectFill"></image>
				<text class="mt-22 font-28 text-9 l-h-40">添加图片</text>
			</view>
		</view>
		
		<!-- 是否关联到社区 -->
		<view class="mt-90 d-flex j-sb a-center">
			<text class="font-32 font-wei text-3 l-h-52">关联到社区</text>
			<u-switch v-model="isRelaComm" :size="40" active-color="#E80404"></u-switch>
		</view>
		
		<!-- 社区话题 -->
		<view class="mt-32">
			<view class="font-32 text-3 l-h-52">#社区话题</view>
			
			<view class="d-flex flex-wrap a-center mt-34">
				<text class="bg-f5f5f5 b-rad-26 mr-24 mb-40 ptb-06-plr-16 font-24 font-wei text-3 l-h-40">#甜渣节</text>
				<text class="bg-f5f5f5 b-rad-26 mr-24 mb-40 ptb-06-plr-16 font-24 font-wei text-3 l-h-40">#重遇拉曼恰</text>
				<text class="bg-f5f5f5 b-rad-26 mr-24 mb-40 ptb-06-plr-16 font-24 font-wei text-3 l-h-40">#红酒与诗</text>
				<text class="bg-e2ebfa b-rad-26 mr-24 mb-40 ptb-06-plr-16 font-24 font-wei text-2e7bff l-h-40">#品酒大会</text>
				<text class="bg-f5f5f5 b-rad-26 mr-24 mb-40 ptb-06-plr-16 font-24 font-wei text-3 l-h-40">#旅行漫画家</text>
				<text class="bg-f5f5f5 b-rad-26 mr-24 mb-40 ptb-06-plr-16 font-24 font-wei text-3 l-h-40">#五一节</text>
				<text class="bg-f5f5f5 b-rad-26 mr-24 mb-40 ptb-06-plr-16 font-24 font-wei text-3 l-h-40">#我的高光期</text>
				
				<view class="w-148 h-50 d-flex j-center a-center b-rad-26 b-s-01-e7e7e7 mb-40">
					<text class="font-24 font-wei text-3">更多话题</text>
					<u-icon name="arrow-right" :size="20" color="#999"></u-icon>
				</view>
			</view>
		</view>
	    
		<!-- 酒云网评论管理规则-->
		<view class="mt-100">
			<view class="bg-ffffff d-flex j-center a-center">
				<image class="w-26 h-26" :src="isRead ? 'https://images.vinehoo.com/vinehoomini/v3/comm/cir_sel.png' : 'https://images.vinehoo.com/vinehoomini/v3/comm/cir_unsel.png'" mode="widthFix" @click="isRead =! isRead"></image>
				<text class="ml-10 font-24 text-9">阅读并接受</text>
				<text class="font-24 text-2b8cf7">《酒云网-评论管理规则》</text>
				<text class="font-24 text-9">和</text>
				<text class="font-24 text-2b8cf7">《社区运营规则》</text>
			</view>
			<view class="bg-ffffff cer-btn-con d-flex j-center mt-24 pb-62">
				<view v-if="isRead">
					<u-button shape="circle" :ripple="true" ripple-bg-color="#ffffff" 
					:custom-style="{width:'646rpx',color:'#fff',backgroundColor: '#E80404',border:'none'}">发表评论</u-button>
				</view>
				<view v-else class="bg-fce4e3 w-646 h-80 d-flex j-center a-center font-28 text-ffffff font-wei-500 b-rad-40">发表评论</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return{
				commContent:'',//评论内容
				isRelaComm:false,//是否关联到社区
				isRead:true,//是否阅读协议
			}
		}
	}
</script>

<style scoped>
</style>
