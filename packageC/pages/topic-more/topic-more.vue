<template>
  <view class="ptb-00-plr-32">
    <vh-navbar title="更多话题" />
    <view class="d-flex a-end h-88">
      <view class="d-flex j-sb a-center w-p100">
        <view class="d-flex j-sb a-center h-68 bg-f7f7f7 b-rad-40 flex-1">
          <view class="w-70">
            <image class="w-44 h-44 ml-26" :src="`${osip}/comm/ser_gray.png`" />
          </view>
          <input
            v-model="title"
            type="text"
            class="h-p100 font-28 text-3 flex-1"
            placeholder="大家都在搜"
            @confirm="searchCommunityTopicList"
          />
          <view v-if="title" class="d-flex a-center w-78 h-p100" @click="clearSearch">
            <image class="w-40 h-40 ml-8" :src="`${osip}/comm/del_gray.png`" />
          </view>
        </view>
        <view class="w-104 font-wei-500 font-28 text-6 text-center" @click="searchCommunityTopicList">搜索</view>
      </view>
    </view>

    <view class="mt-32">
      <view class="d-flex flex-wrap">
        <view
          v-for="item in displayList"
          :key="item.id"
          class="d-flex a-center b-rad-28 mt-36 mr-24 h-50 ptb-00-plr-16 font-24"
          :class="checkedTopicIdList.includes(item.id) ? 'bg-e2ebfa text-2e7bff' : 'bg-f5f5f5 text-3'"
          @click="updateCheckedTopicList(item)"
          >#{{ item.title }}#</view
        >
      </view>
    </view>

    <view class="p-fixed left-0 bottom-0 w-p100 h-104 bg-ffffff d-flex j-center a-center b-sh-00021200-022 z-9999">
      <u-button
        shape="circle"
        :hair-line="false"
        :ripple="true"
        ripple-bg-color="#FFF"
        :custom-style="{
          width: '646rpx',
          height: '64rpx',
          fontSize: '28rpx',
          fontWeight: '500',
          color: '#FFF',
          backgroundColor: '#E80404',
          border: 'none',
          borderRadius: '32rpx',
        }"
        @click="back"
        >已选（{{ checkedTopicListLength }}/{{ checkMax }}）</u-button
      >
    </view>
  </view>
</template>

<script>
import { OSIP } from '@/common/js/fun/constant.js'
import { mapState } from 'vuex'

export default {
  name: 'topicMore',
  data: () => ({
    osip: OSIP,
    topicList: [],
    checkMax: 3,
    title: '',
    searchTopicList: [],
  }),
  computed: {
    ...mapState('topic', ['checkedTopicList', 'wineCommentTopicList', 'wineCommentTopicIdList']),
    checkedTopicListLength({ checkedTopicList }) {
      return checkedTopicList.length
    },
    checkedTopicIdList({ checkedTopicList }) {
      return checkedTopicList.map(({ id }) => id)
    },
    displayList({ title, searchTopicList, topicList }) {
      return title ? searchTopicList : topicList
    },
  },
  methods: {
    async getCommunityTopicList() {
      const params = {
        type: 1,
        status: 1,
        page: 1,
        limit: 20,
      }
      const res = await this.$u.api.getCommunityTopicList(params)
      const list = res?.data?.list || []
      this.topicList = [
        ...this.wineCommentTopicList,
        ...list.filter((item) => !this.wineCommentTopicIdList.includes(item.id)),
      ]
    },
    clearSearch() {
      this.title = ''
      this.searchTopicList = []
    },
    async searchCommunityTopicList() {
      if (!this.title) {
        this.searchTopicList = []
        return
      }
      const params = {
        type: 1,
        status: 1,
        title: this.title,
      }
      const res = await this.$u.api.getCommunityTopicList(params)
      this.searchTopicList = res?.data?.list || []
    },
    updateCheckedTopicList(item) {
      const { id } = item
      if (this.checkedTopicIdList.includes(id)) {
        const findIndex = this.checkedTopicList.findIndex((checkedItem) => checkedItem.id === id)
        this.checkedTopicList.splice(findIndex, 1)
      } else {
        if (this.checkedTopicListLength >= this.checkMax) {
          uni.showToast({
            title: '话题最多只能选择3个',
            icon: 'none',
          })
          return
        }
        this.checkedTopicList.push(item)
      }
    },
    back() {
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]

      if (this.checkedTopicList && this.checkedTopicList.length) {
        if (prevPage.$vm) {
          prevPage.$vm.selectedTopics = [...this.checkedTopicList]

          this.$store.commit('topic/UPDATE_CHECKED_TOPIC_LIST', [...this.checkedTopicList])
        }
      }

      uni.navigateBack()
    },
  },
  onLoad() {
    this.getCommunityTopicList()
  },
}
</script>
