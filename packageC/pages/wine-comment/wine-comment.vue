<template>
  <view class="content">
    <!-- 导航栏 -->
    <vh-navbar title="酒评" show-border />

    <!-- 数据加载完成 -->
    <view v-if="!loading" class="fade-in">
      <!-- tabs -->
      <view class="bg-ffffff ptb-00-plr-40">
        <!-- 用户信息 -->
        <view class="d-flex a-start pt-20">
          <view class="p-rela w-72 h-72">
            <vh-image :src="userInfo.avatar_image" :loading-type="5" :width="72" :height="72" :shape="'circle'" />
            <image
              v-if="userInfo.certified_info"
              class="p-abso right-0 bottom-0 w-24 h-26"
              :src="`${osip}/comm/lv_gold.png`"
            />
          </view>
          <view class="p-rela top-12 flex-c-c ml-10 o-hid">
            <view class="font-32 font-wei-600 text-2d2d2d l-h-44 text-hidden">{{ userInfo.nickname }}</view>
            <view class="flex-shrink flex-c-c ml-12 w-80 h-32 font-wei-600 font-22 text-ffffff bg-ff9127 b-rad-18">
              <text class="p-rela" :class="sysPlatformAndroid ? 'top-n-02' : ''">LV.{{ userInfo.user_level }}</text>
            </view>
          </view>
        </view>

        <!-- tabs选项栏 -->
        <view class="wine-comment__tabs mt-12">
          <u-tabs
            :list="tabList"
            :current="currentTabs"
            :height="92"
            :font-size="28"
            inactive-color="#333"
            active-color="#E80404"
            :bar-width="36"
            :bar-height="8"
            :bar-style="{ background: 'linear-gradient(214deg, #FF8383 0%, #E70000 100%)' }"
            :is-scroll="false"
            @change="changeTabs"
          />
        </view>
      </view>

      <!-- 酒评列表 -->
      <vh-wine-comment-list :list="wineCommentList" />
    </view>
  </view>
</template>

<script>
import { OSIP } from '@/common/js/fun/constant.js'
import { mapState, mapActions } from 'vuex'

const PAGE_SIZE = 10

export default {
  name: 'wine-comment', //酒评
  data() {
    return {
      osip: OSIP, //oss静态图片前缀
      loading: true, //加载中
      tabList: [
        //状态栏选项
        { name: '待评价', list_type: 1 },
        { name: '已评价', list_type: 2 },
      ],
      currentTabs: 0, //当前选中tabs
      wineCommentList: [], //酒评列表
      query: {
        list_type: 1,
        page: 1,
        limit: PAGE_SIZE,
      },
      totalPage: 0,
    }
  },

  computed: {
    ...mapState(['userInfo']),
  },

  methods: {
    ...mapActions(['getUserInfo']),
    async getWineCommentList() {
      const { list_type } = this.tabList[this.currentTabs]
      this.query.list_type = list_type
      const res = await this.$u.api.getWineCommentList(this.query)
      const { list = [], total = 0, evaluatedTotal = 0 } = res?.data || {}
      list.forEach((item) => {
        item.$type = this.currentTabs
      })
      this.wineCommentList = this.query.page === 1 ? list : this.wineCommentList.concat(list)
      switch (list_type) {
        case 1:
          this.totalPage = Math.ceil(total / PAGE_SIZE)
          break
        case 2:
          this.totalPage = Math.ceil(evaluatedTotal / PAGE_SIZE)
          break
        default:
          this.totalPage = 0
      }
      this.tabList = this.$options.data().tabList.map(({ name, list_type }) => {
        let count = 0
        switch (list_type) {
          case 1:
            count = total
            break
          case 2:
            count = evaluatedTotal
            break
        }
        return {
          name: count ? `${name} ${count}` : name,
          list_type,
        }
      })
    },
    // 切换状态栏
    changeTabs(index) {
      this.currentTabs = index
      this.query.page = 1
      this.getWineCommentList()
    },
  },

  onShow() {
    if (this.login.isLogin(this.$vhFrom, 1)) {
      this.query.page = 1
      Promise.all([this.getUserInfo(), this.getWineCommentList()]).finally(() => {
        this.loading = false
      })
    }
  },

  onReachBottom() {
    if (this.query.page === this.totalPage || !this.totalPage) return
    this.query.page++
    this.getWineCommentList()
  },
}
</script>

<style>
@import '../../../common/css/page.css';
</style>

<style lang="scss" scoped>
.wine-comment {
  &__tabs {
    ::v-deep {
      .u-tab {
        &-item {
          font-weight: 600 !important;
          vertical-align: top;
        }

        &-bar {
          top: 100%;
        }
      }
    }
  }
}
</style>
