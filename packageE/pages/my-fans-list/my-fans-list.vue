<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar back-icon-color="#333" title="粉丝" :show-border="true"/>
		
		
		<!-- 我的粉丝列表 -->
		<view class="fade-in">
			<!-- 有数据 -->
			<view v-if="fansList.length" class="pl-32 pr-32">
				<view class="d-flex j-sb a-center pt-24 pb-24 bb-s-01-eeeeee" v-for="(item, index) in fansList" :key="index">
					<view class="d-flex">
						<view class="p-rela w-98 h-98">
							<vh-image :loading-type="2" :src="item.avatar_image" :width="98" :height="98" shape="circle"/>
							<image v-if="item.certified_info" class="p-abso bottom-0 right-0 w-36 h-40" src="https://images.vinehoo.com/vinehoomini/v3/comm/lv_gold.png" mode="aspectFill" />
						</view>
						
						<view class="ml-20">
							<view class="d-flex a-center">
								<text class="font-32 font-wei text-2d2d2d">{{item.nickname}}</text>
								<VhIconGrade :grade="item.user_level"/>
							</view>
							
							<view class="d-flex mt-10">
								<view v-if="item.certified_info" class="bg-252-224-224-030 d-flex j-center a-center ptb-04-plr-24 b-rad-22">
									<text class="mr-06 font-18 font-wei text-e80404 l-h-26">{{item.certified_info}}</text>
									<u-icon name="arrow-right" :size="20" color="#E80404" />
								</view>
								
								<view v-else class="bg-f5f5f5 ptb-04-plr-18 b-rad-22 font-18 font-wei text-9 l-h-26">未认证</view>
							</view>
						</view>
					</view>
					
					<view class="">
						<view v-if="item.is_follow == 1" class="">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'100rpx', height:'40rpx', fontSize:'24rpx', color:'#333', border:'0.5px solid #E7E7E7'}"
							@click="userAttenAndCncelAtten(item, 2)">已关注</u-button>
						</view>
						<view v-else class="">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'100rpx', height:'40rpx', fontSize:'24rpx', color:'#2E7BFF',backgroundColor:'#E2EBFA', border:'0.5px solid #E7E7E7'}"
							@click="userAttenAndCncelAtten(item, 1)">+关注</u-button>
						</view>
					</view>
				</view>
			    
				<view class="ptb-24-plr-00">
					<u-loadmore bg-color="#ffffff" :status="loadStatus" />
				</view>
			</view>
		    
			<!-- 无数据 -->
			<view v-else>
				<vh-empty :padding-top="270" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_fans.png" :text-bottom="0" text="暂无粉丝~" />
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name: 'my-fans-list',
		
		data() {
			return {
				fansList: [], //关注列表
				page: 1, //第几页
				limit: 10, //每页限制多少条
				totalPage:1, //总页数
				loadStatus: 'nomore', //加载状态
			}
		},
		
		onLoad() {
			this.getFansList()
		},
		
		methods: {
			// 获取粉丝列表
			async getFansList() {
				try{
					let res = await this.$u.api.fansAndAttentionList({
						type: 2, //查询类型：1关注列表，2粉丝列表
						limit: this.limit, //每页最大数
						page: this.page, //分页
					})
					let { total, list } = res.data
					this.page == 1 ? this.fansList = list : this.fansList = [...this.fansList, ...list];
					this.totalPage = Math.ceil(total / this.limit)
					this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
					uni.stopPullDownRefresh() //停止下拉刷新
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 用户关注、取消关注 item = 列表某一项, status = 状态（1 = 关注、2 = 取消关注）
			async userAttenAndCncelAtten( item, status) {
				console.log(item)
				console.log(status)
				try {
					await this.$u.api.userAttenAndCncelAtten({operate_uid: item.uid, status})
					this.fansList.forEach( v => {
						if( v.uid == item.uid ) {
							v.is_follow = status == 1 ? 1 : 0
						}
					}) 
					this.feedback.toast({ title: status == 1 ? '关注成功~' : '取关成功~'})
				}catch(e) {
					
				}
			}
		},
		
		onPullDownRefresh() {
			this.page = 1
			this.totalPage = 1
			this.getFansList()
		},
		
		onReachBottom() {
			if (this.loadStatus !== 'loadmore') return;
			this.loadStatus = 'loading'
			this.page++
			this.getFansList()
		}
	}
</script>

<style scoped></style>