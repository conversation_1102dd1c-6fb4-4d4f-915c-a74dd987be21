<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar title="体验券" />
		
		<!-- 体验券列表 -->
		<view class="">
			<!-- 有数据 -->
			<view v-if="experienceCouponList.length" class="ptb-20-plr-00">
				<view class="p-rela bg-ffffff b-rad-20 o-hid mr-24 mb-20 ml-24" v-for="(item, index) in experienceCouponList" :key="item.id">
					<view v-if="item.status != 1" class="p-abso top-14 right-n-40 w-156 h-50 bg-999999 t-ro-45 d-flex j-center a-center font-28 text-ffffff">{{statusList[item.status - 1]}}</view>
					<image v-else class="p-abso right-0 top-0 w-120 h-40" :src="`${osip}/store_order_confirm/drink.png`" mode="widthFix"></image>
					
					<view class="d-flex a-center pt-38 pr-32 mb-20 pl-32">
						<vh-image :loading-type="2" :src="item.status == 1 ? item.available_img : item.invalid_img" :width="190" :height="86" ></vh-image>
						<view class="w-01 h-76 bg-eeeeee ml-20" />
						
						<view class="ml-18">
							<view class="font-28 font-wei text-3">{{item.c_name}}</view>
							<view class="mt-08 font-24 text-6">有效期：{{item.addtime}}-{{item.endtime}}</view>
						</view>
					</view>
					
					<view class="d-flex j-sb a-center">
						<view class="w-12 h-24 bg-f5f5f5 b-tr-br-rad-10"></view>
						<u-line color="#eee" border-style="dashed"></u-line>
						<view class="w-12 h-24 bg-f5f5f5 b-tl-bl-rad-10"></view>
					</view>
					
					<view class="d-flex j-sb a-center pt-20 pr-32 pb-28 pl-32">
						<view class="font-24 text-9">{{item.store_name}}</view>
						
						<view class="d-flex a-center">
							<view v-if="item.status == 1" class="d-flex a-center">
								<button class="w-152 h-42 bg-ffffff d-flex j-center a-center b-rad-22 b-s-01-c59053 font-22 text-c59053" :data-item="item" open-type="share" @click.stop>赠送好友</button>
								<view class="ml-20">
									<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
									:custom-style="{width:'144rpx', height:'42rpx', fontSize:'22rpx', color:'#E80404', backgroundColor: '#FFF', border:'1rpx solid #E80404'}"
									@click="jump.navigateTo(`/packageE/pages/experience-coupon-detail/experience-coupon-detail?id=${item.id}`)">立即使用</u-button>
								</view>
							</view>
							<!-- <view v-else class="">
								<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
								:custom-style="{width:'144rpx', height:'42rpx', fontSize:'22rpx', color:'#999', backgroundColor: '#FFF', border:'1rpx solid #999'}">删除</u-button>
							</view> -->
						</view>
					</view>
				</view>
			    
				<u-loadmore bg-color="#F5F5F5" :status="loadStatus" />
			</view>
			
			<!-- 暂无数据 -->
			<vh-empty v-else :padding-top="300" :padding-bottom="780" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_cou.png" text="暂无体验券~" />
		</view>
	</view>
</template>

<script>
	export default{
		name:"experience-coupon-list",
		
		data(){
			return{
				osip:'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				statusList:['未使用','已使用','转让中','已转让','已过期'],//体验券状态列表
				experienceCouponList:[],//体验券列表
				page: 1, //第几页
				limit: 10, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //底部加载状态
			}
		},
		
		computed:{
			
		},
		
		onShow() {
			this.init()
		},
		
		methods:{
			// 初始化
			async init() {
				this.page = 1
				await Promise.all([this.getExperienceCouponList()])
			},
			
			// 获取体验券列表
			async getExperienceCouponList() {
				let res = await this.$u.api.experienceCouponList({ page:this.page, limit:this.limit })
				let { total, list } = res.data
				this.page == 1 ? this.experienceCouponList = list : this.experienceCouponList = [...this.experienceCouponList, ...list] 
				this.totalPage = Math.ceil(total / this.limit)
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				uni.stopPullDownRefresh() //停止下拉刷新
			},
		},
		
		onPullDownRefresh() {
			this.init()
		},
		
		onShareAppMessage(res) {
		   console.log(res)
		   
		   if( res.from == 'button' ) {
			   let {id, sid, stare_title, share_img } = res.target.dataset.item
			   return {
				 title: stare_title,
				 path: `/packageE/pages/experience-coupon/experience-coupon?id=${id}&sid=${sid}`,
				 imageUrl: share_img,
			   }
		   }else{
			   return {
			     title: '酒云网 与百万发烧友一起淘酒',
			     path: '/pages/index/index',
			     imageUrl: ''
			   }
		   }
		},
	}
	
</script>

<style>
	@import "../../../common/css/page.css";
</style>
