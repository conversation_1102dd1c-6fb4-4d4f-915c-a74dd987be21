<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 数据信息 -->
		<view v-if="!loading" class="fade-in pb-124">
			<!-- 导航栏 -->
			<vh-navbar title="新建收货地址" :show-border="true" />
			
			<!-- 收货信息 -->
			<view class="ml-40 mr-40">
				<!-- 收货人 -->
				<view class="pt-32 pb-32 d-flex a-center bb-s-01-eeeeee">
					<view class="font-32 font-wei text-3 l-h-40">收货人</view>
					<input class="flex-1 ml-34 font-32 text-3" v-model="consignee" type="text" placeholder="收货人姓名" placeholder-style="color:#999;font-size:32rpx;" />
				</view>
				
				<!-- 手机号码 -->
				<view class="pt-32 pb-32 d-flex a-center bb-s-01-eeeeee">
					<view class="font-32 font-wei text-3 l-h-40">手机号码</view>
					<input class="flex-1 ml-34 font-32 text-3" v-model="phone" type="number" placeholder="收货人手机号" placeholder-style="color:#999;font-size:32rpx;" />
				</view>
				
				<!-- 所在地区 -->
				<view class="pt-32 pb-32 d-flex a-center bb-s-01-eeeeee">
					<view class="font-32 font-wei text-3 l-h-40">所在地区</view>
					<view class="flex-1 ml-34 font-32" @click="showRegion = true">
						<text v-if="province && city && area" class="text-3">{{province}} {{city}} {{area}}</text>
						<text v-else class="text-9">省份、市区、地区</text>
					</view>
				</view>
				
				<!-- 详细地址 -->
				<view class="pt-32 pb-32 d-flex a-center" :class="showClipboard ? '' : 'bb-s-01-eeeeee'">
					<view class="font-32 font-wei text-3 l-h-40">详细地址</view>
					<input class="flex-1 ml-34 font-32 text-3" v-model="address" type="text" placeholder="街道、楼牌号等" placeholder-style="color:#999;font-size:32rpx;" />
				</view>
				
				<!-- 粘贴板 -->
				<view v-show="showClipboard" class="fade-in bg-f7f7f7 p-24 b-rad-10">
					<textarea class="w-p100 h-166 font-28 text-3 l-h-44" :cursor-spacing="130" v-model="clipboardText" type="text" placeholder="粘贴收件人姓名、手机号、收货地址，可快速识别您的收货信息" 
					placeholder-style="color:#999;font-size:24rpx;"></textarea>
					
					<view v-if="$u.trim(clipboardText, 'all') !== ''" class="fade-in d-flex j-end a-center mt-24">
						<view class="font-28 text-6" @click="clipboardText = ''">清除</view>
						<view class="ml-60">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'144rpx', height:'52rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="submitAiText">提交</u-button>
						</view>
					</view>
				</view>
				
				<!-- 地址粘贴板按钮(控制显示隐藏) -->
				<view class="pt-40 pb-40 d-flex j-center a-center" @click="showClipboard = !showClipboard">
					<text class="mr-16 font-32 text-3 font-wei l-h-44">地址粘贴板</text>
					<view v-if="showClipboard" class="fade-in">
						<u-icon name="arrow-up" :size="20" color="#333"></u-icon>
					</view>
					<view v-else class="fade-in-up-medium">
						<u-icon name="arrow-down" :size="20" color="#333"></u-icon>
					</view>
				</view>
			</view>
			
			<!-- 间隔槽 -->
			<vh-gap height="16" bg-color="#f5f5f5"></vh-gap>
			
			<!-- 标签 -->
			<view class="ml-40 mr-40 pt-40 pb-40 d-flex bb-s-01-eeeeee">
				<view class="font-32 text-3 font-wei l-h-44 w-s-now">标签</view>
				
				<view class="ml-86 d-flex flex-wrap a-center">
					<text class="b-rad-30 mb-24 ml-24 font-30 text-3 font-wei l-h-42" :class="label == item ? 'fade-in bg-2e7bff ptb-06-plr-44 text-ffffff' : 'b-s-01-979797 ptb-04-plr-42'" 
					v-for="(item, index) in labelList" :key="index" @click="changeLabel(item)">{{item}}</text>
					<image class="mb-24 ml-24 w-144 h-52" src="https://images.vinehoo.com/vinehoomini/v3/mine/add_label.png" mode="widthFix" @click="jump.navigateTo(routeTable.pEAddressNewLabel)"></image>
				</view>
			</view>
			
			<!-- 设置默认地址 -->
			<view class="d-flex j-sb a-center ml-40 mr-40 pt-36 pb-36">
				<view class="">
					<view class="font-32 text-3 font-wei l-h-44">设置默认地址</view>
					<view class="mt-12 text-6 l-h-34">系统会默认使用该地址作为你的收货地址</view>
				</view>
				<u-switch v-model="isDefault" inactive-color="#666" active-color="#E80404"></u-switch>
			</view>
			
			<!-- 获取微信地址 -->
			<!-- <view class="d-flex j-sb a-center bt-s-01-eeeeee ml-40 mr-40 pt-36 pb-36">
				<view class="">
					<view class="font-32 text-3 font-wei l-h-44">获取微信地址</view>
					<view class="mt-12 text-6 l-h-34">微信小程序可获取默认地址</view>
				</view>
				
				<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
				:custom-style="{width:'200rpx', height:'64rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#1AAD19', border:'none'}" @click="goChooseAddress">去获取</u-button>
			</view> -->
			
			<!-- 选择地区 -->
			<vh-region v-model="showRegion" @confirm="confirmRegion"></vh-region>
			
			<!-- 底部按钮框 -->
			<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022">
				<view class="w-p100 h-p100 d-flex j-center a-center">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="save">保存</u-button>
				</view>
			</view>
		</view>
	    
		<!-- 加载状态 -->
		<vh-loading v-else />
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default{
		name: 'address-add',
		
		data() {
			return {
				isEdit:'', //是否编辑 1 = 编辑
				addressId:'', //地址id
				loading: true, //加载中
				regionList: [], //地区列表
				provinces: [], //省列表
				citys: [], //市列表
				areas: [], //区列表
				consignee: '', //收货人
				phone: '', //手机号码
				province: '', //省
				provinceId: '', //省id
				city: '', //市
				cityId: '', //市id
				area: '', //区
				areaId: '', //区id
				address: '', //详细地址
				clipboardText:'', //ai智能识别text
				showClipboard: false, //是否展示粘贴板
				label:'', //选中的标签
				showRegion: false, //是否显示地区组件
				isDefault: false,//是否选中默认地址
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable','addressInfoState','labelList']),
		},
		
		onLoad(options) {
			this.isEditAddress(options)
			this.getRegionList()
		},
		
		onShow() {
			this.getAddLabel()
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muRegionInfo']),
			
			// 是否编辑地址 options = 页面路径参数
			isEditAddress(options) {
				if(options.isEdit){
					const { id, consignee, consignee_phone, province_name, province_id, city_name, city_id, town_name, town_id, address, label, is_default } = this.addressInfoState //vuex里面的地址信息
					this.isEdit = options.isEdit
					this.addressId = id
					this.consignee = consignee
					this.phone = consignee_phone
					this.province = province_name
					this.provinceId = province_id
					this.city = city_name
					this.cityId = city_id
					this.area = town_name
					this.areaId = town_id
					this.address = address
					this.label = label
					this.isDefault = is_default ? true : false
				}
			},
			
			// // 获取地区列表
			async getRegionList() {
				let res = await this.$u.api.regionList({ isJson:true })
				this.regionList = res.data.list
				this.loading = false
				this.getRegionData()
			},
			
			// 获取省市区数据
			getRegionData() {
				this.regionList.forEach((provinceItem,provinceIndex) => {
					this.provinces.push({ label:provinceItem.name,value:provinceItem.id })
					this.citys.push([])
					this.areas.push([])
					provinceItem.children.forEach((cityItem, cityIndex) => {
						this.citys[provinceIndex].push({ label:cityItem.name,value:cityItem.id })
						this.areas[provinceIndex].push([])
						cityItem.children.forEach((areaItem, areaIndex) => {
							this.areas[provinceIndex][cityIndex].push({ label:areaItem.name,value:areaItem.id })
						})
					})
				})
				this.muRegionInfo({ provinces: this.provinces, citys: this.citys, areas: this.areas})
			},
			
			// 保存地区信息（省市区）
			confirmRegion(e) {
				this.province = e.province.label
				this.provinceId = e.province.value
				this.city = e.city.label
				this.cityId = e.city.value
				this.area = e.area.label
				this.areaId = e.area.value
			},
			
			// 提交ai匹配信息
			async submitAiText(){
				this.feedback.loading('识别中...')
				let res = await this.$u.api.addressAiMatch({address:this.clipboardText})
				let { consignee, consignee_phone, province_id, province_name, city_id, city_name, town_id, town_name, address } = res.data //接口返回的数据
				if( consignee && consignee_phone && province_id && province_name && city_id && city_name && town_id && town_name && address ){
					this.consignee = consignee //收货人
					this.phone = consignee_phone //手机号
					this.provinceId = province_id //省id
					this.province = province_name //省名
					this.cityId = city_id //市id
					this.city = city_name //市名
					this.areaId = town_id //区id
					this.area = town_name //区名
					this.address = address//详细地址
					this.feedback.hideLoading()
				}else{
					this.feedback.toast({ title: '所在地区匹配失败，请修改或手动选择~'})
				}
				// if(res.province_name != '' && res.city_name != '' && res.town_name != ''){
				// 	this.province = res.province_name
				// 	this.city = res.city_name
				// 	this.area = res.town_name
				// 	for(let i of this.regionList){
				// 		if(res.province_name === i.name){
				// 			this.provinceId = i.id
				// 			for(let j of i.children){
				// 				if(res.city_name === j.name){
				// 					this.cityId = j.id
				// 					for(let k of j.children){
				// 						if( res.town_name === k.name){
				// 							this.areaId = k.id
				// 							break
				// 						}
				// 					}
				// 					break
				// 				}
				// 			}
				// 			break
				// 		}
				// 	}
				// }
			},
			
			// 切换标签
			changeLabel(item) {
				this.label = item
			},
			
			// 获取添加标签
			getAddLabel() {
				if(this.labelList.length > 3){
					this.label = this.labelList[this.labelList.length - 1]
				}
			},
			
			// 去获取微信地址
			goChooseAddress() {
				wx.chooseAddress({
				  success: async r => {
					 try{
						this.feedback.loading({ title: '获取中...' })
						let {userName, telNumber, provinceName, cityName, countyName, detailInfo } = r //选择地址返回的数据
					 	let res = await this.$u.api.regionId({ province_name: provinceName, city_name: cityName, town_name: countyName }) //获取省市区id
						let { province_id, city_id, town_id } = res.data //接口返回的数据
						if( province_id && city_id && town_id ) {
							this.consignee = userName //收货人
							this.phone = telNumber //手机号
							this.provinceId = province_id //省id
							this.province = provinceName //省名
							this.cityId = city_id //市id
							this.city = cityName //市名
							this.areaId = town_id //区id
							this.area = countyName //区名
							this.address = detailInfo//详细地址
							this.feedback.hideLoading()
						}else{
							this.feedback.toast({ title: '系统暂无该地址，请修改或手动选择~'})
						}
					 	//   this.province = res.provinceName
					 	//   this.city = res.cityName
					 	//   this.area = res.countyName
						 //  console.log(res)
					 	// console.log(res.userName)
					 	// console.log(res.postalCode)
					 	// console.log(res.provinceName)
					 	// console.log(res.cityName)
					 	// console.log(res.countyName)
					 	// console.log(res.detailInfo)
					 	// console.log(res.nationalCode)
					 	// console.log(res.telNumber)
					 }catch(e){
					 	//TODO handle the exception
					 }
				  }
				})
			},
			
			// 保存
			async save() {
				this.feedback.loading({ title:'提交中...' })
				if(this.$u.trim(this.consignee, 'all') == ''){
					return this.feedback.toast({title:'请输入收货人', icon:'error'})
				}else if(this.$u.trim(this.phone, 'all') == ''){
					return this.feedback.toast({title:'请输入手机号', icon:'error'})
				}else if(this.province == '' || this.city == '' || this.area == ''){
					return this.feedback.toast({title:'请选择地址', icon:'error'})
				}else if(this.provinceId == '' || this.cityId == '' || this.areaId == ''){
					return this.feedback.toast({title:'匹配失败,请修改或手动选择'})
				}else if(this.$u.trim(this.address, 'all') == ''){
					return this.feedback.toast({title:'请输入详细地址', icon:'error'})
				}
				let data = {}
				data.consignee = this.consignee //收件人
				data.consignee_phone = this.phone //收件人手机号
				data.address = this.address //详细地址
				data.label = this.label //标签
				data.province_name = this.province //省
				data.province_id = this.provinceId //省id
				data.city_name = this.city //市
				data.city_id = this.cityId //市id
				data.town_name = this.area //区
				data.town_id = this.areaId //区id
				data.is_default = this.isDefault ? 1 : 0 //是否为默认地址：0否，1是
				if(this.isEdit){
					data.id = this.addressId
					await this.$u.api.updateAddress(data)
					this.feedback.toast({title:'修改成功', icon:'success'})
				}else{
					await this.$u.api.addAddress(data)
					this.feedback.toast({title:'新建成功', icon:'success'})
				}
				setTimeout(()=> {this.jump.navigateBack()}, 1500)
			}
		}
	}
</script>

<style scoped></style>
