<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<vh-navbar v-if="from == ''" :background="{ background: loading ? '#FFFFFF' : '#FBEDE1'}" title="我的等级" />
		
		<!-- 数据内容 -->
		<view v-if="!loading" class="fade-in p-rela">
			<!-- banner -->
			<view class="p-abso top-n-156 w-p100">
				<image class="w-p100" :src="`${osip}/my_grade/bg.png`" mode="widthFix" />
			</view>
			
			<!-- 用户信息 -->
			<view class="p-rela z-01 h-180 d-flex j-center a-center">
				<view class="w-670 h-180 d-flex j-sb a-center ptb-00-plr-48">
					<view class="d-flex a-center">
						<view class="w-88 h-88 bg-ffffff d-flex j-center a-center b-rad-p50 o-hid" @click="image.previewImage([userInfo.avatar_image], 0)">
							<image class="w-84 h-84 b-rad-p50 o-hid" :src="userInfo.avatar_image" mode="aspectFill" />
						</view>
						
						<view class="ml-10">
							<view class="w-max-310 font-32 font-wei text-ffffff l-h-44 text-hidden-1">{{userInfo.nickname}}</view>
							<view v-if="userInfo.user_level == gradeList.length - 1" class="font-24 text-ffffff l-h-34">您已是最高等级</view>
							<view v-else class="font-24 text-ffffff l-h-34">还需{{ gradeList[userInfo.user_level + 1].expstandard - userInfo.exps}}经验值可升级</view>
						</view>
					</view>
					
					<view class="">
						<image class="w-84 h-78" :src="`${osip}/my_grade/lv.png`" mode="aspectFill" />
						<text class="font-64 font-wei text-ffffff">{{userInfo.user_level}}</text>
					</view>
				</view>
			</view>
			
			<!-- 用户等级进阶说明 -->
			<view class="p-rela z-01 b-rad-10 mt-20 ml-24 mr-24 pt-44 pb-110">
				<view class="d-flex j-center a-center">
					<image class="w-82 h-06" :src="`${osip}/my_grade/line.png`" mode="aspectFill"/>
					<view class="font-28 text-3 font-wei l-h-44 ml-24 mr-24">等级进阶说明</view>
					<image class="w-82 h-06 t-ro-y-180" :src="`${osip}/my_grade/line.png`" mode="aspectFill"/>
				</view>
				
				<view class="d-flex mt-52">
					<!-- 时间轴 -->
					<view class="d-flex flex-column a-center ml-24 mt-10">
						<block v-for="(item, index) in gradeList.length - 1" :key="index">
							<view  v-if="index == userInfo.user_level" class="p-rela w-24 h-24 bg-ff9300 d-flex j-center a-center b-rad-p50">
								<view class="w-12 h-12 bg-ffffff b-rad-p50"></view>
								<view class="p-abso top-10 right-n-02 w-06 h-06 bg-ff9300 t-ro-n-45"></view>
							</view>
							<view v-else class="w-16 h-16  b-rad-p50" :class="index <= userInfo.user_level ? 'bg-ff9300' : 'bg-d8d8d8'" />
							<view class="w-04 h-84 op-040" :class="index < userInfo.user_level ? 'bg-ff9300' : 'bg-d8d8d8'" />
						</block>
						<view  v-if="userInfo.user_level == 12" class="p-rela w-24 h-24 bg-ff9300 d-flex j-center a-center b-rad-p50">
							<view class="w-12 h-12 bg-ffffff b-rad-p50"></view>
							<view class="p-abso top-10 right-n-02 w-06 h-06 bg-ff9300 t-ro-n-45"></view>
						</view>
						<view v-else class="w-16 h-16 b-rad-p50 bg-d8d8d8"></view>
					</view>
					
					<!-- 等级值、经验值、葡萄酒达人 -->
					<view class="lv-list-con d-flex flex-column ml-28">
						<view class="d-flex a-center mb-62" v-for="(item, index) in gradeList" :key="index">
							<view class="d-flex a-center">
								<text class="font-28" :class="item.level > userInfo.user_level ? 'text-9' : item.level == userInfo.user_level ? 'text-fb9f22 font-wei' : 'text-6'">Lv{{item.level}}</text>
								<text class="b-rad-18 ml-20 ptb-00-plr-16 font-22 font-wei text-ffffff" :class="item.level <= userInfo.user_level ? 'bg-ff9127' : 'bg-li-16'">LV.{{item.level}}</text>
							</view>
							
							<view class="font-28 ml-34" :class="item.level > userInfo.user_level ? 'text-9' : item.level == userInfo.user_level ? 'text-fb9f22 font-wei' : 'text-6'">{{item.expstandard}}经验值</view>
							
							<view class="d-flex a-center ml-50" v-if="item.level_name">
								<image class="w-34 h-34" :src="`${osip}/my_grade/lv_${item.level <= userInfo.user_level ? 'gold' : 'gray'}.png`" mode="widthFix" />
								<text class="font-28 ml-04" :class="item.level > userInfo.user_level ? 'text-9' : item.level == userInfo.user_level ? 'text-fb9f22 font-wei' : 'text-6'">{{item.level_name}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 按钮 -->
			<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-feffff b-sh-00021200-022 d-flex j-center a-center" v-safeBeautyBottom="$safeBeautyBottom">
				<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
				:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#FF9127', border:'none'}"
				@click="getMoreExp()">获取更多经验值</u-button>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<vh-skeleton v-else :has-navigation-bar="true" bg-color="#FFF" :show-loading="false" />
	</view>
</template>

<script>
	export default{
		name:"my-grade",
		
		data(){
			return{
				osip:'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				loading: true, //加载状态
				from:'', //从哪个端进入 1 = 安卓、2 = ios"、3 = pc
				userInfo:{}, //用户信息
				gradeList:[], //我的等级列表
			}
		},
		
		onLoad(options) {
			this.system.setNavigationBarBlack()
			if(options.from) this.from = options.from 
		},
		
		onShow() {
			this.init()
		},
		
		methods: {
			// 初始化（用户信息、等级列表）
			async init() {
				if(this.login.isLogin(this.from)){
					await this.getUserLevelInfo()
					this.loading = false
				}
			},
			
			// 获取用户等级信息
			async getUserLevelInfo() {
				let res = await this.$u.api.userLevelInfo()
				this.userInfo = res.data.user
				this.gradeList = res.data.list
			},
			
			// 获取更多经验
			getMoreExp() {
				if(this.comes.isFromApp(this.from)) {
					wineYunJsBridge.openAppPage({
						client_path: { "ios_path":"DailyCheckViewController", "android_path":"com.stg.rouge.activity.EveryTaskCenterActivity" },
						ad_path_param: [
							{ 
								"ios_key":"login", "ios_val":"1",  
							    "android_key":"login", "android_val":"1" 
							}
						]
					});
				}else{
					this.jump.navigateTo(`/packageE/pages/daily-tasks/daily-tasks`)
				}
			}
		}
	}
</script>

<style scoped>
	.lv-list-con>view:nth-child(1) {
		margin-top: 4rpx;
	}
</style>
