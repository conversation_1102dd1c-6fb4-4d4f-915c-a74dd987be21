<template>
	<view class="content">
		<!-- 导航栏 -->
		<view class="bb-s-01-eeeeee">
			<u-navbar back-icon-color="#333" title="新建收货地址" title-size="36" :title-bold="true" title-color="#333"></u-navbar>
		</view>
		
		<!-- 输入标签名 -->
		<view class="d-flex a-center bb-s-01-eeeeee ml-32 mr-32 pt-42 pb-20">
			<input class="flex-1 ml-34 text-3 font-32" v-model="label" type="text" :maxlength="5" placeholder="请输入标签名称，最多5个字" placeholder-style="color:#999;font-size:32rpx;" />
		</view>
		
		<!-- 提交按钮 -->
		<view class="d-flex j-center a-center mt-100 mr-32 ml-32">
			<view v-if="this.$u.trim(this.label, 'all') !== ''">
				<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
				:custom-style="{width:'670rpx', height:'80rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="submit">提交</u-button>
			</view>
			<view v-else class="w-670 h-80 d-flex j-center a-center b-rad-40 bg-fce4e3 font-28 text-ffffff font-wei">提交</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		name: 'new-label',
		
		data(){
			return{
				label:'',//标签内容
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['labelList']),
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muLabelList']),
			
			// 提交
			submit() {
				let canJump = true
				for(let i of this.labelList){
					if(i == this.label){
						this.feedback.toast({title:'标签不能重复', icon:'error'})
						canJump = false
						break;
					}
				}
				if(!canJump) return
				this.labelList.push(this.label)
				this.muLabelList(this.labelList)
				this.feedback.toast({title:'提交成功', icon:'success'})
				setTimeout(()=>{this.jump.navigateBack()}, 1500)
			}
		}
		
	}
</script>

<style scoped>
</style>
