<template>
  <view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
    <!-- 导航栏 -->
    <vh-navbar :title="`${currentTabs == 6 ? '退款/售后' : showSearch ? '' : '我的订单'}`" :show-border="true">
      <template v-if="currentTabs != 6 && !showSearch">
        <view class="fade-in ml-10 font-30 text-e80404 w-s-now" @click="jump.appAndMiniJump(1, routeTable.pBOrderInvoice, $vhFrom)"
          >开发票</view
        >
        <image slot="right" class="fade-in p-24 w-44 h-44" :src="`${osip}/comm/ser_black.png`" @click="search"></image>
      </template>

      <view v-if="showSearch" class="fade-in d-flex a-center">
        <view class="p-12" @click="closeSearch">
          <u-icon name="close" :size="34" color="#333"></u-icon>
        </view>
        <view class="bg-f7f7f7 d-flex j-sb a-center b-rad-40 pl-26 pr-26">
          <view class="p-rela w-280 h-68 d-flex a-center">
            <image class="w-44 h-44" :src="`${osip}/comm/ser_gray.png`" mode="aspectFill" />
            <input
              class="w-170 h-p100 ml-10 font-28 text-3"
              type="text"
              v-model="keyword"
              :placeholder="'请输入关键字'"
              placeholder-style="color:#999;font-size:28rpx;"
              @confirm="search"
            />
            <view
              v-show="$u.trim(keyword, 'all') !== ''"
              class="p-abso right-0 top-0 w-40 h-p100 d-flex j-center a-center"
              @click="keyword = ''"
            >
              <image
                class="w-40 h-40"
                src="https://images.vinehoo.com/vinehoomini/v3/comm/del_gray.png"
                mode="aspectFill"
              />
            </view>
          </view>
        </view>
        <view class="p-12 font-28 font-wei text-6 w-s-now" @click="search">搜索</view>
      </view>
    </vh-navbar>

    <!-- tabs选项栏 -->
    <view
      v-if="currentTabs != 6 && !showSearch"
      class="p-stic z-980"
      :style="{ top: system.navigationBarHeight() + 'px' }"
    >
      <u-tabs
        :list="tabList"
        :current="currentTabs"
        :height="92"
        :font-size="28"
        inactive-color="#333"
        active-color="#E80404"
        :bar-width="36"
        :bar-height="8"
        :bar-style="{ background: 'linear-gradient(214deg, #FF8383 0%, #E70000 100%)' }"
        :is-scroll="false"
        @change="changeTabs"
      />
    </view>

    <!-- 数据加载完成 -->
    <view v-if="!loading" class="fade-in">
      <!-- 订单列表 -->
      <view class="">
        <!-- 订单列表不为空 -->
        <view v-if="myOrderList.length > 0" class="pb-20">
          <view
            class="bg-ffffff b-rad-16 mt-20 mr-24 mb-20 ml-24 pl-24 pr-24"
            v-for="(item, index) in myOrderList"
            :key="item.order_no"
            @click="openOrderDetail(item)"
          >
            <view
              class="d-flex j-sb a-center pt-32 pb-32 bb-s-01-eeeeee"
              :class="item.status == 4 || (item.status == 8 && currentTabs != 6) ? 'op-050' : ''"
            >
              <text class="font-24 text-3" @click.stop="copy.copyText(item.order_no)">订单号：{{ item.order_no }}</text>
              <OrderListStatusText :item="item" />
            </view>

            <!-- 订单商品信息 -->
            <view
              class="bb-s-01-eeeeee pt-08 pb-28"
              :class="item.status == 4 || (item.status == 8 && currentTabs != 6) ? 'op-050' : ''"
            >
              <OrderListGoodsItem :item="item" />
            </view>

            <!-- 底部操作按钮 -->
            <view class="">
              <!-- 待支付 -->
              <view v-if="![4].includes(item.order_type) && item.status == 0" class="d-flex j-sb a-center pt-28 pb-28">
                <view class="d-flex">
                  <view class="font-24 text-e80404">剩余：</view>
                  <vh-count-down
                    :show-days="false"
                    :showHours="false"
                    :timestamp="item.countdown"
                    separator="zh"
                    bg-color="transparent"
                    color="#E80404"
                    @end="myOrderList[index].countdown = 0"
                  ></vh-count-down>
                </view>

                <view class="">
                  <!-- 删除订单 -->
                  <OrderListBtnDelete v-if="item.countdown == 0" @click="cancelOrDeleteOrder(item, index, 2)" />
                  <view v-else class="d-flex a-center">
                    <!-- 取消订单 -->
                    <template v-if="![11].includes(item.order_type)">
                      <OrderListBtnCancel @click="cancelOrDeleteOrder(item, index, 1)" />
                    </template>
                    <view v-if="item.is_replace_pay != 1" class="ml-20">
                      <u-button
                        :disabled="item.countdown == 0"
                        shape="circle"
                        :hair-line="false"
                        :ripple="true"
                        ripple-bg-color="#FFF"
                        :custom-style="{
                          width: '148rpx',
                          height: '52rpx',
                          fontSize: '24rpx',
                          color: '#FFF',
                          backgroundColor: item.countdown == 0 ? '#FCE4E3' : '#E80404',
                          border: 'none',
                        }"
                        @click="immediatePayment(item)"
                        >立即支付</u-button
                      >
                    </view>
                  </view>
                </view>
              </view>

              <!-- 已支付 -->
              <view v-if="![4].includes(item.order_type) && item.status == 1" class="d-flex j-end a-center pt-28 pb-28">
                <!-- 申请售后 -->
                <OrderListBtnAfterSale :item="item" @click="applyAfterSale(item, item.after_sale_status)" />
                <!-- 提醒发货 -->
                <OrderListBtnRemindShipment @click="remindShipment(item.predict_time)" />
                <!-- 再来一单 -->
                <OrderListBtnOneMoreOrder :orderType="item.order_type" @click="oneMoreOrder(item)" />
              </view>

              <!-- 已发货 -->
              <view
                v-if="item.status == 2"
                class="ptb-28-plr-00"
                :class="[4, 11].includes(item.order_type) ? 'flex-e-c' : 'flex-sb-c'"
              >
                <view v-if="![4, 11].includes(item.order_type)" class="p-rela" @click.stop="showMore(index)">
                  <text class="font-24 text-6">更多</text>
                  <view
                    v-if="item.showMore"
                    class="fade-in p-abso bottom-n-104 left-0 z-04 bg-ffffff b-sh-00042800-012 b-rad-10 font-26 text-6"
                  >
                    <view
                      class="p-rela p-24 bb-s-01-eeeeee w-s-now"
                      @click.stop="applyAfterSale(item, item.after_sale_status)"
                    >
                      {{ item.after_sale_status == 2 ? '售后详情' : '申请售后' }}
                      <view
                        class="p-abso top-0 left-0 w-0 h-0 t-ro-n-45 br-s-20-ffffff bb-s-20-transp bl-s-20-transp"
                      ></view>
                    </view>
                  </view>
                </view>
                <view class="d-flex">
                  <!-- 申请售后 -->
                  <template v-if="[11].includes(item.order_type)">
                    <OrderListBtnAfterSale :item="item" @click="applyAfterSale(item, item.after_sale_status)" />
                  </template>
                  <!-- 查看物流 -->
                  <OrderListBtnViewLogistics @click="viewLogistics(item)" />
                  <!-- 再来一单 -->
                  <OrderListBtnOneMoreOrder :orderType="item.order_type" @click="oneMoreOrder(item)" />
                  <!-- 确认收货 -->
                  <OrderListBtnConfirmReceipt @click="confirmReceipt(item)" />
                </view>
              </view>

              <!-- 已完成 -->
              <view v-if="item.status == 3" class="pt-28 pb-28">
                <view class="flex-sb-c">
                  <view class="flex-c-c">
                    <view v-if="item.leftBtnList.length" class="p-rela">
                      <view class="font-28 text-6" @click.stop="showMore(index)">更多</view>
                      <view
                        v-if="item.showMore"
                        class="fade-in p-abso left-0 z-04 bg-ffffff b-sh-00042800-012 b-rad-10 font-26 text-6"
                      >
                        <template v-for="type in item.leftBtnList">
                          <view
                            v-if="type === 1"
                            :key="type"
                            class="p-rela p-24 w-s-now"
                            @click.stop="applyAfterSale(item, item.after_sale_status)"
                            >{{ item.after_sale_status == 2 ? '售后详情' : '申请售后' }}</view
                          >
                          <view
                            v-else-if="type === 2"
                            :key="type"
                            class="p-rela p-24 w-s-now"
                            @click.stop="cancelOrDeleteOrder(item, index, 2)"
                            >删除订单</view
                          >
                        </template>
                        <view
                          class="p-abso top-0 left-0 w-0 h-0 t-ro-n-45 br-s-20-ffffff bb-s-20-transp bl-s-20-transp"
                        ></view>
                      </view>
                    </view>
                  </view>
                  <view class="flex-c-c">
                    <template v-for="type in item.rightBtnList">
                      <OrderListBtnAfterSale
                        v-if="type === 1"
                        :key="type"
                        :item="item"
                        @click="applyAfterSale(item, item.after_sale_status)"
                      />
                      <OrderListBtnDelete
                        v-else-if="type === 2"
                        :key="type"
                        @click="cancelOrDeleteOrder(item, index, 2)"
                      />
                      <u-button
                        v-else-if="type === 3"
                        :key="type"
                        shape="circle"
                        :hair-line="false"
                        :ripple="true"
                        ripple-bg-color="#FFF"
                        :custom-style="{
                          marginLeft: '20rpx',
                          width: '148rpx',
                          height: '52rpx',
                          fontSize: '24rpx',
                          color: '#666',
                          border: '1rpx solid #666',
                        }"
                        @click="onJumpAuctionGoodsCreate(item)"
                        >一键转拍</u-button
                      >
                      <OrderListBtnWritingWineComment
                        v-else-if="type === 4"
                        :key="type"
                        :item="item"
                        :rabbitHeadCount="rabbitHeadCount"
                        @click="
                        jump.appAndMiniJumpBD(1, `${routeTable.pCWineCommentSend}?orderNo=${item.order_no}`, 7, 3, 701000, 3, $vhFrom)
                          // jump.navigateToBD(`${routeTable.pCWineCommentSend}?orderNo=${item.order_no}`, 7, 3, 701000, 3)
                        "
                      />
                      <OrderListBtnAuctionEvaluate
                        v-else-if="type === 5"
                        :key="type"
                        :item="item"
                        @click="
                          // jump.navigateTo(
                          //   `${routeTable.pHAuctionOrderEvaluateNew}?orderNo=${item.order_no}&goodsImage=${item.goodsInfo[0].goods_img}`
                          // )
                          jump.appAndMiniJump(1, `${routeTable.pHAuctionOrderEvaluateNew}?orderNo=${item.order_no}&goodsImage=${item.goodsInfo[0].goods_img}`, $vhFrom)
                        "
                      />
                      <OrderListBtnOneMoreOrder
                        v-else-if="type === 6"
                        :key="type"
                        :orderType="item.order_type"
                        @click="oneMoreOrder(item)"
                      />
                    </template>
                  </view>
                </view>
              </view>

              <!-- 拼团中 -->
              <view
                v-if="![4, 11].includes(item.order_type) && item.status == 5"
                class="d-flex j-sb a-center pt-28 pb-28"
              >
                <view class="font-24 text-3"
                  >还差<text class="text-e80404">{{ item.group_last_num }}</text
                  >人拼成</view
                >
                <view class="">
                  <u-button
                    shape="circle"
                    :hair-line="false"
                    :ripple="true"
                    ripple-bg-color="#FFF"
                    :custom-style="{
                      width: '148rpx',
                      height: '52rpx',
                      backgroundColor: '#E80404',
                      fontSize: '24rpx',
                      color: '#FFF',
                      border: 'none',
                    }"
                    @click="openOrderDetail(item)"
                    >分享链接</u-button
                  >
                </view>
              </view>

              <!-- 已暂存 -->
              <view
                v-if="![4, 11].includes(item.order_type) && item.status == 6"
                class="d-flex j-end a-center pt-28 pb-28"
              >
                <!-- 申请售后 -->
                <OrderListBtnAfterSale :item="item" @click="applyAfterSale(item, item.after_sale_status)" />
                <!-- 再来一单 -->
                <OrderListBtnOneMoreOrder :orderType="item.order_type" @click="oneMoreOrder(item)" />
                <!-- 立即发货 -->
                <OrderListBtnShipNow @click="shipNow(item)" />
              </view>

              <!-- 退款中 -->
              <view v-if="![4].includes(item.order_type) && item.status == 7" class="d-flex j-end a-center pt-28 pb-28">
                <OrderListBtnAfterSale :item="item" @click="applyAfterSale(item, item.after_sale_status)" />
              </view>

              <!-- 已取消、退款成功 -->
              <view v-if="item.status == 4 || item.status == 8" class="d-flex j-end a-center pt-28 pb-28">
                <template v-if="item.order_type != 4 && item.status == 8 && item.after_sale_status == 2">
                  <OrderListBtnAfterSale :item="item" @click="applyAfterSale(item, item.after_sale_status)" />
                </template>
                <OrderListBtnDelete @click="cancelOrDeleteOrder(item, index, 2)" />
              </view>
            </view>
          </view>

          <u-loadmore :status="loadStatus" />
        </view>

        <!-- 订单列表为空 -->
        <view class="" v-if="myOrderList.length == 0">
          <!-- 占位图 -->
          <vh-empty
            :padding-top="52"
            :padding-bottom="100"
            :image-src="ossIcon(`/empty/emp_order.png`)"
            text="暂无订单"
          />

          <!-- 猜你喜欢文字标题 -->
          <vh-split-line
            :padding-top="52"
            :padding-bottom="32"
            :margin-left="10"
            :margin-right="10"
            text="猜你喜欢"
            :font-bold="true"
            :font-size="36"
            text-color="#333333"
            :show-image="true"
            :image-src="ossIcon(`/comm/guess_love.png`)"
          />

          <!-- 猜你喜欢列表 -->
          <vh-goods-recommend-list />
        </view>
      </view>

      <!-- 弹框 -->
      <view class="">
        <!-- 通知发货弹框 -->
        <u-modal
          v-model="showNoticeShipmentPop"
          :show-title="false"
          content=""
          :width="490"
          :show-confirm-button="false"
          :show-cancel-button="true"
          cancel-text="知道了"
          :cancel-style="{ fontSize: '28rpx', color: '#999' }"
        >
          <view class="pt-86 pb-64">
            <view class="d-flex j-center a-center">
              <image class="w-264 h-184" :src="ossIcon(`/comm/succ_red.png`)" mode="aspectFill" />
            </view>

            <view class="d-flex flex-column j-center a-center mt-30 l-h-44">
              <text class="font-28 text-3">提交成功</text>
              <text class="font-28 text-3">我们将在48小时内发货</text>
            </view>
          </view>
        </u-modal>

        <!-- 再来一单弹框 -->
        <sp-one-more-order-mask
          :show="showOneMoreOrderMask"
          :customerInfo="customerInfo"
          @click="showOneMoreOrderMask = false"
          @reminder="shelfReminder"
        />

        <!-- 再来一单心愿模态框 -->
        <sp-one-more-order-wish-modal v-model="showOneMoreOrderWishModal" />

        <!-- 再来一单消息模态框 -->
        <sp-one-more-order-message-modal v-model="showOneMoreOrderMessageModal" />
      </view>
    </view>

    <!-- 骨架屏 -->
    <vh-skeleton v-else :type="11" bg-color="#f5f5f5" />
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import auctionMyCreateDraftsUtil from '@/common/js/utils/auctionMyCreateDrafts'
export default {
  name: 'my-order',

  data() {
    return {
      osip: 'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
      loading: true, //数据是否加载完成
      showSearch: false, //是否显示搜索
      keyword: '', //搜索关键字
      tabList: [
        //状态栏选项
        { name: '全部' },
        { name: '待支付' },
        { name: '待拼团' },
        { name: '待发货' },
        { name: '待收货' },
        { name: '已完成' },
      ],
      currentTabs: 0, //当前选中tabs
      shareInfo: {},
      hasGotOrderList: 0, //是否请求过订单列表
      myOrderList: [], //我的订单列表
      page: 1, //当前页
      limit: 10, //每页限制多少条
      totalPage: 1, //总页数
      loadStatus: 'loadmore', //加载状态
      // 再来一单
      wishId: '', //心愿清单id
      customerInfo: {}, //客服信息

      // 弹框
      showNoticeShipmentPop: false, //是否显示提醒发货弹框
      showOneMoreOrderMask: false, //是否显示再来一单遮罩层
      showOneMoreOrderWishModal: false, //是否显示再来一单心愿清单模态框
      showOneMoreOrderMessageModal: false, //是否显示再来一单消息通知模态框
      rabbitHeadCount: 0,
      isLoadedRHC: false,
      isAuctionSeller: 0,
      isGetIsAuctionSeller: 0,
    }
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable', 'afterSaleGoodsInfo', 'logisticsInfo']),

    // 获取导航栏高度
    getNavigationBarHeight() {
      return this.system.getSysInfo().statusBarHeight + 48
    },
  },

  onLoad(options) {
    this.getShareInfo()
    const { myOrderStatus, status } = options
    // 兼容payment的return_url
    if (myOrderStatus) {
      this.currentTabs = +myOrderStatus
    } else if (status) {
      this.currentTabs = +status
    }
    this.system.setNavigationBarBlack()
  },

  onShow() {
    this.getIsAuctionSeller().then(() => {
      this.init()
    })
  },

  methods: {
    // Vuex mapMutations辅助函数
    ...mapMutations(['muAfterSaleGoodsInfo', 'muLogisticsInfo', 'muPayInfo']),
    async getShareInfo() {
      const res = await this.$u.api.getShareInfo()
      console.log(res.data)
      this.shareInfo = res.data
    },
    async getIsAuctionSeller() {
      if (this.isGetIsAuctionSeller) return
      if (this.login.isLogin()) {
        try {
          const {
            data: { is_auction_seller = 0 },
          } = await this.$u.api.userSpecifiedData({ field: 'is_auction_seller' })
          this.isAuctionSeller = +is_auction_seller
          this.isGetIsAuctionSeller = 1
        } catch (e) {}
      }
    },

    // 初始化
    async init() {
      this.showOneMoreOrderMask = false
      if (this.login.isLogin()) {
        this.page = 1
        await Promise.all([this.getMyOrderList(), this.initRabbitHeadCount()])
        this.loading = false
      }
    },

    // 切换状态栏
    changeTabs(index) {
      this.currentTabs = index
      this.page = 1
      this.getMyOrderList()
    },

    // 获取我的订单列表
    async getMyOrderList() {
      if (this.hasGotOrderList) this.feedback.loading()
      let {
        data: { list, total },
      } = await this.$u.api.myOrderList({
        keyword: this.keyword, //搜索关键字
        page: this.page, //页码
        limit: this.limit, //每页限制
        type: this.currentTabs, //当前tabs
      })
      list.forEach((v) => {
        v.showMore = false
        const btnList = this.getBottomBtnList(v)
        v.rightBtnList = btnList.slice(-3)
        v.leftBtnList = btnList.slice(0, -3).reverse()
      }) //显示更多
      this.page == 1 ? (this.myOrderList = list) : (this.myOrderList = [...this.myOrderList, ...list])
      this.totalPage = Math.ceil(total / this.limit)
      this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
      // this.myOrderList[0].status = 3
      // this.myOrderList[2].order_type = 11
      // this.myOrderList[2].goodsInfo[0].periods_type = 11
      this.hasGotOrderList = 1
      uni.stopPullDownRefresh() //停止下拉刷新
      this.feedback.hideLoading()
    },
    // 1=申请售后 2=删除订单 3=一键转拍 4=写酒评 5=评价 6=再来一单
    getBottomBtnList(item) {
      switch (item.status) {
        case 3:
          return this.getFinishBottomBtnList(item)
        default:
          return []
      }
    },
    getFinishBottomBtnList(item) {
      const list = []
      if (item.order_type === 11) {
        list.push(2)
        if (item.is_liquor && this.isAuctionSeller) list.push(3)
        if (!item.is_comment) list.push(5)
        return list
      } else if (item.order_type === 4) {
        list.push(1, 2)
        if (!item.is_comment) list.push(4)
        return list
      } else {
        list.push(1, 2)
        if (item.order_type !== 2 && item.is_liquor && this.isAuctionSeller) list.push(3)
        if (!item.is_comment) list.push(4)
        list.push(6)
        return list
      }
    },

    async initRabbitHeadCount() {
      if (this.isLoadedRHC) return
      try {
        const res = await this.$u.api.getWineCommentRHCount()
        this.rabbitHeadCount = res.data.wine_rabbit
        this.isLoadedRHC = true
      } catch (e) {
        this.rabbitHeadCount = 0
      }
    },

    // 搜索
    search() {
      this.showSearch = true
      this.init()
    },

    // 确认搜索
    confirmSearch() {
      this.init()
    },

    // 关闭搜索
    closeSearch() {
      this.keyword = ''
      this.showSearch = false
      this.init()
    },

    // 打开订单详情 item = 列表某一项
    openOrderDetail(item) {
      if (item.status === 8) {
        // 退款成功，跳转售后详情
        if (item.group_status === 3) {
          this.jump.appAndMiniJump(1, `${this.routeTable.pBOrderDetail}?orderNo=${item.order_no}`, this.$vhFrom)
          // this.jump.navigateTo(`${this.routeTable.pBOrderDetail}?orderNo=${item.order_no}`)
        } else {
          this.applyAfterSale(item, 2)
        }
        return
      }
      if (this.currentTabs === 6 && item.after_sale_status === 2) {
        //售后详情
        this.applyAfterSale(item, 2)
      } else {
        //订单详情
        if (item.order_type == 4) {
          //跳转兔头订单详情
          this.jump.appAndMiniJump(1, `${this.routeTable.pBRabbitHeadOrderDetail}?orderNo=${item.order_no}`, this.$vhFrom)
          // this.jump.navigateTo(`${this.routeTable.pBRabbitHeadOrderDetail}?orderNo=${item.order_no}`)
        } else if (item.order_type == 11) {
          //跳转拍卖订单详情
          this.jump.appAndMiniJump(1, `${this.routeTable.pHAuctionOrderDetail}?orderNo=${item.order_no}`, this.$vhFrom)
          // this.jump.navigateTo(`${this.routeTable.pHAuctionOrderDetail}?orderNo=${item.order_no}`)
        } else {
          //跳转普通订单详情
          this.jump.appAndMiniJump(1, `${this.routeTable.pBOrderDetail}?orderNo=${item.order_no}`, this.$vhFrom)
          // this.jump.navigateTo(`${this.routeTable.pBOrderDetail}?orderNo=${item.order_no}`)
        }
      }
    },

    // 立即支付 item = 订单每一条
    immediatePayment(item) {
      if (item.order_type === 11) {
        this.jump.appAndMiniJump(1, `${this.routeTable.pBPayment}?orderNo=${item.order_no}&payPlate=4`, this.$vhFrom)
        // this.jump.navigateTo(`${this.routeTable.pBPayment}?orderNo=${item.order_no}&payPlate=4`)
      } else {
        const is_cross = item.order_type == 2 ? 1 : 0
        this.muPayInfo({ ...item, is_cross })
        // if(this.$vhFrom == 'next'){
        //   uni.setStorageSync('nextpayInfo',{ ...item, is_cross });
        // }
        // this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1)
        if(this.$vhFrom == 'next'){
          // uni.setStorageSync('nextpayInfo',payInfo);
          if(is_cross == 1){
            uni.setStorageSync('nextpayInfo',{ ...item, is_cross });
            this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1)
          } else {
            this.jump.pullAppPay(this.$vhFrom, {main_order_no:item.order_no})
          }
         
        } else {
          this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1)
        }
        // this.jump.redirectTo(this.routeTable.pBPayment)
      }
    },

    // 申请售后/售后详情 item = 订单的每一条数据、type = 类型 0、1 = 申请售后、2 = 售后详情
    applyAfterSale(item, type) {
      console.log('-----------------------我是申请售后事件')
      console.log(JSON.stringify(item))
      console.log(type)
      this.muAfterSaleGoodsInfo(item)
      if(this.$vhFrom == 'next'){
          uni.setStorageSync('SaleGoodsInfo',item);
        }
      type == 2
        ? this.jump.appAndMiniJump(1, this.routeTable.pBAfterSaleDetail, this.$vhFrom)
        : this.jump.appAndMiniJump(1, this.routeTable.pBAfterSaleGoodsService, this.$vhFrom)
    },

    // 查看物流 item = 订单列表某一项
    viewLogistics(item) {
      let { goodsInfo, express_type, express_number } = item
      this.muLogisticsInfo({ image: goodsInfo[0].goods_img, expressType: express_type, logisticCode: express_number })
      // this.jump.navigateTo(this.routeTable.pBLogisticsDetail)
      this.jump.appAndMiniJump(1, this.routeTable.pBLogisticsDetail, this.$vhFrom)
    },

    // 这是提醒发货 predictTime = 预计发货时间（形如 2022-04-06 16:43:00）
    remindShipment(predictTime) {
      let title = ''
      this.date.getSeconds(predictTime) > 0
        ? (title = '您的订单处于正常时效内，请耐心等待~')
        : (title = '已催促仓库尽快发货，请耐心等待~')
      this.feedback.toast({ title })
    },

    // 立即发货 item = 列表每一项
    shipNow(item) {
      this.feedback.showModal({
        content: `确认立即发货吗？`,
        confirm: async () => {
          try {
            let data = {}
            data.order_no = item.order_no
            data.order_type = item.order_type
            data.is_ts = 0
            console.log(data)
            let res = await this.$u.api.updateOrderStatus(data)
            this.feedback.toast({ title: '确认发货成功~', icon: 'success' })
            this.myOrderList.forEach((v) => {
              if (v.order_no == item.order_no) {
                v.status = 1
              }
            })
          } catch (e) {}
        },
      })
    },

    // 确认收货 item = 订单列表每一项
    async confirmReceipt(item) {
      this.feedback.showModal({
        content: `您确认收到货了吗？`,
        confirm: async () => {
          try {
            console.log('======================我是确认收货')
            let data = {}
            data.sub_order_no_str = item.order_no
            console.log(data)
            let res = await this.$u.api.orderReceipt(data)
            this.feedback.toast({ title: '确认收货成功~', icon: 'success' })
            this.myOrderList.forEach((v, i) => {
              if (v.order_no == item.order_no) {
                console.log(v, i)
                v.status = 3
                this.myOrderList.splice(i, 1)
              }
            })
          } catch (e) {}
        },
      })
    },

    // 取消/删除订单 item = 要删除的订单信息 index 列表索引 type = 类型 1 = 取消订单、2 = 删除订单
    cancelOrDeleteOrder(item, index, type) {
      console.log(item)
      console.log(index)
      console.log(type)
      let tips = type == 1 ? '取消' : '删除'
      this.feedback.showModal({
        content: `确认${tips}该订单吗？`,
        confirm: async () => {
          try {
            await this.$u.api.cancelDeleteOrder({ type, order_no: item.order_no })
            this.myOrderList.splice(index, 1)
            this.feedback.toast({ title: `${tips}成功`, icon: 'success' })
          } catch (e) {}
        },
      })
    },

    // 显示更多
    showMore(index) {
      console.log(index)
      this.myOrderList[index].showMore = !this.myOrderList[index].showMore
      this.myOrderList.map((val, idx) => {
        if (index != idx) this.myOrderList[idx].showMore = false
      })
    },

    // 再来一单 item = 订单列表某一项
    async oneMoreOrder(item) {
      const { period, goods_img, goods_title } = item.goodsInfo[0]
      try {
        console.log(item)
        // this.feedback.toast({ title: '接口暂无数据支持~' })
        const {
          data: { id, rid },
        } = await this.$u.api.oneMoreOrder({ sub_order_no: item.order_no })
        if (rid) {
          // 心愿清单
          this.customerInfo = { id: period, title: goods_title, img: goods_img }
          this.showOneMoreOrderMask = true
          this.wishId = rid
        } else {
          if (id) this.jump.appAndMiniJump(1, `${this.routeTable.pgGoodsDetail}?id=${id}`, this.$vhFrom)
        }
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 上架提醒
    async shelfReminder() {
      try {
        this.feedback.loading()
        const res = await this.$u.api.addWishList({ id: this.wishId, is_msg: 1 })
        this.showOneMoreOrderMask = false
        this.showOneMoreOrderWishModal = true
        this.wishId = ''
        this.feedback.hideLoading()
      } catch (e) {
        //TODO handle the exception
      }
    },
    onJumpAuctionGoodsCreate(item) {
      const draftId = auctionMyCreateDraftsUtil.getDraftIdByOrderNo(item.order_no)
      if (draftId) {
        this.jump.appAndMiniJump(1, `${this.$routeTable.pHAuctionGoodsCreateNew}?draftId=${draftId}`, this.$vhFrom)
        // this.jump.navigateTo(`${this.$routeTable.pHAuctionGoodsCreateNew}?draftId=${draftId}`)
        return
      }
      this.jump.appAndMiniJump(1, `${this.$routeTable.pHAuctionGoodsCreateNew}?isWine=1&period=${item.goodsInfo[0].period}&periodType=${item.goodsInfo[0].periods_type}&mainOrderNo=${item.order_no}&orderType=${item.order_type}&packageId=${item.goodsInfo[0].package_id}&buyingPrice=${item.payment_amount}`, this.$vhFrom)
      // this.jump.navigateTo(
      //   `${this.$routeTable.pHAuctionGoodsCreateNew}?isWine=1&period=${item.goodsInfo[0].period}&periodType=${item.goodsInfo[0].periods_type}&mainOrderNo=${item.order_no}&orderType=${item.order_type}&packageId=${item.goodsInfo[0].package_id}&buyingPrice=${item.payment_amount}`
      // )
    },
  },

  onPullDownRefresh() {
    this.init()
  },

  onShareAppMessage(res) {
    console.log(res)
    if (res.from == 'menu') {
      //顶部菜单分享
      return {
        title: '酒云网 与百万发烧友一起淘酒',
        path: '/pages/index/index',
      }
    } else {
      const { goodsInfo, order_type, group_id } = res.target.dataset.item //拿到拼团订单信息
      let url = `/pages/goods-detail/goods-detail?id=${goodsInfo[0].period}&channel=${order_type}&groupId=${group_id}`
      return {
        //按钮分享
        title: this.shareInfo.main_title,
        path: `/pages/goods-detail/goods-detail?id=${goodsInfo[0].period}&channel=${order_type}&groupId=${group_id}`,
        imageUrl: this.shareInfo.share_image,
      }
    }
  },

  onBackPress() {
    console.log('------我是返回')
  },

  onReachBottom() {
    if (this.page == this.totalPage || this.totalPage == 0) return
    this.loadStatus = 'loading'
    this.page++
    this.getMyOrderList()
  },
}
</script>

<style>
@import '../../../common/css/page.css';
</style>
