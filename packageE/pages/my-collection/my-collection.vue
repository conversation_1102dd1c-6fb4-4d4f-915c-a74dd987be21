<template>
  <view :class="currentTabType === TabType.AuctionGoods ? '' : 'pb-124'">
    <!-- 导航栏 -->
    <vh-navbar height="46" title="我的收藏">
      <view v-if="rightTextStatus" slot="right" class="flex-c-c w-108 h-92 font-30 text-3" @click="onRightTextClick">{{
        rightText
      }}</view>
    </vh-navbar>

    <!-- 收藏切换栏 -->
    <view class="my-collection__tabs p-stic z-980 bt-s-02-eeeeee" style="top: 46px">
      <u-tabs
        :list="tabList"
        :current="currentTabIndex"
        :height="92"
        :font-size="32"
        inactive-color="#333333"
        active-color="#E80404"
        :bar-width="36"
        :bar-height="8"
        :bar-style="{ background: 'linear-gradient(214deg, #FF8383 0%, #E70000 100%)' }"
        :is-scroll="false"
        @change="changeTabs"
      ></u-tabs>
    </view>

    <view v-if="!loading">
      <!-- 列表（商品收藏） -->
      <view v-if="currentTabType === TabType.Goods" class="">
        <!-- 有数据 -->
        <view v-if="list.length" class="ptb-20-plr-00">
          <view class="fade-in d-flex a-center" v-for="(item, index) in list" :key="index">
            <view v-if="isShowEdit" class="ml-24">
              <vh-check :checked="item.$checked" @click="item.$checked = !item.$checked"></vh-check>
            </view>

            <view class="flex-1 bg-ffffff mb-20 mr-24 ml-24 p-20 b-rad-10 d-flex j-sb" @click="onJump(item)">
              <view class="w-284 b-rad-06 o-hid">
                <vh-image :loading-type="2" :src="item.period_info.banner_img_str" :height="176" />
              </view>

              <view class="flex-1 d-flex flex-column j-sb ml-20">
                <view class="">
                  <view class="o-hid text-hidden-2">
                    <vh-channel-title-icon
                      :channel="item.periods_type"
                      border-radius="4"
                      :isNewYearTheme="isNewYearTheme"
                      font-size="24"
                    ></vh-channel-title-icon>
                    <text class="ml-14 l-h-36">{{ item.period_info.title }}</text>
                  </view>
                  <view class="mt-08 font-20 text-9 l-h-34 o-hid text-hidden-1">{{ item.period_info.brief }}</view>
                </view>

                <view class="mt-30 d-flex j-sb">
                  <text
                    v-if="item.period_info.is_hidden_price == 1 || [3, 4].includes(item.period_info.onsale_status)"
                    class="font-24 text-e80404 font-wei l-h-30"
                    >价格保密</text
                  >
                  <text v-else class="font-24 text-e80404 font-wei l-h-30">¥{{ item.period_info.price }}</text>
                  <text class="font-20 text-9 l-h-28"
                    >已售<text class="text-e80404">{{
                      item.period_info.purchased + item.period_info.vest_purchased
                    }}</text
                    >份</text
                  >
                </view>
              </view>
            </view>
          </view>

          <u-loadmore :status="reachBottomLoadStatus" />
        </view>

        <!-- 收藏列表（无数据） -->
        <view v-else class="fade-in">
          <vh-empty
            :padding-top="6"
            :padding-bottom="96"
            image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_coll.png"
            :text-bottom="0"
            text="暂无收藏"
          />

          <!-- 猜你喜欢列表 -->
          <vh-split-line
            :padding-top="52"
            :padding-bottom="32"
            :margin-left="10"
            :margin-right="10"
            :text="currentSplit.text"
            :font-bold="true"
            :font-size="36"
            text-color="#333333"
            :show-image="true"
            :image-src="currentSplit.image"
          ></vh-split-line>

          <vh-goods-recommend-list />
        </view>
      </view>

      <AuctionEnjoy
        v-show="currentTabType === TabType.AuctionGoods"
        ref="auctionEnjoyRef"
        isFromMyCollection
        @hiddenEdit="onHiddenEdit"
      />

      <!-- 列表（酒闻收藏） -->
      <view v-if="currentTabType === TabType.WineNews" class="">
        <!-- 有数据 -->
        <view v-if="list.length" class="ptb-20-plr-00">
          <view class="fade-in d-flex a-center" v-for="(item, index) in list" :key="index">
            <view v-if="isShowEdit" class="ml-24">
              <vh-check :checked="item.$checked" @click="item.$checked = !item.$checked"></vh-check>
            </view>

            <view class="flex-1 bg-ffffff mb-20 mr-24 ml-24 p-20 b-rad-10 d-flex j-sb" @click="onJump(item)">
              <view class="w-188 b-rad-06 o-hid">
                <vh-image :loading-type="2" :src="item.img" :height="188" />
              </view>

              <view class="flex-1 d-flex flex-column j-sb ml-20">
                <view class="">
                  <view class="o-hid text-hidden-2 l-h-36">{{ item.title }}</view>
                  <view class="mt-12 font-24 text-9 l-h-34 o-hid text-hidden-1">{{ item.abst }}</view>
                </view>

                <view class="mt-40 d-flex j-end">
                  <view class="d-flex a-center mr-52">
                    <image
                      class="w-30 h-22"
                      src="https://images.vinehoo.com/vinehoomini/v3/comm/view.png"
                      mode="widthFix"
                    ></image>
                    <text class="ml-06 font-24 text-9 l-h-34">{{ item.viewnums | numToThousands }}</text>
                  </view>
                  <view class="d-flex a-center">
                    <image
                      class="w-30 h-22"
                      src="https://images.vinehoo.com/vinehoomini/v3/comm/comm.png"
                      mode="widthFix"
                    ></image>
                    <text class="ml-06 font-24 text-9 l-h-34">{{ item.commentnums | numToThousands }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <u-loadmore :status="reachBottomLoadStatus" />
        </view>

        <!-- 酒闻列表（无数据�� -->
        <view v-else class="fade-in">
          <vh-empty
            :padding-top="6"
            :padding-bottom="96"
            image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_coll.png"
            :text-bottom="0"
            text="暂无收藏"
          ></vh-empty>

          <!-- 热闻列表 -->
          <vh-split-line
            :padding-top="52"
            :padding-bottom="32"
            :margin-left="10"
            :margin-right="10"
            :text="currentSplit.text"
            :font-bold="true"
            :font-size="36"
            text-color="#333333"
            :show-image="true"
            :image-src="currentSplit.image"
          ></vh-split-line>

          <vh-heat-smell-list />
        </view>
      </view>

      <!-- 预约列表 -->
      <view v-if="currentTabType === TabType.Reservation" class="">
        <!-- 有数据 -->
        <view v-if="list.length" class="ptb-20-plr-00">
          <view class="fade-in d-flex a-center" v-for="(item, index) in list" :key="index">
            <view v-if="isShowEdit" class="ml-24">
              <vh-check :checked="item.$checked" @click="item.$checked = !item.$checked"></vh-check>
            </view>

            <view class="flex-1 bg-ffffff mb-20 mr-24 ml-24 p-20 b-rad-10 d-flex j-sb" @click="onJump(item)">
              <view class="w-284 b-rad-06 o-hid" :style="{ opacity: item.period_info.onsale_status === 3 ? 0.45 : 1 }">
                <vh-image :loading-type="2" :src="item.period_info.banner_img_str" :height="176" />
              </view>

              <view
                class="flex-1 d-flex flex-column j-sb ml-20"
                :style="{ opacity: item.period_info.onsale_status === 3 ? 0.45 : 1 }"
              >
                <view class="">
                  <view class="o-hid text-hidden-2">
                    <vh-channel-title-icon
                      :channel="item.periods_type"
                      border-radius="4"
                      font-size="24"
                    ></vh-channel-title-icon>
                    <text class="ml-14 l-h-36">{{ item.period_info.title }}</text>
                  </view>
                  <view class="mt-08 font-20 text-9 l-h-34 o-hid text-hidden-1">{{ item.period_info.brief }}</view>
                </view>

                <view class="mt-30 d-flex j-sb a-center">
                  <text
                    v-if="item.period_info.is_hidden_price == 1 || [3, 4].includes(item.period_info.onsale_status)"
                    class="font-24 text-e80404 font-wei l-h-30"
                    >价格保密</text
                  >
                  <text v-else class="font-24 text-e80404 font-wei l-h-30">¥{{ item.period_info.price }}</text>
                  <button
                    v-if="item.period_info.onsale_status === 1"
                    class="cancel-reservation-btn"
                    @click.stop="cancelReservation(item)"
                  >
                    取消预约
                  </button>
                  <view class="text-999999 font-24" v-if="item.period_info.onsale_status === 2">
                    <text
                      >已售<text>{{ item.period_info.purchased }}</text></text
                    >
                    <text
                      >/限量<text>{{ item.period_info.limit_number }}</text></text
                    >
                  </view>
                  <view class="text-666666 font-24" v-if="item.period_info.onsale_status === 3">已下架 </view>
                </view>
              </view>
            </view>
          </view>
          <u-loadmore :status="reachBottomLoadStatus" />
        </view>

        <!-- 无数据 -->
        <view v-else class="fade-in">
          <vh-empty
            :padding-top="6"
            :padding-bottom="96"
            image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_coll.png"
            :text-bottom="0"
            text="暂无预约"
          />
        </view>
      </view>

      <view
        v-if="isShowEdit && !isAuctionGoods"
        class="p-fixed left-0 bottom-0 flex-sb-c ptb-00-plr-24 w-p100 h-104 bg-ffffff b-sh-00021200-022 z-9999"
      >
        <view class="flex-sb-c" @click="onCheckAllChange">
          <image :src="ossIcon(`/auction/radio${checkAll ? '_h' : ''}_32.png`)" class="ml-08 w-32 h-32" />
          <text class="ml-16 font-32 text-3">全选</text>
        </view>
        <button
          class="vh-btn flex-c-c w-208 h-64 font-wei-500 font-28 text-ffffff b-rad-32"
          :class="checkSome ? 'bg-e80404' : 'bg-fce4e3'"
          @click="onBatchCancel"
        >
          删除收藏
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import AuctionEnjoy from '@/packageH/pages/auction-enjoy/auction-enjoy'
import listMixin from '@/common/js/mixins/listMixin'

const TabType = Object.freeze({
  Goods: 1,
  AuctionGoods: 2,
  WineNews: 3,
  Reservation: 4,
})

export default {
  name: 'my-collection',
  mixins: [listMixin],
  components: {
    AuctionEnjoy,
  },
  data() {
    return {
      TabType,
      isNewYearTheme: false,
      // tabList: [{ name: '商品', type: TabType.Goods }, { name: '拍品', type: TabType.AuctionGoods }, { name: '酒闻', type: TabType.WineNews }], //状态栏选项
      tabList: [
        { name: '商品', type: TabType.Goods },
        { name: '预约', type: TabType.Reservation },
        { name: '酒闻', type: TabType.WineNews },
      ], //状态栏选项
      isShowEdit: false,
    }
  },

  computed: {
    ...mapState(['routeTable']),
    rightText({ isShowEdit }) {
      return isShowEdit ? '完成' : '编辑'
    },
    rightTextStatus({ currentTabType, list }) {
      if (currentTabType === TabType.AuctionGoods) {
        return !!(this.$refs?.auctionEnjoyRef?.enjoyedList?.length || 0)
      }
      return !!list.length
    },
    currentTabType({ tabList, currentTabIndex }) {
      return tabList[currentTabIndex].type
    },
    isAuctionGoods({ currentTabType }) {
      return currentTabType === TabType.AuctionGoods
    },
    currentSplit({ currentTabType }) {
      return {
        [TabType.Goods]: {
          text: '猜你喜欢',
          image: 'https://images.vinehoo.com/vinehoomini/v3/comm/guess_love.png',
        },
        [TabType.WineNews]: {
          text: '热闻推荐',
          image: 'https://images.vinehoo.com/vinehoomini/v3/comm/fire.png',
        },
      }[currentTabType]
    },
    checkAll({ list }) {
      return !!list.length && list.every((item) => item.$checked)
    },
    checkSome({ list }) {
      return list.some((item) => item.$checked)
    },
  },

  methods: {
    async load(query, index) {
      let res
      const { type } = this.tabList[index]
      const { page, limit, last_id = 0 } = query
      switch (type) {
        case TabType.Goods:
          res = await this.$u.api.goodsCollectionList({ page, limit })
          break
        case TabType.AuctionGoods:
          const auctionEnjoyRef = this.$refs?.auctionEnjoyRef
          if (auctionEnjoyRef) {
            delete auctionEnjoyRef.query.page
            res = await auctionEnjoyRef.load({ ...auctionEnjoyRef.query, last_id })
            auctionEnjoyRef.loading = false
          }
          break
        case TabType.WineNews:
          res = await this.$u.api.wineSmellCollectinList({ page, limit })
          break
        case TabType.Reservation:
          res = await this.$u.api.myReservation({ page, limit })
          break
      }
      if (type !== TabType.AuctionGoods) {
        const { list = [] } = res?.data || {}
        const checkAll = this.checkAll
        list.forEach((item) => {
          item.$checked = checkAll
        })
        this.list = page === 1 ? list : this.list.concat(list)
      } else {
        this.list = []
      }
      this.currentTabIndex = index
      this.changeBodyClassList()
      return res
    },
    changeTabs(index) {
      if (this.isShowEdit) return
      const { type } = this.tabList[index]
      this.isLastIdPattern = type === TabType.AuctionGoods
      this.changeTabIndex(index)
    },
    onRightTextClick() {
      this.isShowEdit = !this.isShowEdit
      if (this.isAuctionGoods) {
        const auctionEnjoyRef = this?.$refs?.auctionEnjoyRef
        if (auctionEnjoyRef) {
          auctionEnjoyRef.onRightTextClick()
        }
        return
      }
      this.list.forEach((item) => {
        item.$checked = false
      })
    },
    onCheckAllChange() {
      const checkAll = this.checkAll
      this.list.forEach((item) => {
        item.$checked = !checkAll
      })
    },
    async secondConfig() {
      const res = await this.$u.api.secondConfig()
      if (res.data.isopen) {
        this.isNewYearTheme = true
      }
    },
    onBatchCancel() {
      if (!this.checkSome) return
      this.feedback.showModal({
        content: '确认删除吗？',
        confirm: async () => {
          const checkedIds = this.list.filter(({ $checked }) => $checked).map(({ id }) => id)

          switch (this.currentTabType) {
            case TabType.Goods:
              await this.$u.api.deleteGoodsCollection({ id: checkedIds.join(',') })
              break
            case TabType.WineNews:
              await this.$u.api.wineSmellCollection({ id: checkedIds.join(','), status: 0 })
              break
            case TabType.Reservation:
              await this.$u.api.cancelReservation({
                periods: checkedIds,
              })
              break
          }

          this.reload()
            .then(() => {
              this.onChangeToScroll()
            })
            .finally(() => {
              this.feedback.toast()
            })
          this.isShowEdit = false
        },
      })
    },
    onJump(item) {
      if (this.isShowEdit) return
      let path = ''
      switch (this.currentTabType) {
        case TabType.Goods:
          path = `${this.routeTable.pgGoodsDetail}?id=${item.period_id}`
          break
        case TabType.WineNews:
          path = `${this.routeTable.pDWineSmellDetail}?id=${item.id}`
          break
        case TabType.Reservation:
          path = `${this.routeTable.pgGoodsDetail}?id=${item.id}`
          break
      }
      if (path) this.jump.appAndMiniJump(1, path, this.$vhFrom)
    },
    changeBodyClassList() {
      const { type } = this.tabList[this.currentTabIndex]
      const isAuctionGoods = type === TabType.AuctionGoods
      if (!isAuctionGoods) {
        this.$nextTick(() => {
          document?.body?.classList?.add('bg-f5f5f5')
        })
      } else {
        this.$refs?.auctionEnjoyRef?.changeBodyClassList()
      }
    },
    onHiddenEdit() {
      this.reload().then(() => {
        this.onChangeToScroll()
      })
      this.isShowEdit = false
    },
    cancelReservation(item) {
      this.feedback.showModal({
        content: '确认取消预约吗？',
        confirm: async () => {
          try {
            await this.$u.api.cancelReservation({
              periods: [item.id],
            })
            this.feedback.toast('取消成功')
            this.reload()
          } catch (error) {
            this.feedback.toast('取消失败')
          }
        },
      })
    },
  },
  onLoad() {
    this.login.isLoginV3().then((isLogin) => {
      if (!isLogin) return
      this.load()
      this.secondConfig()
    })
  },
  onShow() {
    this.changeBodyClassList()
  },
  onPullDownRefresh() {
    if (this.isShowEdit) {
      uni.stopPullDownRefresh()
      return
    }
    this.pullDownRefresh()
  },
  onReachBottom() {
    this.reachBottomLoad()
  },
}
</script>

<style lang="scss" scoped>
.my-collection {
  &__tabs {
    ::v-deep {
      .u-tab {
        &-item {
          font-weight: 600 !important;
        }

        &-bar {
          top: 100%;
        }
      }
    }
  }

  .vh-btn {
    &:active {
      opacity: 0.8;
    }
  }
}
.cancel-reservation-btn {
  border-radius: 12px;
  border: 1px solid #d8d8d8;
  font-size: 12px !important;
  color: #333333;
  line-height: 17px;
  padding: 0px 8px;
  margin: 0;
  background-color: #ffffff;

  &:active {
    opacity: 0.8;
  }
}
</style>
