<template>
	<view>
		<vh-navbar title="我的优惠券" :show-border="true" />
		<view v-if="!loading">
			<view class="d-flex h-90 bg-ffffff">
				<view v-for="(item, index) in tabList" :key="item.value" class="p-rela flex-c-c h-p100" :class="[index === 1 ? 'w-230' : 'flex-1']" @click="onTabChange(item)">
					<view class="font-wei-600 font-28 text-3">{{ item.text }}</view>
					<view v-if="query.coupon_type === item.value" class="p-abso bottom-0 w-36 h-08 bg-e80404 b-rad-04"></view>
					<template v-if="index === 1">
						<view class="p-abso left-0 w-02 h-24 bg-bdbdbd"></view>
						<view class="p-abso right-0 w-02 h-24 bg-bdbdbd"></view>
					</template>
				</view>
			</view>
			<view class="menu p-rela h-78">
				<view class="d-flex ptb-00-plr-24 h-p100" :class="[menuContentStatus ? 'bg-ffffff' : '']">
					<view v-for="item in subTabList" :key="item.value" class="flex-c-c w-p25 h-p100" @click="onSubTabChange(item)">
						<view class="font-24 l-h-34" :class="[query.sub_type === item.value ? 'text-e80404' : 'text-3']">{{ item.text }}({{ subTabStats[item.value] || 0 }})</view>
						<image v-if="item.value === 0" :src="ossIcon(menuContentStatus ? '/coupon/arrow_b_red_18_10.png' : '/coupon/arrow_t_gray_18_10.png')" class="ml-06 w-18 h-10"></image>
					</view>
				</view>
				<view v-show="menuContentStatus" :style="[{ height: `${menuContentHeight}px`, background: 'rgba(0,0,0,0.5)', top: '100%' }]" class="p-abso left-0 w-p100 z-9999" @click.stop="menuContentStatus = false" @touchmove.stop.prevent>
					<view class="bg-ffffff b-bl-br-rad-32">
						<view v-for="item in sortList" :key="item.value" class="flex-c-c h-106 bt-s-02-eeeeee" @click.stop="onSortTypeChange(item)">
							<view class="font-wei-500 font-28" :class="[query.sort_type === item.value ? 'text-e80404' : 'text-6']">{{ item.text }}</view>
						</view>
					</view>
				</view>
			</view>
			<view v-if="list.length" class="ptb-00-plr-24">
				<view v-for="item in list" :key="item.id" class="mb-24">
					<CouponListItem :item="item" isJump></CouponListItem>
				</view>
			</view>
			<view v-else class="p-rela flex-c-c mt-176">
				<image :src="ossIcon('/empty/emp_cou.png')" class="w-440 h-360"></image>
				<view class="p-abso bottom-0 font-28 text-9 l-h-40">暂无优惠券</view>
			</view>
			<view>
				<view class="p-fixed bottom-0 flex-c-c w-p100 h-140 bg-ffffff z-999">
					<view class="flex-c-c flex-1 h-p100" @click="jump.appAndMiniJump(1, $routeTable.pECouponHistory, $vhFrom)">
						<view class="font-28 text-9 l-h-40">历史优惠券</view>
						<image :src="ossIcon('/arrow_r_gray_12_24.png')" class="ml-06 w-12 h-24"></image>
					</view>
					<view class="w-02 h-40 bg-979797"></view>
					<view class="flex-c-c flex-1 h-p100" @click="jump.appAndMiniJump(1, `${$routeTable.pBRabbitHeadShop}?loadType=coupon`, $vhFrom)">
						<image :src="ossIcon('/exchange_56_26.png')" class="ml-06 w-56 h-26"></image>
						<view class="ml-04 font-28 text-3 l-h-40">前往兔头商店</view>
						<image :src="ossIcon('/arrow_r_black_12_24.png')" class="ml-06 w-12 h-24"></image>
					</view>
				</view>
				<view class="h-140"></view>
			</view>
		</view>
		<CouponFailedTipsPopup v-model="couponFailedTipsPopupVisible"></CouponFailedTipsPopup>
	</view>
</template>

<script>
import listMixin from '@/common/js/mixins/listMixin'

export default{
	mixins: [listMixin],
	data: () => ({
		tabList: [
			{
				value: 1,
				text: '商品券',
				list: [
					{
						value: 0,
						text: '全部'
					},
					{
						value: 1,
						text: '满减券'
					},
					{
						value: 2,
						text: '立减券'
					},
					{
						value: 3,
						text: '指定券'
					}
				]
			},
			{
				value: 2,
				text: '酒会券',
				list: [
					{
						value: 0,
						text: '全部'
					},
					{
						value: 2,
						text: '立减券'
					},
					{
						value: 4,
						text: '折扣券'
					},
					{
						value: 3,
						text: '指定券'
					}
				]
			},
			{
				value: 3,
				text: '运费券',
				list: [
					{
						value: 2,
						text: '满减券'
					}
				]
			}
		],
		menuContentHeight: '',
		menuContentStatus: false,
		sortList: [
			{
				value: 0,
				text: '综合排序'
			},
			{
				value: 1,
				text: '快过期优先'
			},
			{
				value: 2,
				text: '优惠金额大优先'
			}
		],
		query: {
			type: 1,
			coupon_type: 1,
			sub_type: '',
			sort_type: 0
		},
		subTabStats: {},
		couponFailedTipsPopupVisible: false,
		refreshCouponNums: 0
	}),
	computed: {
		subTabList ({ query, tabList }) {
			return tabList.find(item => item.value === query.coupon_type)?.list || []
		}
	},
	onLoad() {
		this.load({ ...this.query, sub_type: this.getSubType() }).then(res => {
			if (res?.data?.refresh_coupon_nums) {
				this.refreshCouponNums = res.data.refresh_coupon_nums
				this.couponFailedTipsPopupVisible = true
			}
			this.$nextTick(() => {
				this.$uGetRect('.menu').then(res => {
					this.menuContentHeight = this.$u.sys().windowHeight - res.bottom
				})
			})
		})
	},
	onPullDownRefresh() {
		this.pullDownRefresh()
	},
	onReachBottom () {
    this.reachBottomLoad()
  },
	methods: {
		async load (query) {
			const res = await this.$u.api.getCouponList(query)
			const { list = [], sub_type_count = {} } = res?.data || {}
			this.list = query.page === 1 ? list : this.list.concat(list)
			this.subTabStats = sub_type_count
			return res
		},
		getSubType (coupon_type = this.query.coupon_type) {
			return this.tabList.find(item => item.value === coupon_type)?.list?.[0].value || 0
		},
		onTabChange(item) {
			this.load({ ...this.query, coupon_type: item.value, sub_type: this.getSubType(item.value), sort_type: this.sortList[0].value, page: 1 }).then(() => {
				this.menuContentStatus = false
			})
		},
		onSubTabChange(item) {
			if (item.value === 0 && this.query.sub_type === item.value) {
				this.menuContentStatus = !this.menuContentStatus
				return
			}
			this.load({ ...this.query, sub_type: item.value, page: 1 }).then(() => {
				this.menuContentStatus = false
			})
		},
		onSortTypeChange(item) {
			if (item.value === this.query.sort_type) return
			this.load({ ...this.query, sort_type: item.value, page: 1 }).then(() => {
				this.menuContentStatus = false
			})
		}
	},
}
</script>

<style>
	page {
		background-color: #F5F5F5;
	}
</style>