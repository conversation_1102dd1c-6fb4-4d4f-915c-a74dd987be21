<template>
	<view class="content">
		<!-- 导航栏 -->
		<u-navbar back-icon-color="#333" title="修改发票抬头" title-size="36" :title-bold="true" title-color="#333"></u-navbar>
		
		<!-- 添加发票抬头列表 -->
		<view class="bg-ffffff mt-20 ml-24 mr-24 b-rad-10">
			<view class="pl-24 pr-24">
				<!-- 发票类型 -->
				<view class="pt-34 pb-34 d-flex a-center bb-s-01-eeeeee">
					<view class="add-title">发票类型</view>
					<view class="bg-f6f6f6 w-144 h-44 d-flex j-center a-center ml-34 font-24 text-3 b-rad-24" :class="(true ? 'text-e80404 bg-fce4e3 b-s-01-e80404' :'')">普通发票</view>
					<view class="bg-f6f6f6 w-216 h-44 d-flex j-center a-center ml-20 font-24 text-3 b-rad-24">增值税专用发票</view>
				</view>
				
				<!-- 抬头类型 -->
				<view class="pt-34 pb-34 d-flex a-center bb-s-01-eeeeee">
					<view class="add-title">抬头类型</view>
					<view class="bg-f6f6f6 w-96 h-44 d-flex j-center a-center ml-34 font-24 text-3 b-rad-24">个人</view>
					<view class="bg-f6f6f6 w-96 h-44 d-flex j-center a-center ml-20 font-24 text-3 b-rad-24" :class="(true ? 'text-e80404 bg-fce4e3 b-s-01-e80404' :'')">单位</view>
				</view>
				
				<!-- 发票抬头 -->
				<view class="pt-34 pb-34 d-flex a-center bb-s-01-eeeeee">
					<view class="add-title">发票抬头</view>
					<input class="ml-34" type="text" placeholder="请输入发票抬头" placeholder-style="color:#999;font-size:28rpx;" />
				</view>
				
				<!-- 单位税号 -->
				<view class="pt-34 pb-34 d-flex a-center bb-s-01-eeeeee">
					<view class="add-title">单位税号</view>
					<input class="ml-34" type="text" placeholder="请输入单位税号" placeholder-style="color:#999;font-size:28rpx;" />
				</view>
				
				<!-- 邮箱地址 -->
				<view class="pt-34 pb-34 d-flex a-center bb-s-01-eeeeee">
					<view class="add-title">邮箱地址</view>
					<input class="ml-34" type="text" placeholder="请输入邮箱地址" placeholder-style="color:#999;font-size:28rpx;" />
				</view>
				
				<!-- 注册地址 -->
				<view class="pt-34 pb-34 d-flex a-center bb-s-01-eeeeee">
					<view class="add-title">注册地址</view>
					<input class="ml-34" type="text" placeholder="选填" placeholder-style="color:#999;font-size:28rpx;" />
				</view>
				
				<!-- 注册电话 -->
				<view class="pt-34 pb-34 d-flex a-center bb-s-01-eeeeee">
					<view class="add-title">注册电话</view>
					<input class="ml-34" type="text" placeholder="选填" placeholder-style="color:#999;font-size:28rpx;" />
				</view>
				
				<!-- 开户银行 -->
				<view class="pt-34 pb-34 d-flex a-center bb-s-01-eeeeee">
					<view class="add-title">开户银行</view>
					<input class="ml-34" type="text" placeholder="选填" placeholder-style="color:#999;font-size:28rpx;" />
				</view>
				
				<!-- 银行账号 -->
				<view class="pt-34 pb-34 d-flex a-center">
					<view class="add-title">银行账号</view>
					<input class="ml-34" type="text" placeholder="选填" placeholder-style="color:#999;font-size:28rpx;" />
				</view>
			</view>
		</view>
		
		<!-- 增值税专用发票抬头确认书 -->
		<view v-if="false" class="bg-ffffff d-flex a-center mt-20 ml-24 mr-24 pt-38 pb-38 pl-24 b-rad-10">
			<image class="w-32 h-32" :src="isAggrement ? 'https://images.vinehoo.com/vinehoomini/v3/comm/cir_sel.png' : 'https://images.vinehoo.com/vinehoomini/v3/comm/cir_unsel.png'" mode="widthFix" @click="isAggrement = !isAggrement"></image>
			<text class="ml-24 font-24 text-9">我已阅读并同意</text>
			<text class="font-24 text-e80404">《增值税专用发票抬头确认书》</text>
		</view>
		
		<!-- 底部按钮 -->
		<view class="btn-con bg-feffff p-fixed bottom-0 z-999 w-p100 h-104 pl-32 pr-32 d-flex j-sa a-center">
		    <view class="w-308 h-64 d-flex j-center a-center b-rad-40 bg-eeeeee font-28 text-3">删除</view>
			<view v-if="isAggrement">
				<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" :custom-style="{width:'308rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}">立即提交</u-button>
			</view>
			<view v-else class="w-308 h-64 d-flex j-center a-center b-rad-40 bg-fce4e3 font-28 text-ffffff">提交</view>
		</view>
	</view>
</template>

<script>
	export default{
		name: 'invoice-head-update',
		
		data() {
			return{
				isAggrement:false,//是否同意增值税专用发票抬头确认书
			}
		}
	}
</script>

<style scoped>
	.add-title{
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		line-height: 40rpx;
	}
	.btn-con{
		box-shadow: 0px 1px 6px 0px rgba(0,0,0,0.22);
	}
</style>
