<template>
	<view class="content">
		<!-- 导航栏 -->
		<view class="bb-s-01-eeeeee">
			<u-navbar back-icon-color="#333" title="修改昵称" title-size="36" title-color="#333"></u-navbar>
		</view>
		
		<!-- 输入框 -->
		<view class="bg-ffffff pl-32 pr-32">
			<view class="pt-40 pb-20 bb-s-01-eeeeee p-rela">
				<input class="w-640 font-32 text-3 l-h-44" v-model="nickName" placeholder="请输入昵称" type="text" />
				<image v-if="nickName.length > 0" class="fade-in p-abso right-0 bottom-20 w-40 h-40 z-100" src="https://images.vinehoo.com/vinehoomini/v3/mine/up_user_del.png" mode="widthFix" @click="nickName = ''"></image>
			</view>
		</view>
		
		<!-- 按钮 -->
		<view class="mt-100 d-flex j-center a-center">
			<view v-if="$u.trim(nickName, 'all').length > 0">
				<u-button shape="circle" :ripple="true" ripple-bg-color="#ffffff" :custom-style="{width:'670rpx',color:'#fff',backgroundColor: '#E80404',border:'none'}" @click="updateNickName">提交</u-button>
			</view>
			<view v-else class="w-670 h-80 d-flex j-center a-center b-rad-40 bg-e80404 op-040 font-30 text-ffffff">
				提交
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default{
		name:"update-nickname",
		
		data(){
			return{
				nickName:'',//用户昵称
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['userInfo']),
		},
		
		methods:{
			// Vuex mapMutations辅助函数
			...mapMutations(['muUserInfo']),
			
			// 修改用户昵称
			async updateNickName(){
				await this.$u.api.updateUserInfo({nickname:this.nickName})
				this.userInfo.nickname = this.nickName
				this.muUserInfo(this.userInfo)
				this.feedback.toast({title:'修改成功', icon:'success'})
				this.jump.navigateBack()
			}
		}
	}
</script>

<style scoped>
</style>
