<template>
	<view class="content bg-f5f5f5" :class="isShowEdit ? 'pb-124' : ''">
		<!-- 导航栏 -->
		<vh-navbar title="心愿清单">
			<view class="d-flex a-center ml-10 w-s-now font-30 text-3" @click="isShowEdit = !isShowEdit">{{isShowEdit ? '完成' : '编辑'}}</view>
		</vh-navbar>
		
		<!-- 列表（商品收藏） -->
		<view class="">
			<!-- 有数据 -->
			<view v-if="wishList.length" class="ptb-20-plr-00">
				<view class="fade-in d-flex a-center" v-for="(item, index) in wishList" :key="index">
					<view v-if="isShowEdit" class="ml-24">
						<vh-check :checked="item.checked" @click="selectSingle(item)" />
					</view>
					
					<view class="flex-1 bg-ffffff mb-20 mr-24 ml-24 p-20 b-rad-10 d-flex j-sb" @click="jump.appAndMiniJump(1, `${routeTable.pgGoodsDetail}?id=${item.period_id}`, $vhFrom)">
						<view class="w-284 b-rad-06 o-hid">
							<vh-image :loading-type="2" :src="item.period_info.banner_img_str" :height="176" />
						</view>
						
						<view class="flex-1 d-flex flex-column j-sb ml-20">
							<view class="">
								<view class="o-hid text-hidden-2">
									<vh-channel-title-icon :channel="item.periods_type" :font-bold="false" />
									<!-- <vh-channel-title-icon :channel="item.periods_type" border-radius="4" font-size="18"></vh-channel-title-icon> -->
									<text class="ml-14 l-h-36" @longpress.stop="copy.copyText( item.period_info.title )">{{item.period_info.title}}</text>
								</view>
								<view class="mt-08 font-20 text-9 l-h-34 o-hid text-hidden-1">{{item.period_info.brief}}</view>
							</view>
							
							<view class="mt-30 d-flex j-sb">
								<text v-if="item.period_info.is_hidden_price == 1 || [3, 4].includes(item.period_info.onsale_status)" class="font-24 text-3 font-wei l-h-30">价格保密</text>
								<text v-else class="font-24 text-e80404 font-wei l-h-30">¥{{item.period_info.price}}</text>
								<view v-if="[2].includes(item.period_info.onsale_status)" class="w-128 h-38 bg-e80404 flex-c-c b-rad-24 font-24 text-ffffff">立即购买</view>
								<text v-else class="font-20 text-9 l-h-28">已售<text class="text-e80404">{{item.period_info.purchased + item.period_info.vest_purchased}}</text>份</text>
							</view>
						</view>
					</view>
				</view>
				
				<u-loadmore :status="loadStatus" />
			</view>
		    
			<!-- 列表（空） -->
			<vh-empty v-else 
			:padding-top="350"
			 :padding-bottom="700" 
			 :image-src="ossIcon(`/empty/emp_coll.png`)" 
			 :text-bottom="0" text="您还没有添加心愿清单~">
			 </vh-empty>
		</view>
		
		<!-- 底部按钮框 -->
		<view v-if="isShowEdit" class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex a-center pl-32 pr-24" :class="wishList.length ? 'j-sb' : 'j-end'">
			<view v-if="wishList.length" class="d-flex a-center">
				<vh-check :checked="wishList.every( v => v.checked )" @click="selectEvery()"></vh-check>
				<text class="ml-16 font-32 text-3">全选</text>
			</view>
			
			<view class="">
				<u-button :disabled="!canDel" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
				:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: !canDel ? '#FCE4E3' : '#E80404', border:'none'}"
				@click="deleteWish()">删除清单</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		name: 'my-collection',
		
		data(){
			return{
				isShowEdit: false,// 是否可编辑
				wishList: [], //心愿列表
				page: 1, //第几页
				limit: 10, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //底部加载状态
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
			
			// 是否可以删除
			canDel() {
				if ( this.wishList.some(v => v.checked) ) {
					return true
				}
				return false
			},
		},
		
		onLoad() {
			this.init()
		},
		
		methods:{
			// 初始化，接口聚合（心愿清单列表）
			async init() {
				this.page = 1
				await Promise.all([this.getWishList()])
			},
			
			// 获取心愿清单列表
			async getWishList() {
				try{
					this.feedback.loading()
					const { data: { total, list } } = await this.$u.api.wishList({ page: this.page, limit: this.limit })
					list.forEach(v => v.checked = false)
					this.page == 1 ? this.wishList = list : this.wishList = [...this.wishList, ...list]
					this.totalPage = Math.ceil(total / this.limit)
					this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
					this.feedback.hideLoading()
					uni.stopPullDownRefresh() //停止下拉刷新
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 选中单个/取消选中单个地址 item = 列表某一项
			selectSingle(item){
				this.wishList.forEach( v => { if(v.id === item.id) v.checked = !v.checked })
			},
			
			// 全选/取消全选 
			selectEvery(){
				const allChecked = this.wishList.every( v => v.checked )
				this.wishList.forEach( v => { v.checked = !allChecked })
			},
		    
			// 删除清单（批量） 
			deleteWish(){
				this.feedback.showModal({
					content:'确认删除吗？',
					confirm: async () => {
						try{
							const idStr = this.wishList.filter(v => v.checked).map( v => v.id ).join(',')
							await this.$u.api.wishListRemove({ id: idStr })
							this.feedback.toast({ title: '删除成功', icon: 'success' })
							this.getWishList()
						}catch(e){
							this.feedback.toast({ title: '删除失败，请重试', icon: 'none' })
						}
					}
				})
			}
		},
		
		onPullDownRefresh() {
			this.page = 1
			this.getWishList()
		},
		
		onReachBottom() {
			if ( this.page == this.totalPage || this.totalPage == 0 ) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getWishList()
		}
	}
</script>

<style>
	@import "../../../common/css/page.css";
</style>
