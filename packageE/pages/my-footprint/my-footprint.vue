<template>
	<view class="content bg-f7f7f7">
		<vh-navbar title="足迹" height="46" show-border :customBack="customBack">
			<view v-if="list.length" slot="right" class="flex-c-c w-108 h-92 font-26 text-3"
				@click="isShowEdit = !isShowEdit">{{ rightText }}</view>
		</vh-navbar>
			
		<view v-if="!loading" class="ptb-00-plr-24 o-hid" :class="isShowEdit ? 'pb-128' : `${list.length ? 'pb-24' : ''}`">
				<view v-if="list.length" :class="isShowEdit ? 't-trans-x-72' : ''">
					<view v-for="(item, index) in list" :key="index" class="p-rela b-rad-10 mtb-20-mlr-00">
						<view class="pt-32 pb-04 font-wei font-32 text-3"> {{ item.$dateDes | monthDay }} </view>
						<view v-for="(item1, index1) in item.list" :key="index1" class="p-rela mt-20">
							<view class="p-abso top-84 left-0 t-trans-x-m100 flex-c-c w-96">
								<image :src="ossIcon(`/comm/cir_${item1.$checked ? '' : 'un'}sel.png`)" class="w-32 h-32"
									@click="item1.$checked = !item1.$checked" mode="widthFix" />
							</view>
							
							<!-- 拍卖 -->
							<MyFootPrintGoodsAuction v-if="item1.periods_type === 11" :item="item1" />
							
							<MyFootPrintGoods v-else :item="item1" />
							
						</view>
					</view>
					<u-loadmore :status="reachBottomLoadStatus" />
				</view>
				<!-- 商品列表（无数据） -->
				<vh-empty 
					v-else 
					:padding-top="80" 
					:padding-bottom="350" 
					:image-src="ossIcon(`/empty/emp_goods.png`)" 
					text="暂无浏览记录" 
					
				/>
			
				<view v-if="list.length && isShowEdit" class="p-fixed left-0 bottom-0 flex-sb-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-9999" v-safeBeautyBottom="$safeBeautyBottom">
					<view class="flex-sb-c ml-24" @click="selectAll">
						<image :src="ossIcon(`/comm/cir_${checkAll ? '' : 'un'}sel.png`)" class="ml-08 w-32 h-32" mode="widthFix"/>
						<text class="ml-16 font-32 text-3">全选</text>
					</view>
					<view class="flex-c-c">
						<button class="vh-btn flex-c-c w-208 h-64 bg-ffffff font-wei-500 font-28 text-999999 b-rad-32 b-s-02-999999"
							@click="clearVisible = true">清空</button>
						<button :disabled="!checkSome" class="vh-btn flex-c-c ml-40 mr-24 w-208 h-64 bg-e80404 font-wei-500 font-28 text-ffffff b-rad-32" 
							@click="delFootprint">删除</button>
					</view>
				</view>
		</view>
		<MyFootPrintClearModal @confirm="confirm" v-model="clearVisible"/>
	</view>
</template>

<script>
	import listMixin from '@/common/js/mixins/listMixin'
	export default {
		mixins: [listMixin],
		data: () => ({
			clearVisible:false,
			isShowEdit: false,
			cloneList:[],
			query:{
				page:1,
				limit:50
			},
		}),
		computed:{
			customBack () {
				if (this.$app) return this.$customBack
				return null
			},
			rightText({ isShowEdit }) {
				return isShowEdit ? '完成' : '编辑'
			},
			checkAll({ list }) {
				return !!list.length && Object.keys(list).every(index => list[index].list.every(item => item.$checked))
			},
			checkSome({ list }) {
				return !!list.length && Object.keys(list).some(index => list[index].list.some(item => item.$checked))
			}
		},
		onLoad() {
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) {
					this.load()
				}
			})
		},
		methods: {
			//我的足迹列表
			async load(query) {
				const { page, limit } = query
				// 接口返回数据
				let res =  await this.$u.api.getFootprintList({ page , limit })
				const { list = [] } = res?.data || {}
				const sevenDayDiff = ( browseDate = '2023-05-20 00:00' ) => {
					const currTimestamp = Math.floor(Date.now() / 1000)
					const browseTimestamp = Date.parse(browseDate) / 1000
					return Math.floor((currTimestamp - browseTimestamp) / 60 / 60 / 24)
				}
				const groupList = []
				list.forEach((item, index) => {
					 if( item.quota_rule ) item.quota_rule = JSON.parse(item.quota_rule)
					  item.$checked = false
					  item.$dayDiff = sevenDayDiff(item.created_time)
					  if( item.$dayDiff === 0 ) {
						  item.$dateDes = '今天'
					  }else if( item.$dayDiff === 1 ) {
						  item.$dateDes = '昨天'
					  }else if( item.$dayDiff >= 2 && item.$dayDiff <= 7 ) {
						  item.$dateDes = item.created_time
					  }else if( item.$dayDiff > 7 ) {
						  item.$dateDes = '七天前'
					  }else {
						  item.$dateDes = item.created_time
					  }
					  const findGroup = groupList.find(group => group.$dateDes === item.$dateDes)
					  findGroup ? findGroup.list.push(item) : groupList.push({ $dateDes: item.$dateDes, list: [item] })
				})
				if( page === 1 ) {
					this.list = groupList
				}else {
					const { length, [length - 1]: originLastItem } = this.list
					const targetFirstItem = groupList[0]
					if (originLastItem.$dateDes === targetFirstItem.$dateDes) {
					  originLastItem.list.push(...targetFirstItem.list)
					  this.list.push(...groupList.slice(1))
					} else {
					  this.list.push(...groupList)
					}
				}
				console.log(this.list)
				this.changeBodyClassList()
				return res
			},
			// 删除足迹
			async delFootprint(){
				// this.feedback.toast({ title: '删除成功', icon: 'success' })
				if (!this.checkSome) return
				this.feedback.showModal({
					content:'确认删除吗？',
					confirm: async () => {
						const arr  = this.list.map((item) => {
							return item.list
						}).flat()
						const ids = arr.filter(({ $checked }) => $checked).map(({ id }) => id).join(',')
						await this.$u.api.delFootprint({is_clear:0,ids:ids})
						this.cloneList = []
						this.reload().then(() => {
							this.onChangeToScroll()
						}).finally(() => {
							this.feedback.toast()
						})
						this.isShowEdit = false
					}
				})
			},
			//全选
			selectAll(){
				const checkAll = this.checkAll
				this.list.forEach(item => {
					item.list.forEach(item1 => item1.$checked = !checkAll)
					
				})
			},
			//确认清空
			async confirm(){
				await this.$u.api.delFootprint({is_clear:1})
				this.isShowEdit = false
				this.clearVisible = false
				// this.feedback.toast({ title: '清空成功', icon: 'success' })
				this.reload().then(() => {
					this.onChangeToScroll()
				}).finally(() => {
					this.feedback.toast()
				})
			},
			changeBodyClassList () {
			  this.$nextTick(() => {
				document?.body?.classList?.[this.list.length ? 'add' : 'remove']('bg-f5f5f5')
			  })
			}
		},
		onShow () {
		    this.changeBodyClassList()
		},
		onPullDownRefresh () {
			if (this.isShowEdit) {
				uni.stopPullDownRefresh()
				return
			}
			this.cloneList = []
			this.pullDownRefresh()
		},
		onReachBottom () {
			this.reachBottomLoad()
		}
	}
</script>

