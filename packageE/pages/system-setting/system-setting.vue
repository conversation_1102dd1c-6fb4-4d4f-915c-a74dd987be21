<template>
	<view class="content bg-ffffff">
		<!-- 导航栏 -->
		<vh-navbar title="系统设置" :show-border="true" />
		
		<!-- 板块1 用户协议、隐私政策... -->
		<view class="ml-40 mr-40">
			<view class="d-flex j-sb a-center pt-32 pb-32 bb-s-01-eeeeee" v-for="(item,index) in funList1" :key="index" @click="jump.h5Jump(`${agreementPrefix}/${item.agreement}`)">
				<text class="text-3 font-32 font-wei">{{item.name}}</text>
				<u-icon name="arrow-right" :size="20" color="#333"></u-icon>
			</view>
		</view>
		
		<!-- 间隔槽 -->
		<vh-gap height="16" bg-color="#f5f5f5" />
		
		<!-- 板块2 发票、收货地址 -->
		<view class="ml-40 mr-40">
			<view class="d-flex j-sb a-center pt-32 pb-32 bb-s-01-eeeeee" v-for="(item,index) in funList2" :key="index" @click="jump.navigateTo(item.route)">
				<text class="text-3 font-32 font-wei">{{item.name}}</text>
				<u-icon name="arrow-right" :size="20" color="#333"></u-icon>
			</view>
		</view>
		
		<!-- 间隔槽 -->
		<vh-gap height="16" bg-color="#f5f5f5" />
		
		
		
		<!-- 退出按钮 -->
		<!-- <view class="d-flex j-center a-center mt-196">
			<u-button shape="circle" :ripple="true" ripple-bg-color="#ffffff" :custom-style="{width:'646rpx',color:'#fff',backgroundColor: '#E80404',border:'none'}"
			@click="exitAccount">退出账户</u-button>
		</view> -->
		<view class="d-flex j-center a-center pt-32 pb-32" @click="exitAccount">
			<text class="text-3 font-32 font-wei">退出账户</text>
		</view>
		
		<!-- 间隔槽 -->
		<vh-gap height="16" bg-color="#f5f5f5" />
		
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default{
		name:"system-setting",
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable', 'agreementPrefix']),
			
			// 功能列表1
			funList1() {
				return [
					{ name:'用户协议', agreement:'vinehooUser' },
					{ name:'隐私政策', agreement:'PrivacyPolicy' },
					{ name:'内容发布规则', agreement:'ContentPubRules' }
				]
			},
			
			// 功能表2
			funList2(){
				return [
					{  name:'发票抬头管理', route: this.routeTable.pEInvoiceMangement },
					{ name:'收货地址', route: this.routeTable.pEAddressManagement }
				]
			}
		},
		
		methods:{
			// 退出账户
			exitAccount() {
				this.feedback.showModal({
					content:'您确认退出账户吗？',
					confirm: () => {
						this.login.clearLoginData()
						this.jump.reLaunch( this.routeTable.pgIndex )
					}
				})
			}
		}
	}
</script>

<style scoped></style>
