<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<vh-navbar :background="{background: '#E80404' }" back-icon-color="#FFF" title="今日任务" title-color="#FFF" />
		
		<!-- 日常任务列表数据 -->
		<view v-if="!loading" class="fade-in pb-24">
			<!-- banner -->
			<view class="p-abso top-0 w-p100 h-304 bg-e80404" />
			
			<!-- 签到记录 -->
			<view class="p-rela z-01 bg-ffffff b-rad-16 b-sh-00041600-010 mt-20 ml-24 mr-24 pt-40 pr-24 pb-36 pl-24">
				<view class="d-flex j-sb a-center">
					<text class="font-28 font-wei text-3 l-h-40">签到记录</text>
					<text class="font-24 text-6 l-h-34">满签额外+10兔头</text>
				</view>
				
				<view class="d-flex j-sb a-center mt-24">
					<view class="d-flex flex-column a-center" v-for="(item, index) in signInfo.signin" :key="index">
						<view class="w-82 h-116 b-rad-06 o-hid">
							<view class="w-p100 h-32 d-flex j-center a-center font-20 text-ffffff" 
							:class="item.days <= signInfo.signin_record.days ? item.days == signInfo.signin_record.days && !signInfo.signin_record.status ? 'bg-e80404' :'bg-ffa825' : 'bg-9f9f9f'">{{ item.days == signInfo.signin_record.days && !signInfo.signin_record.status  ? `今天` : `第${item.days}天`}}</view>
							<view class="w-p100 h-84 d-flex j-center a-center" 
							:class="item.days <= signInfo.signin_record.days ? item.days == signInfo.signin_record.days && !signInfo.signin_record.status ? 'bg-ffeaea' :'bg-fdf3ef' : 'bg-f6f6f6'">
								<image class="w-46 h-48" 
								:src="`https://images.vinehoo.com/vinehoomini/v3/comm/${item.days <= signInfo.signin_record.days ? item.days == signInfo.signin_record.days && !signInfo.signin_record.status ? 'rab_gold' : 'signed_gold' : 'rab_gray' }.png`" 
								mode="aspectFill" />
							</view>
						</view>
						<view class="font-20 text-9 l-h-28">+{{item.rabbit}}兔头</view>
					</view>
				</view>
				
				<view class="mt-28">
					<view v-if="signInfo.signin_record.status == 1" class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'654rpx', height:'68rpx', fontSize:'24rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#D2D2D2', border:'none'}"
						@click="feedback.toast({title:'今日已签~'})">今日已签</u-button>
					</view>
					<view v-else class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'654rpx', height:'68rpx', fontSize:'24rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="signInNow">立即签到</u-button>
					</view>
				</view>
				
			</view>
			
			<!-- 奖励列表 -->
			<view class="p-rela z-01 bg-ffffff b-rad-16 mt-24 ml-24 mr-24 pl-26 pr-26">
				<view class="d-flex j-sb a-center pt-36 pb-36 bb-s-01-eeeeee" v-for="(item, index) in signInfo.tasks" :key="index">
					<view class="d-flex">
						<vh-image :loading-type="2" :src="item.icon" :width="72" :height="72" :border-radius="20"/>
						<view class="ml-16">
							<view class="font-28 font-wei text-3 l-h-40">{{item.title}}</view>
							<view class="mt-04 font-20 text-6 l-h-28">{{item.task_desc}}</view>
						</view>
					</view>
					
					<view class="">
						<view v-if="item.status == 1" class="">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#F5F5F5"
							:custom-style="{width:'126rpx', height:'46rpx', fontSize:'22rpx', color:'#FFF', backgroundColor: '#D2D2D2', border:'none'}"
							@click="feedback.toast({ title: '该任务已完成，快去完成其他任务吧~' })">已完成</u-button>
						</view>
						
						<view v-if="item.status == 0" class="">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'126rpx', height:'46rpx', fontSize:'22rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
							@click="toComplete(item)">去完成</u-button>
						</view>
					</view>
				</view>
			</view>
		</view>
	   
		<!-- 加载状态 -->
	    <view v-else class="fade-in">
	    	<vh-loading mode="flower" />
	    </view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	
	export default{
		name: 'daily-tasks',
		
		data(){
			return {
				loading: true, //加载状态
				signInfo: {}, //日常任务信息
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
		
		onLoad() {
			this.getDailyTasksList()
		},
		
		methods: {
			// 获取日常任务列表
			async getDailyTasksList(){
				let res = await this.$u.api.dailyTasksList()
				this.signInfo = res.data
				this.loading = false
			},
			
			// 立即签到
			async signInNow(){
				let res = await this.$u.api.signIn()
				this.signInfo.signin_record.status = 1
				this.feedback.toast({title:'签到成功', icon:'success'})
				this.getDailyTasksList()
			},
			
			// 去完成 item = 签到列表某一项
			toComplete( item ) {
				console.log(item)
				switch( item.id ) {
					case 1:
					this.feedback.toast({ title: '点击立即签到，即可完成签到~'})
					break
					case 2:
					case 5:
					if (this.$app)
							wineYunJsBridge.openAppPage({
							client_path: { ios_path: 'goMain', android_path: 'goMain' },
							ad_path_param: [
								{
								ios_key: 'type',
								ios_val: '1',
								android_key: 'type',
								android_val: '1',
								},
							],
							})
						else this.jump.reLaunch(this.routeTable.pgFlashPurchase) //跳转闪购
					
					break
					case 3:
					if (this.$app)
							wineYunJsBridge.openAppPage({
							client_path: { ios_path: 'goMain', android_path: 'goMain' },
							ad_path_param: [
								{
								ios_key: 'type',
								ios_val: '3',
								android_key: 'type',
								android_val: '3',
								},
							],
							})
						else this.jump.reLaunch(this.routeTable.pgMiaoFa) //跳转秒发
					
					break
					case 4:
					if (this.$app)
							wineYunJsBridge.openAppPage({
							client_path: { ios_path: 'goMain', android_path: 'goMain' },
							ad_path_param: [
								{
								ios_key: 'type',
								ios_val: '1',
								android_key: 'type',
								android_val: '1',
								},
							],
							})
						else this.jump.reLaunch(`${this.routeTable.pgFlashPurchase}?initChannel=2`)
					break
					case 6:
					case 7:
					if (this.$app)
							wineYunJsBridge.openAppPage({
							client_path: { ios_path: 'goMain', android_path: 'goMain' },
							ad_path_param: [
								{
								ios_key: 'type',
								ios_val: '2',
								android_key: 'type',
								android_val: '2',
								},
							],
							})
						else
					this.feedback.toast({ title: '请前往APP查看此功能~'})
					break
					default:
					if (this.$app)
					wineYunJsBridge.openAppPage({
                		client_path: { ios_path: 'goMain', android_path: 'goMain' },})
					else this.jump.reLaunch(this.routeTable.pgIndex) //不确定的都跳转首页
					
					
				}
			}
		}
	}
</script>

<style>
	@import "../../../common/css/page.css";
</style>
