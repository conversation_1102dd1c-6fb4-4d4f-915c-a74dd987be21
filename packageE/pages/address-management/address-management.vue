<template>
	<view class="content bg-ffffff">
		<!-- 导航栏 -->
		<vh-navbar title="地址管理" :show-border="true">
			<view class="d-flex a-center ml-10 w-s-now font-30 text-3" @click="isShowEdit = !isShowEdit">{{isShowEdit ? '完成' : '编辑'}}</view>
		</vh-navbar>
		
		<!-- 收货地址列表 -->
		<view class="">
			<view v-if="addressList.length > 0" class="">
				<view class="p-rela" v-for="(item, index) in addressList" :key="item.id" @longpress="openMaskOperation(item)" @click.stop="jumpPage(item)">
					<view class="d-flex j-sb a-center ml-36 mr-36 pt-40 pb-32 bb-s-01-eeeeee">
					    <view v-if="isShowEdit" class="mr-10">
							<vh-check :checked="addressSelectedList.indexOf(item.id) > -1" @click="selectSingle(item)" />
						</view>
						
						<view class="">
							<view class="">
								<text v-if="item.is_default" class="bg-ff0013 b-rad-04 ptb-02-plr-12 text-ffffff font-20 font-wei l-h-28">默认</text>
								<text v-if="item.label" class="bg-2e7bff b-rad-04 ptb-02-plr-12 text-ffffff font-20 font-wei l-h-28" :class="item.is_default ? 'ml-16' : ''">{{item.label}}</text>
								<text class="font-28 font-wei text-3 l-h-40" :class="item.label || item.is_default ? 'ml-20' : ''">{{item.consignee}}</text>
								<text class="ml-18 font-28 text-9 l-h-40">{{item.consignee_phone}}</text>
							</view>
							
							<view class="mt-08 w-528 font-24 text-6">
								{{item.province_name}} {{item.city_name}} {{item.town_name}} {{item.address}}
							</view>
						</view>
						<image class="w-40 h-40 pt-24 pb-24 pl-24" src="https://images.vinehoo.com/vinehoomini/v3/comm/edit_bla.png" @click.stop="editAddress(item)" />
					</view>
					<view v-if="item.showMask" class="fade-in p-abso z-100 top-0 left-0 w-p100 h-p100 bg-000-000-000-030 d-flex j-sa a-center pl-30 pr-30" @click.stop="closeMaskOperation(item)">
						<view class="w-92 h-92 bg-ffffff b-rad-p50 d-flex flex-column j-center a-center" @click.stop="copyAddress(item)">
							<view class="font-22 text-3 font-wei l-h-26">复制</view>
							<view class="font-22 text-3 font-wei l-h-26">地址</view>
						</view>
						
						<view v-if="!item.is_default" class="w-92 h-92 bg-ff9127 b-rad-p50 d-flex flex-column j-center a-center" @click.stop="setDefaultAddress(item.id)">
							<view class="font-22 text-ffffff font-wei l-h-26">设置</view>
							<view class="font-22 text-ffffff font-wei l-h-26">默认</view>
						</view>
						
						<view class="w-92 h-92 bg-eb0404 b-rad-p50 d-flex flex-column j-center a-center" @click.stop="deleteAddressItem(item.id)">
							<view class="font-22 text-ffffff font-wei l-h-26">删除</view>
							<view class="font-22 text-ffffff font-wei l-h-26">地址</view>
						</view>
					</view>
				</view>
			</view>
		    
			<!-- 列表（空） -->
			<view v-else class="">
				<vh-empty :padding-top="270" :padding-bottom="100" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_addr.png" :text-bottom="0" text="您还没有添加收货地址"></vh-empty>
			</view>
		</view>
		
		<!-- 底部按钮框 -->
		<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022">
			<view v-if="isShowEdit" class="fade-in-up-medium w-p100 h-p100 d-flex j-sb a-center pl-32 pr-24">
				<view class="d-flex a-center">
					<vh-check :checked="isSelectAllAddress()" @click="selectAll()" />
					<text class="ml-16 font-32 text-3">全选</text>
				</view>
				
				<view class="">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="deleteAddress">删除</u-button>
				</view>
			</view>
			
			<view v-else class="fade-in-down w-p100 h-p100 d-flex j-center a-center">
				<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
				:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="jump.navigateTo(`../address-add/address-add`)">新建地址</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default {
		name: 'address-management',
		
		data() {
			return {
				comeFrom:'', //来自哪个页面 1 = 普通商品订单确认页、2 = 申请售后页（换货）、3 = 兔头商品订单确认页
				isShowEdit: false,// 是否可编辑
				addressList: [], //收货地址列表
				addressSelectedList: [], // 选中的地址列表
				orderInfo: {}
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable', 'labelList', 'addressInfoState']),
		},
		
		onLoad(options) {
			this.comeFrom = options.comeFrom
			if (['6', '7', '8', '9'].includes(this.comeFrom) && this.pages.getPageLength() === 1) {
				this.jump.redirectTo(this.routeTable.pHAuctionIndex)
				return
			}
			const { subOrderNo, orderType } = options
			this.orderInfo = { subOrderNo, orderType }
			if (['11', '12'].includes(this.comeFrom) && this.pages.getPageLength() === 1) {
				if (subOrderNo && orderType) {
					this.jump.redirectTo(`${this.$routeTable[this.comeFrom === '11' ? 'pBOrderDetail' : 'pHAuctionOrderDetail']}?orderNo=${subOrderNo}`)
				} else {
					this.jump.redirectTo(`${this.$routeTable.pEMyOrder}?status=0`)
				}
				return
			}
		},
		
		onShow() {
			this.getAddressList()
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muAddressInfoState', 'muLabelList']),
			
			// 获取收货地址列表
			async getAddressList() {
				let res = await this.$u.api.addressList()
				this.addressList = res.data.list.map(v => {
					v.showMask = false
					return v
				})
			},
			
			// 长按显示遮罩操作栏 item = 地址列表某一项
			openMaskOperation(item) {
				this.addressList = this.addressList.map( v => {
					item.id == v.id ? v.showMask = true : v.showMask = false
					return v
				})
			},
			
			// 跳转页面 item = 地址列表某一项
			jumpPage(item) {
				// uni.setStorage({
				// 	key:'address',
				// 	data: item,
				// 	success: res => {
				// 		switch(this.comeFrom){
				// 			case '1': // 1 = 订单确认页
				// 			case '2': // 2 = 申请售后页（换货）
				// 			case '3': // 3 = 兔头商品订单确认页
				// 			case '4': // 4 = 门店确认订单页
				// 			this.muAddressInfoState(item)
				// 			this.jump.navigateBack()
				// 			console.log('跳转页面后的打印')
				// 			break
				// 		}
				// 	},
				// 	fail: err => {
				// 		console.log(err)
				// 	}
				// })
				
				switch(this.comeFrom){
					case '1': // 1 = 订单确认页
					case '2': // 2 = 申请售后页（换货）
					case '3': // 3 = 兔头商品订单确认页
					case '4': // 4 = 门店确认订单页
					case '5': // 5 = 售后详情页
					case '6': // 6 = 拍卖卖家订单详情
					case '7': // 7 = 拍卖买家售后详情
					case '8': // 8 = 拍品详情
					case '9': // 9 = 拍品创建
					case '10': // 10 = 添加发票
					case '99': // 99 = 拍卖我发布的
					case '100': // 100 = 实体卡
					this.muAddressInfoState(item)
					this.jump.navigateBack()
					console.log('跳转页面后的打印')
					break
					case '11':
					case '12':
						this.updateOrderAddress(item)
						break
				}
			},
			
			// 复制链接 item = 地址列表某一项
			copyAddress(item){
				uni.setClipboardData({
					data:item.consignee + item.consignee_phone + item.province_name + item.city_name + item.town_name + item.address,
					success:res=>{
						this.feedback.toast({title: '复制成功', icon: 'success'})
					}
				})
				
			},
			
			// 设置默认地址 id = 收货地址id
			async setDefaultAddress(id){
				console.log(id)
				await this.$u.api.setDefaultAddress({id, is_default:1})
				this.feedback.toast({title: '设置成功', icon: 'success'})
				this.getAddressList()
			},
			
			// 删除单个地址 id = 收货地址id
			async deleteAddressItem(id){
				await this.$u.api.deleteAddress({id:[id]})
				this.feedback.toast({title:'删除成功', icon:'success'})
				this.muAddressInfoState({})
				this.getAddressList()
			},
			
			// 关闭遮罩层操作栏 item = 地址列表某一项
			closeMaskOperation(item) {
				this.addressList = this.addressList.map( v => {
					if(item.id == v.id){
						v.showMask = false
					}
					return v
				})
			},
		    
			// 编辑地址 item = 地址列表某一项
			editAddress(item) {
				this.muAddressInfoState(item)
				this.labelList.forEach( v => {
					if( item.label && v != item.label ){
						this.labelList.push(item.label)
						this.muLabelList(Array.from(new Set( this.labelList )))
					}
				})
				this.jump.navigateTo(`${this.routeTable.pEAddressAdd}?isEdit=1`)
			},
		    
			// 判断是否全选所有地址
			isSelectAllAddress(){
				return this.addressList.length === this.addressSelectedList.length && this.addressSelectedList.length
			},
			
			// 全选/取消全选
			selectAll(){
				if(this.isSelectAllAddress()){
					this.addressList.forEach( v => {
						v.checked = false
					})
					this.addressSelectedList = []
				}else{
					this.addressSelectedList = this.addressList.map( v => {
						v.checked = true
						return v.id
					})
				}
			},
			
			// 选中单个/取消选中单个地址 item = 地址列表某一项
			selectSingle(item){
				if(this.addressSelectedList.indexOf(item.id) > -1){
					this.addressList.forEach( v => {
						if(v.id === item.id){
							v.checked = false
						}
					})
					this.addressSelectedList.splice(this.addressSelectedList.indexOf(item.id), 1)
				}else{
					this.addressList.forEach( v => {
						if(v.id === item.id){
							v.checked = true
						}
					})
					this.addressSelectedList.push(item.id)
				}
			},
			
			// 删除地址
			async deleteAddress(){
				if(this.addressList.length == 0) return this.feedback.toast({title:'亲，您还没有收货地址喔！'})
				if(this.addressList.length > 0 && this.addressSelectedList.length == 0) return this.feedback.toast({title:'请选择需要删除的地址'})
				await this.$u.api.deleteAddress({id:this.addressSelectedList})
				this.feedback.toast({title:'删除成功', icon:'success'})
				this.muAddressInfoState({})
				this.getAddressList()
			},
			updateOrderAddress(item) {
				const { province_id, city_id, town_id, province_name, city_name, town_name, address, consignee, consignee_phone } = item
				const { subOrderNo, orderType } = this.orderInfo
				const params = {
					sub_order_no: subOrderNo,
					order_type: orderType,
					type: 4,
					refund_reason: '修改地址',
					receive_province_id: province_id,
					receive_city_id: city_id,
					receive_area_id: town_id,
					receive_province: province_name,
					receive_city: city_name,
					receive_area: town_name,
					receive_address: address,
					receive_name: consignee,
					receive_phone: consignee_phone
				}
				this.$u.api.updateOrderAddress(params).then(() => {
					this.feedback.toast({title: '修改成功', icon: 'success'})
					this.jump.navigateBack()
				})
			}
		} 
	}
</script>

<style scoped></style>
