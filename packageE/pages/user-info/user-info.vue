<template>
  <view class="content">
    <!-- 导航栏 -->
    <vh-navbar title="个人信息" :show-border="true" />

    <!-- 用户板块 -->
    <view class="bg-ffffff pl-32 pr-32">
      <view class="pt-32 pb-32 d-flex j-sb a-center">
        <view class="" @click="image.previewImage([userInfo.avatar_image], 0)">
          <vh-image :src="userInfo.avatar_image" :loading-type="5" :width="80" :height="80" shape="circle" />
        </view>
        <!-- <vh-image :src="`${userInfo.avatar_image}&t=${date.getTimeStamp()}`" :loading-type="5" :width="80" :height="80" shape="circle" /> -->
        <view class="d-flex a-center" @click="showAvatarPop = true">
          <text class="mr-16 font-32 text-9 l-h-44">修改头像</text>
          <u-icon name="arrow-right" :size="20" color="#999" />
        </view>
      </view>

      <view
        class="pt-32 pb-32 d-flex j-sb a-center text-3 font-32 l-h-44 bt-s-01-eeeeee"
        @click="jump.navigateTo(routeTable.pEUpdateNickname)"
      >
        <text class="font-wei">昵称</text>
        <text>{{ userInfo.nickname }}</text>
      </view>

      <view class="pt-32 pb-32 d-flex j-sb a-center text-3 font-32 l-h-44 bt-s-01-eeeeee" @click="showGenderPop = true">
        <text class="font-wei">性别</text>
        <text>{{ genderList[userInfo.sex] }}</text>
      </view>

      <view
        class="pt-32 pb-32 d-flex j-sb a-center text-3 font-32 l-h-44 bt-s-01-eeeeee"
        @click="showBirthdayPicker = true"
      >
        <text class="font-wei">生日</text>
        <text>{{ userInfo.birthday }}</text>
      </view>
    </view>

    <!-- 间隔槽 -->
    <vh-gap :height="16" bg-color="#f5f5f5" />

    <!-- 认证、测试板块 -->
    <view class="bg-ffffff mt-16 pl-32 pr-32">
      <view
        class="pt-32 pb-32 d-flex j-sb a-center bb-s-01-eeeeee"
        @click="
          jump.navigateTo(userInfo.certified_info ? routeTable.pECertificationDetail : routeTable.pECertificationApply)
        "
      >
        <text class="font-32 font-wei text-3 l-h-44">认证</text>
        <view class="d-flex a-center">
          <text class="mr-16 font-32 l-h-44" :class="userInfo.certified_info ? 'text-3' : 'text-9'">{{
            userInfo.certified_info ? userInfo.certified_info : '去认证'
          }}</text>
          <u-icon name="arrow-right" :size="20" color="#999" />
        </view>
      </view>
      <view
        class="pt-32 pb-32 d-flex j-sb a-center bb-s-01-eeeeee"
        @click="jump.navigateTo($routeTable.pHAuctionNavigation)"
      >
        <text class="font-32 font-wei text-3 l-h-44">拍卖记录</text>
        <view class="d-flex a-center">
          <u-icon name="arrow-right" :size="20" color="#999" />
        </view>
      </view>
    </view>

    <!-- 修改头像 -->
    <u-popup v-model="showAvatarPop" :border-radius="20" :safe-area-inset-bottom="true" mode="bottom">
      <view class="d-flex flex-column">
        <!-- 上传组件 -->
        <!-- https://images.wineyun.com/vinehoo/client/user/avatar/fl8u_1654688511922.jpg -->
        <vh-upload
          ref="uUpload"
          :directory="'vinehoo/client/user/avatar/'"
          :plate="2"
          :show-upload-list="false"
          :max-count="1"
          :multiple="false"
          @on-uploaded="onUploaded"
          @takePhoto="takePhoto"
        />

        <view class="mb-46">
          <u-button
            shape="circle"
            :hair-line="false"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{ width: '646rpx', color: '#333', backgroundColor: '#EEE', border: 'none' }"
            @click="showAvatarPop = false"
            >取消</u-button
          >
        </view>
      </view>
    </u-popup>

    <!-- 修改性别 -->
    <u-popup v-model="showGenderPop" :border-radius="20" :safe-area-inset-bottom="true" mode="bottom">
      <view class="d-flex flex-column">
        <view class="mt-48 ml-52 text-3 font-36 font-wei l-h-50">性别</view>

        <view class="mt-74 mb-74 d-flex j-sa a-center pl-68 pr-68">
          <image
            class="w-180 h-180"
            :src="`${osip}/user_info/man${userInfo.sex == 2 ? '_sel' : ''}.png`"
            mode="aspectFill"
            @click="changeSex(2)"
          ></image>
          <image
            class="w-180 h-180"
            :src="`${osip}/user_info/woman${userInfo.sex == 1 ? '_sel' : ''}.png`"
            mode="aspectFill"
            @click="changeSex(1)"
          ></image>
        </view>

        <view class="mb-46">
          <u-button
            @click="showGenderPop = false"
            shape="circle"
            :hair-line="false"
            :ripple="true"
            ripple-bg-color="#FFF"
            :custom-style="{ width: '646rpx', color: '#333', backgroundColor: '#EEE', border: 'none' }"
            >取消</u-button
          >
        </view>
      </view>
    </u-popup>

    <!-- 修改生日 -->
    <u-picker
      mode="time"
      v-model="showBirthdayPicker"
      :params="birthdayParams"
      confirm-color="#E80404"
      @confirm="changeBirthday"
    />
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'user-info',

  data() {
    return {
      osip: 'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
      genderList: ['未知', '女', '男'], //性别
      birthdayParams: {
        //生日时间参数
        year: true,
        month: true,
        day: true,
        hour: false,
        minute: false,
        second: false,
      },
      showAvatarPop: false, //修改头像框
      uploadFileList: [], //上传完成的文件列表
      showGenderPop: false, //修改性别弹框
      showBirthdayPicker: false, //修改生日
    }
  },
  onLoad() {
    const userinfo = uni.getStorageSync('UserInfo')
    if (userinfo) {
      this.muUserInfo(userinfo)
    }
    window.vhAppSendMessage = (obj = {}) => {
      console.log('obj', obj)
      if (typeof obj === 'string') obj = JSON.parse(obj)
      const { type = 1 } = obj
      if (type === 1) return { type, data: '酒云网' }
      if (type === 4) {
        const images = JSON.parse(obj.data).images
        console.log('uploadPictureInfo', images)
        uploadPictureInfo && uploadPictureInfo(images)
        window.uploadPictureInfo = null
      }
      if (type === 6) {
        // APP收银台支付成功
      }
      if (type === 7) {
        // APP收银台取消支付
      }
      if (type === 8) {
        // 拉起APP支付失败
        onPullAppPayFail && onPullAppPayFail()
      }
      if (type == 10) {
        indexTopRefresh && indexTopRefresh()
      }
      if (type == 11) {
        this.handlePhotoMessage(obj)
      }
      return ''
    }
  },
  computed: {
    //Vuex 辅助state函数
    ...mapState(['userInfo', 'routeTable', 'ossPrefix']),
  },
  watch: {},
  methods: {
    // Vuex mapMutations辅助函数
    ...mapMutations(['muUserInfo']),

    takePhoto() {
      console.log('调用相机成功')
      wineYunJs.setDataFromApp('11')
    },

    handlePhotoMessage(data) {
      if (data && data.type === 11) {
        let name = String(new Date().getTime())
        const base64String = 'data:image/jpeg;base64,' + data.data
        // 创建 blob URL
        const blobUrl = this.createBlobUrl(base64String)
        console.log('生成的 blob URL:', blobUrl)
        // 继续上传到服务器
        this.uploadPhoto(blobUrl, this.dataURLtoFile(base64String, name))
      }
    },

    createBlobUrl(base64Data) {
      // 解析 base64 数据
      let arr = base64Data.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n)

      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }

      // 创建 Blob 对象
      const blob = new Blob([u8arr], { type: mime })

      // 生成并返回 blob URL
      return URL.createObjectURL(blob)
    },

    dataURLtoFile(dataurl, fileName) {
      // 将base64转换为blob
      let arr = dataurl.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      var blob = new Blob([u8arr], { type: mime })

      // 直接创建并返回File对象
      return new File([blob], fileName, { type: mime, lastModified: new Date().getTime() })
    },

    async uploadPhoto(photoPath, file) {
      // 确保弹窗显示
      this.showAvatarPop = true

      // 等待组件渲染完成
      await this.$nextTick()

      // 获取vh-upload组件实例
      const uploadComponent = this.$refs.uUpload
      if (!uploadComponent) {
        console.error('上传组件未初始化，尝试重新获取组件实例...')
        // 延迟100ms再次尝试
        await new Promise((resolve) => setTimeout(resolve, 100))
        const retryComponent = this.$refs.uUpload
        if (!retryComponent) {
          this.feedback.toast({ title: '上传组件未初始化' })
          return
        }
        uploadComponent = retryComponent
      }

      // 构造一个完整格式的临时文件对象
      const tempFile = {
        fileType: 'image',
        cerType: null,
        videoCoverImg: '',
        url: photoPath,
        progress: 0,
        error: false,
        file,
        response: null,
      }

      // 将临时文件添加到上传列表
      uploadComponent.lists.push(tempFile)

      // 调用组件的上传方法
      await uploadComponent.uploadFile(uploadComponent.lists.length - 1)
    },

    // 所有上传成功
    onUploaded(list, index) {
      console.log('-------上传所有文件成功')
      console.log(list)
      this.uploadFileList = list
      this.changeAvatar(list[0])
    },

    // 修改头像 avatarInfo = 上传的头像信息
    async changeAvatar(avatarInfo) {
      console.log(avatarInfo)
      await this.$u.api.updateUserInfo({ avatar_image: avatarInfo.response })
      // 添加时间戳以强制刷新图片缓存
      this.userInfo.avatar_image = this.ossPrefix + avatarInfo.response + '?t=' + new Date().getTime()
      this.muUserInfo(this.userInfo)
      // 强制更新头像组件
      this.$nextTick(() => {
        const vhImage = this.$el.querySelector('vh-image')
        if (vhImage) {
          vhImage.$forceUpdate()
        }
      })
      this.feedback.toast({ title: '上传成功', icon: 'success' })
      this.showAvatarPop = false
    },

    // 修改性别
    async changeSex(sex) {
      await this.$u.api.updateUserInfo({ sex })
      this.userInfo.sex = sex
      this.muUserInfo(this.userInfo)
      this.showGenderPop = false
    },

    // 修改生日
    async changeBirthday(e) {
      let birthday = `${e.year}-${e.month}-${e.day}`
      await this.$u.api.updateUserInfo({ birthday })
      this.userInfo.birthday = birthday
      this.muUserInfo(this.userInfo)
      this.showBirthdayPicker = false
    },
  },
}
</script>

<style scoped></style>
