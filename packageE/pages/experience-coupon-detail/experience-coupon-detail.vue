<template>
	<view class="content">
		<!-- 导航栏 -->
		<u-navbar back-icon-color="#FFF" :background="{background: '#E80404'}" title="体验券详情" title-size="36" :title-bold="true" title-color="#FFF" />
		
		<!-- banner -->
		<view class="p-abso w-p100 h-164 bg-e80404" :style="{ top: system.navigationBarHeight() + 'px' }" />
		
		<!-- 核销码背景 -->
		<view class="p-rela z-01 d-flex j-center mt-32">
			<view class="w-730 h-878">
				<image class="p-abso top-0 w-730 h-878" :src="`${osip}/experience_coupon_detail/bg.png`" mode="aspectFill" />
				
				<view class="p-rela z-02 w-730 h-658 d-flex flex-column j-center a-center">
					<view v-if="timeout" class="p-rela w-326 h-326" @click="init(1)">
						<vh-image :loading-type="2" :src="writeOffCode" :width="326" :height="326" />
						<view class="p-abso top-0 z-100 w-326 h-326 bg-000-000-000-060 d-flex flex-column a-center">
							<view class="mt-124 font-48 font-wei text-ffffff">已超时</view>
							<view class="mt-74 font-24 text-ffffff">点我刷新</view>
						</view>
					</view>
					<vh-image v-else :loading-type="2" :src="writeOffCode" :width="326" :height="326" />
					<view class="mt-34 font-42 text-3">{{experienceCouponInfo.write_off_code}}</view>
					<view class="mt-60 font-24 text-c3c3c3">注：本体验券不支持折现退款，店员核销后作废</view>
				</view>
				
				<view class="p-rela z-02 w-730 h-220 d-flex j-center a-center ptb-00-plr-42">
					<vh-image :loading-type="2" :src="experienceCouponInfo.available_img" :width="190" :height="86"></vh-image>
					<view class="w-01 h-76 bg-eeeeee ml-20"></view>
					
					<view class="ml-20">
						<view class="font-28 font-wei text-3">{{experienceCouponInfo.c_name}}</view>
						<view class="mt-08 font-24 text-6">有效期：{{experienceCouponInfo.addtime}}-{{experienceCouponInfo.endtime}}</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 使用须知 -->
		<view class="mt-42 mr-24 ml-24 pb-42">
			<!-- 使用要求 -->
			<!-- <view class="">
				<view class="font-28 text-3">使用要求</view>
				<view class="mt-12 font-24 text-9 l-h-34">仅限重庆渝北区光环门店堂饮使用。</view>
			</view> -->
			
			<!-- 使用规则 -->
			<view class="mt-42">
				<view class="font-28 text-3">使用要求</view>
				<view class="mt-12 font-24 text-9 l-h-34" v-html="experienceCouponInfo.content"></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name:"experience-coupon-detail",
		
		data() {
			return {
				osip:'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				experienceCouponId:'', //体验券id
				experienceCouponInfo:{}, //体验券信息
				writeOffCode:'', //核销码
				timer:null,//定时器
				count:'',//倒计时
				timeout:true,//体验券超时显示
			}
		},
		
		onLoad(options) {
			this.experienceCouponId = parseInt(options.id)
			this.init()
		},
		
		methods: {
			// 初始化（ 获取体验券详情、获取核销码 ）type = 请求类型（ 0 = 初始化、1 = 核销码超时更新）
			async init( type = 0 ) {
				this.feedback.loading({ title: type ? '更新中...' : '加载中...'})
				await this.getExperienceCouponInfo()
				await this.getWriteOffCode()
				this.feedback.hideLoading()
				this.count = 30
				this.timer = setInterval(()=>{
					this.count --
					console.log(this.count)
					if(this.count === 0){
						console.log('倒计时结束，销毁定时器')
						this.clearTimer()
					}
				},1000)
			},
			
			// 获取体验券详情
			async getExperienceCouponInfo() {
				let res = await this.$u.api.experienceCouponInfo({ id: this.experienceCouponId })
				this.experienceCouponInfo = res.data
			},
			
			// 获取核销码
			async getWriteOffCode() {
				let res = await this.$u.api.writeOffCode({ code: this.experienceCouponInfo.write_off_code })
				this.writeOffCode = res.data
				this.timeout = false
			},
			
			// 清除定时器
			clearTimer() {
				if(this.timer) {
				    clearInterval(this.timer);  
				    this.timer = null;  
					this.timeout = true
				} 
			}
			
		},
		
		onUnload(){
		   this.clearTimer()
		}
	}
</script>

<style scoped></style>