<template>
	<view class="content">
		<!-- 导航栏 -->
		<view v-if="!from" class="bb-s-01-eeeeee">
			<u-navbar back-icon-color="#333" title="大转盘" title-size="36" :title-bold="true" title-color="#333" />
		</view>
		
		<!-- 中奖记录 -->
		<view class="">
			<!-- 有中奖记录（中奖列表大于0） -->
			<view v-if="priceRecordList.length" class="">
				<view class="d-flex j-sb a-center bb-s-01-eeeeee ml-32 mr-32 ptb-32-plr-00" v-for="(item, index) in priceRecordList" :key="index">
					<view class="" v-if="item.award.indexOf('谢谢')>=0">
						<text class="font-28 text-6">非常遗憾，</text>
						<text class="font-28 text-e80404">未中奖！</text>
					</view>
					<view class="" v-else>
						<text class="font-28 text-6">恭喜您，获得</text>
						<text class="font-28 text-e80404">{{item.award}}！</text>
					</view>
					
					<view class="font-24 text-9 ml-08 flex-shrink">{{item.addtime}}</view>
				</view>
				
				<view class="ptb-32-plr-00">
					<u-loadmore :status="loadStatus" />
				</view>
			</view>
			
			<!-- 没有中奖记录 -->
			<view v-else class="mt-p40">
				<vh-empty :padding-top="52" :padding-bottom="100" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png" text="暂无中奖记录~" :text-bottom="0" />
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:'large-turntable-prize-record',
		data() {
			return {
				from:'', //从哪个端进入 1 = 安卓、2 = ios"
				priceRecordList:[], //抽奖记录列表
				page: 1, //第几页
				limit: 20, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //底部加载状态
			}
		},
		
		onLoad(options) {
			this.from = options.from
			this.getLargeTurntablePriceRecord()
		},
		
		methods: {
			// 获取大转盘抽奖记录
			async getLargeTurntablePriceRecord() {
				let res = await this.$u.api.largeTurntablePriceRecord({ page: this.page, limit: this.limit})
				let { total, list } = res.data
				this.page == 1 ? this.priceRecordList = list : this.priceRecordList = [...this.priceRecordList, ...list]
				this.totalPage = Math.ceil( total / this.limit )
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				console.log(res)
			}
		},
		
		onReachBottom() {
			if (this.page == this.totalPage) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getLargeTurntablePriceRecord()
		}
	}
</script>

<style scoped></style>
