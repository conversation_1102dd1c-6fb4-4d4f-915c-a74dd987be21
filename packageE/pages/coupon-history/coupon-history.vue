<template>
	<view>
		<vh-navbar title="历史优惠券" :show-border="true" />
		<view v-if="!loading">
			<view class="d-flex h-90 bg-ffffff">
				<view v-for="item in tabList" :key="item.value" class="p-rela flex-c-c w-p50 h-p100" @click="onTabChange(item)">
					<view class="font-wei-600 font-28 text-3">{{ item.text }}</view>
					<view v-if="query.type === item.value" class="p-abso bottom-0 w-36 h-08 bg-e80404 b-rad-04"></view>
				</view>
			</view>
			<view v-if="list.length" class="p-24">
				<view v-for="(item, index) in list" :key="item.id" :class="[index ? 'mt-24' : '']">
					<CouponListItem :item="item" :isUsed="query.type === 2" :isFailed="query.type === 3"></CouponListItem>
				</view>
			</view>
			<view v-else class="p-rela flex-c-c mt-176">
				<image :src="ossIcon('/empty/emp_cou.png')" class="w-440 h-360"></image>
				<view class="p-abso bottom-0 font-28 text-9 l-h-40">暂无优惠券</view>
			</view>
		</view>
	</view>
</template>

<script>
import listMixin from '@/common/js/mixins/listMixin'

export default{
	mixins: [listMixin],
	data: () => ({
		tabList: [
			{
				value: 2,
				text: '已使用'
			},
			{
				value: 3,
				text: '已失效'
			},
		],
		query: {
			type: 2,
		}
	}),
	onLoad() {
		this.load()
	},
	onPullDownRefresh() {
		this.pullDownRefresh()
	},
	onReachBottom () {
    this.reachBottomLoad()
  },
	methods: {
		async load (query) {
			const res = await this.$u.api.getCouponList(query)
			const { list = [] } = res?.data || {}
			this.list = query.page === 1 ? list : this.list.concat(list)
			return res
		},
		onTabChange(item) {
			this.load({ ...this.query, type: item.value, page: 1 })
		}
	},
}
</script>

<style>
	page {
		background-color: #F5F5F5;
	}
</style>