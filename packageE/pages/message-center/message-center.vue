<template>
  <view>
    <!-- 导航栏 -->
    <vh-navbar title="消息中心" height="46">
      <view v-if="isShowMarkBtn" slot="right" class="d-flex a-center">
        <image
          class="fade-in w-44 h-44 pt-12 pr-24 pb-12"
          src="https://images.vinehoo.com/vinehoomini/v3/message_center/clean.png"
          @click="markReadMessage()"
        />
      </view>
    </vh-navbar>

    <!-- 数据加载完成 -->
    <view v-if="!loading" class="">
      <!-- tabs选项卡 -->
      <view class="msg-center__tabs p-stic z-980 bt-s-02-eeeeee" style="top: 46px">
        <u-tabs
          ref="uTabs"
          :list="tabList"
          :current="currentTabs"
          :height="92"
          :font-size="32"
          inactive-color="#333333"
          active-color="#E80404"
          :offset="[15, 40]"
          :bar-width="36"
          :bar-height="8"
          :bar-style="{ background: 'linear-gradient(214deg, #FF8383 0%, #E70000 100%)' }"
          :is-scroll="false"
          @change="changeTabs"
        ></u-tabs>
      </view>

      <!-- 消息列表 -->
      <view class="">
        <!-- 系统通知列表 -->
        <view v-show="currentTabType === MsgType.System" class="">
          <!-- 列表不为空 -->
          <view v-if="messagePushList.length" class="fade-in pt-24">
            <view v-for="(item, index) in messagePushList" :key="index">
              <!-- data_type == 0 公告 -->
              <view v-if="item.data_type == 0" class="bg-ffffff mr-24 mb-20 ml-24 pt-32 pr-24 pb-24 pl-24">
                <view class="d-flex a-center">
                  <image
                    class="w-30 h-30"
                    src="https://images.vinehoo.com/vinehoomini/v3/message_center/not_red.png"
                    mode="widthFix"
                  ></image>
                  <text class="ml-08 font-28 font-wei-500 text-3">公告</text>
                </view>

                <view class="mt-16 font-24 text-6 l-h-40">{{ item.notice_data.content }}</view>
              </view>

              <!-- data_type == 1 商品详情 -->
              <view
                v-if="item.data_type == 1"
                class="bg-ffffff mr-24 mb-20 ml-24 ptb-00-plr-20 b-rad-10"
                @click="jump.appAndMiniJump(1, `${routeTable.pgGoodsDetail}?id=${item.goods_data.id}`, $vhFrom)"
              >
                <view v-if="item.goods_data.data_from == '1'" class="d-flex a-center bb-d-01-eeeeee ptb-20-plr-00">
                  <text class="font-26 font-wei">您想要的商品上线啦！</text>
                  <image class="w-20 h-28" :src="ossIcon(`/message_center/lightning.png`)" mode="aspectFill"></image>
                </view>
                <view class="ptb-24-plr-00">
                  <view class="d-flex">
                    <vh-image
                      :loading-type="2"
                      :src="item.goods_data.banner_img"
                      :width="284"
                      :height="176"
                      :border-radius="6"
                    />

                    <view class="flex-1 d-flex flex-column j-sb ml-18">
                      <view class="">
                        <view class="o-hid text-hidden-2">
                          <vh-channel-title-icon
                            :channel="item.goods_data.periods_type"
                            :border-radius="4"
                            :font-size="20"
                            :font-bold="false"
                          />
                          <text class="ml-06 font-24 text-3 l-h-34">{{ item.goods_data.title }}</text>
                        </view>

                        <view class="mt-08 font-22 text-6 l-h-34 o-hid text-hidden-1">{{ item.goods_data.brief }}</view>
                      </view>

                      <view class="d-flex j-end a-center">
                        <view
                          v-if="item.goods_data.data_from == 1 && [2].includes(item.goods_data.onsale_status)"
                          class="w-128 h-38 bg-e80404 flex-c-c b-rad-24 font-24 text-ffffff"
                          >立即购买</view
                        >
                        <view v-else class="font-22 text-9 l-h-34"
                          >已售{{ item.goods_data.purchased + item.goods_data.vest_purchased }}/限量{{
                            item.goods_data.limit_number
                          }}份</view
                        >
                      </view>
                    </view>
                  </view>
                  <view
                    class="flex-sb-c"
                    :class="[
                      item.goods_data.is_hidden_price == '1' || [3, 4].includes(item.goods_data.onsale_status)
                        ? 'mt-16'
                        : 'mt-10',
                    ]"
                  >
                    <view class="font-24 text-9 l-h-34">{{ item.goods_data.created_at }}</view>
                    <view
                      v-if="item.goods_data.is_hidden_price == '1' || [3, 4].includes(item.goods_data.onsale_status)"
                      class="font-24 text-6 l-h-34"
                      >价格保密</view
                    >
                    <view v-else class="d-flex a-baseline text-e80404">
                      <view class="font-18 l-h-26">¥</view>
                      <view class="font-28 l-h-40">{{ item.goods_data.price }}</view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- <view v-if="item.data_type == 1" class="bg-ffffff mr-24 mb-20 ml-24 p-20 b-rad-10 d-flex" @click="jump.navigateTo(`/pages/goods-detail/goods-detail?id=${item.goods_data.id}`)">
								<vh-image :loading-type="2" :src="item.goods_data.banner_img" :width="284" :height="176" :border-radius="6" />
								
								<view class="flex-1 d-flex flex-column j-sb ml-18">
									<view class="">
										<view class="o-hid text-hidden-2">
											<vh-channel-title-icon :channel="item.goods_data.periods_type" :border-radius="4" :font-size="20" :font-bold="false" />
											<text class="ml-06 font-24 text-3 l-h-34">{{item.goods_data.title}}</text>
										</view>
										
										<view class="mt-08 font-20 text-6 l-h-34 o-hid text-hidden-1">{{item.goods_data.brief}}</view>
									</view>
									
									<view class="d-flex j-sb a-center">
										<text v-if="item.goods_data.is_hidden_price == 1 || [3,4].includes(item.goods_data.onsale_status)" class="font-24 text-ff0013 l-h-34">价格保密</text>
										<text v-else class="font-28 font-wei text-ff0013 l-h-28"><text class="font-18">¥</text>{{item.goods_data.price}}</text>
										<text class="font-22 text-9 l-h-34">已售{{item.goods_data.purchased + item.goods_data.vest_purchased}}/限量{{item.goods_data.limit_number}}份</text>
									</view>
								</view>
							</view> -->

              <!-- data_type == 2 帖子详情 -->
              <view
                v-if="item.data_type == 2"
                class="bg-ffffff mr-24 mb-20 ml-24 pt-32 pr-24 pb-24 pl-24"
                @click="itemClick(item)"
              >
                <view class="d-flex a-center">
                  <image
                    class="w-30 h-30"
                    src="https://images.vinehoo.com/vinehoomini/v3/message_center/top_blu.png"
                    mode="widthFix"
                  />
                  <text class="ml-08 font-28 text-3 font-wei text-hidden-1">{{ item.posts_data.title }}</text>
                </view>

                <view class="mt-16 font-24 text-6 l-h-40 text-hidden-1 o-hid">{{ item.posts_data.content }}</view>
              </view>

              <!-- data_type == 3 活动 -->
              <view
                v-if="item.data_type == 3"
                class="bg-ffffff mr-24 mb-20 ml-24 p-20 b-rad-10 d-flex"
                @click="itemClick(item)"
              >
                <!-- <image class="w-186 h-186 b-rad-06" src="https://images.vinehoo.com/vinehoomini/v3/mine/lv_low.png" mode="aspectFill"></image> -->
                <vh-image
                  :loading-type="2"
                  :src="item.activity_data.image"
                  :width="186"
                  :height="186"
                  :border-radius="5"
                />

                <view class="flex-1 d-flex flex-column j-sb ml-24">
                  <view class="">
                    <view class="font-28 font-wei text-hidden-2 l-h-40">{{ item.activity_data.activity_name }}</view>
                    <view class="mt-12 font-24 text-9 text-hidden-1">{{ item.activity_data.sub_title }}</view>
                  </view>

                  <view class="d-flex j-sb a-center">
                    <text class="font-24 text-9 l-h-34"
                      >{{ item.activity_data.start_at }}-{{ item.activity_data.end_at }}</text
                    >
                    <u-button
                      shape="circle"
                      :hair-line="false"
                      :ripple="true"
                      ripple-bg-color="#FFF"
                      :custom-style="{
                        width: '120rpx',
                        height: '38rpx',
                        fontSize: '24rpx',
                        color: '#FFF',
                        backgroundColor: '#E80404',
                        border: 'none',
                      }"
                      @click="itemClick(item)"
                      >点击查看</u-button
                    >
                  </view>
                </view>
              </view>

              <!-- data_type == 4 酒闻详情 -->
              <view
                v-if="item.data_type == 4"
                class="bg-ffffff mr-24 mb-20 ml-24 p-20 b-rad-10 d-flex j-sb"
                @click="
                  jump.appAndMiniJump(
                    1,
                    `/packageD/pages/wine-smell-detail/wine-smell-detail?id=${item.news_data.id}`,
                    $vhFrom
                  )
                "
              >
                <vh-image :loading-type="4" :src="item.news_data.img" :width="188" :height="188" :border-radius="6" />

                <view class="ml-20 flex-1 d-flex flex-column j-sb">
                  <view class="">
                    <view class="font-28 text-3 l-h-36 text-hidden-2">{{ item.news_data.title }}</view>
                    <view class="mt-12 font-24 text-9 l-h-34 o-hid text-hidden-1">{{ item.news_data.abst }}</view>
                  </view>

                  <view class="mt-36 d-flex j-end">
                    <view class="d-flex a-center mr-56">
                      <image
                        class="w-26 h-26"
                        src="https://images.vinehoo.com/vinehoomini/v3/comm/view.png"
                        mode="widthFix"
                      />
                      <text class="ml-06 font-24 text-9 l-h-34">{{ item.news_data.viewnums | numToThousands }}</text>
                    </view>
                    <view class="d-flex a-center">
                      <image
                        class="w-26 h-26"
                        src="https://images.vinehoo.com/vinehoomini/v3/comm/comm.png"
                        mode="widthFix"
                      />
                      <text class="ml-06 font-24 text-9 l-h-34">{{ item.news_data.commentnums | numToThousands }}</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 分割线 -->
              <!-- <view v-once class="">
								<vh-split-line :line-height="1" />
							</view> -->
            </view>

            <view class="pb-24">
              <u-loadmore bg-color="#F5F5F5" :status="loadStatus" />
            </view>
          </view>

          <!--列表（空） -->
          <view v-else class="h-p100 bg-ffffff">
            <vh-empty
              :padding-top="170"
              :padding-bottom="820"
              image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_send.png"
              :text-bottom="0"
              text="暂无消息通知~"
            ></vh-empty>
          </view>
        </view>

        <AuctionMsg v-show="currentTabType === MsgType.Auction" ref="auctionMsgRef" isFromMsgCenter />

        <!-- 用户通知列表 -->
        <view v-show="currentTabType === MsgType.User" class="">
          <!-- 列表不为空 -->
          <view v-if="messagePushList.length" class="fade-in pt-24">
            <view
              class="p-rela bg-ffffff mr-24 mb-20 ml-24 pt-32 pr-24 pb-32 pl-24 b-rad-10"
              v-for="(item, index) in messagePushList"
              :key="index"
              @click="itemClick(item)"
            >
              <!-- 新增粉丝 -->
              <view v-if="item.data_type == 11">
                <view class="d-flex j-sb a-center">
                  <view class="d-flex a-center">
                    <image
                      class="w-30 h-30"
                      src="https://images.vinehoo.com/vinehoomini/v3/message_center/fans_pup.png"
                      mode="widthFix"
                    ></image>
                    <text class="ml-08 font-28 font-wei text-3 l-h-40">新增粉丝</text>
                  </view>
                  <view class="font-24 text-9">{{ item.fans_data.created_at }}</view>
                </view>

                <view class="mt-16 font-24 text-6 text-hidden-4">{{ item.fans_data.content }}</view>
              </view>

              <!-- 评论板块（帖子、视频） -->
              <!-- 评论帖子 -->
              <view v-if="item.data_type == 12">
                <view class="d-flex j-sb a-center">
                  <view class="">
                    <text class="font-24 text-9">{{ item.comment_data.nickname }}</text>
                    <text class="font-28 font-wei text-2b8cf7">评论了</text>
                    <text class="font-28 font-wei text-3">{{ item.comment_data.type_title }}</text>
                  </view>
                  <view class="font-24 text-9">{{ item.comment_data.created_at }}</view>
                </view>

                <view class="w-520 mt-16 font-24 text-6 text-hidden-1">{{ item.comment_data.content }}</view>
              </view>

              <!-- 回复板块 -->
              <view v-if="item.data_type == 13">
                <view class="d-flex j-sb a-center">
                  <view class="">
                    <text class="font-24 text-9">{{ item.reply_data.nickname }}</text>
                    <text class="font-28 font-wei text-ff9127">回复了</text>
                    <text class="font-28 font-wei text-3">您的评论</text>
                  </view>
                  <view class="font-24 text-9">{{ item.reply_data.created_at }}</view>
                </view>

                <view class="w-520 mt-16 font-24 text-6 text-hidden-1">{{ item.reply_data.content }}</view>
              </view>

              <!-- 点赞板块 （帖子，视频） -->
              <view v-if="item.data_type == 14">
                <view class="d-flex j-sb a-center">
                  <view class="">
                    <text class="font-24 text-9">{{ item.digg_data.nickname }}</text>
                    <text class="font-28 font-wei text-e80404">点赞了</text>
                    <text class="font-28 font-wei text-3">{{ item.digg_data.type_title }}</text>
                  </view>
                  <view class="font-24 text-9">{{ item.digg_data.created_at }}</view>
                </view>
                <view class="w-520 mt-16 font-24 text-6 text-hidden-1">{{ item.digg_data.content }}</view>
              </view>

              <!-- 驳回板块 -->
              <!-- 帖子、视频、帖子违反用户内容发布协议 -->
              <view v-if="item.data_type == 16">
                <view class="d-flex j-sb a-center">
                  <view class="font-28 font-wei text-3">{{ item.reject_data.title }}</view>
                  <view class="font-24 text-9">{{ item.reject_data.created_at }}</view>
                </view>
                <view class="w-520 mt-16 font-24 text-6 text-hidden-1">{{ item.reject_data.content }}</view>
                <view v-if="item.reject_data.refuse_pid == 0" class="mt-16 font-24 text-2b8cf7">{{
                  item.reject_data.type == 2 ? '重新提交' : '编辑重发'
                }}</view>
                <view v-else class="mt-16 font-24 text-9">已重新提交</view>
              </view>

              <!-- 预约直播 -->
              <view v-if="item.data_type == 17">
                <view class="d-flex j-sb a-center">
                  <view class="d-flex a-center">
                    <image
                      class="w-30 h-30"
                      src="https://images.vinehoo.com/vinehoomini/v3/message_center/bro_ora.png"
                      mode="widthFix"
                    ></image>
                    <text class="ml-08 font-28 font-wei text-3 l-h-40">直播预约</text>
                  </view>
                  <view class="font-24 text-9">{{ item.live_data.created_at }}</view>
                </view>

                <view class="mt-16 font-24 text-6 text-hidden-4">{{ item.live_data.content }}</view>
              </view>

              <!-- 违反社区相关公约、涉嫌发布明感词/广告等违规内容、订单通知 -->
              <view v-if="item.data_type == 18">
                <view class="d-flex j-sb a-center">
                  <view class="font-28 font-wei text-3">{{ item.common_data.title }}</view>
                  <view class="font-24 text-9">{{ item.common_data.created_at }}</view>
                </view>
                <view class="w-520 mt-16 font-24 text-6 text-hidden-3">{{ item.common_data.content }}</view>
              </view>

              <view v-if="item.is_read == 0" class="p-abso right-22 bottom-40 w-16 h-16 bg-eb0404 b-rad-p50" />
            </view>

            <view class="pb-24">
              <u-loadmore bg-color="#F5F5F5" :status="loadStatus" />
            </view>
          </view>

          <!--列表（空） -->
          <view v-else class="bg-ffffff h-p100">
            <vh-empty
              :padding-top="170"
              :padding-bottom="820"
              image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_msg.png"
              :text-bottom="0"
              text="暂无消息通知~"
            ></vh-empty>
          </view>
        </view>

        <!-- 物流通知列表 -->
        <view v-show="currentTabType === MsgType.Logistics" class="">
          <!-- 列表不为空 -->
          <view v-if="messagePushList.length" class="fade-in pt-24">
            <view
              class="bg-ffffff mb-20 ml-24 mr-24 p-24"
              v-for="(item, index) in messagePushList"
              :key="index"
              @click="jumpPage(item)"
            >
              <template v-if="item.logistics_data">
                <view class="d-flex j-sb a-center">
                  <text class="font-34 text-0 font-wei l-h-48">{{ item.logistics_data.title }}</text>
                  <text class="font-24 text-9 l-h-34">{{ item.logistics_data.created_at }}</text>
                </view>

                <view class="p-rela h-124 bg-f8f8f8 mt-20 d-flex">
                  <vh-image :loading-type="2" :src="item.logistics_data.cover" :width="200" :height="124" />
                  <view class="flex-1 h-124 ptb-14-plr-20">
                    <view class="font-28 text-6 l-h-48 o-hid text-hidden-1">{{ item.logistics_data.content }}</view>
                    <view class="mt-08 font-28 text-9 l-h-40">查看详情>></view>
                  </view>
                  <view v-if="item.is_read == 0" class="p-abso right-n-04 top-n-06 w-16 h-16 bg-eb0404 b-rad-p50" />
                </view>
              </template>
            </view>

            <view class="pb-24">
              <u-loadmore bg-color="#F5F5F5" :status="loadStatus" />
            </view>
          </view>

          <!--列表（空） -->
          <view v-else class="bg-ffffff h-p100">
            <vh-empty
              :padding-top="170"
              :padding-bottom="820"
              image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_log.png"
              :text-bottom="0"
              text="暂无物流通知~"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 骨架屏 -->
    <view v-else class=""></view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import AuctionMsg from '@/packageH/pages/auction-msg/auction-msg'

const MsgType = {
  System: 0,
  User: 1,
  Logistics: 2,
  Auction: 99,
}

export default {
  name: 'message-center',
  components: {
    AuctionMsg,
  },

  data() {
    return {
      loading: true, //加载状态 true = 加载中、false = 结束加载
      MsgType,
      tabList: [
        { name: '系统通知', count: 0, type: MsgType.System, unreadNumKey: 'system_num' },
        // { name: '拍卖消息', count: 0, type: MsgType.Auction, unreadNumKey: 'auction_num' },
        { name: '用户通知', count: 0, type: MsgType.User, unreadNumKey: 'user_num' },
        { name: '物流通知', count: 0, type: MsgType.Logistics, unreadNumKey: 'logistics_num' },
      ],
      currentTabs: 0, //当前选中tabs
      messagePushList: [], //消息推送列表
      loadStatus: 'loadmore', //底部加载状态
      query: { page: 1, limit: 10 },
      totalPage: 0,
    }
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable', 'logisticsInfo']),
    currentTabType({ tabList, currentTabs }) {
      return tabList[currentTabs].type
    },
    isShowMarkBtn({ currentTabType, messagePushList }) {
      return [MsgType.User, MsgType.Logistics].includes(currentTabType) && messagePushList.length
    },
  },

  methods: {
    // Vuex mapMutations辅助函数
    ...mapMutations(['muLogisticsInfo']),

    async load(query = this.query, index = this.currentTabs) {
      const { page, limit } = query
      const { type } = this.tabList[index]
      if (type === MsgType.Auction) {
        const auctionMsgRef = this.$refs?.auctionMsgRef
        if (auctionMsgRef) {
          await auctionMsgRef.load()
          auctionMsgRef.loading = false
        } else {
          this.feedback.hideLoading()
        }
      } else {
        const res = await this.$u.api.messagePushList({ type: 1, notice_type: type, page, limit })
        const { total = 0, list = [] } = res?.data || {}
        // const now = Date.now()
        // list.forEach(item => {
        // 	if (item.created_at) {
        // 		const timestamp = item.created_at * 1000
        // 		const diff = now - timestamp
        // 		if (diff < 0) {
        // 			item.$created_at_text = ''
        // 			return
        // 		}
        // 		if (diff < 60 * 60 * 1000) {
        // 			item.$created_at_text = `${Math.ceil(diff / (60 * 1000))}分钟之前`
        // 			return
        // 		}
        // 		if (diff < 24 * 60 * 60 * 1000) {
        // 			item.$created_at_text = `${Math.ceil(diff / (60 * 60 * 1000))}小时之前`
        // 			return
        // 		}
        // 		if (diff < 5 * 24 * 60 * 60 * 1000) {
        // 			item.$created_at_text = `${Math.ceil(diff / (24 * 60 * 60 * 1000))}天前`
        // 			return
        // 		}
        // 		item.$created_at_text = this.$u.timeFormat(timestamp, 'yyyy年mm月dd日')
        // 	}
        // })
        page === 1 ? (this.messagePushList = list) : (this.messagePushList = [...this.messagePushList, ...list])
        this.totalPage = Math.ceil(total / limit)
        this.loadStatus = page === this.totalPage ? 'nomore' : 'loadmore'
      }
      this.query = query
      this.currentTabs = index
      this.$nextTick(() => {
        const classList = document?.body?.classList
        if (classList) {
          if (type === MsgType.Auction) {
            classList.remove('bg-f5f5f5')
            classList.add('bg-ffffff')
          } else {
            classList.remove('bg-ffffff')
            classList.add('bg-f5f5f5')
          }
        }
      })
    },

    async getMessageUnreadNum() {
      try {
        const res = await this.$u.api.messageUnreadNum()
        const data = res?.data || {}
        this.tabList.forEach((item) => {
          if (item.unreadNumKey) item.count = data[item.unreadNumKey] || 0
        })
      } catch (e) {}
    },

    // 标记已读消息
    markReadMessage() {
      let hasUnReadMessageIndex = this.messagePushList.findIndex((v) => v.is_read == 0)
      if (hasUnReadMessageIndex == -1) return this.feedback.toast({ title: '当前无未读消息~' })
      this.messagePushList.forEach((v) => {
        if (v.is_read == 0) v.is_read = 1
      })
      this.tabList[this.currentTabs].count = 0
      this.feedback.toast({ title: '已标记为已读~' })
    },

    changeTabs(index) {
      this.feedback.loading()
      this.load({ ...this.query, page: 1 }, index).then(() => {
        this.$nextTick(() => {
          this.system.pageScrollTo(0, 0)
        })
      })
    },
    itemClick(item) {
      console.log('item-----', item)
      if (this.comes.isFromApp(this.$vhFrom)) {
        if (item.path_data.code == 'MyOrder') {
          this.jump.appAndMiniJump(0, `${this.routeTable.pEMyOrder}?status=0`, this.$vhFrom)
        } else if (item.path_data.code == 'PostDetail') {
          this.jump.appAndMiniJump(
            1,
            `${this.routeTable.pCWineCommentDetail}?id=${item.path_data.client_path_param[0].android_val}&source=2`,
            this.$vhFrom
          )
        } else if (item.path_data.code == 'WineCommentDetail') {
          this.jump.appAndMiniJump(
            1,
            `${this.routeTable.pCWineCommentDetail}?id=${item.path_data.client_path_param[0].android_val}&source=6`,
            this.$vhFrom
          )
        } else if (item.path_data.code == 'Fans') {
          this.jump.appAndMiniJump(0, `/packageE/pages/my-fans-list/my-fans-list`, this.$vhFrom, 0, true)
        } else if (item.path_data.code == 'MyCoupon') {
          this.jump.appAndMiniJump(0, `${this.routeTable.pECouponList}`, this.$vhFrom, 0, true)
        } else if (item.path_data.code == 'MyPost') {
          const { uid } = uni.getStorageSync('loginInfo') || {}
          this.jump.appAndMiniJump(1, `/packageC/pages/my-post/my-post?uid=${uid}`, this.$vhFrom, 0, true)
        } else {
          const myItem = { client_path: item.path_data, ad_path_param: item.path_data.client_path_param }
          this.jump.pubConfJump(myItem, this.$vhFrom)
        }
      } else {
        this.feedback.toast({ title: '请前往APP查看此功能~' })
      }
    },
    // 跳转页面 item = 消息推送列表每一项
    jumpPage(item) {
      switch (item.data_type) {
        case 20: //物流通知
          let { cover, expressType, logisticCode } = item.logistics_data //物流信息
          this.muLogisticsInfo({ image: cover, expressType, logisticCode })
          this.jump.appAndMiniJump(0, `/packageB/pages/logistics-detail/logistics-detail`, this.$vhFrom)
          // this.jump.navigateTo(`/packageB/pages/logistics-detail/logistics-detail`)
          break
      }
    },
  },

  onShow() {
    this.login.isLoginV3(this.$vhFrom, 0).then((isLogin) => {
      if (!isLogin) return
      this.feedback.loading()
      Promise.all([this.load(), this.getMessageUnreadNum()]).then(() => {
        this.loading = false
      })
    })
  },

  onReachBottom() {
    if (this.query.page === this.totalPage || !this.totalPage) return
    this.feedback.loading()
    this.loadStatus = 'loading'
    this.load({ ...this.query, page: this.query.page + 1 })
  },
}
</script>

<style lang="scss" scoped>
.msg-center {
  &__tabs {
    background: #fff;

    ::v-deep {
      .u-tab {
        // &-item {
        // 	flex-grow: 0 !important;
        // 	flex-basis: auto !important;
        //   font-weight: 500 !important;
        // 	overflow: visible;

        // 	.u-badge {
        // 		@include flex-row;
        // 		@include size(32rpx);
        // 		transform: none !important;
        // 		top: 16rpx !important;
        // 		right: -20rpx !important;
        // 	}
        // }

        &-bar {
          top: 100%;
        }
      }
    }
  }
}
</style>
