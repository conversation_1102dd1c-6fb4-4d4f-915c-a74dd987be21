<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar back-icon-color="#FFF" :background="{background: '#E80404'}" title="体验券" title-color="#FFF" />
		
		<!-- 体验券 -->
		<view class="fade-in p-rela">
			<!-- 体验券背景图 -->
			<image class="w-p100" :src="experienceCouponinfo.background_map" mode="widthFix" />
			
			<!-- 领取按钮 -->
			<view class="p-abso top-1260 w-p100 d-flex j-center a-center">
				<!-- receiveNow -->
				<image class="w-492 h-110" :src="`${osip}/experience_coupon/get.png`" mode="widthFix" @click="$u.throttle(receiveNow, 1500)" />
			</view>
		</view>
		
		<!-- 弹框 -->
		<view class="">
			<!-- 领取成功/领取失败弹框 -->
			<u-mask :show="showRecMask" :zoom="false">
				<view class="h-p100 d-flex j-center a-center">
					<view class="w-636 bg-ffffff d-flex flex-column a-center b-rad-16 ptb-60-plr-00">
						<image class="w-220 h-230" :src="`${osip}/experience_coupon/${ recSucc ? 'succ' : 'fail' }.png`" mode="aspectFill" />
						<view class="mt-18 font-28 text-f24b4b">{{feedbackContent}}</view>
						<view class="mt-42 font-28 text-3" @click.stop="jump.redirectTo(`/packageE/pages/experience-coupon-list/experience-coupon-list`)">跳转到我的体验券页面</view>
					</view>
				</view>
			</u-mask>
		</view>
	</view>
</template>

<script>
	export default {
		name:'experience-coupon',
		
		data() {
			return {
				osip:'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				sid:'', //门店id
				experienceCouponId:'', //体验券id
				experienceCouponinfo:{}, //体验券信息
				timer: null,//请求定时器
				count: 0,//倒计时 0秒
				recSucc: false, //是否领取成功
				alreadyGet: false, //是否领取过
				feedbackContent:'', //反馈内容
				showRecMask: false, //是否显示领取遮罩层
			}
		},
		
		onLoad( options ) {
			this.experienceCouponId = parseInt(options.id)
			if( options.sid ) this.sid = parseInt(options.sid)
			this.getExperience()
		},
		
		methods: {
			// 获取体验券
			async getExperience() {
				try{
					console.log('---------------------我是初始化获取体验券状态')
					let res = await this.$u.api.experienceCouponInfo({ id: this.experienceCouponId })
					this.experienceCouponinfo = res.data
					let { status, collar_uid } = res.data
					if( status == 4 ) this.alreadyGet = true
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 添加门店用户 userInfo = 用户信息
			async addStoreUser( userInfo ) { 
				try{
					let { uid, telephone, nickname, avatar_image, openid } = userInfo
					let data = {}
					data.uid = uid
					data.loginname = telephone
					data.nickname = nickname
					data.avatar_image = avatar_image
					data.applet_openid = openid
					if( this.sid ) data.sid = this.sid
					let res = await this.$u.api.storeAddRegister(data)
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 立即领取
			receiveNow() {
				if( this.login.isLogin() ) {
					uni.getStorage({
						key: 'loginInfo',
						success: async userRes => {
							try{ 
								// 先添加门店用户、再调用门店优惠券领取、再做定时任务获取体验券状态
								this.feedback.loading({ title: '领取中...' })
								await Promise.all([ this.addStoreUser( userRes.data ), this.receiveExperience() ])
								//定时任务（定时时间为半分钟( 两秒为一次 )，一直请求获取券接口，直到返回的数据发生改变
								this.count = 15
								this.timer = setInterval(() => { 
									this.count --
									this.afterReceiveGetExperience() //获取券信息
									if( this.count === 0 ) {
										this.clearTimer()
										this.feedback.hideLoading()
										this.showRecMask = true
										this.feedbackContent = '领取超时，请重新领取体验券~'
									}
								}, 2000 )
							}catch(e){
								//TODO handle the exception
								// this.count = 15
								// this.timer = setInterval(()=>{ 
								// 	this.count --
								// 	this.getExperience() //获取券信息
								// 	if( this.count === 0 ) {
								// 		this.clearTimer()
								// 		this.showRecMask = true
								// 		this.feedbackContent = '领取超时，请重新领取体验券~'
								// 	}
								// }, 2000 )
							}
						},
						fail:() => {
							this.feedback.toast({ title: '获取用户信息失败！'})
						}
					})
				}
			},
			
			// 领券
			async receiveExperience() {
				try{
					await this.$u.api.experienceCouponTake({ id: this.experienceCouponId }) //领券
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 领取后获取体验券状态
			async afterReceiveGetExperience() {
				console.log('---------------------我是领取后获取体验券状态')
				let res = await this.$u.api.experienceCouponInfo({ id: this.experienceCouponId })
				this.experienceCouponinfo = res.data
				let { status, collar_uid } = res.data
				// 判断领取状态
				uni.getStorage({
					key:'loginInfo',
					success: res => {
						if( status !== 1 ) {
								// this.feedback.hideLoading()
								this.clearTimer()
								this.showRecMask = true
								if( status == 2 ) {
									this.feedbackContent = '领取失败，体验券已被使用~'
								}else if( status == 4 ){
									if( collar_uid === res.data.uid ){
										if(this.alreadyGet){
											this.feedbackContent = '领取失败，您已经领取过！'
										}else{
											this.feedbackContent = '恭喜您,领取成功~'
											this.recSucc = true
											this.alreadyGet = true
										}
									}else{
										this.feedbackContent = '领取失败，已被其他人领取！'
									}
								}
						}else{
							console.log('------------体验券未被领取')
						}
					},
					fail:() => {
						this.feedback.toast({ title: '获取用户信息失败！'})
					}
				})
			},
			
			// 清除定时器
			clearTimer() {
				console.log('销毁了定时器')
				if( this.timer ) {
					clearInterval(this.timer)
					this.timer = null
					this.count = 0
				}
			}
		},
		
		onUnload(){
		   this.clearTimer()
		}
	}
</script>

<style scoped>
</style>