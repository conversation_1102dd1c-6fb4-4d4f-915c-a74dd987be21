<template>
	<view class="iha-form">
		<u-navbar title="申请开票" :is-back="false"></u-navbar>
		<view v-if="!loading" class="pb-104">
			<view v-if="!sid || !orderno" class="flex-c-c h-400 font-wei-500 font-28 text-3 l-h-40">缺少门店ID或订单号</view>
			<view v-else-if="isSubmit" class="flex-c-c h-400 font-wei-500 font-28 text-3 l-h-40">该订单已申请开票</view>
			<view v-else class="bg-ffffff mtb-20-mlr-24 ptb-00-plr-24 b-rad-10">
				<InvoiceHeadFormItem label="发票类型">
					<view class="d-flex">
						<button
							v-for="item in MInvoiceTypeText"
							:key="item.value"
							class="vh-btn flex-c-c mr-20 w-144 h-44 b-rad-24 font-24"
							:class="[item.value === params.invoice_type ? 'bg-fce4e3 text-e80404' : 'bg-f6f6f6 text-3']"
							@click="params.invoice_type = item.value"
						>{{ item.text }}</button>
					</view>
				</InvoiceHeadFormItem>
				<InvoiceHeadFormItem label="抬头类型">
					<view class="d-flex">
						<button
							v-for="item in currMInvoiceFrontTypeText"
							:key="item.value"
							class="vh-btn flex-c-c mr-20 w-144 h-44 b-rad-24 font-24"
							:class="[item.value === params.type_id ? 'bg-fce4e3 text-e80404' : 'bg-f6f6f6 text-3']"
							@click="params.type_id = item.value"
						>{{ item.text }}</button>
					</view>
				</InvoiceHeadFormItem>
				<template v-if="isGeneralInvoiceType && isPersonInvoiceFrontType">
					<InvoiceHeadFormItem label="发票抬头" :model="params" prop="invoice_name" />
					<InvoiceHeadFormItem label="邮箱地址" :model="params" prop="email" />
				</template>
				<template v-if="isGeneralInvoiceType && isCompanyInvoiceFrontType">
					<InvoiceHeadFormItem label="发票抬头" :model="params" prop="invoice_name" />
					<InvoiceHeadFormItem label="单位税号" :model="params" prop="taxpayer" />
					<InvoiceHeadFormItem label="邮箱地址" :model="params" prop="email" />
					<view class="flex-s-c pt-32 bt-s-02-eeeeee" :class="isExpandMore ? 'pb-0' : 'pb-32'" @click="isExpandMore = !isExpandMore">
						<text class="font-28 text-9 l-h-40">更多发票信息(选填)</text>
						<image :src="ossIcon('/invoices/arrow_d_20_12.png')" class="ml-10 w-20 h-12" :class="isExpandMore ? 't-ro-n-180 tran-2' : 'tran-2'" />
					</view>
					<view v-if="isExpandMore">
						<InvoiceHeadFormItem label="单位地址" :model="params" prop="company_address" />
						<InvoiceHeadFormItem label="单位电话" :model="params" prop="company_tel" />
						<InvoiceHeadFormItem label="开户银行" :model="params" prop="opening_bank" />
						<InvoiceHeadFormItem label="银行账号" :model="params" prop="bank_account" />
					</view>
				</template>
				<template v-if="isSpecialInvoiceType && isCompanyInvoiceFrontType">
					<InvoiceHeadFormItem label="发票抬头" :model="params" prop="invoice_name" />
					<InvoiceHeadFormItem label="单位税号" :model="params" prop="taxpayer" />
					<InvoiceHeadFormItem label="邮箱地址" :model="params" prop="email" />
					<InvoiceHeadFormItem label="单位地址" :model="params" prop="company_address" />
					<InvoiceHeadFormItem label="单位电话" :model="params" prop="company_tel" />
					<InvoiceHeadFormItem label="开户银行" :model="params" prop="opening_bank" />
					<InvoiceHeadFormItem label="银行账号" :model="params" prop="bank_account" />
					<view class="flex-sb-c pt-32 bt-s-02-eeeeee" :class="isExpandMore ? 'pb-0' : 'pb-32'" @click="isExpandMore = !isExpandMore">
						<view class="flex-c-c">
							<text class="font-28 text-9 l-h-40">更多发票信息(选填)</text>
							<image :src="ossIcon('/invoices/arrow_d_20_12.png')" class="ml-10 w-20 h-12" :class="isExpandMore ? 't-ro-n-180 tran-2' : 'tran-2'" />
						</view>
						<!-- <button v-if="isExpandMore" @click.stop class="vh-btn flex-c-c w-124 h-36 font-24 text-6 bg-ffffff b-s-01-d8d8d8 b-rad-18" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=10`)">导入地址</button> -->
					</view>
					<view v-if="isExpandMore">
						<InvoiceHeadFormItem label="收票人" :model="params" prop="consignee" placeholder="收票人姓名" />
						<InvoiceHeadFormItem label="手机号码" :model="params" prop="telephone" placeholder="收票人手机号" />
						<InvoiceHeadFormItem label="所在地区" :model="params" prop="address" placeholder="省份、市区、地区">
							<view class="d-flex j-sb" @click="regionVisible = true">
								<view class="font-28 l-h-40">
									<text v-if="params.province_name && params.city_name && params.town_name" class="text-3">{{ params.province_name }} {{ params.city_name }} {{ params.town_name }}</text>
									<text v-else class="text-9">省份、市区、地区</text>
								</view>
								<!-- <view v-if="params.province_name && params.city_name && params.town_name" class="flex-e-c w-40 h-40" @click="onClearRegion">
									<image class="mr-04 w-22 h-22" :src="ossIcon('/comm/del_gray.png')"></image>
								</view> -->
							</view>
						</InvoiceHeadFormItem>
						<InvoiceHeadFormItem label="详细地址" :model="params" prop="address" placeholder="街道、楼牌号等" />
					</view>
				</template>
			</view>
		</view>
		<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-feffff b-sh-00021200-022 d-flex j-center a-center">
			<u-button :disabled="disabled" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
			:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: disabled ? '#FCE4E3' : '#E80404', border:'none'}" @click="submit">{{ btnText }}</u-button>
		</view>

		<vh-region v-if="Object.keys(regionInfo).length" v-model="regionVisible" @confirm="changeRegion"></vh-region>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	import { MInvoiceType, MInvoiceFrontType } from '@/common/js/utils/mapperModel'
	import { MInvoiceTypeText, MInvoiceFrontTypeText } from '@/common/js/utils/mapper'
	import { emailPattern } from '@/common/js/utils/pattern'

	const TypeToKeys = {
		[`${MInvoiceType.General}-${MInvoiceFrontType.Person}`]: {
			requireKeys: ['invoice_name', 'email'],
			optionalKeys: []
		},
		[`${MInvoiceType.General}-${MInvoiceFrontType.Company}`]: {
			requireKeys: ['invoice_name', 'taxpayer', 'email'],
			optionalKeys: ['company_address', 'company_tel', 'opening_bank', 'bank_account']
		},
		[`${MInvoiceType.Special}-${MInvoiceFrontType.Company}`]: {
			requireKeys: ['invoice_name', 'taxpayer', 'email', 'company_address', 'company_tel', 'opening_bank', 'bank_account'],
			optionalKeys: ['consignee', 'telephone', 'province_id', 'city_id', 'town_id', 'province_name', 'city_name', 'town_name', 'address']
		}
	}
	export default{
		data: () => ({
			MInvoiceType,
			MInvoiceFrontType,
			MInvoiceTypeText,
			MInvoiceFrontTypeText,
			loading: true,
			sid: '',
			orderno: '',
			isSubmit: false,
			params: {
				invoice_type: MInvoiceType.General,
				type_id: MInvoiceFrontType.Person,
				invoice_name: '',
				taxpayer: '',
				email: '',
				company_address: '',
				company_tel: '',
				opening_bank: '',
				bank_account: '',
				consignee: '',
				telephone: '',
				province_id: '',
				city_id: '',
				town_id: '',
				province_name: '',
				city_name: '',
				town_name: '',
				address: '',
			},
			isExpandMore: false,
			regionVisible: false,
		}),
		computed: {
			...mapState(['routeTable', 'invoiceInfo', 'addressInfoState', 'regionInfo']),
			isEdit ({ params }) {
				return !!params.id
			},
			title ({ isEdit }) {
				return isEdit ? '修改发票抬头' : '添加发票抬头'
			},
			btnText ({ isEdit }) {
				return isEdit ? '保存' : '提交'
			},
			currType ({ params }) {
				const { invoice_type, type_id } = params
				return `${invoice_type}-${type_id}`
			},
			currKeyObj ({ currType }) {
				return TypeToKeys[currType] || { requireKeys: [], optionalKeys: [] }
			},
			currMInvoiceFrontTypeText ({ params }) {
				if (params.invoice_type === MInvoiceType.Special) {
					return MInvoiceFrontTypeText.filter(({ value }) => value === MInvoiceFrontType.Company)
				}
				return MInvoiceFrontTypeText
			},
			isGeneralInvoiceType ({ params }) {
				return params.invoice_type === MInvoiceType.General
			},
			isSpecialInvoiceType ({ params }) {
				return params.invoice_type === MInvoiceType.Special
			},
			isPersonInvoiceFrontType ({ params }) {
				return params.type_id === MInvoiceFrontType.Person
			},
			isCompanyInvoiceFrontType ({ params }) {
				return params.type_id === MInvoiceFrontType.Company
			},
			disabled ({ params, currKeyObj }) {
				const { requireKeys } = currKeyObj
				const flag = requireKeys.every(key => !!params[key])
				return !flag
			}
		},
		watch: {
			'params.invoice_type' () {
				if (this.isSpecialInvoiceType) {
					this.params.type_id = MInvoiceFrontType.Company
				}
			}
		},
		onLoad (options) {
			const { sid = '', orderno = '' } = options
			this.sid = sid
			this.orderno = orderno
			this.loadStoreApplyInvoiceStatus()
			this.initRegionData()
		},
		methods: {
			...mapMutations(['muInvoiceInfo', 'muAddressInfoState', 'muRegionInfo']),
			loadStoreApplyInvoiceStatus () {
				if (!this.sid || !this.orderno) {
					this.loading = false
					return
				}
				this.$u.api.getStoreApplyInvoiceStatus({ orderno: this.orderno }).then(res => {
					const { is_submit = false } = res?.data
					this.isSubmit = is_submit
				}).finally(() => {
					this.loading = false
				})
			},
			onClearRegion () {
				this.params = Object.assign({}, this.params, {
					province_id: '',
					city_id: '',
					town_id: '',
					province_name: '',
					city_name: '',
					town_name: '',
				})
			},
			submit () {
				const { id, invoice_type, type_id, email } = this.params
				if (!emailPattern.test(email)) {
					this.feedback.toast({ title:'请输入正确的邮箱地址', icon: 'error' })
					return
				}
				const { requireKeys, optionalKeys } = this.currKeyObj
				const keys = requireKeys.concat(optionalKeys)
				const params = { sid: this.sid, orderno: this.orderno, invoice_type, type_id }
				keys.forEach(key => {
					if (['province_id', 'city_id', 'town_id'].includes(key)) { // 值为0时要置为''，不然接口会校验不通过
						params[key] = this.params[key] || ''
					} else {
						params[key] = this.params[key]
					}
				})
				console.log('params', params)
				if (id) {
					params.id = id
					this.$u.api.updateInvoice(params).then(() => {
						this.feedback.toast({ title: '修改成功', icon: 'success' })
					})
				} else {
					this.$u.api.storeApplyInvoice(params).then(() => {
						this.feedback.toast({ title: '申请成功', icon: 'success' })
						this.params = this.$options.data().params
					})
				}
			},
			del () {
				this.feedback.showModal({
					content:'确认删除吗？',
					confirm: () => {
						this.$u.api.deleteInvoice({ id: [this.params.id] }).then(() => {
							this.feedback.toast({ title: '删除成功', icon: 'success' })
							this.back()
						})
					}
				})
			},
			back () {
				const timer = setTimeout(() => {
					this.jump.navigateBack()
					timer && clearTimeout(timer)
				}, 1500)
			},
			initRegionData () {
				if (Object.keys(this.regionInfo).length) return
				this.$u.api.regionList({ isJson:true }).then(res => {
					const { list = [] } = res?.data || {}
					const provinces = []
					const citys = []
					const areas = []
					list.forEach((provinceItem, provinceIndex) => {
						provinces.push({ label: provinceItem.name, value: provinceItem.id })
						citys.push([])
						areas.push([])
						provinceItem.children.forEach((cityItem, cityIndex) => {
							citys[provinceIndex].push({ label: cityItem.name, value: cityItem.id })
							areas[provinceIndex].push([])
							cityItem.children.forEach((areaItem) => {
								areas[provinceIndex][cityIndex].push({ label: areaItem.name, value: areaItem.id })
							})
						})
					})
					this.muRegionInfo({ provinces, citys, areas })
				})
			},
			changeRegion (e) {
				const {
					province: { value: province_id, label: province_name  },
					city: { value: city_id, label: city_name  },
					area: { value: town_id, label: town_name  }
				} = e
				this.params = Object.assign({}, this.params, { province_id, city_id, town_id, province_name, city_name, town_name })
			},
		},
	}
</script>

<style>
	@import '@/common/css/page.css';
</style>

<style lang="scss" scoped>
	.iha-form {
		.ih-form-item {
			&:not(:first-of-type) {
				border-top: 2rpx solid #eee;
			}
		}
	}
</style>
