<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar title="认证详情" />
		
		<!-- 填写资料 -->
		<view v-if="!submitSucc" class="">
			<!-- 认证进度 -->
			<view class="ptb-60-plr-00">
				<view class="d-flex j-center a-center">
					<image class="w-44 h-44" src="https://images.vinehoo.com/vinehoomini/v3/certification_detail/pro_red.png" mode="aspectFill" />
					<view class="w-250 h-04 bg-ffc8c8" />
					<image class="w-44 h-44" :src="`https://images.vinehoo.com/vinehoomini/v3/certification_detail/${ status != null && status >= 0 ? 'pro_red' : 'pro_gray' }.png`" mode="aspectFill" />
					<view class="w-250 h-04" :class="status >= 1 ? 'bg-ffc8c8' : 'bg-e1e1e1'" />
					<!-- this.title = index == 0 ? '新闻' : index == 1 ? '新闻列表' : '雨打梨花深闭门，忘了青春，误了青春'; -->
					<image class="w-44 h-44" :src="`https://images.vinehoo.com/vinehoomini/v3/certification_detail/${ status == 1 ? 'pro_red' : status == 2 ? 'pro_fail' : 'pro_gray' }.png`" mode="aspectFill" />
				</view>
				
				<view class="d-flex j-sb a-center mt-32 ptb-00-plr-32">
					<text class="font-28 font-wei text-3">填写资料</text>
					<text class="font-28" :class="status >= 0 ? 'font-wei text-3' : 'text-9'">等待审核</text>
					<text class="font-28" :class="status >= 1 ? 'font-wei text-3' : 'text-9'">{{status == 2 ? '认证失败' : '完成认证'}}</text>
				</view>
			</view>
			
			<!-- 间隔槽 -->
			<vh-gap height="20" bg-color="#f7f7f7" />
			
			<!-- 认证审核中 -->
			<view v-if="status !== null && recertification" class="">
				<view class="ptb-00-plr-32">
					<!-- 真实姓名 -->
					<view class="d-flex bb-s-01-eeeeee pt-60 pb-32">
						<view class="font-32 font-wei text-3">真实姓名</view>
						<view class="w-480 ml-46 font-32 text-3 text-hidden-1">{{realName}}</view>
					</view>
					
					<!-- 身份证号 -->
					<view class="d-flex bb-s-01-eeeeee pt-32 pb-16">
						<view class="font-32 font-wei text-3">身份证号</view>
						<view class="w-480 ml-46 font-32 text-3 text-hidden-1">{{IdNumber}}</view>
					</view>
					
					<!-- 认证信息 -->
					<view class="d-flex bb-s-01-eeeeee pt-48 pb-32">
						<view class="font-32 font-wei text-3">认证信息</view>
						<view class="w-480 ml-46 font-32 text-3 text-hidden-1">{{cerName}}</view>
					</view>
					
					<!-- 上传的资质 -->
					<view class="pt-32 pb-32">
						<view class="font-32 font-wei text-3">上传照片</view>
						
						<view class="mt-32 d-flex j-sb a-center">
							<view class="bg-f7f7f7 w-328 h-228 d-flex j-center a-center b-rad-10">
								<image class="w-308 h-196 b-rad-10" :src="ossPrefix + idCardImg" mode="aspectFill" />
							</view>
							
							<view class="bg-f7f7f7 w-328 h-228 d-flex j-center a-center b-rad-10">
								<image class="w-308 h-196 b-rad-10" :src="ossPrefix + cerQuaImg" mode="aspectFill" />
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 提交认证、重新认证 -->
			<view v-else class="">
				<!-- 认证信息 -->
				<view class="ptb-00-plr-32">
					<!-- 真实姓名 -->
					<view class="d-flex bb-s-01-eeeeee pt-60 pb-32">
						<view class="font-32 font-wei text-3">真实姓名</view>
						<input class="w-480 ml-46 font-32 text-3" v-model="realName" type="text" placeholder="请输入您的真实姓名" placeholder-style="color:#999;font-size:32rpx;"/>
					</view>
					
					<!-- 身份证号 -->
					<view class="d-flex bb-s-01-eeeeee pt-32 pb-16">
						<view class="font-32 font-wei text-3">身份证号</view>
						<input class="w-480 ml-46 font-32 text-3" v-model="IdNumber" type="idcard" :maxlength="18" placeholder="请输入您的身份证号码" placeholder-style="color:#999;font-size:32rpx;"/>
					</view>
					
					<!-- 认证信息 -->
					<view class="d-flex bb-s-01-eeeeee pt-48 pb-32">
						<view class="font-32 font-wei text-3">认证信息</view>
						<view class="flex-1 d-flex j-sb ml-46" @click="showSelectPop = true">
							<view class="w-480 font-32" :class="cerName ? 'text-3' : 'text-9'">{{cerName ? cerName : '请选择认证信息请选择'}}</view>
							<u-icon name="arrow-right" :size="20" color="#333" />
						</view>
					</view>
				</view>
				
				<!-- 上传照片 -->
				<view class="ptb-32-plr-00">
					<view class="font-32 pl-32 font-wei text-3">上传照片</view>
					<view class="mt-32">
						<vh-upload
							ref="uUpload" 
							:plate="1"
							:width="328"
							:height="228"
							:multiple="false"
							:directory="'vinehoo/client/certification/'" 
							:file-list="uploadFileList"
							:auto-upload="false" 
							@on-list-change="onListChange"
							@on-uploaded="onUploaded" />
					</view>
				</view>
			</view>
			
			<!-- 提交申请 -->
			<view class="ptb-00-plr-32">
				<!-- 温馨提示 -->
				<view class="mt-176">
					<view v-if="status === 2 && rejectionReason" class="">
						<view class="font-24 text-3 l-h-40">温馨提示：</view>
						<view class="font-24 text-3 l-h-40">驳回原因：{{rejectionReason}}</view>
					</view>
					<view v-if="!recertification" class="">
						<view class="font-24 text-9 l-h-40">温馨提示：</view>
						<view class="font-24 text-9 l-h-40">为了确保您的认证信息快速认证，请您认真的填写登记信息，并将您的身份证和专业证书以张片的形式提交，我们将在3个工作日内进行审核，认证成功后，您将获得对应的认证徽章。</view>
					</view>
				</view>
				
				<!-- 提交按钮 -->
				<view class="d-flex j-center a-center mt-30 pb-62">
					<view v-if="status == 0 && recertification">
						<u-button shape="circle" :ripple="true" ripple-bg-color="#ffffff"
						:custom-style="{ width:'646rpx',color:'#fff',backgroundColor: '#FCE4E3', border:'none'}"
						@click="feedback.toast({title: '您提交的信息正在审核中...'})">审核中...</u-button>
					</view>
					<view v-else-if="status > 0 && recertification">
						<u-button shape="circle" :ripple="true" ripple-bg-color="#ffffff"
						:custom-style="{ width:'646rpx',color:'#fff',backgroundColor: '#E80404', border:'none'}"
						@click="handleRecertification">重新认证</u-button>
					</view>
					<view v-else>
						<u-button :disabled="!canSubmit" shape="circle" :ripple="true" ripple-bg-color="#ffffff"
						:custom-style="{ width:'646rpx',color:'#fff',backgroundColor: !canSubmit ? '#FCE4E3' : '#E80404', border:'none'}"
						@click="submit">提交申请</u-button>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 等待审核 -->
		<view v-if="submitSucc" class="d-flex flex-column a-center">
			<image class="mt-196 w-266 h-184" src="https://images.vinehoo.com/vinehoomini/v3/certification_detail/cer_succ.png" mode="aspectFill" />
			<view class="mt-106 font-40 font-wei text-3">提交成功</view>
			<view class="mt-12 font-28 text-9">我们将在七个工作日内将结果短信通知您</view>
			<view class="p-fixed bottom-60 w-p100 d-flex j-center">
				<u-button shape="circle" :ripple="true" ripple-bg-color="#ffffff"
				:custom-style="{ width:'646rpx', color:'#fff', backgroundColor:'#E80404', border:'none' }" @click="back">返回</u-button>
			</view>
		</view>
	    
		<!-- 弹框 -->
		<view class="">
			<!-- 筛选弹框 -->
			<u-popup v-model="showSelectPop" mode="bottom" :height="811" :border-radius="20">
				<view class="p-rela">
					<!-- 标题 -->
					<view class="p-stic top-0 bg-ffffff d-flex j-center a-center bb-s-01-f7f7f7 ptb-40-plr-00 font-36 font-wei text-3">认证选择</view>
					
					<!-- 拥有筛选数据 -->
					<view class="fade-in">
						<!-- 认证列表 -->
						<view class="h-592 d-flex">
							<!-- 一级类别 -->
							<view class="w-284 h-p100 bg-f7f7f7 o-scr-y">
								<view class="p-rela ptb-32-plr-24 text-6" :class="index == firCerIndex ? 'active bg-ffffff font-wei text-3' : ''" 
								v-for="(item, index) in certList" :key="index" @click="selFirCerList(index)">{{item.name}}</view>
							</view>
							
							<!-- 二级类别 -->
							<view class="w-466 h-p100 bg-ffffff o-scr-y">
								<view class="d-flex flex-column pb-48 pl-24 pr-24">
									<view class="mt-40 font-28 text-3" :class="item.info == cerName ? 'bg-fce0e0 b-rad-36 ptb-16-plr-40 text-e80404' : ''" 
									 v-for="(item, index) in certList[firCerIndex].list" :key="index" @click="selSecCerList(item.info)">{{item.info}}</view>
								</view>
							</view>
						</view>
						
						<!-- 底部按钮 -->
						<view class="p-fixed bottom-0 z-999 bg-ffffff w-p100 h-98 d-flex j-sa a-center b-sh-00021200-022 pl-32 pr-32">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'308rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#999', backgroundColor: '#EEEEEE', border:'none'}"
							@click="cancelSelect">取消</u-button>
							
							<u-button :disabled="!cerName" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
							:custom-style="{width:'308rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: !cerName ? '#FCE4E3' : '#E80404', border:'none'}"
							@click="showSelectPop = false">确定</u-button>
						</view>
					</view>
				
				    <!-- 没有筛选数据 -->
					<!-- <view v-else class="fade-in h-550 d-flex j-center a-center">
						<vh-loading mode="circle" />
					</view> -->
				</view>
			</u-popup>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		name: 'certification-detail',
		
		data() {
			return {
				status: null, //认证状态：0未审核，1已通过，2驳回
				rejectionReason:'', //驳回原因
				recertification: true, //是否需要重新认证
				certificationInfo: {}, //认证信息
				realName:'', //真实姓名
				IdNumber:'', //身份证号
				uploadFileList: [], //上传完成的文件列表
				idCardImg:'', //身份证图片
				cerQuaImg:'', //认证资质图片
				cerName:'', //认证名称
				submitSucc: false, //提交成功
				showSelectPop: false, //是否显示选择弹框
				certList:[], //认证列表
				firCerIndex: 0, //一级认证列表索引
			}
		},
		
		onLoad() {
			this.getCertificationInfo()
			this.getCertificationList()
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['ossPrefix']),
			
			// 是否可以提交
			canSubmit() {
				if(this.$u.trim(this.realName, 'all') && this.$u.trim(this.IdNumber, 'all') && this.$u.test.idCard(this.IdNumber) && this.cerName && this.uploadFileList.length == 2) {
					return true
				}
				return false
			}
		},
		
		methods: {
			// 获取认证信息
			async getCertificationInfo() {
				let res = await this.$u.api.certificationInfo()
				if(Object.keys(res.data).length) {
					const {status, real_name, id_card, info, remarks, idcard_img, quali_img } = res.data
					this.certificationInfo = res.data
					this.status = status //认证状态
					this.rejectionReason = remarks //驳回原因
					this.realName = real_name //真实姓名
					this.IdNumber = id_card //身份证号
					this.cerName = info //认证信息
					this.idCardImg = idcard_img //身份证照片
					this.cerQuaImg = quali_img //资质照片
					// if( status !== 0 )  this.initUploadFileList()  //初始化上传图片列表
				}else{
					this.recertification = false
				}
			},
			
			// 初始化文件信息
			initUploadFileList() {
				this.uploadFileList = [
					{
						fileType:'image', // 文件类型
						cerType: 'idCardImg', // 认证类型，身份证
						videoCoverImg: '', //视频封面
						url: this.idCardImg, //身份证图片地址
						response: this.idCardImg, //上传完成的地址
						progress: 100, //上传进度
						error: false, //是否报错
						file: {} ,//文件信息
					},
					{
						fileType:'image', // 文件类型
						cerType: 'cerQuaImg', // 认证类型，资质信息
						videoCoverImg: '', //视频封面
						url: this.cerQuaImg, //资质图片地址
						response: this.cerQuaImg, //上传完成的地址
						progress: 100, //上传进度
						error: false, //是否报错
						file: {} ,//文件信息
					}
				]
			},
			
			// 获取认证等级列表
			async getCertificationList() {
				let res = await this.$u.api.certificationList()
				this.certList = res.data.list
			},
			
			// 选中一级等级列表 index = 一级列表认证索引
			selFirCerList(index){
				this.firCerIndex = index
			},
			
			// 选中二级认证等级列表 name = 二级列表认证名称
			selSecCerList(name) {
				this.cerName = name
			},
			
			// 取消选择
			cancelSelect() {
				this.cerName = ''
				this.firCerIndex = 0
				this.showSelectPop = false
			},
			
			// 上传列表发生改变
			onListChange(list) {
				console.log('-----------------------上传列表发生改变')
				this.uploadFileList = list
				this.handleUploadFiles(list)
			},
			
			// 所有上传成功
			onUploaded(list, index) {
				console.log('-------上传所有文件成功')
				console.log(list)
				console.log(index)
				this.handleUploadFiles(list)
				this.applyCertification()
			},
			
			// 处理上传的文件数据
			handleUploadFiles(list) {
				let idCardImgList = list.filter(item => { return item.cerType == 'idCardImg' }).map(item=>{ return item.response })
				let cerQuaImgList = list.filter(item => { return item.cerType == 'cerQuaImg' }).map(item=>{ return item.response })
				this.idCardImg = idCardImgList.join(',')
				this.cerQuaImg = cerQuaImgList.join(',')
			},
			
			// 申请认证
			async applyCertification() {
				let data = {}
				data.real_name = this.realName //真实姓名
				data.id_card = this.IdNumber //身份证号码
				data.info = this.cerName //认证信息
				data.idcard_img = this.idCardImg //身份证图片
				data.quali_img = this.cerQuaImg //资质图片
				console.log('这是要上传的数据')
				console.log(data)
				try {
					let res = await this.$u.api.submitCertification(data)
					this.submitSucc = true
					this.feedback.toast({ title:'提交成功！' })
				}catch(e) {
					console.log(e)
				}
			},
			
			// 处理重新认证
			handleRecertification() {
				this.recertification = false
				this.initUploadFileList()
				this.status = 0
			},
			
			// 提交
			submit() {
				this.feedback.loading({title:'提交中...'})
				this.$refs.uUpload.upload();
			},
		    
			// 返回
			back() {
				this.recertification = true
				this.submitSucc = false
				this.getCertificationInfo()
			}
		}
	}
</script>

<style scoped>
	.active::after{
		content: '';
		position: absolute;
		top: 32rpx;
		left: 0;
		height: 36rpx;
		width: 8rpx;
		background: linear-gradient(180deg, #E70000 0%, #FF8383 100%);
		border-radius: 4rpx;
	}
</style>
