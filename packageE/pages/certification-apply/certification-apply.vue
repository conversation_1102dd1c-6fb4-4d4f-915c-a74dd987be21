<template>
	<view class="content bg-ffffff">
		<!-- 导航栏 -->
		<view class="p-fixed z-980 h-px-40 w-p100 d-flex a-center" :style="{top: statusBarHeight + 'px'}">
			<view class="h-p100 d-flex a-center ml-24" @click="jump.jumpPrePage($vhFrom)">
				<u-icon name="nav-back" color="#FFF" :size="44" />
			</view>
		</view>
		
		<!-- banner -->
		<image class="w-p100" src="https://images.vinehoo.com/vinehoomini/v3/apply_certification/ban.png" mode="widthFix" />
		
		<!-- 申请须知1 -->
		<view class="bg-ffffff pl-32 pr-32 pb-48">
			<view class="d-flex a-center pb-24">
				<view class="w-04 h-28 bg-e80404 mr-12"></view>
				<view class="font-32 text-3 font-wei l-h-44">申请须知</view>
			</view>
			<view class="bt-s-01-eeeeee ml-16">
				<view class="mt-20 font-28 text-3 l-h-40" v-for="(item, index) in noticeList" :key="index">{{item}}</view>
			</view>
		</view>
        
		<!-- 间隔槽 -->
		<vh-gap height="12" bg-color="#f7f7f7" />
		
		<!-- 申请须知2 -->
		<view class="bg-ffffff pl-32 pr-32 mt-12">
			<view class="d-flex a-center pt-48 pb-24">
				<view class="w-04 h-28 bg-e80404 mr-12"></view>
				<view class="d-flex a-center">
					<text class="font-32 font-wei text-3 l-h-44">申请须知</text>
					<text class="ml-20 font-24 text-9 l-h-40">(如果您符合以下任意一条，即可申请认证)</text>
				</view>
			</view>
			<view class="bt-s-01-eeeeee ml-16">
				<view class="mt-20 font-28 text-3 l-h-40" v-for="(item, index) in certificateList" :key="index">
					<text class="font-wei text-e80404">{{item.name}}</text>
					<text class="ml-10">{{item.intro}}</text>
				</view>
			</view>
		</view>
		
		<!-- 邮箱 -->
		<!-- <view class="bg-ffffff pl-32 pr-32 mt-40 ml-16">
			<text class="font-24 text-9">特殊情况可发送邮件至<text class="text-3">admin@vine</text>进行处理</text>
		</view> -->
		
		<!-- 酒云网个人认证用户管理条例 -->
		<view class="bg-ffffff p-rela d-flex j-center a-center pt-80 pb-28">
			<vh-check :checked="isAgreeReg" @click="isAgreeReg = !isAgreeReg" />
			<text class="ml-10 font-24" :class="isAgreeReg ? 'text-3' : 'text-9'">我已阅读并同意</text>
			<text class="font-24 text-3" @click="jump.jumpH5Agreement(agreementPrefix + '/personApprove')">《酒云网个人认证用户管理条例》</text>
		</view>
		
		<!-- 申请按钮 -->
		<view class="bg-ffffff d-flex j-center pb-62">
			<u-button :disabled="!isAgreeReg" shape="circle" :ripple="true" ripple-bg-color="#ffffff" 
			:custom-style="{ width:'646rpx',color:'#fff',backgroundColor: !isAgreeReg ? '#FCE4E3' : '#E80404', border:'none'}" @click="jump.appAndMiniJump(1, routeTable.pECertificationDetail, $vhFrom)">我要申请</u-button>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	export default{
		name:"certification-apply",
		
		data(){
			return{
				noticeList:[ //须知
					'酒云网对拥有专业证书的用户进行真实身份的确认',
					'认证需要您提供资格证书和相关的材料',
					'认证服务是免费的，认证成功后获得专属的认证徽章'
				],
				certificateList:[ //证书列表
					{
						name:'WSET',
						intro:'一级以上（含一级）证书获得者'
					},
					{
						name:'ISA',
						intro:'侍酒师初级以上（含初级）证书获得者'
					},
					{
						name:'CMS',
						intro:'国际品师初级以上（含初级）证书获得者'
					}
				],
				isAgreeReg: false,//是否勾选协议
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['agreementPrefix', 'routeTable']),
			
			// 获取状态栏高度
			statusBarHeight() {
				return this.system.getSysInfo().statusBarHeight
			}
		},
	}
</script>

<style scoped></style>
