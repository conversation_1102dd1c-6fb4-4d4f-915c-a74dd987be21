<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar title="发票抬头管理">
			<button slot="right" class="vh-btn flex-c-c w-108 h-60 font-30 text-3 bg-ffffff" @click="isShowEdit = !isShowEdit">{{ isShowEdit ? '完成' : '编辑' }}</button>
		</vh-navbar>
		
		<!-- 发票列表 -->
		<view class="">
			<view v-if="invoiceList.length" class="h-vh-100 bg-f5f5f5 o-scr-y">
				<!-- 发票抬头维护限制 -->
				<view class="bg-fcf8d9 d-flex j-sb a-center ptb-16-plr-24" @click="jump.jumpH5Agreement(agreementPrefix + '/commonProblem', $vhFrom)">
					<view class="d-flex a-center">
						<image class="w-26 h-26" src="https://images.vinehoo.com/vinehoomini/v3/invoices/ques_yell.png" mode="aspectFill"></image>
						<text class="ml-08 font-24 text-de6a1b l-h-44">抬头管理维护受到规则限制</text>
					</view>
					
					<view class="d-flex a-center">
						<text class="mr-08 font-24 text-de6a1b l-h-44">查看详情</text>
						<u-icon name="arrow-right" :size="20" color="#DE6A1B"></u-icon>
					</view>
				</view>
				
				<!-- 发票抬头列表 -->
				<view class="ptb-00-plr-24 pt-20 pb-104 o-hid">
					<view :class="isShowEdit ? 't-trans-x-72' : ''">
					<view class="p-rela d-flex j-sb a-center" v-for="(item, index) in invoiceList" :key="index">
						<view v-if="isShowEdit" class="p-abso t-trans-x-m100 flex-c-c w-96">
							<vh-check :checked="invoiceSelectedList.indexOf(item.id) > -1" @click="selectSingle(item)"></vh-check>
						</view>
						
						<view class="flex-1 bg-ffffff mb-20 p-24 b-rad-10">
							<view class="flex-sb-c pb-18 bb-s-02-eeeeee">
								<view class="font-wei-600 font-28 text-3 l-h-40">{{ item.invoice_type | toText('MInvoiceTypeText') }}抬头-{{ item.type_id | toText('MInvoiceFrontTypeText') }}</view>
								<button v-if="comeFrom" class="vh-btn flex-c-c w-112 h-44 font-wei-500 font-26 text-ffffff bg-e80404 b-rad-26" @click="onOrderInvoice(item)">选择</button>
							</view>
							<view class="mt-20 d-flex j-sb a-center">
								<view class="">
									<view class="font-24 text-3 l-h-34">{{item.invoice_name}}</view>
									<view v-if="item.type_id == MInvoiceFrontType.Company" class="mt-04 font-24 text-9 l-h-34">{{item.taxpayer}}</view>
								</view>
								<image class="w-36 h-36" src="https://images.vinehoo.com/vinehoomini/v3/comm/edit_bla.png" mode="aspectFill" @click.stop="editInvoice(item)"></image>
							</view>
						</view>
					</view>
				</view>
				</view>
			</view>
			
			<!-- 列表（空） -->
			<view v-else class="bg-ffffff">
				<vh-empty :padding-top="270" :padding-bottom="100" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png" :text-bottom="0" text="您还没有添加发票抬头"></vh-empty>
			</view>
		</view>
	
		<!-- 底部按钮框 -->
		<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022">
			<view v-if="isShowEdit" class="fade-in-up-medium w-p100 h-p100 d-flex j-sb a-center pl-32 pr-24">
				<view class="d-flex a-center">
					<vh-check :checked="isSelectAllInvoice()" @click="selectAll()"></vh-check>
					<text class="ml-16 font-32 text-3">全选</text>
				</view>
				
				<view class="">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="deleteInvoice">删除</u-button>
				</view>
			</view>
			
			<view v-else class="fade-in-down w-p100 h-p100 d-flex j-center a-center">
				<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
				:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="jump.appAndMiniJump(1, routeTable.pEInvoiceHeadAdd, $vhFrom)">添加发票抬头</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	import { MInvoiceFrontType } from '@/common/js/utils/mapperModel'

	export default{
		name: 'invoice-mangement',
		
		data(){
			return{
				MInvoiceFrontType,
				comeFrom:'', //来自哪个页面 1 = 订单开票页面、2 = 门店订单开票页
				isShowEdit: false,// 是否可编辑
				invoiceList: [], //发票列表
				invoiceSelectedList: [], //选中的发票列表
				orderNo: '',
				orderType: '',
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable', 'invoiceInfo', 'agreementPrefix']),
		},
		
		onLoad(options) {
			if( options.comeFrom ) this.comeFrom = options.comeFrom
			this.orderNo = options.orderNo
			this.orderType = options.orderType
		},
		
		onShow() {
			this.getInvoiceList()
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muInvoiceInfo']),
			
			// 获取发票列表
			async getInvoiceList() {
				let res = await this.$u.api.invoiceList()
				console.log(res)
				this.invoiceList = res.data.list
			},
			
			// 编辑发票列表 item = 发票列表某一项
			editInvoice(item){
				this.muInvoiceInfo(item)
				uni.setStorageSync('InvoiceInfo',item);
				this.jump.appAndMiniJump(1, this.routeTable.pEInvoiceHeadAdd, this.$vhFrom)
				// this.jump.navigateTo(this.routeTable.pEInvoiceHeadAdd)
			},
			
			// 判断是否全选所有地址
			isSelectAllInvoice(){
				return this.invoiceList.length === this.invoiceSelectedList.length && !!this.invoiceSelectedList.length
			},
			
			// 全选/取消全选
			selectAll(){
				if(this.isSelectAllInvoice()){
					this.invoiceList.forEach( v => {
						v.checked = false
					})
					this.invoiceSelectedList = []
				}else{
					this.invoiceSelectedList = this.invoiceList.map( v => {
						v.checked = true
						return v.id
					})
				}
			},
			
			// 选中单个/取消选中单个地址 item = 地址列表某一项
			selectSingle(item){
				// 来自
				if(this.invoiceSelectedList.indexOf(item.id) > -1){
					this.invoiceList.forEach( v => {
						if(v.id === item.id){
							v.checked = false
						}
					})
					this.invoiceSelectedList.splice(this.invoiceSelectedList.indexOf(item.id), 1)
				}else{
					this.invoiceList.forEach( v => {
						if(v.id === item.id){
							v.checked = true
						}
					})
					this.invoiceSelectedList.push(item.id)
				}
			},
		    
			// 订单开票 item = 发票抬头每一项
			onOrderInvoice(item) {
				if (+this.comeFrom === 3) {
					this.feedback.showModal({
						content: `我们将在订单确认完成后的24小时内开具发票，确认使用“${item.invoice_name}”抬头进行开票吗？`,
						confirm: () => {
							const params = {
								sub_order_no: this.orderNo,
								order_type: this.orderType,
								invoice_progress: 1,
								invoice_id: item.id
							}
							this.$u.api.updateOrderInvoiceInfo(params).then(res => {
								this.feedback.toast({ title: res.data.msg })
								const timer = setTimeout(() => {
									this.jump.jumpPrePage(this.$vhFrom)
									timer && clearTimeout(timer)
								}, 500)
							})
						}
					})
					return
				}
				if(this.comeFrom) {
					console.log('--------我来自订单开票页面')
					this.feedback.showModal({
						content: `确认使用“${item.invoice_name}”抬头进行开票吗？`,
						confirm: () => {
							console.log('我点击了确认')
							this.muInvoiceInfo(item)
							uni.setStorageSync('InvoiceInfo',item);
				
							this.jump.jumpPrePage(this.$vhFrom)
						},
					})
				}else {
					console.log('--------我来自其他页面')
				}
			},
			
			// 删除发票
			async deleteInvoice(){
				if(this.invoiceList.length == 0) return this.feedback.toast({title:'亲，您还没有发票喔！'})
				if(this.invoiceList.length > 0 && this.invoiceSelectedList.length == 0) return this.feedback.toast({title:'请选择需要删除的发票'})
				await this.$u.api.deleteInvoice({id:this.invoiceSelectedList})
				this.feedback.toast({title:'删除成功', icon:'success'})
				this.getInvoiceList()
			}
		}
		
	}
</script>

<style scoped></style>
