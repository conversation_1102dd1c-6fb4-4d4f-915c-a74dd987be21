#!/bin/bash

# 遍历指定路径下的所有 .js 文件，并将路径推送到 Redis

# Redis 连接信息
REDIS_HOST=${REDIS_HOST}
REDIS_PORT="6379"
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_DB="5"
REDIS_KEY="cdn_refresh_urls"

# CDN 域名
CDN_DOMAIN="https://h5-cdn.vinehoo.com"

# 遍历的目录
STATIC_DIR="/usr/local/openresty/nginx/html/static"
REPLACE_DIR="/usr/local/openresty/nginx/html"

# 连接 Redis 并推送 URL
push_to_redis() {
    local file_path="$1"
    local relative_path="${file_path#$REPLACE_DIR}"
    local cdn_url="$CDN_DOMAIN$relative_path"
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" -n "$REDIS_DB" --no-auth-warning RPUSH "$REDIS_KEY" "$cdn_url"
    echo "Pushed $cdn_url to Redis"
}

# 遍历目录下所有 .js 和 .css 文件
find "$STATIC_DIR" -type f \( -name "*.js" -o -name "*.css" \) | while read -r file; do
    push_to_redis "$file"
done


echo "Finished pushing all files from $STATIC_DIR to Redis database $REDIS_DB, key $REDIS_KEY"