// 引入依赖
import Vue from 'vue'
import App from './App'
import store from './store'
import '@/common/js/fun/directive'
import { registerFilters } from '@/common/js/fun/filters'
registerFilters(Vue) //添加过滤器

import { registerOssPlugin } from '@/common/js/utils/oss'
registerOssPlugin(Vue)

Vue.config.productionTip = false
App.mpType = 'app'

// 挂载 Vue.prototype 原型连 (只供 .vue文件使用)
Vue.prototype.$store = store
// 引入uview
import uView from 'uview-ui'
Vue.use(uView)

const app = new Vue({
  store,
  ...App,
})

// http拦截器，将此部分放在new Vue()和app.$mount()之间，才能App.vue中正常使用
import httpInterceptor from '@/common/js/http/interceptor.js'
Vue.use(httpInterceptor, app)

// http接口API抽离，免于写url或者一些固定的参数
import httpApi from '@/common/js/http/api.js'
Vue.use(httpApi, app)

// 公共跳转方法，用于多页面跳转方法使用
import jumpRoute from '@/common/js/utils/jump.js'
Vue.use(jumpRoute, app)

// 公共方法，用于获取手机系统的一些信息
import systemInfo from '@/common/js/utils/system.js'
Vue.use(systemInfo, app)

// 公共方法，反馈信息
import feedback from '@/common/js/utils/feedback.js'
Vue.use(feedback, app)

// 公共方法，登录
import login from '@/common/js/utils/login.js'
Vue.use(login, app)

// 公共方法，处理日期
import date from '@/common/js/utils/date.js'
Vue.use(date, app)

// 公共方法， 网络
import network from '@/common/js/utils/network.js'
Vue.use(network, app)

// 公共方法， 来自哪里
import comes from '@/common/js/utils/comes.js'
Vue.use(comes, app)

// 公共方法， 页面
import pages from '@/common/js/utils/pages.js'
Vue.use(pages, app)

// 公共方法， 复制
import copy from '@/common/js/utils/copy.js'
Vue.use(copy, app)

// 公共方法，gps定位
import gps from '@/common/js/utils/gps.js'
Vue.use(gps, app)

// 公共方法，图片工具
import image from '@/common/js/utils/image.js'
Vue.use(image, app)

// 公共方法，参数工具
import param from '@/common/js/utils/param.js'
Vue.use(param, app)

// 公共方法，格式工具
import format from '@/common/js/utils/format.js'
Vue.use(format, app)

// 公共方法，分享
import share from '@/common/js/utils/share.js'
Vue.use(share, app)

// 公共方法，与App进行交互
// #ifdef H5
import wineYunJsBridge from '@/common/js/fun/wineYunJsBridge.js'
window.wineYunJsBridge = wineYunJsBridge
window.appUserInfo = async (appUserInfo) => {
  try {
    uni.setStorageSync('loginInfo', JSON.parse(appUserInfo))
    window.onAppUserInfoSuccess && window.onAppUserInfoSuccess()
    window.onAppUserInfoSuccess = null
  } catch (e) {
    feedback.toast({ title: '保存app用户信息失败' })
  }
}
// #endif

// import Vconsole from 'vconsole'
// Vue.use(new Vconsole())

app.$mount()

Vue.prototype.$isDev = false
