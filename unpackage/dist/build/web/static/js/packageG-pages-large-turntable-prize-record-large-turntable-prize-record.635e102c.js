(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageG-pages-large-turntable-prize-record-large-turntable-prize-record"],{"0402":function(t,e,n){"use strict";n.r(e);var i=n("21e4"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},"10eb":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n("d9e2"),n("d401")},"144f":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"d-flex j-center",style:[this.outerEmpConStyle]},[e("v-uni-view",{staticClass:"p-rela",style:[this.innEmpConStyle]},[e("v-uni-image",{staticClass:"w-p100 h-p100",attrs:{src:this.imageSrc,mode:"aspectFill"}}),e("v-uni-view",{staticClass:"p-abso w-p100 d-flex j-center font-28 text-9",style:[this.textStyle]},[this._v(this._s(this.text))])],1)],1)},a=[]},"21e4":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i=uni.getSystemInfoSync(),a={},r={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"44"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:a,statusBarHeight:i.statusBarHeight}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():uni.navigateBack()}}};e.default=r},"2da4":function(t,e,n){var i=n("39e4");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("64a51bd6",i,!0,{sourceMap:!1,shadowMode:!1})},"36f7":function(t,e,n){"use strict";n.r(e);var i=n("95b6"),a=n("0402");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("b10b");var o=n("f0c5"),u=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"2920cc37",null,!1,i["a"],void 0);e["default"]=u.exports},"39e4":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-load-more-wrap[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center}.u-load-more-inner[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:0 %?12?%}.u-more[data-v-15067509]{position:relative;display:flex;flex-direction:row;justify-content:center}.u-dot-text[data-v-15067509]{font-size:%?28?%}.u-loadmore-icon-wrap[data-v-15067509]{margin-right:%?8?%}.u-loadmore-icon[data-v-15067509]{display:flex;flex-direction:row;align-items:center;justify-content:center}',""]),t.exports=e},"3b53":function(t,e,n){"use strict";n.r(e);var i=n("8eb9"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},4053:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(n("b680"))},"513b":function(t,e,n){"use strict";n.r(e);var i=n("d57d"),a=n("3b53");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);var o=n("f0c5"),u=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"47c36ff7",null,!1,i["a"],void 0);e["default"]=u.exports},"55c2":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"vh-empty",props:{bgColor:{type:String,default:"#FFF"},paddingTop:{type:[String,Number],default:0},paddingBottom:{type:[String,Number],default:0},width:{type:[String,Number],default:440},height:{type:[String,Number],default:360},imageSrc:{type:String,default:""},textBottom:{type:[String,Number],default:46},text:{type:String,default:""}},computed:{outerEmpConStyle:function(){return{backgroundColor:this.bgColor,paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},innEmpConStyle:function(){return{width:this.width+"rpx",height:this.height+"rpx"}},textStyle:function(){return{bottom:this.textBottom+"rpx"}}}};e.default=i},"59df":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-navbar[data-v-2920cc37]{width:100%}.u-navbar-fixed[data-v-2920cc37]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-2920cc37]{width:100%}.u-navbar-inner[data-v-2920cc37]{display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-2920cc37]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-2920cc37]{flex:1}.u-title[data-v-2920cc37]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center}',""]),t.exports=e},"5ba4":function(t,e,n){"use strict";n.r(e);var i=n("144f"),a=n("a58c");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);var o=n("f0c5"),u=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"55488dce",null,!1,i["a"],void 0);e["default"]=u.exports},"6bdb":function(t,e,n){var i=n("59df");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("3dc5b1f9",i,!0,{sourceMap:!1,shadowMode:!1})},"776f":function(t,e,n){"use strict";n.r(e);var i=n("e643"),a=n("e4d5");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("afb6");var o=n("f0c5"),u=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"15067509",null,!1,i["a"],void 0);e["default"]=u.exports},"8a04":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-loadmore",props:{bgColor:{type:String,default:"transparent"},icon:{type:Boolean,default:!0},fontSize:{type:String,default:"28"},color:{type:String,default:"#606266"},status:{type:String,default:"loadmore"},iconType:{type:String,default:"circle"},loadText:{type:Object,default:function(){return{loadmore:"加载更多",loading:"正在加载...",nomore:"没有更多了"}}},isDot:{type:Boolean,default:!1},iconColor:{type:String,default:"#b7b7b7"},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},height:{type:[String,Number],default:"auto"}},data:function(){return{dotText:"●"}},computed:{loadTextStyle:function(){return{color:this.color,fontSize:this.fontSize+"rpx",position:"relative",zIndex:1,backgroundColor:this.bgColor}},cricleStyle:function(){return{borderColor:"#e5e5e5 #e5e5e5 #e5e5e5 ".concat(this.circleColor)}},flowerStyle:function(){return{}},showText:function(){var t="";return t="loadmore"==this.status?this.loadText.loadmore:"loading"==this.status?this.loadText.loading:"nomore"==this.status&&this.isDot?this.dotText:this.loadText.nomore,t}},methods:{loadMore:function(){"loadmore"==this.status&&this.$emit("loadmore")}}};e.default=i},"8eb9":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af");var a=i(n("f07e")),r=i(n("d0ff")),o=i(n("c964")),u={name:"large-turntable-prize-record",data:function(){return{from:"",priceRecordList:[],page:1,limit:20,totalPage:1,loadStatus:"loadmore"}},onLoad:function(t){this.from=t.from,this.getLargeTurntablePriceRecord()},methods:{getLargeTurntablePriceRecord:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n,i,o,u;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.newRabbitShareDrawList({page:t.page,limit:t.limit});case 2:n=e.sent,i=n.data,o=i.total,u=i.list,1==t.page?t.priceRecordList=u:t.priceRecordList=[].concat((0,r.default)(t.priceRecordList),(0,r.default)(u)),t.totalPage=Math.ceil(o/t.limit),t.loadStatus=t.page==t.totalPage?"nomore":"loadmore",console.log(n);case 8:case"end":return e.stop()}}),e)})))()}},onReachBottom:function(){this.page!=this.totalPage&&(this.loadStatus="loading",this.page++,this.getLargeTurntablePriceRecord())}};e.default=u},"95b6":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={uIcon:n("e5e1").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),n("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-icon-wrap"},[n("u-icon",{attrs:{name:t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?n("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?n("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},r=[]},a58c:function(t,e,n){"use strict";n.r(e);var i=n("55c2"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},a9e0:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},n("a4d3"),n("e01a"),n("d3b7"),n("d28b"),n("3ca3"),n("ddb0"),n("a630")},afb6:function(t,e,n){"use strict";var i=n("2da4"),a=n.n(i);a.a},b10b:function(t,e,n){"use strict";var i=n("6bdb"),a=n.n(i);a.a},d0ff:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,a.default)(t)||(0,r.default)(t)||(0,o.default)()};var i=u(n("4053")),a=u(n("a9e0")),r=u(n("dde1")),o=u(n("10eb"));function u(t){return t&&t.__esModule?t:{default:t}}},d57d:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={uNavbar:n("36f7").default,uLoadmore:n("776f").default,vhEmpty:n("5ba4").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"content"},[t.from?t._e():n("v-uni-view",{staticClass:"bb-s-01-eeeeee"},[n("u-navbar",{attrs:{"back-icon-color":"#333",title:"大转盘","title-size":"36","title-bold":!0,"title-color":"#333"}})],1),n("v-uni-view",{},[t.priceRecordList.length?n("v-uni-view",{},[t._l(t.priceRecordList,(function(e,i){return n("v-uni-view",{key:i,staticClass:"d-flex j-sb a-center bb-s-01-eeeeee ml-32 mr-32 ptb-32-plr-00"},[e.award.indexOf("谢谢")>=0?n("v-uni-view",{},[n("v-uni-text",{staticClass:"font-28 text-6"},[t._v("非常遗憾，")]),n("v-uni-text",{staticClass:"font-28 text-e80404"},[t._v("未中奖！")])],1):n("v-uni-view",{},[n("v-uni-text",{staticClass:"font-28 text-6"},[t._v("恭喜您，获得")]),n("v-uni-text",{staticClass:"font-28 text-e80404"},[t._v(t._s(e.award)+"！")])],1),n("v-uni-view",{staticClass:"font-24 text-9 ml-08 flex-shrink"},[t._v(t._s(e.addtime))])],1)})),n("v-uni-view",{staticClass:"ptb-32-plr-00"},[n("u-loadmore",{attrs:{status:t.loadStatus}})],1)],2):n("v-uni-view",{staticClass:"mt-p40"},[n("vh-empty",{attrs:{"padding-top":52,"padding-bottom":100,"image-src":"https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png",text:"暂无中奖记录~","text-bottom":0}})],1)],1)],1)},r=[]},e4d5:function(t,e,n){"use strict";n.r(e);var i=n("8a04"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},e643:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return i}));var i={uLine:n("9ff7").default,uLoading:n("301a").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-load-more-wrap",style:{backgroundColor:t.bgColor,marginBottom:t.marginBottom+"rpx",marginTop:t.marginTop+"rpx",height:t.$u.addUnit(t.height)}},[n("u-line",{attrs:{color:"#d4d4d4",length:"50"}}),n("v-uni-view",{staticClass:"u-load-more-inner",class:"loadmore"==t.status||"nomore"==t.status?"u-more":""},[n("v-uni-view",{staticClass:"u-loadmore-icon-wrap"},[n("u-loading",{staticClass:"u-loadmore-icon",attrs:{color:t.iconColor,mode:"circle"==t.iconType?"circle":"flower",show:"loading"==t.status&&t.icon}})],1),n("v-uni-view",{staticClass:"u-line-1",class:["nomore"==t.status&&1==t.isDot?"u-dot-text":"u-more-text"],style:[t.loadTextStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loadMore.apply(void 0,arguments)}}},[t._v(t._s(t.showText))])],1),n("u-line",{attrs:{color:"#d4d4d4",length:"50"}})],1)},r=[]}}]);