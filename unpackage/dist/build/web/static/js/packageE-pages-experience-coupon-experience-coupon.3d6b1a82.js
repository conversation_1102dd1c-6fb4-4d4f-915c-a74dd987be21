(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageE-pages-experience-coupon-experience-coupon"],{"12c6":function(e,t,n){"use strict";n.r(t);var a=n("51bd"),i=n("f074");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("f2f9");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);t["default"]=s.exports},2071:function(e,t,n){"use strict";var a=n("35ca"),i=n.n(a);i.a},"35ca":function(e,t,n){var a=n("552d");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("175b7730",a,!0,{sourceMap:!1,shadowMode:!1})},"51bd":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":e.isFixed},e.navbarClass],style:[e.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":e.newYearTheme},style:[e.navbarInnerStyle]},[e.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}},[e.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:e.backIconColor,size:e.backIconSize}}):[n("u-icon",{attrs:{name:e.pageLength<=1?"home":e.backIconName,color:e.backIconColor,size:e.backIconSize}}),e.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[e.backTextStyle]},[e._v(e._s(e.backText))]):e._e()]],2):e._e(),e.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[e.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:e.titleColor,fontSize:e.titleSize+"rpx",fontWeight:e.titleBold?"bold":"normal"}},[e._v(e._s(e.title))])],1):e._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[e._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[e._t("right")],2)],1)],1),e.isFixed&&!e.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(e.navbarHeight)+(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}):e._e()],1)},r=[]},"552d":function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-mask[data-v-f63a3092]{position:fixed;top:0;left:0;right:0;bottom:0;opacity:0;transition:-webkit-transform .3s;transition:transform .3s;transition:transform .3s,-webkit-transform .3s}.u-mask-show[data-v-f63a3092]{opacity:1}.u-mask-zoom[data-v-f63a3092]{-webkit-transform:scale(1.2);transform:scale(1.2)}',""]),e.exports=t},5726:function(e,t,n){"use strict";n.r(t);var a=n("fa36"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},"7f1a":function(e,t,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var r=n("26cb"),o=uni.getSystemInfoSync(),s={},c={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,r.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var e={};return e.height=this.navbarHeight+"px",e},navbarStyle:function(){var e={};return e.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(e,this.background),this.showBorder&&Object.assign(e,this.borderStyle),e},titleStyle:function(){var e={};return e.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.width=uni.upx2px(this.titleWidth)+"px",e},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var e=this.pages.getPageFullPath(),t=this.routeTable,n=t.pEAddressAdd,a=t.pEAddressManagement,i=t.pBOrderDepositDetail;(e.includes("/packageH")||e.includes(n)||e.includes(a)||e.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};t.default=c},8719:function(e,t,n){"use strict";n.r(t);var a=n("bda6"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},a126:function(e,t,n){var a=n("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},b216:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var a={vhNavbar:n("12c6").default,uMask:n("e710").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"content"},[n("vh-navbar",{attrs:{"back-icon-color":"#FFF",background:{background:"#E80404"},title:"体验券","title-color":"#FFF"}}),n("v-uni-view",{staticClass:"fade-in p-rela"},[n("v-uni-image",{staticClass:"w-p100",attrs:{src:e.experienceCouponinfo.background_map,mode:"widthFix"}}),n("v-uni-view",{staticClass:"p-abso top-1260 w-p100 d-flex j-center a-center"},[n("v-uni-image",{staticClass:"w-492 h-110",attrs:{src:e.osip+"/experience_coupon/get.png",mode:"widthFix"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$u.throttle(e.receiveNow,1500)}}})],1)],1),n("v-uni-view",{},[n("u-mask",{attrs:{show:e.showRecMask,zoom:!1}},[n("v-uni-view",{staticClass:"h-p100 d-flex j-center a-center"},[n("v-uni-view",{staticClass:"w-636 bg-ffffff d-flex flex-column a-center b-rad-16 ptb-60-plr-00"},[n("v-uni-image",{staticClass:"w-220 h-230",attrs:{src:e.osip+"/experience_coupon/"+(e.recSucc?"succ":"fail")+".png",mode:"aspectFill"}}),n("v-uni-view",{staticClass:"mt-18 font-28 text-f24b4b"},[e._v(e._s(e.feedbackContent))]),n("v-uni-view",{staticClass:"mt-42 font-28 text-3",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.jump.redirectTo("/packageE/pages/experience-coupon-list/experience-coupon-list")}}},[e._v("跳转到我的体验券页面")])],1)],1)],1)],1)],1)},r=[]},bbdc:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),e.exports=t},bda6:function(e,t,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("f07e")),r=a(n("c964"));n("e25e"),n("d3b7"),n("3ca3"),n("ddb0");var o={name:"experience-coupon",data:function(){return{osip:"https://images.vinehoo.com/vinehoomini/v3",sid:"",experienceCouponId:"",experienceCouponinfo:{},timer:null,count:0,recSucc:!1,alreadyGet:!1,feedbackContent:"",showRecMask:!1}},onLoad:function(e){this.experienceCouponId=parseInt(e.id),e.sid&&(this.sid=parseInt(e.sid)),this.getExperience()},methods:{getExperience:function(){var e=this;return(0,r.default)((0,i.default)().mark((function t(){var n,a,r;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,console.log("---------------------我是初始化获取体验券状态"),t.next=4,e.$u.api.experienceCouponInfo({id:e.experienceCouponId});case 4:n=t.sent,e.experienceCouponinfo=n.data,a=n.data,r=a.status,a.collar_uid,4==r&&(e.alreadyGet=!0),t.next=12;break;case 10:t.prev=10,t.t0=t["catch"](0);case 12:case"end":return t.stop()}}),t,null,[[0,10]])})))()},addStoreUser:function(e){var t=this;return(0,r.default)((0,i.default)().mark((function n(){var a,r,o,s,c,u;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,a=e.uid,r=e.telephone,o=e.nickname,s=e.avatar_image,c=e.openid,u={},u.uid=a,u.loginname=r,u.nickname=o,u.avatar_image=s,u.applet_openid=c,t.sid&&(u.sid=t.sid),n.next=11,t.$u.api.storeAddRegister(u);case 11:n.sent,n.next=16;break;case 14:n.prev=14,n.t0=n["catch"](0);case 16:case"end":return n.stop()}}),n,null,[[0,14]])})))()},receiveNow:function(){var e=this;this.login.isLogin()&&uni.getStorage({key:"loginInfo",success:function(){var t=(0,r.default)((0,i.default)().mark((function t(n){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.feedback.loading({title:"领取中..."}),t.next=4,Promise.all([e.addStoreUser(n.data),e.receiveExperience()]);case 4:e.count=15,e.timer=setInterval((function(){e.count--,e.afterReceiveGetExperience(),0===e.count&&(e.clearTimer(),e.feedback.hideLoading(),e.showRecMask=!0,e.feedbackContent="领取超时，请重新领取体验券~")}),2e3),t.next=10;break;case 8:t.prev=8,t.t0=t["catch"](0);case 10:case"end":return t.stop()}}),t,null,[[0,8]])})));return function(e){return t.apply(this,arguments)}}(),fail:function(){e.feedback.toast({title:"获取用户信息失败！"})}})},receiveExperience:function(){var e=this;return(0,r.default)((0,i.default)().mark((function t(){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$u.api.experienceCouponTake({id:e.experienceCouponId});case 3:t.next=7;break;case 5:t.prev=5,t.t0=t["catch"](0);case 7:case"end":return t.stop()}}),t,null,[[0,5]])})))()},afterReceiveGetExperience:function(){var e=this;return(0,r.default)((0,i.default)().mark((function t(){var n,a,r,o;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log("---------------------我是领取后获取体验券状态"),t.next=3,e.$u.api.experienceCouponInfo({id:e.experienceCouponId});case 3:n=t.sent,e.experienceCouponinfo=n.data,a=n.data,r=a.status,o=a.collar_uid,uni.getStorage({key:"loginInfo",success:function(t){1!==r?(e.clearTimer(),e.showRecMask=!0,2==r?e.feedbackContent="领取失败，体验券已被使用~":4==r&&(o===t.data.uid?e.alreadyGet?e.feedbackContent="领取失败，您已经领取过！":(e.feedbackContent="恭喜您,领取成功~",e.recSucc=!0,e.alreadyGet=!0):e.feedbackContent="领取失败，已被其他人领取！")):console.log("------------体验券未被领取")},fail:function(){e.feedback.toast({title:"获取用户信息失败！"})}});case 7:case"end":return t.stop()}}),t)})))()},clearTimer:function(){console.log("销毁了定时器"),this.timer&&(clearInterval(this.timer),this.timer=null,this.count=0)}},onUnload:function(){this.clearTimer()}};t.default=o},dc86:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-mask",class:{"u-mask-zoom":e.zoom,"u-mask-show":e.show},style:[e.maskStyle,e.zoomStyle],attrs:{"hover-stop-propagation":!0},on:{touchmove:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t),function(){}.apply(void 0,arguments)},click:function(t){arguments[0]=t=e.$handleEvent(t),e.click.apply(void 0,arguments)}}},[e._t("default")],2)},i=[]},e710:function(e,t,n){"use strict";n.r(t);var a=n("dc86"),i=n("5726");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("2071");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"f63a3092",null,!1,a["a"],void 0);t["default"]=s.exports},ebc8:function(e,t,n){"use strict";n.r(t);var a=n("b216"),i=n("8719");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"33ed080f",null,!1,a["a"],void 0);t["default"]=s.exports},f074:function(e,t,n){"use strict";n.r(t);var a=n("7f1a"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},f2f9:function(e,t,n){"use strict";var a=n("a126"),i=n.n(a);i.a},fa36:function(e,t,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("b64b");var r={name:"u-mask",props:{show:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:""},customStyle:{type:Object,default:function(){return{}}},zoom:{type:Boolean,default:!0},duration:{type:[Number,String],default:300},maskClickAble:{type:Boolean,default:!0}},data:function(){return{zoomStyle:{transform:""},scale:"scale(1.2, 1.2)"}},watch:{show:function(e){e&&this.zoom?this.zoomStyle.transform="scale(1, 1)":!e&&this.zoom&&(this.zoomStyle.transform=this.scale)}},computed:{maskStyle:function(){var e={backgroundColor:"rgba(0, 0, 0, 0.6)"};return this.show?e.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.mask:e.zIndex=-1,e.transition="all ".concat(this.duration/1e3,"s ease-in-out"),Object.keys(this.customStyle).length&&(e=(0,i.default)((0,i.default)({},e),this.customStyle)),e}},methods:{click:function(){this.maskClickAble&&this.$emit("click")}}};t.default=r}}]);