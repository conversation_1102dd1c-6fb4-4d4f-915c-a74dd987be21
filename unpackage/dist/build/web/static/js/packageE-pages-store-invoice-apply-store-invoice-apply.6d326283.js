(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageE-pages-store-invoice-apply-store-invoice-apply"],{"0402":function(e,t,n){"use strict";n.r(t);var a=n("21e4"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},"0903":function(e,t,n){var a=n("a7c8");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("a5409156",a,!0,{sourceMap:!1,shadowMode:!1})},"0b9e":function(e,t,n){"use strict";var a=n("0903"),i=n.n(a);i.a},"19c0":function(e,t,n){"use strict";n.r(t);var a=n("e2b4"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},"21e4":function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("a9e3");var a=uni.getSystemInfoSync(),i={},o={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"44"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:i,statusBarHeight:a.statusBarHeight}},computed:{navbarInnerStyle:function(){var e={};return e.height=this.navbarHeight+"px",e},navbarStyle:function(){var e={};return e.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(e,this.background),e},titleStyle:function(){var e={};return e.left=(a.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.right=(a.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.width=uni.upx2px(this.titleWidth)+"px",e},navbarHeight:function(){return this.height?this.height:44}},created:function(){},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():uni.navigateBack()}}};t.default=o},2913:function(e,t,n){"use strict";n.r(t);var a=n("be95"),i=n("19c0");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var r=n("f0c5"),c=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=c.exports},"36f7":function(e,t,n){"use strict";n.r(t);var a=n("95b6"),i=n("0402");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("b10b");var r=n("f0c5"),c=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"2920cc37",null,!1,a["a"],void 0);t["default"]=c.exports},"59df":function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-navbar[data-v-2920cc37]{width:100%}.u-navbar-fixed[data-v-2920cc37]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-2920cc37]{width:100%}.u-navbar-inner[data-v-2920cc37]{display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-2920cc37]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-2920cc37]{flex:1}.u-title[data-v-2920cc37]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center}',""]),e.exports=t},"5afb":function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,"uni-page-body[data-v-51712a8c]{background-color:#f5f5f5}body.?%PAGE?%[data-v-51712a8c]{background-color:#f5f5f5}",""]),e.exports=t},"6bdb":function(e,t,n){var a=n("59df");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("3dc5b1f9",a,!0,{sourceMap:!1,shadowMode:!1})},"752d":function(e,t,n){"use strict";n.r(t);var a=n("e590"),i=n("e548");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("cb24"),n("0b9e");var r=n("f0c5"),c=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"51712a8c",null,!1,a["a"],void 0);t["default"]=c.exports},"942c":function(e,t,n){var a=n("5afb");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("0dfa0df7",a,!0,{sourceMap:!1,shadowMode:!1})},"95b6":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":e.isFixed,"u-border-bottom":e.borderBottom},style:[e.navbarStyle]},[n("v-uni-view",{staticClass:"u-status-bar",style:{height:e.statusBarHeight+"px"}}),n("v-uni-view",{staticClass:"u-navbar-inner",style:[e.navbarInnerStyle]},[e.isBack?n("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-icon-wrap"},[n("u-icon",{attrs:{name:e.backIconName,color:e.backIconColor,size:e.backIconSize}})],1),e.backText?n("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[e.backTextStyle]},[e._v(e._s(e.backText))]):e._e()],1):e._e(),e.title?n("v-uni-view",{staticClass:"u-navbar-content-title",style:[e.titleStyle]},[n("v-uni-view",{staticClass:"u-title u-line-1",style:{color:e.titleColor,fontSize:e.titleSize+"rpx",fontWeight:e.titleBold?"bold":"normal"}},[e._v(e._s(e.title))])],1):e._e(),n("v-uni-view",{staticClass:"u-slot-content"},[e._t("default")],2),n("v-uni-view",{staticClass:"u-slot-right"},[e._t("right")],2)],1)],1),e.isFixed&&!e.immersive?n("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(e.navbarHeight)+e.statusBarHeight+"px"}}):e._e()],1)},o=[]},a7c8:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.iha-form .ih-form-item[data-v-51712a8c]:not(:first-of-type){border-top:%?2?% solid #eee}',""]),e.exports=t},b10b:function(e,t,n){"use strict";var a=n("6bdb"),i=n.n(a);i.a},bcdd:function(e,t,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("f3f3")),o=a(n("fc11"));n("99af"),n("4de4"),n("d3b7"),n("ac1f"),n("00b4"),n("159b"),n("caad6"),n("b64b"),n("14d9");var r,c=n("26cb"),s=n("d8be"),l=n("06cd"),u=n("cd8b"),d=(r={},(0,o.default)(r,"".concat(s.MInvoiceType.General,"-").concat(s.MInvoiceFrontType.Person),{requireKeys:["invoice_name","email"],optionalKeys:[]}),(0,o.default)(r,"".concat(s.MInvoiceType.General,"-").concat(s.MInvoiceFrontType.Company),{requireKeys:["invoice_name","taxpayer","email"],optionalKeys:["company_address","company_tel","opening_bank","bank_account"]}),(0,o.default)(r,"".concat(s.MInvoiceType.Special,"-").concat(s.MInvoiceFrontType.Company),{requireKeys:["invoice_name","taxpayer","email","company_address","company_tel","opening_bank","bank_account"],optionalKeys:["consignee","telephone","province_id","city_id","town_id","province_name","city_name","town_name","address"]}),r),p={data:function(){return{MInvoiceType:s.MInvoiceType,MInvoiceFrontType:s.MInvoiceFrontType,MInvoiceTypeText:l.MInvoiceTypeText,MInvoiceFrontTypeText:l.MInvoiceFrontTypeText,loading:!0,sid:"",orderno:"",isSubmit:!1,params:{invoice_type:s.MInvoiceType.General,type_id:s.MInvoiceFrontType.Person,invoice_name:"",taxpayer:"",email:"",company_address:"",company_tel:"",opening_bank:"",bank_account:"",consignee:"",telephone:"",province_id:"",city_id:"",town_id:"",province_name:"",city_name:"",town_name:"",address:""},isExpandMore:!1,regionVisible:!1}},computed:(0,i.default)((0,i.default)({},(0,c.mapState)(["routeTable","invoiceInfo","addressInfoState","regionInfo"])),{},{isEdit:function(e){var t=e.params;return!!t.id},title:function(e){var t=e.isEdit;return t?"修改发票抬头":"添加发票抬头"},btnText:function(e){var t=e.isEdit;return t?"保存":"提交"},currType:function(e){var t=e.params,n=t.invoice_type,a=t.type_id;return"".concat(n,"-").concat(a)},currKeyObj:function(e){var t=e.currType;return d[t]||{requireKeys:[],optionalKeys:[]}},currMInvoiceFrontTypeText:function(e){var t=e.params;return t.invoice_type===s.MInvoiceType.Special?l.MInvoiceFrontTypeText.filter((function(e){var t=e.value;return t===s.MInvoiceFrontType.Company})):l.MInvoiceFrontTypeText},isGeneralInvoiceType:function(e){var t=e.params;return t.invoice_type===s.MInvoiceType.General},isSpecialInvoiceType:function(e){var t=e.params;return t.invoice_type===s.MInvoiceType.Special},isPersonInvoiceFrontType:function(e){var t=e.params;return t.type_id===s.MInvoiceFrontType.Person},isCompanyInvoiceFrontType:function(e){var t=e.params;return t.type_id===s.MInvoiceFrontType.Company},disabled:function(e){var t=e.params,n=e.currKeyObj,a=n.requireKeys,i=a.every((function(e){return!!t[e]}));return!i}}),watch:{"params.invoice_type":function(){this.isSpecialInvoiceType&&(this.params.type_id=s.MInvoiceFrontType.Company)}},onLoad:function(e){var t=e.sid,n=void 0===t?"":t,a=e.orderno,i=void 0===a?"":a;this.sid=n,this.orderno=i,this.loadStoreApplyInvoiceStatus(),this.initRegionData()},methods:(0,i.default)((0,i.default)({},(0,c.mapMutations)(["muInvoiceInfo","muAddressInfoState","muRegionInfo"])),{},{loadStoreApplyInvoiceStatus:function(){var e=this;this.sid&&this.orderno?this.$u.api.getStoreApplyInvoiceStatus({orderno:this.orderno}).then((function(t){var n=null===t||void 0===t?void 0:t.data,a=n.is_submit,i=void 0!==a&&a;e.isSubmit=i})).finally((function(){e.loading=!1})):this.loading=!1},onClearRegion:function(){this.params=Object.assign({},this.params,{province_id:"",city_id:"",town_id:"",province_name:"",city_name:"",town_name:""})},submit:function(){var e=this,t=this.params,n=t.id,a=t.invoice_type,i=t.type_id,o=t.email;if(u.emailPattern.test(o)){var r=this.currKeyObj,c=r.requireKeys,s=r.optionalKeys,l=c.concat(s),d={sid:this.sid,orderno:this.orderno,invoice_type:a,type_id:i};l.forEach((function(t){["province_id","city_id","town_id"].includes(t)?d[t]=e.params[t]||"":d[t]=e.params[t]})),console.log("params",d),n?(d.id=n,this.$u.api.updateInvoice(d).then((function(){e.feedback.toast({title:"修改成功",icon:"success"})}))):this.$u.api.storeApplyInvoice(d).then((function(){e.feedback.toast({title:"申请成功",icon:"success"}),e.params=e.$options.data().params}))}else this.feedback.toast({title:"请输入正确的邮箱地址",icon:"error"})},del:function(){var e=this;this.feedback.showModal({content:"确认删除吗？",confirm:function(){e.$u.api.deleteInvoice({id:[e.params.id]}).then((function(){e.feedback.toast({title:"删除成功",icon:"success"}),e.back()}))}})},back:function(){var e=this,t=setTimeout((function(){e.jump.navigateBack(),t&&clearTimeout(t)}),1500)},initRegionData:function(){var e=this;Object.keys(this.regionInfo).length||this.$u.api.regionList({isJson:!0}).then((function(t){var n=(null===t||void 0===t?void 0:t.data)||{},a=n.list,i=void 0===a?[]:a,o=[],r=[],c=[];i.forEach((function(e,t){o.push({label:e.name,value:e.id}),r.push([]),c.push([]),e.children.forEach((function(e,n){r[t].push({label:e.name,value:e.id}),c[t].push([]),e.children.forEach((function(e){c[t][n].push({label:e.name,value:e.id})}))}))})),e.muRegionInfo({provinces:o,citys:r,areas:c})}))},changeRegion:function(e){var t=e.province,n=t.value,a=t.label,i=e.city,o=i.value,r=i.label,c=e.area,s=c.value,l=c.label;this.params=Object.assign({},this.params,{province_id:n,city_id:o,town_id:s,province_name:a,city_name:r,town_name:l})}})};t.default=p},be95:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"ih-form-item d-flex ptb-32-plr-00"},[e.label?n("v-uni-view",{staticClass:"flex-shrink w-148 font-wei-500 font-28 text-3 l-h-40"},[e._v(e._s(e.label))]):e._e(),n("v-uni-view",{staticClass:"flex-1"},[e._t("default",[n("v-uni-view",{staticClass:"d-flex j-sb"},[n("v-uni-textarea",{staticClass:"flex-1 w-aut font-28 text-3 l-h-40",attrs:{"auto-height":!0,placeholder:e.currPlaceholder,"placeholder-style":"font-size: 28rpx; color: #999;"},on:{focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.onBlur.apply(void 0,arguments)},keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault(),arguments[0]=t=e.$handleEvent(t)}},model:{value:e.model[e.prop],callback:function(t){e.$set(e.model,e.prop,t)},expression:"model[prop]"}})],1)])],2)],1)},i=[]},cb24:function(e,t,n){"use strict";var a=n("942c"),i=n.n(a);i.a},e2b4:function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={props:{label:{type:String,default:""},model:{type:Object,default:function(){return{}}},prop:{type:String,default:""},placeholder:{type:String,default:""}},data:function(){return{isFocus:!1}},computed:{currPlaceholder:function(e){var t=e.label,n=e.placeholder;return n||"请输入".concat(t)}},methods:{onFocus:function(){this.isFocus=!0},onBlur:function(){var e=this,t=setTimeout((function(){e.isFocus=!1,t&&clearTimeout(t)}),100)},onClear:function(){this.model[this.prop]=""}}};t.default=a},e548:function(e,t,n){"use strict";n.r(t);var a=n("bcdd"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},e590:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return a}));var a={uNavbar:n("36f7").default,InvoiceHeadFormItem:n("2913").default,uButton:n("4f1b").default,vhRegion:n("b94e").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"iha-form"},[n("u-navbar",{attrs:{title:"申请开票","is-back":!1}}),e.loading?e._e():n("v-uni-view",{staticClass:"pb-104"},[e.sid&&e.orderno?e.isSubmit?n("v-uni-view",{staticClass:"flex-c-c h-400 font-wei-500 font-28 text-3 l-h-40"},[e._v("该订单已申请开票")]):n("v-uni-view",{staticClass:"bg-ffffff mtb-20-mlr-24 ptb-00-plr-24 b-rad-10"},[n("InvoiceHeadFormItem",{attrs:{label:"发票类型"}},[n("v-uni-view",{staticClass:"d-flex"},e._l(e.MInvoiceTypeText,(function(t){return n("v-uni-button",{key:t.value,staticClass:"vh-btn flex-c-c mr-20 w-144 h-44 b-rad-24 font-24",class:[t.value===e.params.invoice_type?"bg-fce4e3 text-e80404":"bg-f6f6f6 text-3"],on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.params.invoice_type=t.value}}},[e._v(e._s(t.text))])})),1)],1),n("InvoiceHeadFormItem",{attrs:{label:"抬头类型"}},[n("v-uni-view",{staticClass:"d-flex"},e._l(e.currMInvoiceFrontTypeText,(function(t){return n("v-uni-button",{key:t.value,staticClass:"vh-btn flex-c-c mr-20 w-144 h-44 b-rad-24 font-24",class:[t.value===e.params.type_id?"bg-fce4e3 text-e80404":"bg-f6f6f6 text-3"],on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.params.type_id=t.value}}},[e._v(e._s(t.text))])})),1)],1),e.isGeneralInvoiceType&&e.isPersonInvoiceFrontType?[n("InvoiceHeadFormItem",{attrs:{label:"发票抬头",model:e.params,prop:"invoice_name"}}),n("InvoiceHeadFormItem",{attrs:{label:"邮箱地址",model:e.params,prop:"email"}})]:e._e(),e.isGeneralInvoiceType&&e.isCompanyInvoiceFrontType?[n("InvoiceHeadFormItem",{attrs:{label:"发票抬头",model:e.params,prop:"invoice_name"}}),n("InvoiceHeadFormItem",{attrs:{label:"单位税号",model:e.params,prop:"taxpayer"}}),n("InvoiceHeadFormItem",{attrs:{label:"邮箱地址",model:e.params,prop:"email"}}),n("v-uni-view",{staticClass:"flex-s-c pt-32 bt-s-02-eeeeee",class:e.isExpandMore?"pb-0":"pb-32",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.isExpandMore=!e.isExpandMore}}},[n("v-uni-text",{staticClass:"font-28 text-9 l-h-40"},[e._v("更多发票信息(选填)")]),n("v-uni-image",{staticClass:"ml-10 w-20 h-12",class:e.isExpandMore?"t-ro-n-180 tran-2":"tran-2",attrs:{src:e.ossIcon("/invoices/arrow_d_20_12.png")}})],1),e.isExpandMore?n("v-uni-view",[n("InvoiceHeadFormItem",{attrs:{label:"单位地址",model:e.params,prop:"company_address"}}),n("InvoiceHeadFormItem",{attrs:{label:"单位电话",model:e.params,prop:"company_tel"}}),n("InvoiceHeadFormItem",{attrs:{label:"开户银行",model:e.params,prop:"opening_bank"}}),n("InvoiceHeadFormItem",{attrs:{label:"银行账号",model:e.params,prop:"bank_account"}})],1):e._e()]:e._e(),e.isSpecialInvoiceType&&e.isCompanyInvoiceFrontType?[n("InvoiceHeadFormItem",{attrs:{label:"发票抬头",model:e.params,prop:"invoice_name"}}),n("InvoiceHeadFormItem",{attrs:{label:"单位税号",model:e.params,prop:"taxpayer"}}),n("InvoiceHeadFormItem",{attrs:{label:"邮箱地址",model:e.params,prop:"email"}}),n("InvoiceHeadFormItem",{attrs:{label:"单位地址",model:e.params,prop:"company_address"}}),n("InvoiceHeadFormItem",{attrs:{label:"单位电话",model:e.params,prop:"company_tel"}}),n("InvoiceHeadFormItem",{attrs:{label:"开户银行",model:e.params,prop:"opening_bank"}}),n("InvoiceHeadFormItem",{attrs:{label:"银行账号",model:e.params,prop:"bank_account"}}),n("v-uni-view",{staticClass:"flex-sb-c pt-32 bt-s-02-eeeeee",class:e.isExpandMore?"pb-0":"pb-32",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.isExpandMore=!e.isExpandMore}}},[n("v-uni-view",{staticClass:"flex-c-c"},[n("v-uni-text",{staticClass:"font-28 text-9 l-h-40"},[e._v("更多发票信息(选填)")]),n("v-uni-image",{staticClass:"ml-10 w-20 h-12",class:e.isExpandMore?"t-ro-n-180 tran-2":"tran-2",attrs:{src:e.ossIcon("/invoices/arrow_d_20_12.png")}})],1)],1),e.isExpandMore?n("v-uni-view",[n("InvoiceHeadFormItem",{attrs:{label:"收票人",model:e.params,prop:"consignee",placeholder:"收票人姓名"}}),n("InvoiceHeadFormItem",{attrs:{label:"手机号码",model:e.params,prop:"telephone",placeholder:"收票人手机号"}}),n("InvoiceHeadFormItem",{attrs:{label:"所在地区",model:e.params,prop:"address",placeholder:"省份、市区、地区"}},[n("v-uni-view",{staticClass:"d-flex j-sb",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.regionVisible=!0}}},[n("v-uni-view",{staticClass:"font-28 l-h-40"},[e.params.province_name&&e.params.city_name&&e.params.town_name?n("v-uni-text",{staticClass:"text-3"},[e._v(e._s(e.params.province_name)+" "+e._s(e.params.city_name)+" "+e._s(e.params.town_name))]):n("v-uni-text",{staticClass:"text-9"},[e._v("省份、市区、地区")])],1)],1)],1),n("InvoiceHeadFormItem",{attrs:{label:"详细地址",model:e.params,prop:"address",placeholder:"街道、楼牌号等"}})],1):e._e()]:e._e()],2):n("v-uni-view",{staticClass:"flex-c-c h-400 font-wei-500 font-28 text-3 l-h-40"},[e._v("缺少门店ID或订单号")])],1),n("v-uni-view",{staticClass:"p-fixed bottom-0 z-999 w-p100 h-104 bg-feffff b-sh-00021200-022 d-flex j-center a-center"},[n("u-button",{attrs:{disabled:e.disabled,shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:e.disabled?"#FCE4E3":"#E80404",border:"none"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.submit.apply(void 0,arguments)}}},[e._v(e._s(e.btnText))])],1),Object.keys(e.regionInfo).length?n("vh-region",{on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.changeRegion.apply(void 0,arguments)}},model:{value:e.regionVisible,callback:function(t){e.regionVisible=t},expression:"regionVisible"}}):e._e()],1)},o=[]}}]);