(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-seller-order-list-auction-seller-order-list"],{"062a":function(t,e,n){var a=n("aab3");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("2db15654",a,!0,{sourceMap:!1,shadowMode:!1})},"0aa7":function(t,e,n){"use strict";n.r(e);var a=n("cad2"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"0d99":function(t,e,n){"use strict";n.r(e);var a=n("5762"),i=n("f224");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"087980d0",null,!1,a["a"],void 0);e["default"]=s.exports},1037:function(t,e,n){"use strict";n.r(e);var a=n("6cbb"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"10eb":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n("d9e2"),n("d401")},"12c6":function(t,e,n){"use strict";n.r(e);var a=n("51bd"),i=n("f074");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("f2f9");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);e["default"]=s.exports},"213a":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={vhNavbar:n("12c6").default,uIcon:n("e5e1").default,uTabs:n("b14f").default,AuctionOrderListItem:n("da1b").default,uButton:n("4f1b").default,uLoadmore:n("776f").default,AuctionEmpty:n("fb81").default,vhSkeleton:n("591b").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"content",class:t.loading?"h-vh-100 o-hid":""},[n("vh-navbar",{attrs:{title:t.showSearch?"":"我卖出的","show-border":!0}},[t.showSearch?t._e():[n("v-uni-image",{staticClass:"fade-in p-24 w-44 h-44",attrs:{slot:"right",src:t.ossIcon("/comm/ser_black.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)}},slot:"right"})],t.showSearch?n("v-uni-view",{staticClass:"fade-in d-flex a-center"},[n("v-uni-view",{staticClass:"p-12",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeSearch.apply(void 0,arguments)}}},[n("u-icon",{attrs:{name:"close",size:34,color:"#333"}})],1),n("v-uni-view",{staticClass:"bg-f7f7f7 d-flex j-sb a-center b-rad-40 pl-26 pr-26"},[n("v-uni-view",{staticClass:"p-rela w-472 h-68 d-flex a-center"},[n("v-uni-image",{staticClass:"w-44 h-44",attrs:{src:t.ossIcon("/comm/ser_gray.png"),mode:"aspectFill"}}),n("v-uni-input",{staticClass:"w-352 h-p100 ml-10 font-28 text-3",attrs:{type:"text",placeholder:"请输入关键字","placeholder-style":"color:#999;font-size:28rpx;"},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}}),n("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:""!==t.$u.trim(t.keyword,"all"),expression:"$u.trim(keyword, 'all') !== ''"}],staticClass:"p-abso right-0 top-0 w-40 h-p100 d-flex j-center a-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.keyword=""}}},[n("v-uni-image",{staticClass:"w-40 h-40",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/del_gray.png",mode:"aspectFill"}})],1)],1)],1),n("v-uni-view",{staticClass:"p-12 font-28 font-wei text-6 w-s-now",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)}}},[t._v("搜索")])],1):t._e()],2),t.loading?n("vh-skeleton",{attrs:{bgColor:"#FFF",showLoading:!1}}):n("v-uni-view",{staticClass:"fade-in"},[n("v-uni-view",{staticClass:"p-stic z-980",style:{top:t.system.navigationBarHeight()+"px"}},[n("u-tabs",{attrs:{list:t.tabList,current:t.currentTabs,height:92,"font-size":28,"inactive-color":"#333","active-color":"#2E7BFF","bar-width":36,"bar-height":8,"bar-style":{background:"linear-gradient(214deg, #A8C8FF 0%, #2E7BFF 100%);"},"is-scroll":!1},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTabs.apply(void 0,arguments)}}})],1),n("v-uni-view",{},[t.orderList.length>0?n("v-uni-view",{staticClass:"pb-20"},[t._l(t.orderList,(function(e,a){return n("v-uni-view",{key:a,staticClass:"bg-ffffff b-rad-16 mt-20 mr-24 mb-20 ml-24 pl-24 pr-24",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.jump.navigateTo(t.routeTable.pHAuctionSellerOrderDetail+"?orderNo="+e.order_no)}}},[n("AuctionOrderListItem",{attrs:{item:e,type:1}}),n("v-uni-view",{staticClass:"flex-e-c ptb-28-plr-00"},[[0].includes(e.status)?n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"180rpx",height:"52rpx",fontSize:"28rpx",color:"#2E7BFF",border:"1rpx solid #2E7BFF"}},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.remindPayment(e)}}},[t._v("提醒付款")])],1):t._e(),[1].includes(e.status)?n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"180rpx",height:"52rpx",fontSize:"28rpx",color:"#2E7BFF",border:"1rpx solid #2E7BFF"}},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.jump.navigateTo(t.routeTable.pHAuctionSellerOrderDetail+"?orderNo="+e.order_no+"&toShip=1")}}},[t._v("去发货")])],1):t._e(),[2].includes(e.status)?n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"180rpx",height:"52rpx",fontSize:"28rpx",color:"#2E7BFF",border:"1rpx solid #2E7BFF"}},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.viewLogistics(e)}}},[t._v("查看物流")])],1):t._e(),[3].includes(e.status)?n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"180rpx",height:"52rpx",fontSize:"28rpx",color:"#666",border:"1rpx solid #666"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.navigateTo(t.routeTable.pHAuctionOrderEvaluateList+"?currentTabs=1")}}},[t._v("查看评价")])],1):t._e(),[4].includes(e.status)?n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"180rpx",height:"52rpx",fontSize:"28rpx",color:"#666",border:"1rpx solid #666"}},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.jump.navigateTo(t.routeTable.pHAuctionSellerOrderDetail+"?orderNo="+e.order_no)}}},[t._v("查看详情")])],1):t._e(),[7].includes(e.status)?n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"180rpx",height:"52rpx",fontSize:"28rpx",color:"#666",border:"1rpx solid #666"}},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.jump.navigateTo(t.routeTable.pHAuctionSellerAfterSaleDetail+"?refundOrderNo="+e.refund_order_no)}}},[t._v("售后详情")])],1):t._e()],1)],1)})),n("u-loadmore",{attrs:{status:t.loadStatus}})],2):n("AuctionEmpty",{attrs:{imageSrc:t.ossIcon("/auction_empty/emp1.png"),text:"空空如也",subText:"别急早晚会有的～",paddingTop:108,paddingBottom:780}})],1)],1)],1)},r=[]},"2da4":function(t,e,n){var a=n("39e4");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("64a51bd6",a,!0,{sourceMap:!1,shadowMode:!1})},"30d5":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"d-flex flex-column a-center",style:[t.outerEmpConStyle]},[n("v-uni-image",{style:{width:t.width+"rpx",height:t.height+"rpx"},attrs:{src:t.imageSrc,mode:"aspectFill"}}),n("v-uni-view",{staticClass:"mt-60 font-36 font-wei text-3"},[t._v(t._s(t.text))]),t.subText?n("v-uni-view",{staticClass:"mt-20 font-28 text-6"},[t._v(t._s(t.subText))]):t._e()],1)},i=[]},3560:function(t,e,n){"use strict";n.r(e);var a=n("213a"),i=n("0aa7");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("e4a0");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"9fcbcb32",null,!1,a["a"],void 0);e["default"]=s.exports},"39e4":function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-load-more-wrap[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center}.u-load-more-inner[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:0 %?12?%}.u-more[data-v-15067509]{position:relative;display:flex;flex-direction:row;justify-content:center}.u-dot-text[data-v-15067509]{font-size:%?28?%}.u-loadmore-icon-wrap[data-v-15067509]{margin-right:%?8?%}.u-loadmore-icon[data-v-15067509]{display:flex;flex-direction:row;align-items:center;justify-content:center}',""]),t.exports=e},4053:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,a.default)(t)};var a=function(t){return t&&t.__esModule?t:{default:t}}(n("b680"))},"4ce6":function(t,e,n){var a=n("9d1b");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("e2c14d06",a,!0,{sourceMap:!1,shadowMode:!1})},"4f1b":function(t,e,n){"use strict";n.r(e);var a=n("825d"),i=n("8e1d");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("fa94");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"4ed92bb2",null,!1,a["a"],void 0);e["default"]=s.exports},"51bd":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[n("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},r=[]},5762:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-text",{staticClass:"p-rela z-04 w-88 h-30 flex-c-c bs-bb b-rad-02 mb-02 font-18 text-hidden",class:0===this.auctionType?"b-s-01-e80404 text-e80404":"b-s-01-2e7bff text-2e7bff"},[this._v(this._s(0===this.auctionType?"商家拍品":"个人拍品"))])},i=[]},"6cbb":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var a={name:"AuctionEmpty",props:{bgColor:{type:String,default:"#FFF"},paddingTop:{type:[String,Number],default:0},paddingBottom:{type:[String,Number],default:0},width:{type:[String,Number],default:440},height:{type:[String,Number],default:400},imageSrc:{type:String,default:""},text:{type:String,default:""},subText:{type:String,default:""}},computed:{outerEmpConStyle:function(){return{backgroundColor:this.bgColor,paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}}}};e.default=a},"776f":function(t,e,n){"use strict";n.r(e);var a=n("e643"),i=n("e4d5");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("afb6");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"15067509",null,!1,a["a"],void 0);e["default"]=s.exports},"7f1a":function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var r=n("26cb"),o=uni.getSystemInfoSync(),s={},u={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,r.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,n=e.pEAddressAdd,a=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(n)||t.includes(a)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=u},"825d":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?n("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},i=[]},"8a04":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var a={name:"u-loadmore",props:{bgColor:{type:String,default:"transparent"},icon:{type:Boolean,default:!0},fontSize:{type:String,default:"28"},color:{type:String,default:"#606266"},status:{type:String,default:"loadmore"},iconType:{type:String,default:"circle"},loadText:{type:Object,default:function(){return{loadmore:"加载更多",loading:"正在加载...",nomore:"没有更多了"}}},isDot:{type:Boolean,default:!1},iconColor:{type:String,default:"#b7b7b7"},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},height:{type:[String,Number],default:"auto"}},data:function(){return{dotText:"●"}},computed:{loadTextStyle:function(){return{color:this.color,fontSize:this.fontSize+"rpx",position:"relative",zIndex:1,backgroundColor:this.bgColor}},cricleStyle:function(){return{borderColor:"#e5e5e5 #e5e5e5 #e5e5e5 ".concat(this.circleColor)}},flowerStyle:function(){return{}},showText:function(){var t="";return t="loadmore"==this.status?this.loadText.loadmore:"loading"==this.status?this.loadText.loading:"nomore"==this.status&&this.isDot?this.dotText:this.loadText.nomore,t}},methods:{loadMore:function(){"loadmore"==this.status&&this.$emit("loadmore")}}};e.default=a},"8e1d":function(t,e,n){"use strict";n.r(e);var a=n("9476"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},9476:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("c975"),n("d3b7"),n("ac1f");var a={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(n){var a=n[0];if(a.width&&a.width&&(a.targetWidth=a.height>a.width?a.height:a.width,a.targetWidth)){e.fields=a;var i,r;i=t.touches[0].clientX,r=t.touches[0].clientY,e.rippleTop=r-a.top-a.targetWidth/2,e.rippleLeft=i-a.left-a.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var n="";n=uni.createSelectorQuery().in(t),n.select(".u-btn").boundingClientRect(),n.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=a},"9d1b":function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,"uni-page-body[data-v-9fcbcb32]{background-color:#f5f5f5}body.?%PAGE?%[data-v-9fcbcb32]{background-color:#f5f5f5}",""]),t.exports=e},a126:function(t,e,n){var a=n("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},a9e0:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},n("a4d3"),n("e01a"),n("d3b7"),n("d28b"),n("3ca3"),n("ddb0"),n("a630")},aab3:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},afb6:function(t,e,n){"use strict";var a=n("2da4"),i=n.n(a);i.a},bbdc:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},cad2:function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af");var i=a(n("d0ff")),r=a(n("f07e")),o=a(n("c964")),s=a(n("f3f3")),u=n("26cb"),l={name:"auction-seller-order-list",data:function(){return{loading:!0,showSearch:!1,keyword:"",tabList:[{name:"全部"},{name:"待付款"},{name:"待发货"},{name:"待收货"},{name:"已完成"}],currentTabs:0,hasGotOrderList:0,orderList:[],page:1,limit:10,totalPage:1,loadStatus:"loadmore"}},computed:(0,s.default)((0,s.default)({},(0,u.mapState)(["routeTable"])),(0,u.mapState)("auction",["logisticsInfo"])),onShow:function(){var t=this;this.login.isLoginV3(this.$vhFrom).then((function(e){e&&t.init()}))},methods:(0,s.default)((0,s.default)({},(0,u.mapMutations)("auction",["muLogisticsInfo"])),{},{init:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.page=1,e.next=3,t.getOrderList();case 3:t.loading=!1;case 4:case"end":return e.stop()}}),e)})))()},changeTabs:function(t){this.currentTabs=t,this.page=1,this.getOrderList()},getOrderList:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){var n,a,o,s;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.hasGotOrderList&&t.feedback.loading(),e.next=3,t.$u.api.auctionSellerOrderList({type:t.currentTabs,page:t.page,limit:t.limit,keyword:t.keyword});case 3:n=e.sent,a=n.data,o=a.list,s=a.total,1==t.page?t.orderList=o:t.orderList=[].concat((0,i.default)(t.orderList),(0,i.default)(o)),t.totalPage=Math.ceil(s/t.limit),t.loadStatus=t.page==t.totalPage?"nomore":"loadmore",t.hasGotOrderList=1,uni.stopPullDownRefresh(),t.feedback.hideLoading();case 13:case"end":return e.stop()}}),e)})))()},search:function(){this.showSearch=!0,this.init()},confirmSearch:function(){this.init()},closeSearch:function(){this.keyword="",this.showSearch=!1,this.init()},remindPayment:function(t){var e=this;return(0,o.default)((0,r.default)().mark((function n(){var a;return(0,r.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return a=t.order_no,n.prev=1,n.next=4,e.$u.api.auctionRemindNotice({order_no:a,type:1});case 4:e.feedback.toast({title:"提醒付款成功~"}),n.next=9;break;case 7:n.prev=7,n.t0=n["catch"](1);case 9:case"end":return n.stop()}}),n,null,[[1,7]])})))()},viewLogistics:function(t){var e=t.goods_img,n=t.express_type,a=t.express_number,i=t.consignee_phone,r=t.province_name,o=t.city_name,s=t.district_name,u=t.address;this.muLogisticsInfo({image:e,expressType:n,logisticCode:a,phone:i,address:r+o+s+u}),this.jump.navigateTo(this.routeTable.pHAuctionLogisticsInfo)}}),onPullDownRefresh:function(){this.init()},onReachBottom:function(){this.page!=this.totalPage&&0!=this.totalPage&&(this.loadStatus="loading",this.page++,this.getOrderList())}};e.default=l},cea0:function(t,e,n){"use strict";n.r(e);var a=n("fad3"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},d0ff:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,a.default)(t)||(0,i.default)(t)||(0,r.default)(t)||(0,o.default)()};var a=s(n("4053")),i=s(n("a9e0")),r=s(n("dde1")),o=s(n("10eb"));function s(t){return t&&t.__esModule?t:{default:t}}},d305:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var a={name:"AuctionOrderTypeName",props:{auctionType:{type:[Number,String],default:0}}};e.default=a},da1b:function(t,e,n){"use strict";n.r(e);var a=n("fca7"),i=n("cea0");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"c2dece06",null,!1,a["a"],void 0);e["default"]=s.exports},e4a0:function(t,e,n){"use strict";var a=n("4ce6"),i=n.n(a);i.a},e4d5:function(t,e,n){"use strict";n.r(e);var a=n("8a04"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},e643:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={uLine:n("9ff7").default,uLoading:n("301a").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-load-more-wrap",style:{backgroundColor:t.bgColor,marginBottom:t.marginBottom+"rpx",marginTop:t.marginTop+"rpx",height:t.$u.addUnit(t.height)}},[n("u-line",{attrs:{color:"#d4d4d4",length:"50"}}),n("v-uni-view",{staticClass:"u-load-more-inner",class:"loadmore"==t.status||"nomore"==t.status?"u-more":""},[n("v-uni-view",{staticClass:"u-loadmore-icon-wrap"},[n("u-loading",{staticClass:"u-loadmore-icon",attrs:{color:t.iconColor,mode:"circle"==t.iconType?"circle":"flower",show:"loading"==t.status&&t.icon}})],1),n("v-uni-view",{staticClass:"u-line-1",class:["nomore"==t.status&&1==t.isDot?"u-dot-text":"u-more-text"],style:[t.loadTextStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loadMore.apply(void 0,arguments)}}},[t._v(t._s(t.showText))])],1),n("u-line",{attrs:{color:"#d4d4d4",length:"50"}})],1)},r=[]},f074:function(t,e,n){"use strict";n.r(e);var a=n("7f1a"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},f224:function(t,e,n){"use strict";n.r(e);var a=n("d305"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},f2f9:function(t,e,n){"use strict";var a=n("a126"),i=n.n(a);i.a},fa94:function(t,e,n){"use strict";var a=n("062a"),i=n.n(a);i.a},fad3:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var a={name:"AuctionOrderListItem",props:{type:{type:[Number,String],default:0},item:{type:Object,default:function(){return{}}}}};e.default=a},fb81:function(t,e,n){"use strict";n.r(e);var a=n("30d5"),i=n("1037");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"e3e49132",null,!1,a["a"],void 0);e["default"]=s.exports},fca7:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={vhImage:n("ce7c").default,AuctionOrderTypeName:n("0d99").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"d-flex j-sb a-center pt-32 pb-32 bb-s-01-eeeeee"},[n("v-uni-text",{staticClass:"font-24 text-3"},[t._v(t._s(t.item.created_time))]),0===t.type?[0===t.item.status?n("v-uni-text",{staticClass:"font-28 text-e80404"},[t._v("待付款")]):t._e(),1===t.item.status?n("v-uni-text",{staticClass:"font-28 text-e80404"},[t._v("待发货")]):t._e(),2===t.item.status?n("v-uni-text",{staticClass:"font-28 text-e80404"},[t._v("待收货")]):t._e(),3===t.item.status?n("v-uni-text",{staticClass:"font-28 text-3"},[t._v("已完成")]):t._e(),4===t.item.status?n("v-uni-text",{staticClass:"font-28 text-3"},[t._v("订单关闭")]):t._e(),5===t.item.status?n("v-uni-text",{staticClass:"font-28 text-e80404"},[t._v("待评价")]):t._e(),7===t.item.status?n("v-uni-text",{staticClass:"font-28 text-3"},[t._v("已退款")]):t._e()]:t._e(),1===t.type?[0===t.item.status?n("v-uni-text",{staticClass:"font-28 text-2e7bff"},[t._v("待付款")]):t._e(),1===t.item.status?n("v-uni-text",{staticClass:"font-28 text-2e7bff"},[t._v("待发货")]):t._e(),2===t.item.status?n("v-uni-text",{staticClass:"font-28 text-2e7bff"},[t._v("待收货")]):t._e(),3===t.item.status?n("v-uni-text",{staticClass:"font-28 text-3"},[t._v("已完成")]):t._e(),4===t.item.status?n("v-uni-text",{staticClass:"font-28 text-3"},[t._v("订单关闭")]):t._e(),7===t.item.status?n("v-uni-text",{staticClass:"font-28 text-3"},[t._v("已退款")]):t._e()]:t._e()],2),n("v-uni-view",{staticClass:"bb-s-01-eeeeee pt-08 pb-28"},[n("v-uni-view",{staticClass:"d-flex mt-20"},[n("vh-image",{attrs:{"loading-type":2,src:t.item.goods_img,width:160,height:160,"border-radius":10}}),n("v-uni-view",{staticClass:"flex-1 d-flex flex-column j-sb ml-12"},[n("v-uni-view",{},[n("v-uni-view",{staticClass:"font-24 text-0 text-hidden-2"},[t._v(t._s(t.item.goods_name))]),n("v-uni-view",{staticClass:"flex-e-c"},[n("v-uni-text",{staticClass:"font-24 text-9"},[t._v("x"+t._s(t.item.order_qty))])],1)],1),n("v-uni-view",{staticClass:"d-flex j-sb a-center"},[n("AuctionOrderTypeName",{attrs:{auctionType:t.item.auction_type}}),n("v-uni-view",{staticClass:"text-3"},[n("v-uni-text",{staticClass:"font-18"},[t._v("成交价：")]),n("v-uni-text",{staticClass:"font-22 font-wei"},[t._v("¥")]),n("v-uni-text",{staticClass:"font-32 font-wei"},[t._v(t._s(t.item.payment_amount))])],1)],1)],1)],1)],1)],1)},r=[]}}]);