(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageI-pages-learning-records-index"],{"02aa":function(t,e,a){"use strict";a.r(e);var i=a("c0a4"),n=a("af3a");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("e58b");var r=a("f0c5"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"626518c5",null,!1,i["a"],void 0);e["default"]=s.exports},"062a":function(t,e,a){var i=a("aab3");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("2db15654",i,!0,{sourceMap:!1,shadowMode:!1})},"12c6":function(t,e,a){"use strict";a.r(e);var i=a("51bd"),n=a("f074");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("f2f9");var r=a("f0c5"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"519170ec",null,!1,i["a"],void 0);e["default"]=s.exports},"49cf":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("f07e")),o=i(a("c964"));a("ac1f"),a("841c"),a("99af");var r={data:function(){return{background:{backgroundColor:"#D60C0C"},tabs:[{name:"测试"},{name:"章节"}],currentIndex:0,list:[],appStatusBarHeight:0,customStyle:{width:"130rpx",borderRadius:"100rpx"}}},onShow:function(){var t=this;this.search(),uni.getSystemInfo({success:function(e){t.appStatusBarHeight=e.statusBarHeight?e.statusBarHeight:48}})},watch:{currentIndex:function(t){this.list=[],t?this.getChapterScanRecord():this.getExamScanRecord()}},methods:{viewErrorQuestionList:function(t){this.jump.navigateTo("".concat(this.$routeTable.PITestErrorQuestionList,"?paper_id=").concat(t.paper_id))},goTestResume:function(t){console.log(t),this.jump.navigateTo("".concat(this.$routeTable.PIAnswerList,"?id=").concat(t.exam_id,"&paper_id=").concat(t.paper_id,"&from=records"))},goDetails:function(t){this.jump.navigateTo("".concat(this.$routeTable.PICourseDetails,"?id=").concat(t.id))},search:function(){this.currentIndex?this.getChapterScanRecord():this.getExamScanRecord()},cancelFav:function(t){var e=this;return(0,o.default)((0,n.default)().mark((function a(){var i;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i={},i=e.currentIndex?{chapter_id:t.id}:{question_id:t.id},a.next=4,e.$u.api.addCollection(i);case 4:a.sent,e.feedback.toast({title:"操作成功"}),e.search();case 7:case"end":return a.stop()}}),a)})))()},switchTab:function(t){this.currentIndex=t},getChapterScanRecord:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var a,i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={page:1,limit:999},e.next=3,t.$u.api.getChapterScanRecord(a);case 3:i=e.sent,console.log(i),t.list=i.data.list;case 6:case"end":return e.stop()}}),e)})))()},getExamScanRecord:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var a,i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={page:1,limit:999},e.next=3,t.$u.api.getExamScanRecord(a);case 3:i=e.sent,console.log(i),t.list=i.data.list;case 6:case"end":return e.stop()}}),e)})))()},gotoBack:function(){uni.reLaunch({url:"/packageI/pages/index/index"})}}};e.default=r},"4f1b":function(t,e,a){"use strict";a.r(e);var i=a("825d"),n=a("8e1d");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("fa94");var r=a("f0c5"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"4ed92bb2",null,!1,i["a"],void 0);e["default"]=s.exports},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uIcon:a("e5e1").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},o=[]},7079:function(t,e,a){var i=a("ea4f");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("fb54c3a2",i,!0,{sourceMap:!1,shadowMode:!1})},"7f1a":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var o=a("26cb"),r=uni.getSystemInfoSync(),s={},l={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:r.statusBarHeight,appStatusBarHeight:0}},computed:(0,n.default)((0,n.default)({},(0,o.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,i=e.pEAddressManagement,n=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(i)||t.includes(n))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=l},"825d":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?a("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},n=[]},"8e1d":function(t,e,a){"use strict";a.r(e);var i=a("9476"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},9476:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3"),a("c975"),a("d3b7"),a("ac1f");var i={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(a){var i=a[0];if(i.width&&i.width&&(i.targetWidth=i.height>i.width?i.height:i.width,i.targetWidth)){e.fields=i;var n,o;n=t.touches[0].clientX,o=t.touches[0].clientY,e.rippleTop=o-i.top-i.targetWidth/2,e.rippleLeft=n-i.left-i.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var a="";a=uni.createSelectorQuery().in(t),a.select(".u-btn").boundingClientRect(),a.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=i},a126:function(t,e,a){var i=a("bbdc");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("703d0287",i,!0,{sourceMap:!1,shadowMode:!1})},aab3:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},af3a:function(t,e,a){"use strict";a.r(e);var i=a("49cf"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},bbdc:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},c0a4:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={vhNavbar:a("12c6").default,uIcon:a("e5e1").default,uTabs:a("b14f").default,uButton:a("4f1b").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"container"},[a("v-uni-view",[t.$appStatusBarHeight?a("v-uni-view",[a("v-uni-view",{staticClass:"p-fixed z-980 top-0 w-p100",style:{background:"#D60C0C"}},[a("v-uni-view",{style:{height:t.$appStatusBarHeight+"px"}}),a("v-uni-view",{staticClass:"p-rela ml-20 h-px-48 d-flex a-center"},[a("v-uni-view",{staticClass:"h-p100 d-flex a-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.navigateBack()}}},[a("u-icon",{attrs:{name:"nav-back",color:"#fff",size:44}})],1),a("v-uni-view",{staticClass:"font-36 font-wei text-333333"})],1)],1)],1):a("vh-navbar",{attrs:{"back-icon-color":"#fff",title:"",background:t.background}})],1),a("u-tabs",{staticStyle:{position:"fixed",width:"100%","z-index":"11112"},style:{top:t.$appStatusBarHeight?parseInt(t.$appStatusBarHeight)+48+"px":"44px"},attrs:{list:t.tabs,"custom-back":t.gotoBack,"active-color":"#fff","font-size":"34","inactive-color":"#FF8383","bg-color":"#D60C0C","is-scroll":!1,current:t.currentIndex},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.switchTab.apply(void 0,arguments)}}}),t.list.length?a("v-uni-view",{style:{marginTop:t.$appStatusBarHeight?parseInt(t.$appStatusBarHeight)+98+"px":"44px"}},[t.currentIndex?a("v-uni-view",{staticClass:"favorites-list-item"},t._l(t.list,(function(e,i){return a("v-uni-view",{key:i,staticClass:"list"},[a("v-uni-view",{staticClass:"atc",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goDetails(e)}}},[a("img",{staticClass:"atc-list-item-img",attrs:{src:e.cover_img,alt:""}}),a("v-uni-view",{staticClass:"atc-list-item-content"},[a("v-uni-view",{staticClass:"atc-list-item-content-title"},[t._v(t._s(e.title))]),a("v-uni-view",{staticClass:"atc-list-item-content-desc"},[t._v(t._s(e.subtitle))])],1)],1),a("v-uni-view",{staticClass:"atc-line"}),a("v-uni-view",{staticClass:"list-footer"},[a("v-uni-view",{staticClass:"opr"},[t._v(t._s(e.topic))])],1)],1)})),1):a("v-uni-view",{staticClass:"favorites-list-item"},t._l(t.list,(function(e,i){return a("v-uni-view",{key:i,staticClass:"list"},[a("v-uni-view",{staticClass:"test-title"},[t._v(t._s(e.title))]),a("v-uni-view",{staticClass:"atc-line"}),a("v-uni-view",{staticClass:"test-time"},[a("v-uni-view",{staticClass:"test-time-flex",staticStyle:{"margin-bottom":"16rpx"}},[a("v-uni-view",{staticClass:"test-time-title"},[t._v("开始时间")]),a("v-uni-view",{staticClass:"test-time-number"},[t._v(t._s(e.start))])],1),a("v-uni-view",{staticClass:"test-time-flex"},[a("v-uni-view",{staticClass:"test-time-title"},[t._v("成绩")]),a("v-uni-view",{staticClass:"score"},[1===e.status?a("v-uni-text",{staticStyle:{color:"#41cc8e"}},[t._v(t._s(e.score))]):t._e(),1!==e.status?a("v-uni-text",[t._v("-")]):t._e(),t._v("/"+t._s(e.total_score))],1)],1)],1),e.total_score!==e.score?a("v-uni-view",{staticClass:"test-footer"},[a("v-uni-view",{staticClass:"atc-line"}),a("v-uni-view",{staticClass:"test-footer-flex"},[a("v-uni-view",{staticClass:"test-footer-text"},[1!==e.status?a("v-uni-view",[t._v("还剩"),a("v-uni-text",{staticStyle:{color:"#41cc8e"}},[t._v(t._s(e.total_num-e.done+1))]),t._v("道题")],1):t._e(),1===e.status?a("v-uni-view",[t._v("错"),a("v-uni-text",{staticStyle:{color:"#e80404"}},[t._v(t._s(e.incorrect))]),t._v("道题")],1):t._e(),a("v-uni-view")],1),a("v-uni-view",[1!==e.status?a("u-button",{attrs:{"custom-style":t.customStyle,size:"mini",type:"error"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.goTestResume(e)}}},[t._v("继续测试")]):a("u-button",{attrs:{"custom-style":t.customStyle,size:"mini"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.viewErrorQuestionList(e)}}},[t._v("查看错题")])],1)],1)],1):t._e()],1)})),1)],1):a("v-uni-view",{staticClass:"empty"},[a("v-uni-view",{staticClass:"box"},[a("img",{attrs:{src:"http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/empty.png",alt:""}}),a("v-uni-view",{staticClass:"empty-text"},[t._v("目前还没有任何学习记录哦")])],1)],1)],1)},o=[]},e58b:function(t,e,a){"use strict";var i=a("7079"),n=a.n(i);n.a},ea4f:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.container[data-v-626518c5]{padding-bottom:%?20?%;background-color:#f5f5f5}[data-v-626518c5] .u-back-wrap{z-index:1111}.slot-wrap[data-v-626518c5]{display:flex;align-items:center;\n  /* 如果您想让slot内容占满整个导航栏的宽度 */flex:1;\n  /* 如果您想让slot内容与导航栏左右有空隙 */padding:0 %?30?%}.favorites-header[data-v-626518c5]{z-index:1111;background-color:#f5f5f5;background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/learning-record.png);background-repeat:no-repeat;background-size:cover;position:fixed;top:40px;height:%?168?%;width:100%}.empty[data-v-626518c5]{height:100vh;display:flex;justify-content:center;align-items:center}.empty .box[data-v-626518c5]{display:flex;flex-direction:column;align-items:center;justify-content:center}.empty .empty-text[data-v-626518c5]{font-size:%?24?%;color:#999;line-height:%?34?%;margin-top:%?20?%;text-align:center;font-style:normal}.test-footer-flex[data-v-626518c5]{display:flex;justify-content:space-between;align-items:center}.test-footer-text[data-v-626518c5]{font-family:PingFangSC;font-size:%?24?%;letter-spacing:%?0.5?%;line-height:%?34?%;text-align:left;font-style:normal}.header-search[data-v-626518c5]{width:85%;margin:0 auto!important}.back-icon[data-v-626518c5]{margin-top:%?48?%;margin-left:%?14?%}[data-v-626518c5] .uni-scroll-view-content{padding-bottom:%?20?%}.favorites-list-item[data-v-626518c5]{min-height:100vh;padding-top:%?30?%;padding-left:%?32?%;padding-right:%?32?%}.favorites-list-item .list[data-v-626518c5]{background:#fff;margin-bottom:%?20?%;border-radius:%?10?%;padding:%?24?% %?20?% %?20?% %?20?%;min-height:%?142?%}.favorites-list-item .list .test-time .test-time-flex[data-v-626518c5]{display:flex;justify-content:space-between;align-items:center}.favorites-list-item .list .test-time .test-time-number[data-v-626518c5]{font-size:%?26?%;color:#333;line-height:%?36?%;text-align:left;font-style:normal}.favorites-list-item .list .test-time .test-time-title[data-v-626518c5]{font-size:%?26?%;margin-bottom:%?10?%;color:#666;line-height:%?36?%;text-align:left;font-style:normal}.favorites-list-item .list .test-title[data-v-626518c5]{font-weight:500;font-size:%?28?%;color:#333;line-height:%?40?%;text-align:left;font-style:normal}.favorites-list-item .list .atc-line[data-v-626518c5]{width:%?646?%;height:%?2?%;margin:%?20?% 0 %?16?% 0;background:#f5f5f5}.favorites-list-item .list .atc[data-v-626518c5]{display:flex}.favorites-list-item .list .atc .atc-list-item-img[data-v-626518c5]{margin-right:%?20?%;width:%?180?%;height:%?180?%;border-radius:%?6?%}.favorites-list-item .list .atc .atc-list-item-content[data-v-626518c5]{width:%?446?%}.favorites-list-item .list .atc .atc-list-item-content .atc-list-item-content-title[data-v-626518c5]{display:-webkit-box;-webkit-line-clamp:1;\n  /* 显示几行文本 */-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis;margin-bottom:%?20?%;font-weight:500;font-size:%?28?%;color:#333;line-height:%?40?%;text-align:left;font-style:normal}.favorites-list-item .list .atc .atc-list-item-content .atc-list-item-content-desc[data-v-626518c5]{display:-webkit-box;-webkit-line-clamp:3;\n  /* 显示几行文本 */-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis;font-size:%?26?%;color:#666;line-height:%?36?%;text-align:justify;font-style:normal}.favorites-list-item .list .list-footer[data-v-626518c5]{margin-top:%?28?%}.favorites-list-item .list .list-footer .time[data-v-626518c5],\n.favorites-list-item .list .list-footer .opr[data-v-626518c5]{font-size:%?24?%;color:#666;line-height:%?34?%;text-align:left;font-style:normal}.favorites-list-item .list .list-title[data-v-626518c5]{font-weight:500;font-size:%?28?%;color:#555;line-height:%?40?%;text-align:left}.tabs[data-v-626518c5]{display:flex;width:100%;justify-content:space-evenly;align-items:center}.tabs .tabs-title[data-v-626518c5]{font-size:%?36?%;color:#ff8383;line-height:%?50?%;text-align:center;font-style:normal}.active[data-v-626518c5]{color:#fff!important}',""]),t.exports=e},f074:function(t,e,a){"use strict";a.r(e);var i=a("7f1a"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},f2f9:function(t,e,a){"use strict";var i=a("a126"),n=a.n(i);n.a},fa94:function(t,e,a){"use strict";var i=a("062a"),n=a.n(i);n.a}}]);