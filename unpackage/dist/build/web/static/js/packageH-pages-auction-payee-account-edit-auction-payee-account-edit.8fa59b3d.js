(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-payee-account-edit-auction-payee-account-edit"],{"12c6":function(t,e,n){"use strict";n.r(e);var a=n("51bd"),i=n("f074");for(var c in i)["default"].indexOf(c)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(c);n("f2f9");var o=n("f0c5"),r=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);e["default"]=r.exports},3318:function(t,e,n){"use strict";n.r(e);var a=n("a6b8"),i=n.n(a);for(var c in a)["default"].indexOf(c)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(c);e["default"]=i.a},"4e18":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return c})),n.d(e,"a",(function(){return a}));var a={vhNavbar:n("12c6").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("vh-navbar",{attrs:{title:"绑定支付宝",height:"46"}}),n("v-uni-view",[n("v-uni-view",{staticClass:"h-20 bg-f5f5f5"}),n("v-uni-view",{staticClass:"ptb-28-plr-32"},[n("v-uni-view",{staticClass:"font-wei-600 font-24 text-6 l-h-34"},[t._v("请绑定本人支付宝账号")]),n("v-uni-view",{staticClass:"flex-sb-c h-108 bb-s-02-eeeeee"},[n("v-uni-text",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("真实姓名")]),n("v-uni-input",{staticClass:"w-400 font-32 text-3 text-right",attrs:{placeholder:"请输入本人姓名","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.name,callback:function(e){t.name=e},expression:"name"}})],1),n("v-uni-view",{staticClass:"flex-sb-c h-108 bb-s-02-eeeeee"},[n("v-uni-text",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("支付宝号")]),n("v-uni-input",{staticClass:"w-400 font-32 text-3 text-right",attrs:{type:"number",placeholder:"请输入支付宝绑定手机号","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.account,callback:function(e){t.account=e},expression:"account"}})],1),n("v-uni-view",{staticClass:"flex-sb-c h-108 bb-s-02-eeeeee"},[n("v-uni-text",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("验证码")]),n("v-uni-view",{staticClass:"flex-c-c"},[n("v-uni-input",{staticClass:"w-300 font-32 text-3 text-right",attrs:{type:"number",placeholder:"请输入","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.code,callback:function(e){t.code=e},expression:"code"}}),n("v-uni-button",{staticClass:"vh-btn flex-c-c ml-32 w-160 h-62 font-wei-500 font-24 bg-d8d8d8 b-rad-32",class:t.countDownInterval?"text-3":"text-ffffff",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSendCode.apply(void 0,arguments)}}},[t._v(t._s(t.btnText))])],1)],1)],1),n("v-uni-view",{staticClass:"p-fixed left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-********-022 z-9999"},[n("v-uni-button",{staticClass:"vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32",class:t.disabled?"bg-fce4e3":"bg-e80404",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSubmit.apply(void 0,arguments)}}},[t._v("提交绑定")])],1)],1)],1)},c=[]},"51bd":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return c})),n.d(e,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[n("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},c=[]},"7f1a":function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var c=n("26cb"),o=uni.getSystemInfoSync(),r={},u={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:r,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,c.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,n=e.pEAddressAdd,a=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(n)||t.includes(a)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=u},a126:function(t,e,n){var a=n("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},a6b8:function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("ac1f"),n("00b4");var i=a(n("f07e")),c=a(n("c964")),o=a(n("f3f3")),r=n("26cb"),u={name:"auctionPayeeAccountEdit",data:function(){return{name:"",account:"",code:"",btnText:"获取验证码",countDown:60,countDownInterval:null}},computed:(0,o.default)((0,o.default)((0,o.default)({},(0,r.mapState)(["routeTable"])),(0,r.mapState)("auctionPayeeAccount",["auctionPayeeAccount"])),{},{disabled:function(t){var e=t.name,n=t.account,a=t.code;return!(e&&n&&a)}}),methods:{onSubmit:function(){var t=this;return(0,c.default)((0,i.default)().mark((function e(){var n,a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.disabled){e.next=2;break}return e.abrupt("return");case 2:if(t.$u.test.mobile(t.account)){e.next=5;break}return t.feedback.toast({title:"请输入正确的支付宝号"}),e.abrupt("return");case 5:if(t.feedback.loading({title:"添加中..."}),n=t.auctionPayeeAccount.id,a={name:t.name,account:t.account,code:t.code},!n){e.next=14;break}return a.id=n,e.next=12,t.$u.api.updateAuctionPayeeAccount(a);case 12:e.next=16;break;case 14:return e.next=16,t.$u.api.createAuctionPayeeAccount(a);case 16:t.feedback.toast({title:"添加成功"}),uni.navigateBack();case 18:case"end":return e.stop()}}),e)})))()},onSendCode:function(){var t=this;return(0,c.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.countDown===t.$options.data().countDown){e.next=2;break}return e.abrupt("return");case 2:if(t.account){e.next=5;break}return t.feedback.toast({title:"请输入支付宝绑定手机号"}),e.abrupt("return");case 5:return n={telephone:t.account,randstr:t.$u.guid(),ticket:"vinehoo-lt-sms-ticket"},e.next=8,t.$u.api.sendSmsCode(n);case 8:t.startCountDown();case 9:case"end":return e.stop()}}),e)})))()},startCountDown:function(){var t=this;this.btnText="".concat(this.countDown,"s"),this.countDownInterval=setInterval((function(){if(t.countDown--,t.countDown>0)t.btnText="".concat(t.countDown,"s");else{var e=t.$options.data(),n=e.countDown,a=e.countDownInterval;t.btnText="重新获取",t.countDown=n,clearInterval(t.countDownInterval),t.countDownInterval=a}}),1e3)}},onLoad:function(){this.auctionPayeeAccount||this.jump.redirectTo(this.routeTable.pHAuctionPayeeAccount)},onUnload:function(){this.countDownInterval&&clearInterval(this.countDownInterval)}};e.default=u},bbdc:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},d4cb:function(t,e,n){"use strict";n.r(e);var a=n("4e18"),i=n("3318");for(var c in i)["default"].indexOf(c)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(c);var o=n("f0c5"),r=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"0e86162a",null,!1,a["a"],void 0);e["default"]=r.exports},f074:function(t,e,n){"use strict";n.r(e);var a=n("7f1a"),i=n.n(a);for(var c in a)["default"].indexOf(c)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(c);e["default"]=i.a},f2f9:function(t,e,n){"use strict";var a=n("a126"),i=n.n(a);i.a}}]);