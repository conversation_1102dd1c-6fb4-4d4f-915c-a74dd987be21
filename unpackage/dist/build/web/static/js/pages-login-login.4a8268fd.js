(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-login-login"],{"0443":function(e,t,n){"use strict";var a=n("1ccc"),i=n.n(a);i.a},"12c6":function(e,t,n){"use strict";n.r(t);var a=n("51bd"),i=n("f074");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("f2f9");var s=n("f0c5"),r=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);t["default"]=r.exports},"19cd":function(e,t,n){"use strict";n.r(t);var a=n("7298"),i=n("56fd");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("0443");var s=n("f0c5"),r=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"443530de",null,!1,a["a"],void 0);t["default"]=r.exports},"1ccc":function(e,t,n){var a=n("fbf9");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("e9fc368e",a,!0,{sourceMap:!1,shadowMode:!1})},"51bd":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":e.isFixed},e.navbarClass],style:[e.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":e.newYearTheme},style:[e.navbarInnerStyle]},[e.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}},[e.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:e.backIconColor,size:e.backIconSize}}):[n("u-icon",{attrs:{name:e.pageLength<=1?"home":e.backIconName,color:e.backIconColor,size:e.backIconSize}}),e.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[e.backTextStyle]},[e._v(e._s(e.backText))]):e._e()]],2):e._e(),e.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[e.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:e.titleColor,fontSize:e.titleSize+"rpx",fontWeight:e.titleBold?"bold":"normal"}},[e._v(e._s(e.title))])],1):e._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[e._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[e._t("right")],2)],1)],1),e.isFixed&&!e.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(e.navbarHeight)+(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}):e._e()],1)},o=[]},"56fd":function(e,t,n){"use strict";n.r(t);var a=n("95a3"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},7298:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return a}));var a={vhNavbar:n("12c6").default,uIcon:n("e5e1").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"content p-rela h-vh-100 o-hid"},[n("vh-navbar",{attrs:{title:"登录",background:{background:"#C8111B"},"back-icon-color":"#FFF","title-color":"#FFF"}},[e.pages.getPageLength()>1?n("v-uni-view",{staticClass:"d-flex a-center font-32 font-wei text-ffffff w-s-now",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.jump.reLaunch(e.routeTable.pgIndex)}}},[n("u-icon",{attrs:{name:"home",color:"#FFF",size:"44"}})],1):e._e()],1),n("v-uni-view",{staticClass:"d-flex j-center a-center mt-40"},[n("v-uni-image",{staticClass:"w-342 h-272",attrs:{src:"https://img.vinehoo.com/vinehoomini/v2/login/login_logo.png",mode:"aspectFill"}})],1),n("v-uni-view",{staticClass:"mt-132 ptb-00-plr-54"},[n("v-uni-view",{staticClass:"ptb-00-plr-14"},[n("v-uni-input",{staticClass:"bb-s-02-eeeeee h-116 font-44 text-ffffff font-wei-600",attrs:{type:"text",maxlength:11,placeholder:e.placeholder.telephone,"placeholder-style":e.placeholderStyle},model:{value:e.loginParams.telephone,callback:function(t){e.$set(e.loginParams,"telephone",t)},expression:"loginParams.telephone"}}),n("v-uni-view",{staticClass:"d-flex a-center j-sb mt-28"},[n("v-uni-input",{staticClass:"w-418 h-116 bb-s-02-eeeeee font-44 text-ffffff font-wei-600",attrs:{type:"text",maxlength:6,placeholder:e.placeholder.code,"placeholder-style":e.placeholderStyle},model:{value:e.loginParams.code,callback:function(t){e.$set(e.loginParams,"code",t)},expression:"loginParams.code"}}),n("v-uni-button",{staticClass:"w-190 h-62 m-0 p-0 bg-e58e93 b-rad-08 font-30 text-ffffff font-wei-500 l-h-62",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getCode.apply(void 0,arguments)}}},[e._v(e._s(e.btnText))])],1)],1),n("v-uni-button",{staticClass:"h-100 mt-72 bg-e58e93 b-rad-52 font-36 text-ffffff font-wei-500",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleLogin.apply(void 0,arguments)}}},[e._v("登录")])],1),e.sliderStatus?n("v-uni-view",{staticClass:"p-fixed w-p100 h-p100 top-0 z-9999"},[n("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:e.sliderUrl}})]):e._e()],1)},o=[]},"7f1a":function(e,t,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var o=n("26cb"),s=uni.getSystemInfoSync(),r={},l={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:r,statusBarHeight:s.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,o.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var e={};return e.height=this.navbarHeight+"px",e},navbarStyle:function(){var e={};return e.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(e,this.background),this.showBorder&&Object.assign(e,this.borderStyle),e},titleStyle:function(){var e={};return e.left=(s.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.right=(s.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.width=uni.upx2px(this.titleWidth)+"px",e},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var e=this.pages.getPageFullPath(),t=this.routeTable,n=t.pEAddressAdd,a=t.pEAddressManagement,i=t.pBOrderDepositDetail;(e.includes("/packageH")||e.includes(n)||e.includes(a)||e.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};t.default=l},"95a3":function(e,t,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("caad6"),n("2532"),n("99af");var i=a(n("f07e")),o=a(n("c964")),s=a(n("f3f3")),r=n("26cb"),l=n("1e48"),c={name:"login",data:function(){return{placeholderStyle:"font-size: 34rpx; color: white; font-weight: 400;",placeholder:{telephone:"请输入手机号",code:"请输入验证码",slider:"请完成滑块验证"},loginParams:{telephone:"",code:"",reg_from:4},btnText:"获取验证码",countDown:60,sliderUrl:"/html-statics/view/pcLogin.html",sliderStatus:!1,sliderRes:{randstr:"",ticket:""},refererUrl:""}},computed:(0,s.default)({},(0,r.mapState)(["routeTable"])),methods:{getCode:function(){this.countDown===this.$options.data().countDown&&(this.loginParams.telephone?this.sliderStatus=!0:this.feedback.toast({title:this.placeholder.telephone}))},handleMessage:function(e){var t;if("response"===(null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.act)){var n,a=(null===(n=e.data.msg)||void 0===n?void 0:n.answer)||{},i=a.status,o=void 0===i?"close":i,s=a.randstr,r=void 0===s?"":s,l=a.ticket,c=void 0===l?"":l;"success"===o&&(this.sliderRes={randstr:r,ticket:c},this.sendSmsCode()),this.sliderStatus=!1}},sendSmsCode:function(){var e=this.loginParams.telephone,t=this.sliderRes,n=t.randstr,a=t.ticket;if(e)if(n&&a){this.startCountDown();var i={telephone:e,randstr:n,ticket:a};this.$u.api.sendSmsCode(i)}else this.feedback.toast({title:this.placeholder.slider});else this.feedback.toast({title:this.placeholder.telephone})},startCountDown:function(){var e=this;this.countDownStatus=!0,this.btnText="".concat(this.countDown,"s"),this.countDownInterval=setInterval((function(){e.countDown--,e.countDown>0?e.btnText="".concat(e.countDown,"s"):(clearInterval(e.countDownInterval),e.btnText="重新获取",e.countDown=e.$options.data().countDown)}),1e3)},registerUidByTencentMoments:function(){var e=this;return(0,o.default)((0,i.default)().mark((function t(){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.param.isMomentsParam()){t.next=3;break}return t.next=3,e.$u.api.registerUidByTencentMoments();case 3:case"end":return t.stop()}}),t)})))()},handleLogin:function(){var e=this,t=this.loginParams,n=t.telephone,a=t.code;if(n)if(a){var i=this.pages.getPageLength(),o=!1;i>1&&(o=this.pages.getPrvePageFullPath().includes(l.AUCTION_PATH_PREFIX)),console.log("isFromAuctionPage",o);var s=this.loginParams;if(o){var r=l.AUCTION_SOURCE.platform,c=l.AUCTION_SOURCE.event;Object.assign(s,{vhPlatform:r,vhEvent:c})}this.$u.api.loginByCode(this.loginParams).then((function(t){var n;uni.setStorageSync("loginInfo",t.data),uni.removeStorageSync("newPeopleIndexMaskCountDown"),null!==t&&void 0!==t&&null!==(n=t.data)&&void 0!==n&&n.new_user&&e.registerUidByTencentMoments();var a=t.data,i=a.token,o=a.uid;if(document.cookie="h5token=".concat(i,"; path=/; domain=vinehoo.com; max-age=").concat(2592e3),document.cookie="h5uid=".concat(o,"; path=/; domain=vinehoo.com; max-age=").concat(2592e3),e.refererUrl)location.href=e.refererUrl;else{var s=e.pages.getPageLength();if(s<=1)uni.reLaunch({url:"/pages/index/index"});else{if("/pages/mine/mine"===e.pages.getPrvePageFullPath())return void uni.reLaunch({url:"/pages/index/index"});uni.navigateBack()}}}))}else this.feedback.toast({title:this.placeholder.code});else this.feedback.toast({title:this.placeholder.telephone})}},onLoad:function(e){this.refererUrl=e.refererUrl,window.addEventListener("message",this.handleMessage)},onUnload:function(){window.removeEventListener("message",this.handleMessage),this.countDownInterval&&clearInterval(this.countDownInterval)}};t.default=c},a126:function(e,t,n){var a=n("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},bbdc:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),e.exports=t},f074:function(e,t,n){"use strict";n.r(t);var a=n("7f1a"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},f2f9:function(e,t,n){"use strict";var a=n("a126"),i=n.n(a);i.a},fbf9:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,".content[data-v-443530de]{background-image:url(https://img.vinehoo.com/vinehoomini/v2/login/login_bg.png);background-size:cover}\n\n/* uni-page-body {\n\theight: 100%;\n} */",""]),e.exports=t}}]);