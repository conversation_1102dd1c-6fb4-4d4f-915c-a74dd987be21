(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-goods-create-auction-goods-create~packageH-pages-auction-goods-create-new-auc~710fa58f"],{"627a":function(t,e,n){"use strict";n.r(e);var s=n("b32f"),i=n("e84d");for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);n("711e");var o=n("f0c5"),l=Object(o["a"])(i["default"],s["b"],s["c"],!1,null,"05fb5206",null,!1,s["a"],void 0);e["default"]=l.exports},"6fec":function(t,e,n){var s=n("9fb6");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[t.i,s,""]]),s.locals&&(t.exports=s.locals);var i=n("4f06").default;i("5ae1f6d9",s,!0,{sourceMap:!1,shadowMode:!1})},"711e":function(t,e,n){"use strict";var s=n("6fec"),i=n.n(s);i.a},"9fb6":function(t,e,n){var s=n("24fb");e=s(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */',""]),t.exports=e},b32f:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return s}));var s={uLineProgress:n("f064").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.disabled?t._e():n("v-uni-view",{staticClass:"content"},[0===t.plate?n("v-uni-view",{},[t.lists.length?n("v-uni-view",{staticClass:"d-flex flex-wrap ml-n-20"},[t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-210 h-210 b-rad-10 mt-32 ml-16 o-hid"},[n("v-uni-image",{staticClass:"w-210 h-210",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})),t.lists.length<t.maxCount?n("v-uni-view",{staticClass:"w-210 h-210 bg-f8f8f8 d-flex flex-column j-center a-center b-rad-10 mt-32 ml-16",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-54 h-46",attrs:{src:t.osip+"/comm/cam_red.png"}}),n("v-uni-text",{staticClass:"mt-20 font-28 text-9"},[t._v("添加图片")])],1):t._e()],2):n("v-uni-view",{staticClass:"d-flex j-center mt-22"},[n("v-uni-view",{staticClass:"w-662 h-220 bg-f7f7f7 d-flex flex-column j-center a-center b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-54 h-46",attrs:{src:t.osip+"/comm/cam_red.png"}}),n("v-uni-text",{staticClass:"mt-20 font-24 text-6"},[t._v("添加图片（不超3张）")])],1)],1)],1):t._e(),1===t.plate?n("v-uni-view",{staticClass:"d-flex flex-wrap"},[t._l(t.lists,(function(e,s){return t.showUploadList?n("v-uni-view",{key:s,staticClass:"p-rela w-190 h-190 flex-column flex-c-c b-rad-06 mt-12 mr-12 o-hid",style:{width:t.$u.addUnit(t.width),height:t.$u.addUnit(t.height)}},[t.showProgress&&e.progress>0&&!e.error?n("u-line-progress",{staticClass:"vh-progress",attrs:{"show-percent":!1,height:"6","active-color":"#E80404",percent:e.progress}}):t._e(),e.error?n("v-uni-view",{staticClass:"p-abso bottom-0 left-0 right-0 z-09 bg-e80404 ptb-08-plr-00 text-center font-20 l-h-mul-10",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.retry(s)}}},[t._v("点击重试")]):t._e(),t.deletable?n("v-uni-image",{staticClass:"p-abso top-0 right-0 z-10 w-32 h-32 pb-24 pl-24",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/up_del.png",mode:"aspectFill"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}}):t._e(),n("v-uni-image",{staticClass:"d-block w-p100 h-p100 b-rad-10",attrs:{src:e.url||e.path,mode:t.imageMode},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}})],1):t._e()})),t.canUploadImage&&t.lists.length<t.maxCount?n("v-uni-view",{staticClass:"mt-12 mr-12",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile("image")}}},[t.customImageBtn?t._e():n("v-uni-view",{staticClass:"flex-column flex-c-c b-rad-06 b-d-01-999999",style:{width:t.$u.addUnit(t.width),height:t.$u.addUnit(t.height)}},[n("v-uni-image",{staticClass:"w-76 h-60",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/cam_red.png",mode:"aspectFill"}}),n("v-uni-view",{staticClass:"flex-column flex-c-c mt-20 font-24 text-9"},[n("v-uni-text",{},[t._v("上传凭证")]),n("v-uni-text",{},[t._v("（最多3张）")])],1)],1)],1):t._e()],2):t._e(),99===t.plate?n("v-uni-view",{},[t.lists.length?t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-328 h-228 b-rad-10 o-hid"},[n("v-uni-image",{staticClass:"w-328 h-228",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})):n("v-uni-view",{staticClass:"flex-c-c flex-column w-328 h-228 bg-f7f7f7 b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-72 h-60",attrs:{src:t.ossIcon("/comm/cam_red.png")}}),n("v-uni-text",{staticClass:"mt-24 font-24 text-6 l-h-34"},[t._v("上传人像页")])],1)],2):t._e(),98===t.plate?n("v-uni-view",{},[t.lists.length?t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-328 h-228 b-rad-10 o-hid"},[n("v-uni-image",{staticClass:"w-328 h-228",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})):n("v-uni-view",{staticClass:"flex-c-c flex-column w-328 h-228 bg-f7f7f7 b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-72 h-60",attrs:{src:t.ossIcon("/comm/cam_red.png")}}),n("v-uni-text",{staticClass:"mt-24 font-24 text-6 l-h-34"},[t._v("上传国徽页")])],1)],2):t._e(),97===t.plate?n("v-uni-view",{},[t.lists.length?t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-328 h-274 b-rad-10 o-hid"},[n("v-uni-image",{staticClass:"w-328 h-274",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})):n("v-uni-view",{staticClass:"flex-c-c flex-column w-328 h-274 bg-f7f7f7 b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-72 h-60",attrs:{src:t.ossIcon("/comm/cam_red.png")}}),n("v-uni-text",{staticClass:"mt-24 font-24 text-6 l-h-34"},[t._v("上传手持身份证照")])],1)],2):t._e(),96===t.plate?n("v-uni-view",{},[t.lists.length?t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-506 h-298 b-rad-10 o-hid"},[n("v-uni-image",{staticClass:"w-506 h-298",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})):n("v-uni-view",{staticClass:"flex-c-c flex-column w-506 h-298 bg-f7f7f7 b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-120 h-100",attrs:{src:t.ossIcon("/auction/camera_120_100.png")}}),n("v-uni-text",{staticClass:"mt-24 font-24 text-6 l-h-34"},[t._v("请确保图片清晰")])],1)],2):t._e(),95===t.plate?n("v-uni-view",{},[t.lists.length?t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-506 h-298 b-rad-10 o-hid"},[n("v-uni-image",{staticClass:"w-506 h-298",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})):n("v-uni-view",{staticClass:"flex-c-c flex-column w-506 h-298 bg-f7f7f7 b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-120 h-100",attrs:{src:t.ossIcon("/auction/camera_120_100.png")}}),n("v-uni-text",{staticClass:"mt-24 font-24 text-6 l-h-34"},[t._v("上传人像页")])],1)],2):t._e(),94===t.plate?n("v-uni-view",{},[t.lists.length?t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-506 h-298 b-rad-10 o-hid"},[n("v-uni-image",{staticClass:"w-506 h-298",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})):n("v-uni-view",{staticClass:"flex-c-c flex-column w-506 h-298 bg-f7f7f7 b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-120 h-100",attrs:{src:t.ossIcon("/auction/camera_120_100.png")}}),n("v-uni-text",{staticClass:"mt-24 font-24 text-6 l-h-34"},[t._v("上传国徽页")])],1)],2):t._e(),93===t.plate?n("v-uni-view",{},[t.lists.length?n("v-uni-view",{staticClass:"d-flex flex-wrap ml-n-15"},[t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela mt-16 ml-15 w-224 h-224 b-rad-10 o-hid"},[s?t._e():n("v-uni-image",{staticClass:"p-abso z-02 w-104 h-44",attrs:{src:t.ossIcon("/auction/cover_h_104_44.png")}}),n("v-uni-image",{staticClass:"w-224 h-224",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.ossIcon("/auction/del_32.png")},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})),t.lists.length<t.maxCount?n("v-uni-view",{staticClass:"flex-c-c mt-16 ml-15 w-224 h-224 bg-ffffff b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-68 h-56",attrs:{src:t.ossIcon("/auction/camera_68_56.png")}})],1):t._e()],2):n("v-uni-view",{staticClass:"p-rela mt-16 w-224 h-224 bg-ffffff b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"p-abso w-104 h-44",attrs:{src:t.ossIcon("/auction/cover_104_44.png")}}),n("v-uni-view",{staticClass:"flex-c-c h-p100"},[n("v-uni-image",{staticClass:"w-68 h-56",attrs:{src:t.ossIcon("/auction/camera_68_56.png")}})],1)],1)],1):t._e(),92===t.plate?n("v-uni-view",{},[t.lists.length?t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-336 h-228 b-rad-10 o-hid"},[n("v-uni-image",{staticClass:"w-336 h-228",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})):n("v-uni-view",{staticClass:"flex-c-c flex-column w-336 h-228 bg-f7f7f7 b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-72 h-60",attrs:{src:t.ossIcon("/auction/camera_72_60.png")}}),n("v-uni-text",{staticClass:"mt-24 font-24 text-6 l-h-34"},[t._v("鉴定证书")])],1)],2):t._e(),91===t.plate?n("v-uni-view",{},[t.lists.length?t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-336 h-228 b-rad-10 o-hid"},[n("v-uni-image",{staticClass:"w-336 h-228",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})):n("v-uni-view",{staticClass:"flex-c-c flex-column w-336 h-228 bg-f7f7f7 b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-72 h-60",attrs:{src:t.ossIcon("/auction/camera_72_60.png")}}),n("v-uni-text",{staticClass:"mt-24 font-24 text-6 l-h-34"},[t._v("报关单")])],1)],2):t._e(),90===t.plate?n("v-uni-view",{},[t.lists.length?t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-336 h-228 b-rad-10 o-hid"},[n("v-uni-image",{staticClass:"w-336 h-228",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})):n("v-uni-view",{staticClass:"flex-c-c flex-column w-336 h-228 bg-f7f7f7 b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-72 h-60",attrs:{src:t.ossIcon("/auction/camera_72_60.png")}}),n("v-uni-text",{staticClass:"mt-24 font-24 text-6 l-h-34"},[t._v("检验检疫证明")])],1)],2):t._e(),89===t.plate?n("v-uni-view",{},[t.lists.length?t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-336 h-228 b-rad-10 o-hid"},[n("v-uni-image",{staticClass:"w-336 h-228",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}})],1)})):n("v-uni-view",{staticClass:"flex-c-c flex-column w-336 h-228 bg-f7f7f7 b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-72 h-60",attrs:{src:t.ossIcon("/auction/camera_72_60.png")}}),n("v-uni-text",{staticClass:"mt-24 font-24 text-6 l-h-34"},[t._v("购买凭证")])],1)],2):t._e(),[88,87,86,85,84,83,82,81].includes(t.plate)?n("v-uni-view",[t.lists.length?t._l(t.lists,(function(e,s){return n("v-uni-view",{key:s,staticClass:"p-rela w-210 h-210 b-rad-10 o-hid"},[n("v-uni-image",{staticClass:"wh-p100",attrs:{src:e.url||e.path},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.doPreviewImage(e.url||e.path,s)}}}),[88,83].includes(t.plate)?n("v-uni-image",{staticClass:"p-abso top-0 left-0 w-62 h-34",attrs:{src:t.ossIcon("/auction/icon_cover_62_34.png")}}):t._e(),88!==t.plate?n("v-uni-image",{staticClass:"p-abso z-02 right-n-10 top-n-10 w-32 h-32 p-10",attrs:{src:t.osip+"/comm/up_del.png"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.deleteItem(s)}}}):t._e()],1)})):n("v-uni-view",{staticClass:"p-rela flex-c-c flex-column w-210 h-210 bg-f9f9f9 b-rad-10",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFile.apply(void 0,arguments)}}},[82===t.plate?n("v-uni-image",{staticClass:"w-40 h-40",attrs:{src:t.ossIcon("/auction/add_40.png")}}):n("v-uni-image",{staticClass:"w-56 h-52",attrs:{src:t.ossIcon("/auction/camera_56_52.png")}}),n("v-uni-text",{staticClass:"mt-14 font-24 text-6 l-h-34"},[88===t.plate?[t._v("封面图")]:87===t.plate?[t._v("正标")]:86===t.plate?[t._v("背标")]:85===t.plate?[t._v("瓶口")]:84===t.plate?[t._v("酒液位置")]:83===t.plate?[t._v("封面图必传")]:82===t.plate?[t._v("可上传更多")]:81===t.plate?[t._v("3/3")]:t._e()],2),[83].includes(t.plate)?n("v-uni-image",{staticClass:"p-abso top-0 left-0 w-62 h-34",attrs:{src:t.ossIcon("/auction/icon_cover_62_34.png")}}):t._e()],1)],2):t._e()],1)},a=[]},e276:function(t,e,n){"use strict";n("7a82");var s=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=s(n("f07e")),a=s(n("c964")),o=s(n("f3f3"));n("a9e3"),n("d81d"),n("d3b7"),n("14d9"),n("99af"),n("d401"),n("25f0"),n("baa5"),n("fb6a"),n("ac1f"),n("00b4"),n("caad6"),n("a434"),n("5319");var l=n("1e48"),c=n("26cb"),r={name:"vh-community-upload",props:{plate:{type:Number,default:0},showUploadList:{type:Boolean,default:!0},canUploadImage:{type:Boolean,default:!0},canUploadVideo:{type:Boolean,default:!1},directory:{type:String,default:""},maxCount:{type:[String,Number],default:52},showProgress:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},imageMode:{type:String,default:"aspectFill"},name:{type:String,default:"file"},sizeType:{type:Array,default:function(){return["original","compressed"]}},sourceType:{type:Array,default:function(){return["album","camera"]}},previewFullImage:{type:Boolean,default:!0},multiple:{type:Boolean,default:!0},deletable:{type:Boolean,default:!0},maxSize:{type:[String,Number],default:Number.MAX_VALUE},fileList:{type:Array,default:function(){return[]}},autoUpload:{type:Boolean,default:!0},showTips:{type:Boolean,default:!0},customImageBtn:{type:Boolean,default:!1},customVideoBtn:{type:Boolean,default:!1},width:{type:[String,Number],default:190},height:{type:[String,Number],default:190},delColor:{type:String,default:"#ffffff"},delIcon:{type:String,default:"close"},toJson:{type:Boolean,default:!0},beforeUpload:{type:Function,default:null},beforeRemove:{type:Function,default:null},limitType:{type:Array,default:function(){return["png","jpg","jpeg","webp","gif","image"]}},index:{type:[Number,String],default:""}},data:function(){return{afterSaleImageList:[],osip:l.OSIP,lists:[],uploadInfo:{},uploading:!1}},computed:(0,o.default)({},(0,c.mapState)(["ossPrefix"])),watch:{fileList:{immediate:!0,handler:function(t){var e=this;t.map((function(t){var n=e.lists.some((function(e){return e.url==t.url}));!n&&e.lists.push({url:t.url,error:!1,progress:100})}))}},lists:function(t){this.$emit("on-list-change",t,this.index)}},methods:{getOssUploadInfo:function(){var t=this;return(0,a.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.ossUpload({dir:t.directory});case 2:n=e.sent,t.uploadInfo=n.data;case 4:case"end":return e.stop()}}),e)})))()},clear:function(){this.lists=[]},reUpload:function(){this.uploadFile()},selectFile:function(){var t=this;if(88!==this.plate&&!this.disabled){this.plate,this.name;var e=this.maxCount,n=this.multiple,s=this.maxSize,i=this.sizeType,a=this.lists,o=(this.camera,this.compressed,this.maxDuration,this.sourceType),l=null,c=e-a.length;if(this.$android){window.uploadPictureInfo||(window.uploadPictureInfo=function(e){var n=e.map((function(e){var n="".concat(t.ossPrefix).concat(e.uploadPath);return{url:n,response:e.uploadPath}}));console.log("uploadPictureInfo",e,n),t.lists=t.lists.concat(n),t.$emit("on-choose-complete",t.lists,t.index)});var r={fromAppType:"3",max:"".concat(c),uploadDir:"vinehoo/client/wineComment/"};wineYunJsBridge.setDataFromApp(r)}else l=new Promise((function(t,e){uni.chooseImage({count:n?c>9?9:c:1,sourceType:o,sizeType:i,success:t,fail:e})})),l.then((function(i){var o=t.lists.length;i.tempFiles.map((function(i,o){if(t.checkFileExt(i)&&(n||!(o>=1)))if(i.size>s)t.$emit("on-oversize",i,t.lists,t.index),t.showToast("超出允许的文件大小");else{if(e<=a.length)return t.$emit("on-exceed",i,t.lists,t.index),void t.showToast("超出最大允许的文件个数");console.log("------------------进入了列表追加"),a.push({fileType:"image",url:i.path,progress:0,error:!1,file:i})}})),t.$emit("on-choose-complete",t.lists,t.index),t.autoUpload&&t.uploadFile(o)})).catch((function(e){t.$emit("on-choose-fail",e)}))}},showToast:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];(this.showTips||e)&&uni.showToast({title:t,icon:"none"})},upload:function(){this.$android?this.$emit("on-uploaded",this.lists):this.uploadFile()},retry:function(t){this.lists[t].progress=0,this.lists[t].error=!1,this.lists[t].response=null,uni.showLoading({title:"重新上传"}),this.uploadFile(t)},uploadFile:function(){var t=arguments,e=this;return(0,a.default)((0,i.default)().mark((function n(){var s,a,o,l,c,r,u,p;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return s=t.length>0&&void 0!==t[0]?t[0]:0,n.next=3,e.getOssUploadInfo();case 3:if(uni.showLoading({title:"上传中，请稍后",mask:!0}),!e.disabled){n.next=6;break}return n.abrupt("return");case 6:if(!e.uploading){n.next=8;break}return n.abrupt("return");case 8:if(!(s>=e.lists.length)){n.next=11;break}return e.$emit("on-uploaded",e.lists,e.index),n.abrupt("return");case 11:if(100!=e.lists[s].progress){n.next=14;break}return 0==e.autoUpload&&e.uploadFile(s+1),n.abrupt("return");case 14:if(!e.beforeUpload||"function"!==typeof e.beforeUpload){n.next=25;break}if(a=e.beforeUpload.bind(e.$u.$parent.call(e))(s,e.lists),!a||"function"!==typeof a.then){n.next=21;break}return n.next=19,a.then((function(t){})).catch((function(t){return e.uploadFile(s+1)}));case 19:n.next=25;break;case 21:if(!1!==a){n.next=25;break}return n.abrupt("return",e.uploadFile(s+1));case 25:if(e.directory&&e.uploadInfo.host){n.next=28;break}return e.showToast("请配置上传地址",!0),n.abrupt("return");case 28:e.lists[s].error=!1,e.uploading=!0,o=Math.random().toString(36).substr(2,4)+"_"+(new Date).getTime(),l=e.lists[s].file.name,c=l.lastIndexOf("."),r=l.slice(c+1),u={key:e.uploadInfo.dir+o+"."+r,policy:e.uploadInfo.policy,OSSAccessKeyId:e.uploadInfo.accessid,signature:e.uploadInfo.signature,success_action_status:200},console.log("-------------------我是构建的formData"),console.log(u),p=uni.uploadFile({url:e.uploadInfo.host,filePath:e.lists[s].url,name:e.name,formData:u,success:function(t){console.log(t);var n=e.toJson&&e.$u.test.jsonString(t.data)?JSON.parse(t.data):t.data;[200,201,204].includes(t.statusCode)?(console.log("----------------------------------我是获取上传列表信息"),console.log(e.lists[s]),"image"==e.lists[s].fileType?uni.getImageInfo({src:e.lists[s].url,success:function(t){console.log("----------------------------我是获取图片信息"),console.log(t);var i=t.width,a=t.height;e.lists[s].response="/".concat(e.uploadInfo.dir).concat(o,".").concat(r,"?w=").concat(i,"&h=").concat(a),e.lists[s].progress=100,e.lists[s].error=!1,e.$emit("on-success",n,s,e.lists,e.index),console.log("-------------------上传图片成功")},fail:function(t){console.log(t)}}):(e.lists[s].response="/"+e.uploadInfo.dir+o+"."+r,e.lists[s].progress=100,e.lists[s].error=!1,e.$emit("on-success",n,s,e.lists,e.index),console.log(t),console.log("-------------------上传非图片成功"))):e.uploadError(s,n)},fail:function(t){e.uploadError(s,t)},complete:function(t){e.uploading=!1,e.uploadFile(s+1),e.$emit("on-change",t,s,e.lists,e.index)}}),p.onProgressUpdate((function(t){t.progress>0&&(e.lists[s].progress=t.progress,e.$emit("on-progress",t,s,e.lists,e.index))}));case 39:case"end":return n.stop()}}),n)})))()},uploadError:function(t,e){this.lists[t].progress=0,this.lists[t].error=!0,this.lists[t].response=null,this.$emit("on-error",e,t,this.lists,this.index),console.log(t,e),this.showToast("上传失败，请重试")},deleteItem:function(t){var e=this;uni.showModal({title:"提示",content:"您确定要删除此项吗？",success:function(){var n=(0,a.default)((0,i.default)().mark((function n(s){var a;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!s.confirm){n.next=12;break}if(!e.beforeRemove||"function"!==typeof e.beforeRemove){n.next=11;break}if(a=e.beforeRemove.bind(e.$u.$parent.call(e))(t,e.lists),!a||"function"!==typeof a.then){n.next=8;break}return n.next=6,a.then((function(n){e.handlerDeleteItem(t)})).catch((function(t){e.showToast("已终止移除")}));case 6:n.next=9;break;case 8:!1===a?e.showToast("已终止移除"):e.handlerDeleteItem(t);case 9:n.next=12;break;case 11:e.handlerDeleteItem(t);case 12:case"end":return n.stop()}}),n)})));return function(t){return n.apply(this,arguments)}}()})},handlerDeleteItem:function(t){this.lists[t].process<100&&this.lists[t].process>0&&"undefined"!=typeof this.lists[t].uploadTask&&this.lists[t].uploadTask.abort(),this.lists.splice(t,1),this.$forceUpdate(),this.$emit("on-remove",t,this.lists,this.index),this.showToast("移除成功")},remove:function(t){t>=0&&t<this.lists.length&&(this.lists.splice(t,1),this.$emit("on-list-change",this.lists,this.index))},doPreviewImage:function(t,e){var n=this;if(this.previewFullImage){var s=this.lists.map((function(t){return t.url||t.path}));uni.previewImage({urls:s,current:t,success:function(){n.$emit("on-preview",t,n.lists,n.index)},fail:function(){uni.showToast({title:"预览图片失败",icon:"none"})}})}},doPreviewVideo:function(t){this.jump.navigateTo("/packageF/pages/full-screen-video/full-screen-video?videoLink=".concat(t.url))},checkFileExt:function(t){var e,n;return n=t.name.replace(/.+\./,"").toLowerCase(),e=this.limitType.some((function(t){return t.toLowerCase()===n})),e||this.showToast("不允许选择".concat(n,"格式的文件")),e}}};e.default=r},e84d:function(t,e,n){"use strict";n.r(e);var s=n("e276"),i=n.n(s);for(var a in s)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(a);e["default"]=i.a}}]);