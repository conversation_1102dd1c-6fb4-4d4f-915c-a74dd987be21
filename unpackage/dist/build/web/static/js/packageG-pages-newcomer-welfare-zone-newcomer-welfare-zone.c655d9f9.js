(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageG-pages-newcomer-welfare-zone-newcomer-welfare-zone"],{"0402":function(t,e,n){"use strict";n.r(e);var i=n("21e4"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},"0ea7":function(t,e,n){var i=n("35e0");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=n("4f06").default;o("395e99dd",i,!0,{sourceMap:!1,shadowMode:!1})},"10eb":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},n("d9e2"),n("d401")},"15d6":function(t,e,n){"use strict";var i=n("c767"),o=n.n(i);o.a},"21e4":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i=uni.getSystemInfoSync(),o={},a={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"44"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:o,statusBarHeight:i.statusBarHeight}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():uni.navigateBack()}}};e.default=a},"35e0":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.ngoods-list__title[data-v-0c96902c]{display:flex;justify-content:center;align-items:center}.ngoods-list__title uni-text[data-v-0c96902c]{padding:0 %?24?%;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:600;font-size:%?44?%;color:#fff5e6;line-height:%?60?%}.ngoods-list__title uni-image[data-v-0c96902c]{width:%?118?%;height:%?10?%}.ngoods-list__title uni-image[data-v-0c96902c]:first-of-type{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.ngoods-list__subtitle[data-v-0c96902c]{margin:%?4?% 0 0 0;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:400;font-size:%?24?%;color:#fff;line-height:%?34?%;text-align:center}.ngoods-list__content[data-v-0c96902c]{display:flex;justify-content:flex-start;align-items:center;flex-wrap:wrap;margin:%?18?% 0 0 0;padding:0 %?28?%}.ngoods-list__item[data-v-0c96902c]{margin-top:%?14?%;padding:%?12?%;width:%?340?%;height:%?442?%;background:#fff;border-radius:%?10?%}.ngoods-list__item[data-v-0c96902c]:nth-child(even){margin-left:%?14?%}.ngoods-list__ibanner[data-v-0c96902c]{width:%?316?%;height:%?196?%;border-radius:%?10?% %?10?% 0 0}.ngoods-list__icoupon[data-v-0c96902c]{position:relative;width:%?316?%;height:%?46?%}.ngoods-list__icoupon uni-image[data-v-0c96902c]{width:100%;height:100%}.ngoods-list__icoupon uni-view[data-v-0c96902c]{position:absolute;top:0;left:0;height:100%;display:flex;justify-content:center;align-items:center}.ngoods-list__icoupon uni-view uni-text[data-v-0c96902c]{width:%?122?%;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:500;font-size:%?24?%;color:#fff;text-align:center}.ngoods-list__icoupon uni-view uni-text[data-v-0c96902c]:last-of-type{width:%?202?%;font-weight:400;font-size:%?18?%}.ngoods-list__icoupon uni-view uni-text:last-of-type uni-text[data-v-0c96902c]{font-weight:500;font-size:%?32?%}.ngoods-list__icoupon uni-view uni-text:last-of-type uni-text uni-text[data-v-0c96902c]{font-size:%?18?%}.ngoods-list__ititle[data-v-0c96902c]{margin:%?20?% 0 0 0;height:%?60?%;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:500;font-size:%?24?%;color:#333;line-height:%?30?%;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.ngoods-list__ibrief[data-v-0c96902c]{font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:400;font-size:%?20?%;color:#999;line-height:%?34?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.ngoods-list__ifooter[data-v-0c96902c]{margin-top:%?10?%;display:flex;justify-content:space-between;align-items:center}.ngoods-list__ipcurr[data-v-0c96902c]{font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:500;font-size:%?32?%;color:#e80404}.ngoods-list__ipcurr uni-text[data-v-0c96902c]{font-size:%?18?%}.ngoods-list__iporigin[data-v-0c96902c]{margin-left:%?4?%;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:400;font-size:%?20?%;color:#999;text-decoration:line-through}.ngoods-list__ibtn[data-v-0c96902c]{display:flex;justify-content:center;align-items:center;margin:0;padding:0;width:%?92?%;height:%?32?%;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:400;font-size:%?20?%;color:#fff;background:#e80404;border-radius:%?16?%}',""]),t.exports=e},"36f7":function(t,e,n){"use strict";n.r(e);var i=n("95b6"),o=n("0402");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("b10b");var s=n("f0c5"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"2920cc37",null,!1,i["a"],void 0);e["default"]=r.exports},"388d":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uNavbar:n("36f7").default,newcomerGoodsList:n("440b").default},o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"newcomer"},[n("u-navbar",{attrs:{"back-icon-color":"#FFF",background:{background:"transparent"},"is-fixed":!1}}),n("v-uni-view",[n("v-uni-view",{staticClass:"newcomer__title"},[t._v("新人福利专区")]),n("v-uni-view",{staticClass:"newcomer__subtitle"},[t._v("- "+t._s(t.totalPrice)+"元超值大礼包 -")]),n("v-uni-view",{staticClass:"newcomer__ticket",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumpLoginPage.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"newcomer__tbg"}),n("v-uni-view",{staticClass:"newcomer__tcontent"},[n("v-uni-scroll-view",{staticClass:"newcomer__tcview",attrs:{"scroll-x":!0}},t._l(t.couponList,(function(e,i){return n("v-uni-view",{key:i,staticClass:"newcomer__tcitem"},[e?n("v-uni-view",{staticClass:"newcomer__tcicontent"},[n("v-uni-text",{staticClass:"newcomer__tciprice"},[n("v-uni-text",[t._v("¥")]),t._v(t._s(e.coupon_face_value))],1),n("v-uni-text",{staticClass:"newcomer__tcitext"},[t._v("无门槛优惠券")])],1):t._e()],1)})),1),t.received?n("v-uni-view",{staticClass:"newcomer__tctext",class:t.clazz},[t.countdownStr?[t._v("距失效"),n("v-uni-text",[t._v(t._s(t.countdownStr))])]:[t._v("已失效")]],2):n("v-uni-view",{staticClass:"newcomer__tctext",class:t.clazz},[t._v("新人专享超值礼包，每人限领一次")])],1),n("v-uni-image",{staticClass:"newcomer__icon",class:t.clazz,attrs:{src:t.receiveIcon}})],1)],1),n("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showNotice,expression:"showNotice"}],staticClass:"newcomer__notice"},[t.received?[t._v("已领取"),n("v-uni-text",[t._v(t._s(t.totalPrice)+"元")]),t._v("新人礼包，"),t.countdownStr?[t._v("距失效"),n("v-uni-text",[t._v(t._s(t.countdownStr))])]:[t._v("已失效")]]:[t._v("您有"),n("v-uni-text",[t._v(t._s(t.totalPrice)+"元")]),t._v("新人礼包，点击领取 >>")]],2),n("newcomer-goods-list",{attrs:{list:t.firstList,from:t.from}}),n("newcomer-goods-list",{attrs:{type:2,list:t.secondList,from:t.from}}),n("newcomer-goods-list",{attrs:{type:3,list:t.thirdList,from:t.from}})],1)},a=[]},4053:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(n("b680"))},"429a":function(t,e,n){"use strict";n.r(e);var i=n("388d"),o=n("5467");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("15d6");var s=n("f0c5"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"180b0592",null,!1,i["a"],void 0);e["default"]=r.exports},"440b":function(t,e,n){"use strict";n.r(e);var i=n("8c5b"),o=n("f775");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("9159");var s=n("f0c5"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"0c96902c",null,!1,i["a"],void 0);e["default"]=r.exports},5467:function(t,e,n){"use strict";n.r(e);var i=n("e171"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},"59df":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-navbar[data-v-2920cc37]{width:100%}.u-navbar-fixed[data-v-2920cc37]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-2920cc37]{width:100%}.u-navbar-inner[data-v-2920cc37]{display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-2920cc37]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-2920cc37]{flex:1}.u-title[data-v-2920cc37]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center}',""]),t.exports=e},6473:function(t,e,n){"use strict";n.r(e);var i=n("db26"),o=n("b69e");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);var s=n("f0c5"),r=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,"ce683c6a",null,!1,i["a"],void 0);e["default"]=r.exports},"6bdb":function(t,e,n){var i=n("59df");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=n("4f06").default;o("3dc5b1f9",i,!0,{sourceMap:!1,shadowMode:!1})},"713d":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n("f07e")),a=i(n("c964"));n("a9e3"),n("caad6"),n("2532"),n("4ec9"),n("d3b7"),n("3ca3"),n("ddb0");var s=i(n("e75f")),r={name:"vh-channel-title-icon",props:{is_seckill:{type:[Number],default:0},channel:{type:[String,Number],default:0},marketingAttribute:{type:String,default:"0"},warehouseType:{type:[String,Number],default:"0"},showTitle:{type:Boolean,default:!1},borderRadius:{type:[String,Number],default:6},marginLeft:{type:[String,Number],default:0},padding:{type:String,default:"0 10rpx"},fontSize:{type:[String,Number],default:24},fontBold:{type:Boolean,default:!0},isNewYearTheme:{type:Boolean,default:!1},textColor:{type:String,default:"#FFFFFF"},plate:{type:String,default:""}},computed:{getChannel:function(){return this.marketingAttribute.includes("1")?101:9==this.channel?1==this.warehouseType?102:2==this.warehouseType?103:1:this.channel},iconName:function(){var t=!0;this.$android?t=(0,s.default)("9.1.8"):this.$ios&&(t=(0,s.default)("9.24"));var e=new Map([[0,{title:this.is_seckill?"秒杀":"闪购",iconText:this.is_seckill?"秒杀":"闪购",bgColor:this.is_seckill?"#FDE451":"#E80404",textColor:this.is_seckill?"#E80404":"#FFF"}],[1,{title:this.isNewYear?"年货节":"现货速发",iconText:this.isNewYear?"年货节":"现货速发",bgColor:"#FF9127",textColor:"#FFF"}],[2,{title:"跨境",iconText:"跨境",bgColor:"#734cd2",textColor:"#FFF"}],[3,{title:"尾货",iconText:"尾货",bgColor:"#FF9127",textColor:"#FFF"}],[4,{title:"兔头",iconText:"兔头",bgColor:"#FF9127",textColor:"#FFF"}],[11,{title:"拍卖",iconText:"拍卖",bgColor:"#F6B869",textColor:"#FFF"}],[9,{title:this.isNewYear?"年货节":"现货速发",iconText:this.isNewYear?"年货节":"现货速发",bgColor:"#FF9127",textColor:"#FFF"}],[101,{title:"拼团",iconText:"拼团",bgColor:"#FF9127",textColor:"#FFF"}],[102,{title:9===this.channel?this.isNewYear?"年货节":"现货速发":"闪购",iconText:"3小时达",bgColor:"#17E6A1",textColor:"#fff"}],[103,{title:9===this.channel?this.isNewYear?"年货节":"现货速发":"闪购",iconText:t?"次日达":"本地仓",bgColor:"#FAB005",textColor:"#fff"}]]);return e},iconStyle:function(){var t={};console.log("909988---",this.getChannel,this.iconName.get(this.getChannel));var e=this.iconName.get(this.getChannel),n=e.bgColor,i=e.textColor;return t.backgroundColor=n,t.borderRadius=this.borderRadius+"rpx",t.marginLeft=this.marginLeft+"rpx",t.padding=this.padding,t.fontSize=this.fontSize+"rpx",this.fontBold&&(t.fontWeight="bold"),t.color=i,1==this.warehouseType&&9==this.channel&&(t.color="#000",t.fontWeight="bold"),t}},mounted:function(){this.isNewYearTheme||this.secondConfig()},data:function(){return{isNewYear:!1}},methods:{secondConfig:function(){var t=this;return(0,a.default)((0,o.default)().mark((function e(){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.secondConfig();case 2:n=e.sent,n.data.isopen&&(t.isNewYear=!0);case 4:case"end":return e.stop()}}),e)})))()}}};e.default=r},"740c":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.newcomer[data-v-180b0592]{padding:0 0 %?60?% 0;background-image:url(/static/newcomer/bg.png);background-size:contain}.newcomer__title[data-v-180b0592]{background:linear-gradient(180deg,#fff,#ffcd90);-webkit-background-clip:text;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:600;font-size:%?68?%;color:transparent;line-height:%?96?%;text-align:center}.newcomer__subtitle[data-v-180b0592]{font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:400;font-size:%?28?%;color:#ffd8ab;line-height:%?40?%;text-align:center}.newcomer__ticket[data-v-180b0592]{position:relative;margin:%?32?% 0 0 %?52?%;width:%?662?%}.newcomer__tbg[data-v-180b0592]{margin:0 0 0 %?46?%;width:%?582?%;height:%?204?%;background:#e67a36;border-radius:%?20?%}.newcomer__tcontent[data-v-180b0592]{position:absolute;top:%?8?%;width:%?628?%;height:%?186?%}.newcomer__tcview[data-v-180b0592]{white-space:nowrap;box-shadow:0 %?2?% %?2?% %?2?% rgba(184,98,21,.43)}.newcomer__tcitem[data-v-180b0592]{display:inline-block;vertical-align:top;width:%?160?%;height:%?186?%;background-image:url(/static/newcomer/card.png);background-size:%?160?% %?186?%}.newcomer__tcitem[data-v-180b0592]:first-of-type{width:%?164?%;background-image:url(/static/newcomer/card1.png);background-size:%?164?% %?186?%}.newcomer__tcitem:first-of-type .newcomer__tcicontent[data-v-180b0592]{margin-left:%?6?%;width:%?154?%}.newcomer__tcicontent[data-v-180b0592]{position:relative;margin-top:%?6?%;width:%?164?%;height:%?118?%;display:flex;justify-content:flex-start;align-items:center;flex-direction:column}.newcomer__tciprice[data-v-180b0592]{margin-top:%?10?%;background:linear-gradient(214deg,#ff6161,#e70000);-webkit-background-clip:text;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:600;font-size:%?58?%;color:transparent;line-height:%?82?%}.newcomer__tciprice uni-text[data-v-180b0592]{margin-right:%?4?%;font-size:%?20?%;line-height:%?28?%}.newcomer__tcitext[data-v-180b0592]{position:absolute;bottom:%?10?%;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:400;font-size:%?16?%;color:#c87b47;line-height:22px}.newcomer__tctext[data-v-180b0592]{position:absolute;left:0;bottom:%?6?%;display:flex;justify-content:center;align-items:center;width:%?496?%;height:%?62?%;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:400;font-size:%?24?%;color:#c1721e;line-height:34px}.newcomer__tctext.is-receive[data-v-180b0592]{width:%?494?%}.newcomer__tctext uni-text[data-v-180b0592]{font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:500;font-size:%?32?%;color:#e80404;line-height:44px}.newcomer__icon[data-v-180b0592]{position:absolute;right:0;top:0;width:%?166?%;height:%?232?%}.newcomer__icon.is-receive[data-v-180b0592]{top:%?-4?%;width:%?168?%;height:%?236?%}.newcomer__notice[data-v-180b0592]{position:fixed;top:0;z-index:999;display:flex;justify-content:center;align-items:center;width:100%;height:%?80?%;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:400;font-size:%?28?%;color:#a54504;line-height:%?40?%;background:#ffebd7;box-shadow:0 %?2?% %?4?% 0 rgba(98,0,0,.18)}.newcomer__notice uni-text[data-v-180b0592]{color:#e80404}.newcomer .ngoods-list[data-v-180b0592]{margin-top:%?60?%}',""]),t.exports=e},"8c5b":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={vhChannelTitleIcon:n("6473").default},o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.list&&t.list.length?n("v-uni-view",{staticClass:"ngoods-list"},[n("v-uni-view",{staticClass:"ngoods-list__title"},[n("v-uni-image",{attrs:{src:t.ossIcon("/newborn_zone/new_zone_line.png")}}),n("v-uni-text",[t._v(t._s(t.mapperItem.title))]),n("v-uni-image",{attrs:{src:t.ossIcon("/newborn_zone/new_zone_line.png")}})],1),n("v-uni-view",{staticClass:"ngoods-list__subtitle"},[t._v(t._s(t.mapperItem.subtitle))]),n("v-uni-view",{staticClass:"ngoods-list__content"},t._l(t.list,(function(e,i){return n("v-uni-view",{key:i,staticClass:"ngoods-list__item",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.handleJump(t.routeTable.pgGoodsDetail+"?id="+e.period)}}},[n("v-uni-image",{staticClass:"ngoods-list__ibanner",attrs:{src:e.custom_banner_img}}),n("v-uni-view",{staticClass:"ngoods-list__icoupon"},[n("v-uni-image",{attrs:{src:t.ossIcon("/comm/new_zone_pri_bg.png")}}),n("v-uni-view",[n("v-uni-text",[t._v(t._s(t.mapperItem.couponTips))]),1===t.type?n("v-uni-text",[t._v("下单立返¥"+t._s(e.custom_newcomer_price)+"元")]):2===t.type?n("v-uni-text",[t._v("限购1份")]):3===t.type?n("v-uni-text",[t._v("领券低至"),n("v-uni-text",[n("v-uni-text",[t._v("¥")]),t._v(t._s(e.custom_newcomer_price))],1)],1):t._e()],1)],1),n("v-uni-view",{staticClass:"ngoods-list__ititle"},[n("vh-channel-title-icon",{attrs:{channel:e.periods_type}}),n("v-uni-text",[t._v(t._s(e.title))])],1),n("v-uni-view",{staticClass:"ngoods-list__ibrief"},[t._v(t._s(e.brief))]),n("v-uni-view",{staticClass:"ngoods-list__ifooter"},[n("v-uni-view",{staticClass:"ngoods-list__iprice"},[1===t.type?n("v-uni-text",{staticClass:"ngoods-list__ipcurr"},[t._v("0元购")]):2===t.type?n("v-uni-text",{staticClass:"ngoods-list__ipcurr"},[n("v-uni-text",[t._v("¥")]),t._v(t._s(e.custom_newcomer_price))],1):3===t.type?n("v-uni-text",{staticClass:"ngoods-list__ipcurr"},[n("v-uni-text",[t._v("¥")]),t._v(t._s(e.price))],1):t._e(),n("v-uni-text",{staticClass:"ngoods-list__iporigin"},[t._v(t._s(3===t.type?"¥"+e.market_price:"原价"+e.market_price))])],1),n("v-uni-button",{staticClass:"ngoods-list__ibtn"},[t._v("立即抢")])],1)],1)})),1)],1):t._e()},a=[]},9159:function(t,e,n){"use strict";var i=n("0ea7"),o=n.n(i);o.a},"919e":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n("f3f3"));n("a9e3");var a=n("26cb"),s={1:{title:"新人0元购",subtitle:"所有商品下单全额立返",couponTips:"新人专享"},2:{title:"新人专享特价",subtitle:"爆款低价 超值精选",couponTips:"新人特价"},3:{title:"福利专区",subtitle:"新人专享底价 放心购",couponTips:"新人特惠"}},r={name:"newcomer-goods-list",props:{type:{type:Number,default:1},list:{type:Array,default:function(){return[]}},from:{type:String,default:""}},data:function(){return{title:"新人0元购",subtitle:"所有商品下单全额立返"}},computed:(0,o.default)((0,o.default)({},(0,a.mapState)(["routeTable"])),{},{mapperItem:function(t){var e=t.type;return s[e]||s[1]}}),methods:{handleJump:function(t){this.jump.appAndMiniJump(1,t,this.from)}}};e.default=r},"95b6":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uIcon:n("e5e1").default},o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),n("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-icon-wrap"},[n("u-icon",{attrs:{name:t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?n("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?n("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},a=[]},a9e0:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},n("a4d3"),n("e01a"),n("d3b7"),n("d28b"),n("3ca3"),n("ddb0"),n("a630")},b10b:function(t,e,n){"use strict";var i=n("6bdb"),o=n.n(i);o.a},b69e:function(t,e,n){"use strict";n.r(e);var i=n("713d"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},c767:function(t,e,n){var i=n("740c");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=n("4f06").default;o("f86583f2",i,!0,{sourceMap:!1,shadowMode:!1})},cb29:function(t,e,n){"use strict";var i=n("23e7"),o=n("81d5"),a=n("44d2");i({target:"Array",proto:!0},{fill:o}),a("fill")},d0ff:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,o.default)(t)||(0,a.default)(t)||(0,s.default)()};var i=r(n("4053")),o=r(n("a9e0")),a=r(n("dde1")),s=r(n("10eb"));function r(t){return t&&t.__esModule?t:{default:t}}},db26:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return""!==t.getChannel&&"orderConfirm"==t.plate?n("v-uni-view",{staticClass:"flex-s-c"},[t.showTitle?n("v-uni-view",{staticClass:"font-32 font-wei text-3 l-h-40"},[t._v(t._s(t.iconName.get(t.getChannel).title)+"商品")]):t._e(),n("v-uni-view",{staticClass:"mt-04",style:[t.iconStyle]},[t._v(t._s(t.iconName.get(t.getChannel).iconText))])],1):""!==t.getChannel?n("v-uni-text",{},[t.showTitle?n("v-uni-text",{staticClass:"font-32 font-wei text-3 l-h-44"},[t._v(t._s(t.iconName.get(t.getChannel).title)+"商品")]):t._e(),n("v-uni-text",{style:[t.iconStyle]},[t._v(t._s(t.iconName.get(t.getChannel).iconText))])],1):t._e()},o=[]},e171:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n("d0ff"));n("4de4"),n("d3b7"),n("3ca3"),n("ddb0"),n("cb29"),n("99af"),n("159b"),n("caad6"),n("ac1f");var a={name:"newcomer-welfare-zone",data:function(){return{received:!1,targetScrollTop:0,showNotice:!1,timer:null,countdownStr:"",countdownSeconds:0,totalPrice:"",couponList:[],list:[],from:""}},computed:{clazz:function(t){var e=t.received;return{"is-receive":e}},receiveIcon:function(t){var e=t.received;return e?"/static/newcomer/icon_get1.png":"https://images.vinehoo.com/vinehoomini/v3/newborn_zone/new_zone_get.png"},firstList:function(t){var e=t.list;return e.filter((function(t){return 1===t.module}))},secondList:function(t){var e=t.list;return e.filter((function(t){return 2===t.module}))},thirdList:function(t){var e=t.list;return e.filter((function(t){return 3===t.module}))}},methods:{load:function(){var t=this;Promise.all([this.$u.api.getCouponDetail({id:1}),this.$u.api.getNewcomerGoodsList()]).then((function(e){var n,i;console.log("res",e);var a=e[0].data,s=a.coupon_package_value,r=void 0===s?"":s,c=a.package_details,l=void 0===c?[]:c;t.totalPrice=r;var u=l.length,d=new Array(u<4?4-u:1).fill("");t.couponList=[].concat((0,o.default)(l),(0,o.default)(d));var f=(null===(n=e[1])||void 0===n||null===(i=n.data)||void 0===i?void 0:i.list)||[];f.forEach((function(t){var e=t.banner_img,n=void 0===e?"":e,i=t.newcomer_price,o=void 0===i?"":i;t.custom_banner_img=n[0];var a=JSON.parse(o||"{}");t.custom_newcomer_price=a[0].price})),t.list=f}))},statrCountdown:function(){var t=this;this.formatCountdownStr(this.countdownSeconds),this.timer=setInterval((function(){t.countdownSeconds<=0?(t.clearTimer(),t.countdownStr=""):(t.countdownSeconds--,t.formatCountdownStr(t.countdownSeconds))}),1e3)},formatCountdownStr:function(t){var e=0,n=0,i=0,o=0;e=Math.floor(t/86400),n=Math.floor(t/3600)-24*e,i=Math.floor(t/60)-60*n-24*e*60,o=Math.floor(t)-24*e*60*60-60*n*60-60*i,e=e<10?"0"+e:e,n=n<10?"0"+n:n,i=i<10?"0"+i:i,o=o<10?"0"+o:o,this.countdownStr="".concat(e,"天").concat(n,":").concat(i,":").concat(o)},clearTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)},jumpLoginPage:function(){this.received||(["1","2"].includes(this.from)?wineYunJsBridge.openAppPage({client_path:{ios_path:"login",android_path:"login"}}):this.jump.navigateTo("/pages/login/login"))}},onReady:function(){var t=this,e=uni.createSelectorQuery().in(this);e.select(".newcomer__ticket").boundingClientRect((function(e){var n=e.height,i=e.top;t.targetScrollTop=n+i})).exec()},onLoad:function(t){t.from&&(this.from=t.from)},onShow:function(){var t=uni.getStorageSync("loginInfo")||{},e=t.uid,n=void 0===e?"":e,i=t.token,o=void 0===i?"":i,a=t.created_time,s=void 0===a?"":a;if(n&&o){this.received=!0;var r=new Date(s).getTime(),c=r+6048e5,l=Date.now();l<c&&(this.countdownSeconds=Math.ceil((c-l)/1e3),this.statrCountdown())}this.load()},onPageScroll:function(t){this.showNotice=t.scrollTop>this.targetScrollTop},beforeDestroy:function(){this.clearTimer()}};e.default=a},e75f:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d81d");var o=i(n("e143"));e.default=function(t){var e,n=(null===(e=o.default.prototype)||void 0===e?void 0:e.$vhVersion)||"",i=function(t){return t.split(".").map((function(t){return+t}))},a=i(n),s=a.length,r=i(t),c=r.length;if(console.log(a,r),s>c)return!0;if(s<c)return!1;var l=0;while(l<s){if(a[l]>r[l])return!0;if(a[l]<r[l])return!1;l++}return!1}},f775:function(t,e,n){"use strict";n.r(e);var i=n("919e"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a}}]);