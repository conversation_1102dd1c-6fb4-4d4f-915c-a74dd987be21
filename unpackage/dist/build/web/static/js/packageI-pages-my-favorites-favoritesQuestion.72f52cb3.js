(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageI-pages-my-favorites-favoritesQuestion"],{"0936":function(t,e,n){"use strict";n.r(e);var a=n("c076"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"76b0":function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.answer[data-v-06404dd6]{padding:%?32?%}.error-question-list-box[data-v-06404dd6]{min-height:100vh;background-color:#f5f5f5}.error-question-list-box .error-list-item[data-v-06404dd6]{margin-top:%?160?%;padding:%?32?%}.error-question-list-box .error-list-item .item-list[data-v-06404dd6]{margin-bottom:%?20?%;padding:%?24?% %?20?%;background-color:#fff;border-radius:%?10?%}.error-question-list-box .error-list-item .item-list .item-list-title[data-v-06404dd6]{font-weight:500;font-size:%?28?%;color:#555;line-height:%?40?%;text-align:left;font-style:normal}.footer[data-v-06404dd6]{margin-top:%?30?%;display:flex;justify-content:space-between}.view-details[data-v-06404dd6]{color:#666;font-size:%?24?%;line-height:%?34?%}.answer-bottom[data-v-06404dd6]{display:flex;justify-content:flex-start;align-items:center}.answer-bottom .success-answer-title[data-v-06404dd6]{color:#666;font-size:%?24?%;line-height:%?34?%}.answer-bottom .success-answer-option[data-v-06404dd6]{font-size:%?24?%;font-weight:700;margin:0 %?16?% 0 %?4?%;color:#41cc8e}.answer-bottom .error-answer-option[data-v-06404dd6]{font-size:%?24?%;font-weight:700;margin:0 %?16?% 0 %?4?%;color:#e80404}.header[data-v-06404dd6]{position:fixed;background-color:#f5f5f5;top:0;width:100%;left:0}.header .title[data-v-06404dd6]{position:absolute;top:%?90?%;left:40.3%;font-weight:600;font-size:%?32?%;color:#fff;line-height:%?44?%;text-align:center;font-style:normal;z-index:111}.header .icon[data-v-06404dd6]{position:absolute;top:%?48?%;left:%?14?%;z-index:111}.header img[data-v-06404dd6]{width:100%}',""]),t.exports=e},8483:function(t,e,n){"use strict";n.r(e);var a=n("bd57"),i=n("0936");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("a33a");var r=n("f0c5"),s=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"06404dd6",null,!1,a["a"],void 0);e["default"]=s.exports},a33a:function(t,e,n){"use strict";var a=n("c853"),i=n.n(a);i.a},bd57:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return a}));var a={vhNavbar:n("12c6").default,uIcon:n("e5e1").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"error-question-list-box"},[n("v-uni-view",[t.$appStatusBarHeight?n("v-uni-view",[n("v-uni-view",{staticClass:"p-fixed z-980 top-0 w-p100",style:{background:"#D30808"}},[n("v-uni-view",{style:{height:t.$appStatusBarHeight+"px"}}),n("v-uni-view",{staticClass:"p-rela h-px-48 d-flex j-center a-center"},[n("v-uni-view",{staticClass:"p-abso left-24 h-p100 d-flex a-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.navigateBack()}}},[n("u-icon",{attrs:{name:"nav-back",color:"#fff",size:44}})],1),n("v-uni-view",{staticClass:"font-36 font-wei",staticStyle:{color:"#fff"}},[t._v("试题详情")])],1)],1)],1):n("vh-navbar",{attrs:{"back-icon-color":"#fff",title:"试题详情","title-color":"#fff",background:t.background}})],1),1==t.info.category_id?n("answerItem",{ref:"answerItem",staticClass:"answer",style:{paddingTop:t.$appStatusBarHeight?parseInt(t.$appStatusBarHeight)+58+"px":"10px"},attrs:{displayAnalysis:!0,hiddenIndex:2,info:t.info}}):t._e()],1)},o=[]},c076:function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("1349")),o={components:{answerItem:i.default},data:function(){return{info:{},appStatusBarHeight:0,background:{backgroundColor:"#D30808"}}},onLoad:function(t){var e=this;uni.getSystemInfo({success:function(t){e.appStatusBarHeight=t.statusBarHeight?t.statusBarHeight:48}});var n=decodeURIComponent(t.info),a=JSON.parse(n);this.info=a},methods:{back:function(){uni.navigateBack()}}};e.default=o},c853:function(t,e,n){var a=n("76b0");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("5c8bb6b8",a,!0,{sourceMap:!1,shadowMode:!1})}}]);