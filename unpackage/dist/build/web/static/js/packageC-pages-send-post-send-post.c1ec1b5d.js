(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageC-pages-send-post-send-post"],{"062a":function(e,t,o){var n=o("aab3");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=o("4f06").default;a("2db15654",n,!0,{sourceMap:!1,shadowMode:!1})},"09bf":function(e,t,o){"use strict";o.r(t);var n=o("eb4f"),a=o.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(i);t["default"]=a.a},"3e7f":function(e,t,o){"use strict";o.d(t,"b",(function(){return a})),o.d(t,"c",(function(){return i})),o.d(t,"a",(function(){return n}));var n={vhNavbar:o("12c6").default,uIcon:o("e5e1").default,vhCommunityUpload:o("60ac").default,uButton:o("4f1b").default},a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("v-uni-view",{staticClass:"content"},[o("v-uni-view",{staticClass:"bb-s-01-eeeeee"},[o("vh-navbar",{attrs:{"back-icon-color":"#333",title:"发帖","title-color":"#333"}})],1),o("v-uni-view",{staticClass:"p-24"},[o("v-uni-view",{},[o("v-uni-textarea",{staticClass:"w-654 h-320 bg-f7f7f7 b-rad-08 p-24 font-28 text-3",attrs:{maxlength:-1,placeholder:"来吧，尽情发挥吧…","placeholder-style":"color:#999;font-size:28rpx;"},model:{value:e.postCont,callback:function(t){e.postCont=t},expression:"postCont"}})],1),o("v-uni-view",{staticClass:"bg-f7f7f7 d-flex flex-wrap a-center b-rad-08 mt-20 p-24"},[e._l(e.selectedTopics,(function(t,n){return o("v-uni-text",{key:n,staticClass:"bg-e2ebfa text-2e7bff b-rad-26 mr-24 mb-24 ptb-08-plr-20 font-24 font-wei l-h-40",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.removeTopic(n)}}},[e._v("#"+e._s(t.title)+"#")])})),o("v-uni-view",{staticClass:"w-148 h-50 d-flex j-center a-center b-rad-26 b-s-01-e7e7e7 mb-24"},[o("v-uni-text",{staticClass:"font-24 font-wei text-3",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.jump.loginNavigateTo(e.routeTable.PCTopicSelectMore)}}},[e._v("#话题")]),o("u-icon",{attrs:{name:"arrow-right",size:20,color:"#999"}})],1)],2),o("v-uni-view",{staticClass:"mt-20"},[o("vh-community-upload",{ref:"uUpload",attrs:{directory:"vinehoo/client/community/","auto-upload":!1,"max-count":9,width:222,height:222,"border-radius":6,"background-color":"#f7f7f7",icon:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/cam_red.png",width:76,height:60},text:{content:"添加图片",style:"margin-top: 22rpx; font-size: 28rpx; color: #999; line-height: 40rpx;"}},on:{"on-list-change":function(t){arguments[0]=t=e.$handleEvent(t),e.onListChange.apply(void 0,arguments)},"on-uploaded":function(t){arguments[0]=t=e.$handleEvent(t),e.onUploaded.apply(void 0,arguments)}}})],1),o("v-uni-view",{staticClass:"mt-100"},[e.hasAgreedBefore?e._e():o("v-uni-view",{staticClass:"j-center bg-ffffff d-flex a-center"},[o("v-uni-image",{staticClass:"w-26 h-26",attrs:{src:e.isRead?"https://images.vinehoo.com/vinehoomini/v3/comm/cir_sel.png":"https://images.vinehoo.com/vinehoomini/v3/comm/cir_unsel.png",mode:"widthFix"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.isRead=!e.isRead}}}),o("v-uni-text",{staticClass:"ml-10 font-24 text-9",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.isRead=!e.isRead}}},[e._v("阅读并接受")]),o("v-uni-text",{staticClass:"font-24 text-2b8cf7",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goToContentRules.apply(void 0,arguments)}}},[e._v("《酒云网内容发布规则》")])],1),o("v-uni-view",{staticClass:"bg-ffffff cer-btn-con d-flex j-center mt-24 pb-62"},[e.canPublish?o("v-uni-view",[o("u-button",{attrs:{shape:"circle",ripple:!0,"ripple-bg-color":"#ffffff","custom-style":{width:"646rpx",fontSize:"28rpx",color:"#fff",backgroundColor:"#E80404",border:"none"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handlePublish.apply(void 0,arguments)}}},[e._v("发布")])],1):o("v-uni-view",{staticClass:"bg-dddddd w-646 h-80 d-flex j-center a-center font-28 text-ffffff font-wei-500 b-rad-40"},[e._v("发布")])],1)],1)],1)],1)},i=[]},"4f1b":function(e,t,o){"use strict";o.r(t);var n=o("825d"),a=o("8e1d");for(var i in a)["default"].indexOf(i)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(i);o("fa94");var r=o("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"4ed92bb2",null,!1,n["a"],void 0);t["default"]=s.exports},"825d":function(e,t,o){"use strict";o.d(t,"b",(function(){return n})),o.d(t,"c",(function(){return a})),o.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+e.size,e.plain?"u-btn--"+e.type+"--plain":"",e.loading?"u-loading":"","circle"==e.shape?"u-round-circle":"",e.hairLine?e.showHairLineBorder:"u-btn--bold-border","u-btn--"+e.type,e.disabled?"u-btn--"+e.type+"--disabled":""],style:[e.customStyle,{overflow:e.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(e.hoverStartTime),"hover-stay-time":Number(e.hoverStayTime),disabled:e.disabled,"form-type":e.formType,"open-type":e.openType,"app-parameter":e.appParameter,"hover-stop-propagation":e.hoverStopPropagation,"send-message-title":e.sendMessageTitle,"send-message-path":"sendMessagePath",lang:e.lang,"data-name":e.dataName,"session-from":e.sessionFrom,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"hover-class":e.getHoverClass,loading:e.loading},on:{getphonenumber:function(t){arguments[0]=t=e.$handleEvent(t),e.getphonenumber.apply(void 0,arguments)},getuserinfo:function(t){arguments[0]=t=e.$handleEvent(t),e.getuserinfo.apply(void 0,arguments)},error:function(t){arguments[0]=t=e.$handleEvent(t),e.error.apply(void 0,arguments)},opensetting:function(t){arguments[0]=t=e.$handleEvent(t),e.opensetting.apply(void 0,arguments)},launchapp:function(t){arguments[0]=t=e.$handleEvent(t),e.launchapp.apply(void 0,arguments)},click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.click(t)}}},[e._t("default"),e.ripple?o("v-uni-view",{staticClass:"u-wave-ripple",class:[e.waveActive?"u-wave-active":""],style:{top:e.rippleTop+"px",left:e.rippleLeft+"px",width:e.fields.targetWidth+"px",height:e.fields.targetWidth+"px","background-color":e.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):e._e()],2)},a=[]},"8e1d":function(e,t,o){"use strict";o.r(t);var n=o("9476"),a=o.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){o.d(t,e,(function(){return n[e]}))}(i);t["default"]=a.a},9476:function(e,t,o){"use strict";o("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,o("a9e3"),o("c975"),o("d3b7"),o("ac1f");var n={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var e;return e=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",e},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(e){var t=this;this.$u.throttle((function(){!0!==t.loading&&!0!==t.disabled&&(t.ripple&&(t.waveActive=!1,t.$nextTick((function(){this.getWaveQuery(e)}))),t.$emit("click",e))}),this.throttleTime)},getWaveQuery:function(e){var t=this;this.getElQuery().then((function(o){var n=o[0];if(n.width&&n.width&&(n.targetWidth=n.height>n.width?n.height:n.width,n.targetWidth)){t.fields=n;var a,i;a=e.touches[0].clientX,i=e.touches[0].clientY,t.rippleTop=i-n.top-n.targetWidth/2,t.rippleLeft=a-n.left-n.targetWidth/2,t.$nextTick((function(){t.waveActive=!0}))}}))},getElQuery:function(){var e=this;return new Promise((function(t){var o="";o=uni.createSelectorQuery().in(e),o.select(".u-btn").boundingClientRect(),o.exec((function(e){t(e)}))}))},getphonenumber:function(e){this.$emit("getphonenumber",e)},getuserinfo:function(e){this.$emit("getuserinfo",e)},error:function(e){this.$emit("error",e)},opensetting:function(e){this.$emit("opensetting",e)},launchapp:function(e){this.$emit("launchapp",e)}}};t.default=n},aab3:function(e,t,o){var n=o("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),e.exports=t},ae60:function(e,t,o){"use strict";o.r(t);var n=o("3e7f"),a=o("09bf");for(var i in a)["default"].indexOf(i)<0&&function(e){o.d(t,e,(function(){return a[e]}))}(i);var r=o("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"6eef2260",null,!1,n["a"],void 0);t["default"]=s.exports},eb4f:function(e,t,o){"use strict";o("7a82");var n=o("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,o("ac1f"),o("5319"),o("498a"),o("d81d"),o("a434");var a=n(o("f07e")),i=n(o("c964")),r=n(o("f3f3")),s=o("26cb"),d={name:"send-post",computed:(0,r.default)((0,r.default)({},(0,s.mapState)(["routeTable"])),{},{canPublish:function(){var e=this.isRead||this.hasAgreedBefore,t=this.postCont.trim().replace(/[\r\n]/g,"").length>0;return e&&t}}),data:function(){return{postCont:"",isRead:!1,hasAgreedBefore:!1,selectedTopics:[],uploadFileList:[],uploadImageStr:"",isEdit:!1,postId:""}},onLoad:function(e){try{var t=uni.getStorageSync("post_agreement_accepted");if(t&&(this.isRead=!0,this.hasAgreedBefore=!0),e.postData){var o=JSON.parse(decodeURIComponent(e.postData));if(this.isEdit=!0,this.postId=o.id,this.postCont=o.content,o.topics&&o.topics.length&&(this.selectedTopics=o.topics.map((function(e){return{id:e.id,title:e.title}}))),o.type_data){var n=o.type_data.split(",");this.uploadImageStr=o.type_data,this.uploadFileList=n.map((function(e){return{url:e,status:"success",response:e}}))}}else if(e.topicData){var a=JSON.parse(decodeURIComponent(e.topicData));this.selectedTopics=[{id:a.id,title:a.title}]}}catch(i){console.error("获取协议同意状态或话题数据失败:",i)}},onShow:function(){var e=getCurrentPages(),t=e[e.length-1];t.$vm.selectedTopics&&(this.selectedTopics=t.$vm.selectedTopics)},methods:{onListChange:function(e){console.log("上传列表发生改变"),this.uploadFileList=e},onUploaded:function(e){console.log("上传所有文件成功"),this.feedback.toast({title:"所有图片上传成功~"}),this.uploadImageStr=e.map((function(e){var t=e.response;return t})).join(),this.createPost()},validateContent:function(){var e=this.postCont.trim().replace(/[\r\n]/g,"");return!!e||(this.feedback.toast({title:"请输入帖子内容"}),!1)},handlePublish:function(){if(this.validateContent()){if(this.isRead)try{uni.setStorageSync("post_agreement_accepted",!0)}catch(e){console.error("保存协议同意状态失败:",e)}this.uploadFileList.length?this.$refs.uUpload.upload():this.createPost()}},createPost:function(){var e=this;return(0,i.default)((0,a.default)().mark((function t(){var o,n,i;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.validateContent()){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,o={content:e.postCont.trim().replace(/[\r\n]+/g,"\n"),topicid:e.selectedTopics.length?e.selectedTopics.map((function(e){return e.id})).join(","):"",is_best:0,istop:0,type:0,type_data:e.uploadImageStr||""},n={},e.isEdit&&(n={topicid:o.topicid,type_data:o.type_data,content:o.content,refuse_pid:e.postId}),t.next=8,e.$u.api.createPost(e.isEdit?n:o);case 8:i=t.sent,0===i.error_code?(e.feedback.toast({title:e.isEdit?"更新成功":"发布成功"}),e.selectedTopics=[],e.postCont="",e.uploadFileList=[],e.uploadImageStr="",setTimeout((function(){e.jump.jumpPrePage(e.$vhFrom)}),500)):e.feedback.toast({title:i.error_msg||(e.isEdit?"更新失败":"发布失败")}),t.next=16;break;case 12:t.prev=12,t.t0=t["catch"](2),console.error(e.isEdit?"更新帖子失败:":"发布帖子失败:",t.t0),e.feedback.toast({title:(e.isEdit?"更新":"发布")+"失败,请稍后重试"});case 16:case"end":return t.stop()}}),t,null,[[2,12]])})))()},goToContentRules:function(){var e="".concat(this.$store.state.agreementPrefix,"/ContentPubRules");this.jump.jumpH5Agreement(e,this.$vhFrom)},removeTopic:function(e){this.selectedTopics.splice(e,1)}}};t.default=d},fa94:function(e,t,o){"use strict";var n=o("062a"),a=o.n(n);a.a}}]);