(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-rabbit-head-order-detail-rabbit-head-order-detail"],{"0402":function(t,e,i){"use strict";i.r(e);var n=i("21e4"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"062a":function(t,e,i){var n=i("aab3");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("2db15654",n,!0,{sourceMap:!1,shadowMode:!1})},"1e62":function(t,e,i){"use strict";i.r(e);var n=i("5d3e"),a=i("1ee6");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"2a9ac49b",null,!1,n["a"],void 0);e["default"]=s.exports},"1ee6":function(t,e,i){"use strict";i.r(e);var n=i("4cd4"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"21e4":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n=uni.getSystemInfoSync(),a={},r={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"44"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:a,statusBarHeight:n.statusBarHeight}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(n.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(n.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():uni.navigateBack()}}};e.default=r},"36f7":function(t,e,i){"use strict";i.r(e);var n=i("95b6"),a=i("0402");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("b10b");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"2920cc37",null,!1,n["a"],void 0);e["default"]=s.exports},"4cd4":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f07e")),r=n(i("c964")),o=n(i("f3f3")),s=i("26cb"),l={name:"rabbit-head-order-detail",data:function(){return{loading:!0,navBackgroundColor:"rgba(224, 20, 31, 0)",orderNo:"",rabbitOrderInfo:{},logisticInfo:{}}},computed:(0,o.default)({},(0,s.mapState)(["routeTable","logisticsInfo"])),onLoad:function(t){this.orderNo=t.orderNo,this.init()},methods:(0,o.default)((0,o.default)({},(0,s.mapMutations)(["muLogisticsInfo"])),{},{init:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getOrderDetail();case 2:return e.next=4,t.getLogisticsDetail();case 4:t.loading=!1;case 5:case"end":return e.stop()}}),e)})))()},getOrderDetail:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.orderDetail({order_no:t.orderNo});case 2:i=e.sent,t.rabbitOrderInfo=i.data;case 4:case"end":return e.stop()}}),e)})))()},getLogisticsDetail:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i,n,r,o;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=t.rabbitOrderInfo,i.order_no,n=i.express_type,r=i.express_number,!r){e.next=6;break}return e.next=4,t.$u.api.logisticsDetails({logisticCode:r,expressType:n});case 4:o=e.sent,t.logisticInfo=o.data;case 6:case"end":return e.stop()}}),e)})))()},viewLogistics:function(){var t=this.rabbitOrderInfo,e=t.goodsInfo,i=t.express_type,n=t.express_number;this.muLogisticsInfo({image:e[0].goods_img,expressType:i,logisticCode:n}),this.jump.navigateTo("/packageB/pages/logistics-detail/logistics-detail")},deleteOrder:function(){var t=this;this.feedback.showModal({confirm:function(){var e=(0,r.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$u.api.cancelDeleteOrder({type:2,order_no:t.rabbitOrderInfo.order_no});case 3:t.feedback.toast({title:"删除成功",icon:"success"}),setTimeout((function(){t.jump.navigateBack()}),1500),e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](0);case 9:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}()})},confirmReceipt:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log("======================我是确认收货"),i={},i.sub_order_no_str=t.rabbitOrderInfo.order_no,console.log(i),e.next=6,t.$u.api.orderReceipt(i);case 6:e.sent,t.feedback.toast({title:"确认成功",icon:"success"}),t.getOrderDetail(t.orderNo);case 9:case"end":return e.stop()}}),e)})))()}}),onPageScroll:function(t){t.scrollTop<=100?this.navBackgroundColor="rgba(224, 20, 31, ".concat(t.scrollTop/100,")"):this.navBackgroundColor="rgba(224, 20, 31, 1)"}};e.default=l},"4f1b":function(t,e,i){"use strict";i.r(e);var n=i("825d"),a=i("8e1d");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);i("fa94");var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"4ed92bb2",null,!1,n["a"],void 0);e["default"]=s.exports},5047:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"vh-split-line",props:{paddingTop:{type:[String,Number],default:40},paddingBottom:{type:[String,Number],default:40},marginLeft:{type:[String,Number],default:40},marginRight:{type:[String,Number],default:40},text:{type:String,default:"已浏览"},fontSize:{type:[String,Number],default:28},fontBold:{type:Boolean,default:!1},textColor:{type:String,default:"#666666"},isTran:{type:Boolean,default:!1},lineWidth:{type:[String,Number],default:200},lineHeight:{type:[String,Number],default:10},lineColor:{type:String,default:"#E0E0E0"},showImage:{type:Boolean,default:!1},imageSrc:{type:String,default:""},imageWidth:{type:[String,Number],default:36},imageHeight:{type:[String,Number],default:38}},data:function(){return{}},computed:{upDownStyle:function(){return{paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},lineStyle:function(){var t={};return t.width=this.lineWidth+"rpx",t.height=this.lineHeight+"rpx",this.isTran&&(t.transform="scaleY(0.5)"),t.backgroundColor=this.lineColor,t},imageStyle:function(){return{width:this.imageWidth+"rpx",height:this.imageHeight+"rpx"}},textStyle:function(){var t={};return t.marginLeft=this.marginLeft+"rpx",t.marginRight=this.marginRight+"rpx",this.fontBold&&(t.fontWeight="bold"),t.fontSize=this.fontSize+"rpx",t.color=this.textColor,t}}};e.default=n},"55e7":function(t,e,i){"use strict";i.r(e);var n=i("5047"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"59df":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-navbar[data-v-2920cc37]{width:100%}.u-navbar-fixed[data-v-2920cc37]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-2920cc37]{width:100%}.u-navbar-inner[data-v-2920cc37]{display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-2920cc37]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-2920cc37]{flex:1}.u-title[data-v-2920cc37]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center}',""]),t.exports=e},"5d3e":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uNavbar:i("36f7").default,uIcon:i("e5e1").default,vhImage:i("ce7c").default,vhChannelTitleIcon:i("6473").default,vhSplitLine:i("cbda").default,vhGoodsRecommendList:i("6d37").default,uButton:i("4f1b").default,vhSkeleton:i("591b").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"content",class:t.loading?"h-vh-100 o-hid":""},[t.loading?i("v-uni-view",{staticClass:"fade-in"},[i("u-navbar",{attrs:{"back-icon-color":"#FFF",title:"订单详情","title-size":"36","title-bold":!0,"title-color":"#FFF",background:{background:"#E80404"}}}),i("vh-skeleton",{attrs:{type:3,"loading-color":"#E80404"}})],1):i("v-uni-view",{staticClass:"fade-in bg-f5f5f5 pb-104"},[i("u-navbar",{attrs:{"back-icon-color":"#FFF",title:"订单详情","title-bold":!0,"title-color":"#FFF",background:{background:t.navBackgroundColor}}}),i("v-uni-image",{staticClass:"p-abso top-0 w-p100 h-400",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/order_detail/ban.png",mode:"widthFix"}}),i("v-uni-view",{staticClass:"p-rela z-1 mt-n-20 ml-48 mr-48"},[1==t.rabbitOrderInfo.status?i("v-uni-view",{staticClass:"d-flex j-sb a-center"},[i("v-uni-view",{},[i("v-uni-view",{staticClass:"font-36 font-wei text-ffffff"},[t._v("待发货")]),i("v-uni-view",{staticClass:"mt-08 font-28 text-ffffff"},[t._v("买家已付款，等待商家发货")])],1),i("v-uni-image",{staticClass:"w-128 h-128",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_be_ship.png",mode:"aspectFill"}})],1):t._e(),2==t.rabbitOrderInfo.status?i("v-uni-view",{staticClass:"d-flex j-sb a-center"},[i("v-uni-view",{},[i("v-uni-view",{staticClass:"font-36 font-wei text-ffffff"},[t._v("待收货")]),i("v-uni-view",{staticClass:"mt-08 font-28 text-ffffff"},[t._v("自动收货时间："+t._s(t.rabbitOrderInfo.receipt_time))])],1),i("v-uni-image",{staticClass:"w-128 h-128",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_be_rec.png",mode:"aspectFill"}})],1):t._e(),3==t.rabbitOrderInfo.status?i("v-uni-view",{staticClass:"d-flex j-sb a-center"},[i("v-uni-view",{},[i("v-uni-view",{staticClass:"font-36 font-wei text-ffffff"},[t._v("已完成")]),i("v-uni-view",{staticClass:"mt-08 font-28 text-ffffff"},[t._v("订单已完成，感谢您对酒云网的支持！")])],1),i("v-uni-image",{staticClass:"w-128 h-128",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/order_detail/ord_comp.png",mode:"aspectFill"}})],1):t._e()],1),i("v-uni-view",{staticClass:"mt-24 ml-24 mr-24"},[t.logisticInfo.traces&&t.logisticInfo.traces.length?i("v-uni-view",{staticClass:"p-rela z-01 bg-ffffff b-rad-10 pr-24 pl-24",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.viewLogistics()}}},[i("v-uni-view",{staticClass:"d-flex j-sb a-center bb-s-01-eeeeee pt-32 pb-32"},[i("v-uni-view",{staticClass:"d-flex"},[i("v-uni-image",{staticClass:"w-44 h-44 mt-04",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/order_detail/rec_bla.png",mode:"widthFix"}}),i("v-uni-view",{staticClass:"w-540 ml-16"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3 text-hidden-3"},[t._v(t._s(t.logisticInfo.traces[0].context))]),i("v-uni-view",{staticClass:"mt-12 ml-10 font-24 text-9 l-h-34"},[t._v(t._s(t.logisticInfo.traces[0].ftime))])],1)],1),i("u-icon",{attrs:{name:"arrow-right",size:24,color:"#333"}})],1)],1):t._e(),i("v-uni-view",{staticClass:"p-rela z-01 bg-ffffff b-rad-10 d-flex j-sb a-center pt-32 pr-24 pb-32 pl-24"},[i("v-uni-view",{staticClass:"d-flex"},[i("v-uni-image",{staticClass:"w-44 h-44 mt-04",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/order_detail/add_bla.png",mode:"widthFix"}}),i("v-uni-view",{staticClass:"w-540 ml-16"},[i("v-uni-view",{},[i("v-uni-text",{staticClass:"mr-36 font-32 font-wei text-3"},[t._v(t._s(t.rabbitOrderInfo.consignee))]),i("v-uni-text",{staticClass:"font-28 text-3"},[t._v(t._s(t.rabbitOrderInfo.consignee_phone))])],1),i("v-uni-view",{staticClass:"mt-12 font-24 text-9 l-h-34"},[t._v(t._s(t.rabbitOrderInfo.province_name)+" "+t._s(t.rabbitOrderInfo.city_name)+" "+t._s(t.rabbitOrderInfo.district_name)+" "+t._s(t.rabbitOrderInfo.address))])],1)],1)],1)],1),i("v-uni-view",{staticClass:"p-rela z-01"},t._l(t.rabbitOrderInfo.goodsInfo,(function(e,n){return i("v-uni-view",{key:n,staticClass:"bg-ffffff b-rad-10 mt-20 ml-24 mr-24 pt-32 pr-20 pb-32 pl-28",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.jump.redirectTo(t.routeTable.pBRabbitHeadGoodsDetail+"?id="+e.period)}}},[i("v-uni-view",{staticClass:"d-flex j-sb"},[i("vh-image",{attrs:{src:e.goods_img,width:246,height:152,"border-radius":10}}),i("v-uni-view",{staticClass:"flex-1 d-flex flex-column j-sb ml-12"},[i("v-uni-view",{},[i("v-uni-view",{staticClass:"text-hidden-2"},[i("vh-channel-title-icon",{attrs:{channel:e.periods_type,padding:"2rpx 8rpx","font-size":20}}),i("v-uni-text",{staticClass:"ml-12 font-24 text-0 l-h-34"},[t._v(t._s(e.goods_title))])],1),i("v-uni-view",{staticClass:"mt-08"},[i("v-uni-text",{staticClass:"bg-f5f5f5 ptb-02-plr-12 b-rad-04 font-20 text-9"},[t._v(t._s(e.package_name))])],1)],1),i("v-uni-view",{},[i("v-uni-view",{staticClass:"mt-04 d-flex j-sb"},[i("v-uni-view",{staticClass:"font-22 text-6"},[t._v("x"+t._s(e.order_qty))]),i("v-uni-view",{staticClass:"d-flex a-center"},[i("v-uni-text",{staticClass:"font-28 font-wei text-e80404"},[t._v(t._s(e.package_price))]),i("v-uni-image",{staticClass:"w-28 h-28 ml-04",attrs:{src:"http://images.vinehoo.com/vinehoomini/v3/comm/rab_gold.png",mode:"aspectFill"}})],1)],1)],1)],1)],1)],1)})),1),i("v-uni-view",{staticClass:"bg-ffffff b-rad-10 mt-20 ml-24 mr-24"},[i("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"},[i("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("订单编号")]),i("v-uni-text",{staticClass:"font-24 text-3",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.copy.copyText(t.rabbitOrderInfo.order_no)}}},[t._v(t._s(t.rabbitOrderInfo.order_no))])],1),i("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"},[i("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("配送方式")]),i("v-uni-text",{staticClass:"font-24 text-3"},[t._v("默认配送")])],1),i("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"},[i("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("支付方式")]),i("v-uni-text",{staticClass:"font-24 text-3"},[t._v("兔头兑换")])],1),i("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32"},[i("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("预计发货时间")]),i("v-uni-text",{staticClass:"font-24 text-3"},[t._v(t._s(t.rabbitOrderInfo.predict_time)+"前")])],1),i("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"},[i("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("下单时间")]),i("v-uni-text",{staticClass:"font-24 text-3"},[t._v(t._s(t.rabbitOrderInfo.created_time))])],1)],1),i("v-uni-view",{staticClass:"bg-ffffff b-rad-10 mt-20 ml-24 mr-24"},[i("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"},[i("v-uni-view",{staticClass:"font-28 font-wei text-3"},[t._v("商品金额")]),i("v-uni-view",{},[i("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v(t._s(t.rabbitOrderInfo.payment_amount))]),i("v-uni-image",{staticClass:"ml-06 w-28 h-28",attrs:{src:"http://images.vinehoo.com/vinehoomini/v3/comm/rab_gold.png",mode:"aspectFill"}})],1)],1),i("v-uni-view",{staticClass:"d-flex j-end a-center ml-24 mr-24 pt-32 pb-32"},[i("v-uni-text",{staticClass:"font-28 text-3"},[t._v("共1件")]),i("v-uni-text",{staticClass:"ml-10 font-28 font-wei text-3"},[t._v("实付款：")]),i("v-uni-text",{staticClass:"font-32 font-wei text-e80404"},[t._v(t._s(t.rabbitOrderInfo.payment_amount))]),i("v-uni-image",{staticClass:"ml-06 w-28 h-28",attrs:{src:"http://images.vinehoo.com/vinehoomini/v3/comm/rab_gold.png",mode:"aspectFill"}})],1)],1),i("v-uni-view",{},[i("vh-split-line",{attrs:{"padding-top":52,"padding-bottom":32,"margin-left":10,"margin-right":10,text:"猜你喜欢","font-bold":!0,"font-size":36,"text-color":"#333333","show-image":!0,"image-src":"https://images.vinehoo.com/vinehoomini/v3/comm/guess_love.png"}}),i("vh-goods-recommend-list")],1),2==t.rabbitOrderInfo.status||3==t.rabbitOrderInfo.status?i("v-uni-view",{staticClass:"p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022"},[2==t.rabbitOrderInfo.status?i("v-uni-view",{staticClass:"h-104 d-flex j-center a-center"},[i("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmReceipt.apply(void 0,arguments)}}},[t._v("确认收货")])],1):t._e(),3==t.rabbitOrderInfo.status?i("v-uni-view",{staticClass:" h-104 d-flex j-end a-center pr-24"},[i("v-uni-view",{},[i("u-button",{attrs:{shape:"circle",ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#999",border:"2rpx solid #999"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteOrder.apply(void 0,arguments)}}},[t._v("删除订单")])],1)],1):t._e()],1):t._e()],1)],1)},r=[]},6473:function(t,e,i){"use strict";i.r(e);var n=i("db26"),a=i("b69e");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"ce683c6a",null,!1,n["a"],void 0);e["default"]=s.exports},"68a8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={vhImage:i("ce7c").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{style:[t.outerRecommendListConStyle]},[i("v-uni-view",{staticClass:"bg-ffffff p-24 b-rad-10",style:[t.innerRecommendListConStyle]},t._l(t.recommendList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"d-flex j-sb",class:0==n?"":"mt-24",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.click(e)}}},[i("vh-image",{attrs:{"loading-type":2,src:e.banner_img[0],width:288,height:180,"border-radius":6}}),i("v-uni-view",{staticClass:"flex-1 d-flex flex-column j-sb ml-20"},[i("v-uni-view",{staticClass:"text-hidden-3"},[i("v-uni-text",{staticClass:"ml-06 font-24 text-3 l-h-36"},[t._v(t._s(e.title))])],1),i("v-uni-view",{staticClass:"mt-22 d-flex j-sb"},[1==e.is_hidden_price||[3,4].includes(e.onsale_status)?i("v-uni-text",{staticClass:"font-32 text-ff0013 l-h-28"},[t._v("价格保密")]):i("v-uni-text",{staticClass:"font-32 text-ff0013 l-h-28"},[i("v-uni-text",{staticClass:"font-20"},[t._v("¥")]),t._v(t._s(e.price))],1),i("v-uni-text",{staticClass:"font-22 text-9 l-h-34"},[t._v("已售"+t._s(e.purchased+e.vest_purchased)+"份")])],1)],1)],1)})),1)],1)},r=[]},"6bdb":function(t,e,i){var n=i("59df");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("3dc5b1f9",n,!0,{sourceMap:!1,shadowMode:!1})},"6d37":function(t,e,i){"use strict";i.r(e);var n=i("68a8"),a=i("8437");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"35cb9d80",null,!1,n["a"],void 0);e["default"]=s.exports},"713d":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f07e")),r=n(i("c964"));i("a9e3"),i("caad6"),i("2532"),i("4ec9"),i("d3b7"),i("3ca3"),i("ddb0");var o=n(i("e75f")),s={name:"vh-channel-title-icon",props:{is_seckill:{type:[Number],default:0},channel:{type:[String,Number],default:0},marketingAttribute:{type:String,default:"0"},warehouseType:{type:[String,Number],default:"0"},showTitle:{type:Boolean,default:!1},borderRadius:{type:[String,Number],default:6},marginLeft:{type:[String,Number],default:0},padding:{type:String,default:"0 10rpx"},fontSize:{type:[String,Number],default:24},fontBold:{type:Boolean,default:!0},isNewYearTheme:{type:Boolean,default:!1},textColor:{type:String,default:"#FFFFFF"},plate:{type:String,default:""}},computed:{getChannel:function(){return this.marketingAttribute.includes("1")?101:9==this.channel?1==this.warehouseType?102:2==this.warehouseType?103:1:this.channel},iconName:function(){var t=!0;this.$android?t=(0,o.default)("9.1.8"):this.$ios&&(t=(0,o.default)("9.24"));var e=new Map([[0,{title:this.is_seckill?"秒杀":"闪购",iconText:this.is_seckill?"秒杀":"闪购",bgColor:this.is_seckill?"#FDE451":"#E80404",textColor:this.is_seckill?"#E80404":"#FFF"}],[1,{title:this.isNewYear?"年货节":"现货速发",iconText:this.isNewYear?"年货节":"现货速发",bgColor:"#FF9127",textColor:"#FFF"}],[2,{title:"跨境",iconText:"跨境",bgColor:"#734cd2",textColor:"#FFF"}],[3,{title:"尾货",iconText:"尾货",bgColor:"#FF9127",textColor:"#FFF"}],[4,{title:"兔头",iconText:"兔头",bgColor:"#FF9127",textColor:"#FFF"}],[11,{title:"拍卖",iconText:"拍卖",bgColor:"#F6B869",textColor:"#FFF"}],[9,{title:this.isNewYear?"年货节":"现货速发",iconText:this.isNewYear?"年货节":"现货速发",bgColor:"#FF9127",textColor:"#FFF"}],[101,{title:"拼团",iconText:"拼团",bgColor:"#FF9127",textColor:"#FFF"}],[102,{title:9===this.channel?this.isNewYear?"年货节":"现货速发":"闪购",iconText:"3小时达",bgColor:"#17E6A1",textColor:"#fff"}],[103,{title:9===this.channel?this.isNewYear?"年货节":"现货速发":"闪购",iconText:t?"次日达":"本地仓",bgColor:"#FAB005",textColor:"#fff"}]]);return e},iconStyle:function(){var t={};console.log("909988---",this.getChannel,this.iconName.get(this.getChannel));var e=this.iconName.get(this.getChannel),i=e.bgColor,n=e.textColor;return t.backgroundColor=i,t.borderRadius=this.borderRadius+"rpx",t.marginLeft=this.marginLeft+"rpx",t.padding=this.padding,t.fontSize=this.fontSize+"rpx",this.fontBold&&(t.fontWeight="bold"),t.color=n,1==this.warehouseType&&9==this.channel&&(t.color="#000",t.fontWeight="bold"),t}},mounted:function(){this.isNewYearTheme||this.secondConfig()},data:function(){return{isNewYear:!1}},methods:{secondConfig:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.secondConfig();case 2:i=e.sent,i.data.isopen&&(t.isNewYear=!0);case 4:case"end":return e.stop()}}),e)})))()}}};e.default=s},"825d":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?i("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},a=[]},8437:function(t,e,i){"use strict";i.r(e);var n=i("e997"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},"8e1d":function(t,e,i){"use strict";i.r(e);var n=i("9476"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},9476:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("c975"),i("d3b7"),i("ac1f");var n={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(i){var n=i[0];if(n.width&&n.width&&(n.targetWidth=n.height>n.width?n.height:n.width,n.targetWidth)){e.fields=n;var a,r;a=t.touches[0].clientX,r=t.touches[0].clientY,e.rippleTop=r-n.top-n.targetWidth/2,e.rippleLeft=a-n.left-n.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var i="";i=uni.createSelectorQuery().in(t),i.select(".u-btn").boundingClientRect(),i.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=n},"95b6":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n}));var n={uIcon:i("e5e1").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{},[i("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[i("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),i("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?i("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"u-icon-wrap"},[i("u-icon",{attrs:{name:t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?i("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?i("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[i("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),i("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),i("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?i("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},r=[]},a8bc6:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"d-flex j-center a-center",style:[t.upDownStyle]},[t.showImage?[i("v-uni-image",{style:[t.imageStyle],attrs:{src:t.imageSrc,mode:"aspectFill"}}),i("v-uni-view",{style:[t.textStyle]},[t._v(t._s(t.text))])]:[i("v-uni-view",{style:[t.lineStyle]}),i("v-uni-view",{style:[t.textStyle]},[t._v(t._s(t.text))]),i("v-uni-view",{style:[t.lineStyle]})]],2)},a=[]},aab3:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},b10b:function(t,e,i){"use strict";var n=i("6bdb"),a=i.n(n);a.a},b69e:function(t,e,i){"use strict";i.r(e);var n=i("713d"),a=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},cbda:function(t,e,i){"use strict";i.r(e);var n=i("a8bc6"),a=i("55e7");for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);var o=i("f0c5"),s=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"0b206e9a",null,!1,n["a"],void 0);e["default"]=s.exports},db26:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return""!==t.getChannel&&"orderConfirm"==t.plate?i("v-uni-view",{staticClass:"flex-s-c"},[t.showTitle?i("v-uni-view",{staticClass:"font-32 font-wei text-3 l-h-40"},[t._v(t._s(t.iconName.get(t.getChannel).title)+"商品")]):t._e(),i("v-uni-view",{staticClass:"mt-04",style:[t.iconStyle]},[t._v(t._s(t.iconName.get(t.getChannel).iconText))])],1):""!==t.getChannel?i("v-uni-text",{},[t.showTitle?i("v-uni-text",{staticClass:"font-32 font-wei text-3 l-h-44"},[t._v(t._s(t.iconName.get(t.getChannel).title)+"商品")]):t._e(),i("v-uni-text",{style:[t.iconStyle]},[t._v(t._s(t.iconName.get(t.getChannel).iconText))])],1):t._e()},a=[]},e75f:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d81d");var a=n(i("e143"));e.default=function(t){var e,i=(null===(e=a.default.prototype)||void 0===e?void 0:e.$vhVersion)||"",n=function(t){return t.split(".").map((function(t){return+t}))},r=n(i),o=r.length,s=n(t),l=s.length;if(console.log(r,s),o>l)return!0;if(o<l)return!1;var c=0;while(c<o){if(r[c]>s[c])return!0;if(r[c]<s[c])return!1;c++}return!1}},e997:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f07e")),r=n(i("c964")),o=n(i("f3f3"));i("a9e3"),i("99af");var s=i("26cb"),l={name:"vh-goods-recommend-list",props:{from:{type:[String,Number],default:""},outerPaddingBottom:{type:[String,Number],default:24},innMarginLeft:{type:[String,Number],default:24},innMarginRight:{type:[String,Number],default:24},customClick:{type:Boolean,default:!1},jumpType:{type:Number,default:0},isInit:{type:Boolean,default:!0}},data:function(){return{recommendList:[]}},computed:(0,o.default)((0,o.default)({},(0,s.mapState)(["routeTable"])),{},{outerRecommendListConStyle:function(){return{paddingBottom:this.outerPaddingBottom+"rpx"}},innerRecommendListConStyle:function(){var t={};return t.marginLeft=this.innMarginLeft+"rpx",t.marginRight=this.innMarginRight+"rpx",t}}),created:function(){this.isInit&&this.getRecommendList()},methods:{getRecommendList:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.recommendList();case 2:i=e.sent,t.recommendList=i.data;case 4:case"end":return e.stop()}}),e)})))()},click:function(t){this.customClick&&this.$emit("click",t),1===this.jumpType?this.jump.appAndMiniJump(0,"".concat(this.$routeTable.pgGoodsDetail,"?id=").concat(t.id),this.$vhFrom,1):this.jump.appAndMiniJump(0,"".concat(this.routeTable.pgGoodsDetail,"?id=").concat(t.id),this.$vhFrom,0)}}};e.default=l},fa94:function(t,e,i){"use strict";var n=i("062a"),a=i.n(n);a.a}}]);