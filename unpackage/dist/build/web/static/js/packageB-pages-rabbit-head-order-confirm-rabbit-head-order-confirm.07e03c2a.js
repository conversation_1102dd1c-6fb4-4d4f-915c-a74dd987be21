(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-rabbit-head-order-confirm-rabbit-head-order-confirm"],{"00d9":function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-model[data-v-acf792f8]{height:auto;overflow:hidden;font-size:%?32?%;background-color:#fff}.u-model__btn--hover[data-v-acf792f8]{background-color:#e6e6e6}.u-model__title[data-v-acf792f8]{padding-top:%?48?%;font-weight:500;text-align:center;color:#303133}.u-model__content__message[data-v-acf792f8]{padding:%?48?%;font-size:%?30?%;text-align:center;color:#606266}.u-model__footer[data-v-acf792f8]{display:flex;flex-direction:row}.u-model__footer__button[data-v-acf792f8]{flex:1;height:%?100?%;line-height:%?100?%;font-size:%?32?%;box-sizing:border-box;cursor:pointer;text-align:center;border-radius:%?4?%}',""]),t.exports=e},"062a":function(t,e,n){var a=n("aab3");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("2db15654",a,!0,{sourceMap:!1,shadowMode:!1})},"12c6":function(t,e,n){"use strict";n.r(e);var a=n("51bd"),i=n("f074");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("f2f9");var r=n("f0c5"),s=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);e["default"]=s.exports},"2a71":function(t,e,n){"use strict";n.r(e);var a=n("5e5a"),i=n("3a06");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);var r=n("f0c5"),s=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"543e4a1a",null,!1,a["a"],void 0);e["default"]=s.exports},"3a06":function(t,e,n){"use strict";n.r(e);var a=n("c373"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"430f":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return a}));var a={uPopup:n("c4b0").default,uLoading:n("301a").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("u-popup",{attrs:{zoom:t.zoom,mode:"center",popup:!1,"z-index":t.uZIndex,length:t.width,"mask-close-able":t.maskCloseAble,"border-radius":t.borderRadius,"negative-top":t.negativeTop},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.popupClose.apply(void 0,arguments)}},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},[n("v-uni-view",{staticClass:"u-model"},[t.showTitle?n("v-uni-view",{staticClass:"u-model__title u-line-1",style:[t.titleStyle]},[t._v(t._s(t.title))]):t._e(),n("v-uni-view",{staticClass:"u-model__content"},[t.$slots.default||t.$slots.$default?n("v-uni-view",{style:[t.contentStyle]},[t._t("default")],2):n("v-uni-view",{staticClass:"u-model__content__message",style:[t.contentStyle]},[t._v(t._s(t.content))])],1),t.showCancelButton||t.showConfirmButton?n("v-uni-view",{staticClass:"u-model__footer u-border-top"},[t.showCancelButton?n("v-uni-view",{staticClass:"u-model__footer__button",style:[t.cancelBtnStyle],attrs:{"hover-stay-time":100,"hover-class":"u-model__btn--hover"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancel.apply(void 0,arguments)}}},[t._v(t._s(t.cancelText))]):t._e(),t.showConfirmButton||t.$slots["confirm-button"]?n("v-uni-view",{staticClass:"u-model__footer__button hairline-left",style:[t.confirmBtnStyle],attrs:{"hover-stay-time":100,"hover-class":t.asyncClose?"none":"u-model__btn--hover"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm.apply(void 0,arguments)}}},[t.$slots["confirm-button"]?t._t("confirm-button"):[t.loading?n("u-loading",{attrs:{mode:"circle",color:t.confirmColor}}):[t._v(t._s(t.confirmText))]]],2):t._e()],1):t._e()],1)],1)],1)},o=[]},"4f1b":function(t,e,n){"use strict";n.r(e);var a=n("825d"),i=n("8e1d");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("fa94");var r=n("f0c5"),s=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"4ed92bb2",null,!1,a["a"],void 0);e["default"]=s.exports},"51bd":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[n("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},o=[]},5761:function(t,e,n){"use strict";n.r(e);var a=n("430f"),i=n("70e8");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("6f69");var r=n("f0c5"),s=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"acf792f8",null,!1,a["a"],void 0);e["default"]=s.exports},"5e5a":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return a}));var a={vhNavbar:n("12c6").default,uIcon:n("e5e1").default,vhImage:n("ce7c").default,uButton:n("4f1b").default,uModal:n("5761").default,vhSkeleton:n("591b").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"content h-vh-100 bg-f5f5f5 o-scr-y"},[n("vh-navbar",{attrs:{title:"确认订单"}}),t.loading?n("vh-skeleton",{attrs:{type:2,"loading-color":"#FF9127"}}):n("v-uni-view",{staticClass:"fade-in pt-20 pb-20"},[Object.keys(t.addressInfo).length?n("v-uni-view",{staticClass:"bg-ffffff b-rad-10 d-flex j-sb a-center ml-24 mr-24 ptb-32-plr-24",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.navigateTo(t.routeTable.pEAddressManagement+"?comeFrom=3")}}},[n("v-uni-view",{staticClass:"w-500"},[n("v-uni-view",{staticClass:"d-flex a-center"},[n("v-uni-text",{staticClass:"font-32 font-wei text-3"},[t._v(t._s(t.addressInfo.consignee))]),n("v-uni-text",{staticClass:"ml-14 font-28 text-9"},[t._v(t._s(t.addressInfo.consignee_phone))])],1),n("v-uni-view",{staticClass:"mt-12"},[t.addressInfo.is_default?n("v-uni-text",{staticClass:"bg-ff0013 b-rad-04 ptb-02-plr-08 text-ffffff font-20 font-wei l-h-28"},[t._v("默认")]):t._e(),t.addressInfo.label?n("v-uni-text",{staticClass:"bg-2e7bff b-rad-04 ptb-02-plr-16 text-ffffff font-20 font-wei l-h-28",class:t.addressInfo.is_default?"ml-10":""},[t._v(t._s(t.addressInfo.label))]):t._e(),n("v-uni-text",{staticClass:"font-24 text-3 l-h-34",class:t.addressInfo.is_default||t.addressInfo.label?"ml-10":""},[t._v(t._s(t.addressInfo.province_name)+" "+t._s(t.addressInfo.city_name)+" "+t._s(t.addressInfo.town_name)+"\n            "+t._s(t.addressInfo.address))])],1)],1),n("u-icon",{attrs:{name:"arrow-right",size:24,color:"#333"}})],1):n("v-uni-view",{staticClass:"p-rela h-188 b-rad-10 d-flex j-center a-center ml-24 mr-24 o-hid",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.navigateTo(t.routeTable.pEAddressManagement+"?comeFrom=3")}}},[n("v-uni-image",{staticClass:"p-abso z-01 w-702 h-188",attrs:{src:"http://images.vinehoo.com/vinehoomini/v3/order-confirm/add_bg.png",mode:"aspectFill"}}),n("v-uni-view",{staticClass:"p-rela z-02 d-flex flex-column j-center a-center"},[n("v-uni-image",{staticClass:"w-84 h-84",attrs:{src:"http://images.vinehoo.com/vinehoomini/v3/order-confirm/add_ico.png",mode:"aspectFill"}}),n("v-uni-text",{staticClass:"mt-06 font-30 text-3"},[t._v("新建收货地址")])],1)],1),t._l("next"===t.$vhFrom?t.nextrabbitOrderInfo.rabbitGoodsList:t.rabbitOrderInfo.rabbitGoodsList,(function(e,a){return n("v-uni-view",{key:a,staticClass:"bg-ffffff b-rad-10 mt-20 ml-24 mr-24 pt-32 pr-24 pb-32 pl-24"},[n("v-uni-view",{staticClass:"font-32 font-wei text-3 l-h-44"},[t._v("兑换商品")]),n("v-uni-view",{staticClass:"d-flex mt-28"},[n("v-uni-view",{staticClass:"w-246 h-152 b-rad-06 o-hid"},[n("vh-image",{attrs:{"loading-type":2,src:e.banner_img,height:152}})],1),n("v-uni-view",{staticClass:"flex-1 d-flex flex-column j-sb ml-12"},[n("v-uni-view",{},[n("v-uni-view",{staticClass:"font-24 text-0 l-h-34 text-hidden-2"},[t._v(t._s(e.title))]),n("v-uni-text",{staticClass:"bg-f5f5f5 b-rad-04 mt-08 ptb-02-plr-12 font-20 text-9"},[t._v(t._s(e.package_name))])],1),n("v-uni-view",{staticClass:"d-flex j-sb a-center"},[n("v-uni-view",{},[n("v-uni-text",{staticClass:"font-28 font-wei text-e80404 l-h-40"},[t._v(t._s(e.rabbit))]),n("v-uni-image",{staticClass:"ml-06 w-28 h-28",attrs:{src:"http://images.vinehoo.com/vinehoomini/v3/comm/rab_gold.png",mode:"aspectFill"}})],1),n("v-uni-view",{staticClass:"font-24 text-6"},[t._v("x"+t._s(e.nums))])],1)],1)],1),n("v-uni-view",{staticClass:"mt-32 d-flex j-sb a-center"},[n("v-uni-text",{staticClass:"font-28 font-wei text-3 l-h-40"},[t._v("配送")]),n("v-uni-text",{staticClass:"font-24 text-3 l-h-36"},[t._v("默认配送方式")])],1),n("v-uni-view",{staticClass:"mt-20 font-24 text-6"},[t._v("预计发货时间："+t._s(e.predict_time))])],1)})),n("v-uni-view",{staticClass:"bg-ffffff b-rad-10 mt-20 ml-24 mr-24"},[n("v-uni-view",{staticClass:"ml-24 mr-24 pt-32 pb-32 font-28 text-9"},[t._v("温馨提示：不支持7天无理由退货")])],1),n("v-uni-view",{staticClass:"p-abso bottom-160 w-p100"},[n("v-uni-view",{staticClass:"d-flex j-center"},[n("v-uni-text",{staticClass:"font-24 text-9"},[t._v("如有特殊需求，请")]),n("v-uni-text",{staticClass:"font-24 text-2e7bff",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.system.customerService(t.$vhFrom)}}},[t._v("联系客服")]),n("v-uni-text",{staticClass:"font-24 text-9"},[t._v("。")])],1)],1),n("v-uni-view",{staticClass:"p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-32 pr-24"},[n("v-uni-view",{staticClass:"d-flex a-center"},[n("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("合计：")]),n("v-uni-text",{staticClass:"font-40 font-wei text-e80404"},[t._v(t._s("next"===t.$vhFrom?t.nextrabbitOrderInfo.rabbitGoodsList[0].rabbit:t.rabbitOrderInfo.rabbitGoodsList[0].rabbit))]),n("v-uni-image",{staticClass:"ml-06 w-28 h-28",attrs:{src:"http://images.vinehoo.com/vinehoomini/v3/comm/rab_gold.png",mode:"aspectFill"}})],1),n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.exchangeNow.apply(void 0,arguments)}}},[t._v("立即兑换")])],1)],1),n("v-uni-view",{},[n("u-modal",{attrs:{width:540,"show-title":!1,"show-cancel-button":!0,"cancel-text":"知道了","confirm-text":"去赚兔头","cancel-style":{fontSize:"28rpx",color:"#999"},"confirm-style":{fontSize:"28rpx",color:"#E80404"},"content-style":{fontSize:"28rpx",fontWeight:"bold",color:"#333"}},on:{confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.redirectTo(t.routeTable.pEDailyTasks)}},model:{value:t.showRabNotEnoMod,callback:function(e){t.showRabNotEnoMod=e},expression:"showRabNotEnoMod"}},[n("v-uni-view",{staticClass:"ptb-52-plr-00 text-center"},[t._v("您的兔头数量不足，无法兑换")])],1)],1)],2)],1)},o=[]},"6f69":function(t,e,n){"use strict";var a=n("9945"),i=n.n(a);i.a},"70e8":function(t,e,n){"use strict";n.r(e);var a=n("aa64"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},"7f1a":function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var o=n("26cb"),r=uni.getSystemInfoSync(),s={},l={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:r.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,o.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,n=e.pEAddressAdd,a=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(n)||t.includes(a)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=l},"825d":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?n("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},i=[]},"8e1d":function(t,e,n){"use strict";n.r(e);var a=n("9476"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},9476:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("c975"),n("d3b7"),n("ac1f");var a={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(n){var a=n[0];if(a.width&&a.width&&(a.targetWidth=a.height>a.width?a.height:a.width,a.targetWidth)){e.fields=a;var i,o;i=t.touches[0].clientX,o=t.touches[0].clientY,e.rippleTop=o-a.top-a.targetWidth/2,e.rippleLeft=i-a.left-a.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var n="";n=uni.createSelectorQuery().in(t),n.select(".u-btn").boundingClientRect(),n.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=a},9945:function(t,e,n){var a=n("00d9");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("2b69c872",a,!0,{sourceMap:!1,shadowMode:!1})},a126:function(t,e,n){var a=n("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},aa64:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var a={name:"u-modal",props:{value:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:""},title:{type:[String],default:"提示"},width:{type:[Number,String],default:600},content:{type:String,default:"内容"},showTitle:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0},showCancelButton:{type:Boolean,default:!1},confirmText:{type:String,default:"确认"},cancelText:{type:String,default:"取消"},confirmColor:{type:String,default:"#2979ff"},cancelColor:{type:String,default:"#606266"},borderRadius:{type:[Number,String],default:16},titleStyle:{type:Object,default:function(){return{}}},contentStyle:{type:Object,default:function(){return{}}},cancelStyle:{type:Object,default:function(){return{}}},confirmStyle:{type:Object,default:function(){return{}}},zoom:{type:Boolean,default:!0},asyncClose:{type:Boolean,default:!1},maskCloseAble:{type:Boolean,default:!1},negativeTop:{type:[String,Number],default:0}},data:function(){return{loading:!1}},computed:{cancelBtnStyle:function(){return Object.assign({color:this.cancelColor},this.cancelStyle)},confirmBtnStyle:function(){return Object.assign({color:this.confirmColor},this.confirmStyle)},uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},watch:{value:function(t){!0===t&&(this.loading=!1)}},methods:{confirm:function(){this.asyncClose?this.loading=!0:this.$emit("input",!1),this.$emit("confirm")},cancel:function(){var t=this;this.$emit("cancel"),this.$emit("input",!1),setTimeout((function(){t.loading=!1}),300)},popupClose:function(){this.$emit("input",!1)},clearLoading:function(){this.loading=!1}}};e.default=a},aab3:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},bbdc:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},c373:function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b64b"),n("e9c4");var i=a(n("f07e")),o=a(n("c964")),r=a(n("f3f3")),s=n("26cb"),l={name:"rabbit-head-order-confirm",data:function(){return{loading:!0,rabbitNum:0,addressInfo:{},showRabNotEnoMod:!1,nextrabbitOrderInfo:{}}},computed:(0,r.default)({},(0,s.mapState)(["routeTable","rabbitOrderInfo","addressInfoState"])),onShow:function(){this.init()},methods:{init:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return"next"==t.$vhFrom&&(n=uni.getStorageSync("nextrabbitOrderInfo"),console.log(n),n&&(t.nextrabbitOrderInfo=n,uni.removeStorageSync("nextrabbitOrderInfo"))),e.next=3,t.getRabbitNum();case 3:return e.next=5,t.getAddressList();case 5:t.loading=!1;case 6:case"end":return e.stop()}}),e)})))()},getRabbitNum:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.userSpecifiedData({field:"rabbit"});case 2:n=e.sent,t.rabbitNum=n.data.rabbit,console.log("rabbitNum："+t.rabbitNum);case 5:case"end":return e.stop()}}),e)})))()},getAddressList:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){var n,a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.log(Object.keys(t.addressInfoState)),!Object.keys(t.addressInfoState).length){e.next=5;break}t.addressInfo=t.addressInfoState,e.next=10;break;case 5:return e.next=7,t.$u.api.addressList();case 7:if(n=e.sent,console.warn("res:"+JSON.stringify(n.data.list)),n.data.list.length>0)for(console.log(n.data.list.length),a=0;a<n.data.list.length;a++)console.log("for"),n.data.list[a].is_default?(t.addressInfo=n.data.list[a],console.log("true")):(console.log("false"),t.addressInfo=n.data.list[0]);else console.log("null"),t.addressInfo={};case 10:case"end":return e.stop()}}),e)})))()},exchangeNow:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){var n,a,o,r,s,l,d,c,u;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=[],"next"!==t.$vhFrom){e.next=7;break}if(!(t.rabbitNum<t.nextrabbitOrderInfo.rabbitGoodsList[0].rabbit)){e.next=4;break}return e.abrupt("return",t.showRabNotEnoMod=!0);case 4:n=t.nextrabbitOrderInfo.rabbitGoodsList,e.next=10;break;case 7:if(!(t.rabbitNum<t.rabbitOrderInfo.rabbitGoodsList[0].rabbit)){e.next=9;break}return e.abrupt("return",t.showRabNotEnoMod=!0);case 9:n=t.rabbitGoodsList.rabbitGoodsList;case 10:return a=t.addressInfo,o=a.province_id,r=a.city_id,s=a.town_id,l=a.address,d=a.consignee,c=a.consignee_phone,u={items_info:n,order_from:4,province_id:o,city_id:r,district_id:s,address:l,consignee:d,consignee_phone:c},e.prev=12,t.feedback.loading({title:"提交中..."}),e.next=16,t.$u.api.rabbitCreateOrder(u);case 16:e.sent,t.feedback.hideLoading(),t.jump.redirectTo(t.routeTable.pBRabbitExchangeSuccess),e.next=24;break;case 21:e.prev=21,e.t0=e["catch"](12),setTimeout((function(){t.jump.navigateBack()}),1500);case 24:case"end":return e.stop()}}),e,null,[[12,21]])})))()}}};e.default=l},f074:function(t,e,n){"use strict";n.r(e);var a=n("7f1a"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a},f2f9:function(t,e,n){"use strict";var a=n("a126"),i=n.n(a);i.a},fa94:function(t,e,n){"use strict";var a=n("062a"),i=n.n(a);i.a}}]);