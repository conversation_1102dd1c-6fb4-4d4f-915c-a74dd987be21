(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-rn-company-auction-rn-company"],{"1d95":function(t,e,n){"use strict";n.r(e);var i=n("b78c"),a=n("f503");for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);var o=n("f0c5"),c=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"0dbd279a",null,!1,i["a"],void 0);e["default"]=c.exports},b78c:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return i}));var i={vhNavbar:n("12c6").default,AuctionUpload:n("627a").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("vh-navbar",{attrs:{title:"商户认证",height:"46"}}),t.loading?t._e():n("v-uni-view",[t.rnInfo.status===t.MAuctionRNStatus.OnCheck?n("v-uni-view",{staticClass:"pt-332"},[n("v-uni-view",{staticClass:"flex-c-c"},[n("v-uni-image",{staticClass:"w-440 h-400",attrs:{src:t.ossIcon("/auction/rn_on_check_440_400.png")}})],1),n("v-uni-view",{staticClass:"mt-60 font-wei-500 font-36 text-3 l-h-66 text-center"},[t._v("审核中")]),n("v-uni-view",{staticClass:"mt-20 font-28 text-6 l-h-44 text-center"},[t._v("需1-3个工作日, 请兔子君耐心等待～")])],1):t.rnInfo.status===t.MAuctionRNStatus.Rejected?n("v-uni-view",{staticClass:"pt-332"},[n("v-uni-view",{staticClass:"flex-c-c"},[n("v-uni-image",{staticClass:"w-442 h-400",attrs:{src:t.ossIcon("/auction/rn_reject_442_400.png")}})],1),n("v-uni-view",{staticClass:"mt-72 font-wei-500 font-36 text-3 l-h-50 text-center"},[t._v("未通过")]),n("v-uni-view",{staticClass:"mt-20 font-28 text-6 l-h-40 text-center"},[t._v(t._s(t.rnInfo.reject_reason))]),n("v-uni-view",{staticClass:"flex-c-c mt-230"},[n("v-uni-button",{staticClass:"vh-btn flex-c-c w-646 h-80 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-40",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.rnInfo.status=t.MAuctionRNStatus.NotSubmit}}},[t._v("重新认证")])],1)],1):n("v-uni-view",[n("v-uni-view",{staticClass:"h-20 bg-f5f5f5"}),n("v-uni-view",{staticClass:"p-rela"},[n("v-uni-view",{staticClass:"ptb-28-plr-32"},[n("v-uni-view",{staticClass:"d-flex"},[n("v-uni-image",{staticClass:"w-34 h-34",attrs:{src:t.ossIcon("/auction/editor_34.png")}}),n("v-uni-text",{staticClass:"ml-20 font-28 text-6 l-h-40"},[t._v("企业信息")])],1),n("v-uni-view",{staticClass:"flex-s-c h-110 bb-s-02-eeeeee"},[n("v-uni-view",{staticClass:"w-174 font-wei-500 font-32 text-3"},[t._v("企业名称")]),n("v-uni-input",{staticClass:"flex-1 font-32 text-3 text-right",attrs:{placeholder:"请填写营业执照上的企业全称","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.rnInfo.title,callback:function(e){t.$set(t.rnInfo,"title",e)},expression:"rnInfo.title"}})],1),n("v-uni-view",{staticClass:"flex-s-c h-110 bb-s-02-eeeeee"},[n("v-uni-view",{staticClass:"w-174 font-wei-500 font-32 text-3"},[t._v("营业执照号")]),n("v-uni-input",{staticClass:"flex-1 font-32 text-3 text-right",attrs:{placeholder:"请输入营业执照注册号","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.rnInfo.business_license_number,callback:function(e){t.$set(t.rnInfo,"business_license_number",e)},expression:"rnInfo.business_license_number"}})],1),n("v-uni-view",{staticClass:"mt-32"},[n("v-uni-view",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("请上传营业执照")]),n("v-uni-view",{staticClass:"flex-c-c mt-20"},[n("AuctionUpload",{attrs:{plate:96,directory:"vinehoo/client/auctionRNCompany/","max-count":1,fileList:t.businessLicenseImageFileList},on:{"on-list-change":function(e){arguments[0]=e=t.$handleEvent(e),t.onListChange(e,"business_license_image")},"on-uploaded":function(e){arguments[0]=e=t.$handleEvent(e),t.onUploaded(e,"business_license_image")}}})],1)],1),n("v-uni-view",{staticClass:"d-flex mt-50"},[n("v-uni-image",{staticClass:"w-34 h-34",attrs:{src:t.ossIcon("/auction/editor_34.png")}}),n("v-uni-text",{staticClass:"ml-20 font-28 text-6 l-h-40"},[t._v("管理员信息")])],1),n("v-uni-view",{staticClass:"flex-s-c h-110 bb-s-02-eeeeee"},[n("v-uni-view",{staticClass:"w-174 font-wei-500 font-32 text-3"},[t._v("管理员姓名")]),n("v-uni-input",{staticClass:"flex-1 font-32 text-3 text-right",attrs:{placeholder:"请输入","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.rnInfo.name,callback:function(e){t.$set(t.rnInfo,"name",e)},expression:"rnInfo.name"}})],1),n("v-uni-view",{staticClass:"flex-s-c h-110 bb-s-02-eeeeee"},[n("v-uni-view",{staticClass:"w-174 font-wei-500 font-32 text-3"},[t._v("身份证号码")]),n("v-uni-input",{staticClass:"flex-1 font-32 text-3 text-right",attrs:{placeholder:"请输入","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.rnInfo.id_card,callback:function(e){t.$set(t.rnInfo,"id_card",e)},expression:"rnInfo.id_card"}})],1),n("v-uni-view",{staticClass:"flex-s-c h-110 bb-s-02-eeeeee"},[n("v-uni-view",{staticClass:"w-174 font-wei-500 font-32 text-3"},[t._v("手机号码")]),n("v-uni-input",{staticClass:"flex-1 font-32 text-3 text-right",attrs:{placeholder:"请输入","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.rnInfo.phone,callback:function(e){t.$set(t.rnInfo,"phone",e)},expression:"rnInfo.phone"}})],1),n("v-uni-view",{staticClass:"flex-s-c h-110 bb-s-02-eeeeee"},[n("v-uni-view",{staticClass:"w-174 font-wei-500 font-32 text-3"},[t._v("电子邮箱")]),n("v-uni-input",{staticClass:"flex-1 font-32 text-3 text-right",attrs:{placeholder:"请输入","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.rnInfo.email,callback:function(e){t.$set(t.rnInfo,"email",e)},expression:"rnInfo.email"}})],1),n("v-uni-view",{staticClass:"mt-32"},[n("v-uni-view",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("请上传管理员身份证")]),n("v-uni-view",{staticClass:"flex-c-c mt-20"},[n("AuctionUpload",{attrs:{plate:95,directory:"vinehoo/client/auctionRNCompany/","max-count":1,fileList:t.idCardPortraitFileList},on:{"on-list-change":function(e){arguments[0]=e=t.$handleEvent(e),t.onListChange(e,"id_card_portrait")},"on-uploaded":function(e){arguments[0]=e=t.$handleEvent(e),t.onUploaded(e,"id_card_portrait")}}})],1),n("v-uni-view",{staticClass:"flex-c-c mt-20"},[n("AuctionUpload",{attrs:{plate:94,directory:"vinehoo/client/auctionRNCompany/","max-count":1,fileList:t.idCardEmblemFileList},on:{"on-list-change":function(e){arguments[0]=e=t.$handleEvent(e),t.onListChange(e,"id_card_emblem")},"on-uploaded":function(e){arguments[0]=e=t.$handleEvent(e),t.onUploaded(e,"id_card_emblem")}}})],1)],1),n("v-uni-view",{staticClass:"mt-50"},[n("v-uni-view",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("注意事项")]),n("v-uni-view",{staticClass:"mt-24 font-24 text-6 l-h-34"},[n("v-uni-view",[n("v-uni-text",{staticClass:"text-e80404"},[t._v("*")]),t._v("1. 请上传清晰、完整、无遮挡的营业执照和身份证图片")],1),n("v-uni-view",[n("v-uni-text",{staticClass:"text-e80404"},[t._v("*")]),t._v("2. 要求营业执原件红章清晰")],1)],1)],1),n("v-uni-view",{staticClass:"flex-c-c mt-100"},[n("v-uni-button",{staticClass:"vh-btn flex-c-c w-646 h-80 font-wei-500 font-28 text-ffffff b-rad-40",class:t.disabled?"bg-fce4e3":"bg-e80404",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSubmit.apply(void 0,arguments)}}},[t._v("提交审核")])],1)],1),t.rnInfo.status===t.MAuctionRNStatus.Passed?n("v-uni-view",{staticClass:"p-abso top-0 w-p100 h-p100",staticStyle:{background:"rgba(255,255,255,0.53)"}},[n("v-uni-image",{staticClass:"p-abso top-0 right-108 w-240 h-218",attrs:{src:t.ossIcon("/auction/rn_finish_240_218.png")}})],1):t._e()],1)],1)],1)],1)},s=[]},f503:function(t,e,n){"use strict";n.r(e);var i=n("fd3e"),a=n.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);e["default"]=a.a},fd3e:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("ac1f"),n("5319"),n("d3b7"),n("d81d"),n("caad6"),n("2532"),n("99af"),n("159b"),n("00b4");var a=i(n("f07e")),s=i(n("c964")),o=i(n("f3f3")),c=n("d8be"),r=n("26cb"),l={name:"auctionRNCompany",data:function(){return{MAuctionRNStatus:c.MAuctionRNStatus,loading:!0,rnInfo:{type:c.MAuctionRNType.CompanyRN,title:"",business_license_number:"",business_license_image:"",name:"",id_card:"",phone:"",email:"",id_card_portrait:"",id_card_emblem:""},businessLicenseImageFileList:[],idCardPortraitFileList:[],idCardEmblemFileList:[]}},computed:(0,o.default)((0,o.default)({},(0,r.mapState)(["ossPrefix"])),{},{disabled:function(t){var e=t.rnInfo,n=e.title,i=e.business_license_number,a=e.business_license_image,s=e.name,o=e.id_card,c=e.phone,r=e.email,l=e.id_card_portrait,u=e.id_card_emblem;return!(n&&i&&a&&s&&o&&c&&r&&l&&u)}}),methods:{load:function(){var t=this;return(0,s.default)((0,a.default)().mark((function e(){var n,i,s,o,r,l,u,f,d;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.getAuctionRNInfo({type:c.MAuctionRNType.CompanyRN});case 2:n=e.sent,i=(null===n||void 0===n?void 0:n.data)||{},t.rnInfo=Object.assign({},t.rnInfo,i),t.rnInfo.id&&(s=function(e){return e.replace(t.ossPrefix,"")},o=["business_license_image","id_card_portrait","id_card_emblem"],o.forEach((function(e){t.rnInfo[e]=s(t.rnInfo[e])})),r=function(e){return e.split(",").map((function(e){return{fileType:"image",url:e.includes("http")?e:"".concat(t.ossPrefix).concat(e),response:e.includes("http")?e:"".concat(t.ossPrefix).concat(e),progress:100,error:!1,file:{}}}))},l=t.rnInfo,u=l.business_license_image,f=l.id_card_portrait,d=l.id_card_emblem,t.businessLicenseImageFileList=r(u),t.idCardPortraitFileList=r(f),t.idCardEmblemFileList=r(d));case 6:case"end":return e.stop()}}),e)})))()},onSubmit:function(){var t=this;return(0,s.default)((0,a.default)().mark((function e(){var n,i,s,o,c,r,l,u,f,d,v,p,_;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.disabled){e.next=2;break}return e.abrupt("return");case 2:if(n=t.rnInfo,i=n.id,s=n.type,o=n.title,c=n.business_license_number,r=n.business_license_image,l=n.name,u=n.id_card,f=n.phone,d=n.email,v=n.id_card_portrait,p=n.id_card_emblem,t.$u.test.idCard(u)){e.next=6;break}return t.feedback.toast({title:"请填写正确的身份证号码"}),e.abrupt("return");case 6:if(t.$u.test.mobile(f)){e.next=9;break}return t.feedback.toast({title:"请填写正确的手机号"}),e.abrupt("return");case 9:if(t.$u.test.email(d)){e.next=12;break}return t.feedback.toast({title:"请填写正确电子邮箱"}),e.abrupt("return");case 12:if(t.feedback.loading({title:"提交审核中..."}),_={type:s,title:o,business_license_number:c,business_license_image:r,name:l,id_card:u,phone:f,email:d,id_card_portrait:v,id_card_emblem:p},i&&(_.id=i),!i){e.next=20;break}return e.next=18,t.$u.api.editAuctionRNInfo(_);case 18:e.next=22;break;case 20:return e.next=22,t.$u.api.addAuctionRNInfo(_);case 22:t.load();case 23:case"end":return e.stop()}}),e)})))()},onListChange:function(t,e){var n=this;this.rnInfo[e]=t.map((function(t){var e=t.response,i=t.url;return e||i.replace(n.ossPrefix,"")})).join()},onUploaded:function(t,e){this.rnInfo[e]=t.map((function(t){var e=t.response;return e})).join(),uni.hideLoading()}},onLoad:function(){var t=this;this.login.isLoginV3(this.$vhFrom).then((function(e){e&&t.load().finally((function(){t.loading=!1}))}))}};e.default=l}}]);