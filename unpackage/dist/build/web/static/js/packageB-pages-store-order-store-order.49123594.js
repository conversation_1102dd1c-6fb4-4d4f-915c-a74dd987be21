(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-store-order-store-order"],{"062a":function(t,e,a){var n=a("aab3");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("2db15654",n,!0,{sourceMap:!1,shadowMode:!1})},"0efb":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"vh-image-con",class:[t.isMf?"mf-card":""],style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():a("v-uni-image",{staticClass:"vh-image",style:{backgroundColor:t.bgColor,borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.getImage,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?a("v-uni-view",{staticClass:"vh-image-loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[a("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e(),t.showError&&t.isError&&!t.loading?a("v-uni-view",{staticClass:"u-image-error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[a("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e()],1)},i=[]},"10eb":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},a("d9e2"),a("d401")},"12c6":function(t,e,a){"use strict";a.r(e);var n=a("51bd"),i=a("f074");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("f2f9");var o=a("f0c5"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"519170ec",null,!1,n["a"],void 0);e["default"]=s.exports},"144f":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"d-flex j-center",style:[this.outerEmpConStyle]},[e("v-uni-view",{staticClass:"p-rela",style:[this.innEmpConStyle]},[e("v-uni-image",{staticClass:"w-p100 h-p100",attrs:{src:this.imageSrc,mode:"aspectFill"}}),e("v-uni-view",{staticClass:"p-abso w-p100 d-flex j-center font-28 text-9",style:[this.textStyle]},[this._v(this._s(this.text))])],1)],1)},i=[]},"1b14":function(t,e,a){"use strict";a.r(e);var n=a("a6f4"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},2071:function(t,e,a){"use strict";var n=a("35ca"),i=a.n(n);i.a},"2da4":function(t,e,a){var n=a("39e4");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("64a51bd6",n,!0,{sourceMap:!1,shadowMode:!1})},"35ca":function(t,e,a){var n=a("552d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("175b7730",n,!0,{sourceMap:!1,shadowMode:!1})},3835:function(t,e,a){var n=a("4da7");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("28681ab0",n,!0,{sourceMap:!1,shadowMode:!1})},"39e4":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-load-more-wrap[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center}.u-load-more-inner[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:0 %?12?%}.u-more[data-v-15067509]{position:relative;display:flex;flex-direction:row;justify-content:center}.u-dot-text[data-v-15067509]{font-size:%?28?%}.u-loadmore-icon-wrap[data-v-15067509]{margin-right:%?8?%}.u-loadmore-icon[data-v-15067509]{display:flex;flex-direction:row;align-items:center;justify-content:center}',""]),t.exports=e},"3dc3":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("d0af")),r=n(a("f3f3"));a("a9e3"),a("d3b7"),a("159b"),a("e25e"),a("c975");var o=a("26cb"),s={name:"u-image",props:{loadingType:{type:[String,Number],default:1},isMf:{type:Boolean,default:!1},src:{default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!1},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:100},bgColor:{type:String,default:"transparent"},isResize:{type:Boolean,default:!1},resizeRatio:{type:Object,default:function(){return{wratio:16,hratio:9}}},resizeUsePx:{type:Boolean,default:!1},opacityProp:{type:Number,default:1},backgroundImage:{type:String,default:""}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:(0,r.default)((0,r.default)({},(0,o.mapState)(["ossPrefix"])),{},{wrapStyle:function(){var t={};if(t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),this.backgroundImage&&(t.backgroundImage="url(".concat(this.backgroundImage,")"),t.backgroundSize="100% 100%",t.backgroundRepeat="no-repeat",t.backgroundPosition="center"),this.isResize){var e,a,n=(null===this||void 0===this||null===(e=this.src)||void 0===e||null===(a=e.split("?"))||void 0===a?void 0:a[1])||"",r=n.split("&"),o={};r.forEach((function(t){var e=t.split("="),a=(0,i.default)(e,2),n=a[0],r=a[1];o[n]=r}));var s=+((null===o||void 0===o?void 0:o.w)||""),u=+((null===o||void 0===o?void 0:o.h)||"");if(!isNaN(s)&&!isNaN(u)&&s&&u){var d=parseInt(this.width),l=d/s*u,c=this.resizeRatio,f=c.wratio,p=c.hratio;if("auto"!==f&&"auto"!==p){var h=d*f/p,v=d*p/f;l>h?l=h:l<v&&(l=v)}this.resizeUsePx?t.height="".concat(l,"px"):t.height=this.$u.addUnit(l)}}return t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0||"circle"==this.shape?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t},getLoadingImage:function(){return"https://images.vinehoo.com/vinehoomini/v3/occupy_img/new_img".concat(this.loadingType,".png")},getImage:function(){return"string"!==typeof this.src?"":-1==this.src.indexOf("http")?this.ossPrefix+this.src:this.src}}),methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=t.opacityProp,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=s},4053:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,n.default)(t)};var n=function(t){return t&&t.__esModule?t:{default:t}}(a("b680"))},"41b0":function(t,e,a){"use strict";var n=a("3835"),i=a.n(n);i.a},4883:function(t,e,a){var n=a("9905");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("03f8e0ee",n,!0,{sourceMap:!1,shadowMode:!1})},"4da7":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,"uni-page-body[data-v-9f8400d6]{background-color:#f5f5f5}body.?%PAGE?%[data-v-9f8400d6]{background-color:#f5f5f5}",""]),t.exports=e},"4f1b":function(t,e,a){"use strict";a.r(e);var n=a("825d"),i=a("8e1d");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("fa94");var o=a("f0c5"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"4ed92bb2",null,!1,n["a"],void 0);e["default"]=s.exports},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return n}));var n={uIcon:a("e5e1").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},r=[]},"552d":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-mask[data-v-f63a3092]{position:fixed;top:0;left:0;right:0;bottom:0;opacity:0;transition:-webkit-transform .3s;transition:transform .3s;transition:transform .3s,-webkit-transform .3s}.u-mask-show[data-v-f63a3092]{opacity:1}.u-mask-zoom[data-v-f63a3092]{-webkit-transform:scale(1.2);transform:scale(1.2)}',""]),t.exports=e},"55c2":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var n={name:"vh-empty",props:{bgColor:{type:String,default:"#FFF"},paddingTop:{type:[String,Number],default:0},paddingBottom:{type:[String,Number],default:0},width:{type:[String,Number],default:440},height:{type:[String,Number],default:360},imageSrc:{type:String,default:""},textBottom:{type:[String,Number],default:46},text:{type:String,default:""}},computed:{outerEmpConStyle:function(){return{backgroundColor:this.bgColor,paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},innEmpConStyle:function(){return{width:this.width+"rpx",height:this.height+"rpx"}},textStyle:function(){return{bottom:this.textBottom+"rpx"}}}};e.default=n},5726:function(t,e,a){"use strict";a.r(e);var n=a("fa36"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"5ba4":function(t,e,a){"use strict";a.r(e);var n=a("144f"),i=a("a58c");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);var o=a("f0c5"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"55488dce",null,!1,n["a"],void 0);e["default"]=s.exports},"6ab5":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-image-con[data-v-89d76102]{position:relative;transition:opacity .5s ease-in}.vh-image[data-v-89d76102]{width:100%;height:100%}.vh-image-loading[data-v-89d76102],\n.u-image-error[data-v-89d76102]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#fff;color:#909399;font-size:%?46?%}.mf-card[data-v-89d76102]{background-color:#f7f7f7!important;padding:5px}',""]),t.exports=e},"776f":function(t,e,a){"use strict";a.r(e);var n=a("e643"),i=a("e4d5");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("afb6");var o=a("f0c5"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"15067509",null,!1,n["a"],void 0);e["default"]=s.exports},"7f1a":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var r=a("26cb"),o=uni.getSystemInfoSync(),s={},u={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,r.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,n=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(n)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=u},"825d":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?a("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},i=[]},"8a04":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var n={name:"u-loadmore",props:{bgColor:{type:String,default:"transparent"},icon:{type:Boolean,default:!0},fontSize:{type:String,default:"28"},color:{type:String,default:"#606266"},status:{type:String,default:"loadmore"},iconType:{type:String,default:"circle"},loadText:{type:Object,default:function(){return{loadmore:"加载更多",loading:"正在加载...",nomore:"没有更多了"}}},isDot:{type:Boolean,default:!1},iconColor:{type:String,default:"#b7b7b7"},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},height:{type:[String,Number],default:"auto"}},data:function(){return{dotText:"●"}},computed:{loadTextStyle:function(){return{color:this.color,fontSize:this.fontSize+"rpx",position:"relative",zIndex:1,backgroundColor:this.bgColor}},cricleStyle:function(){return{borderColor:"#e5e5e5 #e5e5e5 #e5e5e5 ".concat(this.circleColor)}},flowerStyle:function(){return{}},showText:function(){var t="";return t="loadmore"==this.status?this.loadText.loadmore:"loading"==this.status?this.loadText.loading:"nomore"==this.status&&this.isDot?this.dotText:this.loadText.nomore,t}},methods:{loadMore:function(){"loadmore"==this.status&&this.$emit("loadmore")}}};e.default=n},"8e1d":function(t,e,a){"use strict";a.r(e);var n=a("9476"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},9476:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3"),a("c975"),a("d3b7"),a("ac1f");var n={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(a){var n=a[0];if(n.width&&n.width&&(n.targetWidth=n.height>n.width?n.height:n.width,n.targetWidth)){e.fields=n;var i,r;i=t.touches[0].clientX,r=t.touches[0].clientY,e.rippleTop=r-n.top-n.targetWidth/2,e.rippleLeft=i-n.left-n.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var a="";a=uni.createSelectorQuery().in(t),a.select(".u-btn").boundingClientRect(),a.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=n},9905:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,".triangle[data-v-9f8400d6]{border-left:%?20?% solid transparent;border-right:%?20?% solid #fff;border-bottom:%?20?% solid transparent;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.code-bg[data-v-9f8400d6]{background-image:url(https://images.vinehoo.com/vinehoomini/v3/wine_party_order_detail/code_bg.png);background-size:cover}",""]),t.exports=e},a126:function(t,e,a){var n=a("bbdc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("703d0287",n,!0,{sourceMap:!1,shadowMode:!1})},a58c:function(t,e,a){"use strict";a.r(e);var n=a("55c2"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},a6f4:function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d3b7"),a("3ca3"),a("ddb0"),a("99af");var i=n(a("d0ff")),r=n(a("f07e")),o=n(a("c964")),s=n(a("f3f3")),u=a("26cb"),d={name:"store-order",data:function(){return{loading:!1,tabList:[{name:"待支付"},{name:"已支付"},{name:"已发货"},{name:"已完成"}],currentTabs:0,canInvoice:!1,hasGotOrderList:0,storeOrderList:[],storeOrderInfo:{},showCodeMask:!1,writeOffCode:"",showInvoiceMask:!1,page:1,limit:10,totalPage:1,loadStatus:"loadmore"}},computed:(0,s.default)({},(0,u.mapState)(["routeTable","storeOrderDetail"])),onShow:function(){this.init()},methods:(0,s.default)((0,s.default)({},(0,u.mapMutations)(["muStoreOrderDetail"])),{},{init:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.page=1,e.prev=1,e.next=4,Promise.all([t.getStoreOrderList(),t.getStoreCanInvoiceList()]);case 4:t.loading=!1,e.next=9;break;case 7:e.prev=7,e.t0=e["catch"](1);case 9:case"end":return e.stop()}}),e,null,[[1,7]])})))()},changeTabs:function(t){this.currentTabs=t,this.page=1,this.getStoreOrderList()},getStoreOrderList:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){var a,n,o,s,u;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.hasGotOrderList&&t.feedback.loading(),a={},a.page=t.page,a.limit=t.limit,a.status=t.currentTabs,e.next=7,t.$u.api.storeOrderList(a);case 7:n=e.sent,o=n.data,s=o.list,u=o.total,1==t.page?t.storeOrderList=s:t.storeOrderList=[].concat((0,i.default)(t.storeOrderList),(0,i.default)(s)),t.totalPage=Math.ceil(u/t.limit),t.loadStatus=t.page==t.totalPage?"nomore":"loadmore",t.hasGotOrderList=1,t.feedback.hideLoading(),uni.stopPullDownRefresh(),0===t.currentTabs&&t.storeOrderList.length>0&&2===t.storeOrderList[0].pay_type&&t.getWriteOffCode(t.storeOrderList[0]);case 16:case"end":return e.stop()}}),e)})))()},getStoreCanInvoiceList:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){var a;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.storeCanInvoiceList();case 2:a=e.sent,a.data.list.length>0&&(t.canInvoice=!0),console.log(a);case 5:case"end":return e.stop()}}),e)})))()},payNow:function(t){var e=this;this.feedback.loading({title:"拉取支付中...",icon:"success"}),uni.getStorage({key:"loginInfo",success:function(){var a=(0,o.default)((0,r.default)().mark((function a(n){var i,o,s,u,d,l;return(0,r.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,i=t.mainorderno,o=t.sid,s=t.pay_method,u={},0!=s){a.next=11;break}return console.log("---------------我是门店微信支付"),a.next=7,e.$u.api.storePay({orderno:i,sid:o,openid:n.data.openid});case 7:d=a.sent,u=d.data,a.next=16;break;case 11:return console.log("---------------我是银联支付"),a.next=14,e.$u.api.payMethod({main_order_no:i,payment_method:4,order_type:20,is_cross:0,open_id:n.data.openid});case 14:l=a.sent,u=l.data.mini_pay_info;case 16:e.requestPayment(u),a.next=21;break;case 19:a.prev=19,a.t0=a["catch"](0);case 21:case"end":return a.stop()}}),a,null,[[0,19]])})));return function(t){return a.apply(this,arguments)}}(),fail:function(){console.log("---获取用户信息失败")}})},requestPayment:function(t){var e=this;wx.requestPayment({timeStamp:t.timeStamp,nonceStr:t.nonceStr,package:t.package,signType:t.signType,paySign:t.paySign,success:function(t){e.feedback.toast({title:"支付成功~",icon:"success"}),e.page=1,e.currentTabs=1,e.getStoreOrderList()},fail:function(t){"requestPayment:fail cancel"==t.errMsg?e.feedback.toast({title:"您取消了支付~"}):e.feedback.toast({title:t.errMsg})},complete:function(t){e.feedback.hideLoading()}})},openStoreOrderDetail:function(t){this.muStoreOrderDetail(t),this.jump.navigateTo("/packageB/pages/store-order-detail/store-order-detail")},refundMoney:function(t){console.log(t),this.muStoreOrderDetail(t),this.jump.navigateTo("/packageB/pages/store-order-refund/store-order-refund")},getWriteOffCode:function(t){var e=this;return(0,o.default)((0,r.default)().mark((function a(){var n;return(0,r.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,e.storeOrderInfo=t,e.showCodeMask=!0,a.next=5,e.$u.api.writeOffCode({code:0==t.status?t.mainorderno:t.orderno});case 5:n=a.sent,e.writeOffCode=n.data,a.next=11;break;case 9:a.prev=9,a.t0=a["catch"](0);case 11:case"end":return a.stop()}}),a,null,[[0,9]])})))()},confirmReceipt:function(t){var e=this;return(0,o.default)((0,r.default)().mark((function a(){return(0,r.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return console.log(t),a.prev=1,a.next=4,e.$u.api.storeOrderConfirmReceipt({orderno:t.orderno});case 4:e.page=1,e.getStoreOrderList(),e.feedback.toast({title:"收货成功~",icon:"success"}),a.next=11;break;case 9:a.prev=9,a.t0=a["catch"](1);case 11:case"end":return a.stop()}}),a,null,[[1,9]])})))()}}),onPullDownRefresh:function(){this.page=1,this.getStoreOrderList()},onReachBottom:function(){this.page!=this.totalPage&&0!=this.totalPage&&(this.loadStatus="loading",this.page++,this.getStoreOrderList())}};e.default=d},a9e0:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},a("a4d3"),a("e01a"),a("d3b7"),a("d28b"),a("3ca3"),a("ddb0"),a("a630")},aab3:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},afb6:function(t,e,a){"use strict";var n=a("2da4"),i=a.n(n);i.a},b252:function(t,e,a){var n=a("6ab5");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("0c30beb4",n,!0,{sourceMap:!1,shadowMode:!1})},bbdc:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},bcb9:function(t,e,a){"use strict";var n=a("4883"),i=a.n(n);i.a},c07a:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return n}));var n={vhNavbar:a("12c6").default,uTabs:a("b14f").default,vhImage:a("ce7c").default,uButton:a("4f1b").default,uLoadmore:a("776f").default,vhEmpty:a("5ba4").default,uMask:a("e710").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"content"},[a("vh-navbar",{attrs:{"back-icon-color":"#FFF",background:{background:"#E80404"},title:"门店订单","title-color":"#FFF"}}),a("v-uni-view",{staticClass:"p-stic z-980 bt-s-01-eeeeee",style:{top:t.system.navigationBarHeight()+"px"}},[a("u-tabs",{attrs:{list:t.tabList,current:t.currentTabs,height:92,"font-size":28,"inactive-color":"#333","active-color":"#E80404","bar-width":36,"bar-height":8,"bar-style":{background:"linear-gradient(214deg, #FF8383 0%, #E70000 100%)"},"is-scroll":!1},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTabs.apply(void 0,arguments)}}})],1),t.loading?t._e():a("v-uni-view",{staticClass:"fade-in"},[a("v-uni-view",{},[t.storeOrderList.length?a("v-uni-view",{staticClass:"ptb-20-plr-00"},[t._l(t.storeOrderList,(function(e,n){return a("v-uni-view",{key:e.id,staticClass:"bg-ffffff b-rad-16 mr-24 mb-20 ml-24 pr-20 pb-32 pl-20",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.openStoreOrderDetail(e)}}},[a("v-uni-view",{},[a("v-uni-view",{staticClass:"d-flex j-sb a-center bb-s-01-eeeeee mb-32 ptb-32-plr-00"},[a("v-uni-view",{staticClass:"d-flex a-center"},[a("v-uni-view",{staticClass:"bg-li-21 b-tr-16-bl-16 ptb-04-plr-16 font-20 text-ffffff"},[t._v(t._s(1==e.send_type?"物流配送":2==e.send_type?"门店自提":"门店场饮"))]),a("v-uni-view",{staticClass:"w-max-430 ml-06 font-28 font-wei text-3 text-hidden-1"},[t._v(t._s(e.store_name))])],1),a("v-uni-view",{},[1==e.refund_status?a("v-uni-view",{staticClass:"font-24 text-e80404"},[t._v("退款中")]):2==e.refund_status?a("v-uni-view",{staticClass:"font-24 text-e80404"},[t._v("退款成功")]):3==e.refund_status&&1!==e.invoice_speed?a("v-uni-view",{staticClass:"font-24 text-e80404"},[t._v("退款失败")]):1==e.invoice_speed&&3==e.status?a("v-uni-view",{staticClass:"font-24 text-666"},[t._v("已开票")]):t._l(t.tabList,(function(n,i){return a("v-uni-view",{key:i,staticClass:"font-24 text-e80404"},[i==e.status?a("v-uni-text",[t._v(t._s(n.name))]):t._e()],1)}))],2)],1),t._l(e.goods_info,(function(e,n){return a("v-uni-view",{key:n,staticClass:"d-flex mb-20"},[a("vh-image",{attrs:{"loading-type":2,src:e.goods_images,width:136,height:136,"border-radius":6,mode:"aspectFit"}}),a("v-uni-view",{staticClass:"flex-1 d-flex flex-column j-sb ml-16"},[a("v-uni-view",{staticClass:"font-26 text-3 text-hidden-2 l-h-36"},[t._v(t._s(e.goods_name))]),a("v-uni-view",{staticClass:"d-flex j-sb"},[a("v-uni-text",{staticClass:"bg-eeeeee ptb-02-plr-12 b-rad-06 font-20 text-9"},[t._v(t._s(e.c_name))]),a("v-uni-text",{staticClass:"font-24 text-9"},[t._v("x"+t._s(e.pay_number))])],1)],1)],1)})),a("v-uni-view",{staticClass:"d-flex j-sb mt-32"},[a("v-uni-view",{staticClass:"font-24 text-9 l-h-34"},[t._v("订单号："+t._s(0==e.status?e.mainorderno:e.orderno))]),a("v-uni-view",{staticClass:"ml-20 text-3"},[a("v-uni-text",{staticClass:"font-18"},[t._v("实付款：")]),a("v-uni-text",{staticClass:"font-22 font-wei"},[t._v("¥")]),a("v-uni-text",{staticClass:"font-28 font-wei"},[t._v(t._s(0==e.status?e.pay_money:e.order_money))])],1)],1)],2),a("v-uni-view",{staticClass:"d-flex j-end a-center mt-32"},[0==e.status&&2==e.pay_type?a("v-uni-view",[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"180rpx",height:"54rpx",fontSize:"24rpx",fontWeight:"500",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.getWriteOffCode(e)}}},[t._v("出示核验码")])],1):t._e(),0==e.status&&1==e.pay_type?a("v-uni-view",{staticClass:"ml-20"},[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"180rpx",height:"54rpx",fontSize:"24rpx",fontWeight:"500",color:"#E80404",backgroundColor:"#fff",border:"1rpx solid #E80404"}},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.payNow(e)}}},[t._v("立即支付")])],1):t._e(),0!=e.status&&3!=e.status&&0==e.refund_status&&0==e.is_cup?a("v-uni-view",{staticClass:"ml-20"},[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"180rpx",height:"54rpx",fontSize:"24rpx",fontWeight:"500",color:"#E80404",backgroundColor:"#fff",border:"1rpx solid #E80404"}},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.refundMoney(e)}}},[t._v("申请退款")])],1):t._e(),1!=e.status||1==e.send_type||0!=e.refund_status&&3!=e.refund_status||1!=e.pay_type?t._e():a("v-uni-view",{staticClass:"ml-20"},[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"180rpx",height:"54rpx",fontSize:"24rpx",fontWeight:"500",color:"#E80404",backgroundColor:"#fff",border:"1rpx solid #E80404"}},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.getWriteOffCode(e)}}},[t._v("展示核销码")])],1),2==e.status&&1==e.send_type&&1==e.pay_type?a("v-uni-view",{staticClass:"ml-20"},[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"180rpx",height:"54rpx",fontSize:"24rpx",fontWeight:"500",color:"#E80404",backgroundColor:"#fff",border:"1rpx solid #E80404"}},on:{click:function(a){a.stopPropagation(),arguments[0]=a=t.$handleEvent(a),t.confirmReceipt(e)}}},[t._v("确认收货")])],1):t._e()],1)],1)})),a("u-loadmore",{attrs:{status:t.loadStatus}})],2):a("vh-empty",{attrs:{"padding-top":300,"padding-bottom":680,"image-src":"https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png",text:"暂无门店订单~"}})],1),t.canInvoice&&3==t.currentTabs?a("v-uni-view",{staticClass:"fade-in p-fixed z-100 bottom-148 right-32 w-86 h-86 bg-ffffff d-flex j-center a-center b-sh-00021200-022 b-rad-p50 font-24 text-3",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showInvoiceMask=!0}}},[t._v("发票")]):t._e(),a("v-uni-view",{},[a("u-mask",{attrs:{show:t.showInvoiceMask},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showInvoiceMask=!1}}},[a("v-uni-view",{staticClass:"p-rela h-vh-100 o-hid"},[a("v-uni-view",{staticClass:"p-abso bottom-160 right-140 bg-ffffff b-rad-10 w-200 h-224"},[a("v-uni-view",{staticClass:"h-112 d-flex j-center a-center bb-s-01-eeeeee font-28 font-wei text-3",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.jump.navigateTo(t.routeTable.pBStoreOrderInvoice)}}},[t._v("开发票")]),a("v-uni-view",{staticClass:"p-rela h-112 d-flex j-center a-center",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.jump.navigateTo(t.routeTable.pBStoreHistoryInvoice)}}},[a("v-uni-view",{staticClass:"triangle p-abso right-n-10 bottom-28"}),a("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("历史发票")])],1)],1)],1)],1),a("u-mask",{attrs:{show:t.showCodeMask},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showCodeMask=!1}}},[a("v-uni-view",{staticClass:"h-p100 d-flex j-center a-center"},[a("v-uni-view",{staticClass:"code-bg w-530 h-750"},[a("v-uni-view",{staticClass:"w-530 h-580 d-flex flex-column j-center a-center"},[a("vh-image",{attrs:{"loading-type":2,src:t.writeOffCode,width:300,height:300,duration:0}}),a("v-uni-text",{staticClass:"mt-40 font-36 font-wei text-3"},[t._v(t._s(0==t.storeOrderInfo.status?t.storeOrderInfo.mainorderno:t.storeOrderInfo.orderno))])],1),a("v-uni-view",{staticClass:"w-530 h-150 d-flex j-center a-center font-40 font-wei text-3"},[t._v("未使用")])],1)],1)],1)],1)],1)],1)},r=[]},ce7c:function(t,e,a){"use strict";a.r(e);var n=a("0efb"),i=a("ea26");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("eb5f");var o=a("f0c5"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"89d76102",null,!1,n["a"],void 0);e["default"]=s.exports},d0ff:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,n.default)(t)||(0,i.default)(t)||(0,r.default)(t)||(0,o.default)()};var n=s(a("4053")),i=s(a("a9e0")),r=s(a("dde1")),o=s(a("10eb"));function s(t){return t&&t.__esModule?t:{default:t}}},dc86:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-mask",class:{"u-mask-zoom":t.zoom,"u-mask-show":t.show},style:[t.maskStyle,t.zoomStyle],attrs:{"hover-stop-propagation":!0},on:{touchmove:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),function(){}.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.click.apply(void 0,arguments)}}},[t._t("default")],2)},i=[]},e4d5:function(t,e,a){"use strict";a.r(e);var n=a("8a04"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},e643:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return n}));var n={uLine:a("9ff7").default,uLoading:a("301a").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-load-more-wrap",style:{backgroundColor:t.bgColor,marginBottom:t.marginBottom+"rpx",marginTop:t.marginTop+"rpx",height:t.$u.addUnit(t.height)}},[a("u-line",{attrs:{color:"#d4d4d4",length:"50"}}),a("v-uni-view",{staticClass:"u-load-more-inner",class:"loadmore"==t.status||"nomore"==t.status?"u-more":""},[a("v-uni-view",{staticClass:"u-loadmore-icon-wrap"},[a("u-loading",{staticClass:"u-loadmore-icon",attrs:{color:t.iconColor,mode:"circle"==t.iconType?"circle":"flower",show:"loading"==t.status&&t.icon}})],1),a("v-uni-view",{staticClass:"u-line-1",class:["nomore"==t.status&&1==t.isDot?"u-dot-text":"u-more-text"],style:[t.loadTextStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loadMore.apply(void 0,arguments)}}},[t._v(t._s(t.showText))])],1),a("u-line",{attrs:{color:"#d4d4d4",length:"50"}})],1)},r=[]},e710:function(t,e,a){"use strict";a.r(e);var n=a("dc86"),i=a("5726");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("2071");var o=a("f0c5"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"f63a3092",null,!1,n["a"],void 0);e["default"]=s.exports},ea26:function(t,e,a){"use strict";a.r(e);var n=a("3dc3"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},eb5f:function(t,e,a){"use strict";var n=a("b252"),i=a.n(n);i.a},f074:function(t,e,a){"use strict";a.r(e);var n=a("7f1a"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},f2f9:function(t,e,a){"use strict";var n=a("a126"),i=a.n(n);i.a},f3e3:function(t,e,a){"use strict";a.r(e);var n=a("c07a"),i=a("1b14");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("bcb9"),a("41b0");var o=a("f0c5"),s=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"9f8400d6",null,!1,n["a"],void 0);e["default"]=s.exports},fa36:function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("f3f3"));a("a9e3"),a("b64b");var r={name:"u-mask",props:{show:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:""},customStyle:{type:Object,default:function(){return{}}},zoom:{type:Boolean,default:!0},duration:{type:[Number,String],default:300},maskClickAble:{type:Boolean,default:!0}},data:function(){return{zoomStyle:{transform:""},scale:"scale(1.2, 1.2)"}},watch:{show:function(t){t&&this.zoom?this.zoomStyle.transform="scale(1, 1)":!t&&this.zoom&&(this.zoomStyle.transform=this.scale)}},computed:{maskStyle:function(){var t={backgroundColor:"rgba(0, 0, 0, 0.6)"};return this.show?t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.mask:t.zIndex=-1,t.transition="all ".concat(this.duration/1e3,"s ease-in-out"),Object.keys(this.customStyle).length&&(t=(0,i.default)((0,i.default)({},t),this.customStyle)),t}},methods:{click:function(){this.maskClickAble&&this.$emit("click")}}};e.default=r},fa94:function(t,e,a){"use strict";var n=a("062a"),i=a.n(n);i.a}}]);