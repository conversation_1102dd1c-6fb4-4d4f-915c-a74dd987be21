(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageJ-pages-gift-card-gift-card"],{"062a":function(t,e,a){var i=a("aab3");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("2db15654",i,!0,{sourceMap:!1,shadowMode:!1})},"0e01":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var i={name:"vh-check",props:{width:{type:[String,Number],default:32},height:{type:[String,Number],default:32},checked:{type:Boolean,default:!1},checkedImg:{type:String,default:"https://images.vinehoo.com/vinehoomini/v3/comm/cir_sel.png"},unCheckedImg:{type:String,default:"https://images.vinehoo.com/vinehoomini/v3/comm/cir_unsel.png"},mode:{type:String,default:"aspectFill"},marginTop:{type:[String,Number],default:0}},computed:{checkStyle:function(){var t={};return t.marginTop=this.marginTop+"rpx",t.width=this.width+"rpx",t.height=this.height+"rpx",t}},methods:{click:function(){this.$emit("click")}}};e.default=i},"10eb":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},a("d9e2"),a("d401")},"12c6":function(t,e,a){"use strict";a.r(e);var i=a("51bd"),n=a("f074");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("f2f9");var o=a("f0c5"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"519170ec",null,!1,i["a"],void 0);e["default"]=s.exports},"144f":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"d-flex j-center",style:[this.outerEmpConStyle]},[e("v-uni-view",{staticClass:"p-rela",style:[this.innEmpConStyle]},[e("v-uni-image",{staticClass:"w-p100 h-p100",attrs:{src:this.imageSrc,mode:"aspectFill"}}),e("v-uni-view",{staticClass:"p-abso w-p100 d-flex j-center font-28 text-9",style:[this.textStyle]},[this._v(this._s(this.text))])],1)],1)},n=[]},2036:function(t,e,a){"use strict";a.r(e);var i=a("d4f3"),n=a("28ff");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);var o=a("f0c5"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"0c73edd7",null,!1,i["a"],void 0);e["default"]=s.exports},"28ff":function(t,e,a){"use strict";a.r(e);var i=a("0e01"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"2da4":function(t,e,a){var i=a("39e4");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("64a51bd6",i,!0,{sourceMap:!1,shadowMode:!1})},"39e4":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-load-more-wrap[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center}.u-load-more-inner[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:0 %?12?%}.u-more[data-v-15067509]{position:relative;display:flex;flex-direction:row;justify-content:center}.u-dot-text[data-v-15067509]{font-size:%?28?%}.u-loadmore-icon-wrap[data-v-15067509]{margin-right:%?8?%}.u-loadmore-icon[data-v-15067509]{display:flex;flex-direction:row;align-items:center;justify-content:center}',""]),t.exports=e},"3f1b":function(t,e,a){"use strict";var i=a("be7d"),n=a.n(i);n.a},4053:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(a("b680"))},"4f1b":function(t,e,a){"use strict";a.r(e);var i=a("825d"),n=a("8e1d");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("fa94");var o=a("f0c5"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"4ed92bb2",null,!1,i["a"],void 0);e["default"]=s.exports},"514a":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={vhNavbar:a("12c6").default,uTabs:a("b14f").default,vhEmpty:a("5ba4").default,uLoadmore:a("776f").default,uPopup:a("c4b0").default,vhCheck:a("2036").default,uButton:a("4f1b").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"gift-card-container bg-ffffff"},[""==t.$vhFrom||"next"==t.$vhFrom?a("v-uni-view",{},[a("vh-navbar",{attrs:{title:"礼品卡"}})],1):t._e(),a("u-tabs",{attrs:{"is-scroll":!1,list:t.mainTabs,current:t.mainTabIndex,"bar-width":"36","bar-height":"8","font-size":"32","inactive-color":"#999","active-color":"#333","bar-style":{background:"linear-gradient(214deg, #FF8383 0%, #E70000 100%)"}},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onMainTabChange.apply(void 0,arguments)}}}),"buy"===t.activeTab?a("v-uni-view",{staticClass:"tab-content"},[a("v-uni-view",{staticClass:"buy-steps flex-sb-c"},[a("v-uni-view",{staticClass:"step-item flex-c-c"},[a("v-uni-view",{staticClass:"step-circle flex-c-c text-ffffff",class:t.currentStep>=1?"bg-e80404":"bg-d8d8d8"},[t._v("1")]),a("v-uni-text",{staticClass:"step-text font-26",class:t.currentStep>=1?"text-e80404":"text-9"},[t._v("选择礼品卡")])],1),a("v-uni-view",{staticClass:"step-line",class:t.currentStep>=2?"bg-e80404":"bg-d8d8d8"}),a("v-uni-view",{staticClass:"step-item flex-c-c"},[a("v-uni-view",{staticClass:"step-circle flex-c-c text-ffffff",class:t.currentStep>=2?"bg-e80404":"bg-d8d8d8"},[t._v("2")]),a("v-uni-text",{staticClass:"step-text font-26",class:t.currentStep>=2?"text-e80404":"text-9"},[t._v("支付购买")])],1),a("v-uni-view",{staticClass:"step-line",class:t.currentStep>=3?"bg-e80404":"bg-d8d8d8"}),a("v-uni-view",{staticClass:"step-item flex-c-c"},[a("v-uni-view",{staticClass:"step-circle flex-c-c text-ffffff",class:t.currentStep>=3?"bg-e80404":"bg-d8d8d8"},[t._v("3")]),a("v-uni-text",{staticClass:"step-text font-26",class:t.currentStep>=3?"text-e80404":"text-9"},[t._v("赠送好友")])],1)],1),a("v-uni-view",{staticClass:"gift-card-list"},[t.rechargeOptions.length>0?a("v-uni-view",t._l(t.chunkedCards,(function(e,i){return a("v-uni-view",{key:i,staticClass:"card-row flex-sb-c"},t._l(e,(function(e){return a("v-uni-view",{key:e.id,staticClass:"card-item",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.selectCard(e)}}},[a("v-uni-view",{staticClass:"card-inner ",style:{backgroundImage:"url("+e.background+")",backgroundSize:"cover"}})],1)})),1)})),1):t._e()],1)],1):t._e(),"my"===t.activeTab?a("v-uni-view",{staticClass:"tab-content"},["my"===t.activeTab?a("u-tabs",{attrs:{"is-scroll":!1,list:t.subTabs,current:t.subTabIndex,"bar-width":"36","bar-height":"8","font-size":"28","inactive-color":"#999","active-color":"#333","bar-style":{background:"linear-gradient(214deg, #FF8383 0%, #E70000 100%)"}},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onSubTabChange.apply(void 0,arguments)}}}):t._e(),a("v-uni-view",{staticClass:"my-gift-card-list"},["available"===t.activeSubTab?a("v-uni-view",[t.myGiftCards.available.length>0?a("v-uni-view",t._l(t.myGiftCards.available,(function(e){return a("v-uni-view",{key:e.card_no,staticClass:"gift-card-item"},[a("v-uni-view",{staticClass:"gift-card-image",style:{backgroundImage:"url("+e.background+")"}}),a("v-uni-view",{staticClass:"card-info-row"},[a("v-uni-view",{staticClass:"card-number-info"},[a("v-uni-text",{staticClass:"card-label"},[t._v("卡号：")]),a("v-uni-text",{staticClass:"card-number"},[t._v(t._s(e.card_no))])],1),a("v-uni-view",{staticClass:"card-date-info"},[a("v-uni-image",{staticClass:"clock-icon",attrs:{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZT0iIzk5OTk5OSIgc3Ryb2tlLXdpZHRoPSIxLjUiLz4KPHBhdGggZD0iTTggNFY4TDEwLjUgMTAuNSIgc3Ryb2tlPSIjOTk5OTk5IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=",mode:"aspectFit"}}),a("v-uni-text",{staticClass:"date-text"},[t._v(t._s(e.create_time.split(" ")[0]))])],1)],1),a("v-uni-view",{staticClass:"button-row"},[a("v-uni-view",{staticClass:"btn-self-use",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.useCard(e)}}},[a("v-uni-text",{staticClass:"btn-text-self"},[t._v("自己充")])],1),a("v-uni-view",{staticClass:"btn-gift-send",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.giftCard(e)}}},[a("v-uni-text",{staticClass:"btn-text-gift"},[t._v("赠送他人")])],1)],1)],1)})),1):a("vh-empty",{attrs:{"padding-top":52,"padding-bottom":400,"image-src":"https://images.vinehoo.com/vinehoomini/v3/empty/emp_cou.png",text:"暂无可赠送礼品卡~","text-bottom":0}})],1):t._e(),"sent"===t.activeSubTab?a("v-uni-view",[t.myGiftCards.sent.length>0?a("v-uni-view",t._l(t.myGiftCards.sent,(function(e){return a("v-uni-view",{key:e.card_no,staticClass:"gift-card-item"},[a("v-uni-view",{staticClass:"gift-card-image",style:{backgroundImage:"url("+e.background+")",filter:"grayscale(1)",opacity:"0.6"}}),a("v-uni-view",{staticClass:"card-info-row"},[a("v-uni-view",{staticClass:"card-number-info"},[a("v-uni-text",{staticClass:"card-label"},[t._v("卡号：")]),a("v-uni-text",{staticClass:"card-number"},[t._v(t._s(e.card_no))])],1),a("v-uni-view",{staticClass:"card-date-info"},[a("v-uni-image",{staticClass:"clock-icon",attrs:{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZT0iIzk5OTk5OSIgc3Ryb2tlLXdpZHRoPSIxLjUiLz4KPHBhdGggZD0iTTggNFY4TDEwLjUgMTAuNSIgc3Ryb2tlPSIjOTk5OTk5IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=",mode:"aspectFit"}}),a("v-uni-text",{staticClass:"date-text"},[t._v(t._s(e.transfer_time.split(" ")[0]))])],1)],1)],1)})),1):a("vh-empty",{attrs:{"padding-top":52,"padding-bottom":400,"image-src":"https://images.vinehoo.com/vinehoomini/v3/empty/emp_cou.png",text:"暂无已赠送礼品卡~","text-bottom":0}})],1):t._e(),"used"===t.activeSubTab?a("v-uni-view",[t.myGiftCards.used.length>0?a("v-uni-view",t._l(t.myGiftCards.used,(function(e){return a("v-uni-view",{key:e.card_no,staticClass:"gift-card-item"},[a("v-uni-view",{staticClass:"gift-card-image",style:{backgroundImage:"url("+e.background+")",filter:"grayscale(1)",opacity:"0.6"}}),a("v-uni-view",{staticClass:"card-info-row"},[a("v-uni-view",{staticClass:"card-number-info"},[a("v-uni-text",{staticClass:"card-label"},[t._v("卡号：")]),a("v-uni-text",{staticClass:"card-number"},[t._v(t._s(e.card_no))])],1),a("v-uni-view",{staticClass:"card-date-info"},[a("v-uni-image",{staticClass:"clock-icon",attrs:{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZT0iIzk5OTk5OSIgc3Ryb2tlLXdpZHRoPSIxLjUiLz4KPHBhdGggZD0iTTggNFY4TDEwLjUgMTAuNSIgc3Ryb2tlPSIjOTk5OTk5IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=",mode:"aspectFit"}}),a("v-uni-text",{staticClass:"date-text"},[t._v(t._s(e.use_time.split(" ")[0]))])],1)],1)],1)})),1):a("vh-empty",{attrs:{"padding-top":52,"padding-bottom":400,"image-src":"https://images.vinehoo.com/vinehoomini/v3/empty/emp_cou.png",text:"暂无已使用礼品卡~","text-bottom":0}})],1):t._e()],1),"nomore"!==t.loadStatus?a("u-loadmore",{attrs:{status:t.loadStatus}}):t._e()],1):t._e(),a("u-popup",{attrs:{mode:"bottom",width:"100%","mask-close-able":!0,"border-radius":"20"},model:{value:t.showUseCardPopup,callback:function(e){t.showUseCardPopup=e},expression:"showUseCardPopup"}},[a("v-uni-view",{staticClass:"use-card-popup"},[a("v-uni-view",{staticClass:"gift-card-info"},[a("v-uni-text",{staticClass:"gift-card-title font-26 text-9"},[t._v("充值")]),a("v-uni-view",{staticClass:"use-description font-28 text-3 mt-20 ml-10"},[a("v-uni-text",[t._v("使用一张"+t._s(t.selectedUseCard.amount)+"元储值卡进行充值。")])],1),a("v-uni-view",{staticClass:"bg-ffffff p-rela d-flex  a-center pt-20 pb-32"},[a("vh-check",{attrs:{checked:t.isAgreeReg},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.isAgreeReg=!t.isAgreeReg}}}),a("v-uni-text",{staticClass:"ml-10 font-24",class:t.isAgreeReg?"text-3":"text-9"},[t._v("我已阅读并同意")]),a("v-uni-text",{staticClass:"text-e80404 font-24",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.h5Jump(t.agreementPrefix+"/storedValueCardProtocol",t.$vhFrom)}}},[t._v("《储值协议》")])],1),a("v-uni-view",{staticClass:"bg-ffffff d-flex j-center pb-32"},[a("u-button",{attrs:{disabled:!t.isAgreeReg,shape:"circle",ripple:!0,"ripple-bg-color":"#ffffff","custom-style":{width:"646rpx",color:"#fff",backgroundColor:t.isAgreeReg?"#E80404":"#FCE4E3",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmUseCard.apply(void 0,arguments)}}},[t._v("立即充值")])],1)],1)],1)],1)],1)},r=[]},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={uIcon:a("e5e1").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},r=[]},"55c2":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var i={name:"vh-empty",props:{bgColor:{type:String,default:"#FFF"},paddingTop:{type:[String,Number],default:0},paddingBottom:{type:[String,Number],default:0},width:{type:[String,Number],default:440},height:{type:[String,Number],default:360},imageSrc:{type:String,default:""},textBottom:{type:[String,Number],default:46},text:{type:String,default:""}},computed:{outerEmpConStyle:function(){return{backgroundColor:this.bgColor,paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},innEmpConStyle:function(){return{width:this.width+"rpx",height:this.height+"rpx"}},textStyle:function(){return{bottom:this.textBottom+"rpx"}}}};e.default=i},"5ba4":function(t,e,a){"use strict";a.r(e);var i=a("144f"),n=a("a58c");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);var o=a("f0c5"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"55488dce",null,!1,i["a"],void 0);e["default"]=s.exports},"776f":function(t,e,a){"use strict";a.r(e);var i=a("e643"),n=a("e4d5");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("afb6");var o=a("f0c5"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"15067509",null,!1,i["a"],void 0);e["default"]=s.exports},"7ad3":function(t,e,a){"use strict";a.r(e);var i=a("90be"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"7f1a":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var r=a("26cb"),o=uni.getSystemInfoSync(),s={},d={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,n.default)((0,n.default)({},(0,r.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,i=e.pEAddressManagement,n=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(i)||t.includes(n))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=d},"825d":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?a("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},n=[]},8445:function(t,e,a){"use strict";a.r(e);var i=a("514a"),n=a("7ad3");for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);a("3f1b");var o=a("f0c5"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"51c0aa73",null,!1,i["a"],void 0);e["default"]=s.exports},"8a04":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var i={name:"u-loadmore",props:{bgColor:{type:String,default:"transparent"},icon:{type:Boolean,default:!0},fontSize:{type:String,default:"28"},color:{type:String,default:"#606266"},status:{type:String,default:"loadmore"},iconType:{type:String,default:"circle"},loadText:{type:Object,default:function(){return{loadmore:"加载更多",loading:"正在加载...",nomore:"没有更多了"}}},isDot:{type:Boolean,default:!1},iconColor:{type:String,default:"#b7b7b7"},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},height:{type:[String,Number],default:"auto"}},data:function(){return{dotText:"●"}},computed:{loadTextStyle:function(){return{color:this.color,fontSize:this.fontSize+"rpx",position:"relative",zIndex:1,backgroundColor:this.bgColor}},cricleStyle:function(){return{borderColor:"#e5e5e5 #e5e5e5 #e5e5e5 ".concat(this.circleColor)}},flowerStyle:function(){return{}},showText:function(){var t="";return t="loadmore"==this.status?this.loadText.loadmore:"loading"==this.status?this.loadText.loading:"nomore"==this.status&&this.isDot?this.dotText:this.loadText.nomore,t}},methods:{loadMore:function(){"loadmore"==this.status&&this.$emit("loadmore")}}};e.default=i},"8e1d":function(t,e,a){"use strict";a.r(e);var i=a("9476"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},"90be":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("14d9"),a("fb6a"),a("c740"),a("99af");var n=i(a("d0ff")),r=i(a("f07e")),o=i(a("c964")),s=i(a("f3f3")),d=a("26cb"),c={data:function(){return{activeTab:"buy",activeSubTab:"available",currentStep:0,selectedCard:null,loading:!1,loadStatus:"loadmore",page:1,limit:10,totalPage:1,rechargeOptions:[],myGiftCards:{available:[],sent:[],used:[]},showUseCardPopup:!1,selectedUseCard:{},isAgreeReg:!1,mainTabs:[{name:"购买礼品卡",key:"buy"},{name:"我的礼品卡",key:"my"}],mainTabIndex:0,subTabs:[{name:"可赠送",key:"available"},{name:"已送出",key:"sent"},{name:"已使用",key:"used"}],subTabIndex:0}},computed:(0,s.default)((0,s.default)({},(0,d.mapState)(["routeTable","agreementPrefix"])),{},{chunkedCards:function(){for(var t=[],e=0;e<this.rechargeOptions.length;e+=2)t.push(this.rechargeOptions.slice(e,e+2));return t}}),onShow:function(){var t=this;this.login.isLoginV2(this.$vhFrom).then((function(){t.init()}))},onLoad:function(t){var e=this;t&&(t.tab&&(this.activeTab=t.tab),uni.setNavigationBarTitle({title:"礼品卡"}),window.vhAppSendMessage=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};console.log("礼品卡obj",t),"string"===typeof t&&(t=JSON.parse(t));var a=t,i=a.type,n=void 0===i?1:i;if(1===n)return{type:n,data:"酒云网"};if(4===n){var r=JSON.parse(t.data).images;console.log("uploadPictureInfo",r),uploadPictureInfo&&uploadPictureInfo(r),window.uploadPictureInfo=null}return 6===n&&(console.log("卧室礼品卡支付成功"),e.activeTab="my",e.mainTabIndex=1,e.subTabIndex=0,e.page=1,e.requestMyGiftCards()),8===n&&onPullAppPayFail&&onPullAppPayFail(),10==n&&indexTopRefresh&&indexTopRefresh(),11==n&&e.handlePhotoMessage(t),""})},methods:{init:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:"my"==t.tab?(console.log("showMyGift"),t.page=1,t.requestMyGiftCards()):t.requestGiftCardList();case 1:case"end":return e.stop()}}),e)})))()},switchTab:function(t){this.activeTab=t,this.mainTabIndex=this.mainTabs.findIndex((function(e){return e.key===t})),"my"===t?(this.page=1,this.requestMyGiftCards()):this.requestGiftCardList()},requestGiftCardList:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){var a,i,n;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.loading=!0,e.prev=1,e.next=4,t.$u.api.giftCardList({page:1,limit:20,type:2});case 4:a=e.sent,i=a.data,i.total,n=i.list,t.rechargeOptions=n||[],e.next=13;break;case 9:e.prev=9,e.t0=e["catch"](1),console.error("获取礼品卡列表失败",e.t0),t.rechargeOptions=[];case 13:return e.prev=13,t.loading=!1,e.finish(13);case 16:case"end":return e.stop()}}),e,null,[[1,9,13,16]])})))()},requestMyGiftCards:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){var a,i,o,s,d;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.loading=!0,t.loadStatus="loading",e.prev=2,a=1,"sent"===t.activeSubTab&&(a=2),"used"===t.activeSubTab&&(a=3),e.next=8,t.$u.api.myGiftCardsList({page:t.page,limit:t.limit,type:a});case 8:i=e.sent,o=i.data,s=o.total,d=o.list,1===t.page?1===a?t.myGiftCards.available=d||[]:2===a?t.myGiftCards.sent=d||[]:3===a&&(t.myGiftCards.used=d||[]):1===a?t.myGiftCards.available=[].concat((0,n.default)(t.myGiftCards.available),(0,n.default)(d||[])):2===a?t.myGiftCards.sent=[].concat((0,n.default)(t.myGiftCards.sent),(0,n.default)(d||[])):3===a&&(t.myGiftCards.used=[].concat((0,n.default)(t.myGiftCards.used),(0,n.default)(d||[]))),t.totalPage=Math.ceil(s/t.limit),t.loadStatus=t.page>=t.totalPage?"nomore":"loadmore",e.next=20;break;case 15:e.prev=15,e.t0=e["catch"](2),console.error("获取我的礼品卡列表失败",e.t0),"available"===t.activeSubTab?t.myGiftCards.available=[]:"sent"===t.activeSubTab?t.myGiftCards.sent=[]:"used"===t.activeSubTab&&(t.myGiftCards.used=[]),t.loadStatus="loadmore";case 20:return e.prev=20,t.loading=!1,e.finish(20);case 23:case"end":return e.stop()}}),e,null,[[2,15,20,23]])})))()},switchSubTab:function(t){this.activeSubTab=t,this.subTabIndex=this.subTabs.findIndex((function(e){return e.key===t})),this.page=1,this.requestMyGiftCards()},selectCard:function(t){this.selectedCard=t,this.jump.appAndMiniJump(2,"/packageJ/pages/gift-card/select-gift-card?amount=".concat(t.price,"&id=").concat(t.id),this.$vhFrom)},useCard:function(t){this.selectedUseCard=t,this.showUseCardPopup=!0},confirmUseCard:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.loading=!0,e.prev=1,e.next=4,t.$u.api.giftCarduseVirtual({card_no:t.selectedUseCard.card_no});case 4:e.sent,t.feedback.toast({title:"充值成功"}),t.page=1,t.requestMyGiftCards(),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](1),console.error("获取礼品卡列表失败",e.t0);case 13:return e.prev=13,t.loading=!1,e.finish(13);case 16:t.showUseCardPopup=!1;case 17:case"end":return e.stop()}}),e,null,[[1,10,13,16]])})))()},giftCard:function(t){this.jump.appAndMiniJump(2,"/packageJ/pages/gift-card/send-gift-card?card_no=".concat(t.card_no,"&amount=").concat(t.amount),this.$vhFrom)},onMainTabChange:function(t){this.mainTabIndex=t,this.activeTab=this.mainTabs[t].key,"my"===this.activeTab?(this.page=1,this.requestMyGiftCards()):this.requestGiftCardList()},onSubTabChange:function(t){this.subTabIndex=t,this.activeSubTab=this.subTabs[t].key,this.page=1,this.requestMyGiftCards()}},onPullDownRefresh:function(){this.page=1,"buy"===this.activeTab?this.requestGiftCardList():this.requestMyGiftCards(),uni.stopPullDownRefresh()},onReachBottom:function(){"my"===this.activeTab&&this.page<this.totalPage&&(console.log("3333333355555555555"),this.page++,this.loadStatus="loading",this.requestMyGiftCards())}};e.default=c},9476:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3"),a("c975"),a("d3b7"),a("ac1f");var i={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(a){var i=a[0];if(i.width&&i.width&&(i.targetWidth=i.height>i.width?i.height:i.width,i.targetWidth)){e.fields=i;var n,r;n=t.touches[0].clientX,r=t.touches[0].clientY,e.rippleTop=r-i.top-i.targetWidth/2,e.rippleLeft=n-i.left-i.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var a="";a=uni.createSelectorQuery().in(t),a.select(".u-btn").boundingClientRect(),a.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=i},a126:function(t,e,a){var i=a("bbdc");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("703d0287",i,!0,{sourceMap:!1,shadowMode:!1})},a58c:function(t,e,a){"use strict";a.r(e);var i=a("55c2"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},a9e0:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},a("a4d3"),a("e01a"),a("d3b7"),a("d28b"),a("3ca3"),a("ddb0"),a("a630")},aab3:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},afb6:function(t,e,a){"use strict";var i=a("2da4"),n=a.n(i);n.a},bbdc:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},be7d:function(t,e,a){var i=a("d3db");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("59e5f7e8",i,!0,{sourceMap:!1,shadowMode:!1})},d0ff:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,n.default)(t)||(0,r.default)(t)||(0,o.default)()};var i=s(a("4053")),n=s(a("a9e0")),r=s(a("dde1")),o=s(a("10eb"));function s(t){return t&&t.__esModule?t:{default:t}}},d3db:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,".gift-card-container[data-v-51c0aa73]{min-height:100vh}.gift-card-header[data-v-51c0aa73]{padding:%?30?% 0;text-align:center}\n\n/* 主标签页样式 */.tab-container[data-v-51c0aa73]{width:100%;display:flex;border-bottom:%?1?% solid #f5f5f5}.tab-item[data-v-51c0aa73]{flex:1;position:relative;display:flex;flex-direction:column;align-items:center;padding:%?20?% 0}.tab-text[data-v-51c0aa73]{font-size:%?30?%;padding-bottom:%?10?%}.tab-line[data-v-51c0aa73]{position:absolute;bottom:0;width:%?60?%;height:%?4?%;border-radius:%?2?%}\n\n/* 购买礼品卡内容样式 */.tab-content[data-v-51c0aa73]{padding:%?20?% %?0?%}.buy-steps[data-v-51c0aa73]{padding:%?40?% %?50?%}.step-item[data-v-51c0aa73]{flex-direction:column}.step-circle[data-v-51c0aa73]{width:%?50?%;height:%?50?%;border-radius:%?25?%;margin-bottom:%?10?%}.step-line[data-v-51c0aa73]{flex:1;height:%?2?%;margin:0 %?15?%;margin-bottom:%?30?%}.step-text[data-v-51c0aa73]{margin-top:%?10?%}\n\n/* 礼品卡列表样式 */.gift-card-list[data-v-51c0aa73]{padding:%?20?% %?30?%}.card-row[data-v-51c0aa73]{margin-bottom:%?30?%}.card-item[data-v-51c0aa73]{width:48%;aspect-ratio:1.57;border-radius:%?12?%;overflow:hidden}.card-inner[data-v-51c0aa73]{width:100%;height:100%;display:flex;justify-content:center;align-items:center;padding:%?20?%}.check-process[data-v-51c0aa73]{justify-content:flex-end;margin-top:%?20?%}\n\n/* 我的礼品卡标签页样式 */.sub-tab-container[data-v-51c0aa73]{padding:%?10?% 0;border-bottom:%?1?% solid #f5f5f5}.sub-tab-item[data-v-51c0aa73]{flex:1;position:relative;display:flex;flex-direction:column;align-items:center;padding:%?15?% 0}.sub-tab-text[data-v-51c0aa73]{font-size:%?28?%;padding-bottom:%?8?%}.sub-tab-line[data-v-51c0aa73]{position:absolute;bottom:0;width:%?50?%;height:%?4?%;border-radius:%?2?%}\n\n/* 我的礼品卡列表样式 */.my-gift-card-list[data-v-51c0aa73]{padding:%?30?% %?30?%}\n\n/* 严格按照图片实现的礼品卡样式 */.gift-card-item[data-v-51c0aa73]{width:100%;margin-bottom:%?20?%;box-shadow:0 %?4?% %?20?% rgba(0,0,0,.08);overflow:hidden;padding:%?10?% %?10?% %?20?% %?10?%;border-radius:%?16?%}\n\n/* 礼品卡图片 */.gift-card-image[data-v-51c0aa73]{width:100%;aspect-ratio:503/320;background-size:cover;background-position:50%;background-repeat:no-repeat;border-radius:%?16?%;position:relative;margin-bottom:%?20?%}\n\n/* 状态标签 */.gift-card-tag[data-v-51c0aa73]{position:absolute;top:%?20?%;right:%?20?%;background:rgba(0,0,0,.6);border-radius:%?20?%;padding:%?8?% %?16?%}.gift-card-tag.sent[data-v-51c0aa73],\n.gift-card-tag.used[data-v-51c0aa73]{background:hsla(0,0%,60%,.8)}.tag-text[data-v-51c0aa73]{color:#fff;font-size:%?24?%}\n\n/* 卡片信息行 */.card-info-row[data-v-51c0aa73]{display:flex;justify-content:space-between;align-items:center;margin-bottom:%?20?%;padding:%?14?% %?10?%}.card-number-info[data-v-51c0aa73]{display:flex;align-items:center}.card-label[data-v-51c0aa73]{font-size:%?24?%;color:#666;margin-right:%?4?%}.card-number[data-v-51c0aa73]{font-size:%?24?%;color:#333}.card-date-info[data-v-51c0aa73]{display:flex;align-items:center}.clock-icon[data-v-51c0aa73]{width:%?24?%;height:%?24?%;margin-right:%?8?%}.date-text[data-v-51c0aa73]{font-size:%?24?%;color:#666}\n\n/* 按钮行 */.button-row[data-v-51c0aa73]{display:flex;gap:%?20?%;padding:0 %?10?% %?15?%}\n\n/* 自己充按钮 */.btn-self-use[data-v-51c0aa73]{flex:1;height:%?72?%;background:#fff;border:%?2?% solid #e80404;border-radius:%?18?%;display:flex;align-items:center;justify-content:center}.btn-text-self[data-v-51c0aa73]{font-size:%?28?%;color:#e80404}\n\n/* 赠送他人按钮 */.btn-gift-send[data-v-51c0aa73]{flex:1;height:%?72?%;background:#e80404;border-radius:%?18?%;display:flex;align-items:center;justify-content:center}.btn-text-gift[data-v-51c0aa73]{font-size:%?28?%;color:#fff}\n\n/* 禁用按钮 */.btn-disabled[data-v-51c0aa73]{width:100%;height:%?80?%;background:#f5f5f5;border:%?2?% solid #e0e0e0;border-radius:%?40?%;display:flex;align-items:center;justify-content:center}.btn-text-disabled[data-v-51c0aa73]{font-size:%?28?%;color:#999}\n\n/* 按钮点击效果 */.btn-self-use[data-v-51c0aa73]:active,\n.btn-gift-send[data-v-51c0aa73]:active{-webkit-transform:scale(.96);transform:scale(.96);transition:-webkit-transform .1s ease;transition:transform .1s ease;transition:transform .1s ease,-webkit-transform .1s ease}\n\n/* 保留一些通用样式 */.ml-20[data-v-51c0aa73]{margin-left:%?20?%}.empty-tip[data-v-51c0aa73]{height:%?300?%}\n\n/* 自己充值弹出层样式 */.use-card-popup[data-v-51c0aa73]{width:100%;padding:%?40?% %?30?%;background-color:#fff}.gift-card-info[data-v-51c0aa73]{padding:%?20?%}.gift-card-title[data-v-51c0aa73]{margin-bottom:%?20?%}.gift-card-display[data-v-51c0aa73]{width:100%;height:%?240?%;display:flex;align-items:flex-end;justify-content:flex-end;padding:%?20?%;border-radius:%?12?%}.mt-20[data-v-51c0aa73]{margin-top:%?20?%}.agreement-row[data-v-51c0aa73]{display:flex;align-items:center}.use-button[data-v-51c0aa73]{width:100%;height:%?80?%;border-radius:%?40?%}.bg-e80404[data-v-51c0aa73]{background-color:#e80404}",""]),t.exports=e},d4f3:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"d-flex"},[a("v-uni-image",{staticClass:"fade-in",style:[t.checkStyle],attrs:{src:t.checked?t.checkedImg:t.unCheckedImg,mode:t.mode},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click()}}})],1)},n=[]},e4d5:function(t,e,a){"use strict";a.r(e);var i=a("8a04"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},e643:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return i}));var i={uLine:a("9ff7").default,uLoading:a("301a").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-load-more-wrap",style:{backgroundColor:t.bgColor,marginBottom:t.marginBottom+"rpx",marginTop:t.marginTop+"rpx",height:t.$u.addUnit(t.height)}},[a("u-line",{attrs:{color:"#d4d4d4",length:"50"}}),a("v-uni-view",{staticClass:"u-load-more-inner",class:"loadmore"==t.status||"nomore"==t.status?"u-more":""},[a("v-uni-view",{staticClass:"u-loadmore-icon-wrap"},[a("u-loading",{staticClass:"u-loadmore-icon",attrs:{color:t.iconColor,mode:"circle"==t.iconType?"circle":"flower",show:"loading"==t.status&&t.icon}})],1),a("v-uni-view",{staticClass:"u-line-1",class:["nomore"==t.status&&1==t.isDot?"u-dot-text":"u-more-text"],style:[t.loadTextStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loadMore.apply(void 0,arguments)}}},[t._v(t._s(t.showText))])],1),a("u-line",{attrs:{color:"#d4d4d4",length:"50"}})],1)},r=[]},f074:function(t,e,a){"use strict";a.r(e);var i=a("7f1a"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);e["default"]=n.a},f2f9:function(t,e,a){"use strict";var i=a("a126"),n=a.n(i);n.a},fa94:function(t,e,a){"use strict";var i=a("062a"),n=a.n(i);n.a}}]);