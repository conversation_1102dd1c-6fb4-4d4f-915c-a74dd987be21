(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageE-pages-address-add-address-add"],{"12c6":function(e,t,i){"use strict";i.r(t);var a=i("51bd"),n=i("f074");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);i("f2f9");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);t["default"]=o.exports},1672:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3");var a={name:"u-switch",props:{loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},size:{type:[Number,String],default:50},activeColor:{type:String,default:"#2979ff"},inactiveColor:{type:String,default:"#ffffff"},value:{type:Boolean,default:!1},vibrateShort:{type:Boolean,default:!1},activeValue:{type:[Number,String,Boolean],default:!0},inactiveValue:{type:[Number,String,Boolean],default:!1}},data:function(){return{}},computed:{switchStyle:function(){var e={};return e.fontSize=this.size+"rpx",e.backgroundColor=this.value?this.activeColor:this.inactiveColor,e},loadingColor:function(){return this.value?this.activeColor:null}},methods:{onClick:function(){var e=this;this.disabled||this.loading||(this.vibrateShort&&uni.vibrateShort(),this.$emit("input",!this.value),this.$nextTick((function(){e.$emit("change",e.value?e.activeValue:e.inactiveValue)})))}}};t.default=a},1677:function(e,t,i){"use strict";i.r(t);var a=i("f440"),n=i("724e");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"4efb453b",null,!1,a["a"],void 0);t["default"]=o.exports},"1c5a":function(e,t,i){"use strict";var a=i("2e0a"),n=i.n(a);n.a},"27c5":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3");var a={name:"vh-loading",props:{width:{type:String,default:"100%"},height:{type:String,default:"100vh"},mode:{type:String,default:"circle"},size:{type:Number,default:50},loadingColor:{type:String,default:"#E80404"},showText:{type:Boolean,default:!1},loadingText:{type:String,default:"加载中..."},textSize:{type:[String,Number],default:24}},computed:{loadingContainerStyle:function(){return{width:this.width,height:this.height}},loadingTextStyle:function(){return{color:this.loadingColor,fontSize:this.textSize}}}};t.default=a},"2c73":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return a}));var a={uLoading:i("301a").default},n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"d-flex flex-column j-center a-center",style:[e.loadingContainerStyle]},[i("u-loading",{attrs:{mode:e.mode,size:e.size,color:e.loadingColor}}),e.showText?i("v-uni-view",{staticClass:"mt-10 font-24",style:[e.loadingTextStyle]},[e._v(e._s(e.loadingText))]):e._e()],1)},r=[]},"2e0a":function(e,t,i){var a=i("cc5e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("4f06").default;n("55db19e3",a,!0,{sourceMap:!1,shadowMode:!1})},"301a":function(e,t,i){"use strict";i.r(t);var a=i("3b5c"),n=i("ffc6");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);i("b515");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"19cf7fca",null,!1,a["a"],void 0);t["default"]=o.exports},"3b5c":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){}));var a=function(){var e=this.$createElement,t=this._self._c||e;return this.show?t("v-uni-view",{staticClass:"u-loading",class:"circle"==this.mode?"u-loading-circle":"u-loading-flower",style:[this.cricleStyle]}):this._e()},n=[]},4801:function(e,t,i){"use strict";i.r(t);var a=i("27c5"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a},"51bd":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return a}));var a={uIcon:i("e5e1").default},n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{},[i("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":e.isFixed},e.navbarClass],style:[e.navbarStyle]},[i("v-uni-view",{staticClass:"vh-status-bar",style:{height:(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}),i("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":e.newYearTheme},style:[e.navbarInnerStyle]},[e.isBack?i("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}},[e.vhFrom?i("u-icon",{attrs:{name:"nav-back",color:e.backIconColor,size:e.backIconSize}}):[i("u-icon",{attrs:{name:e.pageLength<=1?"home":e.backIconName,color:e.backIconColor,size:e.backIconSize}}),e.backText?i("v-uni-view",{staticClass:"vh-back-text",style:[e.backTextStyle]},[e._v(e._s(e.backText))]):e._e()]],2):e._e(),e.title?i("v-uni-view",{staticClass:"vh-navbar-content-title",style:[e.titleStyle]},[i("v-uni-view",{staticClass:"vh-title",style:{color:e.titleColor,fontSize:e.titleSize+"rpx",fontWeight:e.titleBold?"bold":"normal"}},[e._v(e._s(e.title))])],1):e._e(),i("v-uni-view",{staticClass:"vh-slot-content"},[e._t("default")],2),i("v-uni-view",{staticClass:"vh-slot-right"},[e._t("right")],2)],1)],1),e.isFixed&&!e.immersive?i("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(e.navbarHeight)+(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}):e._e()],1)},r=[]},5426:function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return a}));var a={uLoading:i("301a").default},n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-switch",class:[1==e.value?"u-switch--on":"",e.disabled?"u-switch--disabled":""],style:[e.switchStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClick.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"u-switch__node node-class",style:{width:e.$u.addUnit(this.size),height:e.$u.addUnit(this.size)}},[i("u-loading",{staticClass:"u-switch__loading",attrs:{show:e.loading,size:.6*e.size,color:e.loadingColor}})],1)],1)},r=[]},"5ba5":function(e,t,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("d3b7"),i("159b"),i("14d9"),i("498a");var n=a(i("f07e")),r=a(i("c964")),s=a(i("f3f3")),o=i("26cb"),c={name:"address-add",data:function(){return{isEdit:"",addressId:"",loading:!0,regionList:[],provinces:[],citys:[],areas:[],consignee:"",phone:"",province:"",provinceId:"",city:"",cityId:"",area:"",areaId:"",address:"",clipboardText:"",showClipboard:!1,label:"",showRegion:!1,isDefault:!1}},computed:(0,s.default)({},(0,o.mapState)(["routeTable","addressInfoState","labelList"])),onLoad:function(e){this.isEditAddress(e),this.getRegionList()},onShow:function(){this.getAddLabel()},methods:(0,s.default)((0,s.default)({},(0,o.mapMutations)(["muRegionInfo"])),{},{isEditAddress:function(e){if(e.isEdit){var t=this.addressInfoState,i=t.id,a=t.consignee,n=t.consignee_phone,r=t.province_name,s=t.province_id,o=t.city_name,c=t.city_id,l=t.town_name,u=t.town_id,d=t.address,f=t.label,v=t.is_default;this.isEdit=e.isEdit,this.addressId=i,this.consignee=a,this.phone=n,this.province=r,this.provinceId=s,this.city=o,this.cityId=c,this.area=l,this.areaId=u,this.address=d,this.label=f,this.isDefault=!!v}},getRegionList:function(){var e=this;return(0,r.default)((0,n.default)().mark((function t(){var i;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$u.api.regionList({isJson:!0});case 2:i=t.sent,e.regionList=i.data.list,e.loading=!1,e.getRegionData();case 6:case"end":return t.stop()}}),t)})))()},getRegionData:function(){var e=this;this.regionList.forEach((function(t,i){e.provinces.push({label:t.name,value:t.id}),e.citys.push([]),e.areas.push([]),t.children.forEach((function(t,a){e.citys[i].push({label:t.name,value:t.id}),e.areas[i].push([]),t.children.forEach((function(t,n){e.areas[i][a].push({label:t.name,value:t.id})}))}))})),this.muRegionInfo({provinces:this.provinces,citys:this.citys,areas:this.areas})},confirmRegion:function(e){this.province=e.province.label,this.provinceId=e.province.value,this.city=e.city.label,this.cityId=e.city.value,this.area=e.area.label,this.areaId=e.area.value},submitAiText:function(){var e=this;return(0,r.default)((0,n.default)().mark((function t(){var i,a,r,s,o,c,l,u,d,f,v;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.feedback.loading("识别中..."),t.next=3,e.$u.api.addressAiMatch({address:e.clipboardText});case 3:i=t.sent,a=i.data,r=a.consignee,s=a.consignee_phone,o=a.province_id,c=a.province_name,l=a.city_id,u=a.city_name,d=a.town_id,f=a.town_name,v=a.address,r&&s&&o&&c&&l&&u&&d&&f&&v?(e.consignee=r,e.phone=s,e.provinceId=o,e.province=c,e.cityId=l,e.city=u,e.areaId=d,e.area=f,e.address=v,e.feedback.hideLoading()):e.feedback.toast({title:"所在地区匹配失败，请修改或手动选择~"});case 6:case"end":return t.stop()}}),t)})))()},changeLabel:function(e){this.label=e},getAddLabel:function(){this.labelList.length>3&&(this.label=this.labelList[this.labelList.length-1])},goChooseAddress:function(){var e=this;wx.chooseAddress({success:function(){var t=(0,r.default)((0,n.default)().mark((function t(i){var a,r,s,o,c,l,u,d,f,v,p;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.feedback.loading({title:"获取中..."}),a=i.userName,r=i.telNumber,s=i.provinceName,o=i.cityName,c=i.countyName,l=i.detailInfo,t.next=5,e.$u.api.regionId({province_name:s,city_name:o,town_name:c});case 5:u=t.sent,d=u.data,f=d.province_id,v=d.city_id,p=d.town_id,f&&v&&p?(e.consignee=a,e.phone=r,e.provinceId=f,e.province=s,e.cityId=v,e.city=o,e.areaId=p,e.area=c,e.address=l,e.feedback.hideLoading()):e.feedback.toast({title:"系统暂无该地址，请修改或手动选择~"}),t.next=12;break;case 10:t.prev=10,t.t0=t["catch"](0);case 12:case"end":return t.stop()}}),t,null,[[0,10]])})));return function(e){return t.apply(this,arguments)}}()})},save:function(){var e=this;return(0,r.default)((0,n.default)().mark((function t(){var i;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.feedback.loading({title:"提交中..."}),""!=e.$u.trim(e.consignee,"all")){t.next=5;break}return t.abrupt("return",e.feedback.toast({title:"请输入收货人",icon:"error"}));case 5:if(""!=e.$u.trim(e.phone,"all")){t.next=9;break}return t.abrupt("return",e.feedback.toast({title:"请输入手机号",icon:"error"}));case 9:if(""!=e.province&&""!=e.city&&""!=e.area){t.next=13;break}return t.abrupt("return",e.feedback.toast({title:"请选择地址",icon:"error"}));case 13:if(""!=e.provinceId&&""!=e.cityId&&""!=e.areaId){t.next=17;break}return t.abrupt("return",e.feedback.toast({title:"匹配失败,请修改或手动选择"}));case 17:if(""!=e.$u.trim(e.address,"all")){t.next=19;break}return t.abrupt("return",e.feedback.toast({title:"请输入详细地址",icon:"error"}));case 19:if(i={},i.consignee=e.consignee,i.consignee_phone=e.phone,i.address=e.address,i.label=e.label,i.province_name=e.province,i.province_id=e.provinceId,i.city_name=e.city,i.city_id=e.cityId,i.town_name=e.area,i.town_id=e.areaId,i.is_default=e.isDefault?1:0,!e.isEdit){t.next=38;break}return i.id=e.addressId,t.next=35,e.$u.api.updateAddress(i);case 35:e.feedback.toast({title:"修改成功",icon:"success"}),t.next=41;break;case 38:return t.next=40,e.$u.api.addAddress(i);case 40:e.feedback.toast({title:"新建成功",icon:"success"});case 41:setTimeout((function(){e.jump.navigateBack()}),1500);case 42:case"end":return t.stop()}}),t)})))()}})};t.default=c},"5dd9":function(e,t,i){"use strict";i.r(t);var a=i("5426"),n=i("b5c7");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);i("1c5a");var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"0d3821ba",null,!1,a["a"],void 0);t["default"]=o.exports},"5f80":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return r})),i.d(t,"a",(function(){return a}));var a={vhNavbar:i("12c6").default,uButton:i("4f1b").default,uIcon:i("e5e1").default,vhGap:i("1677").default,uSwitch:i("5dd9").default,vhRegion:i("b94e").default,vhLoading:i("e189").default},n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"content",class:e.loading?"h-vh-100 o-hid":""},[e.loading?i("vh-loading"):i("v-uni-view",{staticClass:"fade-in pb-124"},[i("vh-navbar",{attrs:{title:"新建收货地址","show-border":!0}}),i("v-uni-view",{staticClass:"ml-40 mr-40"},[i("v-uni-view",{staticClass:"pt-32 pb-32 d-flex a-center bb-s-01-eeeeee"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3 l-h-40"},[e._v("收货人")]),i("v-uni-input",{staticClass:"flex-1 ml-34 font-32 text-3",attrs:{type:"text",placeholder:"收货人姓名","placeholder-style":"color:#999;font-size:32rpx;"},model:{value:e.consignee,callback:function(t){e.consignee=t},expression:"consignee"}})],1),i("v-uni-view",{staticClass:"pt-32 pb-32 d-flex a-center bb-s-01-eeeeee"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3 l-h-40"},[e._v("手机号码")]),i("v-uni-input",{staticClass:"flex-1 ml-34 font-32 text-3",attrs:{type:"number",placeholder:"收货人手机号","placeholder-style":"color:#999;font-size:32rpx;"},model:{value:e.phone,callback:function(t){e.phone=t},expression:"phone"}})],1),i("v-uni-view",{staticClass:"pt-32 pb-32 d-flex a-center bb-s-01-eeeeee"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3 l-h-40"},[e._v("所在地区")]),i("v-uni-view",{staticClass:"flex-1 ml-34 font-32",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showRegion=!0}}},[e.province&&e.city&&e.area?i("v-uni-text",{staticClass:"text-3"},[e._v(e._s(e.province)+" "+e._s(e.city)+" "+e._s(e.area))]):i("v-uni-text",{staticClass:"text-9"},[e._v("省份、市区、地区")])],1)],1),i("v-uni-view",{staticClass:"pt-32 pb-32 d-flex a-center",class:e.showClipboard?"":"bb-s-01-eeeeee"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3 l-h-40"},[e._v("详细地址")]),i("v-uni-input",{staticClass:"flex-1 ml-34 font-32 text-3",attrs:{type:"text",placeholder:"街道、楼牌号等","placeholder-style":"color:#999;font-size:32rpx;"},model:{value:e.address,callback:function(t){e.address=t},expression:"address"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.showClipboard,expression:"showClipboard"}],staticClass:"fade-in bg-f7f7f7 p-24 b-rad-10"},[i("v-uni-textarea",{staticClass:"w-p100 h-166 font-28 text-3 l-h-44",attrs:{"cursor-spacing":130,type:"text",placeholder:"粘贴收件人姓名、手机号、收货地址，可快速识别您的收货信息","placeholder-style":"color:#999;font-size:24rpx;"},model:{value:e.clipboardText,callback:function(t){e.clipboardText=t},expression:"clipboardText"}}),""!==e.$u.trim(e.clipboardText,"all")?i("v-uni-view",{staticClass:"fade-in d-flex j-end a-center mt-24"},[i("v-uni-view",{staticClass:"font-28 text-6",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clipboardText=""}}},[e._v("清除")]),i("v-uni-view",{staticClass:"ml-60"},[i("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"144rpx",height:"52rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.submitAiText.apply(void 0,arguments)}}},[e._v("提交")])],1)],1):e._e()],1),i("v-uni-view",{staticClass:"pt-40 pb-40 d-flex j-center a-center",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showClipboard=!e.showClipboard}}},[i("v-uni-text",{staticClass:"mr-16 font-32 text-3 font-wei l-h-44"},[e._v("地址粘贴板")]),e.showClipboard?i("v-uni-view",{staticClass:"fade-in"},[i("u-icon",{attrs:{name:"arrow-up",size:20,color:"#333"}})],1):i("v-uni-view",{staticClass:"fade-in-up-medium"},[i("u-icon",{attrs:{name:"arrow-down",size:20,color:"#333"}})],1)],1)],1),i("vh-gap",{attrs:{height:"16","bg-color":"#f5f5f5"}}),i("v-uni-view",{staticClass:"ml-40 mr-40 pt-40 pb-40 d-flex bb-s-01-eeeeee"},[i("v-uni-view",{staticClass:"font-32 text-3 font-wei l-h-44 w-s-now"},[e._v("标签")]),i("v-uni-view",{staticClass:"ml-86 d-flex flex-wrap a-center"},[e._l(e.labelList,(function(t,a){return i("v-uni-text",{key:a,staticClass:"b-rad-30 mb-24 ml-24 font-30 text-3 font-wei l-h-42",class:e.label==t?"fade-in bg-2e7bff ptb-06-plr-44 text-ffffff":"b-s-01-979797 ptb-04-plr-42",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.changeLabel(t)}}},[e._v(e._s(t))])})),i("v-uni-image",{staticClass:"mb-24 ml-24 w-144 h-52",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/add_label.png",mode:"widthFix"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.jump.navigateTo(e.routeTable.pEAddressNewLabel)}}})],2)],1),i("v-uni-view",{staticClass:"d-flex j-sb a-center ml-40 mr-40 pt-36 pb-36"},[i("v-uni-view",{},[i("v-uni-view",{staticClass:"font-32 text-3 font-wei l-h-44"},[e._v("设置默认地址")]),i("v-uni-view",{staticClass:"mt-12 text-6 l-h-34"},[e._v("系统会默认使用该地址作为你的收货地址")])],1),i("u-switch",{attrs:{"inactive-color":"#666","active-color":"#E80404"},model:{value:e.isDefault,callback:function(t){e.isDefault=t},expression:"isDefault"}})],1),i("vh-region",{on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.confirmRegion.apply(void 0,arguments)}},model:{value:e.showRegion,callback:function(t){e.showRegion=t},expression:"showRegion"}}),i("v-uni-view",{staticClass:"p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022"},[i("v-uni-view",{staticClass:"w-p100 h-p100 d-flex j-center a-center"},[i("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"64rpx",fontSize:"28rpx",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.save.apply(void 0,arguments)}}},[e._v("保存")])],1)],1)],1)],1)},r=[]},"6e10":function(e,t,i){var a=i("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-loading-circle[data-v-19cf7fca]{display:inline-flex;vertical-align:middle;width:%?28?%;height:%?28?%;background:0 0;border-radius:50%;border:2px solid;border-color:#e5e5e5 #e5e5e5 #e5e5e5 #8f8d8e;-webkit-animation:u-circle-data-v-19cf7fca 1s linear infinite;animation:u-circle-data-v-19cf7fca 1s linear infinite}.u-loading-flower[data-v-19cf7fca]{width:20px;height:20px;display:inline-block;vertical-align:middle;-webkit-animation:u-flower-data-v-19cf7fca 1s steps(12) infinite;animation:u-flower-data-v-19cf7fca 1s steps(12) infinite;background:transparent url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=) no-repeat;background-size:100%}@-webkit-keyframes u-flower-data-v-19cf7fca{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes u-flower-data-v-19cf7fca{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes u-circle-data-v-19cf7fca{0%{-webkit-transform:rotate(0);transform:rotate(0)}100%{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}',""]),e.exports=t},"724e":function(e,t,i){"use strict";i.r(t);var a=i("820d"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a},"7f1a":function(e,t,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(i("f3f3"));i("a9e3"),i("caad6"),i("2532");var r=i("26cb"),s=uni.getSystemInfoSync(),o={},c={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:o,statusBarHeight:s.statusBarHeight,appStatusBarHeight:0}},computed:(0,n.default)((0,n.default)({},(0,r.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var e={};return e.height=this.navbarHeight+"px",e},navbarStyle:function(){var e={};return e.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(e,this.background),this.showBorder&&Object.assign(e,this.borderStyle),e},titleStyle:function(){var e={};return e.left=(s.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.right=(s.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.width=uni.upx2px(this.titleWidth)+"px",e},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var e=this.pages.getPageFullPath(),t=this.routeTable,i=t.pEAddressAdd,a=t.pEAddressManagement,n=t.pBOrderDepositDetail;(e.includes("/packageH")||e.includes(i)||e.includes(a)||e.includes(n))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};t.default=c},"820d":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3");var a={name:"vh-gap",props:{bgColor:{type:String,default:"transparent"},height:{type:[String,Number],default:30}},computed:{gapStyle:function(){return{backgroundColor:this.bgColor,height:this.height+"rpx"}}}};t.default=a},"945b":function(e,t,i){"use strict";i.r(t);var a=i("5ba5"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a},a126:function(e,t,i){var a=i("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("4f06").default;n("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},a89b:function(e,t,i){"use strict";i.r(t);var a=i("5f80"),n=i("945b");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"dd2b968a",null,!1,a["a"],void 0);t["default"]=o.exports},b515:function(e,t,i){"use strict";var a=i("f1e2"),n=i.n(a);n.a},b5c7:function(e,t,i){"use strict";i.r(t);var a=i("1672"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a},bbdc:function(e,t,i){var a=i("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),e.exports=t},cc5e:function(e,t,i){var a=i("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-switch[data-v-0d3821ba]{position:relative;display:inline-block;box-sizing:initial;width:2em;height:1em;background-color:#fff;border:1px solid rgba(0,0,0,.1);border-radius:1em;transition:background-color .3s;font-size:%?50?%}.u-switch__node[data-v-0d3821ba]{display:flex;flex-direction:row;align-items:center;justify-content:center;position:absolute;top:0;left:0;border-radius:100%;z-index:1;background-color:#fff;background-color:#fff;box-shadow:0 3px 1px 0 rgba(0,0,0,.05),0 2px 2px 0 rgba(0,0,0,.1),0 3px 3px 0 rgba(0,0,0,.05);box-shadow:0 3px 1px 0 rgba(0,0,0,.05),0 2px 2px 0 rgba(0,0,0,.1),0 3px 3px 0 rgba(0,0,0,.05);transition:-webkit-transform .3s cubic-bezier(.3,1.05,.4,1.05);transition:transform .3s cubic-bezier(.3,1.05,.4,1.05);transition:transform .3s cubic-bezier(.3,1.05,.4,1.05),-webkit-transform .3s cubic-bezier(.3,1.05,.4,1.05);transition:-webkit-transform cubic-bezier(.3,1.05,.4,1.05);transition:transform cubic-bezier(.3,1.05,.4,1.05);transition:transform cubic-bezier(.3,1.05,.4,1.05),-webkit-transform cubic-bezier(.3,1.05,.4,1.05);transition:transform .3s cubic-bezier(.3,1.05,.4,1.05)}.u-switch__loading[data-v-0d3821ba]{display:flex;flex-direction:row;align-items:center;justify-content:center}.u-switch--on[data-v-0d3821ba]{background-color:#1989fa}.u-switch--on .u-switch__node[data-v-0d3821ba]{-webkit-transform:translateX(100%);transform:translateX(100%)}.u-switch--disabled[data-v-0d3821ba]{opacity:.4}',""]),e.exports=t},e189:function(e,t,i){"use strict";i.r(t);var a=i("2c73"),n=i("4801");for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);var s=i("f0c5"),o=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"16a31500",null,!1,a["a"],void 0);t["default"]=o.exports},f074:function(e,t,i){"use strict";i.r(t);var a=i("7f1a"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a},f1e2:function(e,t,i){var a=i("6e10");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("4f06").default;n("1e029910",a,!0,{sourceMap:!1,shadowMode:!1})},f2f9:function(e,t,i){"use strict";var a=i("a126"),n=i.n(a);n.a},f440:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){}));var a=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{style:[this.gapStyle]})},n=[]},fcd7:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3");var a={name:"u-loading",props:{mode:{type:String,default:"circle"},color:{type:String,default:"#c7c7c7"},size:{type:[String,Number],default:"34"},show:{type:Boolean,default:!0}},computed:{cricleStyle:function(){var e={};return e.width=this.size+"rpx",e.height=this.size+"rpx","circle"==this.mode&&(e.borderColor="#e4e4e4 #e4e4e4 #e4e4e4 ".concat(this.color?this.color:"#c7c7c7")),e}}};t.default=a},ffc6:function(e,t,i){"use strict";i.r(t);var a=i("fcd7"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(r);t["default"]=n.a}}]);