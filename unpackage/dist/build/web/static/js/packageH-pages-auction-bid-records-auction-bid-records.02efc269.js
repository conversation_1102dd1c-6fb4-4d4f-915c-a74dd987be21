(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-bid-records-auction-bid-records"],{"0efb":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"vh-image-con",class:[t.isMf?"mf-card":""],style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():a("v-uni-image",{staticClass:"vh-image",style:{backgroundColor:t.bgColor,borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.getImage,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?a("v-uni-view",{staticClass:"vh-image-loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[a("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e(),t.showError&&t.isError&&!t.loading?a("v-uni-view",{staticClass:"u-image-error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[a("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e()],1)},n=[]},"0ff7":function(t,e,a){var i=a("5bfa");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("731708a8",i,!0,{sourceMap:!1,shadowMode:!1})},"12c6":function(t,e,a){"use strict";a.r(e);var i=a("51bd"),n=a("f074");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("f2f9");var o=a("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"519170ec",null,!1,i["a"],void 0);e["default"]=r.exports},"1b37":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("f07e")),s=i(a("c964"));a("fb6a"),a("dca8"),a("d3b7");var o=a("d8be"),r={name:"auctionBidRecords",data:function(){return{MAuctionGoodsStatus:o.MAuctionGoodsStatus,loading:!0,id:"",onsale_status:"",loginInfo:{},list:[]}},computed:{firstRecord:function(t){var e=t.list;return e[0]},otherRecords:function(t){var e=t.list;return e.slice(1)},isAuctionAbort:function(t){var e=t.onsale_status;return o.MAuctionGoodsStatus.AuctionAbort===e}},methods:{load:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var a,i,s;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.getAuctionBidRecords({id:t.id});case 2:i=e.sent,s=(null===i||void 0===i||null===(a=i.data)||void 0===a?void 0:a.list)||[],t.list=Object.freeze(s);case 5:case"end":return e.stop()}}),e)})))()}},onLoad:function(t){var e=this;this.loginInfo=uni.getStorageSync("loginInfo")||{},this.id=t.id,this.onsale_status=+t.onsale_status,this.load().finally((function(){e.loading=!1}))}};e.default=r},"22ad":function(t,e,a){"use strict";var i=a("bfb8"),n=a.n(i);n.a},"3dbb":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.auction-bid-list > uni-view[data-v-313b0874]::after{content:"";display:block;height:%?2?%;background:linear-gradient(270deg,rgba(232,4,4,.07),rgba(232,4,4,.6) 33%,rgba(232,4,4,.76) 52%,rgba(232,4,4,.6) 67%,rgba(232,4,4,.07));opacity:.5}.auction-bid-list.is-from-detail > uni-view[data-v-313b0874]:last-of-type::after{display:none}',""]),t.exports=e},"3dc3":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("d0af")),s=i(a("f3f3"));a("a9e3"),a("d3b7"),a("159b"),a("e25e"),a("c975");var o=a("26cb"),r={name:"u-image",props:{loadingType:{type:[String,Number],default:1},isMf:{type:Boolean,default:!1},src:{default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!1},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:100},bgColor:{type:String,default:"transparent"},isResize:{type:Boolean,default:!1},resizeRatio:{type:Object,default:function(){return{wratio:16,hratio:9}}},resizeUsePx:{type:Boolean,default:!1},opacityProp:{type:Number,default:1},backgroundImage:{type:String,default:""}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:(0,s.default)((0,s.default)({},(0,o.mapState)(["ossPrefix"])),{},{wrapStyle:function(){var t={};if(t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),this.backgroundImage&&(t.backgroundImage="url(".concat(this.backgroundImage,")"),t.backgroundSize="100% 100%",t.backgroundRepeat="no-repeat",t.backgroundPosition="center"),this.isResize){var e,a,i=(null===this||void 0===this||null===(e=this.src)||void 0===e||null===(a=e.split("?"))||void 0===a?void 0:a[1])||"",s=i.split("&"),o={};s.forEach((function(t){var e=t.split("="),a=(0,n.default)(e,2),i=a[0],s=a[1];o[i]=s}));var r=+((null===o||void 0===o?void 0:o.w)||""),c=+((null===o||void 0===o?void 0:o.h)||"");if(!isNaN(r)&&!isNaN(c)&&r&&c){var u=parseInt(this.width),d=u/r*c,l=this.resizeRatio,f=l.wratio,v=l.hratio;if("auto"!==f&&"auto"!==v){var h=u*f/v,p=u*v/f;d>h?d=h:d<p&&(d=p)}this.resizeUsePx?t.height="".concat(d,"px"):t.height=this.$u.addUnit(d)}}return t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0||"circle"==this.shape?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t},getLoadingImage:function(){return"https://images.vinehoo.com/vinehoomini/v3/occupy_img/new_img".concat(this.loadingType,".png")},getImage:function(){return"string"!==typeof this.src?"":-1==this.src.indexOf("http")?this.ossPrefix+this.src:this.src}}),methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=t.opacityProp,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=r},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={uIcon:a("e5e1").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},s=[]},"5a03":function(t,e,a){"use strict";a.r(e);var i=a("c4b4"),n=a("7e6c");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("22ad");var o=a("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"313b0874",null,!1,i["a"],void 0);e["default"]=r.exports},"5bfa":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.records[data-v-6d6f70ad]{height:100%;display:flex;flex-direction:column}.records__header[data-v-6d6f70ad]{flex-shrink:0;display:flex;justify-content:flex-end;align-items:center;flex-direction:column;margin-top:-46px;background:linear-gradient(139deg,#d4b053,#d03935)}.records__devide[data-v-6d6f70ad]{padding:0 %?26?%;height:100%;background:linear-gradient(270deg,rgba(232,4,4,0),rgba(232,4,4,.6) 33%,rgba(232,4,4,.76) 52%,rgba(232,4,4,.6) 67%,rgba(232,4,4,0));opacity:.5}.records__content[data-v-6d6f70ad]{flex:1;display:flex;justify-content:flex-start;align-items:flex-start;overflow:auto}.records__content[data-v-6d6f70ad]::before, .records__content[data-v-6d6f70ad]::after{content:"";display:block;flex:1;height:100%}.records__content[data-v-6d6f70ad]::before{background:linear-gradient(180deg,#d28848,#efe3d8 83%,hsla(0,0%,100%,0))}.records__content[data-v-6d6f70ad]::after{background:linear-gradient(180deg,#d13935,#f4d0cf 65%,#fff)}.records__content > uni-view[data-v-6d6f70ad]{padding:0 %?26?%;width:%?706?%;height:100%;overflow:auto}',""]),t.exports=e},"67e3":function(t,e,a){"use strict";var i=a("0ff7"),n=a.n(i);n.a},"6ab5":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-image-con[data-v-89d76102]{position:relative;transition:opacity .5s ease-in}.vh-image[data-v-89d76102]{width:100%;height:100%}.vh-image-loading[data-v-89d76102],\n.u-image-error[data-v-89d76102]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#fff;color:#909399;font-size:%?46?%}.mf-card[data-v-89d76102]{background-color:#f7f7f7!important;padding:5px}',""]),t.exports=e},"7e6c":function(t,e,a){"use strict";a.r(e);var i=a("ad66"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},"7f1a":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var s=a("26cb"),o=uni.getSystemInfoSync(),r={},c={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:r,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,n.default)((0,n.default)({},(0,s.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,i=e.pEAddressManagement,n=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(i)||t.includes(n))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=c},"8c91":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,"uni-page-body[data-v-6d6f70ad]{height:100%}",""]),t.exports=e},"9ea8":function(t,e,a){"use strict";a.r(e);var i=a("1b37"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},a126:function(t,e,a){var i=a("bbdc");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("703d0287",i,!0,{sourceMap:!1,shadowMode:!1})},ad66:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a("d8be"),n={props:{list:{type:Array,default:function(){return[]}},uid:{required:!0},onsaleStatus:{required:!0},isFromDetail:{type:Boolean,defualt:!1}},data:function(){return{MAuctionGoodsStatus:i.MAuctionGoodsStatus}},methods:{getStatusClazz:function(t){return this.isFromDetail?t?"w-58 h-34":"w-60 h-36":"w-58 h-34"},getStatusIcon:function(t){return this.isFromDetail?t?"/auction/abl_out_58_34.png":i.MAuctionGoodsStatus.AuctionAbort===this.onsaleStatus?"/auction/abl_success_60_36.png":"/auction/abl_lead_60_36.png":"/auction/abl_out_58_34.png"}}};e.default=n},af29:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={vhNavbar:a("12c6").default,vhImage:a("ce7c").default,AuctionBidList:a("5a03").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"records"},[a("vh-navbar",{attrs:{title:"拍卖记录",height:"46",background:{background:"transparent"},"back-icon-color":"#FFF",titleColor:"#fff"}}),t.loading?t._e():[a("v-uni-view",{staticClass:"records__header",class:t.isAuctionAbort?"h-500":"h-484"},[a("v-uni-view",{staticClass:"p-rela w-706 h-338 bg-ffffff",class:{"mb-22":t.isAuctionAbort}},[a("v-uni-image",{staticClass:"w-p100 h-p100",attrs:{src:t.ossIcon("/auction/crown_706_338.png")}}),a("v-uni-view",{staticClass:"p-abso bottom-0 left-p-50 t-trans-x-m50 w-324 h-386 bg-ffffff"}),a("v-uni-view",{staticClass:"p-abso top-0 w-p100 h-p100 text-center"},[a("v-uni-view",{staticClass:"p-rela mtb-00-mlr-auto flex-c-c w-136 h-136"},[a("v-uni-view",{staticClass:"p-rela flex-c-c w-p100 h-p100"},[a("vh-image",{attrs:{"loading-type":4,src:+t.firstRecord.is_anonymous?"https://images.vinehoo.com/avatars/rabbit.png":t.firstRecord.avatar_image,width:136,height:136,shape:"circle"}}),a("v-uni-image",{staticClass:"p-abso w-138 h-138",attrs:{src:t.ossIcon("/auction/circle_138.png")}})],1),a("v-uni-image",{staticClass:"p-abso w-60 h-36",class:t.isAuctionAbort?"bottom-n-02 right-0":"top-0 left-n-12",attrs:{src:t.ossIcon(t.isAuctionAbort?"/auction/abl_success_60_36.png":"/auction/abl_lead_60_36.png")}}),t.isAuctionAbort?a("v-uni-image",{staticClass:"p-abso top-n-44 w-84 h-58",attrs:{src:t.ossIcon("/auction/crown_84_58.png")}}):t._e()],1),a("v-uni-view",{staticClass:"flex-c-c mt-18 text-e80404"},[a("v-uni-text",{staticClass:"font-20 l-h-26"},[a("v-uni-text",{staticClass:"font-18"},[t._v("NO.")]),t._v(t._s(t.firstRecord.code))],1),a("v-uni-view",{staticClass:"ml-06 w-max-410 font-24 l-h-34 text-hidden"},[t._v(t._s(t.firstRecord.nickname))])],1),a("v-uni-view",{staticClass:"mt-08 font-28 text-9 l-h-28"},[t._v("（地区："+t._s(t.firstRecord.province_name||"未知")+"）")]),a("v-uni-view",{staticClass:"mt-14 font-32 text-e80404 l-h-44"},[t._v("¥"+t._s(t.firstRecord.bid_price))]),a("v-uni-view",{staticClass:"font-24 text-9 l-h-34"},[t._v(t._s(t._f("date")(t.firstRecord.create_time,"mm.dd hh:MM:ss")))])],1)],1),t.isAuctionAbort?t._e():a("v-uni-view",{staticClass:"w-706 h-08 bg-ffffff"},[a("v-uni-view",{staticClass:"records__devide"})],1)],1),a("v-uni-view",{staticClass:"records__content"},[a("AuctionBidList",{attrs:{list:t.otherRecords,uid:t.loginInfo.uid,onsaleStatus:t.onsale_status}})],1)]],2)},s=[]},b252:function(t,e,a){var i=a("6ab5");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("0c30beb4",i,!0,{sourceMap:!1,shadowMode:!1})},bbdc:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},bfb8:function(t,e,a){var i=a("3dbb");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("7cf101e2",i,!0,{sourceMap:!1,shadowMode:!1})},c4b4:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={vhImage:a("ce7c").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"auction-bid-list",class:{"is-from-detail":t.isFromDetail}},t._l(t.list,(function(e,i){return a("v-uni-view",{key:i},[a("v-uni-view",{staticClass:"flex-c-c h-132 text-9"},[a("v-uni-view",{staticClass:"p-rela flex-c-c w-60 h-60"},[a("vh-image",{attrs:{"loading-type":4,src:+e.is_anonymous?"https://images.vinehoo.com/avatars/rabbit.png":e.avatar_image,width:60,height:60,shape:"circle"}}),!i&&t.isFromDetail?a("v-uni-image",{staticClass:"p-abso w-60 h-60",attrs:{src:t.ossIcon("/auction/circle_60.png")}}):t._e()],1),a("v-uni-view",{staticClass:"ml-08"},[a("v-uni-view",{staticClass:"ml-12 w-382 text-hidden",class:{"text-e80404":!i&&t.isFromDetail}},[a("v-uni-text",{staticClass:"font-20 l-h-26"},[a("v-uni-text",{staticClass:"font-18"},[t._v("NO.")]),t._v(t._s(e.code))],1),a("v-uni-text",{staticClass:"ml-06 font-24 l-h-34"},[t._v(t._s(e.nickname))])],1),a("v-uni-view",{staticClass:"mt-10 font-20 text-9 l-h-28"},[t._v("（地区："+t._s(e.province_name||"未知")+"）")])],1),a("v-uni-view",{staticClass:"flex-1 text-right"},[a("v-uni-view",{staticClass:"flex-e-c"},[a("v-uni-image",{class:t.getStatusClazz(i),attrs:{src:t.ossIcon(t.getStatusIcon(i))}}),a("v-uni-text",{staticClass:"ml-06 font-32 l-h-44",class:{"text-e80404":!i&&t.isFromDetail}},[t._v("¥"+t._s(e.bid_price))])],1),a("v-uni-view",{staticClass:"font-24 l-h-34"},[t._v(t._s(t._f("date")(e.create_time,"mm.dd hh:MM:ss")))])],1)],1)],1)})),1)},s=[]},ca73:function(t,e,a){"use strict";var i=a("f687"),n=a.n(i);n.a},cd36:function(t,e,a){"use strict";a.r(e);var i=a("af29"),n=a("9ea8");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("ca73"),a("67e3");var o=a("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"6d6f70ad",null,!1,i["a"],void 0);e["default"]=r.exports},ce7c:function(t,e,a){"use strict";a.r(e);var i=a("0efb"),n=a("ea26");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("eb5f");var o=a("f0c5"),r=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"89d76102",null,!1,i["a"],void 0);e["default"]=r.exports},ea26:function(t,e,a){"use strict";a.r(e);var i=a("3dc3"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},eb5f:function(t,e,a){"use strict";var i=a("b252"),n=a.n(i);n.a},f074:function(t,e,a){"use strict";a.r(e);var i=a("7f1a"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},f2f9:function(t,e,a){"use strict";var i=a("a126"),n=a.n(i);n.a},f687:function(t,e,a){var i=a("8c91");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("74dd84c2",i,!0,{sourceMap:!1,shadowMode:!1})}}]);