(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-activity-activity~pages-invite-activity-invite-activity"],{"0076":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return a}));var a={vhImage:i("ce7c").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"agoods-list"},[i("v-uni-view",{staticClass:"agoods-list__content"},[i("v-uni-view",{staticClass:"agoods-list__title"},[t._v("酒款列表")]),i("v-uni-view",{staticClass:"agoods-list__inner"},t._l(t.list,(function(e){return i("v-uni-view",{key:e.id,staticClass:"agoods-list__item",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onJump(e)}}},[i("v-uni-view",{staticClass:"agoods-list__ileft"},[i("vh-image",{attrs:{"loading-type":2,src:e.banner_img,height:180}})],1),i("v-uni-view",{staticClass:"agoods-list__iright"},[i("v-uni-text",{staticClass:"agoods-list__ititle"},[t._v(t._s(e.title))]),i("v-uni-view",{staticClass:"agoods-list__irbottom"},[i("v-uni-text",{staticClass:"agoods-list__iprice"},[i("v-uni-text",[t._v(t._s(e.is_hidden_price||[3,4].includes(e.onsale_status)?"":"¥"))]),t._v(t._s(e.is_hidden_price||[3,4].includes(e.onsale_status)?"价格保密":e.price))],1),i("v-uni-view",{staticClass:"agoods-list__iquota"},[1===e.periods_type?i("v-uni-text",[t._v("已售"),i("v-uni-text",[t._v(t._s(e.purchased+e.vest_purchased))])],1):i("v-uni-text",[t._v("已售"),i("v-uni-text",[t._v(t._s(e.purchased+e.vest_purchased))]),t._v("/限量"),i("v-uni-text",[t._v(t._s(e.limit_number))]),t._v(t._s("9999"==e.quota_rule.quota_number?"":"/限购")),i("v-uni-text",[t._v(t._s("9999"==e.quota_rule.quota_number?"":e.quota_rule.quota_number))])],1)],1)],1)],1)],1)})),1)],1)],1)},o=[]},"0397":function(t,e,i){var a=i("e2cb");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("280aebb9",a,!0,{sourceMap:!1,shadowMode:!1})},"0efb":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"vh-image-con",class:[t.isMf?"mf-card":""],style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"vh-image",style:{backgroundColor:t.bgColor,borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.getImage,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"vh-image-loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[i("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image-error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[i("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e()],1)},n=[]},"2f2e":function(t,e,i){"use strict";i.r(e);var a=i("d99f"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},"3dc3":function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("d0af")),o=a(i("f3f3"));i("a9e3"),i("d3b7"),i("159b"),i("e25e"),i("c975");var s=i("26cb"),r={name:"u-image",props:{loadingType:{type:[String,Number],default:1},isMf:{type:Boolean,default:!1},src:{default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!1},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:100},bgColor:{type:String,default:"transparent"},isResize:{type:Boolean,default:!1},resizeRatio:{type:Object,default:function(){return{wratio:16,hratio:9}}},resizeUsePx:{type:Boolean,default:!1},opacityProp:{type:Number,default:1},backgroundImage:{type:String,default:""}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:(0,o.default)((0,o.default)({},(0,s.mapState)(["ossPrefix"])),{},{wrapStyle:function(){var t={};if(t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),this.backgroundImage&&(t.backgroundImage="url(".concat(this.backgroundImage,")"),t.backgroundSize="100% 100%",t.backgroundRepeat="no-repeat",t.backgroundPosition="center"),this.isResize){var e,i,a=(null===this||void 0===this||null===(e=this.src)||void 0===e||null===(i=e.split("?"))||void 0===i?void 0:i[1])||"",o=a.split("&"),s={};o.forEach((function(t){var e=t.split("="),i=(0,n.default)(e,2),a=i[0],o=i[1];s[a]=o}));var r=+((null===s||void 0===s?void 0:s.w)||""),d=+((null===s||void 0===s?void 0:s.h)||"");if(!isNaN(r)&&!isNaN(d)&&r&&d){var l=parseInt(this.width),u=l/r*d,c=this.resizeRatio,f=c.wratio,p=c.hratio;if("auto"!==f&&"auto"!==p){var g=l*f/p,v=l*p/f;u>g?u=g:u<v&&(u=v)}this.resizeUsePx?t.height="".concat(u,"px"):t.height=this.$u.addUnit(u)}}return t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0||"circle"==this.shape?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t},getLoadingImage:function(){return"https://images.vinehoo.com/vinehoomini/v3/occupy_img/new_img".concat(this.loadingType,".png")},getImage:function(){return"string"!==typeof this.src?"":-1==this.src.indexOf("http")?this.ossPrefix+this.src:this.src}}),methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=t.opacityProp,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=r},"6ab5":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-image-con[data-v-89d76102]{position:relative;transition:opacity .5s ease-in}.vh-image[data-v-89d76102]{width:100%;height:100%}.vh-image-loading[data-v-89d76102],\n.u-image-error[data-v-89d76102]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#fff;color:#909399;font-size:%?46?%}.mf-card[data-v-89d76102]{background-color:#f7f7f7!important;padding:5px}',""]),t.exports=e},7410:function(t,e,i){"use strict";i.r(e);var a=i("0076"),n=i("2f2e");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("fc1bb");var s=i("f0c5"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"68ea55a8",null,!1,a["a"],void 0);e["default"]=r.exports},b252:function(t,e,i){var a=i("6ab5");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("0c30beb4",a,!0,{sourceMap:!1,shadowMode:!1})},ce7c:function(t,e,i){"use strict";i.r(e);var a=i("0efb"),n=i("ea26");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("eb5f");var s=i("f0c5"),r=Object(s["a"])(n["default"],a["b"],a["c"],!1,null,"89d76102",null,!1,a["a"],void 0);e["default"]=r.exports},d99f:function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("caad6"),i("d81d"),i("4de4"),i("d3b7"),i("b64b"),i("2532"),i("99af");var n=a(i("f3f3")),o=i("26cb"),s=i("1e48"),r={name:"ActivityGoodsList",props:{list:{type:Array,default:function(){return[]}}},computed:(0,n.default)({},(0,o.mapState)(["routeTable"])),methods:{onJump:function(t){var e=this.pages.getCurrenPage().$page,i=e.path,a=e.options;if(["/pages/activity/activity"].includes(i)){var n=Object.keys(a).filter((function(t){return t.includes("source_")})).map((function(t){return"".concat(t,"=").concat(a[t])})).join("&");n=n&&"&".concat(n),window.open("".concat(s.MINI_GOODS_DETAIL_URL,"&id=").concat(t.goods_id).concat(n))}else this.jump.navigateTo("".concat(this.routeTable.pgGoodsDetail,"?id=").concat(t.goods_id))}}};e.default=r},e2cb:function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.agoods-list[data-v-68ea55a8]{background:#fff;border-radius:%?20?%}.agoods-list__content[data-v-68ea55a8]{padding:%?32?% %?24?%}.agoods-list__title[data-v-68ea55a8]{font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:600;font-size:%?36?%;color:#333;line-height:%?50?%}.agoods-list__inner[data-v-68ea55a8]{margin:%?32?% 0 0 0}.agoods-list__item[data-v-68ea55a8]{display:flex;justify-content:center;align-items:stretch}.agoods-list__item[data-v-68ea55a8]:not(:first-of-type){margin:%?20?% 0 0 0}.agoods-list__ileft[data-v-68ea55a8]{flex-shrink:0;width:%?288?%;height:%?180?%;border-radius:%?6?%;overflow:hidden}.agoods-list__iright[data-v-68ea55a8]{flex:1;display:flex;justify-content:space-between;align-items:stretch;flex-direction:column;margin:0 0 0 %?20?%}.agoods-list__ititle[data-v-68ea55a8]{font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:400;font-size:%?24?%;color:#333;line-height:%?34?%;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical}.agoods-list__irbottom[data-v-68ea55a8]{display:flex;justify-content:space-between;align-items:center}.agoods-list__iprice[data-v-68ea55a8]{font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:400;font-size:%?32?%;color:#ff0013;line-height:%?28?%}.agoods-list__iprice uni-text[data-v-68ea55a8]{font-size:%?20?%}.agoods-list__iquota[data-v-68ea55a8]{font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:400;font-size:%?22?%;color:#999;line-height:%?32?%}',""]),t.exports=e},ea26:function(t,e,i){"use strict";i.r(e);var a=i("3dc3"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);e["default"]=n.a},eb5f:function(t,e,i){"use strict";var a=i("b252"),n=i.n(a);n.a},fc1bb:function(t,e,i){"use strict";var a=i("0397"),n=i.n(a);n.a}}]);