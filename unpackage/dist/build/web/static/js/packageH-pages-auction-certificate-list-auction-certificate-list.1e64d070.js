(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-certificate-list-auction-certificate-list"],{"0efb":function(t,e,r){"use strict";r.d(e,"b",(function(){return o})),r.d(e,"c",(function(){return n})),r.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-uni-view",{staticClass:"vh-image-con",class:[t.isMf?"mf-card":""],style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():r("v-uni-image",{staticClass:"vh-image",style:{backgroundColor:t.bgColor,borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.getImage,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?r("v-uni-view",{staticClass:"vh-image-loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[r("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e(),t.showError&&t.isError&&!t.loading?r("v-uni-view",{staticClass:"u-image-error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[r("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e()],1)},n=[]},"12c0":function(t,e,r){r("b64b"),r("a4d3"),r("4de4"),r("d3b7"),r("e439"),r("14d9"),r("159b"),r("dbb4"),r("1d1c"),r("7a82");var o=r("5757");function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,o)}return r}t.exports=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach((function(e){o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t},t.exports.__esModule=!0,t.exports["default"]=t.exports},"12c6":function(t,e,r){"use strict";r.r(e);var o=r("51bd"),n=r("f074");for(var i in n)["default"].indexOf(i)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(i);r("f2f9");var a=r("f0c5"),s=Object(a["a"])(n["default"],o["b"],o["c"],!1,null,"519170ec",null,!1,o["a"],void 0);e["default"]=s.exports},"3dc3":function(t,e,r){"use strict";r("7a82");var o=r("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=o(r("d0af")),i=o(r("f3f3"));r("a9e3"),r("d3b7"),r("159b"),r("e25e"),r("c975");var a=r("26cb"),s={name:"u-image",props:{loadingType:{type:[String,Number],default:1},isMf:{type:Boolean,default:!1},src:{default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!1},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:100},bgColor:{type:String,default:"transparent"},isResize:{type:Boolean,default:!1},resizeRatio:{type:Object,default:function(){return{wratio:16,hratio:9}}},resizeUsePx:{type:Boolean,default:!1},opacityProp:{type:Number,default:1},backgroundImage:{type:String,default:""}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:(0,i.default)((0,i.default)({},(0,a.mapState)(["ossPrefix"])),{},{wrapStyle:function(){var t={};if(t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),this.backgroundImage&&(t.backgroundImage="url(".concat(this.backgroundImage,")"),t.backgroundSize="100% 100%",t.backgroundRepeat="no-repeat",t.backgroundPosition="center"),this.isResize){var e,r,o=(null===this||void 0===this||null===(e=this.src)||void 0===e||null===(r=e.split("?"))||void 0===r?void 0:r[1])||"",i=o.split("&"),a={};i.forEach((function(t){var e=t.split("="),r=(0,n.default)(e,2),o=r[0],i=r[1];a[o]=i}));var s=+((null===a||void 0===a?void 0:a.w)||""),u=+((null===a||void 0===a?void 0:a.h)||"");if(!isNaN(s)&&!isNaN(u)&&s&&u){var c=parseInt(this.width),d=c/s*u,l=this.resizeRatio,f=l.wratio,h=l.hratio;if("auto"!==f&&"auto"!==h){var g=c*f/h,v=c*h/f;d>g?d=g:d<v&&(d=v)}this.resizeUsePx?t.height="".concat(d,"px"):t.height=this.$u.addUnit(d)}}return t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0||"circle"==this.shape?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t},getLoadingImage:function(){return"https://images.vinehoo.com/vinehoomini/v3/occupy_img/new_img".concat(this.loadingType,".png")},getImage:function(){return"string"!==typeof this.src?"":-1==this.src.indexOf("http")?this.ossPrefix+this.src:this.src}}),methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=t.opacityProp,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=s},"51bd":function(t,e,r){"use strict";r.d(e,"b",(function(){return n})),r.d(e,"c",(function(){return i})),r.d(e,"a",(function(){return o}));var o={uIcon:r("e5e1").default},n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-uni-view",{},[r("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[r("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),r("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?r("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?r("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[r("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?r("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?r("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[r("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),r("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),r("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?r("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},i=[]},5757:function(t,e,r){r("7a82");var o=r("9feb");t.exports=function(t,e,r){return e=o(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t},t.exports.__esModule=!0,t.exports["default"]=t.exports},"598d":function(t,e,r){"use strict";r.r(e);var o=r("cfea"),n=r.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(i);e["default"]=n.a},"5bf4":function(t,e,r){"use strict";r.r(e);var o=r("6adb"),n=r("598d");for(var i in n)["default"].indexOf(i)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(i);var a=r("f0c5"),s=Object(a["a"])(n["default"],o["b"],o["c"],!1,null,"22e464e0",null,!1,o["a"],void 0);e["default"]=s.exports},"6ab5":function(t,e,r){var o=r("24fb");e=o(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-image-con[data-v-89d76102]{position:relative;transition:opacity .5s ease-in}.vh-image[data-v-89d76102]{width:100%;height:100%}.vh-image-loading[data-v-89d76102],\n.u-image-error[data-v-89d76102]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#fff;color:#909399;font-size:%?46?%}.mf-card[data-v-89d76102]{background-color:#f7f7f7!important;padding:5px}',""]),t.exports=e},"6adb":function(t,e,r){"use strict";r.d(e,"b",(function(){return n})),r.d(e,"c",(function(){return i})),r.d(e,"a",(function(){return o}));var o={vhNavbar:r("12c6").default,vhImage:r("ce7c").default,AuctionNone:r("f6b7").default,uPopup:r("c4b0").default},n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-uni-view",[r("vh-navbar",{attrs:{title:"收藏证书",height:"46",showBorder:!0}}),t.loading?t._e():r("v-uni-view",[t.list.length?r("v-uni-view",{staticClass:"d-flex flex-wrap ptb-00-plr-24 pt-28 pb-48"},t._l(t.list,(function(e,o){return r("v-uni-view",{key:o,staticClass:"p-rela mt-20 w-346 h-528",class:o%2?"mr-0":"mr-10"},[r("v-uni-image",{staticClass:"p-abso w-p100 h-p100",attrs:{src:t.ossIcon("/auction/certificate_346_528.png")}}),r("v-uni-view",{staticClass:"p-abso w-p100 h-p100",on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.onPreview(e)}}},[r("v-uni-view",{staticClass:"flex-sb-c mt-136 ptb-00-plr-30 font-12 text-3 l-h-16"},[r("v-uni-text",[t._v(t._s(e.buyer_name))]),r("v-uni-text",[t._v(t._s(e.collection_value))])],1),r("v-uni-view",{staticClass:"flex-c-c mt-20"},[r("vh-image",{attrs:{"loading-type":4,src:e.product_img,width:140,height:140}})],1),r("v-uni-view",{staticClass:"mtb-00-mlr-auto mt-20 w-292 font-12 text-3 l-h-16 text-center"},[r("v-uni-view",[t._v(t._s(e.title))]),r("v-uni-view",{staticClass:"mt-06"},[t._v(t._s(e.unique_int))]),r("v-uni-view",{staticClass:"mt-06"},[t._v(t._s(e.order_time))]),r("v-uni-view",{staticClass:"mt-06"},[t._v(t._s(e.net_content))]),r("v-uni-view",{staticClass:"mt-06"},[t._v(t._s(e.category_name))]),r("v-uni-view",{staticClass:"mt-06"},[t._v(t._s(e.years))])],1)],1),e.is_buyer_unlock||e.is_ash?t._e():r("v-uni-view",{staticClass:"p-abso flex-c-c w-p100 h-p100",staticStyle:{background:"rgba(0,0,0,0.5)"},on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.onUnlock(e)}}},[r("v-uni-image",{staticClass:"w-66 h-66",attrs:{src:t.ossIcon(t.isFromMine?"/auction/share_66.png":"/auction/lock_66.png")}})],1)],1)})),1):r("AuctionNone",{staticClass:"pt-200",attrs:{title:t.isFromAppMine?"":"暂无数据",desc:t.isFromAppMine?"取得属于自己的收藏证书～":"",descClazz:t.isFromAppMine?"mt-30 font-24 l-h-36":""}})],1),r("u-popup",{attrs:{mode:"center",width:"552rpx",height:"414rpx","border-radius":"20"},model:{value:t.unlockPopupVisible,callback:function(e){t.unlockPopupVisible=e},expression:"unlockPopupVisible"}},[r("v-uni-view",{staticClass:"p-rela w-p100 h-p100"},[r("v-uni-image",{staticClass:"p-abso w-p100 h-p100",attrs:{src:t.ossIcon("/auction/popup_bg_552_414.png")}}),r("v-uni-view",{staticClass:"p-rela pt-130"},[r("v-uni-image",{staticClass:"p-abso top-24 right-24 w-44 h-44",attrs:{src:t.ossIcon("/auction/close_44.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.unlockPopupVisible=!1}}}),r("v-uni-view",{staticClass:"font-wei-600 font-32 text-6 l-h-44 text-center"},[t._v("评价解锁您的证书哦！")]),r("v-uni-view",{staticClass:"flex-c-c mt-92"},[r("v-uni-button",{staticClass:"vh-btn flex-c-c w-180 h-64 font-wei-500 font-28 text-e80404 bg-ffffff b-s-02-e80404 b-rad-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.toEvaluate.apply(void 0,arguments)}}},[t._v("去评价")])],1)],1)],1)],1),r("u-popup",{staticClass:"bg-transparent-popup",attrs:{mode:"center",width:"100%",height:"100%","mask-custom-style":{background:"rgba(0, 0, 0, 0.3)"}},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.currentItem.$isFirstPreview=!1}},model:{value:t.previewPopupVisible,callback:function(e){t.previewPopupVisible=e},expression:"previewPopupVisible"}},[r("v-uni-view",{staticClass:"p-rela pt-140 pb-80",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.previewPopupVisible=!1}}},[r("v-uni-canvas",{staticClass:"p-abso",staticStyle:{top:"-100%",visibility:"hidden",width:"750px",height:"1146px"},attrs:{"canvas-id":t.canvasId}}),r("v-uni-canvas",{staticClass:"p-abso w-88 h-88",staticStyle:{visibility:"hidden"},attrs:{"canvas-id":t.qrcodeCanvasId}}),t.$isFirstPreview?[t.$app?r("v-uni-view",{staticClass:"mb-50 font-wei-600 font-40 text-ffffff l-h-56 text-center"},[t._v("分享证书赢得兔头！")]):t._e(),r("v-uni-view",{staticClass:"p-rela mtb-00-mlr-auto w-614 h-934"},[r("v-uni-image",{staticClass:"p-abso top-n-64 right-04 w-44 h-44",attrs:{src:t.ossIcon("/auction/close2_44.png")}}),r("v-uni-image",{staticClass:"p-abso w-p100 h-p100",attrs:{src:t.ossIcon("/auction/certificate_614_934.png")}}),r("v-uni-view",{staticClass:"p-abso w-p100 h-p100"},[r("v-uni-view",{staticClass:"flex-sb-c mt-230 ptb-00-plr-62 font-18 l-h-26 text-3"},[r("v-uni-text",[t._v(t._s(t.currentItem.buyer_name))]),r("v-uni-text",[t._v(t._s(t.currentItem.collection_value))])],1),r("v-uni-view",{staticClass:"flex-c-c mt-52"},[r("vh-image",{attrs:{"loading-type":4,src:t.currentItem.product_img,width:234,height:234}})],1),r("v-uni-view",{staticClass:"mtb-00-mlr-auto mt-40 w-490 font-18 text-3 l-h-26 text-center"},[r("v-uni-view",[t._v(t._s(t.currentItem.title))]),r("v-uni-view",{staticClass:"mt-06"},[t._v(t._s(t.currentItem.unique_int))]),r("v-uni-view",{staticClass:"mt-06"},[t._v(t._s(t.currentItem.order_time))]),r("v-uni-view",{staticClass:"mt-06"},[t._v(t._s(t.currentItem.net_content))]),r("v-uni-view",{staticClass:"mt-06"},[t._v(t._s(t.currentItem.category_name))]),r("v-uni-view",{staticClass:"mt-06"},[t._v(t._s(t.currentItem.years))])],1),r("v-uni-view",{staticClass:"p-abso bottom-54 right-108 flex-c-c w-80 h-80 bg-f5f5f5 b-rad-10"},[r("v-uni-canvas",{staticClass:"w-70 h-70",attrs:{"canvas-id":t.smallQrcodeCanvasId}})],1)],1)],1),t.$app?r("v-uni-view",{staticClass:"flex-c-c mt-120"},[r("v-uni-button",{staticClass:"vh-btn flex-c-c w-538 h-82 font-wei-500 font-28 text-ffffff bg-ff9127 b-rad-41",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.onPostCard.apply(void 0,arguments)}}},[t._v("分享到帖子")])],1):t._e()]:[r("v-uni-view",{staticClass:"p-rela h-1146"},[r("v-uni-image",{staticClass:"p-abso w-p100 h-p100",attrs:{src:t.ossIcon("/auction/certificate_750_1146.png")}}),r("v-uni-view",{staticClass:"p-abso w-p100 h-p100"},[r("v-uni-view",{staticClass:"flex-sb-c mt-304 ptb-00-plr-92 font-24 text-3 l-h-34"},[r("v-uni-text",[t._v(t._s(t.currentItem.buyer_name))]),r("v-uni-text",[t._v(t._s(t.currentItem.collection_value))])],1),r("v-uni-view",{staticClass:"flex-c-c mt-64"},[r("vh-image",{attrs:{"loading-type":4,src:t.currentItem.product_img,width:272,height:272}})],1),r("v-uni-view",{staticClass:"mtb-00-mlr-auto mt-44 w-588 font-24 text-3 l-h-34 text-center"},[r("v-uni-view",[t._v(t._s(t.currentItem.title))]),r("v-uni-view",{staticClass:"mt-12"},[t._v(t._s(t.currentItem.unique_int))]),r("v-uni-view",{staticClass:"mt-12"},[t._v(t._s(t.currentItem.order_time))]),t.currentItem.net_content?r("v-uni-view",{staticClass:"mt-12"},[t._v(t._s(t.currentItem.net_content))]):t._e(),r("v-uni-view",{staticClass:"mt-12"},[t._v(t._s(t.currentItem.category_name))]),t.currentItem.years?r("v-uni-view",{staticClass:"mt-12"},[t._v(t._s(t.currentItem.years))]):t._e()],1),r("v-uni-view",{staticClass:"p-abso bottom-60 right-130 flex-c-c w-100 h-100 bg-f5f5f5 b-rad-10"},[r("v-uni-canvas",{staticClass:"w-88 h-88",attrs:{"canvas-id":t.bigQrcodeCanvasId}})],1)],1)],1)]],2),t.$app&&!t.$isFirstPreview?r("v-uni-view",{staticClass:"d-flex j-center h-258 bg-f5f5f5"},[t.isFromMine?r("v-uni-view",{staticClass:"d-flex j-sb ptb-00-plr-48 w-p100 h-p100"},[r("v-uni-view",{staticClass:"pt-40 h-p100",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onShare.apply(void 0,arguments)}}},[r("v-uni-view",{staticClass:"flex-c-c"},[r("v-uni-image",{staticClass:"w-92 h-90",attrs:{src:t.ossIcon("/auction/wx_92_90.png")}})],1),r("v-uni-view",{staticClass:"mt-20 font-28 text-3 l-h-34 text-center"},[t._v("分享好友")])],1),r("v-uni-view",{staticClass:"pt-40 h-p100",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onMoments.apply(void 0,arguments)}}},[r("v-uni-view",{staticClass:"flex-c-c"},[r("v-uni-image",{staticClass:"w-92 h-90",attrs:{src:t.ossIcon("/auction/friends_92_90.png")}})],1),r("v-uni-view",{staticClass:"mt-20 font-28 text-3 l-h-34 text-center"},[t._v("分享朋友圈")])],1),r("v-uni-view",{staticClass:"pt-40 h-p100",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onDownload.apply(void 0,arguments)}}},[r("v-uni-view",{staticClass:"flex-c-c"},[r("v-uni-image",{staticClass:"w-92 h-90",attrs:{src:t.ossIcon("/auction/photo_album_92_90.png")}})],1),r("v-uni-view",{staticClass:"mt-20 font-28 text-3 l-h-34 text-center"},[t._v("保存相册")])],1),r("v-uni-view",{staticClass:"pt-40 h-p100",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onPostCard.apply(void 0,arguments)}}},[r("v-uni-view",{staticClass:"flex-c-c"},[r("v-uni-image",{staticClass:"w-92 h-90",attrs:{src:t.ossIcon("/auction/post_card_92_90.png")}})],1),r("v-uni-view",{staticClass:"mt-20 font-28 text-3 l-h-34 text-center"},[t._v("分享帖子")])],1)],1):[r("v-uni-view",{staticClass:"pt-40 h-p100",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onShare.apply(void 0,arguments)}}},[r("v-uni-view",{staticClass:"flex-c-c"},[r("v-uni-image",{staticClass:"w-90 h-90",attrs:{src:t.ossIcon("/auction/wx_90.png")}})],1),r("v-uni-view",{staticClass:"mt-20 font-wei-500 font-28 text-3 l-h-40 text-center"},[t._v("分享给好友")])],1),r("v-uni-view",{staticClass:"ml-200 pt-40 h-p100",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onDownload.apply(void 0,arguments)}}},[r("v-uni-view",{staticClass:"flex-c-c"},[r("v-uni-image",{staticClass:"w-90 h-90",attrs:{src:t.ossIcon("/auction/photo_ album_90.png")}})],1),r("v-uni-view",{staticClass:"mt-20 font-wei-500 font-28 text-3 l-h-40 text-center"},[t._v("保存至相册")])],1)]],2):t._e()],1),r("v-uni-image",{staticStyle:{display:"none"},attrs:{src:t.ossIcon("/auction/certificate_614_934.png")}}),r("v-uni-image",{staticStyle:{display:"none"},attrs:{src:t.ossIcon("/auction/certificate_750_1146.png")}})],1)},i=[]},"6c57":function(t,e,r){"use strict";var o=r("23e7"),n=r("da84");o({global:!0,forced:n.globalThis!==n},{globalThis:n})},"7b0c":function(t,e,r){"use strict";r.r(e);var o=r("d72e"),n=r.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(i);e["default"]=n.a},"7f1a":function(t,e,r){"use strict";r("7a82");var o=r("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=o(r("f3f3"));r("a9e3"),r("caad6"),r("2532");var i=r("26cb"),a=uni.getSystemInfoSync(),s={},u={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:a.statusBarHeight,appStatusBarHeight:0}},computed:(0,n.default)((0,n.default)({},(0,i.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(a.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(a.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,r=e.pEAddressAdd,o=e.pEAddressManagement,n=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(r)||t.includes(o)||t.includes(n))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=u},"82c9":function(t,e,r){(function(o){var n,i,a=r("9ffb").default,s=r("9705").default,u=r("12c0").default,c=r("62f5").default;r("6c57"),r("14d9"),r("d9e2"),r("d401"),r("1d1c"),r("a9e3"),r("d3b7"),r("159b"),r("b64b"),r("25f0"),r("caad6"),r("2532"),r("cb29"),function(o,a){"object"==c(e)&&"undefined"!=typeof t?t.exports=a():(n=a,i="function"===typeof n?n.call(e,r,e,t):n,void 0===i||(t.exports=i))}("undefined"!==typeof window&&window,(function(){function t(t){this.mode=r.MODE_8BIT_BYTE,this.data=t}function e(t,e){this.typeNumber=t,this.errorCorrectLevel=e,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=new Array}t.prototype={getLength:function(t){return this.data.length},write:function(t){for(var e=0;e<this.data.length;e++)t.put(this.data.charCodeAt(e),8)}},e.prototype={addData:function(e){var r=new t(e);this.dataList.push(r),this.dataCache=null},isDark:function(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw new Error(t+","+e);return this.modules[t][e]},getModuleCount:function(){return this.moduleCount},make:function(){if(this.typeNumber<1){var t=1;for(t=1;t<40;t++){for(var e=l.getRSBlocks(t,this.errorCorrectLevel),r=new f,o=0,i=0;i<e.length;i++)o+=e[i].dataCount;for(i=0;i<this.dataList.length;i++){var a=this.dataList[i];r.put(a.mode,4),r.put(a.getLength(),n.getLengthInBits(a.mode,t)),a.write(r)}if(r.getLengthInBits()<=8*o)break}this.typeNumber=t}this.makeImpl(!1,this.getBestMaskPattern())},makeImpl:function(t,r){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var o=0;o<this.moduleCount;o++){this.modules[o]=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++)this.modules[o][n]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,r),this.typeNumber>=7&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=e.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,r)},setupPositionProbePattern:function(t,e){for(var r=-1;r<=7;r++)if(!(t+r<=-1||this.moduleCount<=t+r))for(var o=-1;o<=7;o++)e+o<=-1||this.moduleCount<=e+o||(this.modules[t+r][e+o]=0<=r&&r<=6&&(0==o||6==o)||0<=o&&o<=6&&(0==r||6==r)||2<=r&&r<=4&&2<=o&&o<=4)},getBestMaskPattern:function(){for(var t=0,e=0,r=0;r<8;r++){this.makeImpl(!0,r);var o=n.getLostPoint(this);(0==r||t>o)&&(t=o,e=r)}return e},createMovieClip:function(t,e,r){var o=t.createEmptyMovieClip(e,r);this.make();for(var n=0;n<this.modules.length;n++)for(var i=1*n,a=0;a<this.modules[n].length;a++){var s=1*a;this.modules[n][a]&&(o.beginFill(0,100),o.moveTo(s,i),o.lineTo(s+1,i),o.lineTo(s+1,i+1),o.lineTo(s,i+1),o.endFill())}return o},setupTimingPattern:function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(var e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)},setupPositionAdjustPattern:function(){for(var t=n.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var r=0;r<t.length;r++){var o=t[e],i=t[r];if(null==this.modules[o][i])for(var a=-2;a<=2;a++)for(var s=-2;s<=2;s++)this.modules[o+a][i+s]=-2==a||2==a||-2==s||2==s||0==a&&0==s}},setupTypeNumber:function(t){for(var e=n.getBCHTypeNumber(this.typeNumber),r=0;r<18;r++){var o=!t&&1==(e>>r&1);this.modules[Math.floor(r/3)][r%3+this.moduleCount-8-3]=o}for(r=0;r<18;r++)o=!t&&1==(e>>r&1),this.modules[r%3+this.moduleCount-8-3][Math.floor(r/3)]=o},setupTypeInfo:function(t,e){for(var r=this.errorCorrectLevel<<3|e,o=n.getBCHTypeInfo(r),i=0;i<15;i++){var a=!t&&1==(o>>i&1);i<6?this.modules[i][8]=a:i<8?this.modules[i+1][8]=a:this.modules[this.moduleCount-15+i][8]=a}for(i=0;i<15;i++)a=!t&&1==(o>>i&1),i<8?this.modules[8][this.moduleCount-i-1]=a:i<9?this.modules[8][15-i-1+1]=a:this.modules[8][15-i-1]=a;this.modules[this.moduleCount-8][8]=!t},mapData:function(t,e){for(var r=-1,o=this.moduleCount-1,i=7,a=0,s=this.moduleCount-1;s>0;s-=2)for(6==s&&s--;;){for(var u=0;u<2;u++)if(null==this.modules[o][s-u]){var c=!1;a<t.length&&(c=1==(t[a]>>>i&1)),n.getMask(e,o,s-u)&&(c=!c),this.modules[o][s-u]=c,-1==--i&&(a++,i=7)}if((o+=r)<0||this.moduleCount<=o){o-=r,r=-r;break}}}},e.PAD0=236,e.PAD1=17,e.createData=function(t,r,o){for(var i=l.getRSBlocks(t,r),a=new f,s=0;s<o.length;s++){var u=o[s];a.put(u.mode,4),a.put(u.getLength(),n.getLengthInBits(u.mode,t)),u.write(a)}var c=0;for(s=0;s<i.length;s++)c+=i[s].dataCount;if(a.getLengthInBits()>8*c)throw new Error("code length overflow. ("+a.getLengthInBits()+">"+8*c+")");for(a.getLengthInBits()+4<=8*c&&a.put(0,4);a.getLengthInBits()%8!=0;)a.putBit(!1);for(;!(a.getLengthInBits()>=8*c||(a.put(e.PAD0,8),a.getLengthInBits()>=8*c));)a.put(e.PAD1,8);return e.createBytes(a,i)},e.createBytes=function(t,e){for(var r=0,o=0,i=0,a=new Array(e.length),s=new Array(e.length),u=0;u<e.length;u++){var c=e[u].dataCount,l=e[u].totalCount-c;o=Math.max(o,c),i=Math.max(i,l),a[u]=new Array(c);for(var f=0;f<a[u].length;f++)a[u][f]=255&t.buffer[f+r];r+=c;var h=n.getErrorCorrectPolynomial(l),g=new d(a[u],h.getLength()-1).mod(h);for(s[u]=new Array(h.getLength()-1),f=0;f<s[u].length;f++){var v=f+g.getLength()-s[u].length;s[u][f]=v>=0?g.get(v):0}}var p=0;for(f=0;f<e.length;f++)p+=e[f].totalCount;var m=new Array(p),b=0;for(f=0;f<o;f++)for(u=0;u<e.length;u++)f<a[u].length&&(m[b++]=a[u][f]);for(f=0;f<i;f++)for(u=0;u<e.length;u++)f<s[u].length&&(m[b++]=s[u][f]);return m};for(var r={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},o={L:1,M:0,Q:3,H:2},n={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(t){for(var e=t<<10;n.getBCHDigit(e)-n.getBCHDigit(n.G15)>=0;)e^=n.G15<<n.getBCHDigit(e)-n.getBCHDigit(n.G15);return(t<<10|e)^n.G15_MASK},getBCHTypeNumber:function(t){for(var e=t<<12;n.getBCHDigit(e)-n.getBCHDigit(n.G18)>=0;)e^=n.G18<<n.getBCHDigit(e)-n.getBCHDigit(n.G18);return t<<12|e},getBCHDigit:function(t){for(var e=0;0!=t;)e++,t>>>=1;return e},getPatternPosition:function(t){return n.PATTERN_POSITION_TABLE[t-1]},getMask:function(t,e,r){switch(t){case 0:return(e+r)%2==0;case 1:return e%2==0;case 2:return r%3==0;case 3:return(e+r)%3==0;case 4:return(Math.floor(e/2)+Math.floor(r/3))%2==0;case 5:return e*r%2+e*r%3==0;case 6:return(e*r%2+e*r%3)%2==0;case 7:return(e*r%3+(e+r)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}},getErrorCorrectPolynomial:function(t){for(var e=new d([1],0),r=0;r<t;r++)e=e.multiply(new d([1,i.gexp(r)],0));return e},getLengthInBits:function(t,e){if(1<=e&&e<10)switch(t){case r.MODE_NUMBER:return 10;case r.MODE_ALPHA_NUM:return 9;case r.MODE_8BIT_BYTE:case r.MODE_KANJI:return 8;default:throw new Error("mode:"+t)}else if(e<27)switch(t){case r.MODE_NUMBER:return 12;case r.MODE_ALPHA_NUM:return 11;case r.MODE_8BIT_BYTE:return 16;case r.MODE_KANJI:return 10;default:throw new Error("mode:"+t)}else{if(!(e<41))throw new Error("type:"+e);switch(t){case r.MODE_NUMBER:return 14;case r.MODE_ALPHA_NUM:return 13;case r.MODE_8BIT_BYTE:return 16;case r.MODE_KANJI:return 12;default:throw new Error("mode:"+t)}}},getLostPoint:function(t){for(var e=t.getModuleCount(),r=0,o=0;o<e;o++)for(var n=0;n<e;n++){for(var i=0,a=t.isDark(o,n),s=-1;s<=1;s++)if(!(o+s<0||e<=o+s))for(var u=-1;u<=1;u++)n+u<0||e<=n+u||0==s&&0==u||a==t.isDark(o+s,n+u)&&i++;i>5&&(r+=3+i-5)}for(o=0;o<e-1;o++)for(n=0;n<e-1;n++){var c=0;t.isDark(o,n)&&c++,t.isDark(o+1,n)&&c++,t.isDark(o,n+1)&&c++,t.isDark(o+1,n+1)&&c++,0!=c&&4!=c||(r+=3)}for(o=0;o<e;o++)for(n=0;n<e-6;n++)t.isDark(o,n)&&!t.isDark(o,n+1)&&t.isDark(o,n+2)&&t.isDark(o,n+3)&&t.isDark(o,n+4)&&!t.isDark(o,n+5)&&t.isDark(o,n+6)&&(r+=40);for(n=0;n<e;n++)for(o=0;o<e-6;o++)t.isDark(o,n)&&!t.isDark(o+1,n)&&t.isDark(o+2,n)&&t.isDark(o+3,n)&&t.isDark(o+4,n)&&!t.isDark(o+5,n)&&t.isDark(o+6,n)&&(r+=40);var d=0;for(n=0;n<e;n++)for(o=0;o<e;o++)t.isDark(o,n)&&d++;return r+Math.abs(100*d/e/e-50)/5*10}},i={glog:function(t){if(t<1)throw new Error("glog("+t+")");return i.LOG_TABLE[t]},gexp:function(t){for(;t<0;)t+=255;for(;t>=256;)t-=255;return i.EXP_TABLE[t]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)},c=0;c<8;c++)i.EXP_TABLE[c]=1<<c;for(c=8;c<256;c++)i.EXP_TABLE[c]=i.EXP_TABLE[c-4]^i.EXP_TABLE[c-5]^i.EXP_TABLE[c-6]^i.EXP_TABLE[c-8];for(c=0;c<255;c++)i.LOG_TABLE[i.EXP_TABLE[c]]=c;function d(t,e){if(null==t.length)throw new Error(t.length+"/"+e);for(var r=0;r<t.length&&0==t[r];)r++;this.num=new Array(t.length-r+e);for(var o=0;o<t.length-r;o++)this.num[o]=t[o+r]}function l(t,e){this.totalCount=t,this.dataCount=e}function f(){this.buffer=new Array,this.length=0}function h(t){return t.setFillStyle=t.setFillStyle||function(e){t.fillStyle=e},t.setFontSize=t.setFontSize||function(e){t.font="".concat(e,"px")},t.setTextAlign=t.setTextAlign||function(e){t.textAlign=e},t.setTextBaseline=t.setTextBaseline||function(e){t.textBaseline=e},t.setGlobalAlpha=t.setGlobalAlpha||function(e){t.globalAlpha=e},t.setStrokeStyle=t.setStrokeStyle||function(e){t.strokeStyle=e},t.setShadow=t.setShadow||function(e,r,o,n){t.shadowOffsetX=e,t.shadowOffsetY=r,t.shadowBlur=o,t.shadowColor=n},t.draw=t.draw||function(t,e){e&&e()},t}function g(t,e){var r=this,o=this.data="";this.dataEncode=!0;var n=this.size=200;this.useDynamicSize=!1,this.dynamicSize=n;var i=this.typeNumber=-1;this.errorCorrectLevel=g.errorCorrectLevel.H;var a=this.margin=0;this.areaColor="#FFFFFF",this.backgroundColor="rgba(255,255,255,0)",this.backgroundImageSrc=void 0;var s=this.backgroundImageWidth=void 0,u=this.backgroundImageHeight=void 0,c=this.backgroundImageX=void 0,d=this.backgroundImageY=void 0;this.backgroundImageAlpha=1,this.backgroundImageBorderRadius=0;var l=this.backgroundPadding=0;this.foregroundColor="#000000",this.foregroundImageSrc=void 0;var f=this.foregroundImageWidth=void 0,v=this.foregroundImageHeight=void 0,p=this.foregroundImageX=void 0,m=this.foregroundImageY=void 0,b=this.foregroundImagePadding=0;this.foregroundImageBackgroundColor="#FFFFFF";var y=this.foregroundImageBorderRadius=0,w=this.foregroundImageShadowOffsetX=0,C=this.foregroundImageShadowOffsetY=0,_=this.foregroundImageShadowBlur=0;this.foregroundImageShadowColor="#808080";var k=this.foregroundPadding=0,x=this.positionProbeBackgroundColor=void 0,I=this.positionProbeForegroundColor=void 0,S=this.separatorColor=void 0,B=this.positionAdjustBackgroundColor=void 0,P=this.positionAdjustForegroundColor=void 0,E=this.timingBackgroundColor=void 0,L=this.timingForegroundColor=void 0,T=this.typeNumberBackgroundColor=void 0,z=this.typeNumberForegroundColor=void 0,A=this.darkBlockColor=void 0;this.base=void 0,this.modules=[],this.moduleCount=0,this.drawModules=[];var M=this.canvasContext=void 0;this.loadImage,this.drawReserve=!1,this.isMaked=!1,Object.defineProperties(this,{data:{get:function(){if(""===o||void 0===o)throw console.error("[uQRCode]: data must be set!"),new g.Error("data must be set!");return o},set:function(t){o=String(t)}},size:{get:function(){return n},set:function(t){n=Number(t)}},typeNumber:{get:function(){return i},set:function(t){i=Number(t)}},margin:{get:function(){return a},set:function(t){a=Number(t)}},backgroundImageWidth:{get:function(){return void 0===s?this.dynamicSize:this.useDynamicSize?this.dynamicSize/this.size*s:s},set:function(t){s=Number(t)}},backgroundImageHeight:{get:function(){return void 0===u?this.dynamicSize:this.useDynamicSize?this.dynamicSize/this.size*u:u},set:function(t){u=Number(t)}},backgroundImageX:{get:function(){return void 0===c?0:this.useDynamicSize?this.dynamicSize/this.size*c:c},set:function(t){c=Number(t)}},backgroundImageY:{get:function(){return void 0===d?0:this.useDynamicSize?this.dynamicSize/this.size*d:d},set:function(t){d=Number(t)}},backgroundPadding:{get:function(){return l},set:function(t){l=t>1?1:t<0?0:t}},foregroundImageWidth:{get:function(){return void 0===f?(this.dynamicSize-2*this.margin)/4:this.useDynamicSize?this.dynamicSize/this.size*f:f},set:function(t){f=Number(t)}},foregroundImageHeight:{get:function(){return void 0===v?(this.dynamicSize-2*this.margin)/4:this.useDynamicSize?this.dynamicSize/this.size*v:v},set:function(t){v=Number(t)}},foregroundImageX:{get:function(){return void 0===p?this.dynamicSize/2-this.foregroundImageWidth/2:this.useDynamicSize?this.dynamicSize/this.size*p:p},set:function(t){p=Number(t)}},foregroundImageY:{get:function(){return void 0===m?this.dynamicSize/2-this.foregroundImageHeight/2:this.useDynamicSize?this.dynamicSize/this.size*m:m},set:function(t){m=Number(t)}},foregroundImagePadding:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*b:b},set:function(t){b=Number(t)}},foregroundImageBorderRadius:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*y:y},set:function(t){y=Number(t)}},foregroundImageShadowOffsetX:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*w:w},set:function(t){w=Number(t)}},foregroundImageShadowOffsetY:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*C:C},set:function(t){C=Number(t)}},foregroundImageShadowBlur:{get:function(){return this.useDynamicSize?this.dynamicSize/this.size*_:_},set:function(t){_=Number(t)}},foregroundPadding:{get:function(){return k},set:function(t){k=t>1?1:t<0?0:t}},positionProbeBackgroundColor:{get:function(){return x||this.backgroundColor},set:function(t){x=t}},positionProbeForegroundColor:{get:function(){return I||this.foregroundColor},set:function(t){I=t}},separatorColor:{get:function(){return S||this.backgroundColor},set:function(t){S=t}},positionAdjustBackgroundColor:{get:function(){return B||this.backgroundColor},set:function(t){B=t}},positionAdjustForegroundColor:{get:function(){return P||this.foregroundColor},set:function(t){P=t}},timingBackgroundColor:{get:function(){return E||this.backgroundColor},set:function(t){E=t}},timingForegroundColor:{get:function(){return L||this.foregroundColor},set:function(t){L=t}},typeNumberBackgroundColor:{get:function(){return T||this.backgroundColor},set:function(t){T=t}},typeNumberForegroundColor:{get:function(){return z||this.foregroundColor},set:function(t){z=t}},darkBlockColor:{get:function(){return A||this.foregroundColor},set:function(t){A=t}},canvasContext:{get:function(){if(void 0===M)throw console.error("[uQRCode]: use drawCanvas, you need to set the canvasContext!"),new g.Error("use drawCanvas, you need to set the canvasContext!");return M},set:function(t){M=h(t)}}}),g.plugins.forEach((function(t){return t(g,r,!1)})),t&&this.setOptions(t),e&&(this.canvasContext=h(e))}return d.prototype={get:function(t){return this.num[t]},getLength:function(){return this.num.length},multiply:function(t){for(var e=new Array(this.getLength()+t.getLength()-1),r=0;r<this.getLength();r++)for(var o=0;o<t.getLength();o++)e[r+o]^=i.gexp(i.glog(this.get(r))+i.glog(t.get(o)));return new d(e,0)},mod:function(t){if(this.getLength()-t.getLength()<0)return this;for(var e=i.glog(this.get(0))-i.glog(t.get(0)),r=new Array(this.getLength()),o=0;o<this.getLength();o++)r[o]=this.get(o);for(o=0;o<t.getLength();o++)r[o]^=i.gexp(i.glog(t.get(o))+e);return new d(r,0).mod(t)}},l.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],l.getRSBlocks=function(t,e){var r=l.getRsBlockTable(t,e);if(null==r)throw new Error("bad rs block @ typeNumber:"+t+"/errorCorrectLevel:"+e);for(var o=r.length/3,n=new Array,i=0;i<o;i++)for(var a=r[3*i+0],s=r[3*i+1],u=r[3*i+2],c=0;c<a;c++)n.push(new l(s,u));return n},l.getRsBlockTable=function(t,e){switch(e){case o.L:return l.RS_BLOCK_TABLE[4*(t-1)+0];case o.M:return l.RS_BLOCK_TABLE[4*(t-1)+1];case o.Q:return l.RS_BLOCK_TABLE[4*(t-1)+2];case o.H:return l.RS_BLOCK_TABLE[4*(t-1)+3];default:return}},f.prototype={get:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var r=0;r<e;r++)this.putBit(1==(t>>>e-r-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},e.errorCorrectLevel=o,g.errorCorrectLevel=e.errorCorrectLevel,g.Error=function(t){this.errMsg="[uQRCode]: "+t},g.plugins=[],g.use=function(t){"function"==typeof t&&g.plugins.push(t)},g.prototype.loadImage=function(t){return Promise.resolve(t)},g.prototype.setOptions=function(t){var e,r,o,n,i,a,s,c,d,l,f,h,g,v,p,m,b,y,w,C,_,k,x,I,S,B,P,E,L,T,z,A,M,O,N,D,F,$,j,R,H,Y,X,U,Q,G,W,q,V,K,J,Z,tt,et,rt,ot,nt=this;t&&(Object.keys(t).forEach((function(e){nt[e]=t[e]})),function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(var n in t=o?e:u({},e),r){var i=r[n];null!=i&&(i.constructor==Object?t[n]=this.deepReplace(t[n],i):i.constructor!=String||i?t[n]=i:t[n]=t[n])}}(this,{data:t.data||t.text,dataEncode:t.dataEncode,size:t.size,useDynamicSize:t.useDynamicSize,typeNumber:t.typeNumber,errorCorrectLevel:t.errorCorrectLevel,margin:t.margin,areaColor:t.areaColor,backgroundColor:t.backgroundColor||(null===(e=t.background)||void 0===e?void 0:e.color),backgroundImageSrc:t.backgroundImageSrc||(null===(r=t.background)||void 0===r||null===(o=r.image)||void 0===o?void 0:o.src),backgroundImageWidth:t.backgroundImageWidth||(null===(n=t.background)||void 0===n||null===(i=n.image)||void 0===i?void 0:i.width),backgroundImageHeight:t.backgroundImageHeight||(null===(a=t.background)||void 0===a||null===(s=a.image)||void 0===s?void 0:s.height),backgroundImageX:t.backgroundImageX||(null===(c=t.background)||void 0===c||null===(d=c.image)||void 0===d?void 0:d.x),backgroundImageY:t.backgroundImageY||(null===(l=t.background)||void 0===l||null===(f=l.image)||void 0===f?void 0:f.y),backgroundImageAlpha:t.backgroundImageAlpha||(null===(h=t.background)||void 0===h||null===(g=h.image)||void 0===g?void 0:g.alpha),backgroundImageBorderRadius:t.backgroundImageBorderRadius||(null===(v=t.background)||void 0===v||null===(p=v.image)||void 0===p?void 0:p.borderRadius),backgroundPadding:t.backgroundPadding,foregroundColor:t.foregroundColor||(null===(m=t.foreground)||void 0===m?void 0:m.color),foregroundImageSrc:t.foregroundImageSrc||(null===(b=t.foreground)||void 0===b||null===(y=b.image)||void 0===y?void 0:y.src),foregroundImageWidth:t.foregroundImageWidth||(null===(w=t.foreground)||void 0===w||null===(C=w.image)||void 0===C?void 0:C.width),foregroundImageHeight:t.foregroundImageHeight||(null===(_=t.foreground)||void 0===_||null===(k=_.image)||void 0===k?void 0:k.height),foregroundImageX:t.foregroundImageX||(null===(x=t.foreground)||void 0===x||null===(I=x.image)||void 0===I?void 0:I.x),foregroundImageY:t.foregroundImageY||(null===(S=t.foreground)||void 0===S||null===(B=S.image)||void 0===B?void 0:B.y),foregroundImagePadding:t.foregroundImagePadding||(null===(P=t.foreground)||void 0===P||null===(E=P.image)||void 0===E?void 0:E.padding),foregroundImageBackgroundColor:t.foregroundImageBackgroundColor||(null===(L=t.foreground)||void 0===L||null===(T=L.image)||void 0===T?void 0:T.backgroundColor),foregroundImageBorderRadius:t.foregroundImageBorderRadius||(null===(z=t.foreground)||void 0===z||null===(A=z.image)||void 0===A?void 0:A.borderRadius),foregroundImageShadowOffsetX:t.foregroundImageShadowOffsetX||(null===(M=t.foreground)||void 0===M||null===(O=M.image)||void 0===O?void 0:O.shadowOffsetX),foregroundImageShadowOffsetY:t.foregroundImageShadowOffsetY||(null===(N=t.foreground)||void 0===N||null===(D=N.image)||void 0===D?void 0:D.shadowOffsetY),foregroundImageShadowBlur:t.foregroundImageShadowBlur||(null===(F=t.foreground)||void 0===F||null===($=F.image)||void 0===$?void 0:$.shadowBlur),foregroundImageShadowColor:t.foregroundImageShadowColor||(null===(j=t.foreground)||void 0===j||null===(R=j.image)||void 0===R?void 0:R.shadowColor),foregroundPadding:t.foregroundPadding,positionProbeBackgroundColor:t.positionProbeBackgroundColor||(null===(H=t.positionProbe)||void 0===H?void 0:H.backgroundColor)||(null===(Y=t.positionDetection)||void 0===Y?void 0:Y.backgroundColor),positionProbeForegroundColor:t.positionProbeForegroundColor||(null===(X=t.positionProbe)||void 0===X?void 0:X.foregroundColor)||(null===(U=t.positionDetection)||void 0===U?void 0:U.foregroundColor),separatorColor:t.separatorColor||(null===(Q=t.separator)||void 0===Q?void 0:Q.color),positionAdjustBackgroundColor:t.positionAdjustBackgroundColor||(null===(G=t.positionAdjust)||void 0===G?void 0:G.backgroundColor)||(null===(W=t.alignment)||void 0===W?void 0:W.backgroundColor),positionAdjustForegroundColor:t.positionAdjustForegroundColor||(null===(q=t.positionAdjust)||void 0===q?void 0:q.foregroundColor)||(null===(V=t.alignment)||void 0===V?void 0:V.foregroundColor),timingBackgroundColor:t.timingBackgroundColor||(null===(K=t.timing)||void 0===K?void 0:K.backgroundColor),timingForegroundColor:t.timingForegroundColor||(null===(J=t.timing)||void 0===J?void 0:J.foregroundColor),typeNumberBackgroundColor:t.typeNumberBackgroundColor||(null===(Z=t.typeNumber)||void 0===Z?void 0:Z.backgroundColor)||(null===(tt=t.versionInformation)||void 0===tt?void 0:tt.backgroundColor),typeNumberForegroundColor:t.typeNumberForegroundColor||(null===(et=t.typeNumber)||void 0===et?void 0:et.foregroundColor)||(null===(rt=t.versionInformation)||void 0===rt?void 0:rt.foregroundColor),darkBlockColor:t.darkBlockColor||(null===(ot=t.darkBlock)||void 0===ot?void 0:ot.color)},!0))},g.prototype.make=function(){var t=this.foregroundColor,r=this.backgroundColor,o=this.typeNumber,n=this.errorCorrectLevel,i=this.data,a=this.dataEncode,s=this.size,u=this.margin,c=this.useDynamicSize;if(t===r)throw console.error("[uQRCode]: foregroundColor and backgroundColor cannot be the same!"),new g.Error("foregroundColor and backgroundColor cannot be the same!");a&&(i=function(t){t=t.toString();for(var e,r="",o=0;o<t.length;o++)(e=t.charCodeAt(o))>=1&&e<=127?r+=t.charAt(o):e>2047?(r+=String.fromCharCode(224|e>>12&15),r+=String.fromCharCode(128|e>>6&63),r+=String.fromCharCode(128|e>>0&63)):(r+=String.fromCharCode(192|e>>6&31),r+=String.fromCharCode(128|e>>0&63));return r}(i));var d=new e(o,n);d.addData(i),d.make(),this.base=d,this.typeNumber=d.typeNumber,this.modules=d.modules,this.moduleCount=d.moduleCount,this.dynamicSize=c?Math.ceil((s-2*u)/d.moduleCount)*d.moduleCount+2*u:s,function(t){var e=t.dynamicSize,r=t.margin,o=t.backgroundColor,n=t.backgroundPadding,i=t.foregroundColor,a=t.foregroundPadding,s=t.modules,u=t.moduleCount,c=(e-2*r)/u,d=c,l=0;n>0&&(d-=2*(l=d*n/2));var f=c,h=0;a>0&&(f-=2*(h=f*a/2));for(var g=0;g<u;g++)for(var v=0;v<u;v++){var p=v*c+r,m=g*c+r;if(s[g][v]){var b=h,y=p+h,w=m+h,C=f,_=f;s[g][v]={type:["foreground"],color:i,isBlack:!0,isDrawn:!1,destX:p,destY:m,destWidth:c,destHeight:c,x:y,y:w,width:C,height:_,paddingTop:b,paddingRight:b,paddingBottom:b,paddingLeft:b}}else b=l,y=p+l,w=m+l,C=d,_=d,s[g][v]={type:["background"],color:o,isBlack:!1,isDrawn:!1,destX:p,destY:m,destWidth:c,destHeight:c,x:y,y:w,width:C,height:_,paddingTop:b,paddingRight:b,paddingBottom:b,paddingLeft:b}}}(this),function(t){var e=t.modules,r=t.moduleCount,o=t.positionProbeBackgroundColor,n=t.positionProbeForegroundColor,i=r-7;[[0,0,1],[1,0,1],[2,0,1],[3,0,1],[4,0,1],[5,0,1],[6,0,1],[0,1,1],[1,1,0],[2,1,0],[3,1,0],[4,1,0],[5,1,0],[6,1,1],[0,2,1],[1,2,0],[2,2,1],[3,2,1],[4,2,1],[5,2,0],[6,2,1],[0,3,1],[1,3,0],[2,3,1],[3,3,1],[4,3,1],[5,3,0],[6,3,1],[0,4,1],[1,4,0],[2,4,1],[3,4,1],[4,4,1],[5,4,0],[6,4,1],[0,5,1],[1,5,0],[2,5,0],[3,5,0],[4,5,0],[5,5,0],[6,5,1],[0,6,1],[1,6,1],[2,6,1],[3,6,1],[4,6,1],[5,6,1],[6,6,1]].forEach((function(t){var r=e[t[0]][t[1]],a=e[t[0]+i][t[1]],s=e[t[0]][t[1]+i];s.type.push("positionProbe"),a.type.push("positionProbe"),r.type.push("positionProbe"),r.color=1==t[2]?n:o,a.color=1==t[2]?n:o,s.color=1==t[2]?n:o}))}(this),function(t){var e=t.modules,r=t.moduleCount,o=t.separatorColor;[[7,0],[7,1],[7,2],[7,3],[7,4],[7,5],[7,6],[7,7],[0,7],[1,7],[2,7],[3,7],[4,7],[5,7],[6,7]].forEach((function(t){var n=e[t[0]][t[1]],i=e[r-t[0]-1][t[1]],a=e[t[0]][r-t[1]-1];a.type.push("separator"),i.type.push("separator"),n.type.push("separator"),n.color=o,i.color=o,a.color=o}))}(this),function(t){var e=t.typeNumber,r=t.modules,o=t.moduleCount,n=t.foregroundColor,i=t.backgroundColor,a=t.positionAdjustForegroundColor,s=t.positionAdjustBackgroundColor,u=t.timingForegroundColor,c=t.timingBackgroundColor,d=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]][e-1];if(d)for(var l=[[-2,-2,1],[-1,-2,1],[0,-2,1],[1,-2,1],[2,-2,1],[-2,-1,1],[-1,-1,0],[0,-1,0],[1,-1,0],[2,-1,1],[-2,0,1],[-1,0,0],[0,0,1],[1,0,0],[2,0,1],[-2,1,1],[-1,1,0],[0,1,0],[1,1,0],[2,1,1],[-2,2,1],[-1,2,1],[0,2,1],[1,2,1],[2,2,1]],f=d.length,h=0;h<f;h++)for(var g=0;g<f;g++){var v={x:d[h],y:d[g]},p=v.x,m=v.y;p<9&&m<9||p>o-9-1&&m<9||m>o-9-1&&p<9||l.forEach((function(t){var e=r[p+t[0]][m+t[1]];e.type.push("positionAdjust"),e.type.includes("timing")?1==t[2]?e.color=a==n?u:a:e.color=a==n&&s==i?c:s:e.color=1==t[2]?a:s}))}}(this),function(t){for(var e=t.modules,r=t.moduleCount,o=t.timingForegroundColor,n=t.timingBackgroundColor,i=r-16,a=0;a<i;a++){var s=e[6][8+a],u=e[8+a][6];s.type.push("timing"),u.type.push("timing"),s.color=1&a^1?o:n,u.color=1&a^1?o:n}}(this),function(t){var e=t.modules,r=t.moduleCount,o=t.darkBlockColor,n=e[r-7-1][8];n.type.push("darkBlock"),n.color=o}(this),function(t){var e=t.typeNumber,r=t.modules,o=t.moduleCount,n=t.typeNumberBackgroundColor,i=t.typeNumberForegroundColor;if(e<7)return r;var a=[0,0,0,0,0,0,0,"000111110010010100","001000010110111100","001001101010011001","001010010011010011","001011101111110110","001100011101100010","001101100001000111","001110011000001101","001111100100101000","010000101101111000","010001010001011101","010010101000010111","010011010100110010","010100100110100110","010101011010000011","010110100011001001","010111011111101100","011000111011000100","011001000111100001","011010111110101011","011011000010001110","011100110000011010","011101001100111111","011110110101110101","011111001001010000","100000100111010101","100001011011110000","100010100010111010","100011011110011111","100100101100001011","100101010000101110","100110101001100100","100111010101000001","101000110001101001"],s=a[e]+a[e],u=[o-11,o-10,o-9];[[5,u[2]],[5,u[1]],[5,u[0]],[4,u[2]],[4,u[1]],[4,u[0]],[3,u[2]],[3,u[1]],[3,u[0]],[2,u[2]],[2,u[1]],[2,u[0]],[1,u[2]],[1,u[1]],[1,u[0]],[0,u[2]],[0,u[1]],[0,u[0]],[u[2],5],[u[1],5],[u[0],5],[u[2],4],[u[1],4],[u[0],4],[u[2],3],[u[1],3],[u[0],3],[u[2],2],[u[1],2],[u[0],2],[u[2],1],[u[1],1],[u[0],1],[u[2],0],[u[1],0],[u[0],0]].forEach((function(t,e){var o=r[t[0]][t[1]];o.type.push("typeNumber"),o.color="1"==s[e]?i:n}))}(this),this.isMaked=!0,this.drawModules=[]},g.prototype.getDrawModules=function(){if(this.drawModules&&this.drawModules.length>0)return this.drawModules;var t=this.drawModules=[],e=this.modules,r=this.moduleCount,o=this.dynamicSize,n=this.areaColor,i=this.backgroundImageSrc,a=this.backgroundImageX,s=this.backgroundImageY,u=this.backgroundImageWidth,c=this.backgroundImageHeight,d=this.backgroundImageAlpha,l=this.backgroundImageBorderRadius,f=this.foregroundImageSrc,h=this.foregroundImageX,g=this.foregroundImageY,v=this.foregroundImageWidth,p=this.foregroundImageHeight,m=this.foregroundImagePadding,b=this.foregroundImageBackgroundColor,y=this.foregroundImageBorderRadius,w=this.foregroundImageShadowOffsetX,C=this.foregroundImageShadowOffsetY,_=this.foregroundImageShadowBlur,k=this.foregroundImageShadowColor;n&&t.push({name:"area",type:"area",color:n,x:0,y:0,width:o,height:o}),i&&t.push({name:"backgroundImage",type:"image",imageSrc:i,mappingName:"backgroundImageSrc",x:a,y:s,width:u,height:c,alpha:d,borderRadius:l});for(var x=0;x<r;x++)for(var I=0;I<r;I++){var S=e[x][I];S.isDrawn||(S.type.includes("foreground")?t.push({name:"foreground",type:"tile",color:S.color,destX:S.destX,destY:S.destY,destWidth:S.destWidth,destHeight:S.destHeight,x:S.x,y:S.y,width:S.width,height:S.height,paddingTop:S.paddingTop,paddingRight:S.paddingRight,paddingBottom:S.paddingBottom,paddingLeft:S.paddingLeft,rowIndex:x,colIndex:I}):t.push({name:"background",type:"tile",color:S.color,destX:S.destX,destY:S.destY,destWidth:S.destWidth,destHeight:S.destHeight,x:S.x,y:S.y,width:S.width,height:S.height,paddingTop:S.paddingTop,paddingRight:S.paddingRight,paddingBottom:S.paddingBottom,paddingLeft:S.paddingLeft,rowIndex:x,colIndex:I}),S.isDrawn=!0)}return f&&t.push({name:"foregroundImage",type:"image",imageSrc:f,mappingName:"foregroundImageSrc",x:h,y:g,width:v,height:p,padding:m,backgroundColor:b,borderRadius:y,shadowOffsetX:w,shadowOffsetY:C,shadowBlur:_,shadowColor:k}),t},g.prototype.isBlack=function(t,e){var r=this.moduleCount;return!(0>t||0>e||t>=r||e>=r)&&this.modules[t][e].isBlack},g.prototype.drawCanvas=function(t){var e=this,r=this.isMaked,o=this.canvasContext,n=(this.useDynamicSize,this.dynamicSize,this.foregroundColor,this.foregroundPadding,this.backgroundColor,this.backgroundPadding,this.drawReserve);this.margin;if(!r)return console.error("[uQRCode]: please execute the make method first!"),Promise.reject(new g.Error("please execute the make method first!"));var i=this.getDrawModules(),u=function(){var r=s(a().mark((function r(s,u){var c,d,l,f,h,v,p,m,b,y,w,C,_,k;return a().wrap((function(r){while(1)switch(r.prev=r.next){case 0:r.prev=0,o.draw(t),c=0;case 3:if(!(c<i.length)){r.next=48;break}d=i[c],r.t0=(o.save(),d.type),r.next="area"===r.t0?8:"tile"===r.t0?10:"image"===r.t0?13:44;break;case 8:return o.setFillStyle(d.color),o.fillRect(d.x,d.y,d.width,d.height),r.abrupt("break",44);case 10:return l=d.x,f=d.y,h=d.width,v=d.height,o.setFillStyle(d.color),o.fillRect(l,f,h,v),r.abrupt("break",44);case 13:if("backgroundImage"!==d.name){r.next=28;break}return l=Math.round(d.x),f=Math.round(d.y),h=Math.round(d.width),v=Math.round(d.height),h<2*(m=Math.round(d.borderRadius))&&(m=h/2),v<2*m&&(m=v/2),o.setGlobalAlpha(d.alpha),m>0&&(o.beginPath(),o.moveTo(l+m,f),o.arcTo(l+h,f,l+h,f+v,m),o.arcTo(l+h,f+v,l,f+v,m),o.arcTo(l,f+v,l,f,m),o.arcTo(l,f,l+h,f,m),o.closePath(),o.setStrokeStyle("rgba(0,0,0,0)"),o.stroke(),o.clip()),r.prev=16,r.next=19,e.loadImage(d.imageSrc);case 19:p=r.sent,o.drawImage(p,l,f,h,v),r.next=26;break;case 23:throw r.prev=23,r.t1=r["catch"](16),console.error("[uQRCode]: ".concat(d.mappingName," invalid!")),new g.Error("".concat(d.mappingName," invalid!"));case 26:r.next=44;break;case 28:if("foregroundImage"!==d.name){r.next=44;break}return l=Math.round(d.x),f=Math.round(d.y),h=Math.round(d.width),v=Math.round(d.height),b=Math.round(d.padding),h<2*(m=Math.round(d.borderRadius))&&(m=h/2),v<2*m&&(m=v/2),y=l-b,w=f-b,C=h+2*b,_=v+2*b,k=Math.round(C/h*m),C<2*k&&(k=C/2),_<2*k&&(k=_/2),o.save(),o.setShadow(d.shadowOffsetX,d.shadowOffsetY,d.shadowBlur,d.shadowColor),k>0?(o.beginPath(),o.moveTo(y+k,w),o.arcTo(y+C,w,y+C,w+_,k),o.arcTo(y+C,w+_,y,w+_,k),o.arcTo(y,w+_,y,w,k),o.arcTo(y,w,y+C,w,k),o.closePath(),o.setFillStyle(d.backgroundColor),o.fill()):(o.setFillStyle(d.backgroundColor),o.fillRect(y,w,C,_)),o.restore(),o.save(),k>0?(o.beginPath(),o.moveTo(y+k,w),o.arcTo(y+C,w,y+C,w+_,k),o.arcTo(y+C,w+_,y,w+_,k),o.arcTo(y,w+_,y,w,k),o.arcTo(y,w,y+C,w,k),o.closePath(),o.setFillStyle(b>0?d.backgroundColor:"rgba(0,0,0,0)"),o.fill()):(o.setFillStyle(b>0?d.backgroundColor:"rgba(0,0,0,0)"),o.fillRect(y,w,C,_)),o.restore(),m>0&&(o.beginPath(),o.moveTo(l+m,f),o.arcTo(l+h,f,l+h,f+v,m),o.arcTo(l+h,f+v,l,f+v,m),o.arcTo(l,f+v,l,f,m),o.arcTo(l,f,l+h,f,m),o.closePath(),o.setStrokeStyle("rgba(0,0,0,0)"),o.stroke(),o.clip()),r.prev=34,r.next=37,e.loadImage(d.imageSrc);case 37:p=r.sent,o.drawImage(p,l,f,h,v),r.next=44;break;case 41:throw r.prev=41,r.t2=r["catch"](34),console.error("[uQRCode]: ".concat(d.mappingName," invalid!")),new g.Error("".concat(d.mappingName," invalid!"));case 44:n&&o.draw(!0),o.restore();case 45:c++,r.next=3;break;case 48:o.draw(!0),setTimeout(s,150),r.next=54;break;case 51:r.prev=51,r.t3=r["catch"](0),u(r.t3);case 54:case"end":return r.stop()}}),r,null,[[0,51],[16,23],[34,41]])})));return function(t,e){return r.apply(this,arguments)}}();return new Promise((function(t,e){u(t,e)}))},g.prototype.draw=function(t){return this.drawCanvas(t)},g.prototype.register=function(t){t&&t(g,this,!0)},g}))}).call(this,r("c8ba"))},9705:function(t,e,r){function o(t,e,r,o,n,i,a){try{var s=t[i](a),u=s.value}catch(c){return void r(c)}s.done?e(u):Promise.resolve(u).then(o,n)}r("d3b7"),t.exports=function(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function s(t){o(a,n,i,s,u,"next",t)}function u(t){o(a,n,i,s,u,"throw",t)}s(void 0)}))}},t.exports.__esModule=!0,t.exports["default"]=t.exports},"9feb":function(t,e,r){var o=r("62f5")["default"],n=r("d5f8");t.exports=function(t){var e=n(t,"string");return"symbol"===o(e)?e:String(e)},t.exports.__esModule=!0,t.exports["default"]=t.exports},"9ffb":function(t,e,r){r("7a82"),r("a4d3"),r("e01a"),r("d3b7"),r("d28b"),r("3ca3"),r("ddb0"),r("b636"),r("944a"),r("0c47"),r("23dc"),r("3410"),r("d9e2"),r("d401"),r("14d9"),r("159b"),r("131a"),r("26e9"),r("fb6a");var o=r("62f5")["default"];function n(){"use strict";
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t.exports=n=function(){return e},t.exports.__esModule=!0,t.exports["default"]=t.exports;var e={},r=Object.prototype,i=r.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},s="function"==typeof Symbol?Symbol:{},u=s.iterator||"@@iterator",c=s.asyncIterator||"@@asyncIterator",d=s.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(T){l=function(t,e,r){return t[e]=r}}function f(t,e,r,o){var n=e&&e.prototype instanceof v?e:v,i=Object.create(n.prototype),s=new P(o||[]);return a(i,"_invoke",{value:x(t,r,s)}),i}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(T){return{type:"throw",arg:T}}}e.wrap=f;var g={};function v(){}function p(){}function m(){}var b={};l(b,u,(function(){return this}));var y=Object.getPrototypeOf,w=y&&y(y(E([])));w&&w!==r&&i.call(w,u)&&(b=w);var C=m.prototype=v.prototype=Object.create(b);function _(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){var r;a(this,"_invoke",{value:function(n,a){function s(){return new e((function(r,s){(function r(n,a,s,u){var c=h(t[n],t,a);if("throw"!==c.type){var d=c.arg,l=d.value;return l&&"object"==o(l)&&i.call(l,"__await")?e.resolve(l.__await).then((function(t){r("next",t,s,u)}),(function(t){r("throw",t,s,u)})):e.resolve(l).then((function(t){d.value=t,s(d)}),(function(t){return r("throw",t,s,u)}))}u(c.arg)})(n,a,r,s)}))}return r=r?r.then(s,s):s()}})}function x(t,e,r){var o="suspendedStart";return function(n,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===n)throw i;return L()}for(r.method=n,r.arg=i;;){var a=r.delegate;if(a){var s=I(a,r);if(s){if(s===g)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===o)throw o="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o="executing";var u=h(t,e,r);if("normal"===u.type){if(o=r.done?"completed":"suspendedYield",u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o="completed",r.method="throw",r.arg=u.arg)}}}function I(t,e){var r=e.method,o=t.iterator[r];if(void 0===o)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=void 0,I(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var n=h(o,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,g;var i=n.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,g):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function B(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function E(t){if(t){var e=t[u];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function e(){for(;++r<t.length;)if(i.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:L}}function L(){return{value:void 0,done:!0}}return p.prototype=m,a(C,"constructor",{value:m,configurable:!0}),a(m,"constructor",{value:p,configurable:!0}),p.displayName=l(m,d,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,l(t,d,"GeneratorFunction")),t.prototype=Object.create(C),t},e.awrap=function(t){return{__await:t}},_(k.prototype),l(k.prototype,c,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,o,n,i){void 0===i&&(i=Promise);var a=new k(f(t,r,o,n),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(C),l(C,d,"Generator"),l(C,u,(function(){return this})),l(C,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var o in e)r.push(o);return r.reverse(),function t(){for(;r.length;){var o=r.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=E,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(B),!t)for(var e in this)"t"===e.charAt(0)&&i.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(r,o){return a.type="throw",a.arg=t,e.next=r,o&&(e.method="next",e.arg=void 0),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var n=this.tryEntries[o],a=n.completion;if("root"===n.tryLoc)return r("end");if(n.tryLoc<=this.prev){var s=i.call(n,"catchLoc"),u=i.call(n,"finallyLoc");if(s&&u){if(this.prev<n.catchLoc)return r(n.catchLoc,!0);if(this.prev<n.finallyLoc)return r(n.finallyLoc)}else if(s){if(this.prev<n.catchLoc)return r(n.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<n.finallyLoc)return r(n.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var n=o;break}}n&&("break"===t||"continue"===t)&&n.tryLoc<=e&&e<=n.finallyLoc&&(n=null);var a=n?n.completion:{};return a.type=t,a.arg=e,n?(this.method="next",this.next=n.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),B(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var o=r.completion;if("throw"===o.type){var n=o.arg;B(r)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:E(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),g}},e}t.exports=n,t.exports.__esModule=!0,t.exports["default"]=t.exports},a126:function(t,e,r){var o=r("bbdc");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=r("4f06").default;n("703d0287",o,!0,{sourceMap:!1,shadowMode:!1})},b252:function(t,e,r){var o=r("6ab5");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var n=r("4f06").default;n("0c30beb4",o,!0,{sourceMap:!1,shadowMode:!1})},bbdc:function(t,e,r){var o=r("24fb");e=o(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},c8f9:function(t,e,r){"use strict";r.d(e,"b",(function(){return o})),r.d(e,"c",(function(){return n})),r.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-uni-view",[r("v-uni-view",{staticClass:"flex-c-c"},[r("v-uni-image",{class:t.imgClazz,attrs:{src:t.ossIcon(t.img)}})],1),t.btnText?r("v-uni-view",{staticClass:"flex-c-c",class:t.btnBlockClazz},[r("v-uni-button",{staticClass:"vh-btn flex-c-c w-208 h-64 font-wei-500 font-32 text-e80404 bg-ffffff b-rad-29 b-s-02-e80404",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.btnFun.apply(void 0,arguments)}}},[t._v(t._s(t.btnText))])],1):t._e(),t.title?r("v-uni-view",{staticClass:"font-wei-500 font-36 text-3 l-h-50 text-center",class:t.titleClazz},[t._v(t._s(t.title))]):t._e(),t.desc?r("v-uni-view",{staticClass:"font-28 text-6 l-h-40 text-center",class:t.descClazz},[t._v(t._s(t.desc))]):t._e()],1)},n=[]},cb29:function(t,e,r){"use strict";var o=r("23e7"),n=r("81d5"),i=r("44d2");o({target:"Array",proto:!0},{fill:n}),i("fill")},ce7c:function(t,e,r){"use strict";r.r(e);var o=r("0efb"),n=r("ea26");for(var i in n)["default"].indexOf(i)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(i);r("eb5f");var a=r("f0c5"),s=Object(a["a"])(n["default"],o["b"],o["c"],!1,null,"89d76102",null,!1,o["a"],void 0);e["default"]=s.exports},cfea:function(t,e,r){"use strict";r("7a82");var o=r("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=o(r("f07e")),i=o(r("d0af")),a=o(r("c964")),s=o(r("f3f3"));r("d3b7"),r("99af"),r("e9c4"),r("159b"),r("d401"),r("25f0"),r("cb29"),r("4de4"),r("d81d"),r("81b2"),r("0eb6"),r("b7ef"),r("8bd4"),r("c19f"),r("ace4"),r("5cc6"),r("907a"),r("9a8c"),r("a975"),r("735e"),r("c1ac"),r("d139"),r("3a7b"),r("986a"),r("1d02"),r("d5d6"),r("82f8"),r("e91f"),r("60bd"),r("5f96"),r("3280"),r("3fcc"),r("ca91"),r("25a1"),r("cd26"),r("3c5d"),r("2954"),r("649e"),r("219c"),r("b39a"),r("72f7"),r("3ca3"),r("ddb0"),r("caad6");var u=r("d8be"),c=r("26cb"),d=o(r("82c9")),l=function(t,e,r){return new Promise((function(o){uni.canvasToTempFilePath({canvasId:t,destWidth:e,destHeight:r,success:function(t){return o(t.tempFilePath)},fail:function(){return o("")}})}))},f={name:"auctionCertificateList",data:function(){return{MAuctionCertificateType:u.MAuctionCertificateType,loading:!0,currentTab:u.MAuctionCertificateType.Buyer,buyerList:[],sellerList:[],unlockPopupVisible:!1,previewPopupVisible:!1,currentItem:{buyer_name:"持有人：张三",collection_value:"收藏价值：123",product_img:"",title:"拍品名称：${title}",unique_int:"独立编号：${unique_int}",order_time:"收藏时间：${order_time}",net_content:"容量：${net_content}ml",category_name:"类型：${category_name}",years:"年份：${years}年"},canvasId:"myCanvas",qrcodeCanvasId:"qrcode",qrcodeSize:88,bigQrcodeCanvasId:"bigQrcode",bigQrcodeSize:88,smallQrcodeCanvasId:"smallQrcode",smallQrcodeSize:70,isFromMine:0}},computed:(0,s.default)((0,s.default)({},(0,c.mapState)(["routeTable"])),{},{list:function(t){var e=t.currentTab,r=t.buyerList,o=t.sellerList;switch(e){case u.MAuctionCertificateType.Buyer:return r;case u.MAuctionCertificateType.Seller:return o;default:return[]}},isFromAppMine:function(t){var e=t.isFromMine,r=t.$app;return e&&r},$isFirstPreview:function(t){var e=t.isFromMine,r=t.currentItem;return e&&r.$isFirstPreview}}),methods:{onUnlock:function(t){var e=this;if(this.isFromMine)return this.feedback.loading(),void this.$u.api.updateAuctionCertificateShareStatus({id:t.id,is_ash:1}).then((function(){t.is_ash=1,e.onPreview(t)}));this.unlockPopupVisible=!0,this.currentItem=t},toEvaluate:function(){var t=this.currentItem,e=t.order_id,r=t.product_img;this.jump.navigateTo("".concat(this.routeTable.pHAuctionOrderEvaluate,"?orderId=").concat(e,"&goodsImage=").concat(r)),this.unlockPopupVisible=!1},onPreview:function(t){var e=this;this.currentItem=t,this.previewPopupVisible=!0,this.$nextTick((function(){e.createQrcode(),e.$isFirstPreview?e.createQrcode(e.smallQrcodeCanvasId,e.smallQrcodeSize):e.createQrcode(e.bigQrcodeCanvasId,e.bigQrcodeSize)}))},onShare:function(){var t=this;this.createCertificate((function(){t.jump.appShare({type:t.$android?0:2,dataType:t.$android?4:2,img:t.currentItem.$certificateImgUrl,toType:1})}))},onDownload:function(){var t=this;this.createCertificate((function(){var e=t.currentItem.$certificateImgUrl;wineYunJsBridge.openAppPage({client_path:{ios_path:"downloadFile",android_path:"downloadFile"},ad_path_param:[{ios_key:"url",ios_val:e,android_key:"url",android_val:e},{android_key:"suffix",android_val:"png"}]})}))},onMoments:function(){var t=this;this.createCertificate((function(){t.jump.appShare({type:t.$android?0:3,dataType:t.$android?4:2,img:t.currentItem.$certificateImgUrl,toType:2})}))},onPostCard:function(){var t=this;this.createCertificate((function(){var e=t.currentItem,r=e.$certificateImgUrl,o=e.goods_id,n="".concat(r,"?w=").concat(750,"&h=").concat(1146);wineYunJsBridge.openAppPage({client_path:{ios_path:"ShareCertificateViewController",android_path:"com.stg.rouge.activity.SendPostActivity"},ad_path_param:[{ios_key:"imageUrl",ios_val:n,android_key:"login",android_val:"1"},{ios_key:"goods_id",ios_val:o,android_key:"oldData",android_val:JSON.stringify({from:3,sendImg:n,goods_id:o})}]})}))},createCertificate:function(t){var e=this;return(0,a.default)((0,n.default)().mark((function r(){var o,s,u,c,d,f,h,g,v,p,m,b,y,w,C,_,k,x;return(0,n.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(!e.currentItem.$certificateImgUrl){r.next=3;break}return t&&t(),r.abrupt("return");case 3:return e.feedback.loading({title:"证书生成中..."}),o="400 24px/34px PingFangSC-Regular, PingFang SC",s=function(t){return new Promise((function(e){uni.downloadFile({url:t,success:function(t){var r=t.statusCode,o=t.tempFilePath;e(200===r?o:"")},fail:function(){return e("")}})}))},u=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:588,o=[{text:"",width:0}],n=0,i=e.split("");return i.forEach((function(e){var i=o[n].text,a="".concat(i).concat(e),s=t.measureText(a).width;s>r?(n++,o[n]={text:e,width:s}):o[n]={text:a,width:s}})),o},c=function(t){return new Promise((function(r,o){e.$u.api.ossUpload({dir:"vinehoo/client/auctionCertificate/"}).then((function(e){var n=null===e||void 0===e?void 0:e.data,i=Math.random().toString(36).substr(2,4)+"_"+Date.now(),a=n.dir,s=n.policy,u=n.accessid,c=n.signature,d=n.host,l=n.img_url,f="".concat(a).concat(i,".png"),h={key:f,policy:s,OSSAccessKeyId:u,signature:c,success_action_status:200};uni.uploadFile({url:d,file:t,name:"file",formData:h,success:function(){return r("".concat(l,"/").concat(f))},fail:function(){return o()}})})).catch((function(){return o()}))}))},d=function(){e.feedback.hideLoading(),e.feedback.toast({title:"证书生成成功"})},f=function(){e.feedback.hideLoading(),e.feedback.toast({title:"证书生成失败，请稍候再试"})},h=function(t,e,r,o,n,i,a,s){t.beginPath(),t.moveTo(o+s,n),t.arcTo(o+i,n,o+i,n+a,s),t.arcTo(o+i,n+a,o,n+a,s),t.arcTo(o,n+a,o,n,s),t.arcTo(o,n,o+s,n,s),t.strokeStyle=e,t.fillStyle=r,t.stroke(),t.fill()},r.prev=11,r.next=14,s(e.ossIcon("/auction/certificate_750_1146.png"));case 14:if(g=r.sent,g){r.next=18;break}return f(),r.abrupt("return");case 18:return r.next=20,s(e.ossIcon(e.currentItem.product_img));case 20:if(v=r.sent,v){r.next=25;break}return r.next=24,s(e.ossIcon("/occupy_img/img4.png"));case 24:v=r.sent;case 25:return p=uni.createCanvasContext(e.canvasId,e),p.drawImage(g,0,0,750,1146),p.font=o,p.fillStyle="#333",p.fillText("".concat(e.currentItem.buyer_name),92,338),m=u(p,e.currentItem.collection_value),b=(0,i.default)(m,1),y=b[0],p.fillText(y.text,658-y.width,338),272,v&&p.drawImage(v,239,402,272,272),34,12,w=["title","unique_int","order_time","net_content","category_name","years"],C=w.map((function(t){return e.currentItem[t]})).filter((function(t){return t})),_=752,C.forEach((function(t){u(p,t).forEach((function(t){var e=t.text,r=t.width;p.fillText(e,(750-r)/2,_),_+=34})),_+=12})),r.next=42,l(e.qrcodeCanvasId,e.qrcodeBgSize,e.qrcodeBgSize);case 42:k=r.sent,k&&(100,522,984,6,h(p,"#f5f5f5","#f5f5f5",522,984,100,100,10),x=e.qrcodeSize,p.drawImage(k,528,990,x,x)),p.draw(),setTimeout((0,a.default)((0,n.default)().mark((function r(){var o,i,a,s,u,h,g,v,p;return(0,n.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,l(e.canvasId,750,1146);case 2:if(o=r.sent,o){r.next=6;break}return f(),r.abrupt("return");case 6:for(i=o.split(","),a=atob(i[1]),s=new ArrayBuffer(a.length),u=new Uint8Array(s),h=0;h<a.length;h++)u[h]=a.charCodeAt(h);return g=new Blob([u],{type:"image/png"}),v=new File([g],"abTempFile",{type:"image/png"}),r.next=15,c(v);case 15:p=r.sent,console.log("certificateImgUrl",p),e.currentItem.$certificateImgUrl=p,d(),t&&t();case 20:case"end":return r.stop()}}),r)}))),300),r.next=52;break;case 48:r.prev=48,r.t0=r["catch"](11),f(),console.log("err",r.t0);case 52:case"end":return r.stop()}}),r,null,[[11,48]])})))()},createQrcode:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.qrcodeCanvasId,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.qrcodeSize;try{var r=new d.default,o=this.currentItem.goods_id,n="".concat(location.origin).concat(this.routeTable.pHAuctionGoodsDetail,"?id=").concat(o);r.data=n,r.size=uni.upx2px(e),r.make();var i=uni.createCanvasContext(t,this);r.canvasContext=i,r.drawCanvas()}catch(a){console.log("createQrcode",a)}},changeBodyClassList:function(){this.buyerList.length&&this.$nextTick((function(){var t,e,r;null===(t=document)||void 0===t||null===(e=t.body)||void 0===e||null===(r=e.classList)||void 0===r||r.add("bg-f5f5f5")}))}},onLoad:function(t){var e=this,r=t.isFromMine,o=void 0===r?0:r;this.isFromMine=+o,this.login.isLoginV3(this.$vhFrom).then((function(t){if(t){var r=uni.getStorageSync("loginInfo")||{};Promise.all([e.$u.api.searchAuctionCertificateList({type:u.MAuctionCertificateType.Buyer,uid:r.uid})]).then((function(t){var r=(0,i.default)(t,2),o=r[0],n=r[1],a=(null===o||void 0===o?void 0:o.data)||[];a.forEach((function(t){var e=t.buyer_name,r=t.collection_value,o=t.product_img,n=t.title,i=t.unique_int,a=t.order_time,s=t.net_content,u=t.category_name,c=t.years,d=t.is_buyer_unlock,l=t.is_ash,f=[2,3,4,5,6].includes(t.product_type);Object.assign(t,{buyer_name:"持有人：".concat(e),collection_value:"收藏价值：".concat(r),product_img:(null===o||void 0===o?void 0:o[0])||"",title:"拍品名称：".concat(n),unique_int:"独立编号：".concat(i),order_time:"收藏时间：".concat(a),net_content:f?"":"容量：".concat(s,"ml"),category_name:"类型：".concat(u),years:f?"":"年份：".concat(c),$certificateImgUrl:"",$isFirstPreview:!d&&!l})})),e.buyerList=a,e.sellerList=(null===n||void 0===n?void 0:n.data)||[],e.changeBodyClassList()})).finally((function(){e.loading=!1}))}}))}};e.default=f},d5f8:function(t,e,r){r("8172"),r("efec"),r("a4d3"),r("e01a"),r("d3b7"),r("d9e2"),r("d401"),r("a9e3");var o=r("62f5")["default"];t.exports=function(t,e){if("object"!==o(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)},t.exports.__esModule=!0,t.exports["default"]=t.exports},d72e:function(t,e,r){"use strict";r("7a82");var o=r("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=o(r("e143")),i={props:{img:{type:String,default:"/auction/none_440_400.png"},imgClazz:{type:String,default:"w-440 h-400"},btnText:{type:String,default:""},btnFun:{type:Function,default:function(){var t=n.default.prototype,e=t.$app,r=t.$ios;e&&(r?wineYunJsBridge.openAppPage({client_path:"jumpCommunityAuction"}):wineYunJsBridge.openAppPage({client_path:{android_path:"goMain"},ad_path_param:[{android_key:"type",android_val:"2"},{android_key:"str",android_val:"1"}]}))}},btnBlockClazz:{type:String,default:"mt-68"},title:{type:String,default:""},titleClazz:{type:String,default:"mt-72"},desc:{type:String,default:""},descClazz:{type:String,default:"mt-20"}}};e.default=i},ea26:function(t,e,r){"use strict";r.r(e);var o=r("3dc3"),n=r.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(i);e["default"]=n.a},eb5f:function(t,e,r){"use strict";var o=r("b252"),n=r.n(o);n.a},f074:function(t,e,r){"use strict";r.r(e);var o=r("7f1a"),n=r.n(o);for(var i in o)["default"].indexOf(i)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(i);e["default"]=n.a},f2f9:function(t,e,r){"use strict";var o=r("a126"),n=r.n(o);n.a},f6b7:function(t,e,r){"use strict";r.r(e);var o=r("c8f9"),n=r("7b0c");for(var i in n)["default"].indexOf(i)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(i);var a=r("f0c5"),s=Object(a["a"])(n["default"],o["b"],o["c"],!1,null,"79787d9f",null,!1,o["a"],void 0);e["default"]=s.exports}}]);