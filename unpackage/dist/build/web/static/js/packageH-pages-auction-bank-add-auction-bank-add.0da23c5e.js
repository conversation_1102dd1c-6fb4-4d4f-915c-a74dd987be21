(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-bank-add-auction-bank-add"],{"12c6":function(t,e,a){"use strict";a.r(e);var n=a("51bd"),i=a("f074");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("f2f9");var c=a("f0c5"),o=Object(c["a"])(i["default"],n["b"],n["c"],!1,null,"519170ec",null,!1,n["a"],void 0);e["default"]=o.exports},"4a9e":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={name:"auctionBankAdd",data:function(){return{bank:{account_holder:"",card_no:"",idcard:"",mobile:"",is_default:0}}},computed:{disabled:function(t){var e=t.bank;return!(e.account_holder&&e.card_no&&e.idcard&&e.mobile)}},methods:{onSubmit:function(){var t=this;this.disabled||(this.feedback.loading({title:"添加中..."}),this.$u.api.addAuctionBank(this.bank).then((function(){t.feedback.toast({title:"添加成功"}),uni.navigateBack()})))}}};e.default=n},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return n}));var n={uIcon:a("e5e1").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},s=[]},"7f1a":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var s=a("26cb"),c=uni.getSystemInfoSync(),o={},l={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:o,statusBarHeight:c.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,s.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(c.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(c.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,n=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(n)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=l},a126:function(t,e,a){var n=a("bbdc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("703d0287",n,!0,{sourceMap:!1,shadowMode:!1})},bbdc:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},c060:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return n}));var n={vhNavbar:a("12c6").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",[a("vh-navbar",{attrs:{title:"增加银行卡",height:"46"}}),a("v-uni-view",{staticClass:"h-20 bg-f5f5f5"}),a("v-uni-view",{staticClass:"ptb-28-plr-32 pb-124"},[a("v-uni-view",{staticClass:"font-wei-600 font-24 text-6 l-h-34"},[t._v("请绑定持卡人本人的银行卡")]),a("v-uni-view",{staticClass:"flex-sb-c h-110 bb-s-02-eeeeee"},[a("v-uni-text",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("持卡人")]),a("v-uni-input",{staticClass:"w-400 font-32 text-3 text-right",attrs:{placeholder:"持卡人本人姓名","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.bank.account_holder,callback:function(e){t.$set(t.bank,"account_holder",e)},expression:"bank.account_holder"}})],1),a("v-uni-view",{staticClass:"flex-sb-c h-110 bb-s-02-eeeeee"},[a("v-uni-text",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("银行卡号")]),a("v-uni-input",{staticClass:"w-400 font-32 text-3 text-right",attrs:{placeholder:"持卡人本人银行卡号","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.bank.card_no,callback:function(e){t.$set(t.bank,"card_no",e)},expression:"bank.card_no"}})],1),a("v-uni-view",{staticClass:"flex-sb-c h-110 bb-s-02-eeeeee"},[a("v-uni-text",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("身份证号")]),a("v-uni-input",{staticClass:"w-400 font-32 text-3 text-right",attrs:{placeholder:"持卡人本人身份证号","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.bank.idcard,callback:function(e){t.$set(t.bank,"idcard",e)},expression:"bank.idcard"}})],1),a("v-uni-view",{staticClass:"flex-sb-c h-110 bb-s-02-eeeeee"},[a("v-uni-text",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("预留手机号")]),a("v-uni-input",{staticClass:"w-400 font-32 text-3 text-right",attrs:{placeholder:"银行卡预留手机号","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.bank.mobile,callback:function(e){t.$set(t.bank,"mobile",e)},expression:"bank.mobile"}})],1),a("v-uni-view",{staticClass:"flex-sb-c mt-32"},[a("v-uni-text",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("设置默认")]),t.bank.is_default?a("v-uni-image",{staticClass:"w-56 h-26",attrs:{src:t.ossIcon("/auction/switch_h_56_26.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.bank.is_default=0}}}):a("v-uni-image",{staticClass:"w-56 h-26",attrs:{src:t.ossIcon("/auction/switch_56_26.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.bank.is_default=1}}})],1),a("v-uni-view",{staticClass:"mt-20 font-24 text-6 l-h-34"},[t._v("系统会默认使用该账户作为您的收款账户")])],1),a("v-uni-view",{staticClass:"p-fixed left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-********-022 z-9999"},[a("v-uni-button",{staticClass:"vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32",class:t.disabled?"bg-fce4e3":"bg-e80404",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSubmit.apply(void 0,arguments)}}},[t._v("提交绑定")])],1)],1)},s=[]},de1f:function(t,e,a){"use strict";a.r(e);var n=a("4a9e"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},f074:function(t,e,a){"use strict";a.r(e);var n=a("7f1a"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},f2f9:function(t,e,a){"use strict";var n=a("a126"),i=a.n(n);i.a},fef1:function(t,e,a){"use strict";a.r(e);var n=a("c060"),i=a("de1f");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);var c=a("f0c5"),o=Object(c["a"])(i["default"],n["b"],n["c"],!1,null,"48bd2866",null,!1,n["a"],void 0);e["default"]=o.exports}}]);