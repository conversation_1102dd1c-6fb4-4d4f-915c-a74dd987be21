(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-seller-after-sale-detail-auction-seller-after-sale-detail"],{"033e":function(e,t,a){"use strict";var n=a("6522"),r=a.n(n);r.a},"062a":function(e,t,a){var n=a("aab3");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var r=a("4f06").default;r("2db15654",n,!0,{sourceMap:!1,shadowMode:!1})},"06b6":function(e,t,a){"use strict";a.r(t);var n=a("bca1"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"0fdd":function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var n={vhNavbar:a("12c6").default,vhImage:a("ce7c").default,ActionAfterSaleOrderDetail:a("3667").default,AuctionAfterSaleDetail:a("45ae").default,uButton:a("4f1b").default,uMask:a("e710").default,vhSkeleton:a("591b").default},r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"content pb-124",class:e.loading?"h-vh-100 o-hid":""},[a("vh-navbar",{attrs:{title:"售后详情"}}),e.loading?a("vh-skeleton",{attrs:{bgColor:"#FFF",showLoading:!1}}):a("v-uni-view",{staticClass:"fade-in"},[a("v-uni-view",{staticClass:"bg-ffffff b-rad-10 o-hid mt-20 ml-24 mr-24"},[a("v-uni-view",{staticClass:"w-p100 h-222 bg-li-30 flex-sb-c pl-24 pr-48"},[a("v-uni-view",{staticClass:"font-36 font-wei text-ffffff"},[e._v(e._s(e.afterSaleStatus[e.afterSaleInfo.status].text))]),a("vh-image",{attrs:{"loading-type":2,src:e.ossIcon("/auction_seller_after_sale_detail/icon"+e.afterSaleInfo.status+".png"),width:112,height:112}})],1),a("ActionAfterSaleOrderDetail",{attrs:{type:1,afterSaleInfo:e.afterSaleInfo}})],1),a("AuctionAfterSaleDetail",{attrs:{type:1,afterSaleInfo:e.afterSaleInfo}}),a("v-uni-view",{staticClass:"p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff flex-e-c b-sh-00021200-022 ptb-00-plr-24"},[[0,2].includes(e.afterSaleInfo.status)?a("v-uni-view",{staticClass:"flex-c-c"},[a("v-uni-view",{},[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#666",border:"1rpx solid #666"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showRejectMask=!0}}},[e._v("拒绝")])],1),a("v-uni-view",{staticClass:"ml-20"},[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#2E7BFF",border:"1rpx solid #2E7BFF"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleAfterSaleType(1)}}},[e._v("同意")])],1)],1):e._e(),[1,3,5,6].includes(e.afterSaleInfo.status)?a("v-uni-view",{},[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#999",border:"1rpx solid #999"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.jump.navigateTo(e.routeTable.pHAuctionAfterSaleProgress+"?refundOrderNo="+e.afterSaleInfo.refund_order_no)}}},[e._v("售后进度")])],1):e._e()],1),a("v-uni-view",{},[a("u-mask",{attrs:{show:e.showRejectMask,zoom:!1},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showRejectMask=!1}}},[a("v-uni-view",{staticClass:"h-p100 flex-column flex-c-c"},[a("v-uni-view",{staticClass:"flex-column flex-c-c"},[a("v-uni-view",{staticClass:"p-rela w-624 h-572 bg-ffffff b-rad-30 mb-46 o-hid",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)}}},[a("v-uni-image",{staticClass:"p-abso w-p100 h-p100 b-rad-30",attrs:{src:e.ossIcon("/auction_seller_after_sale_detail/reason_bg.png")}}),a("v-uni-view",{staticClass:"p-rela z-02 w-p100 h-p100 d-flex flex-column j-center b-rad-30 ptb-00-plr-40"},[a("v-uni-view",{staticClass:"font-32 font-wei text-3"},[e._v("拒绝原因")]),a("v-uni-view",{staticClass:"w-544 bg-fffafa b-rad-20 b-s-02-efc1cd mt-24 p-16"},[a("v-uni-textarea",{staticClass:"w-p100 h-168 font-24 text-9 l-h-34",attrs:{maxlength:50,placeholder:"请填写您的备注","placeholder-style":"color:#999;font-size:28rpx;"},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.rejectReasonChange.apply(void 0,arguments)}},model:{value:e.rejectReason,callback:function(t){e.rejectReason=t},expression:"rejectReason"}}),a("v-uni-view",{staticClass:"d-flex j-end mt-20 font-24 text-9"},[e._v(e._s(e.rejectReason.length>50?50:e.rejectReason.length)+"/50")])],1),a("v-uni-view",{staticClass:"flex-sb-c mt-50"},[a("v-uni-view",{},[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"240rpx",height:"64rpx",backgroundColor:"#FFF",fontSize:"28rpx",fontWeight:"bold",color:"#666",border:"1px solid #666666"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showRejectMask=!1}}},[e._v("取消")])],1),a("v-uni-view",{},[a("u-button",{attrs:{disabled:!e.canConfirmReject,shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"240rpx",height:"64rpx",backgroundColor:e.canConfirmReject?"#E80404":"#FCE4E3",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",border:"none"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleAfterSaleType(0)}}},[e._v("确认")])],1)],1)],1)],1)],1)],1)],1)],1)],1)],1)},i=[]},"12c6":function(e,t,a){"use strict";a.r(t);var n=a("51bd"),r=a("f074");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("f2f9");var o=a("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"519170ec",null,!1,n["a"],void 0);t["default"]=s.exports},2071:function(e,t,a){"use strict";var n=a("35ca"),r=a.n(n);r.a},"35ca":function(e,t,a){var n=a("552d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var r=a("4f06").default;r("175b7730",n,!0,{sourceMap:!1,shadowMode:!1})},3667:function(e,t,a){"use strict";a.r(t);var n=a("d5d0"),r=a("8eb1");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"f99db442",null,!1,n["a"],void 0);t["default"]=s.exports},"45ae":function(e,t,a){"use strict";a.r(t);var n=a("aab7"),r=a("06b6");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);var o=a("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"1fee2ea5",null,!1,n["a"],void 0);t["default"]=s.exports},"4f1b":function(e,t,a){"use strict";a.r(t);var n=a("825d"),r=a("8e1d");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("fa94");var o=a("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"4ed92bb2",null,!1,n["a"],void 0);t["default"]=s.exports},"51bd":function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var n={uIcon:a("e5e1").default},r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":e.isFixed},e.navbarClass],style:[e.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":e.newYearTheme},style:[e.navbarInnerStyle]},[e.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}},[e.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:e.backIconColor,size:e.backIconSize}}):[a("u-icon",{attrs:{name:e.pageLength<=1?"home":e.backIconName,color:e.backIconColor,size:e.backIconSize}}),e.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[e.backTextStyle]},[e._v(e._s(e.backText))]):e._e()]],2):e._e(),e.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[e.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:e.titleColor,fontSize:e.titleSize+"rpx",fontWeight:e.titleBold?"bold":"normal"}},[e._v(e._s(e.title))])],1):e._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[e._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[e._t("right")],2)],1)],1),e.isFixed&&!e.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(e.navbarHeight)+(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}):e._e()],1)},i=[]},"552d":function(e,t,a){var n=a("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-mask[data-v-f63a3092]{position:fixed;top:0;left:0;right:0;bottom:0;opacity:0;transition:-webkit-transform .3s;transition:transform .3s;transition:transform .3s,-webkit-transform .3s}.u-mask-show[data-v-f63a3092]{opacity:1}.u-mask-zoom[data-v-f63a3092]{-webkit-transform:scale(1.2);transform:scale(1.2)}',""]),e.exports=t},5726:function(e,t,a){"use strict";a.r(t);var n=a("fa36"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},6522:function(e,t,a){var n=a("ae59");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var r=a("4f06").default;r("c28f36e6",n,!0,{sourceMap:!1,shadowMode:!1})},"66a8":function(e,t,a){"use strict";a.r(t);var n=a("cea1"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"7f1a":function(e,t,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var i=a("26cb"),o=uni.getSystemInfoSync(),s={},l={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,r.default)((0,r.default)({},(0,i.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var e={};return e.height=this.navbarHeight+"px",e},navbarStyle:function(){var e={};return e.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(e,this.background),this.showBorder&&Object.assign(e,this.borderStyle),e},titleStyle:function(){var e={};return e.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.width=uni.upx2px(this.titleWidth)+"px",e},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var e=this.pages.getPageFullPath(),t=this.routeTable,a=t.pEAddressAdd,n=t.pEAddressManagement,r=t.pBOrderDepositDetail;(e.includes("/packageH")||e.includes(a)||e.includes(n)||e.includes(r))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};t.default=l},"825d":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+e.size,e.plain?"u-btn--"+e.type+"--plain":"",e.loading?"u-loading":"","circle"==e.shape?"u-round-circle":"",e.hairLine?e.showHairLineBorder:"u-btn--bold-border","u-btn--"+e.type,e.disabled?"u-btn--"+e.type+"--disabled":""],style:[e.customStyle,{overflow:e.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(e.hoverStartTime),"hover-stay-time":Number(e.hoverStayTime),disabled:e.disabled,"form-type":e.formType,"open-type":e.openType,"app-parameter":e.appParameter,"hover-stop-propagation":e.hoverStopPropagation,"send-message-title":e.sendMessageTitle,"send-message-path":"sendMessagePath",lang:e.lang,"data-name":e.dataName,"session-from":e.sessionFrom,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"hover-class":e.getHoverClass,loading:e.loading},on:{getphonenumber:function(t){arguments[0]=t=e.$handleEvent(t),e.getphonenumber.apply(void 0,arguments)},getuserinfo:function(t){arguments[0]=t=e.$handleEvent(t),e.getuserinfo.apply(void 0,arguments)},error:function(t){arguments[0]=t=e.$handleEvent(t),e.error.apply(void 0,arguments)},opensetting:function(t){arguments[0]=t=e.$handleEvent(t),e.opensetting.apply(void 0,arguments)},launchapp:function(t){arguments[0]=t=e.$handleEvent(t),e.launchapp.apply(void 0,arguments)},click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.click(t)}}},[e._t("default"),e.ripple?a("v-uni-view",{staticClass:"u-wave-ripple",class:[e.waveActive?"u-wave-active":""],style:{top:e.rippleTop+"px",left:e.rippleLeft+"px",width:e.fields.targetWidth+"px",height:e.fields.targetWidth+"px","background-color":e.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):e._e()],2)},r=[]},"8e1d":function(e,t,a){"use strict";a.r(t);var n=a("9476"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},"8eb1":function(e,t,a){"use strict";a.r(t);var n=a("e51b3"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},9476:function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3"),a("c975"),a("d3b7"),a("ac1f");var n={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var e;return e=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",e},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(e){var t=this;this.$u.throttle((function(){!0!==t.loading&&!0!==t.disabled&&(t.ripple&&(t.waveActive=!1,t.$nextTick((function(){this.getWaveQuery(e)}))),t.$emit("click",e))}),this.throttleTime)},getWaveQuery:function(e){var t=this;this.getElQuery().then((function(a){var n=a[0];if(n.width&&n.width&&(n.targetWidth=n.height>n.width?n.height:n.width,n.targetWidth)){t.fields=n;var r,i;r=e.touches[0].clientX,i=e.touches[0].clientY,t.rippleTop=i-n.top-n.targetWidth/2,t.rippleLeft=r-n.left-n.targetWidth/2,t.$nextTick((function(){t.waveActive=!0}))}}))},getElQuery:function(){var e=this;return new Promise((function(t){var a="";a=uni.createSelectorQuery().in(e),a.select(".u-btn").boundingClientRect(),a.exec((function(e){t(e)}))}))},getphonenumber:function(e){this.$emit("getphonenumber",e)},getuserinfo:function(e){this.$emit("getuserinfo",e)},error:function(e){this.$emit("error",e)},opensetting:function(e){this.$emit("opensetting",e)},launchapp:function(e){this.$emit("launchapp",e)}}};t.default=n},a126:function(e,t,a){var n=a("bbdc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var r=a("4f06").default;r("703d0287",n,!0,{sourceMap:!1,shadowMode:!1})},aab3:function(e,t,a){var n=a("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),e.exports=t},aab7:function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n}));var n={vhImage:a("ce7c").default},r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"bg-ffffff b-rad-10 o-hid mt-20 ml-24 mr-24 ptb-00-plr-20"},[a("v-uni-view",{staticClass:"flex-sb-c bt-s-01-eeeeee ptb-32-plr-00"},[a("v-uni-text",{staticClass:"font-28 font-wei text-6"},[e._v("发起时间")]),a("v-uni-text",{staticClass:"font-28 text-3"},[e._v(e._s(e.afterSaleInfo.created_time))])],1),a("v-uni-view",{staticClass:"flex-sb-c bt-s-01-eeeeee ptb-32-plr-00"},[a("v-uni-text",{staticClass:"font-28 font-wei text-6"},[e._v("售后类型")]),a("v-uni-text",{staticClass:"font-28 font-wei text-3"},[e._v(e._s(0===e.afterSaleInfo.service_type?"仅退款":"退货退款"))])],1),e.afterSaleInfo.refund_time?a("v-uni-view",{staticClass:"flex-sb-c bt-s-01-eeeeee ptb-32-plr-00"},[a("v-uni-text",{staticClass:"font-28 font-wei text-6"},[e._v("退款时间")]),a("v-uni-text",{staticClass:"font-28 text-3"},[e._v(e._s(e.afterSaleInfo.refund_time))])],1):e._e(),a("v-uni-view",{staticClass:"flex-sb-c bt-s-01-eeeeee ptb-32-plr-00"},[a("v-uni-text",{staticClass:"font-28 font-wei text-6"},[e._v("退回方向")]),a("v-uni-text",{staticClass:"font-28 text-3"},[e._v("原路退回")])],1),a("v-uni-view",{staticClass:"flex-sb-c bt-s-01-eeeeee ptb-32-plr-00"},[a("v-uni-view",{staticClass:"font-28 font-wei text-6"},[e._v("退货原因")]),a("v-uni-view",{staticClass:"w-480 flex-e-c font-28 text-3"},[e._v(e._s(e.afterSaleInfo.refund_reason))])],1),e.afterSaleInfo.describe?a("v-uni-view",{staticClass:"bt-s-01-eeeeee ptb-32-plr-00"},[a("v-uni-view",{staticClass:"font-28 font-wei text-6"},[e._v("退款描述")]),a("v-uni-view",{staticClass:"bg-f6f6f6 b-rad-10 mt-20 p-24"},[e._v(e._s(e.afterSaleInfo.describe))])],1):e._e(),e.afterSaleInfo.voucher.length?a("v-uni-view",{staticClass:"pb-32"},[a("v-uni-view",{staticClass:"font-28 font-wei text-3"},[e._v("图片")]),a("v-uni-view",{staticClass:"d-flex flex-nowrap a-center"},e._l(e.afterSaleInfo.voucher,(function(t,n){return a("v-uni-view",{key:n,staticClass:"w-210 h-210 b-rad-10 mt-20 mr-16",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.image.previewImageList(e.afterSaleInfo.voucher,n)}}},[a("vh-image",{attrs:{"loading-type":2,src:t,width:210,height:210,"border-radius":10}})],1)})),1)],1):e._e()],1)},i=[]},ae59:function(e,t,a){var n=a("24fb");t=n(!1),t.push([e.i,"uni-page-body[data-v-a255263e]{background-color:#f5f5f5}body.?%PAGE?%[data-v-a255263e]{background-color:#f5f5f5}",""]),e.exports=t},bbdc:function(e,t,a){var n=a("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),e.exports=t},bca1:function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var n={name:"AuctionAfterSaleDetail",props:{type:{type:[Number,String],default:0},afterSaleInfo:{type:Object,default:function(){return{}}}}};t.default=n},ca2f:function(e,t,a){"use strict";a.r(t);var n=a("0fdd"),r=a("66a8");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("033e");var o=a("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"a255263e",null,!1,n["a"],void 0);t["default"]=s.exports},cea1:function(e,t,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("498a");var r=n(a("f07e")),i=n(a("c964")),o=n(a("f3f3")),s=a("26cb"),l={name:"auction-seller-after-sale-detail",data:function(){return{loading:!0,refundOrderNo:"",afterSaleInfo:{},showRejectMask:!1,rejectReason:""}},computed:(0,o.default)((0,o.default)({},(0,s.mapState)(["routeTable"])),{},{afterSaleStatus:function(){return{0:{text:"待处理"},1:{text:"等待买家寄回"},2:{text:"等待收货"},3:{text:"退款成功"},5:{text:"拒绝退款"},6:{text:"拒绝收货"}}},canConfirmReject:function(){return""!==this.$u.trim(this.rejectReason,"all")}}),onLoad:function(e){var t=this;this.refundOrderNo=e.refundOrderNo,this.login.isLoginV3(this.$vhFrom).then((function(e){e&&t.init()}))},methods:{init:function(){var e=this;return(0,i.default)((0,r.default)().mark((function t(){return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getAfterSaleDetail();case 2:e.loading=!1;case 3:case"end":return t.stop()}}),t)})))()},getAfterSaleDetail:function(){var e=this;return(0,i.default)((0,r.default)().mark((function t(){var a;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$u.api.auctionAfterSaleDetail({refund_order_no:e.refundOrderNo});case 2:a=t.sent,e.afterSaleInfo=a.data;case 4:case"end":return t.stop()}}),t)})))()},afterSaleAudit:function(e){var t=this;return(0,i.default)((0,r.default)().mark((function a(){var n;return(0,r.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,n={},n.order_no=t.afterSaleInfo.order_no,n.deal_type=e,0===e&&(n.refuse_reason=t.rejectReason),a.next=7,t.$u.api.auctionAfterSaleDetailAudit(n);case 7:t.feedback.toast({title:"操作成功~"}),a.next=13;break;case 10:a.prev=10,a.t0=a["catch"](0),console.log(a.t0);case 13:case"end":return a.stop()}}),a,null,[[0,10]])})))()},afterSaleSellerReceiptDeal:function(e){var t=this;return(0,i.default)((0,r.default)().mark((function a(){var n;return(0,r.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,n={},n.refund_order_no=t.afterSaleInfo.refund_order_no,n.deal_type=e,0===e&&(n.refuse_receipt_reason=t.rejectReason),a.next=7,t.$u.api.auctionAfterSaleDetailSellerReceiptDeal(n);case 7:t.feedback.toast({title:"操作成功~"}),a.next=12;break;case 10:a.prev=10,a.t0=a["catch"](0);case 12:case"end":return a.stop()}}),a,null,[[0,10]])})))()},handleAfterSaleType:function(e){var t=this;return(0,i.default)((0,r.default)().mark((function a(){var n;return(0,r.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,n=t.afterSaleInfo.status,0!==n){a.next=7;break}return a.next=5,t.afterSaleAudit(e);case 5:a.next=10;break;case 7:if(2!==n){a.next=10;break}return a.next=10,t.afterSaleSellerReceiptDeal(e);case 10:t.getAfterSaleDetail(),a.next=15;break;case 13:a.prev=13,a.t0=a["catch"](0);case 15:return a.prev=15,t.showRejectMask=!1,a.finish(15);case 18:case"end":return a.stop()}}),a,null,[[0,13,15,18]])})))()},rejectReasonChange:function(e){console.log(e),this.rejectReason=e.detail.value.substr(0,50)}}};t.default=l},d5d0:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"ptb-32-plr-24"},[a("v-uni-view",{staticClass:"flex-sb-c"},[a("v-uni-text",{staticClass:"font-32 font-wei text-6"},[e._v("订单编号：")]),a("v-uni-view",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.copy.copyText(e.afterSaleInfo.order_no)}}},[a("v-uni-text",{staticClass:"font-24 text-6"},[e._v(e._s(e.afterSaleInfo.order_no))]),a("v-uni-text",{staticClass:"bg-eeeeee b-rad-18 ml-10 ptb-02-plr-10 font-18 text-3"},[e._v("复制")])],1)],1),a("v-uni-view",{staticClass:"flex-sb-c mt-20"},[a("v-uni-text",{staticClass:"font-32 font-wei text-6"},[e._v("退款金额")]),a("v-uni-text",{staticClass:"font-32 font-wei text-e80404"},[a("v-uni-text",{staticClass:"font-22"},[e._v("¥")]),e._v(e._s(e.afterSaleInfo.refund_money))],1)],1),e.afterSaleInfo.express_number?a("v-uni-view",{staticClass:"flex-sb-c mt-20"},[a("v-uni-text",{staticClass:"font-32 font-wei text-6"},[e._v("快递编号：")]),a("v-uni-view",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.copy.copyText(e.afterSaleInfo.express_number)}}},[a("v-uni-text",{staticClass:"font-24 text-6"},[e._v(e._s(e.afterSaleInfo.express_number))]),a("v-uni-text",{staticClass:"bg-eeeeee b-rad-18 ml-10 ptb-02-plr-10 font-18 text-3"},[e._v("复制")])],1)],1):e._e()],1)},r=[]},dc86:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"u-mask",class:{"u-mask-zoom":e.zoom,"u-mask-show":e.show},style:[e.maskStyle,e.zoomStyle],attrs:{"hover-stop-propagation":!0},on:{touchmove:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t),function(){}.apply(void 0,arguments)},click:function(t){arguments[0]=t=e.$handleEvent(t),e.click.apply(void 0,arguments)}}},[e._t("default")],2)},r=[]},e51b3:function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var n={name:"ActionAfterSaleOrderDetail",props:{type:{type:[Number,String],default:0},afterSaleInfo:{type:Object,default:function(){return{}}}}};t.default=n},e710:function(e,t,a){"use strict";a.r(t);var n=a("dc86"),r=a("5726");for(var i in r)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(i);a("2071");var o=a("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"f63a3092",null,!1,n["a"],void 0);t["default"]=s.exports},f074:function(e,t,a){"use strict";a.r(t);var n=a("7f1a"),r=a.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(i);t["default"]=r.a},f2f9:function(e,t,a){"use strict";var n=a("a126"),r=a.n(n);r.a},fa36:function(e,t,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a("f3f3"));a("a9e3"),a("b64b");var i={name:"u-mask",props:{show:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:""},customStyle:{type:Object,default:function(){return{}}},zoom:{type:Boolean,default:!0},duration:{type:[Number,String],default:300},maskClickAble:{type:Boolean,default:!0}},data:function(){return{zoomStyle:{transform:""},scale:"scale(1.2, 1.2)"}},watch:{show:function(e){e&&this.zoom?this.zoomStyle.transform="scale(1, 1)":!e&&this.zoom&&(this.zoomStyle.transform=this.scale)}},computed:{maskStyle:function(){var e={backgroundColor:"rgba(0, 0, 0, 0.6)"};return this.show?e.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.mask:e.zIndex=-1,e.transition="all ".concat(this.duration/1e3,"s ease-in-out"),Object.keys(this.customStyle).length&&(e=(0,r.default)((0,r.default)({},e),this.customStyle)),e}},methods:{click:function(){this.maskClickAble&&this.$emit("click")}}};t.default=i},fa94:function(e,t,a){"use strict";var n=a("062a"),r=a.n(n);r.a}}]);