(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageE-pages-invoice-head-add-invoice-head-add"],{"0f0c":function(e,t,n){"use strict";n.r(t);var a=n("ffda"),i=n("4078");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("7b2a"),n("7b45");var r=n("f0c5"),c=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"4ac7c3d4",null,!1,a["a"],void 0);t["default"]=c.exports},"12c6":function(e,t,n){"use strict";n.r(t);var a=n("51bd"),i=n("f074");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("f2f9");var r=n("f0c5"),c=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);t["default"]=c.exports},"19c0":function(e,t,n){"use strict";n.r(t);var a=n("e2b4"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},2913:function(e,t,n){"use strict";n.r(t);var a=n("be95"),i=n("19c0");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);var r=n("f0c5"),c=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=c.exports},4078:function(e,t,n){"use strict";n.r(t);var a=n("fb8c"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},"51bd":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":e.isFixed},e.navbarClass],style:[e.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":e.newYearTheme},style:[e.navbarInnerStyle]},[e.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}},[e.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:e.backIconColor,size:e.backIconSize}}):[n("u-icon",{attrs:{name:e.pageLength<=1?"home":e.backIconName,color:e.backIconColor,size:e.backIconSize}}),e.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[e.backTextStyle]},[e._v(e._s(e.backText))]):e._e()]],2):e._e(),e.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[e.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:e.titleColor,fontSize:e.titleSize+"rpx",fontWeight:e.titleBold?"bold":"normal"}},[e._v(e._s(e.title))])],1):e._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[e._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[e._t("right")],2)],1)],1),e.isFixed&&!e.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(e.navbarHeight)+(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}):e._e()],1)},o=[]},"7b2a":function(e,t,n){"use strict";var a=n("b876"),i=n.n(a);i.a},"7b45":function(e,t,n){"use strict";var a=n("8d11"),i=n.n(a);i.a},"7f1a":function(e,t,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var o=n("26cb"),r=uni.getSystemInfoSync(),c={},s={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:c,statusBarHeight:r.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,o.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var e={};return e.height=this.navbarHeight+"px",e},navbarStyle:function(){var e={};return e.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(e,this.background),this.showBorder&&Object.assign(e,this.borderStyle),e},titleStyle:function(){var e={};return e.left=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.right=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.width=uni.upx2px(this.titleWidth)+"px",e},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var e=this.pages.getPageFullPath(),t=this.routeTable,n=t.pEAddressAdd,a=t.pEAddressManagement,i=t.pBOrderDepositDetail;(e.includes("/packageH")||e.includes(n)||e.includes(a)||e.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};t.default=s},87962:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,"uni-page-body[data-v-4ac7c3d4]{background-color:#f5f5f5}body.?%PAGE?%[data-v-4ac7c3d4]{background-color:#f5f5f5}",""]),e.exports=t},"8d11":function(e,t,n){var a=n("98cc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("3c0cc6ce",a,!0,{sourceMap:!1,shadowMode:!1})},"98cc":function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.iha-form .ih-form-item[data-v-4ac7c3d4]:not(:first-of-type){border-top:%?2?% solid #eee}',""]),e.exports=t},a126:function(e,t,n){var a=n("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},b876:function(e,t,n){var a=n("87962");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("2aa0dcc2",a,!0,{sourceMap:!1,shadowMode:!1})},bbdc:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),e.exports=t},be95:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"ih-form-item d-flex ptb-32-plr-00"},[e.label?n("v-uni-view",{staticClass:"flex-shrink w-148 font-wei-500 font-28 text-3 l-h-40"},[e._v(e._s(e.label))]):e._e(),n("v-uni-view",{staticClass:"flex-1"},[e._t("default",[n("v-uni-view",{staticClass:"d-flex j-sb"},[n("v-uni-textarea",{staticClass:"flex-1 w-aut font-28 text-3 l-h-40",attrs:{"auto-height":!0,placeholder:e.currPlaceholder,"placeholder-style":"font-size: 28rpx; color: #999;"},on:{focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.onBlur.apply(void 0,arguments)},keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault(),arguments[0]=t=e.$handleEvent(t)}},model:{value:e.model[e.prop],callback:function(t){e.$set(e.model,e.prop,t)},expression:"model[prop]"}})],1)])],2)],1)},i=[]},e2b4:function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={props:{label:{type:String,default:""},model:{type:Object,default:function(){return{}}},prop:{type:String,default:""},placeholder:{type:String,default:""}},data:function(){return{isFocus:!1}},computed:{currPlaceholder:function(e){var t=e.label,n=e.placeholder;return n||"请输入".concat(t)}},methods:{onFocus:function(){this.isFocus=!0},onBlur:function(){var e=this,t=setTimeout((function(){e.isFocus=!1,t&&clearTimeout(t)}),100)},onClear:function(){this.model[this.prop]=""}}};t.default=a},f074:function(e,t,n){"use strict";n.r(t);var a=n("7f1a"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},f2f9:function(e,t,n){"use strict";var a=n("a126"),i=n.n(a);i.a},fb8c:function(e,t,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("f3f3")),o=a(n("fc11"));n("99af"),n("4de4"),n("d3b7"),n("ac1f"),n("00b4"),n("159b"),n("caad6"),n("b64b"),n("14d9");var r,c=n("26cb"),s=n("d8be"),l=n("06cd"),u=n("cd8b"),d=(r={},(0,o.default)(r,"".concat(s.MInvoiceType.General,"-").concat(s.MInvoiceFrontType.Person),{requireKeys:["invoice_name","email"],optionalKeys:[]}),(0,o.default)(r,"".concat(s.MInvoiceType.General,"-").concat(s.MInvoiceFrontType.Company),{requireKeys:["invoice_name","taxpayer","email"],optionalKeys:["company_address","company_tel","opening_bank","bank_account"]}),(0,o.default)(r,"".concat(s.MInvoiceType.Special,"-").concat(s.MInvoiceFrontType.Company),{requireKeys:["invoice_name","taxpayer","email","company_address","company_tel","opening_bank","bank_account"],optionalKeys:["consignee","telephone","province_id","city_id","town_id","province_name","city_name","town_name","address"]}),r),p={data:function(){return{MInvoiceType:s.MInvoiceType,MInvoiceFrontType:s.MInvoiceFrontType,MInvoiceTypeText:l.MInvoiceTypeText,MInvoiceFrontTypeText:l.MInvoiceFrontTypeText,params:{invoice_type:s.MInvoiceType.General,type_id:s.MInvoiceFrontType.Person,invoice_name:"",taxpayer:"",email:"",company_address:"",company_tel:"",opening_bank:"",bank_account:"",consignee:"",telephone:"",province_id:"",city_id:"",town_id:"",province_name:"",city_name:"",town_name:"",address:"",is_default:0},isExpandMore:!1,regionVisible:!1}},computed:(0,i.default)((0,i.default)({},(0,c.mapState)(["routeTable","invoiceInfo","addressInfoState","regionInfo"])),{},{isEdit:function(e){var t=e.params;return!!t.id},title:function(e){var t=e.isEdit;return t?"修改发票抬头":"添加发票抬头"},btnText:function(e){var t=e.isEdit;return t?"保存":"提交"},currType:function(e){var t=e.params,n=t.invoice_type,a=t.type_id;return"".concat(n,"-").concat(a)},currKeyObj:function(e){var t=e.currType;return d[t]||{requireKeys:[],optionalKeys:[]}},currMInvoiceFrontTypeText:function(e){var t=e.params;return t.invoice_type===s.MInvoiceType.Special?l.MInvoiceFrontTypeText.filter((function(e){var t=e.value;return t===s.MInvoiceFrontType.Company})):l.MInvoiceFrontTypeText},isGeneralInvoiceType:function(e){var t=e.params;return t.invoice_type===s.MInvoiceType.General},isSpecialInvoiceType:function(e){var t=e.params;return t.invoice_type===s.MInvoiceType.Special},isPersonInvoiceFrontType:function(e){var t=e.params;return t.type_id===s.MInvoiceFrontType.Person},isCompanyInvoiceFrontType:function(e){var t=e.params;return t.type_id===s.MInvoiceFrontType.Company},disabled:function(e){var t=e.params,n=e.currKeyObj,a=n.requireKeys,i=a.every((function(e){return!!t[e]}));return!i}}),watch:{"params.invoice_type":function(){this.isSpecialInvoiceType&&(this.params.type_id=s.MInvoiceFrontType.Company)}},methods:(0,i.default)((0,i.default)({},(0,c.mapMutations)(["muInvoiceInfo","muAddressInfoState","muRegionInfo"])),{},{onClearRegion:function(){this.params=Object.assign({},this.params,{province_id:"",city_id:"",town_id:"",province_name:"",city_name:"",town_name:""})},submit:function(){var e=this,t=this.params,n=t.id,a=t.invoice_type,i=t.type_id,o=t.email,r=t.is_default;if(u.emailPattern.test(o)){var c=this.currKeyObj,s=c.requireKeys,l=c.optionalKeys,d=s.concat(l),p={invoice_type:a,type_id:i,is_default:r};d.forEach((function(t){["province_id","city_id","town_id"].includes(t)?p[t]=e.params[t]||"":p[t]=e.params[t]})),console.log("params",p),n?(p.id=n,this.$u.api.updateInvoice(p).then((function(){e.feedback.toast({title:"修改成功",icon:"success"}),e.back()}))):this.$u.api.addInvoice(p).then((function(){e.feedback.toast({title:"添加成功",icon:"success"}),e.back()}))}else this.feedback.toast({title:"请输入正确的邮箱地址",icon:"error"})},del:function(){var e=this;this.feedback.showModal({content:"确认删除吗？",confirm:function(){e.$u.api.deleteInvoice({id:[e.params.id]}).then((function(){e.feedback.toast({title:"删除成功",icon:"success"}),e.back()}))}})},back:function(){var e=this,t=setTimeout((function(){e.jump.jumpPrePage(e.$vhFrom),t&&clearTimeout(t)}),1500)},initRegionData:function(){var e=this;Object.keys(this.regionInfo).length||this.$u.api.regionList({isJson:!0}).then((function(t){var n=(null===t||void 0===t?void 0:t.data)||{},a=n.list,i=void 0===a?[]:a,o=[],r=[],c=[];i.forEach((function(e,t){o.push({label:e.name,value:e.id}),r.push([]),c.push([]),e.children.forEach((function(e,n){r[t].push({label:e.name,value:e.id}),c[t].push([]),e.children.forEach((function(e){c[t][n].push({label:e.name,value:e.id})}))}))})),e.muRegionInfo({provinces:o,citys:r,areas:c})}))},changeRegion:function(e){var t=e.province,n=t.value,a=t.label,i=e.city,o=i.value,r=i.label,c=e.area,s=c.value,l=c.label;this.params=Object.assign({},this.params,{province_id:n,city_id:o,town_id:s,province_name:a,city_name:r,town_name:l})}}),onLoad:function(){var e=this;this.login.isLoginV3(this.$vhFrom).then((function(t){if(t){var n=uni.getStorageSync("InvoiceInfo");n&&(e.muInvoiceInfo(n),uni.removeStorageSync("InvoiceInfo")),e.initRegionData(),e.params=Object.assign({},e.params,e.invoiceInfo),e.muInvoiceInfo({})}}))},onShow:function(){if(Object.keys(this.addressInfoState).length){var e=this.addressInfoState,t=e.consignee,n=e.consignee_phone,a=e.province_id,i=e.city_id,o=e.town_id,r=e.province_name,c=e.city_name,s=e.town_name,l=e.address;this.params=Object.assign({},this.params,{consignee:t,telephone:n,province_id:a,city_id:i,town_id:o,province_name:r,city_name:c,town_name:s,address:l}),this.muAddressInfoState({})}}};t.default=p},ffda:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return a}));var a={vhNavbar:n("12c6").default,InvoiceHeadFormItem:n("2913").default,uButton:n("4f1b").default,vhRegion:n("b94e").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"iha-form"},[n("vh-navbar",{attrs:{title:e.title,height:"46"}},[e.isEdit?n("v-uni-button",{staticClass:"vh-btn flex-c-c w-108 h-60 font-30 text-3 bg-ffffff",attrs:{slot:"right"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.del.apply(void 0,arguments)}},slot:"right"},[e._v("删除")]):e._e()],1),n("v-uni-view",{staticClass:"pb-104"},[n("v-uni-view",{staticClass:"bg-ffffff mtb-20-mlr-24 ptb-00-plr-24 b-rad-10"},[n("InvoiceHeadFormItem",{attrs:{label:"发票类型"}},[n("v-uni-view",{staticClass:"d-flex"},e._l(e.MInvoiceTypeText,(function(t){return n("v-uni-button",{key:t.value,staticClass:"vh-btn flex-c-c mr-20 w-144 h-44 b-rad-24 font-24",class:[t.value===e.params.invoice_type?"bg-fce4e3 text-e80404":"bg-f6f6f6 text-3"],on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.params.invoice_type=t.value}}},[e._v(e._s(t.text))])})),1)],1),n("InvoiceHeadFormItem",{attrs:{label:"抬头类型"}},[n("v-uni-view",{staticClass:"d-flex"},e._l(e.currMInvoiceFrontTypeText,(function(t){return n("v-uni-button",{key:t.value,staticClass:"vh-btn flex-c-c mr-20 w-144 h-44 b-rad-24 font-24",class:[t.value===e.params.type_id?"bg-fce4e3 text-e80404":"bg-f6f6f6 text-3"],on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.params.type_id=t.value}}},[e._v(e._s(t.text))])})),1)],1),e.isGeneralInvoiceType&&e.isPersonInvoiceFrontType?[n("InvoiceHeadFormItem",{attrs:{label:"发票抬头",model:e.params,prop:"invoice_name"}}),n("InvoiceHeadFormItem",{attrs:{label:"邮箱地址",model:e.params,prop:"email"}})]:e._e(),e.isGeneralInvoiceType&&e.isCompanyInvoiceFrontType?[n("InvoiceHeadFormItem",{attrs:{label:"发票抬头",model:e.params,prop:"invoice_name"}}),n("InvoiceHeadFormItem",{attrs:{label:"单位税号",model:e.params,prop:"taxpayer"}}),n("InvoiceHeadFormItem",{attrs:{label:"邮箱地址",model:e.params,prop:"email"}}),n("v-uni-view",{staticClass:"flex-s-c pt-32 bt-s-02-eeeeee",class:e.isExpandMore?"pb-0":"pb-32",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.isExpandMore=!e.isExpandMore}}},[n("v-uni-text",{staticClass:"font-28 text-9 l-h-40"},[e._v("更多发票信息(选填)")]),n("v-uni-image",{staticClass:"ml-10 w-20 h-12",class:e.isExpandMore?"t-ro-n-180 tran-2":"tran-2",attrs:{src:e.ossIcon("/invoices/arrow_d_20_12.png")}})],1),e.isExpandMore?n("v-uni-view",[n("InvoiceHeadFormItem",{attrs:{label:"单位地址",model:e.params,prop:"company_address"}}),n("InvoiceHeadFormItem",{attrs:{label:"单位电话",model:e.params,prop:"company_tel"}}),n("InvoiceHeadFormItem",{attrs:{label:"开户银行",model:e.params,prop:"opening_bank"}}),n("InvoiceHeadFormItem",{attrs:{label:"银行账号",model:e.params,prop:"bank_account"}})],1):e._e()]:e._e(),e.isSpecialInvoiceType&&e.isCompanyInvoiceFrontType?[n("InvoiceHeadFormItem",{attrs:{label:"发票抬头",model:e.params,prop:"invoice_name"}}),n("InvoiceHeadFormItem",{attrs:{label:"单位税号",model:e.params,prop:"taxpayer"}}),n("InvoiceHeadFormItem",{attrs:{label:"邮箱地址",model:e.params,prop:"email"}}),n("InvoiceHeadFormItem",{attrs:{label:"单位地址",model:e.params,prop:"company_address"}}),n("InvoiceHeadFormItem",{attrs:{label:"单位电话",model:e.params,prop:"company_tel"}}),n("InvoiceHeadFormItem",{attrs:{label:"开户银行",model:e.params,prop:"opening_bank"}}),n("InvoiceHeadFormItem",{attrs:{label:"银行账号",model:e.params,prop:"bank_account"}}),n("v-uni-view",{staticClass:"flex-sb-c pt-32 bt-s-02-eeeeee",class:e.isExpandMore?"pb-0":"pb-32",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.isExpandMore=!e.isExpandMore}}},[n("v-uni-view",{staticClass:"flex-c-c"},[n("v-uni-text",{staticClass:"font-28 text-9 l-h-40"},[e._v("更多发票信息(选填)")]),n("v-uni-image",{staticClass:"ml-10 w-20 h-12",class:e.isExpandMore?"t-ro-n-180 tran-2":"tran-2",attrs:{src:e.ossIcon("/invoices/arrow_d_20_12.png")}})],1),e.isExpandMore?n("v-uni-button",{staticClass:"vh-btn flex-c-c w-124 h-36 font-24 text-6 bg-ffffff b-s-01-d8d8d8 b-rad-18",on:{click:[function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)},function(t){arguments[0]=t=e.$handleEvent(t),e.jump.navigateTo(e.routeTable.pEAddressManagement+"?comeFrom=10")}]}},[e._v("导入地址")]):e._e()],1),e.isExpandMore?n("v-uni-view",[n("InvoiceHeadFormItem",{attrs:{label:"收票人",model:e.params,prop:"consignee",placeholder:"收票人姓名"}}),n("InvoiceHeadFormItem",{attrs:{label:"手机号码",model:e.params,prop:"telephone",placeholder:"收票人手机号"}}),n("InvoiceHeadFormItem",{attrs:{label:"所在地区",model:e.params,prop:"address",placeholder:"省份、市区、地区"}},[n("v-uni-view",{staticClass:"d-flex j-sb",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.regionVisible=!0}}},[n("v-uni-view",{staticClass:"font-28 l-h-40"},[e.params.province_name&&e.params.city_name&&e.params.town_name?n("v-uni-text",{staticClass:"text-3"},[e._v(e._s(e.params.province_name)+" "+e._s(e.params.city_name)+" "+e._s(e.params.town_name))]):n("v-uni-text",{staticClass:"text-9"},[e._v("省份、市区、地区")])],1)],1)],1),n("InvoiceHeadFormItem",{attrs:{label:"详细地址",model:e.params,prop:"address",placeholder:"街道、楼牌号等"}})],1):e._e()]:e._e()],2)],1),n("v-uni-view",{staticClass:"p-fixed bottom-0 z-999 w-p100 h-104 bg-feffff b-sh-00021200-022 d-flex j-center a-center"},[n("u-button",{attrs:{disabled:e.disabled,shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:e.disabled?"#FCE4E3":"#E80404",border:"none"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.submit.apply(void 0,arguments)}}},[e._v(e._s(e.btnText))])],1),Object.keys(e.regionInfo).length?n("vh-region",{on:{confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.changeRegion.apply(void 0,arguments)}},model:{value:e.regionVisible,callback:function(t){e.regionVisible=t},expression:"regionVisible"}}):e._e()],1)},o=[]}}]);