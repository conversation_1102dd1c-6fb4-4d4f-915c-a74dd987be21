(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-goods-detail-auction-goods-detail"],{"00dc":function(t,e,i){(function(t){var n=i("58a2"),r=i("c24d"),a=i("561d");var o={binary:!0,hex:!0,base64:!0};e.<PERSON><PERSON>ie<PERSON>ellmanGroup=e.createDiffieHellmanGroup=e.getDiffie<PERSON>ellman=function(e){var i=new t(r[e].prime,"hex"),n=new t(r[e].gen,"hex");return new a(i,n)},e.createDiffie<PERSON>ell<PERSON>=e.<PERSON><PERSON><PERSON>=function e(i,r,s,c){return t.isBuffer(r)||void 0===o[r]?e(i,"binary",r,s):(r=r||"binary",c=c||"binary",s=s||new t([2]),t.is<PERSON>uffer(s)||(s=new t(s,c)),"number"===typeof i?new a(n(i,s),s,!0):(t.isBuffer(i)||(i=new t(i,r)),new a(i,s,!0)))}}).call(this,i("b639").Buffer)},"0145":function(t,e){e.encrypt=function(t,e){return t._cipher.encryptBlock(e)},e.decrypt=function(t,e){return t._cipher.decryptBlock(e)}},"0184":function(t,e,i){"use strict";var n=i("da3e");function r(t){this.options=t,this.type=this.options.type,this.blockSize=8,this._init(),this.buffer=new Array(this.blockSize),this.bufferOff=0}t.exports=r,r.prototype._init=function(){},r.prototype.update=function(t){return 0===t.length?[]:"decrypt"===this.type?this._updateDecrypt(t):this._updateEncrypt(t)},r.prototype._buffer=function(t,e){for(var i=Math.min(this.buffer.length-this.bufferOff,t.length-e),n=0;n<i;n++)this.buffer[this.bufferOff+n]=t[e+n];return this.bufferOff+=i,i},r.prototype._flushBuffer=function(t,e){return this._update(this.buffer,0,t,e),this.bufferOff=0,this.blockSize},r.prototype._updateEncrypt=function(t){var e=0,i=0,n=(this.bufferOff+t.length)/this.blockSize|0,r=new Array(n*this.blockSize);0!==this.bufferOff&&(e+=this._buffer(t,e),this.bufferOff===this.buffer.length&&(i+=this._flushBuffer(r,i)));for(var a=t.length-(t.length-e)%this.blockSize;e<a;e+=this.blockSize)this._update(t,e,r,i),i+=this.blockSize;for(;e<t.length;e++,this.bufferOff++)this.buffer[this.bufferOff]=t[e];return r},r.prototype._updateDecrypt=function(t){for(var e=0,i=0,n=Math.ceil((this.bufferOff+t.length)/this.blockSize)-1,r=new Array(n*this.blockSize);n>0;n--)e+=this._buffer(t,e),i+=this._flushBuffer(r,i);return e+=this._buffer(t,e),r},r.prototype.final=function(t){var e,i;return t&&(e=this.update(t)),i="encrypt"===this.type?this._finalEncrypt():this._finalDecrypt(),e?e.concat(i):i},r.prototype._pad=function(t,e){if(0===e)return!1;while(e<t.length)t[e++]=0;return!0},r.prototype._finalEncrypt=function(){if(!this._pad(this.buffer,this.bufferOff))return[];var t=new Array(this.blockSize);return this._update(this.buffer,0,t,0),t},r.prototype._unpad=function(t){return t},r.prototype._finalDecrypt=function(){n.equal(this.bufferOff,this.blockSize,"Not enough data to decrypt");var t=new Array(this.blockSize);return this._flushBuffer(t,0),this._unpad(t)}},"0211":function(t,e,i){"use strict";const n=e;n._reverse=function(t){const e={};return Object.keys(t).forEach((function(i){(0|i)==i&&(i|=0);const n=t[i];e[n]=i})),e},n.der=i("8b71")},"0240":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={AuctionDelayPopup:i("5f76").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"font-24 text-6 l-h-34"},[i("v-uni-view",{staticClass:"flex-s-c"},[i("v-uni-view",{staticClass:"flex-s-c w-328"},[i("v-uni-text",[t._v("起拍价")]),i("v-uni-text",{staticClass:"ml-46 font-28 text-3 l-h-40"},[t._v("¥"+t._s(t.goods.price))])],1),i("v-uni-view",{staticClass:"ml-20 flex-c-c"},[i("v-uni-text",[t._v("保证金")]),i("v-uni-text",{staticClass:"ml-20 font-28 text-3 l-h-40"},[t._v("¥"+t._s(t.goods.margin))]),t.earnestStatus?i("v-uni-image",{staticClass:"ml-06 w-28 h-28",attrs:{src:t.ossIcon("/auction/ask_red_28.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumpNewFundsDetail.apply(void 0,arguments)}}}):t._e()],1)],1),i("v-uni-view",{staticClass:"flex-s-c mt-20"},[i("v-uni-text",{staticClass:"flex-shrink"},[t._v("加价幅度")]),i("v-uni-text",{staticClass:"ml-20 font-28 text-3 l-h-40"},[t._v("¥"+t._s(t.goods.markup))])],1),t.goods.sell_time&&t.goods.closing_auction_time?i("v-uni-view",{staticClass:"d-flex mt-20"},[i("v-uni-text",[t._v("竞拍周期")]),i("v-uni-text",{staticClass:"ml-20 font-26 text-3 l-h-34"},[t._v(t._s(t._f("date")(t.goods.sell_time,"yyyy.mm.dd hh:MM:ss"))+" - "+t._s(t._f("date")(t.goods.closing_auction_time,"yyyy.mm.dd hh:MM:ss")))])],1):t._e(),i("v-uni-view",{staticClass:"flex-s-c mt-20"},[i("v-uni-text",[t._v("延时周期")]),i("v-uni-text",{staticClass:"ml-20 font-28 text-3 l-h-40"},[t._v("5分钟/次")]),i("v-uni-image",{staticClass:"ml-06 w-28 h-28",attrs:{src:t.ossIcon("/auction/icon_ask.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.delayPopupVisible=!0}}})],1),t.isShowNonsupport?i("v-uni-view",{staticClass:"flex-s-c mt-20 pt-20 font-24 text-6 l-h-34 bt-s-02-eeeeee"},[i("v-uni-view",{staticClass:"flex-s-c w-328"},[i("v-uni-image",{staticClass:"w-28 h-28",attrs:{src:t.ossIcon("/auction/tips_28.png")}}),i("v-uni-text",{staticClass:"ml-06"},[t._v("不支持七天无理由退货")])],1),i("v-uni-view",{staticClass:"flex-s-c"},[i("v-uni-image",{staticClass:"w-28 h-28",attrs:{src:t.ossIcon("/auction/tips_28.png")}}),i("v-uni-text",{staticClass:"ml-06"},[t._v("不支持发票")])],1)],1):t._e()],1),i("AuctionDelayPopup",{model:{value:t.delayPopupVisible,callback:function(e){t.delayPopupVisible=e},expression:"delayPopupVisible"}})],1)},a=[]},"05f3":function(t,e,i){"use strict";i.r(e);var n=i("9c27"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"0792":function(t,e){var i={errorImg:null,filter:null,highlight:null,onText:null,entities:{quot:'"',apos:"'",semi:";",nbsp:" ",ensp:" ",emsp:" ",ndash:"–",mdash:"—",middot:"·",lsquo:"‘",rsquo:"’",ldquo:"“",rdquo:"”",bull:"•",hellip:"…"},blankChar:n(" , ,\t,\r,\n,\f"),boolAttrs:n("allowfullscreen,autoplay,autostart,controls,ignore,loop,muted"),blockTags:n("address,article,aside,body,caption,center,cite,footer,header,html,nav,pre,section"),ignoreTags:n("area,base,canvas,frame,iframe,input,link,map,meta,param,script,source,style,svg,textarea,title,track,wbr"),richOnlyTags:n("a,colgroup,fieldset,legend"),selfClosingTags:n("area,base,br,col,circle,ellipse,embed,frame,hr,img,input,line,link,meta,param,path,polygon,rect,source,track,use,wbr"),trustTags:n("a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video"),userAgentStyles:{address:"font-style:italic",big:"display:inline;font-size:1.2em",blockquote:"background-color:#f6f6f6;border-left:3px solid #dbdbdb;color:#6c6c6c;padding:5px 0 5px 10px",caption:"display:table-caption;text-align:center",center:"text-align:center",cite:"font-style:italic",dd:"margin-left:40px",mark:"background-color:yellow",pre:"font-family:monospace;white-space:pre;overflow:scroll",s:"text-decoration:line-through",small:"display:inline;font-size:0.8em",u:"text-decoration:underline"}};function n(t){for(var e=Object.create(null),i=t.split(","),n=i.length;n--;)e[i[n]]=!0;return e}t.exports=i},"07dd":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("b64b"),i("7db0"),i("d3b7");var r=n(i("f07e")),a=n(i("c964")),o=n(i("f3f3")),s=i("26cb"),c=i("d8be"),f={props:{value:{type:Boolean,default:!0},goods:{type:Object,default:function(){return{}}},earnestCouponInfo:{type:Object,default:function(){return{}}}},data:function(){return{list:["保证金由委托方确认，只有缴纳保证金后方可获得竞拍资格；","竞拍结束后，如果您未出价或者被淘汰，平台将于72小时内退还保证金至原支付账户；",'如竞拍成功，保证金将会在您<text class="text-e80404">【支付货款后72小时内退至原支付账户】</text>','如竞拍成功后48小时内未付款则被视为违约，<text class="text-e80404">保证金将被全额扣除，同时扣除30个信用分，并禁止在未来的一个月内参与拍卖</text>。'],addressInfo:{},checked:!1}},computed:(0,o.default)((0,o.default)({},(0,s.mapState)(["routeTable","addressInfoState","agreementPrefix"])),{},{money:function(){var t=this.earnestCouponInfo,e=t.id,i=t.coupon_face_value;return e?this.goods.margin>i?this.goods.margin-i:.01:this.goods.margin}}),methods:(0,o.default)((0,o.default)({},(0,s.mapMutations)(["muPayInfo"])),{},{onInput:function(t){this.$emit("input",t)},initAddressInfo:function(){Object.keys(this.addressInfoState).length?this.addressInfo=this.addressInfoState:this.loadAddressInfo()},loadAddressInfo:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i,n,a,o;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.addressList();case 2:n=e.sent,a=(null===n||void 0===n||null===(i=n.data)||void 0===i?void 0:i.list)||[],a.length&&(o=a.find((function(t){return t.is_default})),t.addressInfo=o||a[0]);case 5:case"end":return e.stop()}}),e)})))()},onSubmit:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i,n,a,o,s,f;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.log("onSubmit",t.addressInfo.id,t.checked),t.addressInfo.id&&t.checked){e.next=3;break}return e.abrupt("return");case 3:return t.feedback.loading({title:"提交中..."}),i=2,n=t.$android,a=t.$ios,n?i=1:a&&(i=0),o={order_from:i,type:c.MAuctionEarnestType.Bidding,goods_id:t.goods.id,address_id:t.addressInfo.id},t.earnestCouponInfo.id&&(o.coupon_issue_id=t.earnestCouponInfo.id),e.prev=9,e.next=12,t.$u.api.createAuctionEarnestOrder(o);case 12:s=e.sent,f=(null===s||void 0===s?void 0:s.data)||{},t.$emit("orderCreateSuccess",f),e.next=20;break;case 17:e.prev=17,e.t0=e["catch"](9),t.$emit("orderCreateError");case 20:case"end":return e.stop()}}),e,null,[[9,17]])})))()}})};e.default=f},"07f2":function(t,e,i){"use strict";var n=i("c3c0"),r=i("6eed");function a(){if(!(this instanceof a))return new a;r.call(this),this.h=[3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428]}n.inherits(a,r),t.exports=a,a.blockSize=512,a.outSize=224,a.hmacStrength=192,a.padLength=64,a.prototype._digest=function(t){return"hex"===t?n.toHex32(this.h.slice(0,7),"big"):n.split32(this.h.slice(0,7),"big")}},"087f":function(t,e,i){var n=i("3fb5"),r=i("b672"),a=i("8707").Buffer,o=[1518500249,1859775393,-1894007588,-899497514],s=new Array(80);function c(){this.init(),this._w=s,r.call(this,64,56)}function f(t){return t<<5|t>>>27}function u(t){return t<<30|t>>>2}function d(t,e,i,n){return 0===t?e&i|~e&n:2===t?e&i|e&n|i&n:e^i^n}n(c,r),c.prototype.init=function(){return this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520,this},c.prototype._update=function(t){for(var e=this._w,i=0|this._a,n=0|this._b,r=0|this._c,a=0|this._d,s=0|this._e,c=0;c<16;++c)e[c]=t.readInt32BE(4*c);for(;c<80;++c)e[c]=e[c-3]^e[c-8]^e[c-14]^e[c-16];for(var h=0;h<80;++h){var l=~~(h/20),p=f(i)+d(l,n,r,a)+s+e[h]+o[l]|0;s=a,a=r,r=u(n),n=i,i=p}this._a=i+this._a|0,this._b=n+this._b|0,this._c=r+this._c|0,this._d=a+this._d|0,this._e=s+this._e|0},c.prototype._hash=function(){var t=a.allocUnsafe(20);return t.writeInt32BE(0|this._a,0),t.writeInt32BE(0|this._b,4),t.writeInt32BE(0|this._c,8),t.writeInt32BE(0|this._d,12),t.writeInt32BE(0|this._e,16),t},t.exports=c},"0885":function(t,e,i){var n=i("78a8");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("53c0b9a4",n,!0,{sourceMap:!1,shadowMode:!1})},"08de":function(t,e,i){"use strict";i.r(e);var n=i("1435"),r=i("3995");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"d0e9a7dc",null,!1,n["a"],void 0);e["default"]=s.exports},"0960":function(t,e,i){t.exports=i("b19a")},"09c0":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("ac1f");var n={props:{item:{type:Object,default:function(){return{}}}},data:function(){return{isHiddenText:!1}},computed:{id:function(t){var e=t.item;return"comment-content-".concat(e.id)}},methods:{onExpand:function(){this.isHiddenText=!1}},mounted:function(){var t=this;uni.createSelectorQuery().in(this).select("#".concat(this.id)).boundingClientRect((function(e){var i=(null===e||void 0===e?void 0:e.height)||0;i>uni.upx2px(80)&&(t.isHiddenText=!0)})).exec()}};e.default=n},"09f5":function(t,e,i){var n=i("39f5"),r=i("8707").Buffer,a=i("6430"),o=i("3fb5");function s(t,e,i,o){a.call(this),this._cipher=new n.AES(e),this._prev=r.from(i),this._cache=r.allocUnsafe(0),this._secCache=r.allocUnsafe(0),this._decrypt=o,this._mode=t}o(s,a),s.prototype._update=function(t){return this._mode.encrypt(this,t,this._decrypt)},s.prototype._final=function(){this._cipher.scrub()},t.exports=s},"0be83":function(t,e){e["des-ecb"]={key:8,iv:0},e["des-cbc"]=e.des={key:8,iv:8},e["des-ede3-cbc"]=e.des3={key:24,iv:8},e["des-ede3"]={key:24,iv:0},e["des-ede-cbc"]={key:16,iv:8},e["des-ede"]={key:16,iv:0}},"0cbb":function(t,e,i){"use strict";var n,r=e,a=i("7d92"),o=i("4136"),s=i("f3a3"),c=s.assert;function f(t){"short"===t.type?this.curve=new o.short(t):"edwards"===t.type?this.curve=new o.edwards(t):this.curve=new o.mont(t),this.g=this.curve.g,this.n=this.curve.n,this.hash=t.hash,c(this.g.validate(),"Invalid curve"),c(this.g.mul(this.n).isInfinity(),"Invalid curve, G*N != O")}function u(t,e){Object.defineProperty(r,t,{configurable:!0,enumerable:!0,get:function(){var i=new f(e);return Object.defineProperty(r,t,{configurable:!0,enumerable:!0,value:i}),i}})}r.PresetCurve=f,u("p192",{type:"short",prime:"p192",p:"ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff",a:"ffffffff ffffffff ffffffff fffffffe ffffffff fffffffc",b:"64210519 e59c80e7 0fa7e9ab 72243049 feb8deec c146b9b1",n:"ffffffff ffffffff ffffffff 99def836 146bc9b1 b4d22831",hash:a.sha256,gRed:!1,g:["188da80e b03090f6 7cbf20eb 43a18800 f4ff0afd 82ff1012","07192b95 ffc8da78 631011ed 6b24cdd5 73f977a1 1e794811"]}),u("p224",{type:"short",prime:"p224",p:"ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001",a:"ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff fffffffe",b:"b4050a85 0c04b3ab f5413256 5044b0b7 d7bfd8ba 270b3943 2355ffb4",n:"ffffffff ffffffff ffffffff ffff16a2 e0b8f03e 13dd2945 5c5c2a3d",hash:a.sha256,gRed:!1,g:["b70e0cbd 6bb4bf7f 321390b9 4a03c1d3 56c21122 343280d6 115c1d21","bd376388 b5f723fb 4c22dfe6 cd4375a0 5a074764 44d58199 85007e34"]}),u("p256",{type:"short",prime:null,p:"ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff ffffffff",a:"ffffffff 00000001 00000000 00000000 00000000 ffffffff ffffffff fffffffc",b:"5ac635d8 aa3a93e7 b3ebbd55 769886bc 651d06b0 cc53b0f6 3bce3c3e 27d2604b",n:"ffffffff 00000000 ffffffff ffffffff bce6faad a7179e84 f3b9cac2 fc632551",hash:a.sha256,gRed:!1,g:["6b17d1f2 e12c4247 f8bce6e5 63a440f2 77037d81 2deb33a0 f4a13945 d898c296","4fe342e2 fe1a7f9b 8ee7eb4a 7c0f9e16 2bce3357 6b315ece cbb64068 37bf51f5"]}),u("p384",{type:"short",prime:null,p:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe ffffffff 00000000 00000000 ffffffff",a:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe ffffffff 00000000 00000000 fffffffc",b:"b3312fa7 e23ee7e4 988e056b e3f82d19 181d9c6e fe814112 0314088f 5013875a c656398d 8a2ed19d 2a85c8ed d3ec2aef",n:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff c7634d81 f4372ddf 581a0db2 48b0a77a ecec196a ccc52973",hash:a.sha384,gRed:!1,g:["aa87ca22 be8b0537 8eb1c71e f320ad74 6e1d3b62 8ba79b98 59f741e0 82542a38 5502f25d bf55296c 3a545e38 72760ab7","3617de4a 96262c6f 5d9e98bf 9292dc29 f8f41dbd 289a147c e9da3113 b5f0b8c0 0a60b1ce 1d7e819d 7a431d7c 90ea0e5f"]}),u("p521",{type:"short",prime:null,p:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff",a:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffc",b:"00000051 953eb961 8e1c9a1f 929a21a0 b68540ee a2da725b 99b315f3 b8b48991 8ef109e1 56193951 ec7e937b 1652c0bd 3bb1bf07 3573df88 3d2c34f1 ef451fd4 6b503f00",n:"000001ff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffa 51868783 bf2f966b 7fcc0148 f709a5d0 3bb5c9b8 899c47ae bb6fb71e 91386409",hash:a.sha512,gRed:!1,g:["000000c6 858e06b7 0404e9cd 9e3ecb66 2395b442 9c648139 053fb521 f828af60 6b4d3dba a14b5e77 efe75928 fe1dc127 a2ffa8de 3348b3c1 856a429b f97e7e31 c2e5bd66","00000118 39296a78 9a3bc004 5c8a5fb4 2c7d1bd9 98f54449 579b4468 17afbd17 273e662c 97ee7299 5ef42640 c550b901 3fad0761 353c7086 a272c240 88be9476 9fd16650"]}),u("curve25519",{type:"mont",prime:"p25519",p:"7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed",a:"76d06",b:"1",n:"1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed",hash:a.sha256,gRed:!1,g:["9"]}),u("ed25519",{type:"edwards",prime:"p25519",p:"7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed",a:"-1",c:"1",d:"52036cee2b6ffe73 8cc740797779e898 00700a4d4141d8ab 75eb4dca135978a3",n:"1000000000000000 0000000000000000 14def9dea2f79cd6 5812631a5cf5d3ed",hash:a.sha256,gRed:!1,g:["216936d3cd6e53fec0a4e231fdd6dc5c692cc7609525a7b2c9562d608f25d51a","6666666666666666666666666666666666666666666666666666666666666658"]});try{n=i("409b")}catch(d){n=void 0}u("secp256k1",{type:"short",prime:"k256",p:"ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f",a:"0",b:"7",n:"ffffffff ffffffff ffffffff fffffffe baaedce6 af48a03b bfd25e8c d0364141",h:"1",hash:a.sha256,beta:"7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee",lambda:"5363ad4cc05c30e0a5261c028812645a122e22ea20816678df02967c1b23bd72",basis:[{a:"3086d221a7d46bcde86c90e49284eb15",b:"-e4437ed6010e88286f547fa90abfe4c3"},{a:"114ca50f7a8e2f3f657c1108d9d44cfd8",b:"3086d221a7d46bcde86c90e49284eb15"}],gRed:!1,g:["79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798","483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8",n]})},"0da4":function(t,e,i){"use strict";var n=i("da3e"),r=i("3fb5"),a={};function o(t){n.equal(t.length,8,"Invalid IV length"),this.iv=new Array(8);for(var e=0;e<this.iv.length;e++)this.iv[e]=t[e]}e.instantiate=function(t){function e(e){t.call(this,e),this._cbcInit()}r(e,t);for(var i=Object.keys(a),n=0;n<i.length;n++){var o=i[n];e.prototype[o]=a[o]}return e.create=function(t){return new e(t)},e},a._cbcInit=function(){var t=new o(this.options.iv);this._cbcState=t},a._update=function(t,e,i,n){var r=this._cbcState,a=this.constructor.super_.prototype,o=r.iv;if("encrypt"===this.type){for(var s=0;s<this.blockSize;s++)o[s]^=t[e+s];a._update.call(this,o,0,i,n);for(s=0;s<this.blockSize;s++)o[s]=i[n+s]}else{a._update.call(this,t,e,i,n);for(s=0;s<this.blockSize;s++)i[n+s]^=o[s];for(s=0;s<this.blockSize;s++)o[s]=t[e+s]}}},"0e01":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"vh-check",props:{width:{type:[String,Number],default:32},height:{type:[String,Number],default:32},checked:{type:Boolean,default:!1},checkedImg:{type:String,default:"https://images.vinehoo.com/vinehoomini/v3/comm/cir_sel.png"},unCheckedImg:{type:String,default:"https://images.vinehoo.com/vinehoomini/v3/comm/cir_unsel.png"},mode:{type:String,default:"aspectFill"},marginTop:{type:[String,Number],default:0}},computed:{checkStyle:function(){var t={};return t.marginTop=this.marginTop+"rpx",t.width=this.width+"rpx",t.height=this.height+"rpx",t}},methods:{click:function(){this.$emit("click")}}};e.default=n},"0e30":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d81d"),i("b64b"),i("99af"),i("caad6"),i("2532"),i("4de4"),i("d3b7"),i("ac1f"),i("00b4");var r=n(i("f07e")),a=n(i("c964")),o=n(i("f3f3")),s=i("26cb"),c=i("d8be"),f=i("1e48"),u=n(i("097a")),d={WX:1,ALI:2},h={props:{value:{type:Boolean,default:!0},orderInfo:{type:Object,default:function(){return{}}}},data:function(){return{PAY_TYPE:d,isWxProcess:!1,type:"",ailPayForm:"",code:"",wxPayOpenId:""}},computed:(0,o.default)((0,o.default)({},(0,s.mapState)(["routeTable"])),{},{mainOrderNo:function(t){var e=t.orderInfo,i=e||{},n=i.main_order_no,r=void 0===n?"":n,a=i.order_no,o=void 0===a?"":a;return r||o},pageFullPath:function(){return this.pages.getPageFullPath()},isShowWxSelect:function(t){var e=t.$app,i=t.isWxProcess;return!!e||i},isShowAliSelect:function(t){var e=t.$app,i=t.isWxProcess;return!!e||!i}}),watch:{value:function(){this.type=this.$options.data().type}},methods:{onInput:function(t){this.$emit("input",t)},onPay:function(){var t=arguments,e=this;return(0,a.default)((0,r.default)().mark((function i(){var n;return(0,r.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(n=t.length>0&&void 0!==t[0]?t[0]:d.WX,i.prev=1,e.feedback.loading({title:"支付中..."}),e.type=n,!e.$app){i.next=8;break}return window.onPullAppPayFail=function(){e.feedback.hideLoading()},e.jump.pullAppPay(e.$vhFrom,Object.assign({},e.orderInfo,{$payment_method:e.type,$from:3})),i.abrupt("return");case 8:i.t0=n,i.next=i.t0===d.WX?11:i.t0===d.ALI?14:17;break;case 11:return i.next=13,e.wxPay();case 13:return i.abrupt("break",17);case 14:return i.next=16,e.aliPay();case 16:return i.abrupt("break",17);case 17:return i.prev=17,e.$app||(e.type=e.$options.data().type),i.finish(17);case 20:case"end":return i.stop()}}),i,null,[[1,,17,20]])})))()},wxH5WxPay:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i,n,a,o,s;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i={pageFullPath:t.pageFullPath,vhType:3},n=Object.keys(i).map((function(t){return"".concat(t,"=").concat(i[t])})).join("&"),a="".concat(location.origin).concat(t.routeTable.pPaySuccessJump,"?").concat(n),o={main_order_no:t.mainOrderNo,payment_method:2,order_type:2,is_cross:0,return_url:a},e.next=6,t.$u.api.payMethod(o);case 6:s=e.sent,location.href=s.data.h5_pay_info;case 8:case"end":return e.stop()}}),e)})))()},h5WxPay:function(){var t=uni.getStorageSync("loginInfo")||{},e=t.token,i=t.uid,n={main_order_no:this.mainOrderNo,payment_method:4,order_type:1,is_cross:0,token:e,uid:i,from:5},r=Object.keys(n).map((function(t){return"".concat(t,"=").concat(n[t])})).join("&"),a="".concat(this.h5WxPayOrigin,"?").concat(r),o=window.open("/");o?o.location=a:location.href=a},h5AliPay:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i,n,a,o,s;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i=window.open("/"),n="".concat(location.origin).concat(t.pageFullPath),a={main_order_no:t.mainOrderNo,payment_method:1,order_type:2,is_cross:0,return_url:n},e.next=5,t.$u.api.payMethod(a);case 5:o=e.sent,s=o.data.h5_pay_info,i?i.location=s:location.href=s;case 8:case"end":return e.stop()}}),e)})))()},aliPay:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i,n,a,o,s,f;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i=t.orderInfo.$paySuccessReturnUrl,n=void 0===i?"":i,a=n||"".concat(t.pageFullPath).concat(t.pageFullPath.includes("?")?"&":"?","aePaySuccess=1"),o={source:c.MAppPaymentSource.AuctionEarnest,main_order_no:t.mainOrderNo,payment_method:c.MPaymentMethod.AliH5,return_url:"".concat(location.origin).concat(a)},e.prev=3,e.next=6,t.$u.api.appPayment(o);case 6:f=e.sent,t.feedback.loading({title:"支付中..."}),t.ailPayForm=(null===f||void 0===f||null===(s=f.data)||void 0===s?void 0:s.pay_info)||"",t.$nextTick((function(){var e,i;null===(e=document)||void 0===e||null===(i=e.forms["alipay_submit"])||void 0===i||i.submit(),t.feedback.hideLoading()})),e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](3),t.$emit("pullPayFail");case 15:case"end":return e.stop()}}),e,null,[[3,12]])})))()},wxPay:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i,n,a,o,s,f,d,h;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.loadWxPayOpenId();case 2:return i={source:c.MAppPaymentSource.AuctionEarnest,main_order_no:t.mainOrderNo,payment_method:c.MPaymentMethod.WxJSAPI,open_id:t.wxPayOpenId},e.prev=3,e.next=6,t.$u.api.appPayment(i);case 6:a=e.sent,o=(null===a||void 0===a||null===(n=a.data)||void 0===n?void 0:n.pay_info)||{},s=o.timeStamp,f=o.nonceStr,d=o.signType,h=o.paySign,u.default.chooseWXPay({timestamp:s,nonceStr:f,package:o.package,signType:d,paySign:h}),e.next=15;break;case 12:e.prev=12,e.t0=e["catch"](3),t.$emit("pullPayFail");case 15:case"end":return e.stop()}}),e,null,[[3,12]])})))()},queryPayStatus:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i,n,a,o;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.feedback.hideLoading(),e.next=3,t.$u.api.getAuctionEarnestOrderDetail({main_order_no:t.mainOrderNo});case 3:return i=e.sent,n=(null===i||void 0===i?void 0:i.data)||{},a=n.status,o=void 0===a?0:a,[1,2,3,5].includes(o)?t.$emit("paySuccess"):t.$emit("payFail"),e.abrupt("return",i);case 7:case"end":return e.stop()}}),e)})))()},loadWxPayOpenId:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i,n,a,o,s;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,!t.wxPayOpenId){e.next=3;break}return e.abrupt("return");case 3:return e.next=5,t.$u.api.getWxPayOpenIdByCode({code:t.code,genre:1});case 5:n=e.sent,t.wxPayOpenId=(null===n||void 0===n||null===(i=n.data)||void 0===i?void 0:i.openid)||"",e.next=15;break;case 9:e.prev=9,e.t0=e["catch"](0),a=t.pages.getCurrenPage().$page.options,o=Object.keys(a).filter((function(t){return"code"!==t})).map((function(t){return"".concat(t,"=").concat(a[t])})).join("&"),s="".concat(location.pathname).concat(o?"?".concat(o):""),location.href=s;case 15:case"end":return e.stop()}}),e,null,[[0,9]])})))()},wxConfig:function(){var t=this,e=window.location.href.split("#")[0];this.$isDev&&(e="https://activity.vinehoo.com/activities-v3/WechatCode"),this.$u.api.getJsapiSign({url:e,appid:f.WX_APPID_PROD}).then((function(e){var i=e||{},n=i.appid,r=i.noncestr,a=i.sign,o=i.timestamp,s={debug:t.$isDev,appId:n,nonceStr:r,signature:a,timestamp:o,jsApiList:["chooseWXPay"]};console.log("configData",s),u.default.config(s)}))}},created:function(){var t=window.navigator.userAgent.toLowerCase();if(this.isWxProcess=/micromessenger/.test(t),this.isWxProcess){var e=this.pages.getCurrenPage().$page.options.code;if(this.$isDev){if(!e)return void(location.href="https://activity.vinehoo.com/activities-v3/RedirectFetchWxCode?appid=".concat(f.WX_APPID_PROD,"&redirectUrl=").concat(encodeURIComponent(window.location.href.split("#")[0])));this.code=e}else{if(!e)return void(location.href="https://open.weixin.qq.com/connect/oauth2/authorize?appid="+f.WX_APPID_PROD+"&redirect_uri="+encodeURIComponent(window.location.href)+"&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect");this.code=e}this.wxConfig()}}};e.default=h},"0eb9":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uPopup:i("c4b0").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-popup",{attrs:{value:t.value,mode:"bottom",width:"100%",height:"548","border-radius":"20"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"d-flex j-center pt-98"},[i("v-uni-image",{staticClass:"w-266 h-184",attrs:{src:t.ossIcon("/auction/icon_pay_success.png")}})],1),i("v-uni-view",{staticClass:"mt-16 font-32 text-3 text-center"},[t._v("支付成功")]),i("v-uni-button",{staticClass:"vh-btn flex-c-c mtb-00-mlr-auto mt-96 w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onConfirm.apply(void 0,arguments)}}},[t._v("确认")])],1)},a=[]},"0f2c":function(t,e,i){var n=i("2aee"),r=i("f460"),a=i("83d5"),o=i("399f"),s=i("a958"),c=i("98e6"),f=i("5291"),u=i("8707").Buffer;t.exports=function(t,e,i){var d;d=t.padding?t.padding:i?1:4;var h,l=n(t),p=l.modulus.byteLength();if(e.length>p||new o(e).cmp(l.modulus)>=0)throw new Error("decryption error");h=i?f(new o(e),l):s(e,l);var b=u.alloc(p-h.length);if(h=u.concat([b,h],p),4===d)return function(t,e){var i=t.modulus.byteLength(),n=c("sha1").update(u.alloc(0)).digest(),o=n.length;if(0!==e[0])throw new Error("decryption error");var s=e.slice(1,o+1),f=e.slice(o+1),d=a(s,r(f,o)),h=a(f,r(d,i-o-1));if(function(t,e){t=u.from(t),e=u.from(e);var i=0,n=t.length;t.length!==e.length&&(i++,n=Math.min(t.length,e.length));var r=-1;while(++r<n)i+=t[r]^e[r];return i}(n,h.slice(0,o)))throw new Error("decryption error");var l=o;while(0===h[l])l++;if(1!==h[l++])throw new Error("decryption error");return h.slice(l)}(l,h);if(1===d)return function(t,e,i){var n=e.slice(0,2),r=2,a=0;while(0!==e[r++])if(r>=e.length){a++;break}var o=e.slice(2,r-1);("0002"!==n.toString("hex")&&!i||"0001"!==n.toString("hex")&&i)&&a++;o.length<8&&a++;if(a)throw new Error("decryption error");return e.slice(r)}(0,h,i);if(3===d)return h;throw new Error("unknown padding")}},1:function(t,e){},1007:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.lv-text[data-v-8ea5eff0]{line-height:%?32?%;display:inline-block}',""]),t.exports=e},"10eb":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},i("d9e2"),i("d401")},"116d":function(t,e,i){t.exports=i("b4e8")},"11dc":function(t,e,i){"use strict";(function(e,n){var r=i("8707").Buffer,a=e.crypto||e.msCrypto;a&&a.getRandomValues?t.exports=function(t,e){if(t>4294967295)throw new RangeError("requested too many random bytes");var i=r.allocUnsafe(t);if(t>0)if(t>65536)for(var o=0;o<t;o+=65536)a.getRandomValues(i.slice(o,o+65536));else a.getRandomValues(i);if("function"===typeof e)return n.nextTick((function(){e(null,i)}));return i}:t.exports=function(){throw new Error("Secure random number generation is not supported by this browser.\nUse Chrome, Firefox or Internet Explorer 11")}}).call(this,i("c8ba"),i("4362"))},1251:function(t,e,i){"use strict";i.r(e);var n=i("f99f"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"13e2":function(t,e,i){"use strict";var n=i("c3c0"),r=i("edc9"),a=i("aa56"),o=n.rotl32,s=n.sum32,c=n.sum32_5,f=a.ft_1,u=r.BlockHash,d=[1518500249,1859775393,2400959708,3395469782];function h(){if(!(this instanceof h))return new h;u.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.W=new Array(80)}n.inherits(h,u),t.exports=h,h.blockSize=512,h.outSize=160,h.hmacStrength=80,h.padLength=64,h.prototype._update=function(t,e){for(var i=this.W,n=0;n<16;n++)i[n]=t[e+n];for(;n<i.length;n++)i[n]=o(i[n-3]^i[n-8]^i[n-14]^i[n-16],1);var r=this.h[0],a=this.h[1],u=this.h[2],h=this.h[3],l=this.h[4];for(n=0;n<i.length;n++){var p=~~(n/20),b=c(o(r,5),f(p,a,u,h),l,i[n],d[p]);l=h,h=u,u=o(a,30),a=r,r=b}this.h[0]=s(this.h[0],r),this.h[1]=s(this.h[1],a),this.h[2]=s(this.h[2],u),this.h[3]=s(this.h[3],h),this.h[4]=s(this.h[4],l)},h.prototype._digest=function(t){return"hex"===t?n.toHex32(this.h,"big"):n.split32(this.h,"big")}},1435:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uPopup:i("c4b0").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-popup",{attrs:{value:t.value,mode:"center",width:"552rpx",height:"410rpx","border-radius":"20"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"p-rela wh-p100"},[i("v-uni-image",{staticClass:"p-abso wh-p100",attrs:{src:t.ossIcon("/auction/popup_bg_552_414.png")}}),i("v-uni-view",{staticClass:"p-rela pt-84 pb-20 h-p100"},[i("v-uni-view",{staticClass:"font-wei-500 font-32 text-3 text-center"},[t._v("运费说明")]),i("v-uni-view",{staticClass:"mt-24 ptb-00-plr-84 font-26 text-3 l-h-36 text-center"},[t._v("此拍品为包邮拍品，运费将由卖家承担。")]),i("v-uni-view",{staticClass:"p-abso bottom-20 w-p100",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput(!1)}}},[i("v-uni-view",{staticClass:"w-p100 h-01 bg-dedede"}),i("v-uni-view",{staticClass:"flex-c-c h-104 font-wei-500 font-28 text-e80404"},[t._v("知道了")])],1)],1)],1)],1)},a=[]},1545:function(t,e,i){"use strict";e.utils=i("5ee7"),e.Cipher=i("0184"),e.DES=i("4e2b"),e.CBC=i("0da4"),e.EDE=i("1fec")},"1a2a":function(t,e,i){"use strict";var n=i("3fb5"),r=i("d424"),a=i("6430"),o=i("8707").Buffer,s=i("5a76"),c=i("b5ca"),f=i("69f2"),u=o.alloc(128);function d(t,e){a.call(this,"digest"),"string"===typeof e&&(e=o.from(e));var i="sha512"===t||"sha384"===t?128:64;if(this._alg=t,this._key=e,e.length>i){var n="rmd160"===t?new c:f(t);e=n.update(e).digest()}else e.length<i&&(e=o.concat([e,u],i));for(var r=this._ipad=o.allocUnsafe(i),s=this._opad=o.allocUnsafe(i),d=0;d<i;d++)r[d]=54^e[d],s[d]=92^e[d];this._hash="rmd160"===t?new c:f(t),this._hash.update(r)}n(d,a),d.prototype._update=function(t){this._hash.update(t)},d.prototype._final=function(){var t=this._hash.digest(),e="rmd160"===this._alg?new c:f(this._alg);return e.update(this._opad).update(t).digest()},t.exports=function(t,e){return t=t.toLowerCase(),"rmd160"===t||"ripemd160"===t?new d("rmd160",e):"md5"===t?new r(s,e):new d(t,e)}},"1c46":function(t,e,i){"use strict";e.randomBytes=e.rng=e.pseudoRandomBytes=e.prng=i("11dc"),e.createHash=e.Hash=i("98e6"),e.createHmac=e.Hmac=i("1a2a");var n=i("116d"),r=Object.keys(n),a=["sha1","sha224","sha256","sha384","sha512","md5","rmd160"].concat(r);e.getHashes=function(){return a};var o=i("a099");e.pbkdf2=o.pbkdf2,e.pbkdf2Sync=o.pbkdf2Sync;var s=i("956a");e.Cipher=s.Cipher,e.createCipher=s.createCipher,e.Cipheriv=s.Cipheriv,e.createCipheriv=s.createCipheriv,e.Decipher=s.Decipher,e.createDecipher=s.createDecipher,e.Decipheriv=s.Decipheriv,e.createDecipheriv=s.createDecipheriv,e.getCiphers=s.getCiphers,e.listCiphers=s.listCiphers;var c=i("00dc");e.DiffieHellmanGroup=c.DiffieHellmanGroup,e.createDiffieHellmanGroup=c.createDiffieHellmanGroup,e.getDiffieHellman=c.getDiffieHellman,e.createDiffieHellman=c.createDiffieHellman,e.DiffieHellman=c.DiffieHellman;var f=i("b692");e.createSign=f.createSign,e.Sign=f.Sign,e.createVerify=f.createVerify,e.Verify=f.Verify,e.createECDH=i("e1d3");var u=i("6442");e.publicEncrypt=u.publicEncrypt,e.privateEncrypt=u.privateEncrypt,e.publicDecrypt=u.publicDecrypt,e.privateDecrypt=u.privateDecrypt;var d=i("75cc");e.randomFill=d.randomFill,e.randomFillSync=d.randomFillSync,e.createCredentials=function(){throw new Error(["sorry, createCredentials is not implemented yet","we accept pull requests","https://github.com/crypto-browserify/crypto-browserify"].join("\n"))},e.constants={DH_CHECK_P_NOT_SAFE_PRIME:2,DH_CHECK_P_NOT_PRIME:1,DH_UNABLE_TO_CHECK_GENERATOR:4,DH_NOT_SUITABLE_GENERATOR:8,NPN_ENABLED:1,ALPN_ENABLED:1,RSA_PKCS1_PADDING:1,RSA_SSLV23_PADDING:2,RSA_NO_PADDING:3,RSA_PKCS1_OAEP_PADDING:4,RSA_X931_PADDING:5,RSA_PKCS1_PSS_PADDING:6,POINT_CONVERSION_COMPRESSED:2,POINT_CONVERSION_UNCOMPRESSED:4,POINT_CONVERSION_HYBRID:6}},"1e3c":function(t,e,i){var n=i("6430"),r=i("1545"),a=i("3fb5"),o=i("8707").Buffer,s={"des-ede3-cbc":r.CBC.instantiate(r.EDE),"des-ede3":r.EDE,"des-ede-cbc":r.CBC.instantiate(r.EDE),"des-ede":r.EDE,"des-cbc":r.CBC.instantiate(r.DES),"des-ecb":r.DES};function c(t){n.call(this);var e,i=t.mode.toLowerCase(),r=s[i];e=t.decrypt?"decrypt":"encrypt";var a=t.key;o.isBuffer(a)||(a=o.from(a)),"des-ede"!==i&&"des-ede-cbc"!==i||(a=o.concat([a,a.slice(0,8)]));var c=t.iv;o.isBuffer(c)||(c=o.from(c)),this._des=r.create({key:a,iv:c,type:e})}s.des=s["des-cbc"],s.des3=s["des-ede3-cbc"],t.exports=c,a(c,n),c.prototype._update=function(t){return o.from(this._des.update(t))},c.prototype._final=function(){return o.from(this._des.final())}},"1fb5":function(t,e,i){"use strict";e.byteLength=function(t){var e=f(t),i=e[0],n=e[1];return 3*(i+n)/4-n},e.toByteArray=function(t){var e,i,n=f(t),o=n[0],s=n[1],c=new a(function(t,e,i){return 3*(e+i)/4-i}(0,o,s)),u=0,d=s>0?o-4:o;for(i=0;i<d;i+=4)e=r[t.charCodeAt(i)]<<18|r[t.charCodeAt(i+1)]<<12|r[t.charCodeAt(i+2)]<<6|r[t.charCodeAt(i+3)],c[u++]=e>>16&255,c[u++]=e>>8&255,c[u++]=255&e;2===s&&(e=r[t.charCodeAt(i)]<<2|r[t.charCodeAt(i+1)]>>4,c[u++]=255&e);1===s&&(e=r[t.charCodeAt(i)]<<10|r[t.charCodeAt(i+1)]<<4|r[t.charCodeAt(i+2)]>>2,c[u++]=e>>8&255,c[u++]=255&e);return c},e.fromByteArray=function(t){for(var e,i=t.length,r=i%3,a=[],o=0,s=i-r;o<s;o+=16383)a.push(d(t,o,o+16383>s?s:o+16383));1===r?(e=t[i-1],a.push(n[e>>2]+n[e<<4&63]+"==")):2===r&&(e=(t[i-2]<<8)+t[i-1],a.push(n[e>>10]+n[e>>4&63]+n[e<<2&63]+"="));return a.join("")};for(var n=[],r=[],a="undefined"!==typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,c=o.length;s<c;++s)n[s]=o[s],r[o.charCodeAt(s)]=s;function f(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var i=t.indexOf("=");-1===i&&(i=e);var n=i===e?0:4-i%4;return[i,n]}function u(t){return n[t>>18&63]+n[t>>12&63]+n[t>>6&63]+n[63&t]}function d(t,e,i){for(var n,r=[],a=e;a<i;a+=3)n=(t[a]<<16&16711680)+(t[a+1]<<8&65280)+(255&t[a+2]),r.push(u(n));return r.join("")}r["-".charCodeAt(0)]=62,r["_".charCodeAt(0)]=63},"1fec":function(t,e,i){"use strict";var n=i("da3e"),r=i("3fb5"),a=i("0184"),o=i("4e2b");function s(t,e){n.equal(e.length,24,"Invalid key length");var i=e.slice(0,8),r=e.slice(8,16),a=e.slice(16,24);this.ciphers="encrypt"===t?[o.create({type:"encrypt",key:i}),o.create({type:"decrypt",key:r}),o.create({type:"encrypt",key:a})]:[o.create({type:"decrypt",key:a}),o.create({type:"encrypt",key:r}),o.create({type:"decrypt",key:i})]}function c(t){a.call(this,t);var e=new s(this.type,this.options.key);this._edeState=e}r(c,a),t.exports=c,c.create=function(t){return new c(t)},c.prototype._update=function(t,e,i,n){var r=this._edeState;r.ciphers[0]._update(t,e,i,n),r.ciphers[1]._update(i,n,i,n),r.ciphers[2]._update(i,n,i,n)},c.prototype._pad=o.prototype._pad,c.prototype._unpad=o.prototype._unpad},2:function(t,e){},2036:function(t,e,i){"use strict";i.r(e);var n=i("d4f3"),r=i("28ff");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"0c73edd7",null,!1,n["a"],void 0);e["default"]=s.exports},"206d":function(t,e,i){(function(e){var n,r,a=i("8707").Buffer,o=i("7d2a"),s=i("9f9d"),c=i("e07b"),f=i("8be6"),u=e.crypto&&e.crypto.subtle,d={sha:"SHA-1","sha-1":"SHA-1",sha1:"SHA-1",sha256:"SHA-256","sha-256":"SHA-256",sha384:"SHA-384","sha-384":"SHA-384","sha-512":"SHA-512",sha512:"SHA-512"},h=[];function l(){return r||(r=e.process&&e.process.nextTick?e.process.nextTick:e.queueMicrotask?e.queueMicrotask:e.setImmediate?e.setImmediate:e.setTimeout,r)}function p(t,e,i,n,r){return u.importKey("raw",t,{name:"PBKDF2"},!1,["deriveBits"]).then((function(t){return u.deriveBits({name:"PBKDF2",salt:e,iterations:i,hash:{name:r}},t,n<<3)})).then((function(t){return a.from(t)}))}t.exports=function(t,i,r,b,v,m){"function"===typeof v&&(m=v,v=void 0),v=v||"sha1";var g=d[v.toLowerCase()];if(g&&"function"===typeof e.Promise){if(o(r,b),t=f(t,s,"Password"),i=f(i,s,"Salt"),"function"!==typeof m)throw new Error("No callback provided to pbkdf2");(function(t,e){t.then((function(t){l()((function(){e(null,t)}))}),(function(t){l()((function(){e(t)}))}))})(function(t){if(e.process&&!e.process.browser)return Promise.resolve(!1);if(!u||!u.importKey||!u.deriveBits)return Promise.resolve(!1);if(void 0!==h[t])return h[t];n=n||a.alloc(8);var i=p(n,n,10,128,t).then((function(){return!0})).catch((function(){return!1}));return h[t]=i,i}(g).then((function(e){return e?p(t,i,r,b,g):c(t,i,r,b,v)})),m)}else l()((function(){var e;try{e=c(t,i,r,b,v)}catch(n){return m(n)}m(null,e)}))}}).call(this,i("c8ba"))},"20f6":function(t,e,i){"use strict";const n=e;n.der=i("cfbd"),n.pem=i("8df7")},2137:function(t,e,i){"use strict";var n=i("c3c0"),r=i("da3e");function a(t,e,i){if(!(this instanceof a))return new a(t,e,i);this.Hash=t,this.blockSize=t.blockSize/8,this.outSize=t.outSize/8,this.inner=null,this.outer=null,this._init(n.toArray(e,i))}t.exports=a,a.prototype._init=function(t){t.length>this.blockSize&&(t=(new this.Hash).update(t).digest()),r(t.length<=this.blockSize);for(var e=t.length;e<this.blockSize;e++)t.push(0);for(e=0;e<t.length;e++)t[e]^=54;for(this.inner=(new this.Hash).update(t),e=0;e<t.length;e++)t[e]^=106;this.outer=(new this.Hash).update(t)},a.prototype.update=function(t,e){return this.inner.update(t,e),this},a.prototype.digest=function(t){return this.outer.update(this.inner.digest()),this.outer.digest(t)}},"22ad":function(t,e,i){"use strict";var n=i("bfb8"),r=i.n(n);r.a},"23df":function(t,e,i){"use strict";i.r(e);var n=i("0eb9"),r=i("d57a");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"3afd19c5",null,!1,n["a"],void 0);e["default"]=s.exports},"27bf":function(t,e,i){"use strict";t.exports=o;var n=i("b19a"),r=Object.create(i("3a7c"));function a(t,e){var i=this._transformState;i.transforming=!1;var n=i.writecb;if(!n)return this.emit("error",new Error("write callback called multiple times"));i.writechunk=null,i.writecb=null,null!=e&&this.push(e),n(t);var r=this._readableState;r.reading=!1,(r.needReadable||r.length<r.highWaterMark)&&this._read(r.highWaterMark)}function o(t){if(!(this instanceof o))return new o(t);n.call(this,t),this._transformState={afterTransform:a.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"===typeof t.transform&&(this._transform=t.transform),"function"===typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",s)}function s(){var t=this;"function"===typeof this._flush?this._flush((function(e,i){c(t,e,i)})):c(this,null,null)}function c(t,e,i){if(e)return t.emit("error",e);if(null!=i&&t.push(i),t._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(t._transformState.transforming)throw new Error("Calling transform done when still transforming");return t.push(null)}r.inherits=i("3fb5"),r.inherits(o,n),o.prototype.push=function(t,e){return this._transformState.needTransform=!1,n.prototype.push.call(this,t,e)},o.prototype._transform=function(t,e,i){throw new Error("_transform() is not implemented")},o.prototype._write=function(t,e,i){var n=this._transformState;if(n.writecb=i,n.writechunk=t,n.writeencoding=e,!n.transforming){var r=this._readableState;(n.needTransform||r.needReadable||r.length<r.highWaterMark)&&this._read(r.highWaterMark)}},o.prototype._read=function(t){var e=this._transformState;null!==e.writechunk&&e.writecb&&!e.transforming?(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform)):e.needTransform=!0},o.prototype._destroy=function(t,e){var i=this;n.prototype._destroy.call(this,t,(function(t){e(t),i.emit("close")}))}},2801:function(t){t.exports=JSON.parse('{"name":"elliptic","version":"6.5.4","description":"EC cryptography","main":"lib/elliptic.js","files":["lib"],"scripts":{"lint":"eslint lib test","lint:fix":"npm run lint -- --fix","unit":"istanbul test _mocha --reporter=spec test/index.js","test":"npm run lint && npm run unit","version":"grunt dist && git add dist/"},"repository":{"type":"git","url":"**************:indutny/elliptic"},"keywords":["EC","Elliptic","curve","Cryptography"],"author":"Fedor Indutny <<EMAIL>>","license":"MIT","bugs":{"url":"https://github.com/indutny/elliptic/issues"},"homepage":"https://github.com/indutny/elliptic","devDependencies":{"brfs":"^2.0.2","coveralls":"^3.1.0","eslint":"^7.6.0","grunt":"^1.2.1","grunt-browserify":"^5.3.0","grunt-cli":"^1.3.2","grunt-contrib-connect":"^3.0.0","grunt-contrib-copy":"^1.0.0","grunt-contrib-uglify":"^5.0.0","grunt-mocha-istanbul":"^5.0.2","grunt-saucelabs":"^9.0.1","istanbul":"^0.4.5","mocha":"^8.0.1"},"dependencies":{"bn.js":"^4.11.9","brorand":"^1.1.0","hash.js":"^1.0.0","hmac-drbg":"^1.0.1","inherits":"^2.0.4","minimalistic-assert":"^1.0.1","minimalistic-crypto-utils":"^1.0.1"}}')},"28ff":function(t,e,i){"use strict";i.r(e);var n=i("0e01"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"2aee":function(t,e,i){var n=i("4111"),r=i("d70e"),a=i("4dd0"),o=i("fda6"),s=i("a099"),c=i("8707").Buffer;function f(t){var e;"object"!==typeof t||c.isBuffer(t)||(e=t.passphrase,t=t.key),"string"===typeof t&&(t=c.from(t));var i,f,u=a(t,e),d=u.tag,h=u.data;switch(d){case"CERTIFICATE":f=n.certificate.decode(h,"der").tbsCertificate.subjectPublicKeyInfo;case"PUBLIC KEY":switch(f||(f=n.PublicKey.decode(h,"der")),i=f.algorithm.algorithm.join("."),i){case"1.2.840.113549.1.1.1":return n.RSAPublicKey.decode(f.subjectPublicKey.data,"der");case"1.2.840.10045.2.1":return f.subjectPrivateKey=f.subjectPublicKey,{type:"ec",data:f};case"1.2.840.10040.4.1":return f.algorithm.params.pub_key=n.DSAparam.decode(f.subjectPublicKey.data,"der"),{type:"dsa",data:f.algorithm.params};default:throw new Error("unknown key id "+i)}case"ENCRYPTED PRIVATE KEY":h=n.EncryptedPrivateKey.decode(h,"der"),h=function(t,e){var i=t.algorithm.decrypt.kde.kdeparams.salt,n=parseInt(t.algorithm.decrypt.kde.kdeparams.iters.toString(),10),a=r[t.algorithm.decrypt.cipher.algo.join(".")],f=t.algorithm.decrypt.cipher.iv,u=t.subjectPrivateKey,d=parseInt(a.split("-")[1],10)/8,h=s.pbkdf2Sync(e,i,n,d,"sha1"),l=o.createDecipheriv(a,h,f),p=[];return p.push(l.update(u)),p.push(l.final()),c.concat(p)}(h,e);case"PRIVATE KEY":switch(f=n.PrivateKey.decode(h,"der"),i=f.algorithm.algorithm.join("."),i){case"1.2.840.113549.1.1.1":return n.RSAPrivateKey.decode(f.subjectPrivateKey,"der");case"1.2.840.10045.2.1":return{curve:f.algorithm.curve,privateKey:n.ECPrivateKey.decode(f.subjectPrivateKey,"der").privateKey};case"1.2.840.10040.4.1":return f.algorithm.params.priv_key=n.DSAparam.decode(f.subjectPrivateKey,"der"),{type:"dsa",params:f.algorithm.params};default:throw new Error("unknown key id "+i)}case"RSA PUBLIC KEY":return n.RSAPublicKey.decode(h,"der");case"RSA PRIVATE KEY":return n.RSAPrivateKey.decode(h,"der");case"DSA PRIVATE KEY":return{type:"dsa",params:n.DSAPrivateKey.decode(h,"der")};case"EC PRIVATE KEY":return h=n.ECPrivateKey.decode(h,"der"),{curve:h.parameters.value,privateKey:h.privateKey};default:throw new Error("unknown key type "+d)}}t.exports=f,f.signature=n.signature},"2af0":function(t,e,i){"use strict";i.r(e);var n=i("0e30"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"2b7a":function(t,e,i){"use strict";i.r(e);var n=i("e12f"),r=i("6703");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"515daa9a",null,!1,n["a"],void 0);e["default"]=s.exports},"2b9b":function(t,e,i){"use strict";i.r(e);var n=i("9bfd"),r=i("9c98");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);i("5a44");var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"59f2b89f",null,!1,n["a"],void 0);e["default"]=s.exports},"2c63":function(t,e,i){t.exports=i("dc14")},"2c89":function(t,e,i){"use strict";i.r(e);var n=i("d97b"),r=i("b2cf");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);i("8d13");var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"5189efcc",null,!1,n["a"],void 0);e["default"]=s.exports},3:function(t,e){},3300:function(t,e,i){"use strict";var n=i("f3a3"),r=i("399f"),a=i("3fb5"),o=i("ea537"),s=n.assert;function c(t){o.call(this,"short",t),this.a=new r(t.a,16).toRed(this.red),this.b=new r(t.b,16).toRed(this.red),this.tinv=this.two.redInvm(),this.zeroA=0===this.a.fromRed().cmpn(0),this.threeA=0===this.a.fromRed().sub(this.p).cmpn(-3),this.endo=this._getEndomorphism(t),this._endoWnafT1=new Array(4),this._endoWnafT2=new Array(4)}function f(t,e,i,n){o.BasePoint.call(this,t,"affine"),null===e&&null===i?(this.x=null,this.y=null,this.inf=!0):(this.x=new r(e,16),this.y=new r(i,16),n&&(this.x.forceRed(this.curve.red),this.y.forceRed(this.curve.red)),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.inf=!1)}function u(t,e,i,n){o.BasePoint.call(this,t,"jacobian"),null===e&&null===i&&null===n?(this.x=this.curve.one,this.y=this.curve.one,this.z=new r(0)):(this.x=new r(e,16),this.y=new r(i,16),this.z=new r(n,16)),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.z.red||(this.z=this.z.toRed(this.curve.red)),this.zOne=this.z===this.curve.one}a(c,o),t.exports=c,c.prototype._getEndomorphism=function(t){if(this.zeroA&&this.g&&this.n&&1===this.p.modn(3)){var e,i,n;if(t.beta)e=new r(t.beta,16).toRed(this.red);else{var a=this._getEndoRoots(this.p);e=a[0].cmp(a[1])<0?a[0]:a[1],e=e.toRed(this.red)}if(t.lambda)i=new r(t.lambda,16);else{var o=this._getEndoRoots(this.n);0===this.g.mul(o[0]).x.cmp(this.g.x.redMul(e))?i=o[0]:(i=o[1],s(0===this.g.mul(i).x.cmp(this.g.x.redMul(e))))}return n=t.basis?t.basis.map((function(t){return{a:new r(t.a,16),b:new r(t.b,16)}})):this._getEndoBasis(i),{beta:e,lambda:i,basis:n}}},c.prototype._getEndoRoots=function(t){var e=t===this.p?this.red:r.mont(t),i=new r(2).toRed(e).redInvm(),n=i.redNeg(),a=new r(3).toRed(e).redNeg().redSqrt().redMul(i),o=n.redAdd(a).fromRed(),s=n.redSub(a).fromRed();return[o,s]},c.prototype._getEndoBasis=function(t){var e,i,n,a,o,s,c,f,u,d=this.n.ushrn(Math.floor(this.n.bitLength()/2)),h=t,l=this.n.clone(),p=new r(1),b=new r(0),v=new r(0),m=new r(1),g=0;while(0!==h.cmpn(0)){var y=l.div(h);f=l.sub(y.mul(h)),u=v.sub(y.mul(p));var w=m.sub(y.mul(b));if(!n&&f.cmp(d)<0)e=c.neg(),i=p,n=f.neg(),a=u;else if(n&&2===++g)break;c=f,l=h,h=f,v=p,p=u,m=b,b=w}o=f.neg(),s=u;var _=n.sqr().add(a.sqr()),x=o.sqr().add(s.sqr());return x.cmp(_)>=0&&(o=e,s=i),n.negative&&(n=n.neg(),a=a.neg()),o.negative&&(o=o.neg(),s=s.neg()),[{a:n,b:a},{a:o,b:s}]},c.prototype._endoSplit=function(t){var e=this.endo.basis,i=e[0],n=e[1],r=n.b.mul(t).divRound(this.n),a=i.b.neg().mul(t).divRound(this.n),o=r.mul(i.a),s=a.mul(n.a),c=r.mul(i.b),f=a.mul(n.b),u=t.sub(o).sub(s),d=c.add(f).neg();return{k1:u,k2:d}},c.prototype.pointFromX=function(t,e){t=new r(t,16),t.red||(t=t.toRed(this.red));var i=t.redSqr().redMul(t).redIAdd(t.redMul(this.a)).redIAdd(this.b),n=i.redSqrt();if(0!==n.redSqr().redSub(i).cmp(this.zero))throw new Error("invalid point");var a=n.fromRed().isOdd();return(e&&!a||!e&&a)&&(n=n.redNeg()),this.point(t,n)},c.prototype.validate=function(t){if(t.inf)return!0;var e=t.x,i=t.y,n=this.a.redMul(e),r=e.redSqr().redMul(e).redIAdd(n).redIAdd(this.b);return 0===i.redSqr().redISub(r).cmpn(0)},c.prototype._endoWnafMulAdd=function(t,e,i){for(var n=this._endoWnafT1,r=this._endoWnafT2,a=0;a<t.length;a++){var o=this._endoSplit(e[a]),s=t[a],c=s._getBeta();o.k1.negative&&(o.k1.ineg(),s=s.neg(!0)),o.k2.negative&&(o.k2.ineg(),c=c.neg(!0)),n[2*a]=s,n[2*a+1]=c,r[2*a]=o.k1,r[2*a+1]=o.k2}for(var f=this._wnafMulAdd(1,n,r,2*a,i),u=0;u<2*a;u++)n[u]=null,r[u]=null;return f},a(f,o.BasePoint),c.prototype.point=function(t,e,i){return new f(this,t,e,i)},c.prototype.pointFromJSON=function(t,e){return f.fromJSON(this,t,e)},f.prototype._getBeta=function(){if(this.curve.endo){var t=this.precomputed;if(t&&t.beta)return t.beta;var e=this.curve.point(this.x.redMul(this.curve.endo.beta),this.y);if(t){var i=this.curve,n=function(t){return i.point(t.x.redMul(i.endo.beta),t.y)};t.beta=e,e.precomputed={beta:null,naf:t.naf&&{wnd:t.naf.wnd,points:t.naf.points.map(n)},doubles:t.doubles&&{step:t.doubles.step,points:t.doubles.points.map(n)}}}return e}},f.prototype.toJSON=function(){return this.precomputed?[this.x,this.y,this.precomputed&&{doubles:this.precomputed.doubles&&{step:this.precomputed.doubles.step,points:this.precomputed.doubles.points.slice(1)},naf:this.precomputed.naf&&{wnd:this.precomputed.naf.wnd,points:this.precomputed.naf.points.slice(1)}}]:[this.x,this.y]},f.fromJSON=function(t,e,i){"string"===typeof e&&(e=JSON.parse(e));var n=t.point(e[0],e[1],i);if(!e[2])return n;function r(e){return t.point(e[0],e[1],i)}var a=e[2];return n.precomputed={beta:null,doubles:a.doubles&&{step:a.doubles.step,points:[n].concat(a.doubles.points.map(r))},naf:a.naf&&{wnd:a.naf.wnd,points:[n].concat(a.naf.points.map(r))}},n},f.prototype.inspect=function(){return this.isInfinity()?"<EC Point Infinity>":"<EC Point x: "+this.x.fromRed().toString(16,2)+" y: "+this.y.fromRed().toString(16,2)+">"},f.prototype.isInfinity=function(){return this.inf},f.prototype.add=function(t){if(this.inf)return t;if(t.inf)return this;if(this.eq(t))return this.dbl();if(this.neg().eq(t))return this.curve.point(null,null);if(0===this.x.cmp(t.x))return this.curve.point(null,null);var e=this.y.redSub(t.y);0!==e.cmpn(0)&&(e=e.redMul(this.x.redSub(t.x).redInvm()));var i=e.redSqr().redISub(this.x).redISub(t.x),n=e.redMul(this.x.redSub(i)).redISub(this.y);return this.curve.point(i,n)},f.prototype.dbl=function(){if(this.inf)return this;var t=this.y.redAdd(this.y);if(0===t.cmpn(0))return this.curve.point(null,null);var e=this.curve.a,i=this.x.redSqr(),n=t.redInvm(),r=i.redAdd(i).redIAdd(i).redIAdd(e).redMul(n),a=r.redSqr().redISub(this.x.redAdd(this.x)),o=r.redMul(this.x.redSub(a)).redISub(this.y);return this.curve.point(a,o)},f.prototype.getX=function(){return this.x.fromRed()},f.prototype.getY=function(){return this.y.fromRed()},f.prototype.mul=function(t){return t=new r(t,16),this.isInfinity()?this:this._hasDoubles(t)?this.curve._fixedNafMul(this,t):this.curve.endo?this.curve._endoWnafMulAdd([this],[t]):this.curve._wnafMul(this,t)},f.prototype.mulAdd=function(t,e,i){var n=[this,e],r=[t,i];return this.curve.endo?this.curve._endoWnafMulAdd(n,r):this.curve._wnafMulAdd(1,n,r,2)},f.prototype.jmulAdd=function(t,e,i){var n=[this,e],r=[t,i];return this.curve.endo?this.curve._endoWnafMulAdd(n,r,!0):this.curve._wnafMulAdd(1,n,r,2,!0)},f.prototype.eq=function(t){return this===t||this.inf===t.inf&&(this.inf||0===this.x.cmp(t.x)&&0===this.y.cmp(t.y))},f.prototype.neg=function(t){if(this.inf)return this;var e=this.curve.point(this.x,this.y.redNeg());if(t&&this.precomputed){var i=this.precomputed,n=function(t){return t.neg()};e.precomputed={naf:i.naf&&{wnd:i.naf.wnd,points:i.naf.points.map(n)},doubles:i.doubles&&{step:i.doubles.step,points:i.doubles.points.map(n)}}}return e},f.prototype.toJ=function(){if(this.inf)return this.curve.jpoint(null,null,null);var t=this.curve.jpoint(this.x,this.y,this.curve.one);return t},a(u,o.BasePoint),c.prototype.jpoint=function(t,e,i){return new u(this,t,e,i)},u.prototype.toP=function(){if(this.isInfinity())return this.curve.point(null,null);var t=this.z.redInvm(),e=t.redSqr(),i=this.x.redMul(e),n=this.y.redMul(e).redMul(t);return this.curve.point(i,n)},u.prototype.neg=function(){return this.curve.jpoint(this.x,this.y.redNeg(),this.z)},u.prototype.add=function(t){if(this.isInfinity())return t;if(t.isInfinity())return this;var e=t.z.redSqr(),i=this.z.redSqr(),n=this.x.redMul(e),r=t.x.redMul(i),a=this.y.redMul(e.redMul(t.z)),o=t.y.redMul(i.redMul(this.z)),s=n.redSub(r),c=a.redSub(o);if(0===s.cmpn(0))return 0!==c.cmpn(0)?this.curve.jpoint(null,null,null):this.dbl();var f=s.redSqr(),u=f.redMul(s),d=n.redMul(f),h=c.redSqr().redIAdd(u).redISub(d).redISub(d),l=c.redMul(d.redISub(h)).redISub(a.redMul(u)),p=this.z.redMul(t.z).redMul(s);return this.curve.jpoint(h,l,p)},u.prototype.mixedAdd=function(t){if(this.isInfinity())return t.toJ();if(t.isInfinity())return this;var e=this.z.redSqr(),i=this.x,n=t.x.redMul(e),r=this.y,a=t.y.redMul(e).redMul(this.z),o=i.redSub(n),s=r.redSub(a);if(0===o.cmpn(0))return 0!==s.cmpn(0)?this.curve.jpoint(null,null,null):this.dbl();var c=o.redSqr(),f=c.redMul(o),u=i.redMul(c),d=s.redSqr().redIAdd(f).redISub(u).redISub(u),h=s.redMul(u.redISub(d)).redISub(r.redMul(f)),l=this.z.redMul(o);return this.curve.jpoint(d,h,l)},u.prototype.dblp=function(t){if(0===t)return this;if(this.isInfinity())return this;if(!t)return this.dbl();var e;if(this.curve.zeroA||this.curve.threeA){var i=this;for(e=0;e<t;e++)i=i.dbl();return i}var n=this.curve.a,r=this.curve.tinv,a=this.x,o=this.y,s=this.z,c=s.redSqr().redSqr(),f=o.redAdd(o);for(e=0;e<t;e++){var u=a.redSqr(),d=f.redSqr(),h=d.redSqr(),l=u.redAdd(u).redIAdd(u).redIAdd(n.redMul(c)),p=a.redMul(d),b=l.redSqr().redISub(p.redAdd(p)),v=p.redISub(b),m=l.redMul(v);m=m.redIAdd(m).redISub(h);var g=f.redMul(s);e+1<t&&(c=c.redMul(h)),a=b,s=g,f=m}return this.curve.jpoint(a,f.redMul(r),s)},u.prototype.dbl=function(){return this.isInfinity()?this:this.curve.zeroA?this._zeroDbl():this.curve.threeA?this._threeDbl():this._dbl()},u.prototype._zeroDbl=function(){var t,e,i;if(this.zOne){var n=this.x.redSqr(),r=this.y.redSqr(),a=r.redSqr(),o=this.x.redAdd(r).redSqr().redISub(n).redISub(a);o=o.redIAdd(o);var s=n.redAdd(n).redIAdd(n),c=s.redSqr().redISub(o).redISub(o),f=a.redIAdd(a);f=f.redIAdd(f),f=f.redIAdd(f),t=c,e=s.redMul(o.redISub(c)).redISub(f),i=this.y.redAdd(this.y)}else{var u=this.x.redSqr(),d=this.y.redSqr(),h=d.redSqr(),l=this.x.redAdd(d).redSqr().redISub(u).redISub(h);l=l.redIAdd(l);var p=u.redAdd(u).redIAdd(u),b=p.redSqr(),v=h.redIAdd(h);v=v.redIAdd(v),v=v.redIAdd(v),t=b.redISub(l).redISub(l),e=p.redMul(l.redISub(t)).redISub(v),i=this.y.redMul(this.z),i=i.redIAdd(i)}return this.curve.jpoint(t,e,i)},u.prototype._threeDbl=function(){var t,e,i;if(this.zOne){var n=this.x.redSqr(),r=this.y.redSqr(),a=r.redSqr(),o=this.x.redAdd(r).redSqr().redISub(n).redISub(a);o=o.redIAdd(o);var s=n.redAdd(n).redIAdd(n).redIAdd(this.curve.a),c=s.redSqr().redISub(o).redISub(o);t=c;var f=a.redIAdd(a);f=f.redIAdd(f),f=f.redIAdd(f),e=s.redMul(o.redISub(c)).redISub(f),i=this.y.redAdd(this.y)}else{var u=this.z.redSqr(),d=this.y.redSqr(),h=this.x.redMul(d),l=this.x.redSub(u).redMul(this.x.redAdd(u));l=l.redAdd(l).redIAdd(l);var p=h.redIAdd(h);p=p.redIAdd(p);var b=p.redAdd(p);t=l.redSqr().redISub(b),i=this.y.redAdd(this.z).redSqr().redISub(d).redISub(u);var v=d.redSqr();v=v.redIAdd(v),v=v.redIAdd(v),v=v.redIAdd(v),e=l.redMul(p.redISub(t)).redISub(v)}return this.curve.jpoint(t,e,i)},u.prototype._dbl=function(){var t=this.curve.a,e=this.x,i=this.y,n=this.z,r=n.redSqr().redSqr(),a=e.redSqr(),o=i.redSqr(),s=a.redAdd(a).redIAdd(a).redIAdd(t.redMul(r)),c=e.redAdd(e);c=c.redIAdd(c);var f=c.redMul(o),u=s.redSqr().redISub(f.redAdd(f)),d=f.redISub(u),h=o.redSqr();h=h.redIAdd(h),h=h.redIAdd(h),h=h.redIAdd(h);var l=s.redMul(d).redISub(h),p=i.redAdd(i).redMul(n);return this.curve.jpoint(u,l,p)},u.prototype.trpl=function(){if(!this.curve.zeroA)return this.dbl().add(this);var t=this.x.redSqr(),e=this.y.redSqr(),i=this.z.redSqr(),n=e.redSqr(),r=t.redAdd(t).redIAdd(t),a=r.redSqr(),o=this.x.redAdd(e).redSqr().redISub(t).redISub(n);o=o.redIAdd(o),o=o.redAdd(o).redIAdd(o),o=o.redISub(a);var s=o.redSqr(),c=n.redIAdd(n);c=c.redIAdd(c),c=c.redIAdd(c),c=c.redIAdd(c);var f=r.redIAdd(o).redSqr().redISub(a).redISub(s).redISub(c),u=e.redMul(f);u=u.redIAdd(u),u=u.redIAdd(u);var d=this.x.redMul(s).redISub(u);d=d.redIAdd(d),d=d.redIAdd(d);var h=this.y.redMul(f.redMul(c.redISub(f)).redISub(o.redMul(s)));h=h.redIAdd(h),h=h.redIAdd(h),h=h.redIAdd(h);var l=this.z.redAdd(o).redSqr().redISub(i).redISub(s);return this.curve.jpoint(d,h,l)},u.prototype.mul=function(t,e){return t=new r(t,e),this.curve._wnafMul(this,t)},u.prototype.eq=function(t){if("affine"===t.type)return this.eq(t.toJ());if(this===t)return!0;var e=this.z.redSqr(),i=t.z.redSqr();if(0!==this.x.redMul(i).redISub(t.x.redMul(e)).cmpn(0))return!1;var n=e.redMul(this.z),r=i.redMul(t.z);return 0===this.y.redMul(r).redISub(t.y.redMul(n)).cmpn(0)},u.prototype.eqXToP=function(t){var e=this.z.redSqr(),i=t.toRed(this.curve.red).redMul(e);if(0===this.x.cmp(i))return!0;for(var n=t.clone(),r=this.curve.redN.redMul(e);;){if(n.iadd(this.curve.n),n.cmp(this.curve.p)>=0)return!1;if(i.redIAdd(r),0===this.x.cmp(i))return!0}},u.prototype.inspect=function(){return this.isInfinity()?"<EC JPoint Infinity>":"<EC JPoint x: "+this.x.toString(16,2)+" y: "+this.y.toString(16,2)+" z: "+this.z.toString(16,2)+">"},u.prototype.isInfinity=function(){return 0===this.z.cmpn(0)}},3337:function(t,e,i){"use strict";var n=e;n.version=i("2801").version,n.utils=i("f3a3"),n.rand=i("fdac"),n.curve=i("4136"),n.curves=i("0cbb"),n.ec=i("b9a8"),n.eddsa=i("945d")},"343e":function(t,e,i){"use strict";const n=e;n.der=i("3768"),n.pem=i("85b3")},3505:function(t,e,i){var n=i("8707").Buffer,r=i("8c8a");function a(t,e,i){var a=e.length,o=r(e,t._cache);return t._cache=t._cache.slice(a),t._prev=n.concat([t._prev,i?e:o]),o}e.encrypt=function(t,e,i){var r,o=n.allocUnsafe(0);while(e.length){if(0===t._cache.length&&(t._cache=t._cipher.encryptBlock(t._prev),t._prev=n.allocUnsafe(0)),!(t._cache.length<=e.length)){o=n.concat([o,a(t,e,i)]);break}r=t._cache.length,o=n.concat([o,a(t,e.slice(0,r),i)]),e=e.slice(r)}return o}},3768:function(t,e,i){"use strict";const n=i("3fb5"),r=i("c591").Buffer,a=i("8360"),o=i("8b71");function s(t){this.enc="der",this.name=t.name,this.entity=t,this.tree=new c,this.tree._init(t.body)}function c(t){a.call(this,"der",t)}function f(t){return t<10?"0"+t:t}t.exports=s,s.prototype.encode=function(t,e){return this.tree._encode(t,e).join()},n(c,a),c.prototype._encodeComposite=function(t,e,i,n){const a=function(t,e,i,n){let r;"seqof"===t?t="seq":"setof"===t&&(t="set");if(o.tagByName.hasOwnProperty(t))r=o.tagByName[t];else{if("number"!==typeof t||(0|t)!==t)return n.error("Unknown tag: "+t);r=t}if(r>=31)return n.error("Multi-octet tag encoding unsupported");e||(r|=32);return r|=o.tagClassByName[i||"universal"]<<6,r}(t,e,i,this.reporter);if(n.length<128){const t=r.alloc(2);return t[0]=a,t[1]=n.length,this._createEncoderBuffer([t,n])}let s=1;for(let r=n.length;r>=256;r>>=8)s++;const c=r.alloc(2+s);c[0]=a,c[1]=128|s;for(let r=1+s,o=n.length;o>0;r--,o>>=8)c[r]=255&o;return this._createEncoderBuffer([c,n])},c.prototype._encodeStr=function(t,e){if("bitstr"===e)return this._createEncoderBuffer([0|t.unused,t.data]);if("bmpstr"===e){const e=r.alloc(2*t.length);for(let i=0;i<t.length;i++)e.writeUInt16BE(t.charCodeAt(i),2*i);return this._createEncoderBuffer(e)}return"numstr"===e?this._isNumstr(t)?this._createEncoderBuffer(t):this.reporter.error("Encoding of string type: numstr supports only digits and space"):"printstr"===e?this._isPrintstr(t)?this._createEncoderBuffer(t):this.reporter.error("Encoding of string type: printstr supports only latin upper and lower case letters, digits, space, apostrophe, left and rigth parenthesis, plus sign, comma, hyphen, dot, slash, colon, equal sign, question mark"):/str$/.test(e)||"objDesc"===e?this._createEncoderBuffer(t):this.reporter.error("Encoding of string type: "+e+" unsupported")},c.prototype._encodeObjid=function(t,e,i){if("string"===typeof t){if(!e)return this.reporter.error("string objid given, but no values map found");if(!e.hasOwnProperty(t))return this.reporter.error("objid not found in values map");t=e[t].split(/[\s.]+/g);for(let e=0;e<t.length;e++)t[e]|=0}else if(Array.isArray(t)){t=t.slice();for(let e=0;e<t.length;e++)t[e]|=0}if(!Array.isArray(t))return this.reporter.error("objid() should be either array or string, got: "+JSON.stringify(t));if(!i){if(t[1]>=40)return this.reporter.error("Second objid identifier OOB");t.splice(0,2,40*t[0]+t[1])}let n=0;for(let r=0;r<t.length;r++){let e=t[r];for(n++;e>=128;e>>=7)n++}const a=r.alloc(n);let o=a.length-1;for(let r=t.length-1;r>=0;r--){let e=t[r];a[o--]=127&e;while((e>>=7)>0)a[o--]=128|127&e}return this._createEncoderBuffer(a)},c.prototype._encodeTime=function(t,e){let i;const n=new Date(t);return"gentime"===e?i=[f(n.getUTCFullYear()),f(n.getUTCMonth()+1),f(n.getUTCDate()),f(n.getUTCHours()),f(n.getUTCMinutes()),f(n.getUTCSeconds()),"Z"].join(""):"utctime"===e?i=[f(n.getUTCFullYear()%100),f(n.getUTCMonth()+1),f(n.getUTCDate()),f(n.getUTCHours()),f(n.getUTCMinutes()),f(n.getUTCSeconds()),"Z"].join(""):this.reporter.error("Encoding "+e+" time is not supported yet"),this._encodeStr(i,"octstr")},c.prototype._encodeNull=function(){return this._createEncoderBuffer("")},c.prototype._encodeInt=function(t,e){if("string"===typeof t){if(!e)return this.reporter.error("String int or enum given, but no values map");if(!e.hasOwnProperty(t))return this.reporter.error("Values map doesn't contain: "+JSON.stringify(t));t=e[t]}if("number"!==typeof t&&!r.isBuffer(t)){const e=t.toArray();!t.sign&&128&e[0]&&e.unshift(0),t=r.from(e)}if(r.isBuffer(t)){let e=t.length;0===t.length&&e++;const i=r.alloc(e);return t.copy(i),0===t.length&&(i[0]=0),this._createEncoderBuffer(i)}if(t<128)return this._createEncoderBuffer(t);if(t<256)return this._createEncoderBuffer([0,t]);let i=1;for(let r=t;r>=256;r>>=8)i++;const n=new Array(i);for(let r=n.length-1;r>=0;r--)n[r]=255&t,t>>=8;return 128&n[0]&&n.unshift(0),this._createEncoderBuffer(r.from(n))},c.prototype._encodeBool=function(t){return this._createEncoderBuffer(t?255:0)},c.prototype._use=function(t,e){return"function"===typeof t&&(t=t(e)),t._getEncoder("der").tree},c.prototype._skipDefault=function(t,e,i){const n=this._baseState;let r;if(null===n["default"])return!1;const a=t.join();if(void 0===n.defaultBuffer&&(n.defaultBuffer=this._encodeValue(n["default"],e,i).join()),a.length!==n.defaultBuffer.length)return!1;for(r=0;r<a.length;r++)if(a[r]!==n.defaultBuffer[r])return!1;return!0}},"380f":function(t,e,i){"use strict";var n=i("f3a3"),r=n.assert,a=n.parseBytes,o=n.cachedProperty;function s(t,e){this.eddsa=t,this._secret=a(e.secret),t.isPoint(e.pub)?this._pub=e.pub:this._pubBytes=a(e.pub)}s.fromPublic=function(t,e){return e instanceof s?e:new s(t,{pub:e})},s.fromSecret=function(t,e){return e instanceof s?e:new s(t,{secret:e})},s.prototype.secret=function(){return this._secret},o(s,"pubBytes",(function(){return this.eddsa.encodePoint(this.pub())})),o(s,"pub",(function(){return this._pubBytes?this.eddsa.decodePoint(this._pubBytes):this.eddsa.g.mul(this.priv())})),o(s,"privBytes",(function(){var t=this.eddsa,e=this.hash(),i=t.encodingLength-1,n=e.slice(0,t.encodingLength);return n[0]&=248,n[i]&=127,n[i]|=64,n})),o(s,"priv",(function(){return this.eddsa.decodeInt(this.privBytes())})),o(s,"hash",(function(){return this.eddsa.hash().update(this.secret()).digest()})),o(s,"messagePrefix",(function(){return this.hash().slice(this.eddsa.encodingLength)})),s.prototype.sign=function(t){return r(this._secret,"KeyPair can only verify"),this.eddsa.sign(t,this)},s.prototype.verify=function(t,e){return this.eddsa.verify(t,e,this)},s.prototype.getSecret=function(t){return r(this._secret,"KeyPair is public only"),n.encode(this.secret(),t)},s.prototype.getPublic=function(t){return n.encode(this.pubBytes(),t)},t.exports=s},3995:function(t,e,i){"use strict";i.r(e);var n=i("8d46"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"399f":function(t,e,i){(function(t){(function(t,e){"use strict";function n(t,e){if(!t)throw new Error(e||"Assertion failed")}function r(t,e){t.super_=e;var i=function(){};i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t}function a(t,e,i){if(a.isBN(t))return t;this.negative=0,this.words=null,this.length=0,this.red=null,null!==t&&("le"!==e&&"be"!==e||(i=e,e=10),this._init(t||0,e||10,i||"be"))}var o;"object"===typeof t?t.exports=a:e.BN=a,a.BN=a,a.wordSize=26;try{o="undefined"!==typeof window&&"undefined"!==typeof window.Buffer?window.Buffer:i(3).Buffer}catch(A){}function s(t,e){var i=t.charCodeAt(e);return i>=65&&i<=70?i-55:i>=97&&i<=102?i-87:i-48&15}function c(t,e,i){var n=s(t,i);return i-1>=e&&(n|=s(t,i-1)<<4),n}function f(t,e,i,n){for(var r=0,a=Math.min(t.length,i),o=e;o<a;o++){var s=t.charCodeAt(o)-48;r*=n,r+=s>=49?s-49+10:s>=17?s-17+10:s}return r}a.isBN=function(t){return t instanceof a||null!==t&&"object"===typeof t&&t.constructor.wordSize===a.wordSize&&Array.isArray(t.words)},a.max=function(t,e){return t.cmp(e)>0?t:e},a.min=function(t,e){return t.cmp(e)<0?t:e},a.prototype._init=function(t,e,i){if("number"===typeof t)return this._initNumber(t,e,i);if("object"===typeof t)return this._initArray(t,e,i);"hex"===e&&(e=16),n(e===(0|e)&&e>=2&&e<=36),t=t.toString().replace(/\s+/g,"");var r=0;"-"===t[0]&&(r++,this.negative=1),r<t.length&&(16===e?this._parseHex(t,r,i):(this._parseBase(t,e,r),"le"===i&&this._initArray(this.toArray(),e,i)))},a.prototype._initNumber=function(t,e,i){t<0&&(this.negative=1,t=-t),t<67108864?(this.words=[67108863&t],this.length=1):t<4503599627370496?(this.words=[67108863&t,t/67108864&67108863],this.length=2):(n(t<9007199254740992),this.words=[67108863&t,t/67108864&67108863,1],this.length=3),"le"===i&&this._initArray(this.toArray(),e,i)},a.prototype._initArray=function(t,e,i){if(n("number"===typeof t.length),t.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(t.length/3),this.words=new Array(this.length);for(var r=0;r<this.length;r++)this.words[r]=0;var a,o,s=0;if("be"===i)for(r=t.length-1,a=0;r>=0;r-=3)o=t[r]|t[r-1]<<8|t[r-2]<<16,this.words[a]|=o<<s&67108863,this.words[a+1]=o>>>26-s&67108863,s+=24,s>=26&&(s-=26,a++);else if("le"===i)for(r=0,a=0;r<t.length;r+=3)o=t[r]|t[r+1]<<8|t[r+2]<<16,this.words[a]|=o<<s&67108863,this.words[a+1]=o>>>26-s&67108863,s+=24,s>=26&&(s-=26,a++);return this.strip()},a.prototype._parseHex=function(t,e,i){this.length=Math.ceil((t.length-e)/6),this.words=new Array(this.length);for(var n=0;n<this.length;n++)this.words[n]=0;var r,a=0,o=0;if("be"===i)for(n=t.length-1;n>=e;n-=2)r=c(t,e,n)<<a,this.words[o]|=67108863&r,a>=18?(a-=18,o+=1,this.words[o]|=r>>>26):a+=8;else{var s=t.length-e;for(n=s%2===0?e+1:e;n<t.length;n+=2)r=c(t,e,n)<<a,this.words[o]|=67108863&r,a>=18?(a-=18,o+=1,this.words[o]|=r>>>26):a+=8}this.strip()},a.prototype._parseBase=function(t,e,i){this.words=[0],this.length=1;for(var n=0,r=1;r<=67108863;r*=e)n++;n--,r=r/e|0;for(var a=t.length-i,o=a%n,s=Math.min(a,a-o)+i,c=0,u=i;u<s;u+=n)c=f(t,u,u+n,e),this.imuln(r),this.words[0]+c<67108864?this.words[0]+=c:this._iaddn(c);if(0!==o){var d=1;for(c=f(t,u,t.length,e),u=0;u<o;u++)d*=e;this.imuln(d),this.words[0]+c<67108864?this.words[0]+=c:this._iaddn(c)}this.strip()},a.prototype.copy=function(t){t.words=new Array(this.length);for(var e=0;e<this.length;e++)t.words[e]=this.words[e];t.length=this.length,t.negative=this.negative,t.red=this.red},a.prototype.clone=function(){var t=new a(null);return this.copy(t),t},a.prototype._expand=function(t){while(this.length<t)this.words[this.length++]=0;return this},a.prototype.strip=function(){while(this.length>1&&0===this.words[this.length-1])this.length--;return this._normSign()},a.prototype._normSign=function(){return 1===this.length&&0===this.words[0]&&(this.negative=0),this},a.prototype.inspect=function(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"};var u=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],d=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],h=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];function l(t,e,i){i.negative=e.negative^t.negative;var n=t.length+e.length|0;i.length=n,n=n-1|0;var r=0|t.words[0],a=0|e.words[0],o=r*a,s=67108863&o,c=o/67108864|0;i.words[0]=s;for(var f=1;f<n;f++){for(var u=c>>>26,d=67108863&c,h=Math.min(f,e.length-1),l=Math.max(0,f-t.length+1);l<=h;l++){var p=f-l|0;r=0|t.words[p],a=0|e.words[l],o=r*a+d,u+=o/67108864|0,d=67108863&o}i.words[f]=0|d,c=0|u}return 0!==c?i.words[f]=0|c:i.length--,i.strip()}a.prototype.toString=function(t,e){var i;if(t=t||10,e=0|e||1,16===t||"hex"===t){i="";for(var r=0,a=0,o=0;o<this.length;o++){var s=this.words[o],c=(16777215&(s<<r|a)).toString(16);a=s>>>24-r&16777215,i=0!==a||o!==this.length-1?u[6-c.length]+c+i:c+i,r+=2,r>=26&&(r-=26,o--)}0!==a&&(i=a.toString(16)+i);while(i.length%e!==0)i="0"+i;return 0!==this.negative&&(i="-"+i),i}if(t===(0|t)&&t>=2&&t<=36){var f=d[t],l=h[t];i="";var p=this.clone();p.negative=0;while(!p.isZero()){var b=p.modn(l).toString(t);p=p.idivn(l),i=p.isZero()?b+i:u[f-b.length]+b+i}this.isZero()&&(i="0"+i);while(i.length%e!==0)i="0"+i;return 0!==this.negative&&(i="-"+i),i}n(!1,"Base should be between 2 and 36")},a.prototype.toNumber=function(){var t=this.words[0];return 2===this.length?t+=67108864*this.words[1]:3===this.length&&1===this.words[2]?t+=4503599627370496+67108864*this.words[1]:this.length>2&&n(!1,"Number can only safely store up to 53 bits"),0!==this.negative?-t:t},a.prototype.toJSON=function(){return this.toString(16)},a.prototype.toBuffer=function(t,e){return n("undefined"!==typeof o),this.toArrayLike(o,t,e)},a.prototype.toArray=function(t,e){return this.toArrayLike(Array,t,e)},a.prototype.toArrayLike=function(t,e,i){var r=this.byteLength(),a=i||Math.max(1,r);n(r<=a,"byte array longer than desired length"),n(a>0,"Requested array length <= 0"),this.strip();var o,s,c="le"===e,f=new t(a),u=this.clone();if(c){for(s=0;!u.isZero();s++)o=u.andln(255),u.iushrn(8),f[s]=o;for(;s<a;s++)f[s]=0}else{for(s=0;s<a-r;s++)f[s]=0;for(s=0;!u.isZero();s++)o=u.andln(255),u.iushrn(8),f[a-s-1]=o}return f},Math.clz32?a.prototype._countBits=function(t){return 32-Math.clz32(t)}:a.prototype._countBits=function(t){var e=t,i=0;return e>=4096&&(i+=13,e>>>=13),e>=64&&(i+=7,e>>>=7),e>=8&&(i+=4,e>>>=4),e>=2&&(i+=2,e>>>=2),i+e},a.prototype._zeroBits=function(t){if(0===t)return 26;var e=t,i=0;return 0===(8191&e)&&(i+=13,e>>>=13),0===(127&e)&&(i+=7,e>>>=7),0===(15&e)&&(i+=4,e>>>=4),0===(3&e)&&(i+=2,e>>>=2),0===(1&e)&&i++,i},a.prototype.bitLength=function(){var t=this.words[this.length-1],e=this._countBits(t);return 26*(this.length-1)+e},a.prototype.zeroBits=function(){if(this.isZero())return 0;for(var t=0,e=0;e<this.length;e++){var i=this._zeroBits(this.words[e]);if(t+=i,26!==i)break}return t},a.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},a.prototype.toTwos=function(t){return 0!==this.negative?this.abs().inotn(t).iaddn(1):this.clone()},a.prototype.fromTwos=function(t){return this.testn(t-1)?this.notn(t).iaddn(1).ineg():this.clone()},a.prototype.isNeg=function(){return 0!==this.negative},a.prototype.neg=function(){return this.clone().ineg()},a.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},a.prototype.iuor=function(t){while(this.length<t.length)this.words[this.length++]=0;for(var e=0;e<t.length;e++)this.words[e]=this.words[e]|t.words[e];return this.strip()},a.prototype.ior=function(t){return n(0===(this.negative|t.negative)),this.iuor(t)},a.prototype.or=function(t){return this.length>t.length?this.clone().ior(t):t.clone().ior(this)},a.prototype.uor=function(t){return this.length>t.length?this.clone().iuor(t):t.clone().iuor(this)},a.prototype.iuand=function(t){var e;e=this.length>t.length?t:this;for(var i=0;i<e.length;i++)this.words[i]=this.words[i]&t.words[i];return this.length=e.length,this.strip()},a.prototype.iand=function(t){return n(0===(this.negative|t.negative)),this.iuand(t)},a.prototype.and=function(t){return this.length>t.length?this.clone().iand(t):t.clone().iand(this)},a.prototype.uand=function(t){return this.length>t.length?this.clone().iuand(t):t.clone().iuand(this)},a.prototype.iuxor=function(t){var e,i;this.length>t.length?(e=this,i=t):(e=t,i=this);for(var n=0;n<i.length;n++)this.words[n]=e.words[n]^i.words[n];if(this!==e)for(;n<e.length;n++)this.words[n]=e.words[n];return this.length=e.length,this.strip()},a.prototype.ixor=function(t){return n(0===(this.negative|t.negative)),this.iuxor(t)},a.prototype.xor=function(t){return this.length>t.length?this.clone().ixor(t):t.clone().ixor(this)},a.prototype.uxor=function(t){return this.length>t.length?this.clone().iuxor(t):t.clone().iuxor(this)},a.prototype.inotn=function(t){n("number"===typeof t&&t>=0);var e=0|Math.ceil(t/26),i=t%26;this._expand(e),i>0&&e--;for(var r=0;r<e;r++)this.words[r]=67108863&~this.words[r];return i>0&&(this.words[r]=~this.words[r]&67108863>>26-i),this.strip()},a.prototype.notn=function(t){return this.clone().inotn(t)},a.prototype.setn=function(t,e){n("number"===typeof t&&t>=0);var i=t/26|0,r=t%26;return this._expand(i+1),this.words[i]=e?this.words[i]|1<<r:this.words[i]&~(1<<r),this.strip()},a.prototype.iadd=function(t){var e,i,n;if(0!==this.negative&&0===t.negative)return this.negative=0,e=this.isub(t),this.negative^=1,this._normSign();if(0===this.negative&&0!==t.negative)return t.negative=0,e=this.isub(t),t.negative=1,e._normSign();this.length>t.length?(i=this,n=t):(i=t,n=this);for(var r=0,a=0;a<n.length;a++)e=(0|i.words[a])+(0|n.words[a])+r,this.words[a]=67108863&e,r=e>>>26;for(;0!==r&&a<i.length;a++)e=(0|i.words[a])+r,this.words[a]=67108863&e,r=e>>>26;if(this.length=i.length,0!==r)this.words[this.length]=r,this.length++;else if(i!==this)for(;a<i.length;a++)this.words[a]=i.words[a];return this},a.prototype.add=function(t){var e;return 0!==t.negative&&0===this.negative?(t.negative=0,e=this.sub(t),t.negative^=1,e):0===t.negative&&0!==this.negative?(this.negative=0,e=t.sub(this),this.negative=1,e):this.length>t.length?this.clone().iadd(t):t.clone().iadd(this)},a.prototype.isub=function(t){if(0!==t.negative){t.negative=0;var e=this.iadd(t);return t.negative=1,e._normSign()}if(0!==this.negative)return this.negative=0,this.iadd(t),this.negative=1,this._normSign();var i,n,r=this.cmp(t);if(0===r)return this.negative=0,this.length=1,this.words[0]=0,this;r>0?(i=this,n=t):(i=t,n=this);for(var a=0,o=0;o<n.length;o++)e=(0|i.words[o])-(0|n.words[o])+a,a=e>>26,this.words[o]=67108863&e;for(;0!==a&&o<i.length;o++)e=(0|i.words[o])+a,a=e>>26,this.words[o]=67108863&e;if(0===a&&o<i.length&&i!==this)for(;o<i.length;o++)this.words[o]=i.words[o];return this.length=Math.max(this.length,o),i!==this&&(this.negative=1),this.strip()},a.prototype.sub=function(t){return this.clone().isub(t)};var p=function(t,e,i){var n,r,a,o=t.words,s=e.words,c=i.words,f=0,u=0|o[0],d=8191&u,h=u>>>13,l=0|o[1],p=8191&l,b=l>>>13,v=0|o[2],m=8191&v,g=v>>>13,y=0|o[3],w=8191&y,_=y>>>13,x=0|o[4],S=8191&x,k=x>>>13,A=0|o[5],E=8191&A,M=A>>>13,C=0|o[6],P=8191&C,I=C>>>13,B=0|o[7],R=8191&B,O=B>>>13,T=0|o[8],j=8191&T,L=T>>>13,D=0|o[9],N=8191&D,U=D>>>13,z=0|s[0],$=8191&z,q=z>>>13,F=0|s[1],V=8191&F,H=F>>>13,K=0|s[2],G=8191&K,W=K>>>13,Y=0|s[3],X=8191&Y,J=Y>>>13,Z=0|s[4],Q=8191&Z,tt=Z>>>13,et=0|s[5],it=8191&et,nt=et>>>13,rt=0|s[6],at=8191&rt,ot=rt>>>13,st=0|s[7],ct=8191&st,ft=st>>>13,ut=0|s[8],dt=8191&ut,ht=ut>>>13,lt=0|s[9],pt=8191&lt,bt=lt>>>13;i.negative=t.negative^e.negative,i.length=19,n=Math.imul(d,$),r=Math.imul(d,q),r=r+Math.imul(h,$)|0,a=Math.imul(h,q);var vt=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(vt>>>26)|0,vt&=67108863,n=Math.imul(p,$),r=Math.imul(p,q),r=r+Math.imul(b,$)|0,a=Math.imul(b,q),n=n+Math.imul(d,V)|0,r=r+Math.imul(d,H)|0,r=r+Math.imul(h,V)|0,a=a+Math.imul(h,H)|0;var mt=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(mt>>>26)|0,mt&=67108863,n=Math.imul(m,$),r=Math.imul(m,q),r=r+Math.imul(g,$)|0,a=Math.imul(g,q),n=n+Math.imul(p,V)|0,r=r+Math.imul(p,H)|0,r=r+Math.imul(b,V)|0,a=a+Math.imul(b,H)|0,n=n+Math.imul(d,G)|0,r=r+Math.imul(d,W)|0,r=r+Math.imul(h,G)|0,a=a+Math.imul(h,W)|0;var gt=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(gt>>>26)|0,gt&=67108863,n=Math.imul(w,$),r=Math.imul(w,q),r=r+Math.imul(_,$)|0,a=Math.imul(_,q),n=n+Math.imul(m,V)|0,r=r+Math.imul(m,H)|0,r=r+Math.imul(g,V)|0,a=a+Math.imul(g,H)|0,n=n+Math.imul(p,G)|0,r=r+Math.imul(p,W)|0,r=r+Math.imul(b,G)|0,a=a+Math.imul(b,W)|0,n=n+Math.imul(d,X)|0,r=r+Math.imul(d,J)|0,r=r+Math.imul(h,X)|0,a=a+Math.imul(h,J)|0;var yt=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(yt>>>26)|0,yt&=67108863,n=Math.imul(S,$),r=Math.imul(S,q),r=r+Math.imul(k,$)|0,a=Math.imul(k,q),n=n+Math.imul(w,V)|0,r=r+Math.imul(w,H)|0,r=r+Math.imul(_,V)|0,a=a+Math.imul(_,H)|0,n=n+Math.imul(m,G)|0,r=r+Math.imul(m,W)|0,r=r+Math.imul(g,G)|0,a=a+Math.imul(g,W)|0,n=n+Math.imul(p,X)|0,r=r+Math.imul(p,J)|0,r=r+Math.imul(b,X)|0,a=a+Math.imul(b,J)|0,n=n+Math.imul(d,Q)|0,r=r+Math.imul(d,tt)|0,r=r+Math.imul(h,Q)|0,a=a+Math.imul(h,tt)|0;var wt=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(wt>>>26)|0,wt&=67108863,n=Math.imul(E,$),r=Math.imul(E,q),r=r+Math.imul(M,$)|0,a=Math.imul(M,q),n=n+Math.imul(S,V)|0,r=r+Math.imul(S,H)|0,r=r+Math.imul(k,V)|0,a=a+Math.imul(k,H)|0,n=n+Math.imul(w,G)|0,r=r+Math.imul(w,W)|0,r=r+Math.imul(_,G)|0,a=a+Math.imul(_,W)|0,n=n+Math.imul(m,X)|0,r=r+Math.imul(m,J)|0,r=r+Math.imul(g,X)|0,a=a+Math.imul(g,J)|0,n=n+Math.imul(p,Q)|0,r=r+Math.imul(p,tt)|0,r=r+Math.imul(b,Q)|0,a=a+Math.imul(b,tt)|0,n=n+Math.imul(d,it)|0,r=r+Math.imul(d,nt)|0,r=r+Math.imul(h,it)|0,a=a+Math.imul(h,nt)|0;var _t=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(_t>>>26)|0,_t&=67108863,n=Math.imul(P,$),r=Math.imul(P,q),r=r+Math.imul(I,$)|0,a=Math.imul(I,q),n=n+Math.imul(E,V)|0,r=r+Math.imul(E,H)|0,r=r+Math.imul(M,V)|0,a=a+Math.imul(M,H)|0,n=n+Math.imul(S,G)|0,r=r+Math.imul(S,W)|0,r=r+Math.imul(k,G)|0,a=a+Math.imul(k,W)|0,n=n+Math.imul(w,X)|0,r=r+Math.imul(w,J)|0,r=r+Math.imul(_,X)|0,a=a+Math.imul(_,J)|0,n=n+Math.imul(m,Q)|0,r=r+Math.imul(m,tt)|0,r=r+Math.imul(g,Q)|0,a=a+Math.imul(g,tt)|0,n=n+Math.imul(p,it)|0,r=r+Math.imul(p,nt)|0,r=r+Math.imul(b,it)|0,a=a+Math.imul(b,nt)|0,n=n+Math.imul(d,at)|0,r=r+Math.imul(d,ot)|0,r=r+Math.imul(h,at)|0,a=a+Math.imul(h,ot)|0;var xt=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(xt>>>26)|0,xt&=67108863,n=Math.imul(R,$),r=Math.imul(R,q),r=r+Math.imul(O,$)|0,a=Math.imul(O,q),n=n+Math.imul(P,V)|0,r=r+Math.imul(P,H)|0,r=r+Math.imul(I,V)|0,a=a+Math.imul(I,H)|0,n=n+Math.imul(E,G)|0,r=r+Math.imul(E,W)|0,r=r+Math.imul(M,G)|0,a=a+Math.imul(M,W)|0,n=n+Math.imul(S,X)|0,r=r+Math.imul(S,J)|0,r=r+Math.imul(k,X)|0,a=a+Math.imul(k,J)|0,n=n+Math.imul(w,Q)|0,r=r+Math.imul(w,tt)|0,r=r+Math.imul(_,Q)|0,a=a+Math.imul(_,tt)|0,n=n+Math.imul(m,it)|0,r=r+Math.imul(m,nt)|0,r=r+Math.imul(g,it)|0,a=a+Math.imul(g,nt)|0,n=n+Math.imul(p,at)|0,r=r+Math.imul(p,ot)|0,r=r+Math.imul(b,at)|0,a=a+Math.imul(b,ot)|0,n=n+Math.imul(d,ct)|0,r=r+Math.imul(d,ft)|0,r=r+Math.imul(h,ct)|0,a=a+Math.imul(h,ft)|0;var St=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(St>>>26)|0,St&=67108863,n=Math.imul(j,$),r=Math.imul(j,q),r=r+Math.imul(L,$)|0,a=Math.imul(L,q),n=n+Math.imul(R,V)|0,r=r+Math.imul(R,H)|0,r=r+Math.imul(O,V)|0,a=a+Math.imul(O,H)|0,n=n+Math.imul(P,G)|0,r=r+Math.imul(P,W)|0,r=r+Math.imul(I,G)|0,a=a+Math.imul(I,W)|0,n=n+Math.imul(E,X)|0,r=r+Math.imul(E,J)|0,r=r+Math.imul(M,X)|0,a=a+Math.imul(M,J)|0,n=n+Math.imul(S,Q)|0,r=r+Math.imul(S,tt)|0,r=r+Math.imul(k,Q)|0,a=a+Math.imul(k,tt)|0,n=n+Math.imul(w,it)|0,r=r+Math.imul(w,nt)|0,r=r+Math.imul(_,it)|0,a=a+Math.imul(_,nt)|0,n=n+Math.imul(m,at)|0,r=r+Math.imul(m,ot)|0,r=r+Math.imul(g,at)|0,a=a+Math.imul(g,ot)|0,n=n+Math.imul(p,ct)|0,r=r+Math.imul(p,ft)|0,r=r+Math.imul(b,ct)|0,a=a+Math.imul(b,ft)|0,n=n+Math.imul(d,dt)|0,r=r+Math.imul(d,ht)|0,r=r+Math.imul(h,dt)|0,a=a+Math.imul(h,ht)|0;var kt=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(kt>>>26)|0,kt&=67108863,n=Math.imul(N,$),r=Math.imul(N,q),r=r+Math.imul(U,$)|0,a=Math.imul(U,q),n=n+Math.imul(j,V)|0,r=r+Math.imul(j,H)|0,r=r+Math.imul(L,V)|0,a=a+Math.imul(L,H)|0,n=n+Math.imul(R,G)|0,r=r+Math.imul(R,W)|0,r=r+Math.imul(O,G)|0,a=a+Math.imul(O,W)|0,n=n+Math.imul(P,X)|0,r=r+Math.imul(P,J)|0,r=r+Math.imul(I,X)|0,a=a+Math.imul(I,J)|0,n=n+Math.imul(E,Q)|0,r=r+Math.imul(E,tt)|0,r=r+Math.imul(M,Q)|0,a=a+Math.imul(M,tt)|0,n=n+Math.imul(S,it)|0,r=r+Math.imul(S,nt)|0,r=r+Math.imul(k,it)|0,a=a+Math.imul(k,nt)|0,n=n+Math.imul(w,at)|0,r=r+Math.imul(w,ot)|0,r=r+Math.imul(_,at)|0,a=a+Math.imul(_,ot)|0,n=n+Math.imul(m,ct)|0,r=r+Math.imul(m,ft)|0,r=r+Math.imul(g,ct)|0,a=a+Math.imul(g,ft)|0,n=n+Math.imul(p,dt)|0,r=r+Math.imul(p,ht)|0,r=r+Math.imul(b,dt)|0,a=a+Math.imul(b,ht)|0,n=n+Math.imul(d,pt)|0,r=r+Math.imul(d,bt)|0,r=r+Math.imul(h,pt)|0,a=a+Math.imul(h,bt)|0;var At=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(At>>>26)|0,At&=67108863,n=Math.imul(N,V),r=Math.imul(N,H),r=r+Math.imul(U,V)|0,a=Math.imul(U,H),n=n+Math.imul(j,G)|0,r=r+Math.imul(j,W)|0,r=r+Math.imul(L,G)|0,a=a+Math.imul(L,W)|0,n=n+Math.imul(R,X)|0,r=r+Math.imul(R,J)|0,r=r+Math.imul(O,X)|0,a=a+Math.imul(O,J)|0,n=n+Math.imul(P,Q)|0,r=r+Math.imul(P,tt)|0,r=r+Math.imul(I,Q)|0,a=a+Math.imul(I,tt)|0,n=n+Math.imul(E,it)|0,r=r+Math.imul(E,nt)|0,r=r+Math.imul(M,it)|0,a=a+Math.imul(M,nt)|0,n=n+Math.imul(S,at)|0,r=r+Math.imul(S,ot)|0,r=r+Math.imul(k,at)|0,a=a+Math.imul(k,ot)|0,n=n+Math.imul(w,ct)|0,r=r+Math.imul(w,ft)|0,r=r+Math.imul(_,ct)|0,a=a+Math.imul(_,ft)|0,n=n+Math.imul(m,dt)|0,r=r+Math.imul(m,ht)|0,r=r+Math.imul(g,dt)|0,a=a+Math.imul(g,ht)|0,n=n+Math.imul(p,pt)|0,r=r+Math.imul(p,bt)|0,r=r+Math.imul(b,pt)|0,a=a+Math.imul(b,bt)|0;var Et=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(Et>>>26)|0,Et&=67108863,n=Math.imul(N,G),r=Math.imul(N,W),r=r+Math.imul(U,G)|0,a=Math.imul(U,W),n=n+Math.imul(j,X)|0,r=r+Math.imul(j,J)|0,r=r+Math.imul(L,X)|0,a=a+Math.imul(L,J)|0,n=n+Math.imul(R,Q)|0,r=r+Math.imul(R,tt)|0,r=r+Math.imul(O,Q)|0,a=a+Math.imul(O,tt)|0,n=n+Math.imul(P,it)|0,r=r+Math.imul(P,nt)|0,r=r+Math.imul(I,it)|0,a=a+Math.imul(I,nt)|0,n=n+Math.imul(E,at)|0,r=r+Math.imul(E,ot)|0,r=r+Math.imul(M,at)|0,a=a+Math.imul(M,ot)|0,n=n+Math.imul(S,ct)|0,r=r+Math.imul(S,ft)|0,r=r+Math.imul(k,ct)|0,a=a+Math.imul(k,ft)|0,n=n+Math.imul(w,dt)|0,r=r+Math.imul(w,ht)|0,r=r+Math.imul(_,dt)|0,a=a+Math.imul(_,ht)|0,n=n+Math.imul(m,pt)|0,r=r+Math.imul(m,bt)|0,r=r+Math.imul(g,pt)|0,a=a+Math.imul(g,bt)|0;var Mt=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(Mt>>>26)|0,Mt&=67108863,n=Math.imul(N,X),r=Math.imul(N,J),r=r+Math.imul(U,X)|0,a=Math.imul(U,J),n=n+Math.imul(j,Q)|0,r=r+Math.imul(j,tt)|0,r=r+Math.imul(L,Q)|0,a=a+Math.imul(L,tt)|0,n=n+Math.imul(R,it)|0,r=r+Math.imul(R,nt)|0,r=r+Math.imul(O,it)|0,a=a+Math.imul(O,nt)|0,n=n+Math.imul(P,at)|0,r=r+Math.imul(P,ot)|0,r=r+Math.imul(I,at)|0,a=a+Math.imul(I,ot)|0,n=n+Math.imul(E,ct)|0,r=r+Math.imul(E,ft)|0,r=r+Math.imul(M,ct)|0,a=a+Math.imul(M,ft)|0,n=n+Math.imul(S,dt)|0,r=r+Math.imul(S,ht)|0,r=r+Math.imul(k,dt)|0,a=a+Math.imul(k,ht)|0,n=n+Math.imul(w,pt)|0,r=r+Math.imul(w,bt)|0,r=r+Math.imul(_,pt)|0,a=a+Math.imul(_,bt)|0;var Ct=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(Ct>>>26)|0,Ct&=67108863,n=Math.imul(N,Q),r=Math.imul(N,tt),r=r+Math.imul(U,Q)|0,a=Math.imul(U,tt),n=n+Math.imul(j,it)|0,r=r+Math.imul(j,nt)|0,r=r+Math.imul(L,it)|0,a=a+Math.imul(L,nt)|0,n=n+Math.imul(R,at)|0,r=r+Math.imul(R,ot)|0,r=r+Math.imul(O,at)|0,a=a+Math.imul(O,ot)|0,n=n+Math.imul(P,ct)|0,r=r+Math.imul(P,ft)|0,r=r+Math.imul(I,ct)|0,a=a+Math.imul(I,ft)|0,n=n+Math.imul(E,dt)|0,r=r+Math.imul(E,ht)|0,r=r+Math.imul(M,dt)|0,a=a+Math.imul(M,ht)|0,n=n+Math.imul(S,pt)|0,r=r+Math.imul(S,bt)|0,r=r+Math.imul(k,pt)|0,a=a+Math.imul(k,bt)|0;var Pt=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(Pt>>>26)|0,Pt&=67108863,n=Math.imul(N,it),r=Math.imul(N,nt),r=r+Math.imul(U,it)|0,a=Math.imul(U,nt),n=n+Math.imul(j,at)|0,r=r+Math.imul(j,ot)|0,r=r+Math.imul(L,at)|0,a=a+Math.imul(L,ot)|0,n=n+Math.imul(R,ct)|0,r=r+Math.imul(R,ft)|0,r=r+Math.imul(O,ct)|0,a=a+Math.imul(O,ft)|0,n=n+Math.imul(P,dt)|0,r=r+Math.imul(P,ht)|0,r=r+Math.imul(I,dt)|0,a=a+Math.imul(I,ht)|0,n=n+Math.imul(E,pt)|0,r=r+Math.imul(E,bt)|0,r=r+Math.imul(M,pt)|0,a=a+Math.imul(M,bt)|0;var It=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(It>>>26)|0,It&=67108863,n=Math.imul(N,at),r=Math.imul(N,ot),r=r+Math.imul(U,at)|0,a=Math.imul(U,ot),n=n+Math.imul(j,ct)|0,r=r+Math.imul(j,ft)|0,r=r+Math.imul(L,ct)|0,a=a+Math.imul(L,ft)|0,n=n+Math.imul(R,dt)|0,r=r+Math.imul(R,ht)|0,r=r+Math.imul(O,dt)|0,a=a+Math.imul(O,ht)|0,n=n+Math.imul(P,pt)|0,r=r+Math.imul(P,bt)|0,r=r+Math.imul(I,pt)|0,a=a+Math.imul(I,bt)|0;var Bt=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(Bt>>>26)|0,Bt&=67108863,n=Math.imul(N,ct),r=Math.imul(N,ft),r=r+Math.imul(U,ct)|0,a=Math.imul(U,ft),n=n+Math.imul(j,dt)|0,r=r+Math.imul(j,ht)|0,r=r+Math.imul(L,dt)|0,a=a+Math.imul(L,ht)|0,n=n+Math.imul(R,pt)|0,r=r+Math.imul(R,bt)|0,r=r+Math.imul(O,pt)|0,a=a+Math.imul(O,bt)|0;var Rt=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(Rt>>>26)|0,Rt&=67108863,n=Math.imul(N,dt),r=Math.imul(N,ht),r=r+Math.imul(U,dt)|0,a=Math.imul(U,ht),n=n+Math.imul(j,pt)|0,r=r+Math.imul(j,bt)|0,r=r+Math.imul(L,pt)|0,a=a+Math.imul(L,bt)|0;var Ot=(f+n|0)+((8191&r)<<13)|0;f=(a+(r>>>13)|0)+(Ot>>>26)|0,Ot&=67108863,n=Math.imul(N,pt),r=Math.imul(N,bt),r=r+Math.imul(U,pt)|0,a=Math.imul(U,bt);var Tt=(f+n|0)+((8191&r)<<13)|0;return f=(a+(r>>>13)|0)+(Tt>>>26)|0,Tt&=67108863,c[0]=vt,c[1]=mt,c[2]=gt,c[3]=yt,c[4]=wt,c[5]=_t,c[6]=xt,c[7]=St,c[8]=kt,c[9]=At,c[10]=Et,c[11]=Mt,c[12]=Ct,c[13]=Pt,c[14]=It,c[15]=Bt,c[16]=Rt,c[17]=Ot,c[18]=Tt,0!==f&&(c[19]=f,i.length++),i};function b(t,e,i){var n=new v;return n.mulp(t,e,i)}function v(t,e){this.x=t,this.y=e}Math.imul||(p=l),a.prototype.mulTo=function(t,e){var i,n=this.length+t.length;return i=10===this.length&&10===t.length?p(this,t,e):n<63?l(this,t,e):n<1024?function(t,e,i){i.negative=e.negative^t.negative,i.length=t.length+e.length;for(var n=0,r=0,a=0;a<i.length-1;a++){var o=r;r=0;for(var s=67108863&n,c=Math.min(a,e.length-1),f=Math.max(0,a-t.length+1);f<=c;f++){var u=a-f,d=0|t.words[u],h=0|e.words[f],l=d*h,p=67108863&l;o=o+(l/67108864|0)|0,p=p+s|0,s=67108863&p,o=o+(p>>>26)|0,r+=o>>>26,o&=67108863}i.words[a]=s,n=o,o=r}return 0!==n?i.words[a]=n:i.length--,i.strip()}(this,t,e):b(this,t,e),i},v.prototype.makeRBT=function(t){for(var e=new Array(t),i=a.prototype._countBits(t)-1,n=0;n<t;n++)e[n]=this.revBin(n,i,t);return e},v.prototype.revBin=function(t,e,i){if(0===t||t===i-1)return t;for(var n=0,r=0;r<e;r++)n|=(1&t)<<e-r-1,t>>=1;return n},v.prototype.permute=function(t,e,i,n,r,a){for(var o=0;o<a;o++)n[o]=e[t[o]],r[o]=i[t[o]]},v.prototype.transform=function(t,e,i,n,r,a){this.permute(a,t,e,i,n,r);for(var o=1;o<r;o<<=1)for(var s=o<<1,c=Math.cos(2*Math.PI/s),f=Math.sin(2*Math.PI/s),u=0;u<r;u+=s)for(var d=c,h=f,l=0;l<o;l++){var p=i[u+l],b=n[u+l],v=i[u+l+o],m=n[u+l+o],g=d*v-h*m;m=d*m+h*v,v=g,i[u+l]=p+v,n[u+l]=b+m,i[u+l+o]=p-v,n[u+l+o]=b-m,l!==s&&(g=c*d-f*h,h=c*h+f*d,d=g)}},v.prototype.guessLen13b=function(t,e){var i=1|Math.max(e,t),n=1&i,r=0;for(i=i/2|0;i;i>>>=1)r++;return 1<<r+1+n},v.prototype.conjugate=function(t,e,i){if(!(i<=1))for(var n=0;n<i/2;n++){var r=t[n];t[n]=t[i-n-1],t[i-n-1]=r,r=e[n],e[n]=-e[i-n-1],e[i-n-1]=-r}},v.prototype.normalize13b=function(t,e){for(var i=0,n=0;n<e/2;n++){var r=8192*Math.round(t[2*n+1]/e)+Math.round(t[2*n]/e)+i;t[n]=67108863&r,i=r<67108864?0:r/67108864|0}return t},v.prototype.convert13b=function(t,e,i,r){for(var a=0,o=0;o<e;o++)a+=0|t[o],i[2*o]=8191&a,a>>>=13,i[2*o+1]=8191&a,a>>>=13;for(o=2*e;o<r;++o)i[o]=0;n(0===a),n(0===(-8192&a))},v.prototype.stub=function(t){for(var e=new Array(t),i=0;i<t;i++)e[i]=0;return e},v.prototype.mulp=function(t,e,i){var n=2*this.guessLen13b(t.length,e.length),r=this.makeRBT(n),a=this.stub(n),o=new Array(n),s=new Array(n),c=new Array(n),f=new Array(n),u=new Array(n),d=new Array(n),h=i.words;h.length=n,this.convert13b(t.words,t.length,o,n),this.convert13b(e.words,e.length,f,n),this.transform(o,a,s,c,n,r),this.transform(f,a,u,d,n,r);for(var l=0;l<n;l++){var p=s[l]*u[l]-c[l]*d[l];c[l]=s[l]*d[l]+c[l]*u[l],s[l]=p}return this.conjugate(s,c,n),this.transform(s,c,h,a,n,r),this.conjugate(h,a,n),this.normalize13b(h,n),i.negative=t.negative^e.negative,i.length=t.length+e.length,i.strip()},a.prototype.mul=function(t){var e=new a(null);return e.words=new Array(this.length+t.length),this.mulTo(t,e)},a.prototype.mulf=function(t){var e=new a(null);return e.words=new Array(this.length+t.length),b(this,t,e)},a.prototype.imul=function(t){return this.clone().mulTo(t,this)},a.prototype.imuln=function(t){n("number"===typeof t),n(t<67108864);for(var e=0,i=0;i<this.length;i++){var r=(0|this.words[i])*t,a=(67108863&r)+(67108863&e);e>>=26,e+=r/67108864|0,e+=a>>>26,this.words[i]=67108863&a}return 0!==e&&(this.words[i]=e,this.length++),this},a.prototype.muln=function(t){return this.clone().imuln(t)},a.prototype.sqr=function(){return this.mul(this)},a.prototype.isqr=function(){return this.imul(this.clone())},a.prototype.pow=function(t){var e=function(t){for(var e=new Array(t.bitLength()),i=0;i<e.length;i++){var n=i/26|0,r=i%26;e[i]=(t.words[n]&1<<r)>>>r}return e}(t);if(0===e.length)return new a(1);for(var i=this,n=0;n<e.length;n++,i=i.sqr())if(0!==e[n])break;if(++n<e.length)for(var r=i.sqr();n<e.length;n++,r=r.sqr())0!==e[n]&&(i=i.mul(r));return i},a.prototype.iushln=function(t){n("number"===typeof t&&t>=0);var e,i=t%26,r=(t-i)/26,a=67108863>>>26-i<<26-i;if(0!==i){var o=0;for(e=0;e<this.length;e++){var s=this.words[e]&a,c=(0|this.words[e])-s<<i;this.words[e]=c|o,o=s>>>26-i}o&&(this.words[e]=o,this.length++)}if(0!==r){for(e=this.length-1;e>=0;e--)this.words[e+r]=this.words[e];for(e=0;e<r;e++)this.words[e]=0;this.length+=r}return this.strip()},a.prototype.ishln=function(t){return n(0===this.negative),this.iushln(t)},a.prototype.iushrn=function(t,e,i){var r;n("number"===typeof t&&t>=0),r=e?(e-e%26)/26:0;var a=t%26,o=Math.min((t-a)/26,this.length),s=67108863^67108863>>>a<<a,c=i;if(r-=o,r=Math.max(0,r),c){for(var f=0;f<o;f++)c.words[f]=this.words[f];c.length=o}if(0===o);else if(this.length>o)for(this.length-=o,f=0;f<this.length;f++)this.words[f]=this.words[f+o];else this.words[0]=0,this.length=1;var u=0;for(f=this.length-1;f>=0&&(0!==u||f>=r);f--){var d=0|this.words[f];this.words[f]=u<<26-a|d>>>a,u=d&s}return c&&0!==u&&(c.words[c.length++]=u),0===this.length&&(this.words[0]=0,this.length=1),this.strip()},a.prototype.ishrn=function(t,e,i){return n(0===this.negative),this.iushrn(t,e,i)},a.prototype.shln=function(t){return this.clone().ishln(t)},a.prototype.ushln=function(t){return this.clone().iushln(t)},a.prototype.shrn=function(t){return this.clone().ishrn(t)},a.prototype.ushrn=function(t){return this.clone().iushrn(t)},a.prototype.testn=function(t){n("number"===typeof t&&t>=0);var e=t%26,i=(t-e)/26,r=1<<e;if(this.length<=i)return!1;var a=this.words[i];return!!(a&r)},a.prototype.imaskn=function(t){n("number"===typeof t&&t>=0);var e=t%26,i=(t-e)/26;if(n(0===this.negative,"imaskn works only with positive numbers"),this.length<=i)return this;if(0!==e&&i++,this.length=Math.min(i,this.length),0!==e){var r=67108863^67108863>>>e<<e;this.words[this.length-1]&=r}return this.strip()},a.prototype.maskn=function(t){return this.clone().imaskn(t)},a.prototype.iaddn=function(t){return n("number"===typeof t),n(t<67108864),t<0?this.isubn(-t):0!==this.negative?1===this.length&&(0|this.words[0])<t?(this.words[0]=t-(0|this.words[0]),this.negative=0,this):(this.negative=0,this.isubn(t),this.negative=1,this):this._iaddn(t)},a.prototype._iaddn=function(t){this.words[0]+=t;for(var e=0;e<this.length&&this.words[e]>=67108864;e++)this.words[e]-=67108864,e===this.length-1?this.words[e+1]=1:this.words[e+1]++;return this.length=Math.max(this.length,e+1),this},a.prototype.isubn=function(t){if(n("number"===typeof t),n(t<67108864),t<0)return this.iaddn(-t);if(0!==this.negative)return this.negative=0,this.iaddn(t),this.negative=1,this;if(this.words[0]-=t,1===this.length&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var e=0;e<this.length&&this.words[e]<0;e++)this.words[e]+=67108864,this.words[e+1]-=1;return this.strip()},a.prototype.addn=function(t){return this.clone().iaddn(t)},a.prototype.subn=function(t){return this.clone().isubn(t)},a.prototype.iabs=function(){return this.negative=0,this},a.prototype.abs=function(){return this.clone().iabs()},a.prototype._ishlnsubmul=function(t,e,i){var r,a,o=t.length+i;this._expand(o);var s=0;for(r=0;r<t.length;r++){a=(0|this.words[r+i])+s;var c=(0|t.words[r])*e;a-=67108863&c,s=(a>>26)-(c/67108864|0),this.words[r+i]=67108863&a}for(;r<this.length-i;r++)a=(0|this.words[r+i])+s,s=a>>26,this.words[r+i]=67108863&a;if(0===s)return this.strip();for(n(-1===s),s=0,r=0;r<this.length;r++)a=-(0|this.words[r])+s,s=a>>26,this.words[r]=67108863&a;return this.negative=1,this.strip()},a.prototype._wordDiv=function(t,e){var i=(this.length,t.length),n=this.clone(),r=t,o=0|r.words[r.length-1],s=this._countBits(o);i=26-s,0!==i&&(r=r.ushln(i),n.iushln(i),o=0|r.words[r.length-1]);var c,f=n.length-r.length;if("mod"!==e){c=new a(null),c.length=f+1,c.words=new Array(c.length);for(var u=0;u<c.length;u++)c.words[u]=0}var d=n.clone()._ishlnsubmul(r,1,f);0===d.negative&&(n=d,c&&(c.words[f]=1));for(var h=f-1;h>=0;h--){var l=67108864*(0|n.words[r.length+h])+(0|n.words[r.length+h-1]);l=Math.min(l/o|0,67108863),n._ishlnsubmul(r,l,h);while(0!==n.negative)l--,n.negative=0,n._ishlnsubmul(r,1,h),n.isZero()||(n.negative^=1);c&&(c.words[h]=l)}return c&&c.strip(),n.strip(),"div"!==e&&0!==i&&n.iushrn(i),{div:c||null,mod:n}},a.prototype.divmod=function(t,e,i){return n(!t.isZero()),this.isZero()?{div:new a(0),mod:new a(0)}:0!==this.negative&&0===t.negative?(s=this.neg().divmod(t,e),"mod"!==e&&(r=s.div.neg()),"div"!==e&&(o=s.mod.neg(),i&&0!==o.negative&&o.iadd(t)),{div:r,mod:o}):0===this.negative&&0!==t.negative?(s=this.divmod(t.neg(),e),"mod"!==e&&(r=s.div.neg()),{div:r,mod:s.mod}):0!==(this.negative&t.negative)?(s=this.neg().divmod(t.neg(),e),"div"!==e&&(o=s.mod.neg(),i&&0!==o.negative&&o.isub(t)),{div:s.div,mod:o}):t.length>this.length||this.cmp(t)<0?{div:new a(0),mod:this}:1===t.length?"div"===e?{div:this.divn(t.words[0]),mod:null}:"mod"===e?{div:null,mod:new a(this.modn(t.words[0]))}:{div:this.divn(t.words[0]),mod:new a(this.modn(t.words[0]))}:this._wordDiv(t,e);var r,o,s},a.prototype.div=function(t){return this.divmod(t,"div",!1).div},a.prototype.mod=function(t){return this.divmod(t,"mod",!1).mod},a.prototype.umod=function(t){return this.divmod(t,"mod",!0).mod},a.prototype.divRound=function(t){var e=this.divmod(t);if(e.mod.isZero())return e.div;var i=0!==e.div.negative?e.mod.isub(t):e.mod,n=t.ushrn(1),r=t.andln(1),a=i.cmp(n);return a<0||1===r&&0===a?e.div:0!==e.div.negative?e.div.isubn(1):e.div.iaddn(1)},a.prototype.modn=function(t){n(t<=67108863);for(var e=(1<<26)%t,i=0,r=this.length-1;r>=0;r--)i=(e*i+(0|this.words[r]))%t;return i},a.prototype.idivn=function(t){n(t<=67108863);for(var e=0,i=this.length-1;i>=0;i--){var r=(0|this.words[i])+67108864*e;this.words[i]=r/t|0,e=r%t}return this.strip()},a.prototype.divn=function(t){return this.clone().idivn(t)},a.prototype.egcd=function(t){n(0===t.negative),n(!t.isZero());var e=this,i=t.clone();e=0!==e.negative?e.umod(t):e.clone();var r=new a(1),o=new a(0),s=new a(0),c=new a(1),f=0;while(e.isEven()&&i.isEven())e.iushrn(1),i.iushrn(1),++f;var u=i.clone(),d=e.clone();while(!e.isZero()){for(var h=0,l=1;0===(e.words[0]&l)&&h<26;++h,l<<=1);if(h>0){e.iushrn(h);while(h-- >0)(r.isOdd()||o.isOdd())&&(r.iadd(u),o.isub(d)),r.iushrn(1),o.iushrn(1)}for(var p=0,b=1;0===(i.words[0]&b)&&p<26;++p,b<<=1);if(p>0){i.iushrn(p);while(p-- >0)(s.isOdd()||c.isOdd())&&(s.iadd(u),c.isub(d)),s.iushrn(1),c.iushrn(1)}e.cmp(i)>=0?(e.isub(i),r.isub(s),o.isub(c)):(i.isub(e),s.isub(r),c.isub(o))}return{a:s,b:c,gcd:i.iushln(f)}},a.prototype._invmp=function(t){n(0===t.negative),n(!t.isZero());var e=this,i=t.clone();e=0!==e.negative?e.umod(t):e.clone();var r,o=new a(1),s=new a(0),c=i.clone();while(e.cmpn(1)>0&&i.cmpn(1)>0){for(var f=0,u=1;0===(e.words[0]&u)&&f<26;++f,u<<=1);if(f>0){e.iushrn(f);while(f-- >0)o.isOdd()&&o.iadd(c),o.iushrn(1)}for(var d=0,h=1;0===(i.words[0]&h)&&d<26;++d,h<<=1);if(d>0){i.iushrn(d);while(d-- >0)s.isOdd()&&s.iadd(c),s.iushrn(1)}e.cmp(i)>=0?(e.isub(i),o.isub(s)):(i.isub(e),s.isub(o))}return r=0===e.cmpn(1)?o:s,r.cmpn(0)<0&&r.iadd(t),r},a.prototype.gcd=function(t){if(this.isZero())return t.abs();if(t.isZero())return this.abs();var e=this.clone(),i=t.clone();e.negative=0,i.negative=0;for(var n=0;e.isEven()&&i.isEven();n++)e.iushrn(1),i.iushrn(1);do{while(e.isEven())e.iushrn(1);while(i.isEven())i.iushrn(1);var r=e.cmp(i);if(r<0){var a=e;e=i,i=a}else if(0===r||0===i.cmpn(1))break;e.isub(i)}while(1);return i.iushln(n)},a.prototype.invm=function(t){return this.egcd(t).a.umod(t)},a.prototype.isEven=function(){return 0===(1&this.words[0])},a.prototype.isOdd=function(){return 1===(1&this.words[0])},a.prototype.andln=function(t){return this.words[0]&t},a.prototype.bincn=function(t){n("number"===typeof t);var e=t%26,i=(t-e)/26,r=1<<e;if(this.length<=i)return this._expand(i+1),this.words[i]|=r,this;for(var a=r,o=i;0!==a&&o<this.length;o++){var s=0|this.words[o];s+=a,a=s>>>26,s&=67108863,this.words[o]=s}return 0!==a&&(this.words[o]=a,this.length++),this},a.prototype.isZero=function(){return 1===this.length&&0===this.words[0]},a.prototype.cmpn=function(t){var e,i=t<0;if(0!==this.negative&&!i)return-1;if(0===this.negative&&i)return 1;if(this.strip(),this.length>1)e=1;else{i&&(t=-t),n(t<=67108863,"Number is too big");var r=0|this.words[0];e=r===t?0:r<t?-1:1}return 0!==this.negative?0|-e:e},a.prototype.cmp=function(t){if(0!==this.negative&&0===t.negative)return-1;if(0===this.negative&&0!==t.negative)return 1;var e=this.ucmp(t);return 0!==this.negative?0|-e:e},a.prototype.ucmp=function(t){if(this.length>t.length)return 1;if(this.length<t.length)return-1;for(var e=0,i=this.length-1;i>=0;i--){var n=0|this.words[i],r=0|t.words[i];if(n!==r){n<r?e=-1:n>r&&(e=1);break}}return e},a.prototype.gtn=function(t){return 1===this.cmpn(t)},a.prototype.gt=function(t){return 1===this.cmp(t)},a.prototype.gten=function(t){return this.cmpn(t)>=0},a.prototype.gte=function(t){return this.cmp(t)>=0},a.prototype.ltn=function(t){return-1===this.cmpn(t)},a.prototype.lt=function(t){return-1===this.cmp(t)},a.prototype.lten=function(t){return this.cmpn(t)<=0},a.prototype.lte=function(t){return this.cmp(t)<=0},a.prototype.eqn=function(t){return 0===this.cmpn(t)},a.prototype.eq=function(t){return 0===this.cmp(t)},a.red=function(t){return new S(t)},a.prototype.toRed=function(t){return n(!this.red,"Already a number in reduction context"),n(0===this.negative,"red works only with positives"),t.convertTo(this)._forceRed(t)},a.prototype.fromRed=function(){return n(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},a.prototype._forceRed=function(t){return this.red=t,this},a.prototype.forceRed=function(t){return n(!this.red,"Already a number in reduction context"),this._forceRed(t)},a.prototype.redAdd=function(t){return n(this.red,"redAdd works only with red numbers"),this.red.add(this,t)},a.prototype.redIAdd=function(t){return n(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,t)},a.prototype.redSub=function(t){return n(this.red,"redSub works only with red numbers"),this.red.sub(this,t)},a.prototype.redISub=function(t){return n(this.red,"redISub works only with red numbers"),this.red.isub(this,t)},a.prototype.redShl=function(t){return n(this.red,"redShl works only with red numbers"),this.red.shl(this,t)},a.prototype.redMul=function(t){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.mul(this,t)},a.prototype.redIMul=function(t){return n(this.red,"redMul works only with red numbers"),this.red._verify2(this,t),this.red.imul(this,t)},a.prototype.redSqr=function(){return n(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},a.prototype.redISqr=function(){return n(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},a.prototype.redSqrt=function(){return n(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},a.prototype.redInvm=function(){return n(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},a.prototype.redNeg=function(){return n(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},a.prototype.redPow=function(t){return n(this.red&&!t.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,t)};var m={k256:null,p224:null,p192:null,p25519:null};function g(t,e){this.name=t,this.p=new a(e,16),this.n=this.p.bitLength(),this.k=new a(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}function y(){g.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}function w(){g.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}function _(){g.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}function x(){g.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}function S(t){if("string"===typeof t){var e=a._prime(t);this.m=e.p,this.prime=e}else n(t.gtn(1),"modulus must be greater than 1"),this.m=t,this.prime=null}function k(t){S.call(this,t),this.shift=this.m.bitLength(),this.shift%26!==0&&(this.shift+=26-this.shift%26),this.r=new a(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}g.prototype._tmp=function(){var t=new a(null);return t.words=new Array(Math.ceil(this.n/13)),t},g.prototype.ireduce=function(t){var e,i=t;do{this.split(i,this.tmp),i=this.imulK(i),i=i.iadd(this.tmp),e=i.bitLength()}while(e>this.n);var n=e<this.n?-1:i.ucmp(this.p);return 0===n?(i.words[0]=0,i.length=1):n>0?i.isub(this.p):void 0!==i.strip?i.strip():i._strip(),i},g.prototype.split=function(t,e){t.iushrn(this.n,0,e)},g.prototype.imulK=function(t){return t.imul(this.k)},r(y,g),y.prototype.split=function(t,e){for(var i=Math.min(t.length,9),n=0;n<i;n++)e.words[n]=t.words[n];if(e.length=i,t.length<=9)return t.words[0]=0,void(t.length=1);var r=t.words[9];for(e.words[e.length++]=4194303&r,n=10;n<t.length;n++){var a=0|t.words[n];t.words[n-10]=(4194303&a)<<4|r>>>22,r=a}r>>>=22,t.words[n-10]=r,0===r&&t.length>10?t.length-=10:t.length-=9},y.prototype.imulK=function(t){t.words[t.length]=0,t.words[t.length+1]=0,t.length+=2;for(var e=0,i=0;i<t.length;i++){var n=0|t.words[i];e+=977*n,t.words[i]=67108863&e,e=64*n+(e/67108864|0)}return 0===t.words[t.length-1]&&(t.length--,0===t.words[t.length-1]&&t.length--),t},r(w,g),r(_,g),r(x,g),x.prototype.imulK=function(t){for(var e=0,i=0;i<t.length;i++){var n=19*(0|t.words[i])+e,r=67108863&n;n>>>=26,t.words[i]=r,e=n}return 0!==e&&(t.words[t.length++]=e),t},a._prime=function(t){if(m[t])return m[t];var e;if("k256"===t)e=new y;else if("p224"===t)e=new w;else if("p192"===t)e=new _;else{if("p25519"!==t)throw new Error("Unknown prime "+t);e=new x}return m[t]=e,e},S.prototype._verify1=function(t){n(0===t.negative,"red works only with positives"),n(t.red,"red works only with red numbers")},S.prototype._verify2=function(t,e){n(0===(t.negative|e.negative),"red works only with positives"),n(t.red&&t.red===e.red,"red works only with red numbers")},S.prototype.imod=function(t){return this.prime?this.prime.ireduce(t)._forceRed(this):t.umod(this.m)._forceRed(this)},S.prototype.neg=function(t){return t.isZero()?t.clone():this.m.sub(t)._forceRed(this)},S.prototype.add=function(t,e){this._verify2(t,e);var i=t.add(e);return i.cmp(this.m)>=0&&i.isub(this.m),i._forceRed(this)},S.prototype.iadd=function(t,e){this._verify2(t,e);var i=t.iadd(e);return i.cmp(this.m)>=0&&i.isub(this.m),i},S.prototype.sub=function(t,e){this._verify2(t,e);var i=t.sub(e);return i.cmpn(0)<0&&i.iadd(this.m),i._forceRed(this)},S.prototype.isub=function(t,e){this._verify2(t,e);var i=t.isub(e);return i.cmpn(0)<0&&i.iadd(this.m),i},S.prototype.shl=function(t,e){return this._verify1(t),this.imod(t.ushln(e))},S.prototype.imul=function(t,e){return this._verify2(t,e),this.imod(t.imul(e))},S.prototype.mul=function(t,e){return this._verify2(t,e),this.imod(t.mul(e))},S.prototype.isqr=function(t){return this.imul(t,t.clone())},S.prototype.sqr=function(t){return this.mul(t,t)},S.prototype.sqrt=function(t){if(t.isZero())return t.clone();var e=this.m.andln(3);if(n(e%2===1),3===e){var i=this.m.add(new a(1)).iushrn(2);return this.pow(t,i)}var r=this.m.subn(1),o=0;while(!r.isZero()&&0===r.andln(1))o++,r.iushrn(1);n(!r.isZero());var s=new a(1).toRed(this),c=s.redNeg(),f=this.m.subn(1).iushrn(1),u=this.m.bitLength();u=new a(2*u*u).toRed(this);while(0!==this.pow(u,f).cmp(c))u.redIAdd(c);var d=this.pow(u,r),h=this.pow(t,r.addn(1).iushrn(1)),l=this.pow(t,r),p=o;while(0!==l.cmp(s)){for(var b=l,v=0;0!==b.cmp(s);v++)b=b.redSqr();n(v<p);var m=this.pow(d,new a(1).iushln(p-v-1));h=h.redMul(m),d=m.redSqr(),l=l.redMul(d),p=v}return h},S.prototype.invm=function(t){var e=t._invmp(this.m);return 0!==e.negative?(e.negative=0,this.imod(e).redNeg()):this.imod(e)},S.prototype.pow=function(t,e){if(e.isZero())return new a(1).toRed(this);if(0===e.cmpn(1))return t.clone();var i=new Array(16);i[0]=new a(1).toRed(this),i[1]=t;for(var n=2;n<i.length;n++)i[n]=this.mul(i[n-1],t);var r=i[0],o=0,s=0,c=e.bitLength()%26;for(0===c&&(c=26),n=e.length-1;n>=0;n--){for(var f=e.words[n],u=c-1;u>=0;u--){var d=f>>u&1;r!==i[0]&&(r=this.sqr(r)),0!==d||0!==o?(o<<=1,o|=d,s++,(4===s||0===n&&0===u)&&(r=this.mul(r,i[o]),s=0,o=0)):s=0}c=26}return r},S.prototype.convertTo=function(t){var e=t.umod(this.m);return e===t?e.clone():e},S.prototype.convertFrom=function(t){var e=t.clone();return e.red=null,e},a.mont=function(t){return new k(t)},r(k,S),k.prototype.convertTo=function(t){return this.imod(t.ushln(this.shift))},k.prototype.convertFrom=function(t){var e=this.imod(t.mul(this.rinv));return e.red=null,e},k.prototype.imul=function(t,e){if(t.isZero()||e.isZero())return t.words[0]=0,t.length=1,t;var i=t.imul(e),n=i.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),r=i.isub(n).iushrn(this.shift),a=r;return r.cmp(this.m)>=0?a=r.isub(this.m):r.cmpn(0)<0&&(a=r.iadd(this.m)),a._forceRed(this)},k.prototype.mul=function(t,e){if(t.isZero()||e.isZero())return new a(0)._forceRed(this);var i=t.mul(e),n=i.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),r=i.isub(n).iushrn(this.shift),o=r;return r.cmp(this.m)>=0?o=r.isub(this.m):r.cmpn(0)<0&&(o=r.iadd(this.m)),o._forceRed(this)},k.prototype.invm=function(t){var e=this.imod(t._invmp(this.m).mul(this.r2));return e._forceRed(this)}})(t,this)}).call(this,i("62e4")(t))},"39f5":function(t,e,i){var n=i("8707").Buffer;function r(t){n.isBuffer(t)||(t=n.from(t));for(var e=t.length/4|0,i=new Array(e),r=0;r<e;r++)i[r]=t.readUInt32BE(4*r);return i}function a(t){for(;0<t.length;t++)t[0]=0}function o(t,e,i,n,r){for(var a,o,s,c,f=i[0],u=i[1],d=i[2],h=i[3],l=t[0]^e[0],p=t[1]^e[1],b=t[2]^e[2],v=t[3]^e[3],m=4,g=1;g<r;g++)a=f[l>>>24]^u[p>>>16&255]^d[b>>>8&255]^h[255&v]^e[m++],o=f[p>>>24]^u[b>>>16&255]^d[v>>>8&255]^h[255&l]^e[m++],s=f[b>>>24]^u[v>>>16&255]^d[l>>>8&255]^h[255&p]^e[m++],c=f[v>>>24]^u[l>>>16&255]^d[p>>>8&255]^h[255&b]^e[m++],l=a,p=o,b=s,v=c;return a=(n[l>>>24]<<24|n[p>>>16&255]<<16|n[b>>>8&255]<<8|n[255&v])^e[m++],o=(n[p>>>24]<<24|n[b>>>16&255]<<16|n[v>>>8&255]<<8|n[255&l])^e[m++],s=(n[b>>>24]<<24|n[v>>>16&255]<<16|n[l>>>8&255]<<8|n[255&p])^e[m++],c=(n[v>>>24]<<24|n[l>>>16&255]<<16|n[p>>>8&255]<<8|n[255&b])^e[m++],a>>>=0,o>>>=0,s>>>=0,c>>>=0,[a,o,s,c]}var s=[0,1,2,4,8,16,32,64,128,27,54],c=function(){for(var t=new Array(256),e=0;e<256;e++)t[e]=e<128?e<<1:e<<1^283;for(var i=[],n=[],r=[[],[],[],[]],a=[[],[],[],[]],o=0,s=0,c=0;c<256;++c){var f=s^s<<1^s<<2^s<<3^s<<4;f=f>>>8^255&f^99,i[o]=f,n[f]=o;var u=t[o],d=t[u],h=t[d],l=257*t[f]^16843008*f;r[0][o]=l<<24|l>>>8,r[1][o]=l<<16|l>>>16,r[2][o]=l<<8|l>>>24,r[3][o]=l,l=16843009*h^65537*d^257*u^16843008*o,a[0][f]=l<<24|l>>>8,a[1][f]=l<<16|l>>>16,a[2][f]=l<<8|l>>>24,a[3][f]=l,0===o?o=s=1:(o=u^t[t[t[h^u]]],s^=t[t[s]])}return{SBOX:i,INV_SBOX:n,SUB_MIX:r,INV_SUB_MIX:a}}();function f(t){this._key=r(t),this._reset()}f.blockSize=16,f.keySize=32,f.prototype.blockSize=f.blockSize,f.prototype.keySize=f.keySize,f.prototype._reset=function(){for(var t=this._key,e=t.length,i=e+6,n=4*(i+1),r=[],a=0;a<e;a++)r[a]=t[a];for(a=e;a<n;a++){var o=r[a-1];a%e===0?(o=o<<8|o>>>24,o=c.SBOX[o>>>24]<<24|c.SBOX[o>>>16&255]<<16|c.SBOX[o>>>8&255]<<8|c.SBOX[255&o],o^=s[a/e|0]<<24):e>6&&a%e===4&&(o=c.SBOX[o>>>24]<<24|c.SBOX[o>>>16&255]<<16|c.SBOX[o>>>8&255]<<8|c.SBOX[255&o]),r[a]=r[a-e]^o}for(var f=[],u=0;u<n;u++){var d=n-u,h=r[d-(u%4?0:4)];f[u]=u<4||d<=4?h:c.INV_SUB_MIX[0][c.SBOX[h>>>24]]^c.INV_SUB_MIX[1][c.SBOX[h>>>16&255]]^c.INV_SUB_MIX[2][c.SBOX[h>>>8&255]]^c.INV_SUB_MIX[3][c.SBOX[255&h]]}this._nRounds=i,this._keySchedule=r,this._invKeySchedule=f},f.prototype.encryptBlockRaw=function(t){return t=r(t),o(t,this._keySchedule,c.SUB_MIX,c.SBOX,this._nRounds)},f.prototype.encryptBlock=function(t){var e=this.encryptBlockRaw(t),i=n.allocUnsafe(16);return i.writeUInt32BE(e[0],0),i.writeUInt32BE(e[1],4),i.writeUInt32BE(e[2],8),i.writeUInt32BE(e[3],12),i},f.prototype.decryptBlock=function(t){t=r(t);var e=t[1];t[1]=t[3],t[3]=e;var i=o(t,this._invKeySchedule,c.INV_SUB_MIX,c.INV_SBOX,this._nRounds),a=n.allocUnsafe(16);return a.writeUInt32BE(i[0],0),a.writeUInt32BE(i[3],4),a.writeUInt32BE(i[2],8),a.writeUInt32BE(i[1],12),a},f.prototype.scrub=function(){a(this._keySchedule),a(this._invKeySchedule),a(this._key)},t.exports.AES=f},"3a7c":function(t,e,i){function n(t){return Object.prototype.toString.call(t)}e.isArray=function(t){return Array.isArray?Array.isArray(t):"[object Array]"===n(t)},e.isBoolean=function(t){return"boolean"===typeof t},e.isNull=function(t){return null===t},e.isNullOrUndefined=function(t){return null==t},e.isNumber=function(t){return"number"===typeof t},e.isString=function(t){return"string"===typeof t},e.isSymbol=function(t){return"symbol"===typeof t},e.isUndefined=function(t){return void 0===t},e.isRegExp=function(t){return"[object RegExp]"===n(t)},e.isObject=function(t){return"object"===typeof t&&null!==t},e.isDate=function(t){return"[object Date]"===n(t)},e.isError=function(t){return"[object Error]"===n(t)||t instanceof Error},e.isFunction=function(t){return"function"===typeof t},e.isPrimitive=function(t){return null===t||"boolean"===typeof t||"number"===typeof t||"string"===typeof t||"symbol"===typeof t||"undefined"===typeof t},e.isBuffer=i("b639").Buffer.isBuffer},"3daf":function(t,e,i){"use strict";var n=i("f3a3"),r=i("399f"),a=i("3fb5"),o=i("ea537"),s=n.assert;function c(t){this.twisted=1!==(0|t.a),this.mOneA=this.twisted&&-1===(0|t.a),this.extended=this.mOneA,o.call(this,"edwards",t),this.a=new r(t.a,16).umod(this.red.m),this.a=this.a.toRed(this.red),this.c=new r(t.c,16).toRed(this.red),this.c2=this.c.redSqr(),this.d=new r(t.d,16).toRed(this.red),this.dd=this.d.redAdd(this.d),s(!this.twisted||0===this.c.fromRed().cmpn(1)),this.oneC=1===(0|t.c)}function f(t,e,i,n,a){o.BasePoint.call(this,t,"projective"),null===e&&null===i&&null===n?(this.x=this.curve.zero,this.y=this.curve.one,this.z=this.curve.one,this.t=this.curve.zero,this.zOne=!0):(this.x=new r(e,16),this.y=new r(i,16),this.z=n?new r(n,16):this.curve.one,this.t=a&&new r(a,16),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.y.red||(this.y=this.y.toRed(this.curve.red)),this.z.red||(this.z=this.z.toRed(this.curve.red)),this.t&&!this.t.red&&(this.t=this.t.toRed(this.curve.red)),this.zOne=this.z===this.curve.one,this.curve.extended&&!this.t&&(this.t=this.x.redMul(this.y),this.zOne||(this.t=this.t.redMul(this.z.redInvm()))))}a(c,o),t.exports=c,c.prototype._mulA=function(t){return this.mOneA?t.redNeg():this.a.redMul(t)},c.prototype._mulC=function(t){return this.oneC?t:this.c.redMul(t)},c.prototype.jpoint=function(t,e,i,n){return this.point(t,e,i,n)},c.prototype.pointFromX=function(t,e){t=new r(t,16),t.red||(t=t.toRed(this.red));var i=t.redSqr(),n=this.c2.redSub(this.a.redMul(i)),a=this.one.redSub(this.c2.redMul(this.d).redMul(i)),o=n.redMul(a.redInvm()),s=o.redSqrt();if(0!==s.redSqr().redSub(o).cmp(this.zero))throw new Error("invalid point");var c=s.fromRed().isOdd();return(e&&!c||!e&&c)&&(s=s.redNeg()),this.point(t,s)},c.prototype.pointFromY=function(t,e){t=new r(t,16),t.red||(t=t.toRed(this.red));var i=t.redSqr(),n=i.redSub(this.c2),a=i.redMul(this.d).redMul(this.c2).redSub(this.a),o=n.redMul(a.redInvm());if(0===o.cmp(this.zero)){if(e)throw new Error("invalid point");return this.point(this.zero,t)}var s=o.redSqrt();if(0!==s.redSqr().redSub(o).cmp(this.zero))throw new Error("invalid point");return s.fromRed().isOdd()!==e&&(s=s.redNeg()),this.point(s,t)},c.prototype.validate=function(t){if(t.isInfinity())return!0;t.normalize();var e=t.x.redSqr(),i=t.y.redSqr(),n=e.redMul(this.a).redAdd(i),r=this.c2.redMul(this.one.redAdd(this.d.redMul(e).redMul(i)));return 0===n.cmp(r)},a(f,o.BasePoint),c.prototype.pointFromJSON=function(t){return f.fromJSON(this,t)},c.prototype.point=function(t,e,i,n){return new f(this,t,e,i,n)},f.fromJSON=function(t,e){return new f(t,e[0],e[1],e[2])},f.prototype.inspect=function(){return this.isInfinity()?"<EC Point Infinity>":"<EC Point x: "+this.x.fromRed().toString(16,2)+" y: "+this.y.fromRed().toString(16,2)+" z: "+this.z.fromRed().toString(16,2)+">"},f.prototype.isInfinity=function(){return 0===this.x.cmpn(0)&&(0===this.y.cmp(this.z)||this.zOne&&0===this.y.cmp(this.curve.c))},f.prototype._extDbl=function(){var t=this.x.redSqr(),e=this.y.redSqr(),i=this.z.redSqr();i=i.redIAdd(i);var n=this.curve._mulA(t),r=this.x.redAdd(this.y).redSqr().redISub(t).redISub(e),a=n.redAdd(e),o=a.redSub(i),s=n.redSub(e),c=r.redMul(o),f=a.redMul(s),u=r.redMul(s),d=o.redMul(a);return this.curve.point(c,f,d,u)},f.prototype._projDbl=function(){var t,e,i,n,r,a,o=this.x.redAdd(this.y).redSqr(),s=this.x.redSqr(),c=this.y.redSqr();if(this.curve.twisted){n=this.curve._mulA(s);var f=n.redAdd(c);this.zOne?(t=o.redSub(s).redSub(c).redMul(f.redSub(this.curve.two)),e=f.redMul(n.redSub(c)),i=f.redSqr().redSub(f).redSub(f)):(r=this.z.redSqr(),a=f.redSub(r).redISub(r),t=o.redSub(s).redISub(c).redMul(a),e=f.redMul(n.redSub(c)),i=f.redMul(a))}else n=s.redAdd(c),r=this.curve._mulC(this.z).redSqr(),a=n.redSub(r).redSub(r),t=this.curve._mulC(o.redISub(n)).redMul(a),e=this.curve._mulC(n).redMul(s.redISub(c)),i=n.redMul(a);return this.curve.point(t,e,i)},f.prototype.dbl=function(){return this.isInfinity()?this:this.curve.extended?this._extDbl():this._projDbl()},f.prototype._extAdd=function(t){var e=this.y.redSub(this.x).redMul(t.y.redSub(t.x)),i=this.y.redAdd(this.x).redMul(t.y.redAdd(t.x)),n=this.t.redMul(this.curve.dd).redMul(t.t),r=this.z.redMul(t.z.redAdd(t.z)),a=i.redSub(e),o=r.redSub(n),s=r.redAdd(n),c=i.redAdd(e),f=a.redMul(o),u=s.redMul(c),d=a.redMul(c),h=o.redMul(s);return this.curve.point(f,u,h,d)},f.prototype._projAdd=function(t){var e,i,n=this.z.redMul(t.z),r=n.redSqr(),a=this.x.redMul(t.x),o=this.y.redMul(t.y),s=this.curve.d.redMul(a).redMul(o),c=r.redSub(s),f=r.redAdd(s),u=this.x.redAdd(this.y).redMul(t.x.redAdd(t.y)).redISub(a).redISub(o),d=n.redMul(c).redMul(u);return this.curve.twisted?(e=n.redMul(f).redMul(o.redSub(this.curve._mulA(a))),i=c.redMul(f)):(e=n.redMul(f).redMul(o.redSub(a)),i=this.curve._mulC(c).redMul(f)),this.curve.point(d,e,i)},f.prototype.add=function(t){return this.isInfinity()?t:t.isInfinity()?this:this.curve.extended?this._extAdd(t):this._projAdd(t)},f.prototype.mul=function(t){return this._hasDoubles(t)?this.curve._fixedNafMul(this,t):this.curve._wnafMul(this,t)},f.prototype.mulAdd=function(t,e,i){return this.curve._wnafMulAdd(1,[this,e],[t,i],2,!1)},f.prototype.jmulAdd=function(t,e,i){return this.curve._wnafMulAdd(1,[this,e],[t,i],2,!0)},f.prototype.normalize=function(){if(this.zOne)return this;var t=this.z.redInvm();return this.x=this.x.redMul(t),this.y=this.y.redMul(t),this.t&&(this.t=this.t.redMul(t)),this.z=this.curve.one,this.zOne=!0,this},f.prototype.neg=function(){return this.curve.point(this.x.redNeg(),this.y,this.z,this.t&&this.t.redNeg())},f.prototype.getX=function(){return this.normalize(),this.x.fromRed()},f.prototype.getY=function(){return this.normalize(),this.y.fromRed()},f.prototype.eq=function(t){return this===t||0===this.getX().cmp(t.getX())&&0===this.getY().cmp(t.getY())},f.prototype.eqXToP=function(t){var e=t.toRed(this.curve.red).redMul(this.z);if(0===this.x.cmp(e))return!0;for(var i=t.clone(),n=this.curve.redN.redMul(this.z);;){if(i.iadd(this.curve.n),i.cmp(this.curve.p)>=0)return!1;if(e.redIAdd(n),0===this.x.cmp(e))return!0}},f.prototype.toP=f.prototype.normalize,f.prototype.mixedAdd=f.prototype.add},"3dbb":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.auction-bid-list > uni-view[data-v-313b0874]::after{content:"";display:block;height:%?2?%;background:linear-gradient(270deg,rgba(232,4,4,.07),rgba(232,4,4,.6) 33%,rgba(232,4,4,.76) 52%,rgba(232,4,4,.6) 67%,rgba(232,4,4,.07));opacity:.5}.auction-bid-list.is-from-detail > uni-view[data-v-313b0874]:last-of-type::after{display:none}',""]),t.exports=e},"3e0d":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{goods:{type:Object,default:function(){return{}}}}};e.default=n},"3f62":function(t,e,i){var n=i("8707").Buffer,r=n.alloc(16,0);function a(t){var e=n.allocUnsafe(16);return e.writeUInt32BE(t[0]>>>0,0),e.writeUInt32BE(t[1]>>>0,4),e.writeUInt32BE(t[2]>>>0,8),e.writeUInt32BE(t[3]>>>0,12),e}function o(t){this.h=t,this.state=n.alloc(16,0),this.cache=n.allocUnsafe(0)}o.prototype.ghash=function(t){var e=-1;while(++e<t.length)this.state[e]^=t[e];this._multiply()},o.prototype._multiply=function(){var t,e,i,n=function(t){return[t.readUInt32BE(0),t.readUInt32BE(4),t.readUInt32BE(8),t.readUInt32BE(12)]}(this.h),r=[0,0,0,0],o=-1;while(++o<128){for(e=0!==(this.state[~~(o/8)]&1<<7-o%8),e&&(r[0]^=n[0],r[1]^=n[1],r[2]^=n[2],r[3]^=n[3]),i=0!==(1&n[3]),t=3;t>0;t--)n[t]=n[t]>>>1|(1&n[t-1])<<31;n[0]=n[0]>>>1,i&&(n[0]=n[0]^225<<24)}this.state=a(r)},o.prototype.update=function(t){var e;this.cache=n.concat([this.cache,t]);while(this.cache.length>=16)e=this.cache.slice(0,16),this.cache=this.cache.slice(16),this.ghash(e)},o.prototype.final=function(t,e){return this.cache.length&&this.ghash(n.concat([this.cache,r],16)),this.ghash(a([0,t,0,e])),this.state},t.exports=o},"3f63":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={vhImage:i("ce7c").default,CommentContent:i("2b7a").default,CommentTime:i("b149").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",t._l(t.list,(function(e,n){return i("v-uni-view",{key:e.id},[n?i("v-uni-view",{staticClass:"mt-32 mb-32 h-02 bg-eeeeee"}):t._e(),i("v-uni-view",{staticClass:"flex-s-c",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.jump.jumpUserHome(e.uid,t.$vhFrom)}}},[i("v-uni-view",{staticClass:"p-rela w-60 h-60"},[i("vh-image",{attrs:{"loading-type":4,src:e.avatar_image,width:60,height:60,shape:"circle"}}),e.certified_info?i("v-uni-image",{staticClass:"p-abso bottom-n-02 right-0 w-24 h-26",attrs:{src:t.ossIcon("/comm/certified_24_26.png")}}):t._e()],1),i("v-uni-view",{staticClass:"flex-s-c ml-12 o-hid"},[i("v-uni-text",{staticClass:"font-wei-600 font-26 text-3 l-h-36 text-hidden"},[t._v(t._s(e.nickname))]),2==e.user_type?i("v-uni-view",{staticClass:"flex-shrink flex-c-c ml-12 w-56 h-28 font-wei-500 font-22 text-ffffff bg-e80404 b-rad-04 w-s-now"},[t._v("官方")]):[i("v-uni-view",{staticClass:"flex-shrink flex-c-c ml-12 w-80 h-32 font-wei-600 font-22 text-ffffff bg-ff9127 b-rad-18"},[i("v-uni-text",{staticClass:"lv-text"},[t._v("LV."+t._s(e.user_level))])],1),e.is_buy||e.is_deposit?i("v-uni-view",{staticClass:"flex-shrink flex-c-c ml-06 w-72 h-32 font-wei-500 font-22 text-ffffff bg-e80404 b-rad-18 w-s-now"},[i("v-uni-text",{staticClass:"lv-text"},[t._v(t._s(e.is_buy?"已购":"已订"))])],1):t._e()]],2)],1),i("v-uni-view",{staticClass:"mt-12"},[i("CommentContent",{attrs:{item:e}}),i("v-uni-view",{staticClass:"mt-20"},[i("CommentTime",{attrs:{item:e},on:{enjoy:function(e){arguments[0]=e=t.$handleEvent(e),t.onEnjoy.apply(void 0,arguments)},reply:function(e){arguments[0]=e=t.$handleEvent(e),t.onReply.apply(void 0,arguments)}}})],1)],1),e.$replysLen?i("v-uni-view",{staticClass:"mt-20 ptb-28-plr-24 bg-f9f9f9 b-rad-08"},[t._l(e.replys.slice(0,e.$isShowAllReply?e.$replysLen:t.showReplyLen),(function(n,r){return i("v-uni-view",{key:"reply-"+n.id,class:r?"mt-32":""},[i("v-uni-view",{staticClass:"d-flex"},[i("v-uni-view",{staticClass:"p-rela w-42 h-42"},[i("vh-image",{attrs:{"loading-type":4,src:n.avatar_image,width:42,height:42,shape:"circle"}}),n.certified_info?i("v-uni-image",{staticClass:"p-abso bottom-0 right-n-10 w-24 h-26",attrs:{src:t.ossIcon("/comm/certified_24_26.png")}}):t._e()],1),i("v-uni-view",{staticClass:"flex-c-c ml-12"},[i("v-uni-view",{staticClass:"w-max-194 font-wei-500 font-22 text-9 l-h-34 text-hidden",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.jump.jumpUserHome(n.uid,t.$vhFrom)}}},[t._v(t._s(n.nickname))]),2==n.user_type?i("v-uni-view",{staticClass:"p-rela flex-c-c ml-10 w-40 h-22 font-wei-600 font-16 text-ffffff bg-e80404 b-rad-04 w-s-now"},[t._v("官方")]):[i("v-uni-view",{staticClass:"p-rela flex-c-c ml-10 w-54 h-22 font-wei-500 font-16 text-fca72e w-s-now"},[t._v("LV."+t._s(n.user_level)),i("v-uni-view",{staticStyle:{position:"absolute",width:"200%",height:"200%","border-radius":"8rpx",border:"1px solid #ff9127",transform:"scale(0.5)"}})],1),n.is_buy?i("v-uni-view",{staticClass:"p-rela flex-c-c ml-06 w-44 h-22 font-14 text-e80404 w-s-now"},[t._v("已购"),i("v-uni-view",{staticStyle:{position:"absolute",width:"200%",height:"200%","border-radius":"8rpx",border:"1px solid #ff8585",transform:"scale(0.5)"}})],1):t._e()],e.id!==n.pid&&n.p_nickname?i("v-uni-view",{staticClass:"flex-c-c ml-12"},[i("v-uni-image",{staticClass:"mr-12 w-14 h-18",attrs:{src:t.ossIcon("/comm/triangle_r_14_18.png")}}),i("v-uni-view",{staticClass:"w-max-194 font-wei-500 font-22 text-9 l-h-34 text-hidden",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.jump.jumpUserHome(n.p_uid,t.$vhFrom)}}},[t._v(t._s(n.p_nickname))])],1):t._e()],2)],1),i("v-uni-view",{staticClass:"ml-54"},[i("CommentContent",{attrs:{item:n}}),i("v-uni-view",{staticClass:"mt-20"},[i("CommentTime",{attrs:{item:n},on:{enjoy:function(e){arguments[0]=e=t.$handleEvent(e),t.onEnjoy.apply(void 0,arguments)},reply:function(e){arguments[0]=e=t.$handleEvent(e),t.onReply.apply(void 0,arguments)}}})],1)],1)],1)})),e.$replysLen>t.showReplyLen?i("v-uni-view",{staticClass:"flex-s-c mt-20 ml-54",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.onToggleReply(e)}}},[i("v-uni-text",{staticClass:"mr-08 font-28 text-9 l-h-40"},[t._v(t._s(e.$isShowAllReply?"收起":"展开全部回复"))]),i("v-uni-image",{staticClass:"w-20 h-12 tran-2",class:e.$isShowAllReply?"t-ro-n-180":"",attrs:{src:t.ossIcon("/comm/arrow_b_20_12.png")}})],1):t._e()],2):t._e()],1)})),1)},a=[]},"3fb5":function(t,e){"function"===typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var i=function(){};i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t}}},4:function(t,e){},4053:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,n.default)(t)};var n=function(t){return t&&t.__esModule?t:{default:t}}(i("b680"))},"409b":function(t,e){t.exports={doubles:{step:4,points:[["e60fce93b59e9ec53011aabc21c23e97b2a31369b87a5ae9c44ee89e2a6dec0a","f7e3507399e595929db99f34f57937101296891e44d23f0be1f32cce69616821"],["8282263212c609d9ea2a6e3e172de238d8c39cabd5ac1ca10646e23fd5f51508","11f8a8098557dfe45e8256e830b60ace62d613ac2f7b17bed31b6eaff6e26caf"],["175e159f728b865a72f99cc6c6fc846de0b93833fd2222ed73fce5b551e5b739","d3506e0d9e3c79eba4ef97a51ff71f5eacb5955add24345c6efa6ffee9fed695"],["363d90d447b00c9c99ceac05b6262ee053441c7e55552ffe526bad8f83ff4640","4e273adfc732221953b445397f3363145b9a89008199ecb62003c7f3bee9de9"],["8b4b5f165df3c2be8c6244b5b745638843e4a781a15bcd1b69f79a55dffdf80c","4aad0a6f68d308b4b3fbd7813ab0da04f9e336546162ee56b3eff0c65fd4fd36"],["723cbaa6e5db996d6bf771c00bd548c7b700dbffa6c0e77bcb6115925232fcda","96e867b5595cc498a921137488824d6e2660a0653779494801dc069d9eb39f5f"],["eebfa4d493bebf98ba5feec812c2d3b50947961237a919839a533eca0e7dd7fa","5d9a8ca3970ef0f269ee7edaf178089d9ae4cdc3a711f712ddfd4fdae1de8999"],["100f44da696e71672791d0a09b7bde459f1215a29b3c03bfefd7835b39a48db0","cdd9e13192a00b772ec8f3300c090666b7ff4a18ff5195ac0fbd5cd62bc65a09"],["e1031be262c7ed1b1dc9227a4a04c017a77f8d4464f3b3852c8acde6e534fd2d","9d7061928940405e6bb6a4176597535af292dd419e1ced79a44f18f29456a00d"],["feea6cae46d55b530ac2839f143bd7ec5cf8b266a41d6af52d5e688d9094696d","e57c6b6c97dce1bab06e4e12bf3ecd5c981c8957cc41442d3155debf18090088"],["da67a91d91049cdcb367be4be6ffca3cfeed657d808583de33fa978bc1ec6cb1","9bacaa35481642bc41f463f7ec9780e5dec7adc508f740a17e9ea8e27a68be1d"],["53904faa0b334cdda6e000935ef22151ec08d0f7bb11069f57545ccc1a37b7c0","5bc087d0bc80106d88c9eccac20d3c1c13999981e14434699dcb096b022771c8"],["8e7bcd0bd35983a7719cca7764ca906779b53a043a9b8bcaeff959f43ad86047","10b7770b2a3da4b3940310420ca9514579e88e2e47fd68b3ea10047e8460372a"],["385eed34c1cdff21e6d0818689b81bde71a7f4f18397e6690a841e1599c43862","283bebc3e8ea23f56701de19e9ebf4576b304eec2086dc8cc0458fe5542e5453"],["6f9d9b803ecf191637c73a4413dfa180fddf84a5947fbc9c606ed86c3fac3a7","7c80c68e603059ba69b8e2a30e45c4d47ea4dd2f5c281002d86890603a842160"],["3322d401243c4e2582a2147c104d6ecbf774d163db0f5e5313b7e0e742d0e6bd","56e70797e9664ef5bfb019bc4ddaf9b72805f63ea2873af624f3a2e96c28b2a0"],["85672c7d2de0b7da2bd1770d89665868741b3f9af7643397721d74d28134ab83","7c481b9b5b43b2eb6374049bfa62c2e5e77f17fcc5298f44c8e3094f790313a6"],["948bf809b1988a46b06c9f1919413b10f9226c60f668832ffd959af60c82a0a","53a562856dcb6646dc6b74c5d1c3418c6d4dff08c97cd2bed4cb7f88d8c8e589"],["6260ce7f461801c34f067ce0f02873a8f1b0e44dfc69752accecd819f38fd8e8","bc2da82b6fa5b571a7f09049776a1ef7ecd292238051c198c1a84e95b2b4ae17"],["e5037de0afc1d8d43d8348414bbf4103043ec8f575bfdc432953cc8d2037fa2d","4571534baa94d3b5f9f98d09fb990bddbd5f5b03ec481f10e0e5dc841d755bda"],["e06372b0f4a207adf5ea905e8f1771b4e7e8dbd1c6a6c5b725866a0ae4fce725","7a908974bce18cfe12a27bb2ad5a488cd7484a7787104870b27034f94eee31dd"],["213c7a715cd5d45358d0bbf9dc0ce02204b10bdde2a3f58540ad6908d0559754","4b6dad0b5ae462507013ad06245ba190bb4850f5f36a7eeddff2c27534b458f2"],["4e7c272a7af4b34e8dbb9352a5419a87e2838c70adc62cddf0cc3a3b08fbd53c","17749c766c9d0b18e16fd09f6def681b530b9614bff7dd33e0b3941817dcaae6"],["fea74e3dbe778b1b10f238ad61686aa5c76e3db2be43057632427e2840fb27b6","6e0568db9b0b13297cf674deccb6af93126b596b973f7b77701d3db7f23cb96f"],["76e64113f677cf0e10a2570d599968d31544e179b760432952c02a4417bdde39","c90ddf8dee4e95cf577066d70681f0d35e2a33d2b56d2032b4b1752d1901ac01"],["c738c56b03b2abe1e8281baa743f8f9a8f7cc643df26cbee3ab150242bcbb891","893fb578951ad2537f718f2eacbfbbbb82314eef7880cfe917e735d9699a84c3"],["d895626548b65b81e264c7637c972877d1d72e5f3a925014372e9f6588f6c14b","febfaa38f2bc7eae728ec60818c340eb03428d632bb067e179363ed75d7d991f"],["b8da94032a957518eb0f6433571e8761ceffc73693e84edd49150a564f676e03","2804dfa44805a1e4d7c99cc9762808b092cc584d95ff3b511488e4e74efdf6e7"],["e80fea14441fb33a7d8adab9475d7fab2019effb5156a792f1a11778e3c0df5d","eed1de7f638e00771e89768ca3ca94472d155e80af322ea9fcb4291b6ac9ec78"],["a301697bdfcd704313ba48e51d567543f2a182031efd6915ddc07bbcc4e16070","7370f91cfb67e4f5081809fa25d40f9b1735dbf7c0a11a130c0d1a041e177ea1"],["90ad85b389d6b936463f9d0512678de208cc330b11307fffab7ac63e3fb04ed4","e507a3620a38261affdcbd9427222b839aefabe1582894d991d4d48cb6ef150"],["8f68b9d2f63b5f339239c1ad981f162ee88c5678723ea3351b7b444c9ec4c0da","662a9f2dba063986de1d90c2b6be215dbbea2cfe95510bfdf23cbf79501fff82"],["e4f3fb0176af85d65ff99ff9198c36091f48e86503681e3e6686fd5053231e11","1e63633ad0ef4f1c1661a6d0ea02b7286cc7e74ec951d1c9822c38576feb73bc"],["8c00fa9b18ebf331eb961537a45a4266c7034f2f0d4e1d0716fb6eae20eae29e","efa47267fea521a1a9dc343a3736c974c2fadafa81e36c54e7d2a4c66702414b"],["e7a26ce69dd4829f3e10cec0a9e98ed3143d084f308b92c0997fddfc60cb3e41","2a758e300fa7984b471b006a1aafbb18d0a6b2c0420e83e20e8a9421cf2cfd51"],["b6459e0ee3662ec8d23540c223bcbdc571cbcb967d79424f3cf29eb3de6b80ef","67c876d06f3e06de1dadf16e5661db3c4b3ae6d48e35b2ff30bf0b61a71ba45"],["d68a80c8280bb840793234aa118f06231d6f1fc67e73c5a5deda0f5b496943e8","db8ba9fff4b586d00c4b1f9177b0e28b5b0e7b8f7845295a294c84266b133120"],["324aed7df65c804252dc0270907a30b09612aeb973449cea4095980fc28d3d5d","648a365774b61f2ff130c0c35aec1f4f19213b0c7e332843967224af96ab7c84"],["4df9c14919cde61f6d51dfdbe5fee5dceec4143ba8d1ca888e8bd373fd054c96","35ec51092d8728050974c23a1d85d4b5d506cdc288490192ebac06cad10d5d"],["9c3919a84a474870faed8a9c1cc66021523489054d7f0308cbfc99c8ac1f98cd","ddb84f0f4a4ddd57584f044bf260e641905326f76c64c8e6be7e5e03d4fc599d"],["6057170b1dd12fdf8de05f281d8e06bb91e1493a8b91d4cc5a21382120a959e5","9a1af0b26a6a4807add9a2daf71df262465152bc3ee24c65e899be932385a2a8"],["a576df8e23a08411421439a4518da31880cef0fba7d4df12b1a6973eecb94266","40a6bf20e76640b2c92b97afe58cd82c432e10a7f514d9f3ee8be11ae1b28ec8"],["7778a78c28dec3e30a05fe9629de8c38bb30d1f5cf9a3a208f763889be58ad71","34626d9ab5a5b22ff7098e12f2ff580087b38411ff24ac563b513fc1fd9f43ac"],["928955ee637a84463729fd30e7afd2ed5f96274e5ad7e5cb09eda9c06d903ac","c25621003d3f42a827b78a13093a95eeac3d26efa8a8d83fc5180e935bcd091f"],["85d0fef3ec6db109399064f3a0e3b2855645b4a907ad354527aae75163d82751","1f03648413a38c0be29d496e582cf5663e8751e96877331582c237a24eb1f962"],["ff2b0dce97eece97c1c9b6041798b85dfdfb6d8882da20308f5404824526087e","493d13fef524ba188af4c4dc54d07936c7b7ed6fb90e2ceb2c951e01f0c29907"],["827fbbe4b1e880ea9ed2b2e6301b212b57f1ee148cd6dd28780e5e2cf856e241","c60f9c923c727b0b71bef2c67d1d12687ff7a63186903166d605b68baec293ec"],["eaa649f21f51bdbae7be4ae34ce6e5217a58fdce7f47f9aa7f3b58fa2120e2b3","be3279ed5bbbb03ac69a80f89879aa5a01a6b965f13f7e59d47a5305ba5ad93d"],["e4a42d43c5cf169d9391df6decf42ee541b6d8f0c9a137401e23632dda34d24f","4d9f92e716d1c73526fc99ccfb8ad34ce886eedfa8d8e4f13a7f7131deba9414"],["1ec80fef360cbdd954160fadab352b6b92b53576a88fea4947173b9d4300bf19","aeefe93756b5340d2f3a4958a7abbf5e0146e77f6295a07b671cdc1cc107cefd"],["146a778c04670c2f91b00af4680dfa8bce3490717d58ba889ddb5928366642be","b318e0ec3354028add669827f9d4b2870aaa971d2f7e5ed1d0b297483d83efd0"],["fa50c0f61d22e5f07e3acebb1aa07b128d0012209a28b9776d76a8793180eef9","6b84c6922397eba9b72cd2872281a68a5e683293a57a213b38cd8d7d3f4f2811"],["da1d61d0ca721a11b1a5bf6b7d88e8421a288ab5d5bba5220e53d32b5f067ec2","8157f55a7c99306c79c0766161c91e2966a73899d279b48a655fba0f1ad836f1"],["a8e282ff0c9706907215ff98e8fd416615311de0446f1e062a73b0610d064e13","7f97355b8db81c09abfb7f3c5b2515888b679a3e50dd6bd6cef7c73111f4cc0c"],["174a53b9c9a285872d39e56e6913cab15d59b1fa512508c022f382de8319497c","ccc9dc37abfc9c1657b4155f2c47f9e6646b3a1d8cb9854383da13ac079afa73"],["959396981943785c3d3e57edf5018cdbe039e730e4918b3d884fdff09475b7ba","2e7e552888c331dd8ba0386a4b9cd6849c653f64c8709385e9b8abf87524f2fd"],["d2a63a50ae401e56d645a1153b109a8fcca0a43d561fba2dbb51340c9d82b151","e82d86fb6443fcb7565aee58b2948220a70f750af484ca52d4142174dcf89405"],["64587e2335471eb890ee7896d7cfdc866bacbdbd3839317b3436f9b45617e073","d99fcdd5bf6902e2ae96dd6447c299a185b90a39133aeab358299e5e9faf6589"],["8481bde0e4e4d885b3a546d3e549de042f0aa6cea250e7fd358d6c86dd45e458","38ee7b8cba5404dd84a25bf39cecb2ca900a79c42b262e556d64b1b59779057e"],["13464a57a78102aa62b6979ae817f4637ffcfed3c4b1ce30bcd6303f6caf666b","69be159004614580ef7e433453ccb0ca48f300a81d0942e13f495a907f6ecc27"],["bc4a9df5b713fe2e9aef430bcc1dc97a0cd9ccede2f28588cada3a0d2d83f366","d3a81ca6e785c06383937adf4b798caa6e8a9fbfa547b16d758d666581f33c1"],["8c28a97bf8298bc0d23d8c749452a32e694b65e30a9472a3954ab30fe5324caa","40a30463a3305193378fedf31f7cc0eb7ae784f0451cb9459e71dc73cbef9482"],["8ea9666139527a8c1dd94ce4f071fd23c8b350c5a4bb33748c4ba111faccae0","620efabbc8ee2782e24e7c0cfb95c5d735b783be9cf0f8e955af34a30e62b945"],["dd3625faef5ba06074669716bbd3788d89bdde815959968092f76cc4eb9a9787","7a188fa3520e30d461da2501045731ca941461982883395937f68d00c644a573"],["f710d79d9eb962297e4f6232b40e8f7feb2bc63814614d692c12de752408221e","ea98e67232d3b3295d3b535532115ccac8612c721851617526ae47a9c77bfc82"]]},naf:{wnd:7,points:[["f9308a019258c31049344f85f89d5229b531c845836f99b08601f113bce036f9","388f7b0f632de8140fe337e62a37f3566500a99934c2231b6cb9fd7584b8e672"],["2f8bde4d1a07209355b4a7250a5c5128e88b84bddc619ab7cba8d569b240efe4","d8ac222636e5e3d6d4dba9dda6c9c426f788271bab0d6840dca87d3aa6ac62d6"],["5cbdf0646e5db4eaa398f365f2ea7a0e3d419b7e0330e39ce92bddedcac4f9bc","6aebca40ba255960a3178d6d861a54dba813d0b813fde7b5a5082628087264da"],["acd484e2f0c7f65309ad178a9f559abde09796974c57e714c35f110dfc27ccbe","cc338921b0a7d9fd64380971763b61e9add888a4375f8e0f05cc262ac64f9c37"],["774ae7f858a9411e5ef4246b70c65aac5649980be5c17891bbec17895da008cb","d984a032eb6b5e190243dd56d7b7b365372db1e2dff9d6a8301d74c9c953c61b"],["f28773c2d975288bc7d1d205c3748651b075fbc6610e58cddeeddf8f19405aa8","ab0902e8d880a89758212eb65cdaf473a1a06da521fa91f29b5cb52db03ed81"],["d7924d4f7d43ea965a465ae3095ff41131e5946f3c85f79e44adbcf8e27e080e","581e2872a86c72a683842ec228cc6defea40af2bd896d3a5c504dc9ff6a26b58"],["defdea4cdb677750a420fee807eacf21eb9898ae79b9768766e4faa04a2d4a34","4211ab0694635168e997b0ead2a93daeced1f4a04a95c0f6cfb199f69e56eb77"],["2b4ea0a797a443d293ef5cff444f4979f06acfebd7e86d277475656138385b6c","85e89bc037945d93b343083b5a1c86131a01f60c50269763b570c854e5c09b7a"],["352bbf4a4cdd12564f93fa332ce333301d9ad40271f8107181340aef25be59d5","321eb4075348f534d59c18259dda3e1f4a1b3b2e71b1039c67bd3d8bcf81998c"],["2fa2104d6b38d11b0230010559879124e42ab8dfeff5ff29dc9cdadd4ecacc3f","2de1068295dd865b64569335bd5dd80181d70ecfc882648423ba76b532b7d67"],["9248279b09b4d68dab21a9b066edda83263c3d84e09572e269ca0cd7f5453714","73016f7bf234aade5d1aa71bdea2b1ff3fc0de2a887912ffe54a32ce97cb3402"],["daed4f2be3a8bf278e70132fb0beb7522f570e144bf615c07e996d443dee8729","a69dce4a7d6c98e8d4a1aca87ef8d7003f83c230f3afa726ab40e52290be1c55"],["c44d12c7065d812e8acf28d7cbb19f9011ecd9e9fdf281b0e6a3b5e87d22e7db","2119a460ce326cdc76c45926c982fdac0e106e861edf61c5a039063f0e0e6482"],["6a245bf6dc698504c89a20cfded60853152b695336c28063b61c65cbd269e6b4","e022cf42c2bd4a708b3f5126f16a24ad8b33ba48d0423b6efd5e6348100d8a82"],["1697ffa6fd9de627c077e3d2fe541084ce13300b0bec1146f95ae57f0d0bd6a5","b9c398f186806f5d27561506e4557433a2cf15009e498ae7adee9d63d01b2396"],["605bdb019981718b986d0f07e834cb0d9deb8360ffb7f61df982345ef27a7479","2972d2de4f8d20681a78d93ec96fe23c26bfae84fb14db43b01e1e9056b8c49"],["62d14dab4150bf497402fdc45a215e10dcb01c354959b10cfe31c7e9d87ff33d","80fc06bd8cc5b01098088a1950eed0db01aa132967ab472235f5642483b25eaf"],["80c60ad0040f27dade5b4b06c408e56b2c50e9f56b9b8b425e555c2f86308b6f","1c38303f1cc5c30f26e66bad7fe72f70a65eed4cbe7024eb1aa01f56430bd57a"],["7a9375ad6167ad54aa74c6348cc54d344cc5dc9487d847049d5eabb0fa03c8fb","d0e3fa9eca8726909559e0d79269046bdc59ea10c70ce2b02d499ec224dc7f7"],["d528ecd9b696b54c907a9ed045447a79bb408ec39b68df504bb51f459bc3ffc9","eecf41253136e5f99966f21881fd656ebc4345405c520dbc063465b521409933"],["49370a4b5f43412ea25f514e8ecdad05266115e4a7ecb1387231808f8b45963","758f3f41afd6ed428b3081b0512fd62a54c3f3afbb5b6764b653052a12949c9a"],["77f230936ee88cbbd73df930d64702ef881d811e0e1498e2f1c13eb1fc345d74","958ef42a7886b6400a08266e9ba1b37896c95330d97077cbbe8eb3c7671c60d6"],["f2dac991cc4ce4b9ea44887e5c7c0bce58c80074ab9d4dbaeb28531b7739f530","e0dedc9b3b2f8dad4da1f32dec2531df9eb5fbeb0598e4fd1a117dba703a3c37"],["463b3d9f662621fb1b4be8fbbe2520125a216cdfc9dae3debcba4850c690d45b","5ed430d78c296c3543114306dd8622d7c622e27c970a1de31cb377b01af7307e"],["f16f804244e46e2a09232d4aff3b59976b98fac14328a2d1a32496b49998f247","cedabd9b82203f7e13d206fcdf4e33d92a6c53c26e5cce26d6579962c4e31df6"],["caf754272dc84563b0352b7a14311af55d245315ace27c65369e15f7151d41d1","cb474660ef35f5f2a41b643fa5e460575f4fa9b7962232a5c32f908318a04476"],["2600ca4b282cb986f85d0f1709979d8b44a09c07cb86d7c124497bc86f082120","4119b88753c15bd6a693b03fcddbb45d5ac6be74ab5f0ef44b0be9475a7e4b40"],["7635ca72d7e8432c338ec53cd12220bc01c48685e24f7dc8c602a7746998e435","91b649609489d613d1d5e590f78e6d74ecfc061d57048bad9e76f302c5b9c61"],["754e3239f325570cdbbf4a87deee8a66b7f2b33479d468fbc1a50743bf56cc18","673fb86e5bda30fb3cd0ed304ea49a023ee33d0197a695d0c5d98093c536683"],["e3e6bd1071a1e96aff57859c82d570f0330800661d1c952f9fe2694691d9b9e8","59c9e0bba394e76f40c0aa58379a3cb6a5a2283993e90c4167002af4920e37f5"],["186b483d056a033826ae73d88f732985c4ccb1f32ba35f4b4cc47fdcf04aa6eb","3b952d32c67cf77e2e17446e204180ab21fb8090895138b4a4a797f86e80888b"],["df9d70a6b9876ce544c98561f4be4f725442e6d2b737d9c91a8321724ce0963f","55eb2dafd84d6ccd5f862b785dc39d4ab157222720ef9da217b8c45cf2ba2417"],["5edd5cc23c51e87a497ca815d5dce0f8ab52554f849ed8995de64c5f34ce7143","efae9c8dbc14130661e8cec030c89ad0c13c66c0d17a2905cdc706ab7399a868"],["290798c2b6476830da12fe02287e9e777aa3fba1c355b17a722d362f84614fba","e38da76dcd440621988d00bcf79af25d5b29c094db2a23146d003afd41943e7a"],["af3c423a95d9f5b3054754efa150ac39cd29552fe360257362dfdecef4053b45","f98a3fd831eb2b749a93b0e6f35cfb40c8cd5aa667a15581bc2feded498fd9c6"],["766dbb24d134e745cccaa28c99bf274906bb66b26dcf98df8d2fed50d884249a","744b1152eacbe5e38dcc887980da38b897584a65fa06cedd2c924f97cbac5996"],["59dbf46f8c94759ba21277c33784f41645f7b44f6c596a58ce92e666191abe3e","c534ad44175fbc300f4ea6ce648309a042ce739a7919798cd85e216c4a307f6e"],["f13ada95103c4537305e691e74e9a4a8dd647e711a95e73cb62dc6018cfd87b8","e13817b44ee14de663bf4bc808341f326949e21a6a75c2570778419bdaf5733d"],["7754b4fa0e8aced06d4167a2c59cca4cda1869c06ebadfb6488550015a88522c","30e93e864e669d82224b967c3020b8fa8d1e4e350b6cbcc537a48b57841163a2"],["948dcadf5990e048aa3874d46abef9d701858f95de8041d2a6828c99e2262519","e491a42537f6e597d5d28a3224b1bc25df9154efbd2ef1d2cbba2cae5347d57e"],["7962414450c76c1689c7b48f8202ec37fb224cf5ac0bfa1570328a8a3d7c77ab","100b610ec4ffb4760d5c1fc133ef6f6b12507a051f04ac5760afa5b29db83437"],["3514087834964b54b15b160644d915485a16977225b8847bb0dd085137ec47ca","ef0afbb2056205448e1652c48e8127fc6039e77c15c2378b7e7d15a0de293311"],["d3cc30ad6b483e4bc79ce2c9dd8bc54993e947eb8df787b442943d3f7b527eaf","8b378a22d827278d89c5e9be8f9508ae3c2ad46290358630afb34db04eede0a4"],["1624d84780732860ce1c78fcbfefe08b2b29823db913f6493975ba0ff4847610","68651cf9b6da903e0914448c6cd9d4ca896878f5282be4c8cc06e2a404078575"],["733ce80da955a8a26902c95633e62a985192474b5af207da6df7b4fd5fc61cd4","f5435a2bd2badf7d485a4d8b8db9fcce3e1ef8e0201e4578c54673bc1dc5ea1d"],["15d9441254945064cf1a1c33bbd3b49f8966c5092171e699ef258dfab81c045c","d56eb30b69463e7234f5137b73b84177434800bacebfc685fc37bbe9efe4070d"],["a1d0fcf2ec9de675b612136e5ce70d271c21417c9d2b8aaaac138599d0717940","edd77f50bcb5a3cab2e90737309667f2641462a54070f3d519212d39c197a629"],["e22fbe15c0af8ccc5780c0735f84dbe9a790badee8245c06c7ca37331cb36980","a855babad5cd60c88b430a69f53a1a7a38289154964799be43d06d77d31da06"],["311091dd9860e8e20ee13473c1155f5f69635e394704eaa74009452246cfa9b3","66db656f87d1f04fffd1f04788c06830871ec5a64feee685bd80f0b1286d8374"],["34c1fd04d301be89b31c0442d3e6ac24883928b45a9340781867d4232ec2dbdf","9414685e97b1b5954bd46f730174136d57f1ceeb487443dc5321857ba73abee"],["f219ea5d6b54701c1c14de5b557eb42a8d13f3abbcd08affcc2a5e6b049b8d63","4cb95957e83d40b0f73af4544cccf6b1f4b08d3c07b27fb8d8c2962a400766d1"],["d7b8740f74a8fbaab1f683db8f45de26543a5490bca627087236912469a0b448","fa77968128d9c92ee1010f337ad4717eff15db5ed3c049b3411e0315eaa4593b"],["32d31c222f8f6f0ef86f7c98d3a3335ead5bcd32abdd94289fe4d3091aa824bf","5f3032f5892156e39ccd3d7915b9e1da2e6dac9e6f26e961118d14b8462e1661"],["7461f371914ab32671045a155d9831ea8793d77cd59592c4340f86cbc18347b5","8ec0ba238b96bec0cbdddcae0aa442542eee1ff50c986ea6b39847b3cc092ff6"],["ee079adb1df1860074356a25aa38206a6d716b2c3e67453d287698bad7b2b2d6","8dc2412aafe3be5c4c5f37e0ecc5f9f6a446989af04c4e25ebaac479ec1c8c1e"],["16ec93e447ec83f0467b18302ee620f7e65de331874c9dc72bfd8616ba9da6b5","5e4631150e62fb40d0e8c2a7ca5804a39d58186a50e497139626778e25b0674d"],["eaa5f980c245f6f038978290afa70b6bd8855897f98b6aa485b96065d537bd99","f65f5d3e292c2e0819a528391c994624d784869d7e6ea67fb18041024edc07dc"],["78c9407544ac132692ee1910a02439958ae04877151342ea96c4b6b35a49f51","f3e0319169eb9b85d5404795539a5e68fa1fbd583c064d2462b675f194a3ddb4"],["494f4be219a1a77016dcd838431aea0001cdc8ae7a6fc688726578d9702857a5","42242a969283a5f339ba7f075e36ba2af925ce30d767ed6e55f4b031880d562c"],["a598a8030da6d86c6bc7f2f5144ea549d28211ea58faa70ebf4c1e665c1fe9b5","204b5d6f84822c307e4b4a7140737aec23fc63b65b35f86a10026dbd2d864e6b"],["c41916365abb2b5d09192f5f2dbeafec208f020f12570a184dbadc3e58595997","4f14351d0087efa49d245b328984989d5caf9450f34bfc0ed16e96b58fa9913"],["841d6063a586fa475a724604da03bc5b92a2e0d2e0a36acfe4c73a5514742881","73867f59c0659e81904f9a1c7543698e62562d6744c169ce7a36de01a8d6154"],["5e95bb399a6971d376026947f89bde2f282b33810928be4ded112ac4d70e20d5","39f23f366809085beebfc71181313775a99c9aed7d8ba38b161384c746012865"],["36e4641a53948fd476c39f8a99fd974e5ec07564b5315d8bf99471bca0ef2f66","d2424b1b1abe4eb8164227b085c9aa9456ea13493fd563e06fd51cf5694c78fc"],["336581ea7bfbbb290c191a2f507a41cf5643842170e914faeab27c2c579f726","ead12168595fe1be99252129b6e56b3391f7ab1410cd1e0ef3dcdcabd2fda224"],["8ab89816dadfd6b6a1f2634fcf00ec8403781025ed6890c4849742706bd43ede","6fdcef09f2f6d0a044e654aef624136f503d459c3e89845858a47a9129cdd24e"],["1e33f1a746c9c5778133344d9299fcaa20b0938e8acff2544bb40284b8c5fb94","60660257dd11b3aa9c8ed618d24edff2306d320f1d03010e33a7d2057f3b3b6"],["85b7c1dcb3cec1b7ee7f30ded79dd20a0ed1f4cc18cbcfcfa410361fd8f08f31","3d98a9cdd026dd43f39048f25a8847f4fcafad1895d7a633c6fed3c35e999511"],["29df9fbd8d9e46509275f4b125d6d45d7fbe9a3b878a7af872a2800661ac5f51","b4c4fe99c775a606e2d8862179139ffda61dc861c019e55cd2876eb2a27d84b"],["a0b1cae06b0a847a3fea6e671aaf8adfdfe58ca2f768105c8082b2e449fce252","ae434102edde0958ec4b19d917a6a28e6b72da1834aff0e650f049503a296cf2"],["4e8ceafb9b3e9a136dc7ff67e840295b499dfb3b2133e4ba113f2e4c0e121e5","cf2174118c8b6d7a4b48f6d534ce5c79422c086a63460502b827ce62a326683c"],["d24a44e047e19b6f5afb81c7ca2f69080a5076689a010919f42725c2b789a33b","6fb8d5591b466f8fc63db50f1c0f1c69013f996887b8244d2cdec417afea8fa3"],["ea01606a7a6c9cdd249fdfcfacb99584001edd28abbab77b5104e98e8e3b35d4","322af4908c7312b0cfbfe369f7a7b3cdb7d4494bc2823700cfd652188a3ea98d"],["af8addbf2b661c8a6c6328655eb96651252007d8c5ea31be4ad196de8ce2131f","6749e67c029b85f52a034eafd096836b2520818680e26ac8f3dfbcdb71749700"],["e3ae1974566ca06cc516d47e0fb165a674a3dabcfca15e722f0e3450f45889","2aeabe7e4531510116217f07bf4d07300de97e4874f81f533420a72eeb0bd6a4"],["591ee355313d99721cf6993ffed1e3e301993ff3ed258802075ea8ced397e246","b0ea558a113c30bea60fc4775460c7901ff0b053d25ca2bdeee98f1a4be5d196"],["11396d55fda54c49f19aa97318d8da61fa8584e47b084945077cf03255b52984","998c74a8cd45ac01289d5833a7beb4744ff536b01b257be4c5767bea93ea57a4"],["3c5d2a1ba39c5a1790000738c9e0c40b8dcdfd5468754b6405540157e017aa7a","b2284279995a34e2f9d4de7396fc18b80f9b8b9fdd270f6661f79ca4c81bd257"],["cc8704b8a60a0defa3a99a7299f2e9c3fbc395afb04ac078425ef8a1793cc030","bdd46039feed17881d1e0862db347f8cf395b74fc4bcdc4e940b74e3ac1f1b13"],["c533e4f7ea8555aacd9777ac5cad29b97dd4defccc53ee7ea204119b2889b197","6f0a256bc5efdf429a2fb6242f1a43a2d9b925bb4a4b3a26bb8e0f45eb596096"],["c14f8f2ccb27d6f109f6d08d03cc96a69ba8c34eec07bbcf566d48e33da6593","c359d6923bb398f7fd4473e16fe1c28475b740dd098075e6c0e8649113dc3a38"],["a6cbc3046bc6a450bac24789fa17115a4c9739ed75f8f21ce441f72e0b90e6ef","21ae7f4680e889bb130619e2c0f95a360ceb573c70603139862afd617fa9b9f"],["347d6d9a02c48927ebfb86c1359b1caf130a3c0267d11ce6344b39f99d43cc38","60ea7f61a353524d1c987f6ecec92f086d565ab687870cb12689ff1e31c74448"],["da6545d2181db8d983f7dcb375ef5866d47c67b1bf31c8cf855ef7437b72656a","49b96715ab6878a79e78f07ce5680c5d6673051b4935bd897fea824b77dc208a"],["c40747cc9d012cb1a13b8148309c6de7ec25d6945d657146b9d5994b8feb1111","5ca560753be2a12fc6de6caf2cb489565db936156b9514e1bb5e83037e0fa2d4"],["4e42c8ec82c99798ccf3a610be870e78338c7f713348bd34c8203ef4037f3502","7571d74ee5e0fb92a7a8b33a07783341a5492144cc54bcc40a94473693606437"],["3775ab7089bc6af823aba2e1af70b236d251cadb0c86743287522a1b3b0dedea","be52d107bcfa09d8bcb9736a828cfa7fac8db17bf7a76a2c42ad961409018cf7"],["cee31cbf7e34ec379d94fb814d3d775ad954595d1314ba8846959e3e82f74e26","8fd64a14c06b589c26b947ae2bcf6bfa0149ef0be14ed4d80f448a01c43b1c6d"],["b4f9eaea09b6917619f6ea6a4eb5464efddb58fd45b1ebefcdc1a01d08b47986","39e5c9925b5a54b07433a4f18c61726f8bb131c012ca542eb24a8ac07200682a"],["d4263dfc3d2df923a0179a48966d30ce84e2515afc3dccc1b77907792ebcc60e","62dfaf07a0f78feb30e30d6295853ce189e127760ad6cf7fae164e122a208d54"],["48457524820fa65a4f8d35eb6930857c0032acc0a4a2de422233eeda897612c4","25a748ab367979d98733c38a1fa1c2e7dc6cc07db2d60a9ae7a76aaa49bd0f77"],["dfeeef1881101f2cb11644f3a2afdfc2045e19919152923f367a1767c11cceda","ecfb7056cf1de042f9420bab396793c0c390bde74b4bbdff16a83ae09a9a7517"],["6d7ef6b17543f8373c573f44e1f389835d89bcbc6062ced36c82df83b8fae859","cd450ec335438986dfefa10c57fea9bcc521a0959b2d80bbf74b190dca712d10"],["e75605d59102a5a2684500d3b991f2e3f3c88b93225547035af25af66e04541f","f5c54754a8f71ee540b9b48728473e314f729ac5308b06938360990e2bfad125"],["eb98660f4c4dfaa06a2be453d5020bc99a0c2e60abe388457dd43fefb1ed620c","6cb9a8876d9cb8520609af3add26cd20a0a7cd8a9411131ce85f44100099223e"],["13e87b027d8514d35939f2e6892b19922154596941888336dc3563e3b8dba942","fef5a3c68059a6dec5d624114bf1e91aac2b9da568d6abeb2570d55646b8adf1"],["ee163026e9fd6fe017c38f06a5be6fc125424b371ce2708e7bf4491691e5764a","1acb250f255dd61c43d94ccc670d0f58f49ae3fa15b96623e5430da0ad6c62b2"],["b268f5ef9ad51e4d78de3a750c2dc89b1e626d43505867999932e5db33af3d80","5f310d4b3c99b9ebb19f77d41c1dee018cf0d34fd4191614003e945a1216e423"],["ff07f3118a9df035e9fad85eb6c7bfe42b02f01ca99ceea3bf7ffdba93c4750d","438136d603e858a3a5c440c38eccbaddc1d2942114e2eddd4740d098ced1f0d8"],["8d8b9855c7c052a34146fd20ffb658bea4b9f69e0d825ebec16e8c3ce2b526a1","cdb559eedc2d79f926baf44fb84ea4d44bcf50fee51d7ceb30e2e7f463036758"],["52db0b5384dfbf05bfa9d472d7ae26dfe4b851ceca91b1eba54263180da32b63","c3b997d050ee5d423ebaf66a6db9f57b3180c902875679de924b69d84a7b375"],["e62f9490d3d51da6395efd24e80919cc7d0f29c3f3fa48c6fff543becbd43352","6d89ad7ba4876b0b22c2ca280c682862f342c8591f1daf5170e07bfd9ccafa7d"],["7f30ea2476b399b4957509c88f77d0191afa2ff5cb7b14fd6d8e7d65aaab1193","ca5ef7d4b231c94c3b15389a5f6311e9daff7bb67b103e9880ef4bff637acaec"],["5098ff1e1d9f14fb46a210fada6c903fef0fb7b4a1dd1d9ac60a0361800b7a00","9731141d81fc8f8084d37c6e7542006b3ee1b40d60dfe5362a5b132fd17ddc0"],["32b78c7de9ee512a72895be6b9cbefa6e2f3c4ccce445c96b9f2c81e2778ad58","ee1849f513df71e32efc3896ee28260c73bb80547ae2275ba497237794c8753c"],["e2cb74fddc8e9fbcd076eef2a7c72b0ce37d50f08269dfc074b581550547a4f7","d3aa2ed71c9dd2247a62df062736eb0baddea9e36122d2be8641abcb005cc4a4"],["8438447566d4d7bedadc299496ab357426009a35f235cb141be0d99cd10ae3a8","c4e1020916980a4da5d01ac5e6ad330734ef0d7906631c4f2390426b2edd791f"],["4162d488b89402039b584c6fc6c308870587d9c46f660b878ab65c82c711d67e","67163e903236289f776f22c25fb8a3afc1732f2b84b4e95dbda47ae5a0852649"],["3fad3fa84caf0f34f0f89bfd2dcf54fc175d767aec3e50684f3ba4a4bf5f683d","cd1bc7cb6cc407bb2f0ca647c718a730cf71872e7d0d2a53fa20efcdfe61826"],["674f2600a3007a00568c1a7ce05d0816c1fb84bf1370798f1c69532faeb1a86b","299d21f9413f33b3edf43b257004580b70db57da0b182259e09eecc69e0d38a5"],["d32f4da54ade74abb81b815ad1fb3b263d82d6c692714bcff87d29bd5ee9f08f","f9429e738b8e53b968e99016c059707782e14f4535359d582fc416910b3eea87"],["30e4e670435385556e593657135845d36fbb6931f72b08cb1ed954f1e3ce3ff6","462f9bce619898638499350113bbc9b10a878d35da70740dc695a559eb88db7b"],["be2062003c51cc3004682904330e4dee7f3dcd10b01e580bf1971b04d4cad297","62188bc49d61e5428573d48a74e1c655b1c61090905682a0d5558ed72dccb9bc"],["93144423ace3451ed29e0fb9ac2af211cb6e84a601df5993c419859fff5df04a","7c10dfb164c3425f5c71a3f9d7992038f1065224f72bb9d1d902a6d13037b47c"],["b015f8044f5fcbdcf21ca26d6c34fb8197829205c7b7d2a7cb66418c157b112c","ab8c1e086d04e813744a655b2df8d5f83b3cdc6faa3088c1d3aea1454e3a1d5f"],["d5e9e1da649d97d89e4868117a465a3a4f8a18de57a140d36b3f2af341a21b52","4cb04437f391ed73111a13cc1d4dd0db1693465c2240480d8955e8592f27447a"],["d3ae41047dd7ca065dbf8ed77b992439983005cd72e16d6f996a5316d36966bb","bd1aeb21ad22ebb22a10f0303417c6d964f8cdd7df0aca614b10dc14d125ac46"],["463e2763d885f958fc66cdd22800f0a487197d0a82e377b49f80af87c897b065","bfefacdb0e5d0fd7df3a311a94de062b26b80c61fbc97508b79992671ef7ca7f"],["7985fdfd127c0567c6f53ec1bb63ec3158e597c40bfe747c83cddfc910641917","603c12daf3d9862ef2b25fe1de289aed24ed291e0ec6708703a5bd567f32ed03"],["74a1ad6b5f76e39db2dd249410eac7f99e74c59cb83d2d0ed5ff1543da7703e9","cc6157ef18c9c63cd6193d83631bbea0093e0968942e8c33d5737fd790e0db08"],["30682a50703375f602d416664ba19b7fc9bab42c72747463a71d0896b22f6da3","553e04f6b018b4fa6c8f39e7f311d3176290d0e0f19ca73f17714d9977a22ff8"],["9e2158f0d7c0d5f26c3791efefa79597654e7a2b2464f52b1ee6c1347769ef57","712fcdd1b9053f09003a3481fa7762e9ffd7c8ef35a38509e2fbf2629008373"],["176e26989a43c9cfeba4029c202538c28172e566e3c4fce7322857f3be327d66","ed8cc9d04b29eb877d270b4878dc43c19aefd31f4eee09ee7b47834c1fa4b1c3"],["75d46efea3771e6e68abb89a13ad747ecf1892393dfc4f1b7004788c50374da8","9852390a99507679fd0b86fd2b39a868d7efc22151346e1a3ca4726586a6bed8"],["809a20c67d64900ffb698c4c825f6d5f2310fb0451c869345b7319f645605721","9e994980d9917e22b76b061927fa04143d096ccc54963e6a5ebfa5f3f8e286c1"],["1b38903a43f7f114ed4500b4eac7083fdefece1cf29c63528d563446f972c180","4036edc931a60ae889353f77fd53de4a2708b26b6f5da72ad3394119daf408f9"]]}}},4105:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{value:{type:Boolean,default:!0}},methods:{onInput:function(t){this.$emit("input",t)},onConfirm:function(){this.onInput(!1),this.$emit("confirm")}}};e.default=n},4111:function(t,e,i){"use strict";var n=i("7f7a");e.certificate=i("56b5");var r=n.define("RSAPrivateKey",(function(){this.seq().obj(this.key("version").int(),this.key("modulus").int(),this.key("publicExponent").int(),this.key("privateExponent").int(),this.key("prime1").int(),this.key("prime2").int(),this.key("exponent1").int(),this.key("exponent2").int(),this.key("coefficient").int())}));e.RSAPrivateKey=r;var a=n.define("RSAPublicKey",(function(){this.seq().obj(this.key("modulus").int(),this.key("publicExponent").int())}));e.RSAPublicKey=a;var o=n.define("SubjectPublicKeyInfo",(function(){this.seq().obj(this.key("algorithm").use(s),this.key("subjectPublicKey").bitstr())}));e.PublicKey=o;var s=n.define("AlgorithmIdentifier",(function(){this.seq().obj(this.key("algorithm").objid(),this.key("none").null_().optional(),this.key("curve").objid().optional(),this.key("params").seq().obj(this.key("p").int(),this.key("q").int(),this.key("g").int()).optional())})),c=n.define("PrivateKeyInfo",(function(){this.seq().obj(this.key("version").int(),this.key("algorithm").use(s),this.key("subjectPrivateKey").octstr())}));e.PrivateKey=c;var f=n.define("EncryptedPrivateKeyInfo",(function(){this.seq().obj(this.key("algorithm").seq().obj(this.key("id").objid(),this.key("decrypt").seq().obj(this.key("kde").seq().obj(this.key("id").objid(),this.key("kdeparams").seq().obj(this.key("salt").octstr(),this.key("iters").int())),this.key("cipher").seq().obj(this.key("algo").objid(),this.key("iv").octstr()))),this.key("subjectPrivateKey").octstr())}));e.EncryptedPrivateKey=f;var u=n.define("DSAPrivateKey",(function(){this.seq().obj(this.key("version").int(),this.key("p").int(),this.key("q").int(),this.key("g").int(),this.key("pub_key").int(),this.key("priv_key").int())}));e.DSAPrivateKey=u,e.DSAparam=n.define("DSAparam",(function(){this.int()}));var d=n.define("ECPrivateKey",(function(){this.seq().obj(this.key("version").int(),this.key("privateKey").octstr(),this.key("parameters").optional().explicit(0).use(h),this.key("publicKey").optional().explicit(1).bitstr())}));e.ECPrivateKey=d;var h=n.define("ECParameters",(function(){this.choice({namedCurve:this.objid()})}));e.signature=n.define("signature",(function(){this.seq().obj(this.key("r").int(),this.key("s").int())}))},4136:function(t,e,i){"use strict";var n=e;n.base=i("ea537"),n.short=i("3300"),n.mont=i("676f"),n.edwards=i("3daf")},"41df":function(t,e,i){"use strict";const n=e;n.Reporter=i("d1c8").Reporter,n.DecoderBuffer=i("6283").DecoderBuffer,n.EncoderBuffer=i("6283").EncoderBuffer,n.Node=i("8360")},4228:function(t,e,i){var n=i("82f0"),r=i("8707").Buffer,a=i("bac2"),o=i("09f5"),s=i("6430"),c=i("39f5"),f=i("ae84"),u=i("3fb5");function d(t,e,i){s.call(this),this._cache=new h,this._last=void 0,this._cipher=new c.AES(e),this._prev=r.from(i),this._mode=t,this._autopadding=!0}function h(){this.cache=r.allocUnsafe(0)}function l(t,e,i){var s=a[t.toLowerCase()];if(!s)throw new TypeError("invalid suite type");if("string"===typeof i&&(i=r.from(i)),"GCM"!==s.mode&&i.length!==s.iv)throw new TypeError("invalid iv length "+i.length);if("string"===typeof e&&(e=r.from(e)),e.length!==s.key/8)throw new TypeError("invalid key length "+e.length);return"stream"===s.type?new o(s.module,e,i,!0):"auth"===s.type?new n(s.module,e,i,!0):new d(s.module,e,i)}u(d,s),d.prototype._update=function(t){var e,i;this._cache.add(t);var n=[];while(e=this._cache.get(this._autopadding))i=this._mode.decrypt(this,e),n.push(i);return r.concat(n)},d.prototype._final=function(){var t=this._cache.flush();if(this._autopadding)return function(t){var e=t[15];if(e<1||e>16)throw new Error("unable to decrypt data");var i=-1;while(++i<e)if(t[i+(16-e)]!==e)throw new Error("unable to decrypt data");if(16===e)return;return t.slice(0,16-e)}(this._mode.decrypt(this,t));if(t)throw new Error("data not multiple of block length")},d.prototype.setAutoPadding=function(t){return this._autopadding=!!t,this},h.prototype.add=function(t){this.cache=r.concat([this.cache,t])},h.prototype.get=function(t){var e;if(t){if(this.cache.length>16)return e=this.cache.slice(0,16),this.cache=this.cache.slice(16),e}else if(this.cache.length>=16)return e=this.cache.slice(0,16),this.cache=this.cache.slice(16),e;return null},h.prototype.flush=function(){if(this.cache.length)return this.cache},e.createDecipher=function(t,e){var i=a[t.toLowerCase()];if(!i)throw new TypeError("invalid suite type");var n=f(e,!1,i.key,i.iv);return l(t,n.key,n.iv)},e.createDecipheriv=l},"424f":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{value:{type:Boolean,default:!0}},methods:{onInput:function(t){this.$emit("input",t)}}};e.default=n},"429b":function(t,e,i){t.exports=i("faa1").EventEmitter},"43a3":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={VhCommentList:i("f2d3").default,uPopup:i("c4b0").default,vhCheck:i("2036").default,vhImage:i("ce7c").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"font-wei-500 font-36 text-3"},[t._v("评论")]),t.comments.length?i("v-uni-view",{staticClass:"mt-32"},[i("VhCommentList",{attrs:{list:t.comments},on:{enjoy:function(e){arguments[0]=e=t.$handleEvent(e),t.onEnjoy.apply(void 0,arguments)},reply:function(e){arguments[0]=e=t.$handleEvent(e),t.onReply.apply(void 0,arguments)},toggleReply:function(e){arguments[0]=e=t.$handleEvent(e),t.onToggleReply.apply(void 0,arguments)}}})],1):i("v-uni-view",{staticClass:"flex-c-c flex-column mt-n-08"},[i("v-uni-image",{staticClass:"w-130 h-110",attrs:{src:t.ossIcon("/auction/comments_none_130_110.png")}}),i("v-uni-view",{staticClass:"mt-10 font-24 text-9 l-h-34 text-center"},[t._v("快去夺首评!")])],1),i("u-popup",{attrs:{mode:"bottom",width:"100%","border-radius":"40"},model:{value:t.popupVisible,callback:function(e){t.popupVisible=e},expression:"popupVisible"}},[i("v-uni-view",[i("v-uni-view",{staticClass:"pt-24"},[i("v-uni-textarea",{staticClass:"bs-bb mtb-00-mlr-auto p-24 w-702 h-168 bg-f7f7f7 b-rad-10 font-28 text-3 l-h-40",attrs:{placeholder:t.placeholder,"placeholder-style":"color:#999"},model:{value:t.commentParams.content,callback:function(e){t.$set(t.commentParams,"content",e)},expression:"commentParams.content"}})],1),i("v-uni-view",{staticClass:"flex-sb-c ptb-32-plr-24 pt-20"},[i("v-uni-view",[t.isAgreed?t._e():i("v-uni-view",{staticClass:"flex-c-c"},[i("vh-check",{attrs:{checked:t.checked,width:26,height:26},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.checked=!t.checked}}}),i("v-uni-view",{staticClass:"ml-10 font-24 text-9 l-h-34"},[i("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.checked=!t.checked}}},[t._v("阅读并接受")]),i("v-uni-text",{staticClass:"text-2b8cf7",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.jump.jumpH5Agreement(t.agreementPrefix+"/ContentPubRules",t.$vhFrom)}}},[t._v("《酒云网内容发布规则》")])],1)],1)],1),i("v-uni-view",{staticClass:"flex-c-c"},[i("v-uni-image",{staticClass:"w-44 h-44",attrs:{src:t.ossIcon("/auction/emoji_44.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showEmo=!t.showEmo}}}),i("v-uni-image",{staticClass:"ml-24 w-44 h-44",attrs:{src:t.ossIcon("/auction/publish"+(t.disabled?"":"_h")+"_44.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSubmit.apply(void 0,arguments)}}})],1)],1),t.commentParams.emoji_image?i("v-uni-view",{staticClass:"p-rela w-120 h-120 bg-f5f5f5 b-rad-10 ml-24 mb-24"},[i("v-uni-image",{staticClass:"w-120 h-120 mb-46 mr-20",attrs:{src:t.commentParams.emoji_image}}),i("v-uni-image",{staticClass:"p-abso top-06 right-06 w-32 h-32",attrs:{src:t.ossIcon("/comm/del_gray.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.commentParams.emoji_image=""}}})],1):t._e(),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.showEmo,expression:"showEmo"}]},[i("v-uni-view",{staticClass:"w-p100 h-350 bt-s-01-eeeeee bb-s-01-eeeeee"},[i("v-uni-scroll-view",{staticClass:"h-p100",attrs:{"scroll-y":"true"}},[i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:0==t.emoTypeIndex,expression:"emoTypeIndex == 0"}],staticClass:"h-p100 d-flex flex-wrap pt-32 pl-24 pr-24"},t._l(t.RabbitHeadEmojiList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"w-120 d-flex flex-column j-center a-center mb-20 mr-20 b-rad-10 o-hid",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.commentParams.emoji_image=e.img}}},[i("vh-image",{attrs:{"loading-type":"2",src:e.img,width:120,height:120}}),i("v-uni-text",{staticClass:"mt-10 font-24 text-9"},[t._v(t._s(e.name))])],1)})),1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:1==t.emoTypeIndex,expression:"emoTypeIndex == 1"}],staticClass:"h-p100 d-flex flex-wrap pt-32 pl-24 pr-24"},t._l(t.RabbitEmojiList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"w-120 d-flex flex-column j-center a-center mb-20 mr-20 b-rad-10 o-hid",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.commentParams.emoji_image=e.img}}},[i("vh-image",{attrs:{"loading-type":"2",src:e.img,width:120,height:120}}),i("v-uni-text",{staticClass:"mt-10 font-24 text-9"},[t._v(t._s(e.name))])],1)})),1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:2==t.emoTypeIndex,expression:"emoTypeIndex == 2"}],staticClass:"h-p100 d-flex flex-wrap pt-32 pl-24 pr-24"},t._l(t.CommonEmojiList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"font-40 ml-10 mr-10 mb-20",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.addEmoji(e)}}},[t._v(t._s(e))])})),1)],1)],1),i("v-uni-view",{staticClass:"h-80 d-flex bg-f5f5f5"},t._l(t.emoTypeList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"h-p100 d-flex j-center a-center ptb-00-plr-42 text-6 font-28",class:t.emoTypeIndex==n?"bg-ffffff":"bg-f5f5f5",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectEmoType(n)}}},[t._v(t._s(e))])})),1)],1)],1)],1)],1)},a=[]},"43ea":function(t,e,i){"use strict";i.r(e);var n=i("5786"),r=i("b949");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"f763f0f8",null,!1,n["a"],void 0);e["default"]=s.exports},"44a3":function(t,e,i){"use strict";var n=i("399f"),r=i("f3a3"),a=r.assert,o=r.cachedProperty,s=r.parseBytes;function c(t,e){this.eddsa=t,"object"!==typeof e&&(e=s(e)),Array.isArray(e)&&(e={R:e.slice(0,t.encodingLength),S:e.slice(t.encodingLength)}),a(e.R&&e.S,"Signature without R or S"),t.isPoint(e.R)&&(this._R=e.R),e.S instanceof n&&(this._S=e.S),this._Rencoded=Array.isArray(e.R)?e.R:e.Rencoded,this._Sencoded=Array.isArray(e.S)?e.S:e.Sencoded}o(c,"S",(function(){return this.eddsa.decodeInt(this.Sencoded())})),o(c,"R",(function(){return this.eddsa.decodePoint(this.Rencoded())})),o(c,"Rencoded",(function(){return this.eddsa.encodePoint(this.R())})),o(c,"Sencoded",(function(){return this.eddsa.encodeInt(this.S())})),c.prototype.toBytes=function(){return this.Rencoded().concat(this.Sencoded())},c.prototype.toHex=function(){return r.encode(this.toBytes(),"hex").toUpperCase()},t.exports=c},"45c4":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uPopup:i("c4b0").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-popup",{attrs:{value:t.value,mode:"bottom","mask-close-able":!1,width:"100%","border-radius":"30"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"p-rela flex-c-c h-100"},[i("v-uni-view",{staticClass:"p-abso flex-c-c w-88 h-88",staticStyle:{left:"26rpx",top:"50%",transform:"translateY(-50%)"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput(!1)}}},[i("v-uni-image",{staticClass:"w-44 h-44",attrs:{src:t.ossIcon("/auction/close_44.png")}})],1),i("v-uni-text",{staticClass:"font-wei-600 font-32 text-6"},[t._v("请选择支付方式")])],1),i("v-uni-view",{staticClass:"mt-52 ptb-00-plr-56 pb-70"},[t.isShowWxSelect?i("v-uni-view",{staticClass:"flex-sb-c h-150",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onPay(t.PAY_TYPE.WX)}}},[i("v-uni-view",{staticClass:"flex-c-c"},[i("v-uni-image",{staticClass:"w-54 h-54",attrs:{src:t.ossIcon("/auction/wx_54.png")}}),i("v-uni-text",{staticClass:"ml-34 font-32 text-3"},[t._v("微信支付")])],1),t.type===t.PAY_TYPE.WX?i("v-uni-image",{staticClass:"w-32 h-32",attrs:{src:t.ossIcon("/auction/radio_h_32.png")}}):i("v-uni-view",{staticClass:"w-36 h-36 bg-e5e6e7 b-rad-p50"})],1):t._e(),t.isShowWxSelect&&t.isShowAliSelect?i("v-uni-view",{staticClass:"h-02 bg-eeeeee"}):t._e(),t.isShowAliSelect?i("v-uni-view",{staticClass:"flex-sb-c h-150",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onPay(t.PAY_TYPE.ALI)}}},[i("v-uni-view",{staticClass:"flex-c-c"},[i("v-uni-image",{staticClass:"w-54 h-54",attrs:{src:t.ossIcon("/auction/ali_54.png")}}),i("v-uni-text",{staticClass:"ml-34 font-32 text-3"},[t._v("支付宝支付")])],1),t.type===t.PAY_TYPE.ALI?i("v-uni-image",{staticClass:"w-32 h-32",attrs:{src:t.ossIcon("/auction/radio_h_32.png")}}):i("v-uni-view",{staticClass:"w-36 h-36 bg-e5e6e7 b-rad-p50"})],1):t._e()],1),i("v-uni-view",{domProps:{innerHTML:t._s(t.ailPayForm)}})],1)},a=[]},4681:function(t,e,i){"use strict";var n=i("966d");function r(t,e){t.emit("error",e)}t.exports={destroy:function(t,e){var i=this,a=this._readableState&&this._readableState.destroyed,o=this._writableState&&this._writableState.destroyed;return a||o?(e?e(t):!t||this._writableState&&this._writableState.errorEmitted||n.nextTick(r,this,t),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,(function(t){!e&&t?(n.nextTick(r,i,t),i._writableState&&(i._writableState.errorEmitted=!0)):e&&e(t)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},"478c":function(t,e,i){"use strict";i.r(e);var n=i("76a6"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"4dd0":function(t,e,i){var n=/Proc-Type: 4,ENCRYPTED[\n\r]+DEK-Info: AES-((?:128)|(?:192)|(?:256))-CBC,([0-9A-H]+)[\n\r]+([0-9A-z\n\r+/=]+)[\n\r]+/m,r=/^-----BEGIN ((?:.*? KEY)|CERTIFICATE)-----/m,a=/^-----BEGIN ((?:.*? KEY)|CERTIFICATE)-----([0-9A-z\n\r+/=]+)-----END \1-----$/m,o=i("ae84"),s=i("fda6"),c=i("8707").Buffer;t.exports=function(t,e){var i,f=t.toString(),u=f.match(n);if(u){var d="aes"+u[1],h=c.from(u[2],"hex"),l=c.from(u[3].replace(/[\r\n]/g,""),"base64"),p=o(e,h.slice(0,8),parseInt(u[1],10)).key,b=[],v=s.createDecipheriv(d,p,h);b.push(v.update(l)),b.push(v.final()),i=c.concat(b)}else{var m=f.match(a);i=c.from(m[2].replace(/[\r\n]/g,""),"base64")}var g=f.match(r)[1];return{tag:g,data:i}}},"4e2b":function(t,e,i){"use strict";var n=i("da3e"),r=i("3fb5"),a=i("5ee7"),o=i("0184");function s(){this.tmp=new Array(2),this.keys=null}function c(t){o.call(this,t);var e=new s;this._desState=e,this.deriveKeys(e,t.key)}r(c,o),t.exports=c,c.create=function(t){return new c(t)};var f=[1,1,2,2,2,2,2,2,1,2,2,2,2,2,2,1];c.prototype.deriveKeys=function(t,e){t.keys=new Array(32),n.equal(e.length,this.blockSize,"Invalid key length");var i=a.readUInt32BE(e,0),r=a.readUInt32BE(e,4);a.pc1(i,r,t.tmp,0),i=t.tmp[0],r=t.tmp[1];for(var o=0;o<t.keys.length;o+=2){var s=f[o>>>1];i=a.r28shl(i,s),r=a.r28shl(r,s),a.pc2(i,r,t.keys,o)}},c.prototype._update=function(t,e,i,n){var r=this._desState,o=a.readUInt32BE(t,e),s=a.readUInt32BE(t,e+4);a.ip(o,s,r.tmp,0),o=r.tmp[0],s=r.tmp[1],"encrypt"===this.type?this._encrypt(r,o,s,r.tmp,0):this._decrypt(r,o,s,r.tmp,0),o=r.tmp[0],s=r.tmp[1],a.writeUInt32BE(i,o,n),a.writeUInt32BE(i,s,n+4)},c.prototype._pad=function(t,e){for(var i=t.length-e,n=e;n<t.length;n++)t[n]=i;return!0},c.prototype._unpad=function(t){for(var e=t[t.length-1],i=t.length-e;i<t.length;i++)n.equal(t[i],e);return t.slice(0,t.length-e)},c.prototype._encrypt=function(t,e,i,n,r){for(var o=e,s=i,c=0;c<t.keys.length;c+=2){var f=t.keys[c],u=t.keys[c+1];a.expand(s,t.tmp,0),f^=t.tmp[0],u^=t.tmp[1];var d=a.substitute(f,u),h=a.permute(d),l=s;s=(o^h)>>>0,o=l}a.rip(s,o,n,r)},c.prototype._decrypt=function(t,e,i,n,r){for(var o=i,s=e,c=t.keys.length-2;c>=0;c-=2){var f=t.keys[c],u=t.keys[c+1];a.expand(o,t.tmp,0),f^=t.tmp[0],u^=t.tmp[1];var d=a.substitute(f,u),h=a.permute(d),l=o;o=(s^h)>>>0,s=l}a.rip(o,s,n,r)}},"4fd1":function(t,e,i){var n=i("3fb5"),r=i("b672"),a=i("8707").Buffer,o=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591],s=new Array(160);function c(){this.init(),this._w=s,r.call(this,128,112)}function f(t,e,i){return i^t&(e^i)}function u(t,e,i){return t&e|i&(t|e)}function d(t,e){return(t>>>28|e<<4)^(e>>>2|t<<30)^(e>>>7|t<<25)}function h(t,e){return(t>>>14|e<<18)^(t>>>18|e<<14)^(e>>>9|t<<23)}function l(t,e){return(t>>>1|e<<31)^(t>>>8|e<<24)^t>>>7}function p(t,e){return(t>>>1|e<<31)^(t>>>8|e<<24)^(t>>>7|e<<25)}function b(t,e){return(t>>>19|e<<13)^(e>>>29|t<<3)^t>>>6}function v(t,e){return(t>>>19|e<<13)^(e>>>29|t<<3)^(t>>>6|e<<26)}function m(t,e){return t>>>0<e>>>0?1:0}n(c,r),c.prototype.init=function(){return this._ah=1779033703,this._bh=3144134277,this._ch=1013904242,this._dh=2773480762,this._eh=1359893119,this._fh=2600822924,this._gh=528734635,this._hh=1541459225,this._al=4089235720,this._bl=2227873595,this._cl=4271175723,this._dl=1595750129,this._el=2917565137,this._fl=725511199,this._gl=4215389547,this._hl=327033209,this},c.prototype._update=function(t){for(var e=this._w,i=0|this._ah,n=0|this._bh,r=0|this._ch,a=0|this._dh,s=0|this._eh,c=0|this._fh,g=0|this._gh,y=0|this._hh,w=0|this._al,_=0|this._bl,x=0|this._cl,S=0|this._dl,k=0|this._el,A=0|this._fl,E=0|this._gl,M=0|this._hl,C=0;C<32;C+=2)e[C]=t.readInt32BE(4*C),e[C+1]=t.readInt32BE(4*C+4);for(;C<160;C+=2){var P=e[C-30],I=e[C-30+1],B=l(P,I),R=p(I,P);P=e[C-4],I=e[C-4+1];var O=b(P,I),T=v(I,P),j=e[C-14],L=e[C-14+1],D=e[C-32],N=e[C-32+1],U=R+L|0,z=B+j+m(U,R)|0;U=U+T|0,z=z+O+m(U,T)|0,U=U+N|0,z=z+D+m(U,N)|0,e[C]=z,e[C+1]=U}for(var $=0;$<160;$+=2){z=e[$],U=e[$+1];var q=u(i,n,r),F=u(w,_,x),V=d(i,w),H=d(w,i),K=h(s,k),G=h(k,s),W=o[$],Y=o[$+1],X=f(s,c,g),J=f(k,A,E),Z=M+G|0,Q=y+K+m(Z,M)|0;Z=Z+J|0,Q=Q+X+m(Z,J)|0,Z=Z+Y|0,Q=Q+W+m(Z,Y)|0,Z=Z+U|0,Q=Q+z+m(Z,U)|0;var tt=H+F|0,et=V+q+m(tt,H)|0;y=g,M=E,g=c,E=A,c=s,A=k,k=S+Z|0,s=a+Q+m(k,S)|0,a=r,S=x,r=n,x=_,n=i,_=w,w=Z+tt|0,i=Q+et+m(w,Z)|0}this._al=this._al+w|0,this._bl=this._bl+_|0,this._cl=this._cl+x|0,this._dl=this._dl+S|0,this._el=this._el+k|0,this._fl=this._fl+A|0,this._gl=this._gl+E|0,this._hl=this._hl+M|0,this._ah=this._ah+i+m(this._al,w)|0,this._bh=this._bh+n+m(this._bl,_)|0,this._ch=this._ch+r+m(this._cl,x)|0,this._dh=this._dh+a+m(this._dl,S)|0,this._eh=this._eh+s+m(this._el,k)|0,this._fh=this._fh+c+m(this._fl,A)|0,this._gh=this._gh+g+m(this._gl,E)|0,this._hh=this._hh+y+m(this._hl,M)|0},c.prototype._hash=function(){var t=a.allocUnsafe(64);function e(e,i,n){t.writeInt32BE(e,n),t.writeInt32BE(i,n+4)}return e(this._ah,this._al,0),e(this._bh,this._bl,8),e(this._ch,this._cl,16),e(this._dh,this._dl,24),e(this._eh,this._el,32),e(this._fh,this._fl,40),e(this._gh,this._gl,48),e(this._hh,this._hl,56),t},t.exports=c},50476:function(t,e,i){"use strict";i.r(e);var n=i("eefe"),r=i("478c");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"aae2853a",null,!1,n["a"],void 0);e["default"]=s.exports},5165:function(t,e,i){(function(t){var n=i("8c8a");function r(t){return t._prev=t._cipher.encryptBlock(t._prev),t._prev}e.encrypt=function(e,i){while(e._cache.length<i.length)e._cache=t.concat([e._cache,r(e)]);var a=e._cache.slice(0,i.length);return e._cache=e._cache.slice(i.length),n(i,a)}}).call(this,i("b639").Buffer)},5239:function(t,e,i){var n=i("8707").Buffer;function r(t,e,i){var n,r,o,s=-1,c=0;while(++s<8)n=t._cipher.encryptBlock(t._prev),r=e&1<<7-s?128:0,o=n[0]^r,c+=(128&o)>>s%8,t._prev=a(t._prev,i?r:o);return c}function a(t,e){var i=t.length,r=-1,a=n.allocUnsafe(t.length);t=n.concat([t,n.from([e])]);while(++r<i)a[r]=t[r]<<1|t[r+1]>>7;return a}e.encrypt=function(t,e,i){var a=e.length,o=n.allocUnsafe(a),s=-1;while(++s<a)o[s]=r(t,e[s],i);return o}},5291:function(t,e,i){var n=i("399f"),r=i("8707").Buffer;t.exports=function(t,e){return r.from(t.toRed(n.mont(e.modulus)).redPow(new n(e.publicExponent)).fromRed().toArray())}},"53f0":function(t,e,i){"use strict";i.r(e);var n=i("3e0d"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"54f8":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var i="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=(0,n.default)(t))||e&&t&&"number"===typeof t.length){i&&(t=i);var r=0,a=function(){};return{s:a,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,c=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return s=t.done,t},e:function(t){c=!0,o=t},f:function(){try{s||null==i["return"]||i["return"]()}finally{if(c)throw o}}}},i("a4d3"),i("e01a"),i("d3b7"),i("d28b"),i("3ca3"),i("ddb0"),i("d9e2"),i("d401");var n=function(t){return t&&t.__esModule?t:{default:t}}(i("dde1"))},"561d":function(t,e,i){(function(e){var n=i("399f"),r=i("7a10"),a=new r,o=new n(24),s=new n(11),c=new n(10),f=new n(3),u=new n(7),d=i("58a2"),h=i("11dc");function l(t,i){return i=i||"utf8",e.isBuffer(t)||(t=new e(t,i)),this._pub=new n(t),this}function p(t,i){return i=i||"utf8",e.isBuffer(t)||(t=new e(t,i)),this._priv=new n(t),this}t.exports=v;var b={};function v(t,e,i){this.setGenerator(e),this.__prime=new n(t),this._prime=n.mont(this.__prime),this._primeLen=t.length,this._pub=void 0,this._priv=void 0,this._primeCode=void 0,i?(this.setPublicKey=l,this.setPrivateKey=p):this._primeCode=8}function m(t,i){var n=new e(t.toArray());return i?n.toString(i):n}Object.defineProperty(v.prototype,"verifyError",{enumerable:!0,get:function(){return"number"!==typeof this._primeCode&&(this._primeCode=function(t,e){var i=e.toString("hex"),n=[i,t.toString(16)].join("_");if(n in b)return b[n];var r,h=0;if(t.isEven()||!d.simpleSieve||!d.fermatTest(t)||!a.test(t))return h+=1,h+="02"===i||"05"===i?8:4,b[n]=h,h;switch(a.test(t.shrn(1))||(h+=2),i){case"02":t.mod(o).cmp(s)&&(h+=8);break;case"05":r=t.mod(c),r.cmp(f)&&r.cmp(u)&&(h+=8);break;default:h+=4}return b[n]=h,h}(this.__prime,this.__gen)),this._primeCode}}),v.prototype.generateKeys=function(){return this._priv||(this._priv=new n(h(this._primeLen))),this._pub=this._gen.toRed(this._prime).redPow(this._priv).fromRed(),this.getPublicKey()},v.prototype.computeSecret=function(t){t=new n(t),t=t.toRed(this._prime);var i=t.redPow(this._priv).fromRed(),r=new e(i.toArray()),a=this.getPrime();if(r.length<a.length){var o=new e(a.length-r.length);o.fill(0),r=e.concat([o,r])}return r},v.prototype.getPublicKey=function(t){return m(this._pub,t)},v.prototype.getPrivateKey=function(t){return m(this._priv,t)},v.prototype.getPrime=function(t){return m(this.__prime,t)},v.prototype.getGenerator=function(t){return m(this._gen,t)},v.prototype.setGenerator=function(t,i){return i=i||"utf8",e.isBuffer(t)||(t=new e(t,i)),this.__gen=t,this._gen=new n(t),this}}).call(this,i("b639").Buffer)},"56b5":function(t,e,i){"use strict";var n=i("7f7a"),r=n.define("Time",(function(){this.choice({utcTime:this.utctime(),generalTime:this.gentime()})})),a=n.define("AttributeTypeValue",(function(){this.seq().obj(this.key("type").objid(),this.key("value").any())})),o=n.define("AlgorithmIdentifier",(function(){this.seq().obj(this.key("algorithm").objid(),this.key("parameters").optional(),this.key("curve").objid().optional())})),s=n.define("SubjectPublicKeyInfo",(function(){this.seq().obj(this.key("algorithm").use(o),this.key("subjectPublicKey").bitstr())})),c=n.define("RelativeDistinguishedName",(function(){this.setof(a)})),f=n.define("RDNSequence",(function(){this.seqof(c)})),u=n.define("Name",(function(){this.choice({rdnSequence:this.use(f)})})),d=n.define("Validity",(function(){this.seq().obj(this.key("notBefore").use(r),this.key("notAfter").use(r))})),h=n.define("Extension",(function(){this.seq().obj(this.key("extnID").objid(),this.key("critical").bool().def(!1),this.key("extnValue").octstr())})),l=n.define("TBSCertificate",(function(){this.seq().obj(this.key("version").explicit(0).int().optional(),this.key("serialNumber").int(),this.key("signature").use(o),this.key("issuer").use(u),this.key("validity").use(d),this.key("subject").use(u),this.key("subjectPublicKeyInfo").use(s),this.key("issuerUniqueID").implicit(1).bitstr().optional(),this.key("subjectUniqueID").implicit(2).bitstr().optional(),this.key("extensions").explicit(3).seqof(h).optional())})),p=n.define("X509Certificate",(function(){this.seq().obj(this.key("tbsCertificate").use(l),this.key("signatureAlgorithm").use(o),this.key("signatureValue").bitstr())}));t.exports=p},5712:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("14d9"),i("d3b7"),i("3ca3"),i("ddb0"),i("ac1f"),i("5319"),i("fb6a"),i("99af"),i("caad6"),i("2532"),i("e9c4"),i("841c");var r=n(i("d0ff")),a=n(i("f07e")),o=n(i("c964")),s=n(i("f3f3")),c=i("d8be"),f=i("06cd"),u=i("1e48"),d=n(i("7e99")),h=i("26cb"),l=null,p=null,b={name:"auctionGoodsDetail",data:function(){return{MAuctionGoodsStatus:c.MAuctionGoodsStatus,loading:!0,id:"",goodsDetail:{},swiperIndex:0,isLogin:!1,loginInfo:{},bidRecords:[],allBidRecordsLength:0,warnPopupVisible:!1,earnestPayPopupVisible:!1,earnestPaySelectPopupVisible:!1,earnestPaySuccessPopupVisible:!1,bidPopupVisible:!1,bidSuccessPopupVisible:!1,EUseCouponPopupVisible:!1,PAWarnPopupVisible:!1,freeShipmentPopupVisible:!1,reversePricePopupVisible:!1,earnestOrderInfo:null,earnestPayStatus:!1,earnestStatus:0,earnestOrderNo:"",anonymousName:"",provinceName:"",priceChangeToastParams:{duration:1500,visible:!1,timer:null},isExistPayeeAccount:!1,isShowFixedTime:!1,isLoadedGoodsComments:!1,isLoadedGoodsRecommends:!1,earnestCouponInfo:{}}},computed:(0,s.default)((0,s.default)((0,s.default)({},(0,h.mapState)(["routeTable","userInfo"])),(0,h.mapGetters)("auctionUser",["isDisabledAuctionUser"])),{},{isPendPutaway:function(t){var e=t.goodsDetail;return e.onsale_status===c.MAuctionGoodsStatus.PendPutaway},goodsStatusObj:function(t){var e=t.goodsDetail;return f.MAuctionGoodsStatusObjMapper[e.onsale_status]||f.MAuctionGoodsStatusObjMapper[c.MAuctionGoodsStatus.Unsuccessful]},goodsStatusText:function(t){var e=t.goodsStatusObj;return e.statusText},goodsStatusBg:function(t){var e=t.goodsStatusObj;return e.statusBg},goodsStatusTimeText:function(t){var e=t.goodsStatusObj;return e.timeText},goodsStatusTimeTextClazz:function(t){var e=t.goodsStatusObj;return e.timeTextClazz},goodsStatusTimeTextBgClazz:function(t){var e=t.goodsStatusObj;return e.timeTextBgClazz},goodsStatusTimeValue:function(t){var e=t.goodsDetail,i=t.goodsStatusObj;return e[i.timeKeyValue]},fixedTimeShowTop:function(){return uni.upx2px(534)},navbarAndFixedTimeHeight:function(){return 46+uni.upx2px(70)},isExistReservePrice:function(t){var e=t.goodsDetail;return!!e.is_reserve_price}}),methods:(0,s.default)((0,s.default)((0,s.default)((0,s.default)({},(0,h.mapMutations)(["muAddressInfoState"])),(0,h.mapActions)(["getUserInfo"])),(0,h.mapActions)("auctionUser",["getAuctionUserInfo"])),{},{init:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,i=[],!t.isLogin){e.next=7;break}return e.next=5,t.getUserInfo();case 5:t.loginInfo=uni.getStorageSync("loginInfo")||{},i.push(t.loadEarnestPayStatus(),t.getAuctionUserInfo());case 7:return i.push(t.loadAuctionGoodsDetail(),t.loadBidRecords()),e.next=10,Promise.all(i);case 10:t.loading=!1,t.connectSocket(),e.next=18;break;case 14:e.prev=14,e.t0=e["catch"](0),console.log("err",e.t0),t.pages.getPageLength()>1&&t.jump.navigateBack();case 18:case"end":return e.stop()}}),e,null,[[0,14]])})))()},loadUserForbidden:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i,n,r,o,s;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.getAuctionUserInfo();case 2:n=e.sent,r=(null===n||void 0===n||null===(i=n.data)||void 0===i?void 0:i.info)||{},o=r.status,s=void 0===o?1:o,t.isForbidden=!s;case 5:case"end":return e.stop()}}),e)})))()},loadPayeeAccount:function(){return(0,o.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})))()},loadAuctionGoodsDetail:function(){var t=arguments,e=this;return(0,o.default)((0,a.default)().mark((function i(){var n,r,o,s,c,f,u,d,h,l;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n=!(t.length>0&&void 0!==t[0])||t[0],r={id:e.id},n&&(r.source=1),e.userInfo.uid&&(r.uid=e.userInfo.uid),i.next=6,e.$u.api.getAuctionGoodsDetail(r);case 6:o=i.sent,s=(null===o||void 0===o?void 0:o.data)||{},c=s.sell_time,f=s.closing_auction_time,s.sell_time="string"===typeof c?c.replace(/-/g,"/"):c,s.closing_auction_time="string"===typeof f?f.replace(/-/g,"/"):f,u=s.product_img,d=void 0===u?[]:u,h=s.final_auction_price,l=s.price,s.$swiperList=d,s.final_auction_price="0.00"===h?l:h,e.goodsDetail=s;case 15:case"end":return i.stop()}}),i)})))()},loadBidRecords:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i,n,r;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$u.api.getAuctionBidRecords({id:t.id});case 3:n=e.sent,r=(null===n||void 0===n||null===(i=n.data)||void 0===i?void 0:i.list)||[],t.bidRecords=r.slice(0,3),t.allBidRecordsLength=r.length,e.next=11;break;case 9:e.prev=9,e.t0=e["catch"](0);case 11:case"end":return e.stop()}}),e,null,[[0,9]])})))()},loadEarnestPayStatus:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i,n,r,o,s,f,u,d,h;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.isLogin){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,t.$u.api.getAuctionEarnestStatus({type:c.MAuctionEarnestType.Bidding,goods_id:t.id});case 4:i=e.sent,n=(null===i||void 0===i?void 0:i.data)||{},r=n.is_payment,o=void 0===r?0:r,s=n.province_name,f=n.status,u=void 0===f?0:f,d=n.main_order_no,h=void 0===d?"":d,t.earnestPayStatus=!!o,t.anonymousName="".concat(s||"酒云","用户"),t.provinceName=s||"未知",t.earnestStatus=u,t.earnestOrderNo=h;case 11:case"end":return e.stop()}}),e)})))()},onShare:function(){var t=this.goodsDetail,e=t.id,i=t.title,n=t.brief,r=t.product_img;this.jump.appShare({dataType:1,title:i,des:n,img:r&&r[0]||"",path:"".concat(this.routeTable.pHAuctionGoodsDetail,"?id=").concat(e)})},onSwiperChange:function(t){this.swiperIndex=t},onSwiperClick:function(){uni.previewImage({current:this.swiperIndex,indicator:!0,urls:this.goodsDetail.$swiperList})},onEnjoy:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i,n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.login.isLoginV3(t.$vhFrom);case 2:if(i=e.sent,!i){e.next=16;break}if(n=t.goodsDetail.is_like,!n){e.next=12;break}return e.next=8,t.$u.api.cancelEnjoyAuctionGoods({goods_id:t.id});case 8:t.feedback.toast({title:"已取消收藏"}),t.goodsDetail.is_like=0,e.next=16;break;case 12:return e.next=14,t.$u.api.addEnjoyAuctionGoods({goods_id:t.id});case 14:t.feedback.toast({title:"已添加至收藏"}),t.goodsDetail.is_like=1;case 16:case"end":return e.stop()}}),e)})))()},onRemind:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i,n,r,o,s;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.login.isLoginV3(t.$vhFrom);case 2:if(i=e.sent,!i){e.next=12;break}return n=t.goodsDetail.is_msg,r={uid:t.userInfo.uid,goods_id:t.id,type:n?c.MAuctionRemindOperation.Delete:c.MAuctionRemindOperation.Add},e.next=8,t.$u.api.editRemindAuctionGoods(r);case 8:t.feedback.toast({title:n?"已取消提醒":"已添加至提醒"}),t.goodsDetail.is_msg=n?0:1,o=t.goodsDetail.msg_count,s=void 0===o?0:o,t.goodsDetail.msg_count=n?--s:++s;case 12:case"end":return e.stop()}}),e)})))()},onComment:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i,n,r;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.login.isLoginV3(t.$vhFrom);case 2:i=e.sent,i&&(uni.createSelectorQuery().in(t).select("#auction-goods-comments").boundingClientRect((function(e){uni.createSelectorQuery().in(t).select("#goods-detail").boundingClientRect((function(i){uni.pageScrollTo({scrollTop:e.top-i.top-t.navbarAndFixedTimeHeight})})).exec()})).exec(),null===t||void 0===t||null===(n=t.$refs)||void 0===n||null===(r=n.auctionGoodsCommentsRef)||void 0===r||r.open());case 4:case"end":return e.stop()}}),e)})))()},handleBid:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.login.isLoginV3(t.$vhFrom);case 2:if(i=e.sent,i){e.next=5;break}return e.abrupt("return");case 5:if(!t.isDisabledAuctionUser){e.next=8;break}return t.feedback.toast({title:"账号异常，请联系客服"}),e.abrupt("return");case 8:if(t.goodsDetail.onsale_status===c.MAuctionGoodsStatus.OnAuction){e.next=10;break}return e.abrupt("return");case 10:t.earnestPayStatus?t.bidPopupVisible=!0:t.warnPopupVisible=!0;case 11:case"end":return e.stop()}}),e)})))()},onEarnestOrderCreateSuccess:function(t){this.earnestOrderInfo=t,this.earnestPaySelectPopupVisible=!0},onEarnestOrderCreateError:function(){this.earnestPayPopupVisible=!1},onEarnestOrderPaySuccess:function(){this.earnestPayPopupVisible=!1,this.earnestPaySelectPopupVisible=!1,this.earnestPaySuccessPopupVisible=!0,this.earnestPayStatus=!0,this.earnestOrderInfo=this.$options.data().earnestOrderInfo},connectSocket:function(){var t=this,e=c.MAuctionGoodsStatus.PendAuction,i=c.MAuctionGoodsStatus.OnAuction;if([e,i].includes(this.goodsDetail.onsale_status)){var n=this.userInfo.uid||uni.getStorageSync("uniqueId")||this.$u.guid(),r=this.loginInfo.token||"",a=Date.now(),o="".concat(a).concat(r),s=JSON.stringify({action:"reg",uid:n,auction_id:+this.id}),f=d.default.encrypt(o,s);console.log("key",o),console.log("data",f);var h=4;this.$android?h=1:this.$ios&&(h=2);var l=JSON.stringify({timestamp:a,uid:n,client:h,data:f});console.log("msg",l);var p=d.default.decrypt(o,f);console.log("decryptData",p,(0,u.AUCTION_SOCKET_URL)()),uni.connectSocket({url:(0,u.AUCTION_SOCKET_URL)()}),uni.onSocketOpen((function(){uni.sendSocketMessage({data:l})})),uni.onSocketMessage((function(e){if(console.log("收到服务器内容 res",e),"heartbeats"!==e.data){var i=JSON.parse(e.data||"{}"),n=i.code,r=i.data;switch(n){case 1002:t.onBidSuccess();break;case 2e3:t.onPriceChange(r);break;case 2001:t.onAuctionDelay(r);break;case 3e3:t.onAuctionStart();break;case 3001:t.onAuctionAbort();break}}else t.isSocketDeath()})),uni.onSocketClose((function(){console.log("连接关闭")}))}},isSocketDeath:function(){this.heartBeatTimer&&clearTimeout(this.heartBeatTimer),this.heartBeatTimer=setTimeout((function(){console.log("socket death"),uni.closeSocket()}),7e4)},onBidSuccess:function(){this.bidSuccessPopupVisible=!0},onPriceChange:function(t){var e=this,i=t.bid_price,n=t.uid;if(this.goodsDetail.final_auction_price=i,this.bidRecords=[t].concat((0,r.default)(this.bidRecords.slice(0,2))),this.allBidRecordsLength++,this.bidPopupVisible&&+this.userInfo.uid!==+n&&!this.priceChangeToastParams.visible){var a=this.priceChangeToastParams.duration;this.feedback.toast({title:"当前价已变动",duration:a}),this.toastVisible=!0,this.priceChangeToastParams.timer&&clearTimeout(this.priceChangeToastParams.timer),this.priceChangeToastParams.timer=setTimeout((function(){e.priceChangeToastParams.visible=!1}),a)}},onAuctionDelay:function(t){var e=t.bid_end_time,i=1e3*+e;this.goodsDetail.closing_auction_time=i},onAuctionStart:function(){this.goodsDetail.onsale_status=c.MAuctionGoodsStatus.OnAuction},onAuctionAbort:function(){this.goodsDetail.onsale_status=c.MAuctionGoodsStatus.AuctionAbort,this.bidPopupVisible=!1,uni.closeSocket()},onClosePage:function(){uni.closeSocket(),this.heartBeatTimer&&clearTimeout(this.heartBeatTimer),this.priceChangeToastParams.timer&&clearTimeout(this.priceChangeToastParams.timer),this.closeGoodsCommentsObserver(),this.closeGoodsRecommendsObserver()},openGoodsCommentsObserver:function(){var t=this;l=uni.createIntersectionObserver(this),l.relativeToViewport().observe("#auction-goods-comments",(function(e){if(e.intersectionRatio>0){var i,n;if(!t.isLoadedGoodsComments)null===t||void 0===t||null===(i=t.$refs)||void 0===i||null===(n=i.auctionGoodsCommentsRef)||void 0===n||n.load();t.isLoadedGoodsComments=!0}}))},closeGoodsCommentsObserver:function(){l&&(l.disconnect(),l=null)},openGoodsRecommendsObserver:function(){var t=this;p=uni.createIntersectionObserver(this),p.relativeToViewport().observe("#auction-goods-recommends",(function(e){if(e.intersectionRatio>0){var i,n;if(!t.isLoadedGoodsRecommends)null===t||void 0===t||null===(i=t.$refs)||void 0===i||null===(n=i.auctionGoodsRecommendsRef)||void 0===n||n.load();t.isLoadedGoodsRecommends=!0}}))},closeGoodsRecommendsObserver:function(){p&&(p.disconnect(),p=null)},onAuctionWarnAgree:function(){var t=this;this.$u.api.getAuctionEarnestCouponList().then((function(e){var i=(null===e||void 0===e?void 0:e.data)||[];t.warnPopupVisible=!1,i.length?(t.EUseCouponPopupVisible=!0,t.earnestCouponInfo=i[0]):(t.earnestPayPopupVisible=!0,t.earnestCouponInfo=t.$options.data().earnestCouponInfo)}))},onAEUseCouponCanel:function(){this.EUseCouponPopupVisible=!1,this.earnestCouponInfo=this.$options.data().earnestCouponInfo,this.earnestPayPopupVisible=!0},onAEUseCouponUse:function(){this.EUseCouponPopupVisible=!1,this.earnestPayPopupVisible=!0}}),onLoad:function(t){var e=t.id,i=t.aePaySuccess;if(this.id=e,this.muAddressInfoState({}),i){var n,r;this.onEarnestOrderPaySuccess();var a=location||{},o=a.search;null===(n=window)||void 0===n||null===(r=n.history)||void 0===r||r.replaceState(null,"",o.replace("aePaySuccess=1",""))}},onShow:function(){var t=this;this.login.isLoginV3(this.$vhFrom,0).then((function(e){var i,n;t.isLogin=e,t.init().finally((function(){if(t.isLogin){var e,i,n,r;if(t.earnestOrderInfo)null===t||void 0===t||null===(n=t.$refs)||void 0===n||null===(r=n.auctionEarnestPaySelectPopupRef)||void 0===r||r.queryPayStatus();null===t||void 0===t||null===(e=t.$refs)||void 0===e||null===(i=e.auctionGoodsCommentsRef)||void 0===i||i.loadAgreementStatus()}t.openGoodsCommentsObserver(),t.openGoodsRecommendsObserver()})),t.isLogin&&(null===t||void 0===t||null===(i=t.$refs)||void 0===i||null===(n=i.auctionEarnestPayPopupRef)||void 0===n||n.initAddressInfo())}))},onHide:function(){console.log("onHide"),this.onClosePage()},onUnload:function(){console.log("onUnload"),this.onClosePage()},onPullDownRefresh:function(){var t,e;this.onClosePage(),null===this||void 0===this||null===(t=this.$refs)||void 0===t||null===(e=t.auctionGoodsCommentsRef)||void 0===e||e.load(),this.init().finally((function(){uni.stopPullDownRefresh()}))},onPageScroll:function(t){this.isPendPutaway||(this.isShowFixedTime=t.scrollTop>this.fixedTimeShowTop)}};e.default=b},5756:function(t,e,i){"use strict";i.r(e);var n=i("c6cc"),r=i("c3d3");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"df3aa5c0",null,!1,n["a"],void 0);e["default"]=s.exports},5786:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uPopup:i("c4b0").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-popup",{attrs:{value:t.value,mode:"bottom",width:"100%",height:"600","border-radius":"20"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"d-flex j-center pt-98"},[i("v-uni-image",{staticClass:"w-266 h-184",attrs:{src:t.ossIcon("/auction/icon_pay_success.png")}})],1),i("v-uni-view",{staticClass:"mt-16 font-32 text-3 text-center"},[t._v("出价成功")]),i("v-uni-view",{staticClass:"mt-10 font-28 text-6 text-center"},[t._v("请持续关注，竞拍情况")]),i("v-uni-button",{staticClass:"vh-btn flex-c-c mtb-00-mlr-auto mt-96 w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput(!1)}}},[t._v("确认")])],1)},a=[]},"58a2":function(t,e,i){var n=i("11dc");t.exports=y,y.simpleSieve=m,y.fermatTest=g;var r=i("399f"),a=new r(24),o=i("7a10"),s=new o,c=new r(1),f=new r(2),u=new r(5),d=(new r(16),new r(8),new r(10)),h=new r(3),l=(new r(7),new r(11)),p=new r(4),b=(new r(12),null);function v(){if(null!==b)return b;var t=[];t[0]=2;for(var e=1,i=3;i<1048576;i+=2){for(var n=Math.ceil(Math.sqrt(i)),r=0;r<e&&t[r]<=n;r++)if(i%t[r]===0)break;e!==r&&t[r]<=n||(t[e++]=i)}return b=t,t}function m(t){for(var e=v(),i=0;i<e.length;i++)if(0===t.modn(e[i]))return 0===t.cmpn(e[i]);return!0}function g(t){var e=r.mont(t);return 0===f.toRed(e).redPow(t.subn(1)).fromRed().cmpn(1)}function y(t,e){if(t<16)return new r(2===e||5===e?[140,123]:[140,39]);var i,o;e=new r(e);while(1){i=new r(n(Math.ceil(t/8)));while(i.bitLength()>t)i.ishrn(1);if(i.isEven()&&i.iadd(c),i.testn(1)||i.iadd(f),e.cmp(f)){if(!e.cmp(u))while(i.mod(d).cmp(h))i.iadd(p)}else while(i.mod(a).cmp(l))i.iadd(p);if(o=i.shrn(1),m(o)&&m(i)&&g(o)&&g(i)&&s.test(o)&&s.test(i))return i}}},"58e9":function(t,e,i){"use strict";i.r(e);var n=i("aa7d"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},5919:function(t,e,i){"use strict";e.sha1=i("13e2"),e.sha224=i("07f2"),e.sha256=i("6eed"),e.sha384=i("8b95"),e.sha512=i("b525")},"5a03":function(t,e,i){"use strict";i.r(e);var n=i("c4b4"),r=i("7e6c");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);i("22ad");var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"313b0874",null,!1,n["a"],void 0);e["default"]=s.exports},"5a44":function(t,e,i){"use strict";var n=i("0885"),r=i.n(n);r.a},"5a76":function(t,e,i){var n=i("f576");t.exports=function(t){return(new n).update(t).digest()}},"5d80":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d3b7"),i("159b"),i("7db0"),i("3c65"),i("caad6"),i("2532");var r=n(i("f07e")),a=n(i("c964")),o=n(i("f3f3")),s=i("70c3"),c=i("26cb"),f={props:{goodsId:{required:!0}},data:function(){return{RabbitHeadEmojiList:s.RabbitHeadEmojiList,RabbitEmojiList:s.RabbitEmojiList,CommonEmojiList:s.CommonEmojiList,comments:[],popupVisible:!1,commentParams:{goods_id:"",content:"",emoji_image:"",pid:""},showEmo:!1,emoTypeList:["兔头","兔子","Emoji"],emoTypeIndex:0,placeholder:"说说你的观点…",checked:!1,isAgreed:!1}},computed:(0,o.default)((0,o.default)({},(0,c.mapState)(["agreementPrefix","userInfo"])),{},{disabled:function(t){var e=t.commentParams,i=t.checked,n=t.isAgreed;return!(e.content.length&&(n||i))}}),methods:{load:function(){var t=this;this.$u.api.getAuctionGoodsComments({goods_id:this.goodsId}).then((function(e){var i,n=(null===e||void 0===e||null===(i=e.data)||void 0===i?void 0:i.list)||[];n.forEach((function(t){t.$replysLen=(null===t||void 0===t?void 0:t.replys.length)||0,t.$isShowAllReply=!1})),t.comments=n}))},open:function(){this.popupVisible=!0;var t=this.$options.data(),e=t.commentParams,i=t.placeholder;this.commentParams=Object.assign({},e,{goods_id:this.goodsId}),this.placeholder=i},addEmoji:function(t){this.commText=this.commText+t},selectEmoType:function(t){this.emoTypeIndex=t},onEnjoy:function(t){var e=this;return(0,a.default)((0,r.default)().mark((function i(){var n,a,o,s,c,f,u,d;return(0,r.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,e.login.isLoginV3(e.$vhFrom);case 2:if(n=i.sent,n){i.next=5;break}return i.abrupt("return");case 5:if(a=t.id,o=t.top_pid,s=t.like_status,c=null,o?(f=e.comments.find((function(t){return t.id===o}))||{},u=f.replys,d=void 0===u?[]:u,c=d.find((function(t){return t.id===a}))):c=e.comments.find((function(t){return t.id===a})),c){i.next=10;break}return i.abrupt("return");case 10:if(e.feedback.loading({title:""}),i.prev=11,!s){i.next=19;break}return i.next=15,e.$u.api.cancelEnjoyAuctionGoodsComment({goods_id:e.goodsId,comment_id:a});case 15:c.like_status=0,c.like_nums--,i.next=23;break;case 19:return i.next=21,e.$u.api.addEnjoyAuctionGoodsComment({goods_id:e.goodsId,comment_id:a});case 21:c.like_status=1,c.like_nums++;case 23:return i.prev=23,e.feedback.hideLoading(),i.finish(23);case 26:case"end":return i.stop()}}),i,null,[[11,,23,26]])})))()},onSubmit:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i,n,a,o,s,c;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.disabled){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,t.$u.api.addAuctionGoodsComment(t.commentParams);case 4:if(i=e.sent,n=(null===i||void 0===i?void 0:i.data)||null,n){e.next=8;break}return e.abrupt("return");case 8:a=t.commentParams,o=a.pid,s=a.topPid,"刚刚",o?(c=t.comments.find((function(t){return t.id==s})),c&&(Object.assign(n,{like_status:0,like_nums:0,comment_time:"刚刚"}),c.replys.unshift(n),c.$replysLen++)):(Object.assign(n,{replys:[],$replysLen:0,$isShowAllReply:!1,like_status:0,like_nums:0,comment_time:"刚刚"}),t.comments.unshift(n)),t.popupVisible=!1,t.isAgreed||t.$u.api.agreeProtocol({xid:1}).then((function(){uni.setStorageSync("protocolList",["1"]),t.isAgreed=!0}));case 13:case"end":return e.stop()}}),e)})))()},onReply:function(t){var e=this;return(0,a.default)((0,r.default)().mark((function i(){var n,a,o,s;return(0,r.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return i.next=2,e.login.isLoginV3(e.$vhFrom);case 2:if(n=i.sent,n){i.next=5;break}return i.abrupt("return");case 5:a=t.id,o=t.top_pid,s=t.nickname,e.popupVisible=!0,e.commentParams=Object.assign({},e.$options.data().commentParams,{goods_id:e.goodsId,pid:a,topPid:o||a}),e.placeholder="@".concat(s,":");case 9:case"end":return i.stop()}}),i)})))()},loadAgreementStatus:function(){var t=this;uni.getStorage({key:"protocolList",success:function(e){t.isAgreed=e.data.includes("1")},fail:function(){var e=(0,a.default)((0,r.default)().mark((function e(){var i,n;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:n=(null===t||void 0===t||null===(i=t.userInfo)||void 0===i?void 0:i.protocol)||[],t.isAgreed=n.includes("1"),t.isAgreed&&uni.setStorageSync("protocolList",n);case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}()})},onToggleReply:function(t){var e=t.id,i=this.comments.find((function(t){return t.id===e}));i&&(i.$isShowAllReply=!i.$isShowAllReply)}}};e.default=f},"5e1a":function(t,e,i){"use strict";var n=i("8707").Buffer,r=i(2);function a(t,e,i){t.copy(e,i)}t.exports=function(){function t(){(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")})(this,t),this.head=null,this.tail=null,this.length=0}return t.prototype.push=function(t){var e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length},t.prototype.unshift=function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length},t.prototype.shift=function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}},t.prototype.clear=function(){this.head=this.tail=null,this.length=0},t.prototype.join=function(t){if(0===this.length)return"";var e=this.head,i=""+e.data;while(e=e.next)i+=t+e.data;return i},t.prototype.concat=function(t){if(0===this.length)return n.alloc(0);if(1===this.length)return this.head.data;var e=n.allocUnsafe(t>>>0),i=this.head,r=0;while(i)a(i.data,e,r),r+=i.data.length,i=i.next;return e},t}(),r&&r.inspect&&r.inspect.custom&&(t.exports.prototype[r.inspect.custom]=function(){var t=r.inspect({length:this.length});return this.constructor.name+" "+t})},"5ee7":function(t,e,i){"use strict";e.readUInt32BE=function(t,e){var i=t[0+e]<<24|t[1+e]<<16|t[2+e]<<8|t[3+e];return i>>>0},e.writeUInt32BE=function(t,e,i){t[0+i]=e>>>24,t[1+i]=e>>>16&255,t[2+i]=e>>>8&255,t[3+i]=255&e},e.ip=function(t,e,i,n){for(var r=0,a=0,o=6;o>=0;o-=2){for(var s=0;s<=24;s+=8)r<<=1,r|=e>>>s+o&1;for(s=0;s<=24;s+=8)r<<=1,r|=t>>>s+o&1}for(o=6;o>=0;o-=2){for(s=1;s<=25;s+=8)a<<=1,a|=e>>>s+o&1;for(s=1;s<=25;s+=8)a<<=1,a|=t>>>s+o&1}i[n+0]=r>>>0,i[n+1]=a>>>0},e.rip=function(t,e,i,n){for(var r=0,a=0,o=0;o<4;o++)for(var s=24;s>=0;s-=8)r<<=1,r|=e>>>s+o&1,r<<=1,r|=t>>>s+o&1;for(o=4;o<8;o++)for(s=24;s>=0;s-=8)a<<=1,a|=e>>>s+o&1,a<<=1,a|=t>>>s+o&1;i[n+0]=r>>>0,i[n+1]=a>>>0},e.pc1=function(t,e,i,n){for(var r=0,a=0,o=7;o>=5;o--){for(var s=0;s<=24;s+=8)r<<=1,r|=e>>s+o&1;for(s=0;s<=24;s+=8)r<<=1,r|=t>>s+o&1}for(s=0;s<=24;s+=8)r<<=1,r|=e>>s+o&1;for(o=1;o<=3;o++){for(s=0;s<=24;s+=8)a<<=1,a|=e>>s+o&1;for(s=0;s<=24;s+=8)a<<=1,a|=t>>s+o&1}for(s=0;s<=24;s+=8)a<<=1,a|=t>>s+o&1;i[n+0]=r>>>0,i[n+1]=a>>>0},e.r28shl=function(t,e){return t<<e&268435455|t>>>28-e};var n=[14,11,17,4,27,23,25,0,13,22,7,18,5,9,16,24,2,20,12,21,1,8,15,26,15,4,25,19,9,1,26,16,5,11,23,8,12,7,17,0,22,3,10,14,6,20,27,24];e.pc2=function(t,e,i,r){for(var a=0,o=0,s=n.length>>>1,c=0;c<s;c++)a<<=1,a|=t>>>n[c]&1;for(c=s;c<n.length;c++)o<<=1,o|=e>>>n[c]&1;i[r+0]=a>>>0,i[r+1]=o>>>0},e.expand=function(t,e,i){var n=0,r=0;n=(1&t)<<5|t>>>27;for(var a=23;a>=15;a-=4)n<<=6,n|=t>>>a&63;for(a=11;a>=3;a-=4)r|=t>>>a&63,r<<=6;r|=(31&t)<<1|t>>>31,e[i+0]=n>>>0,e[i+1]=r>>>0};var r=[14,0,4,15,13,7,1,4,2,14,15,2,11,13,8,1,3,10,10,6,6,12,12,11,5,9,9,5,0,3,7,8,4,15,1,12,14,8,8,2,13,4,6,9,2,1,11,7,15,5,12,11,9,3,7,14,3,10,10,0,5,6,0,13,15,3,1,13,8,4,14,7,6,15,11,2,3,8,4,14,9,12,7,0,2,1,13,10,12,6,0,9,5,11,10,5,0,13,14,8,7,10,11,1,10,3,4,15,13,4,1,2,5,11,8,6,12,7,6,12,9,0,3,5,2,14,15,9,10,13,0,7,9,0,14,9,6,3,3,4,15,6,5,10,1,2,13,8,12,5,7,14,11,12,4,11,2,15,8,1,13,1,6,10,4,13,9,0,8,6,15,9,3,8,0,7,11,4,1,15,2,14,12,3,5,11,10,5,14,2,7,12,7,13,13,8,14,11,3,5,0,6,6,15,9,0,10,3,1,4,2,7,8,2,5,12,11,1,12,10,4,14,15,9,10,3,6,15,9,0,0,6,12,10,11,1,7,13,13,8,15,9,1,4,3,5,14,11,5,12,2,7,8,2,4,14,2,14,12,11,4,2,1,12,7,4,10,7,11,13,6,1,8,5,5,0,3,15,15,10,13,3,0,9,14,8,9,6,4,11,2,8,1,12,11,7,10,1,13,14,7,2,8,13,15,6,9,15,12,0,5,9,6,10,3,4,0,5,14,3,12,10,1,15,10,4,15,2,9,7,2,12,6,9,8,5,0,6,13,1,3,13,4,14,14,0,7,11,5,3,11,8,9,4,14,3,15,2,5,12,2,9,8,5,12,15,3,10,7,11,0,14,4,1,10,7,1,6,13,0,11,8,6,13,4,13,11,0,2,11,14,7,15,4,0,9,8,1,13,10,3,14,12,3,9,5,7,12,5,2,10,15,6,8,1,6,1,6,4,11,11,13,13,8,12,1,3,4,7,10,14,7,10,9,15,5,6,0,8,15,0,14,5,2,9,3,2,12,13,1,2,15,8,13,4,8,6,10,15,3,11,7,1,4,10,12,9,5,3,6,14,11,5,0,0,14,12,9,7,2,7,2,11,1,4,14,1,7,9,4,12,10,14,8,2,13,0,15,6,12,10,9,13,0,15,3,3,5,5,6,8,11];e.substitute=function(t,e){for(var i=0,n=0;n<4;n++){var a=t>>>18-6*n&63,o=r[64*n+a];i<<=4,i|=o}for(n=0;n<4;n++){a=e>>>18-6*n&63,o=r[256+64*n+a];i<<=4,i|=o}return i>>>0};var a=[16,25,12,11,3,20,4,15,31,17,9,6,27,14,1,22,30,24,8,18,0,5,29,23,13,19,2,26,10,21,28,7];e.permute=function(t){for(var e=0,i=0;i<a.length;i++)e<<=1,e|=t>>>a[i]&1;return e>>>0},e.padSplit=function(t,e,i){var n=t.toString(2);while(n.length<e)n="0"+n;for(var r=[],a=0;a<e;a+=i)r.push(n.slice(a,a+i));return r.join(" ")}},"5f76":function(t,e,i){"use strict";i.r(e);var n=i("8ca3"),r=i("db4c");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"76d6ce57",null,!1,n["a"],void 0);e["default"]=s.exports},6280:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"font-wei-500 font-32 text-3 l-h-44 text-center"},[t._v("温馨提示")]),i("v-uni-view",{staticClass:"mt-20"},t._l(t.tips,(function(e,n){return i("v-uni-view",{key:n,class:n?"mt-28":""},[i("v-uni-view",{staticClass:"font-wei-500 font-28 text-3 l-h-48"},[t._v(t._s(e.title))]),t._l(e.list,(function(e,n){return i("v-uni-view",{key:n,staticClass:"d-flex mt-10"},[i("v-uni-view",{staticClass:"pt-12 w-30"},[i("v-uni-view",{staticClass:"w-10 h-10 bg-ff9127 b-rad-p50"})],1),i("v-uni-view",{staticClass:"flex-1 font-24 text-6 l-h-34"},[t._v(t._s(e))])],1)}))],2)})),1)],1)},r=[]},6283:function(t,e,i){"use strict";const n=i("3fb5"),r=i("d1c8").Reporter,a=i("c591").Buffer;function o(t,e){r.call(this,e),a.isBuffer(t)?(this.base=t,this.offset=0,this.length=t.length):this.error("Input not Buffer")}function s(t,e){if(Array.isArray(t))this.length=0,this.value=t.map((function(t){return s.isEncoderBuffer(t)||(t=new s(t,e)),this.length+=t.length,t}),this);else if("number"===typeof t){if(!(0<=t&&t<=255))return e.error("non-byte EncoderBuffer value");this.value=t,this.length=1}else if("string"===typeof t)this.value=t,this.length=a.byteLength(t);else{if(!a.isBuffer(t))return e.error("Unsupported type: "+typeof t);this.value=t,this.length=t.length}}n(o,r),e.DecoderBuffer=o,o.isDecoderBuffer=function(t){if(t instanceof o)return!0;const e="object"===typeof t&&a.isBuffer(t.base)&&"DecoderBuffer"===t.constructor.name&&"number"===typeof t.offset&&"number"===typeof t.length&&"function"===typeof t.save&&"function"===typeof t.restore&&"function"===typeof t.isEmpty&&"function"===typeof t.readUInt8&&"function"===typeof t.skip&&"function"===typeof t.raw;return e},o.prototype.save=function(){return{offset:this.offset,reporter:r.prototype.save.call(this)}},o.prototype.restore=function(t){const e=new o(this.base);return e.offset=t.offset,e.length=this.offset,this.offset=t.offset,r.prototype.restore.call(this,t.reporter),e},o.prototype.isEmpty=function(){return this.offset===this.length},o.prototype.readUInt8=function(t){return this.offset+1<=this.length?this.base.readUInt8(this.offset++,!0):this.error(t||"DecoderBuffer overrun")},o.prototype.skip=function(t,e){if(!(this.offset+t<=this.length))return this.error(e||"DecoderBuffer overrun");const i=new o(this.base);return i._reporterState=this._reporterState,i.offset=this.offset,i.length=this.offset+t,this.offset+=t,i},o.prototype.raw=function(t){return this.base.slice(t?t.offset:this.offset,this.length)},e.EncoderBuffer=s,s.isEncoderBuffer=function(t){if(t instanceof s)return!0;const e="object"===typeof t&&"EncoderBuffer"===t.constructor.name&&"number"===typeof t.length&&"function"===typeof t.join;return e},s.prototype.join=function(t,e){return t||(t=a.alloc(this.length)),e||(e=0),0===this.length||(Array.isArray(this.value)?this.value.forEach((function(i){i.join(t,e),e+=i.length})):("number"===typeof this.value?t[e]=this.value:"string"===typeof this.value?t.write(this.value,e):a.isBuffer(this.value)&&this.value.copy(t,e),e+=this.length)),t}},"62c9":function(t,e,i){var n=i("8707").Buffer;function r(t,e,i){var r=t._cipher.encryptBlock(t._prev),a=r[0]^e;return t._prev=n.concat([t._prev.slice(1),n.from([i?e:a])]),a}e.encrypt=function(t,e,i){var a=e.length,o=n.allocUnsafe(a),s=-1;while(++s<a)o[s]=r(t,e[s],i);return o}},6430:function(t,e,i){var n=i("8707").Buffer,r=i("d485").Transform,a=i("7d72").StringDecoder,o=i("3fb5");function s(t){r.call(this),this.hashMode="string"===typeof t,this.hashMode?this[t]=this._finalOrDigest:this.final=this._finalOrDigest,this._final&&(this.__final=this._final,this._final=null),this._decoder=null,this._encoding=null}o(s,r),s.prototype.update=function(t,e,i){"string"===typeof t&&(t=n.from(t,e));var r=this._update(t);return this.hashMode?this:(i&&(r=this._toString(r,i)),r)},s.prototype.setAutoPadding=function(){},s.prototype.getAuthTag=function(){throw new Error("trying to get auth tag in unsupported state")},s.prototype.setAuthTag=function(){throw new Error("trying to set auth tag in unsupported state")},s.prototype.setAAD=function(){throw new Error("trying to set aad in unsupported state")},s.prototype._transform=function(t,e,i){var n;try{this.hashMode?this._update(t):this.push(this._update(t))}catch(r){n=r}finally{i(n)}},s.prototype._flush=function(t){var e;try{this.push(this.__final())}catch(i){e=i}t(e)},s.prototype._finalOrDigest=function(t){var e=this.__final()||n.alloc(0);return t&&(e=this._toString(e,t,!0)),e},s.prototype._toString=function(t,e,i){if(this._decoder||(this._decoder=new a(e),this._encoding=e),this._encoding!==e)throw new Error("can't switch encodings");var n=this._decoder.write(t);return i&&(n+=this._decoder.end()),n},t.exports=s},6442:function(t,e,i){e.publicEncrypt=i("ad25"),e.privateDecrypt=i("0f2c"),e.privateEncrypt=function(t,i){return e.publicEncrypt(t,i,!0)},e.publicDecrypt=function(t,i){return e.privateDecrypt(t,i,!0)}},6703:function(t,e,i){"use strict";i.r(e);var n=i("09c0"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"676f":function(t,e,i){"use strict";var n=i("399f"),r=i("3fb5"),a=i("ea537"),o=i("f3a3");function s(t){a.call(this,"mont",t),this.a=new n(t.a,16).toRed(this.red),this.b=new n(t.b,16).toRed(this.red),this.i4=new n(4).toRed(this.red).redInvm(),this.two=new n(2).toRed(this.red),this.a24=this.i4.redMul(this.a.redAdd(this.two))}function c(t,e,i){a.BasePoint.call(this,t,"projective"),null===e&&null===i?(this.x=this.curve.one,this.z=this.curve.zero):(this.x=new n(e,16),this.z=new n(i,16),this.x.red||(this.x=this.x.toRed(this.curve.red)),this.z.red||(this.z=this.z.toRed(this.curve.red)))}r(s,a),t.exports=s,s.prototype.validate=function(t){var e=t.normalize().x,i=e.redSqr(),n=i.redMul(e).redAdd(i.redMul(this.a)).redAdd(e),r=n.redSqrt();return 0===r.redSqr().cmp(n)},r(c,a.BasePoint),s.prototype.decodePoint=function(t,e){return this.point(o.toArray(t,e),1)},s.prototype.point=function(t,e){return new c(this,t,e)},s.prototype.pointFromJSON=function(t){return c.fromJSON(this,t)},c.prototype.precompute=function(){},c.prototype._encode=function(){return this.getX().toArray("be",this.curve.p.byteLength())},c.fromJSON=function(t,e){return new c(t,e[0],e[1]||t.one)},c.prototype.inspect=function(){return this.isInfinity()?"<EC Point Infinity>":"<EC Point x: "+this.x.fromRed().toString(16,2)+" z: "+this.z.fromRed().toString(16,2)+">"},c.prototype.isInfinity=function(){return 0===this.z.cmpn(0)},c.prototype.dbl=function(){var t=this.x.redAdd(this.z),e=t.redSqr(),i=this.x.redSub(this.z),n=i.redSqr(),r=e.redSub(n),a=e.redMul(n),o=r.redMul(n.redAdd(this.curve.a24.redMul(r)));return this.curve.point(a,o)},c.prototype.add=function(){throw new Error("Not supported on Montgomery curve")},c.prototype.diffAdd=function(t,e){var i=this.x.redAdd(this.z),n=this.x.redSub(this.z),r=t.x.redAdd(t.z),a=t.x.redSub(t.z),o=a.redMul(i),s=r.redMul(n),c=e.z.redMul(o.redAdd(s).redSqr()),f=e.x.redMul(o.redISub(s).redSqr());return this.curve.point(c,f)},c.prototype.mul=function(t){for(var e=t.clone(),i=this,n=this.curve.point(null,null),r=[];0!==e.cmpn(0);e.iushrn(1))r.push(e.andln(1));for(var a=r.length-1;a>=0;a--)0===r[a]?(i=i.diffAdd(n,this),n=n.dbl()):(n=i.diffAdd(n,this),i=i.dbl());return n},c.prototype.mulAdd=function(){throw new Error("Not supported on Montgomery curve")},c.prototype.jumlAdd=function(){throw new Error("Not supported on Montgomery curve")},c.prototype.eq=function(t){return 0===this.getX().cmp(t.getX())},c.prototype.normalize=function(){return this.x=this.x.redMul(this.z.redInvm()),this.z=this.curve.one,this},c.prototype.getX=function(){return this.normalize(),this.x.fromRed()}},"69f2":function(t,e,i){e=t.exports=function(t){t=t.toLowerCase();var i=e[t];if(!i)throw new Error(t+" is not supported (we accept pull requests)");return new i};e.sha=i("087f"),e.sha1=i("7e78"),e.sha224=i("72aa"),e.sha256=i("a255"),e.sha384=i("b837"),e.sha512=i("4fd1")},"6aa2":function(t,e,i){"use strict";var n=i("7d92"),r=i("7658"),a=i("da3e");function o(t){if(!(this instanceof o))return new o(t);this.hash=t.hash,this.predResist=!!t.predResist,this.outLen=this.hash.outSize,this.minEntropy=t.minEntropy||this.hash.hmacStrength,this._reseed=null,this.reseedInterval=null,this.K=null,this.V=null;var e=r.toArray(t.entropy,t.entropyEnc||"hex"),i=r.toArray(t.nonce,t.nonceEnc||"hex"),n=r.toArray(t.pers,t.persEnc||"hex");a(e.length>=this.minEntropy/8,"Not enough entropy. Minimum is: "+this.minEntropy+" bits"),this._init(e,i,n)}t.exports=o,o.prototype._init=function(t,e,i){var n=t.concat(e).concat(i);this.K=new Array(this.outLen/8),this.V=new Array(this.outLen/8);for(var r=0;r<this.V.length;r++)this.K[r]=0,this.V[r]=1;this._update(n),this._reseed=1,this.reseedInterval=281474976710656},o.prototype._hmac=function(){return new n.hmac(this.hash,this.K)},o.prototype._update=function(t){var e=this._hmac().update(this.V).update([0]);t&&(e=e.update(t)),this.K=e.digest(),this.V=this._hmac().update(this.V).digest(),t&&(this.K=this._hmac().update(this.V).update([1]).update(t).digest(),this.V=this._hmac().update(this.V).digest())},o.prototype.reseed=function(t,e,i,n){"string"!==typeof e&&(n=i,i=e,e=null),t=r.toArray(t,e),i=r.toArray(i,n),a(t.length>=this.minEntropy/8,"Not enough entropy. Minimum is: "+this.minEntropy+" bits"),this._update(t.concat(i||[])),this._reseed=1},o.prototype.generate=function(t,e,i,n){if(this._reseed>this.reseedInterval)throw new Error("Reseed is required");"string"!==typeof e&&(n=i,i=e,e=null),i&&(i=r.toArray(i,n||"hex"),this._update(i));var a=[];while(a.length<t)this.V=this._hmac().update(this.V).digest(),a=a.concat(this.V);var o=a.slice(0,t);return this._update(i),this._reseed++,r.encode(o,e)}},"6ade":function(t,e,i){var n=i("8c8a"),r=i("8707").Buffer,a=i("bd9d");function o(t){var e=t._cipher.encryptBlockRaw(t._prev);return a(t._prev),e}e.encrypt=function(t,e){var i=Math.ceil(e.length/16),a=t._cache.length;t._cache=r.concat([t._cache,r.allocUnsafe(16*i)]);for(var s=0;s<i;s++){var c=o(t),f=a+16*s;t._cache.writeUInt32BE(c[0],f+0),t._cache.writeUInt32BE(c[1],f+4),t._cache.writeUInt32BE(c[2],f+8),t._cache.writeUInt32BE(c[3],f+12)}var u=t._cache.slice(0,e.length);return t._cache=t._cache.slice(e.length),n(e,u)}},"6eed":function(t,e,i){"use strict";var n=i("c3c0"),r=i("edc9"),a=i("aa56"),o=i("da3e"),s=n.sum32,c=n.sum32_4,f=n.sum32_5,u=a.ch32,d=a.maj32,h=a.s0_256,l=a.s1_256,p=a.g0_256,b=a.g1_256,v=r.BlockHash,m=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];function g(){if(!(this instanceof g))return new g;v.call(this),this.h=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],this.k=m,this.W=new Array(64)}n.inherits(g,v),t.exports=g,g.blockSize=512,g.outSize=256,g.hmacStrength=192,g.padLength=64,g.prototype._update=function(t,e){for(var i=this.W,n=0;n<16;n++)i[n]=t[e+n];for(;n<i.length;n++)i[n]=c(b(i[n-2]),i[n-7],p(i[n-15]),i[n-16]);var r=this.h[0],a=this.h[1],v=this.h[2],m=this.h[3],g=this.h[4],y=this.h[5],w=this.h[6],_=this.h[7];for(o(this.k.length===i.length),n=0;n<i.length;n++){var x=f(_,l(g),u(g,y,w),this.k[n],i[n]),S=s(h(r),d(r,a,v));_=w,w=y,y=g,g=s(m,x),m=v,v=a,a=r,r=s(x,S)}this.h[0]=s(this.h[0],r),this.h[1]=s(this.h[1],a),this.h[2]=s(this.h[2],v),this.h[3]=s(this.h[3],m),this.h[4]=s(this.h[4],g),this.h[5]=s(this.h[5],y),this.h[6]=s(this.h[6],w),this.h[7]=s(this.h[7],_)},g.prototype._digest=function(t){return"hex"===t?n.toHex32(this.h,"big"):n.split32(this.h,"big")}},"6fe7":function(t,e,i){var n=i("8707").Buffer,r=i("1a2a"),a=i("a958"),o=i("3337").ec,s=i("399f"),c=i("2aee"),f=i("cd91");function u(t,e,i,a){if(t=n.from(t.toArray()),t.length<e.byteLength()){var o=n.alloc(e.byteLength()-t.length);t=n.concat([o,t])}var s=i.length,c=function(t,e){t=d(t,e),t=t.mod(e);var i=n.from(t.toArray());if(i.length<e.byteLength()){var r=n.alloc(e.byteLength()-i.length);i=n.concat([r,i])}return i}(i,e),f=n.alloc(s);f.fill(1);var u=n.alloc(s);return u=r(a,u).update(f).update(n.from([0])).update(t).update(c).digest(),f=r(a,u).update(f).digest(),u=r(a,u).update(f).update(n.from([1])).update(t).update(c).digest(),f=r(a,u).update(f).digest(),{k:u,v:f}}function d(t,e){var i=new s(t),n=(t.length<<3)-e.bitLength();return n>0&&i.ishrn(n),i}function h(t,e,i){var a,o;do{a=n.alloc(0);while(8*a.length<t.bitLength())e.v=r(i,e.k).update(e.v).digest(),a=n.concat([a,e.v]);o=d(a,t),e.k=r(i,e.k).update(e.v).update(n.from([0])).digest(),e.v=r(i,e.k).update(e.v).digest()}while(-1!==o.cmp(t));return o}function l(t,e,i,n){return t.toRed(s.mont(i)).redPow(e).fromRed().mod(n)}t.exports=function(t,e,i,r,p){var b=c(e);if(b.curve){if("ecdsa"!==r&&"ecdsa/rsa"!==r)throw new Error("wrong private key type");return function(t,e){var i=f[e.curve.join(".")];if(!i)throw new Error("unknown curve "+e.curve.join("."));var r=new o(i),a=r.keyFromPrivate(e.privateKey),s=a.sign(t);return n.from(s.toDER())}(t,b)}if("dsa"===b.type){if("dsa"!==r)throw new Error("wrong private key type");return function(t,e,i){var r,a=e.params.priv_key,o=e.params.p,c=e.params.q,f=e.params.g,p=new s(0),b=d(t,c).mod(c),v=!1,m=u(a,c,t,i);while(!1===v)r=h(c,m,i),p=l(f,r,o,c),v=r.invm(c).imul(b.add(a.mul(p))).mod(c),0===v.cmpn(0)&&(v=!1,p=new s(0));return function(t,e){t=t.toArray(),e=e.toArray(),128&t[0]&&(t=[0].concat(t));128&e[0]&&(e=[0].concat(e));var i=t.length+e.length+4,r=[48,i,2,t.length];return r=r.concat(t,[2,e.length],e),n.from(r)}(p,v)}(t,b,i)}if("rsa"!==r&&"ecdsa/rsa"!==r)throw new Error("wrong private key type");t=n.concat([p,t]);var v=b.modulus.byteLength(),m=[0,1];while(t.length+m.length+1<v)m.push(255);m.push(0);var g=-1;while(++g<t.length)m.push(t[g]);var y=a(m,b);return y},t.exports.getKey=u,t.exports.makeKey=h},"70c3":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.RabbitHeadEmojiList=e.RabbitEmojiList=e.CommonEmojiList=void 0;var r=n(i("f3f3"));i("dca8"),i("d81d");var a=i("b178"),o=function(t){return(0,a.ossIcon)("".concat(t,".gif"),0,"https://images.vinehoo.com/vinehoomini/v3/emoticon/")},s=Object.freeze([{reg:"[rabbithead_吨吨吨]",img:"rh1",name:"吨吨吨"},{reg:"[rabbithead_买买买]",img:"rh2",name:"买买买"},{reg:"[rabbithead_剁手]",img:"rh3",name:"剁手"},{reg:"[rabbithead_难喝]",img:"rh4",name:"难喝"},{reg:"[rabbithead_喜欢]",img:"rh5",name:"喜欢"},{reg:"[rabbithead_好]",img:"rh6",name:"好"},{reg:"[rabbithead_嘿嘿嘿]",img:"rh7",name:"嘿嘿嘿"},{reg:"[rabbithead_晕了]",img:"rh8",name:"晕了"},{reg:"[rabbithead_哇]",img:"rh9",name:"哇"},{reg:"[rabbithead_推荐]",img:"rh10",name:"推荐"},{reg:"[rabbithead_悠闲]",img:"rh11",name:"悠闲"},{reg:"[rabbithead_帅气]",img:"rh12",name:"帅气"},{reg:"[rabbithead_思考]",img:"rh13",name:"思考"},{reg:"[rabbithead_笑哭]",img:"rh14",name:"笑哭"},{reg:"[rabbithead_愤怒]",img:"rh15",name:"愤怒"},{reg:"[rabbithead_摸鱼]",img:"rh16",name:"摸鱼"}].map((function(t){return(0,r.default)((0,r.default)({},t),{},{img:o(t.img)})})));e.RabbitHeadEmojiList=s;var c=Object.freeze([{reg:"[rabbit_喝酒去]",img:"rb1",name:"喝酒去"},{reg:"[rabbit_还不错]",img:"rb2",name:"还不错"},{reg:"[rabbit_倒酒]",img:"rb3",name:"倒酒"},{reg:"[rabbit_没酒喝]",img:"rb4",name:"没酒喝"},{reg:"[rabbit_走一个]",img:"rb5",name:"走一个"},{reg:"[rabbit_嘿嘿]",img:"rb6",name:"嘿嘿"},{reg:"[rabbit_红酒杯]",img:"rb7",name:"红酒杯"},{reg:"[rabbit_自罚一杯]",img:"rb8",name:"自罚一杯"},{reg:"[rabbit_摇耳朵]",img:"rb9",name:"摇耳朵"},{reg:"[rabbit_捂脸]",img:"rb10",name:"捂脸"},{reg:"[rabbit_坏笑]",img:"rb11",name:"坏笑"},{reg:"[rabbit_蹦迪]",img:"rb12",name:"蹦迪"},{reg:"[rabbit_安排]",img:"rb13",name:"安排"},{reg:"[rabbit_赞]",img:"rb14",name:"赞"},{reg:"[rabbit_酒归你]",img:"rb15",name:"酒归你"},{reg:"[rabbit_来啊]",img:"rb16",name:"来啊"},{reg:"[rabbit_笔芯]",img:"rb17",name:"笔芯"},{reg:"[rabbit_买买买]",img:"rb18",name:"买买买"},{reg:"[rabbit_出来玩]",img:"rb19",name:"出来玩"},{reg:"[rabbit_打脑壳]",img:"rb20",name:"打脑壳"},{reg:"[rabbit_嗨]",img:"rb21",name:"嗨"},{reg:"[rabbit_拜]",img:"rb22",name:"拜"},{reg:"[rabbit_白眼]",img:"rb23",name:"白眼"},{reg:"[rabbit_沉迷工作]",img:"rb24",name:"沉迷工作"},{reg:"[rabbit_没看见]",img:"rb25",name:"没看见"},{reg:"[rabbit_不约]",img:"rb26",name:"不约"},{reg:"[rabbit_努力加油]",img:"rb27",name:"努力加油"},{reg:"[rabbit_吃瓜]",img:"rb28",name:"吃瓜"},{reg:"[rabbit_没错]",img:"rb29",name:"没错"},{reg:"[rabbit_暗中观察]",img:"rb30",name:"暗中观察"},{reg:"[rabbit_你继续]",img:"rb31",name:"你继续"},{reg:"[rabbit_太难了]",img:"rb32",name:"太难了"}].map((function(t){return(0,r.default)((0,r.default)({},t),{},{img:o(t.img)})})));e.RabbitEmojiList=c;var f=Object.freeze(["😀","😁","😂","🤣","😃","😄","😅","😆","😉","😊","😋","😎","😍","😘","😗","😙","😚","☺️","🙂","🤗","🤩","🤔","🤨","😐","😑","😶","🙄","😏","😣","😥","😮","🤐","😯","😪","😫","😴","😌","😛","😜","😝","🤤","😒","😓","😔","😕","🙃","🤑","😲","☹️","🙁","😖","😞","😟","😤","😢","😭","😦","😧","😨","😩","🤯","😬","😰","😱","😳","🤪","😵","😡","😠","🤬","😷","🤒","🤕","🤢","🤮","🤧","😇","🤠","🤡","🤥","🤫","🤭","🧐","🤓","😈","👿","👹","👺","💀","👻","👽","🤖","💩","😺","😸","😹","😻","😼","😽","🙀","😿","😾"]);e.CommonEmojiList=f},"72aa":function(t,e,i){var n=i("3fb5"),r=i("a255"),a=i("b672"),o=i("8707").Buffer,s=new Array(64);function c(){this.init(),this._w=s,a.call(this,64,56)}n(c,r),c.prototype.init=function(){return this._a=3238371032,this._b=914150663,this._c=812702999,this._d=4144912697,this._e=4290775857,this._f=1750603025,this._g=1694076839,this._h=3204075428,this},c.prototype._hash=function(){var t=o.allocUnsafe(28);return t.writeInt32BE(this._a,0),t.writeInt32BE(this._b,4),t.writeInt32BE(this._c,8),t.writeInt32BE(this._d,12),t.writeInt32BE(this._e,16),t.writeInt32BE(this._f,20),t.writeInt32BE(this._g,24),t},t.exports=c},"74c5":function(t,e,i){"use strict";i.r(e);var n=i("45c4"),r=i("2af0");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"550f2871",null,!1,n["a"],void 0);e["default"]=s.exports},"75cc":function(t,e,i){"use strict";(function(t,n){function r(){throw new Error("secure random number generation not supported by this browser\nuse chrome, FireFox or Internet Explorer 11")}var a=i("8707"),o=i("11dc"),s=a.Buffer,c=a.kMaxLength,f=t.crypto||t.msCrypto,u=Math.pow(2,32)-1;function d(t,e){if("number"!==typeof t||t!==t)throw new TypeError("offset must be a number");if(t>u||t<0)throw new TypeError("offset must be a uint32");if(t>c||t>e)throw new RangeError("offset out of range")}function h(t,e,i){if("number"!==typeof t||t!==t)throw new TypeError("size must be a number");if(t>u||t<0)throw new TypeError("size must be a uint32");if(t+e>i||t>c)throw new RangeError("buffer too small")}function l(t,e,i,r){if(n.browser){var a=t.buffer,s=new Uint8Array(a,e,i);return f.getRandomValues(s),r?void n.nextTick((function(){r(null,t)})):t}if(!r){var c=o(i);return c.copy(t,e),t}o(i,(function(i,n){if(i)return r(i);n.copy(t,e),r(null,t)}))}f&&f.getRandomValues||!n.browser?(e.randomFill=function(e,i,n,r){if(!s.isBuffer(e)&&!(e instanceof t.Uint8Array))throw new TypeError('"buf" argument must be a Buffer or Uint8Array');if("function"===typeof i)r=i,i=0,n=e.length;else if("function"===typeof n)r=n,n=e.length-i;else if("function"!==typeof r)throw new TypeError('"cb" argument must be a function');return d(i,e.length),h(n,i,e.length),l(e,i,n,r)},e.randomFillSync=function(e,i,n){"undefined"===typeof i&&(i=0);if(!s.isBuffer(e)&&!(e instanceof t.Uint8Array))throw new TypeError('"buf" argument must be a Buffer or Uint8Array');d(i,e.length),void 0===n&&(n=e.length-i);return h(n,i,e.length),l(e,i,n)}):(e.randomFill=r,e.randomFillSync=r)}).call(this,i("c8ba"),i("4362"))},7622:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uPopup:i("c4b0").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-popup",{attrs:{value:t.value,mode:"center","mask-close-able":!1,width:"552",height:"414","border-radius":"20"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"p-rela w-552 h-414"},[i("v-uni-image",{staticClass:"p-abso w-552 h-414",attrs:{src:t.ossIcon("/auction/popup_bg_552_414.png")}}),i("v-uni-view",{staticClass:"p-rela pt-84"},[i("v-uni-view",{staticClass:"ptb-00-plr-52 font-wei-500 font-32 text-6 l-h-44 text-center"},[t._v("鉴于拍卖的特殊性，非质量问题，"),i("v-uni-text",{staticClass:"text-3"},[t._v("不支持七天无理由退货")])],1),i("v-uni-view",{staticClass:"flex-c-c mt-94"},[i("v-uni-button",{staticClass:"vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput(!1)}}},[t._v("不同意")]),i("v-uni-button",{staticClass:"vh-btn flex-c-c ml-60 w-180 h-64 font-wei-500 font-28 text-ffffff l-h-40 b-rad-32",class:t.time?"bg-d8d8d8":"bg-e80404",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onAgree.apply(void 0,arguments)}}},[t._v("同意"),i("v-uni-text",{staticClass:"font-24 text-e80404 l-h-34"},[t._v(t._s(t.time?t.time+"s":""))])],1)],1)],1)],1)],1)},a=[]},7658:function(t,e,i){"use strict";var n=e;function r(t){return 1===t.length?"0"+t:t}function a(t){for(var e="",i=0;i<t.length;i++)e+=r(t[i].toString(16));return e}n.toArray=function(t,e){if(Array.isArray(t))return t.slice();if(!t)return[];var i=[];if("string"!==typeof t){for(var n=0;n<t.length;n++)i[n]=0|t[n];return i}if("hex"===e){t=t.replace(/[^a-z0-9]+/gi,""),t.length%2!==0&&(t="0"+t);for(n=0;n<t.length;n+=2)i.push(parseInt(t[n]+t[n+1],16))}else for(n=0;n<t.length;n++){var r=t.charCodeAt(n),a=r>>8,o=255&r;a?i.push(a,o):i.push(o)}return i},n.zero2=r,n.toHex=a,n.encode=function(t,e){return"hex"===e?a(t):t}},"76a6":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{value:{type:Boolean,default:!0}},methods:{onInput:function(t){this.$emit("input",t)}}};e.default=n},"780f":function(t,e,i){"use strict";t.exports=a;var n=i("27bf"),r=Object.create(i("3a7c"));function a(t){if(!(this instanceof a))return new a(t);n.call(this,t)}r.inherits=i("3fb5"),r.inherits(a,n),a.prototype._transform=function(t,e,i){i(null,t)}},"78a8":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.auction-gdetail__swiper[data-v-59f2b89f]  .vh-indicator-item-round{margin:0 %?6?%;width:%?8?%;height:%?8?%;background-color:#e0e0e0}.auction-gdetail__swiper[data-v-59f2b89f]  .vh-indicator-item-round-active{width:%?22?%;background-color:#e80404;border-radius:%?5?%}',""]),t.exports=e},"7a10":function(t,e,i){var n=i("399f"),r=i("fdac");function a(t){this.rand=t||new r.Rand}t.exports=a,a.create=function(t){return new a(t)},a.prototype._randbelow=function(t){var e=t.bitLength(),i=Math.ceil(e/8);do{var r=new n(this.rand.generate(i))}while(r.cmp(t)>=0);return r},a.prototype._randrange=function(t,e){var i=e.sub(t);return t.add(this._randbelow(i))},a.prototype.test=function(t,e,i){var r=t.bitLength(),a=n.mont(t),o=new n(1).toRed(a);e||(e=Math.max(1,r/48|0));for(var s=t.subn(1),c=0;!s.testn(c);c++);for(var f=t.shrn(c),u=s.toRed(a);e>0;e--){var d=this._randrange(new n(2),s);i&&i(d);var h=d.toRed(a).redPow(f);if(0!==h.cmp(o)&&0!==h.cmp(u)){for(var l=1;l<c;l++){if(h=h.redSqr(),0===h.cmp(o))return!1;if(0===h.cmp(u))break}if(l===c)return!1}}return!0},a.prototype.getDivisor=function(t,e){var i=t.bitLength(),r=n.mont(t),a=new n(1).toRed(r);e||(e=Math.max(1,i/48|0));for(var o=t.subn(1),s=0;!o.testn(s);s++);for(var c=t.shrn(s),f=o.toRed(r);e>0;e--){var u=this._randrange(new n(2),o),d=t.gcd(u);if(0!==d.cmpn(1))return d;var h=u.toRed(r).redPow(c);if(0!==h.cmp(a)&&0!==h.cmp(f)){for(var l=1;l<s;l++){if(h=h.redSqr(),0===h.cmp(a))return h.fromRed().subn(1).gcd(t);if(0===h.cmp(f))break}if(l===s)return h=h.redSqr(),h.fromRed().subn(1).gcd(t)}}return!1}},"7bc2":function(t,e,i){"use strict";i.r(e);var n=i("a4fa"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"7c29":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uParse:i("2c89").default},r=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[e("v-uni-view",{staticClass:"font-wei-500 font-32 text-3"},[this._v("拍品详情")]),e("v-uni-view",{staticClass:"mt-20 w-b-b-w font-28 o-hid",staticStyle:{"line-height":"1.4"}},[e("u-parse",{staticStyle:{"user-select":"auto"},attrs:{html:this.goods.detail,"show-with-animation":!0}})],1)],1)},a=[]},"7d2a":function(t,e){var i=Math.pow(2,30)-1;t.exports=function(t,e){if("number"!==typeof t)throw new TypeError("Iterations not a number");if(t<0)throw new TypeError("Bad iterations");if("number"!==typeof e)throw new TypeError("Key length not a number");if(e<0||e>i||e!==e)throw new TypeError("Bad key length")}},"7d72":function(t,e,i){"use strict";var n=i("8707").Buffer,r=n.isEncoding||function(t){switch(t=""+t,t&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function a(t){var e;switch(this.encoding=function(t){var e=function(t){if(!t)return"utf8";var e;while(1)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}(t);if("string"!==typeof e&&(n.isEncoding===r||!r(t)))throw new Error("Unknown encoding: "+t);return e||t}(t),this.encoding){case"utf16le":this.text=c,this.end=f,e=4;break;case"utf8":this.fillLast=s,e=4;break;case"base64":this.text=u,this.end=d,e=3;break;default:return this.write=h,void(this.end=l)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(e)}function o(t){return t<=127?0:t>>5===6?2:t>>4===14?3:t>>3===30?4:t>>6===2?-1:-2}function s(t){var e=this.lastTotal-this.lastNeed,i=function(t,e,i){if(128!==(192&e[0]))return t.lastNeed=0,"�";if(t.lastNeed>1&&e.length>1){if(128!==(192&e[1]))return t.lastNeed=1,"�";if(t.lastNeed>2&&e.length>2&&128!==(192&e[2]))return t.lastNeed=2,"�"}}(this,t);return void 0!==i?i:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function c(t,e){if((t.length-e)%2===0){var i=t.toString("utf16le",e);if(i){var n=i.charCodeAt(i.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],i.slice(0,-1)}return i}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function f(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var i=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,i)}return e}function u(t,e){var i=(t.length-e)%3;return 0===i?t.toString("base64",e):(this.lastNeed=3-i,this.lastTotal=3,1===i?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-i))}function d(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function h(t){return t.toString(this.encoding)}function l(t){return t&&t.length?this.write(t):""}e.StringDecoder=a,a.prototype.write=function(t){if(0===t.length)return"";var e,i;if(this.lastNeed){if(e=this.fillLast(t),void 0===e)return"";i=this.lastNeed,this.lastNeed=0}else i=0;return i<t.length?e?e+this.text(t,i):this.text(t,i):e||""},a.prototype.end=function(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�":e},a.prototype.text=function(t,e){var i=function(t,e,i){var n=e.length-1;if(n<i)return 0;var r=o(e[n]);if(r>=0)return r>0&&(t.lastNeed=r-1),r;if(--n<i||-2===r)return 0;if(r=o(e[n]),r>=0)return r>0&&(t.lastNeed=r-2),r;if(--n<i||-2===r)return 0;if(r=o(e[n]),r>=0)return r>0&&(2===r?r=0:t.lastNeed=r-3),r;return 0}(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=i;var n=t.length-(i-this.lastNeed);return t.copy(this.lastChar,0,n),t.toString("utf8",e,n)},a.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length}},"7d92":function(t,e,i){var n=e;n.utils=i("c3c0"),n.common=i("edc9"),n.sha=i("5919"),n.ripemd=i("bb44"),n.hmac=i("2137"),n.sha1=n.sha.sha1,n.sha256=n.sha.sha256,n.sha224=n.sha.sha224,n.sha384=n.sha.sha384,n.sha512=n.sha.sha512,n.ripemd160=n.ripemd.ripemd160},"7e6c":function(t,e,i){"use strict";i.r(e);var n=i("ad66"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"7e78":function(t,e,i){var n=i("3fb5"),r=i("b672"),a=i("8707").Buffer,o=[1518500249,1859775393,-1894007588,-899497514],s=new Array(80);function c(){this.init(),this._w=s,r.call(this,64,56)}function f(t){return t<<1|t>>>31}function u(t){return t<<5|t>>>27}function d(t){return t<<30|t>>>2}function h(t,e,i,n){return 0===t?e&i|~e&n:2===t?e&i|e&n|i&n:e^i^n}n(c,r),c.prototype.init=function(){return this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520,this},c.prototype._update=function(t){for(var e=this._w,i=0|this._a,n=0|this._b,r=0|this._c,a=0|this._d,s=0|this._e,c=0;c<16;++c)e[c]=t.readInt32BE(4*c);for(;c<80;++c)e[c]=f(e[c-3]^e[c-8]^e[c-14]^e[c-16]);for(var l=0;l<80;++l){var p=~~(l/20),b=u(i)+h(p,n,r,a)+s+e[l]+o[p]|0;s=a,a=r,r=d(n),n=i,i=b}this._a=i+this._a|0,this._b=n+this._b|0,this._c=r+this._c|0,this._d=a+this._d|0,this._e=s+this._e|0},c.prototype._hash=function(){var t=a.allocUnsafe(20);return t.writeInt32BE(0|this._a,0),t.writeInt32BE(0|this._b,4),t.writeInt32BE(0|this._c,8),t.writeInt32BE(0|this._d,12),t.writeInt32BE(0|this._e,16),t},t.exports=c},"7e99":function(t,e,i){"use strict";var n,r,a=i("62f5").default;i("d401"),i("d3b7"),i("25f0"),i("fb6a"),i("99af"),i("14d9"),i("ac1f"),i("5319"),i("e25e"),i("c975"),i("ace4"),i("fb2c"),i("907a"),i("9a8c"),i("a975"),i("735e"),i("c1ac"),i("d139"),i("3a7b"),i("986a"),i("1d02"),i("d5d6"),i("82f8"),i("e91f"),i("60bd"),i("5f96"),i("3280"),i("3fcc"),i("ca91"),i("25a1"),i("cd26"),i("3c5d"),i("2954"),i("649e"),i("219c"),i("b39a"),i("72f7"),i("a434"),i("5cc6"),i("c19f"),i("466d"),i("1276");var o={cipher:{},hash:{},keyexchange:{},mode:{},misc:{},codec:{},exception:{corrupt:function(t){this.toString=function(){return"CORRUPT: "+this.message},this.message=t},invalid:function(t){this.toString=function(){return"INVALID: "+this.message},this.message=t},bug:function(t){this.toString=function(){return"BUG: "+this.message},this.message=t},notReady:function(t){this.toString=function(){return"NOT READY: "+this.message},this.message=t}}};function s(t,e,i){if(4!==e.length)throw new o.exception.invalid("invalid aes block size");var n=t.b[i],r=e[0]^n[0],a=e[i?3:1]^n[1],s=e[2]^n[2];e=e[i?1:3]^n[3];var c,f,u,d,h=n.length/4-2,l=4,p=[0,0,0,0];c=t.s[i],t=c[0];var b=c[1],v=c[2],m=c[3],g=c[4];for(d=0;d<h;d++)c=t[r>>>24]^b[a>>16&255]^v[s>>8&255]^m[255&e]^n[l],f=t[a>>>24]^b[s>>16&255]^v[e>>8&255]^m[255&r]^n[l+1],u=t[s>>>24]^b[e>>16&255]^v[r>>8&255]^m[255&a]^n[l+2],e=t[e>>>24]^b[r>>16&255]^v[a>>8&255]^m[255&s]^n[l+3],l+=4,r=c,a=f,s=u;for(d=0;4>d;d++)p[i?3&-d:d]=g[r>>>24]<<24^g[a>>16&255]<<16^g[s>>8&255]<<8^g[255&e]^n[l++],c=r,r=a,a=s,s=e,e=c;return p}function c(t,e){var i,n,r,a=t.F,o=t.b,s=a[0],c=a[1],f=a[2],u=a[3],d=a[4],h=a[5],l=a[6],p=a[7];for(i=0;64>i;i++)16>i?n=e[i]:(n=e[i+1&15],r=e[i+14&15],n=e[15&i]=(n>>>7^n>>>18^n>>>3^n<<25^n<<14)+(r>>>17^r>>>19^r>>>10^r<<15^r<<13)+e[15&i]+e[i+9&15]|0),n=n+p+(d>>>6^d>>>11^d>>>25^d<<26^d<<21^d<<7)+(l^d&(h^l))+o[i],p=l,l=h,h=d,d=u+n|0,u=f,f=c,c=s,s=n+(c&f^u&(c^f))+(c>>>2^c>>>13^c>>>22^c<<30^c<<19^c<<10)|0;a[0]=a[0]+s|0,a[1]=a[1]+c|0,a[2]=a[2]+f|0,a[3]=a[3]+u|0,a[4]=a[4]+d|0,a[5]=a[5]+h|0,a[6]=a[6]+l|0,a[7]=a[7]+p|0}function f(t,e){var i,n=o.random.K[t],r=[];for(i in n)n.hasOwnProperty(i)&&r.push(n[i]);for(i=0;i<r.length;i++)r[i](e)}function u(t,e){"undefined"!==typeof window&&window.performance&&"function"===typeof window.performance.now?t.addEntropy(window.performance.now(),e,"loadtime"):t.addEntropy((new Date).valueOf(),e,"loadtime")}function d(t){t.b=h(t).concat(h(t)),t.L=new o.cipher.aes(t.b)}function h(t){for(var e=0;4>e&&(t.h[e]=t.h[e]+1|0,!t.h[e]);e++);return t.L.encrypt(t.h)}function l(t,e){return function(){e.apply(t,arguments)}}o.cipher.aes=function(t){this.s[0][0][0]||this.O();var e,i,n,r,a=this.s[0][4],s=this.s[1];e=t.length;var c=1;if(4!==e&&6!==e&&8!==e)throw new o.exception.invalid("invalid aes key size");for(this.b=[n=t.slice(0),r=[]],t=e;t<4*e+28;t++)i=n[t-1],(0===t%e||8===e&&4===t%e)&&(i=a[i>>>24]<<24^a[i>>16&255]<<16^a[i>>8&255]<<8^a[255&i],0===t%e&&(i=i<<8^i>>>24^c<<24,c=c<<1^283*(c>>7))),n[t]=n[t-e]^i;for(e=0;t;e++,t--)i=n[3&e?t:t-4],r[e]=4>=t||4>e?i:s[0][a[i>>>24]]^s[1][a[i>>16&255]]^s[2][a[i>>8&255]]^s[3][a[255&i]]},o.cipher.aes.prototype={encrypt:function(t){return s(this,t,0)},decrypt:function(t){return s(this,t,1)},s:[[[],[],[],[],[]],[[],[],[],[],[]]],O:function(){var t,e,i,n,r,a,o,s=this.s[0],c=this.s[1],f=s[4],u=c[4],d=[],h=[];for(t=0;256>t;t++)h[(d[t]=t<<1^283*(t>>7))^t]=t;for(e=i=0;!f[e];e^=n||1,i=h[i]||1)for(a=i^i<<1^i<<2^i<<3^i<<4,a=a>>8^255&a^99,f[e]=a,u[a]=e,r=d[t=d[n=d[e]]],o=16843009*r^65537*t^257*n^16843008*e,r=257*d[a]^16843008*a,t=0;4>t;t++)s[t][e]=r=r<<24^r>>>8,c[t][a]=o=o<<24^o>>>8;for(t=0;5>t;t++)s[t]=s[t].slice(0),c[t]=c[t].slice(0)}},o.bitArray={bitSlice:function(t,e,i){return t=o.bitArray.$(t.slice(e/32),32-(31&e)).slice(1),void 0===i?t:o.bitArray.clamp(t,i-e)},extract:function(t,e,i){var n=Math.floor(-e-i&31);return(-32&(e+i-1^e)?t[e/32|0]<<32-n^t[e/32+1|0]>>>n:t[e/32|0]>>>n)&(1<<i)-1},concat:function(t,e){if(0===t.length||0===e.length)return t.concat(e);var i=t[t.length-1],n=o.bitArray.getPartial(i);return 32===n?t.concat(e):o.bitArray.$(e,n,0|i,t.slice(0,t.length-1))},bitLength:function(t){var e=t.length;return 0===e?0:32*(e-1)+o.bitArray.getPartial(t[e-1])},clamp:function(t,e){if(32*t.length<e)return t;t=t.slice(0,Math.ceil(e/32));var i=t.length;return e&=31,0<i&&e&&(t[i-1]=o.bitArray.partial(e,t[i-1]&2147483648>>e-1,1)),t},partial:function(t,e,i){return 32===t?e:(i?0|e:e<<32-t)+1099511627776*t},getPartial:function(t){return Math.round(t/1099511627776)||32},equal:function(t,e){if(o.bitArray.bitLength(t)!==o.bitArray.bitLength(e))return!1;var i,n=0;for(i=0;i<t.length;i++)n|=t[i]^e[i];return 0===n},$:function(t,e,i,n){var r;for(r=0,void 0===n&&(n=[]);32<=e;e-=32)n.push(i),i=0;if(0===e)return n.concat(t);for(r=0;r<t.length;r++)n.push(i|t[r]>>>e),i=t[r]<<32-e;return r=t.length?t[t.length-1]:0,t=o.bitArray.getPartial(r),n.push(o.bitArray.partial(e+t&31,32<e+t?i:n.pop(),1)),n},i:function(t,e){return[t[0]^e[0],t[1]^e[1],t[2]^e[2],t[3]^e[3]]},byteswapM:function(t){var e,i;for(e=0;e<t.length;++e)i=t[e],t[e]=i>>>24|i>>>8&65280|(65280&i)<<8|i<<24;return t}},o.codec.utf8String={fromBits:function(t){var e,i,n="",r=o.bitArray.bitLength(t);for(e=0;e<r/8;e++)0===(3&e)&&(i=t[e/4]),n+=String.fromCharCode(i>>>8>>>8>>>8),i<<=8;return decodeURIComponent(escape(n))},toBits:function(t){t=unescape(encodeURIComponent(t));var e,i=[],n=0;for(e=0;e<t.length;e++)n=n<<8|t.charCodeAt(e),3===(3&e)&&(i.push(n),n=0);return 3&e&&i.push(o.bitArray.partial(8*(3&e),n)),i}},o.codec.hex={fromBits:function(t){var e,i="";for(e=0;e<t.length;e++)i+=(0xf00000000000+(0|t[e])).toString(16).substr(4);return i.substr(0,o.bitArray.bitLength(t)/4)},toBits:function(t){var e,i,n=[];for(t=t.replace(/\s|0x/g,""),i=t.length,t+="00000000",e=0;e<t.length;e+=8)n.push(0^parseInt(t.substr(e,8),16));return o.bitArray.clamp(n,4*i)}},o.codec.base32={B:"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",X:"0123456789ABCDEFGHIJKLMNOPQRSTUV",BITS:32,BASE:5,REMAINING:27,fromBits:function(t,e,i){var n=o.codec.base32.BASE,r=o.codec.base32.REMAINING,a="",s=0,c=o.codec.base32.B,f=0,u=o.bitArray.bitLength(t);for(i&&(c=o.codec.base32.X),i=0;a.length*n<u;)a+=c.charAt((f^t[i]>>>s)>>>r),s<n?(f=t[i]<<n-s,s+=r,i++):(f<<=n,s-=n);for(;7&a.length&&!e;)a+="=";return a},toBits:function(t,e){t=t.replace(/\s|=/g,"").toUpperCase();var i,n,r=o.codec.base32.BITS,a=o.codec.base32.BASE,s=o.codec.base32.REMAINING,c=[],f=0,u=o.codec.base32.B,d=0,h="base32";for(e&&(u=o.codec.base32.X,h="base32hex"),i=0;i<t.length;i++){if(n=u.indexOf(t.charAt(i)),0>n){if(!e)try{return o.codec.base32hex.toBits(t)}catch(l){}throw new o.exception.invalid("this isn't "+h+"!")}f>s?(f-=s,c.push(d^n>>>f),d=n<<r-f):(f+=a,d^=n<<r-f)}return 56&f&&c.push(o.bitArray.partial(56&f,d,1)),c}},o.codec.base32hex={fromBits:function(t,e){return o.codec.base32.fromBits(t,e,1)},toBits:function(t){return o.codec.base32.toBits(t,1)}},o.codec.base64={B:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",fromBits:function(t,e,i){var n="",r=0,a=o.codec.base64.B,s=0,c=o.bitArray.bitLength(t);for(i&&(a=a.substr(0,62)+"-_"),i=0;6*n.length<c;)n+=a.charAt((s^t[i]>>>r)>>>26),6>r?(s=t[i]<<6-r,r+=26,i++):(s<<=6,r-=6);for(;3&n.length&&!e;)n+="=";return n},toBits:function(t,e){t=t.replace(/\s|=/g,"");var i,n,r=[],a=0,s=o.codec.base64.B,c=0;for(e&&(s=s.substr(0,62)+"-_"),i=0;i<t.length;i++){if(n=s.indexOf(t.charAt(i)),0>n)throw new o.exception.invalid("this isn't base64!");26<a?(a-=26,r.push(c^n>>>a),c=n<<32-a):(a+=6,c^=n<<32-a)}return 56&a&&r.push(o.bitArray.partial(56&a,c,1)),r}},o.codec.base64url={fromBits:function(t){return o.codec.base64.fromBits(t,1,1)},toBits:function(t){return o.codec.base64.toBits(t,1)}},o.hash.sha256=function(t){this.b[0]||this.O(),t?(this.F=t.F.slice(0),this.A=t.A.slice(0),this.l=t.l):this.reset()},o.hash.sha256.hash=function(t){return(new o.hash.sha256).update(t).finalize()},o.hash.sha256.prototype={blockSize:512,reset:function(){return this.F=this.Y.slice(0),this.A=[],this.l=0,this},update:function(t){"string"===typeof t&&(t=o.codec.utf8String.toBits(t));var e,i=this.A=o.bitArray.concat(this.A,t);if(e=this.l,t=this.l=e+o.bitArray.bitLength(t),9007199254740991<t)throw new o.exception.invalid("Cannot hash more than 2^53 - 1 bits");if("undefined"!==typeof Uint32Array){var n=new Uint32Array(i),r=0;for(e=512+e-(512+e&511);e<=t;e+=512)c(this,n.subarray(16*r,16*(r+1))),r+=1;i.splice(0,16*r)}else for(e=512+e-(512+e&511);e<=t;e+=512)c(this,i.splice(0,16));return this},finalize:function(){var t,e=this.A,i=this.F;e=o.bitArray.concat(e,[o.bitArray.partial(1,1)]);for(t=e.length+2;15&t;t++)e.push(0);for(e.push(Math.floor(this.l/4294967296)),e.push(0|this.l);e.length;)c(this,e.splice(0,16));return this.reset(),i},Y:[],b:[],O:function(){function t(t){return 4294967296*(t-Math.floor(t))|0}for(var e,i,n=0,r=2;64>n;r++){for(i=!0,e=2;e*e<=r;e++)if(0===r%e){i=!1;break}i&&(8>n&&(this.Y[n]=t(Math.pow(r,.5))),this.b[n]=t(Math.pow(r,1/3)),n++)}}},o.mode.ccm={name:"ccm",G:[],listenProgress:function(t){o.mode.ccm.G.push(t)},unListenProgress:function(t){t=o.mode.ccm.G.indexOf(t),-1<t&&o.mode.ccm.G.splice(t,1)},fa:function(t){var e,i=o.mode.ccm.G.slice();for(e=0;e<i.length;e+=1)i[e](t)},encrypt:function(t,e,i,n,r){var a,s=e.slice(0),c=o.bitArray,f=c.bitLength(i)/8,u=c.bitLength(s)/8;if(r=r||64,n=n||[],7>f)throw new o.exception.invalid("ccm: iv must be at least 7 bytes");for(a=2;4>a&&u>>>8*a;a++);return a<15-f&&(a=15-f),i=c.clamp(i,8*(15-a)),e=o.mode.ccm.V(t,e,i,n,r,a),s=o.mode.ccm.C(t,s,i,e,r,a),c.concat(s.data,s.tag)},decrypt:function(t,e,i,n,r){r=r||64,n=n||[];var a=o.bitArray,s=a.bitLength(i)/8,c=a.bitLength(e),f=a.clamp(e,c-r),u=a.bitSlice(e,c-r);c=(c-r)/8;if(7>s)throw new o.exception.invalid("ccm: iv must be at least 7 bytes");for(e=2;4>e&&c>>>8*e;e++);if(e<15-s&&(e=15-s),i=a.clamp(i,8*(15-e)),f=o.mode.ccm.C(t,f,i,u,r,e),t=o.mode.ccm.V(t,f.data,i,n,r,e),!a.equal(f.tag,t))throw new o.exception.corrupt("ccm: tag doesn't match");return f.data},na:function(t,e,i,n,r,a){var s=[],c=o.bitArray,f=c.i;if(n=[c.partial(8,(e.length?64:0)|n-2<<2|a-1)],n=c.concat(n,i),n[3]|=r,n=t.encrypt(n),e.length)for(i=c.bitLength(e)/8,65279>=i?s=[c.partial(16,i)]:4294967295>=i&&(s=c.concat([c.partial(16,65534)],[i])),s=c.concat(s,e),e=0;e<s.length;e+=4)n=t.encrypt(f(n,s.slice(e,e+4).concat([0,0,0])));return n},V:function(t,e,i,n,r,a){var s=o.bitArray,c=s.i;if(r/=8,r%2||4>r||16<r)throw new o.exception.invalid("ccm: invalid tag length");if(4294967295<n.length||4294967295<e.length)throw new o.exception.bug("ccm: can't deal with 4GiB or more data");for(i=o.mode.ccm.na(t,n,i,r,s.bitLength(e)/8,a),n=0;n<e.length;n+=4)i=t.encrypt(c(i,e.slice(n,n+4).concat([0,0,0])));return s.clamp(i,8*r)},C:function(t,e,i,n,r,a){var s,c=o.bitArray;s=c.i;var f=e.length,u=c.bitLength(e),d=f/50,h=d;if(i=c.concat([c.partial(8,a-1)],i).concat([0,0,0]).slice(0,4),n=c.bitSlice(s(n,t.encrypt(i)),0,r),!f)return{tag:n,data:[]};for(s=0;s<f;s+=4)s>d&&(o.mode.ccm.fa(s/f),d+=h),i[3]++,r=t.encrypt(i),e[s]^=r[0],e[s+1]^=r[1],e[s+2]^=r[2],e[s+3]^=r[3];return{tag:n,data:c.clamp(e,u)}}},o.mode.ocb2={name:"ocb2",encrypt:function(t,e,i,n,r,a){if(128!==o.bitArray.bitLength(i))throw new o.exception.invalid("ocb iv must be 128 bits");var s,c=o.mode.ocb2.S,f=o.bitArray,u=f.i,d=[0,0,0,0];i=c(t.encrypt(i));var h,l=[];for(n=n||[],r=r||64,s=0;s+4<e.length;s+=4)h=e.slice(s,s+4),d=u(d,h),l=l.concat(u(i,t.encrypt(u(i,h)))),i=c(i);return h=e.slice(s),e=f.bitLength(h),s=t.encrypt(u(i,[0,0,0,e])),h=f.clamp(u(h.concat([0,0,0]),s),e),d=u(d,u(h.concat([0,0,0]),s)),d=t.encrypt(u(d,u(i,c(i)))),n.length&&(d=u(d,a?n:o.mode.ocb2.pmac(t,n))),l.concat(f.concat(h,f.clamp(d,r)))},decrypt:function(t,e,i,n,r,a){if(128!==o.bitArray.bitLength(i))throw new o.exception.invalid("ocb iv must be 128 bits");r=r||64;var s,c,f=o.mode.ocb2.S,u=o.bitArray,d=u.i,h=[0,0,0,0],l=f(t.encrypt(i)),p=o.bitArray.bitLength(e)-r,b=[];for(n=n||[],i=0;i+4<p/32;i+=4)s=d(l,t.decrypt(d(l,e.slice(i,i+4)))),h=d(h,s),b=b.concat(s),l=f(l);if(c=p-32*i,s=t.encrypt(d(l,[0,0,0,c])),s=d(s,u.clamp(e.slice(i),c).concat([0,0,0])),h=d(h,s),h=t.encrypt(d(h,d(l,f(l)))),n.length&&(h=d(h,a?n:o.mode.ocb2.pmac(t,n))),!u.equal(u.clamp(h,r),u.bitSlice(e,p)))throw new o.exception.corrupt("ocb: tag doesn't match");return b.concat(u.clamp(s,c))},pmac:function(t,e){var i,n=o.mode.ocb2.S,r=o.bitArray,a=r.i,s=[0,0,0,0],c=t.encrypt([0,0,0,0]);c=a(c,n(n(c)));for(i=0;i+4<e.length;i+=4)c=n(c),s=a(s,t.encrypt(a(c,e.slice(i,i+4))));return i=e.slice(i),128>r.bitLength(i)&&(c=a(c,n(c)),i=r.concat(i,[-2147483648,0,0,0])),s=a(s,i),t.encrypt(a(n(a(c,n(c))),s))},S:function(t){return[t[0]<<1^t[1]>>>31,t[1]<<1^t[2]>>>31,t[2]<<1^t[3]>>>31,t[3]<<1^135*(t[0]>>>31)]}},o.mode.gcm={name:"gcm",encrypt:function(t,e,i,n,r){var a=e.slice(0);return e=o.bitArray,n=n||[],t=o.mode.gcm.C(!0,t,a,n,i,r||128),e.concat(t.data,t.tag)},decrypt:function(t,e,i,n,r){var a=e.slice(0),s=o.bitArray,c=s.bitLength(a);if(r=r||128,n=n||[],r<=c?(e=s.bitSlice(a,c-r),a=s.bitSlice(a,0,c-r)):(e=a,a=[]),t=o.mode.gcm.C(!1,t,a,n,i,r),!s.equal(t.tag,e))throw new o.exception.corrupt("gcm: tag doesn't match");return t.data},ka:function(t,e){var i,n,r,a,s,c=o.bitArray.i;for(r=[0,0,0,0],a=e.slice(0),i=0;128>i;i++){for((n=0!==(t[Math.floor(i/32)]&1<<31-i%32))&&(r=c(r,a)),s=0!==(1&a[3]),n=3;0<n;n--)a[n]=a[n]>>>1|(1&a[n-1])<<31;a[0]>>>=1,s&&(a[0]^=-520093696)}return r},j:function(t,e,i){var n,r=i.length;for(e=e.slice(0),n=0;n<r;n+=4)e[0]^=4294967295&i[n],e[1]^=4294967295&i[n+1],e[2]^=4294967295&i[n+2],e[3]^=4294967295&i[n+3],e=o.mode.gcm.ka(e,t);return e},C:function(t,e,i,n,r,a){var s,c,f,u,d,h,l,p,b=o.bitArray;for(h=i.length,l=b.bitLength(i),p=b.bitLength(n),c=b.bitLength(r),s=e.encrypt([0,0,0,0]),96===c?(r=r.slice(0),r=b.concat(r,[1])):(r=o.mode.gcm.j(s,[0,0,0,0],r),r=o.mode.gcm.j(s,r,[0,0,Math.floor(c/4294967296),4294967295&c])),c=o.mode.gcm.j(s,[0,0,0,0],n),d=r.slice(0),n=c.slice(0),t||(n=o.mode.gcm.j(s,c,i)),u=0;u<h;u+=4)d[3]++,f=e.encrypt(d),i[u]^=f[0],i[u+1]^=f[1],i[u+2]^=f[2],i[u+3]^=f[3];return i=b.clamp(i,l),t&&(n=o.mode.gcm.j(s,c,i)),t=[Math.floor(p/4294967296),4294967295&p,Math.floor(l/4294967296),4294967295&l],n=o.mode.gcm.j(s,n,t),f=e.encrypt(r),n[0]^=f[0],n[1]^=f[1],n[2]^=f[2],n[3]^=f[3],{tag:b.bitSlice(n,0,a),data:i}}},o.misc.hmac=function(t,e){this.W=e=e||o.hash.sha256;var i,n=[[],[]],r=e.prototype.blockSize/32;for(this.w=[new e,new e],t.length>r&&(t=e.hash(t)),i=0;i<r;i++)n[0][i]=909522486^t[i],n[1][i]=1549556828^t[i];this.w[0].update(n[0]),this.w[1].update(n[1]),this.R=new e(this.w[0])},o.misc.hmac.prototype.encrypt=o.misc.hmac.prototype.mac=function(t){if(this.aa)throw new o.exception.invalid("encrypt on already updated hmac called!");return this.update(t),this.digest(t)},o.misc.hmac.prototype.reset=function(){this.R=new this.W(this.w[0]),this.aa=!1},o.misc.hmac.prototype.update=function(t){this.aa=!0,this.R.update(t)},o.misc.hmac.prototype.digest=function(){var t=this.R.finalize();t=new this.W(this.w[1]).update(t).finalize();return this.reset(),t},o.misc.pbkdf2=function(t,e,i,n,r){if(i=i||1e4,0>n||0>i)throw new o.exception.invalid("invalid params to pbkdf2");"string"===typeof t&&(t=o.codec.utf8String.toBits(t)),"string"===typeof e&&(e=o.codec.utf8String.toBits(e)),r=r||o.misc.hmac,t=new r(t);var a,s,c,f,u=[],d=o.bitArray;for(f=1;32*u.length<(n||1);f++){for(r=a=t.encrypt(d.concat(e,[f])),s=1;s<i;s++)for(a=t.encrypt(a),c=0;c<a.length;c++)r[c]^=a[c];u=u.concat(r)}return n&&(u=d.clamp(u,n)),u},o.prng=function(t){this.c=[new o.hash.sha256],this.m=[0],this.P=0,this.H={},this.N=0,this.U={},this.Z=this.f=this.o=this.ha=0,this.b=[0,0,0,0,0,0,0,0],this.h=[0,0,0,0],this.L=void 0,this.M=t,this.D=!1,this.K={progress:{},seeded:{}},this.u=this.ga=0,this.I=1,this.J=2,this.ca=65536,this.T=[0,48,64,96,128,192,256,384,512,768,1024],this.da=3e4,this.ba=80},o.prng.prototype={randomWords:function(t,e){var i,n,r=[];if(i=this.isReady(e),i===this.u)throw new o.exception.notReady("generator isn't seeded");if(i&this.J){i=!(i&this.I),n=[];var a,s=0;for(this.Z=n[0]=(new Date).valueOf()+this.da,a=0;16>a;a++)n.push(4294967296*Math.random()|0);for(a=0;a<this.c.length&&(n=n.concat(this.c[a].finalize()),s+=this.m[a],this.m[a]=0,i||!(this.P&1<<a));a++);for(this.P>=1<<this.c.length&&(this.c.push(new o.hash.sha256),this.m.push(0)),this.f-=s,s>this.o&&(this.o=s),this.P++,this.b=o.hash.sha256.hash(this.b.concat(n)),this.L=new o.cipher.aes(this.b),i=0;4>i&&(this.h[i]=this.h[i]+1|0,!this.h[i]);i++);}for(i=0;i<t;i+=4)0===(i+1)%this.ca&&d(this),n=h(this),r.push(n[0],n[1],n[2],n[3]);return d(this),r.slice(0,t)},setDefaultParanoia:function(t,e){if(0===t&&"Setting paranoia=0 will ruin your security; use it only for testing"!==e)throw new o.exception.invalid("Setting paranoia=0 will ruin your security; use it only for testing");this.M=t},addEntropy:function(t,e,i){i=i||"user";var n,r,s=(new Date).valueOf(),c=this.H[i],u=this.isReady(),d=0;switch(n=this.U[i],void 0===n&&(n=this.U[i]=this.ha++),void 0===c&&(c=this.H[i]=0),this.H[i]=(this.H[i]+1)%this.c.length,a(t)){case"number":void 0===e&&(e=1),this.c[c].update([n,this.N++,1,e,s,1,0|t]);break;case"object":if(i=Object.prototype.toString.call(t),"[object Uint32Array]"===i){for(r=[],i=0;i<t.length;i++)r.push(t[i]);t=r}else for("[object Array]"!==i&&(d=1),i=0;i<t.length&&!d;i++)"number"!==typeof t[i]&&(d=1);if(!d){if(void 0===e)for(i=e=0;i<t.length;i++)for(r=t[i];0<r;)e++,r>>>=1;this.c[c].update([n,this.N++,2,e,s,t.length].concat(t))}break;case"string":void 0===e&&(e=t.length),this.c[c].update([n,this.N++,3,e,s,t.length]),this.c[c].update(t);break;default:d=1}if(d)throw new o.exception.bug("random: addEntropy only supports number, array of numbers or string");this.m[c]+=e,this.f+=e,u===this.u&&(this.isReady()!==this.u&&f("seeded",Math.max(this.o,this.f)),f("progress",this.getProgress()))},isReady:function(t){return t=this.T[void 0!==t?t:this.M],this.o&&this.o>=t?this.m[0]>this.ba&&(new Date).valueOf()>this.Z?this.J|this.I:this.I:this.f>=t?this.J|this.u:this.u},getProgress:function(t){return t=this.T[t||this.M],this.o>=t||this.f>t?1:this.f/t},startCollectors:function(){if(!this.D){if(this.a={loadTimeCollector:l(this,this.ma),mouseCollector:l(this,this.oa),keyboardCollector:l(this,this.la),accelerometerCollector:l(this,this.ea),touchCollector:l(this,this.qa)},window.addEventListener)window.addEventListener("load",this.a.loadTimeCollector,!1),window.addEventListener("mousemove",this.a.mouseCollector,!1),window.addEventListener("keypress",this.a.keyboardCollector,!1),window.addEventListener("devicemotion",this.a.accelerometerCollector,!1),window.addEventListener("touchmove",this.a.touchCollector,!1);else{if(!document.attachEvent)throw new o.exception.bug("can't attach event");document.attachEvent("onload",this.a.loadTimeCollector),document.attachEvent("onmousemove",this.a.mouseCollector),document.attachEvent("keypress",this.a.keyboardCollector)}this.D=!0}},stopCollectors:function(){this.D&&(window.removeEventListener?(window.removeEventListener("load",this.a.loadTimeCollector,!1),window.removeEventListener("mousemove",this.a.mouseCollector,!1),window.removeEventListener("keypress",this.a.keyboardCollector,!1),window.removeEventListener("devicemotion",this.a.accelerometerCollector,!1),window.removeEventListener("touchmove",this.a.touchCollector,!1)):document.detachEvent&&(document.detachEvent("onload",this.a.loadTimeCollector),document.detachEvent("onmousemove",this.a.mouseCollector),document.detachEvent("keypress",this.a.keyboardCollector)),this.D=!1)},addEventListener:function(t,e){this.K[t][this.ga++]=e},removeEventListener:function(t,e){var i,n,r=this.K[t],a=[];for(n in r)r.hasOwnProperty(n)&&r[n]===e&&a.push(n);for(i=0;i<a.length;i++)n=a[i],delete r[n]},la:function(){u(this,1)},oa:function(t){var e,i;try{e=t.x||t.clientX||t.offsetX||0,i=t.y||t.clientY||t.offsetY||0}catch(n){i=e=0}0!=e&&0!=i&&this.addEntropy([e,i],2,"mouse"),u(this,0)},qa:function(t){t=t.touches[0]||t.changedTouches[0],this.addEntropy([t.pageX||t.clientX,t.pageY||t.clientY],1,"touch"),u(this,0)},ma:function(){u(this,2)},ea:function(t){if(t=t.accelerationIncludingGravity.x||t.accelerationIncludingGravity.y||t.accelerationIncludingGravity.z,window.orientation){var e=window.orientation;"number"===typeof e&&this.addEntropy(e,1,"accelerometer")}t&&this.addEntropy(t,2,"accelerometer"),u(this,0)}},o.random=new o.prng(6);t:try{var p,b,v,m;if(m=t.exports){var g;try{g=i("1c46")}catch(y){g=null}m=b=g}if(m&&b.randomBytes)p=b.randomBytes(128),p=new Uint32Array(new Uint8Array(p).buffer),o.random.addEntropy(p,1024,"crypto['randomBytes']");else if("undefined"!==typeof window&&"undefined"!==typeof Uint32Array){if(v=new Uint32Array(32),window.crypto&&window.crypto.getRandomValues)window.crypto.getRandomValues(v);else{if(!window.msCrypto||!window.msCrypto.getRandomValues)break t;window.msCrypto.getRandomValues(v)}o.random.addEntropy(v,1024,"crypto['getRandomValues']")}}catch(y){"undefined"!==typeof window&&window.console&&(console.log("There was an error collecting entropy from the browser:"),console.log(y))}o.json={defaults:{v:1,iter:1e4,ks:128,ts:64,mode:"ccm",adata:"",cipher:"aes"},ja:function(t,e,i,n){i=i||{},n=n||{};var r,a=o.json,s=a.g({iv:o.random.randomWords(4,0)},a.defaults);if(a.g(s,i),i=s.adata,"string"===typeof s.salt&&(s.salt=o.codec.base64.toBits(s.salt)),"string"===typeof s.iv&&(s.iv=o.codec.base64.toBits(s.iv)),!o.mode[s.mode]||!o.cipher[s.cipher]||"string"===typeof t&&100>=s.iter||64!==s.ts&&96!==s.ts&&128!==s.ts||128!==s.ks&&192!==s.ks&&256!==s.ks||2>s.iv.length||4<s.iv.length)throw new o.exception.invalid("json encrypt: invalid parameters");return"string"===typeof t?(r=o.misc.cachedPbkdf2(t,s),t=r.key.slice(0,s.ks/32),s.salt=r.salt):o.ecc&&t instanceof o.ecc.elGamal.publicKey&&(r=t.kem(),s.kemtag=r.tag,t=r.key.slice(0,s.ks/32)),"string"===typeof e&&(e=o.codec.utf8String.toBits(e)),"string"===typeof i&&(s.adata=i=o.codec.utf8String.toBits(i)),r=new o.cipher[s.cipher](t),a.g(n,s),n.key=t,s.ct="ccm"===s.mode&&o.arrayBuffer&&o.arrayBuffer.ccm&&e instanceof ArrayBuffer?o.arrayBuffer.ccm.encrypt(r,e,s.iv,i,s.ts):o.mode[s.mode].encrypt(r,e,s.iv,i,s.ts),s},encrypt:function(t,e,i,n){var r=o.json,a=r.ja.apply(r,arguments);return r.encode(a)},ia:function(t,e,i,n){i=i||{},n=n||{};var r,a,s=o.json;if(e=s.g(s.g(s.g({},s.defaults),e),i,!0),r=e.adata,"string"===typeof e.salt&&(e.salt=o.codec.base64.toBits(e.salt)),"string"===typeof e.iv&&(e.iv=o.codec.base64.toBits(e.iv)),!o.mode[e.mode]||!o.cipher[e.cipher]||"string"===typeof t&&100>=e.iter||64!==e.ts&&96!==e.ts&&128!==e.ts||128!==e.ks&&192!==e.ks&&256!==e.ks||!e.iv||2>e.iv.length||4<e.iv.length)throw new o.exception.invalid("json decrypt: invalid parameters");return"string"===typeof t?(a=o.misc.cachedPbkdf2(t,e),t=a.key.slice(0,e.ks/32),e.salt=a.salt):o.ecc&&t instanceof o.ecc.elGamal.secretKey&&(t=t.unkem(o.codec.base64.toBits(e.kemtag)).slice(0,e.ks/32)),"string"===typeof r&&(r=o.codec.utf8String.toBits(r)),a=new o.cipher[e.cipher](t),r="ccm"===e.mode&&o.arrayBuffer&&o.arrayBuffer.ccm&&e.ct instanceof ArrayBuffer?o.arrayBuffer.ccm.decrypt(a,e.ct,e.iv,e.tag,r,e.ts):o.mode[e.mode].decrypt(a,e.ct,e.iv,r,e.ts),s.g(n,e),n.key=t,1===i.raw?r:o.codec.utf8String.fromBits(r)},decrypt:function(t,e,i,n){var r=o.json;return r.ia(t,r.decode(e),i,n)},encode:function(t){var e,i="{",n="";for(e in t)if(t.hasOwnProperty(e)){if(!e.match(/^[a-z0-9]+$/i))throw new o.exception.invalid("json encode: invalid property name");switch(i+=n+'"'+e+'":',n=",",a(t[e])){case"number":case"boolean":i+=t[e];break;case"string":i+='"'+escape(t[e])+'"';break;case"object":i+='"'+o.codec.base64.fromBits(t[e],0)+'"';break;default:throw new o.exception.bug("json encode: unsupported type")}}return i+"}"},decode:function(t){if(t=t.replace(/\s/g,""),!t.match(/^\{.*\}$/))throw new o.exception.invalid("json decode: this isn't json!");t=t.replace(/^\{|\}$/g,"").split(/,/);var e,i,n={};for(e=0;e<t.length;e++){if(!(i=t[e].match(/^\s*(?:(["']?)([a-z][a-z0-9]*)\1)\s*:\s*(?:(-?\d+)|"([a-z0-9+\/%*_.@=\-]*)"|(true|false))$/i)))throw new o.exception.invalid("json decode: this isn't json!");null!=i[3]?n[i[2]]=parseInt(i[3],10):null!=i[4]?n[i[2]]=i[2].match(/^(ct|adata|salt|iv)$/)?o.codec.base64.toBits(i[4]):unescape(i[4]):null!=i[5]&&(n[i[2]]="true"===i[5])}return n},g:function(t,e,i){if(void 0===t&&(t={}),void 0===e)return t;for(var n in e)if(e.hasOwnProperty(n)){if(i&&void 0!==t[n]&&t[n]!==e[n])throw new o.exception.invalid("required parameter overridden");t[n]=e[n]}return t},sa:function(t,e){var i,n={};for(i in t)t.hasOwnProperty(i)&&t[i]!==e[i]&&(n[i]=t[i]);return n},ra:function(t,e){var i,n={};for(i=0;i<e.length;i++)void 0!==t[e[i]]&&(n[e[i]]=t[e[i]]);return n}},o.encrypt=o.json.encrypt,o.decrypt=o.json.decrypt,o.misc.pa={},o.misc.cachedPbkdf2=function(t,e){var i,n=o.misc.pa;return e=e||{},i=e.iter||1e3,n=n[t]=n[t]||{},i=n[i]=n[i]||{firstSalt:e.salt&&e.salt.length?e.salt.slice(0):o.random.randomWords(2,0)},n=void 0===e.salt?i.firstSalt:e.salt,i[n]=i[n]||o.misc.pbkdf2(t,n,e.iter),{key:i[n].slice(0),salt:n.slice(0)}},t.exports&&(t.exports=o),n=[],r=function(){return o}.apply(e,n),void 0===r||(t.exports=r)},"7f7a":function(t,e,i){"use strict";const n=e;n.bignum=i("399f"),n.define=i("ef3a").define,n.base=i("41df"),n.constants=i("0211"),n.decoders=i("20f6"),n.encoders=i("343e")},"82f0":function(t,e,i){var n=i("39f5"),r=i("8707").Buffer,a=i("6430"),o=i("3fb5"),s=i("3f62"),c=i("8c8a"),f=i("bd9d");function u(t,e,i,o){a.call(this);var c=r.alloc(4,0);this._cipher=new n.AES(e);var u=this._cipher.encryptBlock(c);this._ghash=new s(u),i=function(t,e,i){if(12===e.length)return t._finID=r.concat([e,r.from([0,0,0,1])]),r.concat([e,r.from([0,0,0,2])]);var n=new s(i),a=e.length,o=a%16;n.update(e),o&&(o=16-o,n.update(r.alloc(o,0))),n.update(r.alloc(8,0));var c=8*a,u=r.alloc(8);u.writeUIntBE(c,0,8),n.update(u),t._finID=n.state;var d=r.from(t._finID);return f(d),d}(this,i,u),this._prev=r.from(i),this._cache=r.allocUnsafe(0),this._secCache=r.allocUnsafe(0),this._decrypt=o,this._alen=0,this._len=0,this._mode=t,this._authTag=null,this._called=!1}o(u,a),u.prototype._update=function(t){if(!this._called&&this._alen){var e=16-this._alen%16;e<16&&(e=r.alloc(e,0),this._ghash.update(e))}this._called=!0;var i=this._mode.encrypt(this,t);return this._decrypt?this._ghash.update(t):this._ghash.update(i),this._len+=t.length,i},u.prototype._final=function(){if(this._decrypt&&!this._authTag)throw new Error("Unsupported state or unable to authenticate data");var t=c(this._ghash.final(8*this._alen,8*this._len),this._cipher.encryptBlock(this._finID));if(this._decrypt&&function(t,e){var i=0;t.length!==e.length&&i++;for(var n=Math.min(t.length,e.length),r=0;r<n;++r)i+=t[r]^e[r];return i}(t,this._authTag))throw new Error("Unsupported state or unable to authenticate data");this._authTag=t,this._cipher.scrub()},u.prototype.getAuthTag=function(){if(this._decrypt||!r.isBuffer(this._authTag))throw new Error("Attempting to get auth tag in unsupported state");return this._authTag},u.prototype.setAuthTag=function(t){if(!this._decrypt)throw new Error("Attempting to set auth tag in unsupported state");this._authTag=t},u.prototype.setAAD=function(t){if(this._called)throw new Error("Attempting to set AAD in unsupported state");this._ghash.update(t),this._alen+=t.length},t.exports=u},8360:function(t,e,i){"use strict";const n=i("d1c8").Reporter,r=i("6283").EncoderBuffer,a=i("6283").DecoderBuffer,o=i("da3e"),s=["seq","seqof","set","setof","objid","bool","gentime","utctime","null_","enum","int","objDesc","bitstr","bmpstr","charstr","genstr","graphstr","ia5str","iso646str","numstr","octstr","printstr","t61str","unistr","utf8str","videostr"],c=["key","obj","use","optional","explicit","implicit","def","choice","any","contains"].concat(s);function f(t,e,i){const n={};this._baseState=n,n.name=i,n.enc=t,n.parent=e||null,n.children=null,n.tag=null,n.args=null,n.reverseArgs=null,n.choice=null,n.optional=!1,n.any=!1,n.obj=!1,n.use=null,n.useDecoder=null,n.key=null,n["default"]=null,n.explicit=null,n.implicit=null,n.contains=null,n.parent||(n.children=[],this._wrap())}t.exports=f;const u=["enc","parent","children","tag","args","reverseArgs","choice","optional","any","obj","use","alteredUse","key","default","explicit","implicit","contains"];f.prototype.clone=function(){const t=this._baseState,e={};u.forEach((function(i){e[i]=t[i]}));const i=new this.constructor(e.parent);return i._baseState=e,i},f.prototype._wrap=function(){const t=this._baseState;c.forEach((function(e){this[e]=function(){const i=new this.constructor(this);return t.children.push(i),i[e].apply(i,arguments)}}),this)},f.prototype._init=function(t){const e=this._baseState;o(null===e.parent),t.call(this),e.children=e.children.filter((function(t){return t._baseState.parent===this}),this),o.equal(e.children.length,1,"Root node can have only one child")},f.prototype._useArgs=function(t){const e=this._baseState,i=t.filter((function(t){return t instanceof this.constructor}),this);t=t.filter((function(t){return!(t instanceof this.constructor)}),this),0!==i.length&&(o(null===e.children),e.children=i,i.forEach((function(t){t._baseState.parent=this}),this)),0!==t.length&&(o(null===e.args),e.args=t,e.reverseArgs=t.map((function(t){if("object"!==typeof t||t.constructor!==Object)return t;const e={};return Object.keys(t).forEach((function(i){i==(0|i)&&(i|=0);const n=t[i];e[n]=i})),e})))},["_peekTag","_decodeTag","_use","_decodeStr","_decodeObjid","_decodeTime","_decodeNull","_decodeInt","_decodeBool","_decodeList","_encodeComposite","_encodeStr","_encodeObjid","_encodeTime","_encodeNull","_encodeInt","_encodeBool"].forEach((function(t){f.prototype[t]=function(){const e=this._baseState;throw new Error(t+" not implemented for encoding: "+e.enc)}})),s.forEach((function(t){f.prototype[t]=function(){const e=this._baseState,i=Array.prototype.slice.call(arguments);return o(null===e.tag),e.tag=t,this._useArgs(i),this}})),f.prototype.use=function(t){o(t);const e=this._baseState;return o(null===e.use),e.use=t,this},f.prototype.optional=function(){const t=this._baseState;return t.optional=!0,this},f.prototype.def=function(t){const e=this._baseState;return o(null===e["default"]),e["default"]=t,e.optional=!0,this},f.prototype.explicit=function(t){const e=this._baseState;return o(null===e.explicit&&null===e.implicit),e.explicit=t,this},f.prototype.implicit=function(t){const e=this._baseState;return o(null===e.explicit&&null===e.implicit),e.implicit=t,this},f.prototype.obj=function(){const t=this._baseState,e=Array.prototype.slice.call(arguments);return t.obj=!0,0!==e.length&&this._useArgs(e),this},f.prototype.key=function(t){const e=this._baseState;return o(null===e.key),e.key=t,this},f.prototype.any=function(){const t=this._baseState;return t.any=!0,this},f.prototype.choice=function(t){const e=this._baseState;return o(null===e.choice),e.choice=t,this._useArgs(Object.keys(t).map((function(e){return t[e]}))),this},f.prototype.contains=function(t){const e=this._baseState;return o(null===e.use),e.contains=t,this},f.prototype._decode=function(t,e){const i=this._baseState;if(null===i.parent)return t.wrapResult(i.children[0]._decode(t,e));let n,r=i["default"],o=!0,s=null;if(null!==i.key&&(s=t.enterKey(i.key)),i.optional){let n=null;if(null!==i.explicit?n=i.explicit:null!==i.implicit?n=i.implicit:null!==i.tag&&(n=i.tag),null!==n||i.any){if(o=this._peekTag(t,n,i.any),t.isError(o))return o}else{const n=t.save();try{null===i.choice?this._decodeGeneric(i.tag,t,e):this._decodeChoice(t,e),o=!0}catch(c){o=!1}t.restore(n)}}if(i.obj&&o&&(n=t.enterObject()),o){if(null!==i.explicit){const e=this._decodeTag(t,i.explicit);if(t.isError(e))return e;t=e}const n=t.offset;if(null===i.use&&null===i.choice){let e;i.any&&(e=t.save());const n=this._decodeTag(t,null!==i.implicit?i.implicit:i.tag,i.any);if(t.isError(n))return n;i.any?r=t.raw(e):t=n}if(e&&e.track&&null!==i.tag&&e.track(t.path(),n,t.length,"tagged"),e&&e.track&&null!==i.tag&&e.track(t.path(),t.offset,t.length,"content"),i.any||(r=null===i.choice?this._decodeGeneric(i.tag,t,e):this._decodeChoice(t,e)),t.isError(r))return r;if(i.any||null!==i.choice||null===i.children||i.children.forEach((function(i){i._decode(t,e)})),i.contains&&("octstr"===i.tag||"bitstr"===i.tag)){const n=new a(r);r=this._getUse(i.contains,t._reporterState.obj)._decode(n,e)}}return i.obj&&o&&(r=t.leaveObject(n)),null===i.key||null===r&&!0!==o?null!==s&&t.exitKey(s):t.leaveKey(s,i.key,r),r},f.prototype._decodeGeneric=function(t,e,i){const n=this._baseState;return"seq"===t||"set"===t?null:"seqof"===t||"setof"===t?this._decodeList(e,t,n.args[0],i):/str$/.test(t)?this._decodeStr(e,t,i):"objid"===t&&n.args?this._decodeObjid(e,n.args[0],n.args[1],i):"objid"===t?this._decodeObjid(e,null,null,i):"gentime"===t||"utctime"===t?this._decodeTime(e,t,i):"null_"===t?this._decodeNull(e,i):"bool"===t?this._decodeBool(e,i):"objDesc"===t?this._decodeStr(e,t,i):"int"===t||"enum"===t?this._decodeInt(e,n.args&&n.args[0],i):null!==n.use?this._getUse(n.use,e._reporterState.obj)._decode(e,i):e.error("unknown tag: "+t)},f.prototype._getUse=function(t,e){const i=this._baseState;return i.useDecoder=this._use(t,e),o(null===i.useDecoder._baseState.parent),i.useDecoder=i.useDecoder._baseState.children[0],i.implicit!==i.useDecoder._baseState.implicit&&(i.useDecoder=i.useDecoder.clone(),i.useDecoder._baseState.implicit=i.implicit),i.useDecoder},f.prototype._decodeChoice=function(t,e){const i=this._baseState;let n=null,r=!1;return Object.keys(i.choice).some((function(a){const o=t.save(),s=i.choice[a];try{const i=s._decode(t,e);if(t.isError(i))return!1;n={type:a,value:i},r=!0}catch(c){return t.restore(o),!1}return!0}),this),r?n:t.error("Choice not matched")},f.prototype._createEncoderBuffer=function(t){return new r(t,this.reporter)},f.prototype._encode=function(t,e,i){const n=this._baseState;if(null!==n["default"]&&n["default"]===t)return;const r=this._encodeValue(t,e,i);return void 0===r||this._skipDefault(r,e,i)?void 0:r},f.prototype._encodeValue=function(t,e,i){const r=this._baseState;if(null===r.parent)return r.children[0]._encode(t,e||new n);let a=null;if(this.reporter=e,r.optional&&void 0===t){if(null===r["default"])return;t=r["default"]}let o=null,s=!1;if(r.any)a=this._createEncoderBuffer(t);else if(r.choice)a=this._encodeChoice(t,e);else if(r.contains)o=this._getUse(r.contains,i)._encode(t,e),s=!0;else if(r.children)o=r.children.map((function(i){if("null_"===i._baseState.tag)return i._encode(null,e,t);if(null===i._baseState.key)return e.error("Child should have a key");const n=e.enterKey(i._baseState.key);if("object"!==typeof t)return e.error("Child expected, but input is not object");const r=i._encode(t[i._baseState.key],e,t);return e.leaveKey(n),r}),this).filter((function(t){return t})),o=this._createEncoderBuffer(o);else if("seqof"===r.tag||"setof"===r.tag){if(!r.args||1!==r.args.length)return e.error("Too many args for : "+r.tag);if(!Array.isArray(t))return e.error("seqof/setof, but data is not Array");const i=this.clone();i._baseState.implicit=null,o=this._createEncoderBuffer(t.map((function(i){const n=this._baseState;return this._getUse(n.args[0],t)._encode(i,e)}),i))}else null!==r.use?a=this._getUse(r.use,i)._encode(t,e):(o=this._encodePrimitive(r.tag,t),s=!0);if(!r.any&&null===r.choice){const t=null!==r.implicit?r.implicit:r.tag,i=null===r.implicit?"universal":"context";null===t?null===r.use&&e.error("Tag could be omitted only for .use()"):null===r.use&&(a=this._encodeComposite(t,s,i,o))}return null!==r.explicit&&(a=this._encodeComposite(r.explicit,!1,"context",a)),a},f.prototype._encodeChoice=function(t,e){const i=this._baseState,n=i.choice[t.type];return n||o(!1,t.type+" not found in "+JSON.stringify(Object.keys(i.choice))),n._encode(t.value,e)},f.prototype._encodePrimitive=function(t,e){const i=this._baseState;if(/str$/.test(t))return this._encodeStr(e,t);if("objid"===t&&i.args)return this._encodeObjid(e,i.reverseArgs[0],i.args[1]);if("objid"===t)return this._encodeObjid(e,null,null);if("gentime"===t||"utctime"===t)return this._encodeTime(e,t);if("null_"===t)return this._encodeNull();if("int"===t||"enum"===t)return this._encodeInt(e,i.args&&i.reverseArgs[0]);if("bool"===t)return this._encodeBool(e);if("objDesc"===t)return this._encodeStr(e,t);throw new Error("Unsupported tag: "+t)},f.prototype._isNumstr=function(t){return/^[0-9 ]*$/.test(t)},f.prototype._isPrintstr=function(t){return/^[A-Za-z0-9 '()+,-./:=?]*$/.test(t)}},"83d5":function(t,e){t.exports=function(t,e){var i=t.length,n=-1;while(++n<i)t[n]^=e[n];return t}},"85b3":function(t,e,i){"use strict";const n=i("3fb5"),r=i("3768");function a(t){r.call(this,t),this.enc="pem"}n(a,r),t.exports=a,a.prototype.encode=function(t,e){const i=r.prototype.encode.call(this,t),n=i.toString("base64"),a=["-----BEGIN "+e.label+"-----"];for(let r=0;r<n.length;r+=64)a.push(n.slice(r,r+64));return a.push("-----END "+e.label+"-----"),a.join("\n")}},"863d":function(t,e,i){"use strict";i.r(e);var n=i("87a9"),r=i("1251");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"24cfc75f",null,!1,n["a"],void 0);e["default"]=s.exports},8707:function(t,e,i){
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
var n=i("b639"),r=n.Buffer;function a(t,e){for(var i in t)e[i]=t[i]}function o(t,e,i){return r(t,e,i)}r.from&&r.alloc&&r.allocUnsafe&&r.allocUnsafeSlow?t.exports=n:(a(n,e),e.Buffer=o),o.prototype=Object.create(r.prototype),a(r,o),o.from=function(t,e,i){if("number"===typeof t)throw new TypeError("Argument must not be a number");return r(t,e,i)},o.alloc=function(t,e,i){if("number"!==typeof t)throw new TypeError("Argument must be a number");var n=r(t);return void 0!==e?"string"===typeof i?n.fill(e,i):n.fill(e):n.fill(0),n},o.allocUnsafe=function(t){if("number"!==typeof t)throw new TypeError("Argument must be a number");return r(t)},o.allocUnsafeSlow=function(t){if("number"!==typeof t)throw new TypeError("Argument must be a number");return n.SlowBuffer(t)}},"87a9":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uPopup:i("c4b0").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-popup",{attrs:{value:t.value,mode:"center","mask-close-able":!1,width:"552",height:"452","border-radius":"20"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"p-rela wh-p100"},[i("v-uni-image",{staticClass:"p-abso wh-p100",attrs:{src:t.ossIcon("/auction/popup_bg_552_444.png")}}),i("v-uni-view",{staticClass:"p-rela pt-84"},[i("v-uni-view",{staticClass:"font-wei-500 font-32 text-3 l-h-44 text-center"},[t._v("温馨提示")]),i("v-uni-view",{staticClass:"mt-24 ptb-00-plr-50 font-28 text-3 l-h-40 text-center"},[t._v("您有一张29元保证金低扣券，点击使用即刻参拍～")]),i("v-uni-view",{staticClass:"flex-sb-c ptb-00-plr-76 mt-72"},[i("v-uni-button",{staticClass:"vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$emit("cancel")}}},[t._v("取消")]),i("v-uni-button",{staticClass:"vh-btn flex-c-c w-180 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$emit("use")}}},[t._v("立即使用")])],1)],1)],1)],1)},a=[]},8907:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(i("54f8"));i("c975"),i("caad6"),i("2532"),i("ac1f"),i("466d"),i("841c"),i("5319"),i("e25e"),i("14d9"),i("99af"),i("acd8");var a=uni.getSystemInfoSync(),o=a.windowWidth,s=(a.platform,i("0792")),c={name:"parser",data:function(){return{uid:this._uid,showAm:"",nodes:[]}},props:{html:String,autopause:{type:Boolean,default:!0},autoscroll:Boolean,autosetTitle:{type:Boolean,default:!0},domain:String,lazyLoad:Boolean,selectable:Boolean,tagStyle:Object,showWithAnimation:Boolean,useAnchor:Boolean},watch:{html:function(t){this.setContent(t)}},created:function(){this.imgList=[],this.imgList.each=function(t){for(var e=0,i=this.length;e<i;e++)this.setItem(e,t(this[e],e,this))},this.imgList.setItem=function(t,e){if(void 0!=t&&e){if(0==e.indexOf("http")&&this.includes(e)){for(var i,n=e.split("://")[0],r=n.length;i=e[r];r++){if("/"==i&&"/"!=e[r-1]&&"/"!=e[r+1])break;n+=Math.random()>.5?i.toUpperCase():i}return n+=e.substr(r),this[t]=n}if(this[t]=e,e.includes("data:image")){var a=e.match(/data:image\/(\S+?);(\S+?),(.+)/);if(!a)return}}}},mounted:function(){this.document=document.getElementById("rtf"+this._uid),this.html&&this.setContent(this.html)},beforeDestroy:function(){this._observer&&this._observer.disconnect(),this.imgList.each((function(t){})),clearInterval(this._timer)},methods:{setContent:function(t,e){var i=this;if(t){var n=document.createElement("div");e?this.rtf?this.rtf.appendChild(n):this.rtf=n:(this.rtf&&this.rtf.parentNode.removeChild(this.rtf),this.rtf=n),n.innerHTML=this._handleHtml(t,e);for(var a,c=this.rtf.getElementsByTagName("style"),f=0;a=c[f++];)a.innerHTML=a.innerHTML.replace(/body/g,"#rtf"+this._uid),a.setAttribute("scoped","true");!this._observer&&this.lazyLoad&&IntersectionObserver&&(this._observer=new IntersectionObserver((function(t){for(var e,n=0;e=t[n++];)e.isIntersecting&&(e.target.src=e.target.getAttribute("data-src"),e.target.removeAttribute("data-src"),i._observer.unobserve(e.target))}),{rootMargin:"500px 0px 500px 0px"}));var u=this,d=this.rtf.getElementsByTagName("title");d.length&&this.autosetTitle&&uni.setNavigationBarTitle({title:d[0].innerText});var h=function(t){var e=t.getAttribute("src");i.domain&&e&&("/"==e[0]?"/"==e[1]?t.src=(i.domain.includes("://")?i.domain.split("://")[0]:"")+":"+e:t.src=i.domain+e:e.includes("://")||0==e.indexOf("data:")||(t.src=i.domain+"/"+e))};this.imgList.length=0;for(var l,p=this.rtf.getElementsByTagName("img"),b=0,v=0;l=p[b];b++)parseInt(l.style.width||l.getAttribute("width"))>o&&(l.style.height="auto"),h(l),l.hasAttribute("ignore")||"A"==l.parentElement.nodeName||(l.i=v++,u.imgList.push(l.getAttribute("original-src")||l.src||l.getAttribute("data-src")),l.onclick=function(t){t.stopPropagation();var e=!0;this.ignore=function(){return e=!1},u.$emit("imgtap",this),e&&uni.previewImage({current:this.i,urls:u.imgList})}),l.onerror=function(){s.errorImg&&(u.imgList[this.i]=this.src=s.errorImg),u.$emit("error",{source:"img",target:this})},u.lazyLoad&&this._observer&&l.src&&0!=l.i&&(l.setAttribute("data-src",l.src),l.removeAttribute("src"),this._observer.observe(l));var m,g=this.rtf.getElementsByTagName("a"),y=(0,r.default)(g);try{for(y.s();!(m=y.n()).done;){var w=m.value;w.onclick=function(t){t.stopPropagation();var e=!0,i=this.getAttribute("href");if(u.$emit("linkpress",{href:i,ignore:function(){return e=!1}}),e&&i)if("#"==i[0])u.useAnchor&&u.navigateTo({id:i.substr(1)});else{if(0==i.indexOf("http")||0==i.indexOf("//"))return!0;uni.navigateTo({url:i})}return!1}}}catch(T){y.e(T)}finally{y.f()}var _=this.rtf.getElementsByTagName("video");u.videoContexts=_;for(var x,S=0;x=_[S++];)h(x),x.style.maxWidth="100%",x.onerror=function(){u.$emit("error",{source:"video",target:this})},x.onplay=function(){if(u.autopause)for(var t,e=0;t=u.videoContexts[e++];)t!=this&&t.pause()};var k,A,E=this.rtf.getElementsByTagName("audio"),M=(0,r.default)(E);try{for(M.s();!(k=M.n()).done;){var C=k.value;h(C),C.onerror=function(){u.$emit("error",{source:"audio",target:this})}}}catch(T){M.e(T)}finally{M.f()}if(this.autoscroll){var P,I=this.rtf.getElementsByTagName("table"),B=(0,r.default)(I);try{for(B.s();!(P=B.n()).done;){var R=P.value,O=document.createElement("div");O.style.overflow="scroll",R.parentNode.replaceChild(O,R),O.appendChild(R)}}catch(T){B.e(T)}finally{B.f()}}e||this.document.appendChild(this.rtf),this.$nextTick((function(){i.nodes=[1],i.$emit("load")})),setTimeout((function(){return i.showAm=""}),500),clearInterval(this._timer),this._timer=setInterval((function(){i.rect=i.rtf.getBoundingClientRect(),i.rect.height==A&&(i.$emit("ready",i.rect),clearInterval(i._timer)),A=i.rect.height}),350),this.showWithAnimation&&!e&&(this.showAm="animation:_show .5s")}else this.rtf&&!e&&this.rtf.parentNode.removeChild(this.rtf)},getText:function(){arguments.length>0&&void 0!==arguments[0]||this.nodes;var t="";return t=this.rtf.innerText,t},in:function(t){t.page&&t.selector&&t.scrollTop&&(this._in=t)},navigateTo:function(t){var e=this;if(!this.useAnchor)return t.fail&&t.fail("Anchor is disabled");var i=uni.createSelectorQuery().in(this._in?this._in.page:this).select((this._in?this._in.selector:"#_top")+(t.id?"".concat(" ","#").concat(t.id,",").concat(this._in?this._in.selector:"#_top").concat(" ",".").concat(t.id):"")).boundingClientRect();this._in?i.select(this._in.selector).scrollOffset().select(this._in.selector).boundingClientRect():i.selectViewport().scrollOffset(),i.exec((function(i){if(!i[0])return t.fail&&t.fail("Label not found");var n=i[1].scrollTop+i[0].top-(i[2]?i[2].top:0)+(t.offset||0);e._in?e._in.page[e._in.scrollTop]=n:uni.pageScrollTo({scrollTop:n,duration:300}),t.success&&t.success()}))},getVideoContext:function(t){if(!t)return this.videoContexts;for(var e=this.videoContexts.length;e--;)if(this.videoContexts[e].id==t)return this.videoContexts[e]},_handleHtml:function(t,e){if(!e){var i="<style scoped>@keyframes _show{0%{opacity:0}100%{opacity:1}}img{max-width:100%}";for(var n in s.userAgentStyles)i+="".concat(n,"{").concat(s.userAgentStyles[n],"}");for(n in this.tagStyle)i+="".concat(n,"{").concat(this.tagStyle[n],"}");i+="</style>",t=i+t}return t.includes("rpx")&&(t=t.replace(/[0-9.]+\s*rpx/g,(function(t){return parseFloat(t)*o/750+"px"}))),t}}};e.default=c},8947:function(t,e,i){var n=i("bac2"),r=i("82f0"),a=i("8707").Buffer,o=i("09f5"),s=i("6430"),c=i("39f5"),f=i("ae84"),u=i("3fb5");function d(t,e,i){s.call(this),this._cache=new l,this._cipher=new c.AES(e),this._prev=a.from(i),this._mode=t,this._autopadding=!0}u(d,s),d.prototype._update=function(t){var e,i;this._cache.add(t);var n=[];while(e=this._cache.get())i=this._mode.encrypt(this,e),n.push(i);return a.concat(n)};var h=a.alloc(16,16);function l(){this.cache=a.allocUnsafe(0)}function p(t,e,i){var s=n[t.toLowerCase()];if(!s)throw new TypeError("invalid suite type");if("string"===typeof e&&(e=a.from(e)),e.length!==s.key/8)throw new TypeError("invalid key length "+e.length);if("string"===typeof i&&(i=a.from(i)),"GCM"!==s.mode&&i.length!==s.iv)throw new TypeError("invalid iv length "+i.length);return"stream"===s.type?new o(s.module,e,i):"auth"===s.type?new r(s.module,e,i):new d(s.module,e,i)}d.prototype._final=function(){var t=this._cache.flush();if(this._autopadding)return t=this._mode.encrypt(this,t),this._cipher.scrub(),t;if(!t.equals(h))throw this._cipher.scrub(),new Error("data not multiple of block length")},d.prototype.setAutoPadding=function(t){return this._autopadding=!!t,this},l.prototype.add=function(t){this.cache=a.concat([this.cache,t])},l.prototype.get=function(){if(this.cache.length>15){var t=this.cache.slice(0,16);return this.cache=this.cache.slice(16),t}return null},l.prototype.flush=function(){var t=16-this.cache.length,e=a.allocUnsafe(t),i=-1;while(++i<t)e.writeUInt8(t,i);return a.concat([this.cache,e])},e.createCipheriv=p,e.createCipher=function(t,e){var i=n[t.toLowerCase()];if(!i)throw new TypeError("invalid suite type");var r=f(e,!1,i.key,i.iv);return p(t,r.key,r.iv)}},8987:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,"@-webkit-keyframes _show-data-v-5189efcc{0%{opacity:0}100%{opacity:1}}@keyframes _show-data-v-5189efcc{0%{opacity:0}100%{opacity:1}}\n\n\n\n",""]),t.exports=e},"8b71":function(t,e,i){"use strict";function n(t){const e={};return Object.keys(t).forEach((function(i){(0|i)==i&&(i|=0);const n=t[i];e[n]=i})),e}e.tagClass={0:"universal",1:"application",2:"context",3:"private"},e.tagClassByName=n(e.tagClass),e.tag={0:"end",1:"bool",2:"int",3:"bitstr",4:"octstr",5:"null_",6:"objid",7:"objDesc",8:"external",9:"real",10:"enum",11:"embed",12:"utf8str",13:"relativeOid",16:"seq",17:"set",18:"numstr",19:"printstr",20:"t61str",21:"videostr",22:"ia5str",23:"utctime",24:"gentime",25:"graphstr",26:"iso646str",27:"genstr",28:"unistr",29:"charstr",30:"bmpstr"},e.tagByName=n(e.tag)},"8b95":function(t,e,i){"use strict";var n=i("c3c0"),r=i("b525");function a(){if(!(this instanceof a))return new a;r.call(this),this.h=[3418070365,3238371032,1654270250,914150663,2438529370,812702999,355462360,4144912697,1731405415,4290775857,2394180231,1750603025,3675008525,1694076839,1203062813,3204075428]}n.inherits(a,r),t.exports=a,a.blockSize=1024,a.outSize=384,a.hmacStrength=192,a.padLength=128,a.prototype._digest=function(t){return"hex"===t?n.toHex32(this.h.slice(0,12),"big"):n.split32(this.h.slice(0,12),"big")}},"8be6":function(t,e,i){var n=i("8707").Buffer;t.exports=function(t,e,i){if(n.isBuffer(t))return t;if("string"===typeof t)return n.from(t,e);if(ArrayBuffer.isView(t))return n.from(t.buffer);throw new TypeError(i+" must be a string, a Buffer, a typed array or a DataView")}},"8c8a":function(t,e,i){(function(e){t.exports=function(t,i){for(var n=Math.min(t.length,i.length),r=new e(n),a=0;a<n;++a)r[a]=t[a]^i[a];return r}}).call(this,i("b639").Buffer)},"8ca3":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uPopup:i("c4b0").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-popup",{attrs:{value:t.value,mode:"center",width:"552rpx",height:"414rpx","border-radius":"20"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"p-rela w-552 h-414"},[i("v-uni-image",{staticClass:"p-abso w-552 h-414",attrs:{src:t.ossIcon("/auction/popup_bg_552_414.png")}}),i("v-uni-view",{staticClass:"p-rela pt-52"},[i("v-uni-view",{staticClass:"font-wei-600 font-32 text-3 text-center"},[t._v("“延时周期”是什么？")]),i("v-uni-view",{staticClass:"mt-20 ptb-00-plr-38 font-28 text-6 l-h-48 text-center"},[t._v("竞拍结束前2分钟内，若有其他人出价，则竞拍时间将延长"+t._s(t.delayTime)+"分钟，直至最后2分钟内无人出价，竞拍结束。")]),i("v-uni-view",{staticClass:"mt-62 font-wei-500 font-28 text-e80404 text-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput(!1)}}},[t._v("知道了")])],1)],1)],1)},a=[]},"8d13":function(t,e,i){"use strict";var n=i("f157"),r=i.n(n);r.a},"8d46":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{value:{type:Boolean,default:!0}},methods:{onInput:function(t){this.$emit("input",t)}}};e.default=n},"8df7":function(t,e,i){"use strict";const n=i("3fb5"),r=i("c591").Buffer,a=i("cfbd");function o(t){a.call(this,t),this.enc="pem"}n(o,a),t.exports=o,o.prototype.decode=function(t,e){const i=t.toString().split(/[\r\n]+/g),n=e.label.toUpperCase(),o=/^-----(BEGIN|END) ([^-]+)-----$/;let s=-1,c=-1;for(let r=0;r<i.length;r++){const t=i[r].match(o);if(null!==t&&t[2]===n){if(-1!==s){if("END"!==t[1])break;c=r;break}if("BEGIN"!==t[1])break;s=r}}if(-1===s||-1===c)throw new Error("PEM section not found for: "+n);const f=i.slice(s+1,c).join("");f.replace(/[^a-z0-9+/=]+/gi,"");const u=r.from(f,"base64");return a.prototype.decode.call(this,u,e)}},"8e2b":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{staticClass:"flex-s-c"},[i("v-uni-text",{staticClass:"font-wei-500 font-32 text-3 l-h-44"},[t._v("拍品参数")]),i("v-uni-view",{staticClass:"flex-c-c ml-20 ptb-00-plr-20 h-46 font-wei-500 font-24 text-e80404 l-h-34 b-rad-23",staticStyle:{background:"rgba(232, 4, 4, 0.11)"}},[t._v(t._s(t.category.category_name))])],1),0===t.goods.label||1===t.goods.label&&1===t.goods.product_type?t._l(t.rows,(function(e,n){return i("v-uni-view",{key:n,staticClass:"flex-sb-c pt-20 bt-s-02-eeeeee",class:n?"mt-20":"mt-28"},t._l(e,(function(e,n){return i("v-uni-view",{key:n,staticClass:"w-250"},[i("v-uni-view",{staticClass:"font-24 text-6 l-h-34"},[t._v(t._s(e.title))]),i("v-uni-view",{staticClass:"mt-12 font-wei-500 font-28 text-3 l-h-40"},[t._v(t._s(e.desc))])],1)})),1)})):t._e()],2)},r=[]},"8e2e":function(t,e,i){"use strict";i.r(e);var n=i("6280"),r=i("d6e4");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"48a0385c",null,!1,n["a"],void 0);e["default"]=s.exports},"901d":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{item:{type:Object,default:function(){return{}}}}};e.default=n},"90d9":function(t,e,i){"use strict";i.r(e);var n=i("5d80"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"90fe":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{list:{type:Array,default:function(){return[]}}},data:function(){return{showReplyLen:2}},computed:{sysPlatformAndroid:function(){return this.system.sysPlatformAndroid()}},methods:{onEnjoy:function(t){this.$emit("enjoy",t)},onReply:function(t){this.$emit("reply",t)},onToggleReply:function(t){this.$emit("toggleReply",t)}}};e.default=n},9152:function(t,e){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
e.read=function(t,e,i,n,r){var a,o,s=8*r-n-1,c=(1<<s)-1,f=c>>1,u=-7,d=i?r-1:0,h=i?-1:1,l=t[e+d];for(d+=h,a=l&(1<<-u)-1,l>>=-u,u+=s;u>0;a=256*a+t[e+d],d+=h,u-=8);for(o=a&(1<<-u)-1,a>>=-u,u+=n;u>0;o=256*o+t[e+d],d+=h,u-=8);if(0===a)a=1-f;else{if(a===c)return o?NaN:1/0*(l?-1:1);o+=Math.pow(2,n),a-=f}return(l?-1:1)*o*Math.pow(2,a-n)},e.write=function(t,e,i,n,r,a){var o,s,c,f=8*a-r-1,u=(1<<f)-1,d=u>>1,h=23===r?Math.pow(2,-24)-Math.pow(2,-77):0,l=n?0:a-1,p=n?1:-1,b=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,o=u):(o=Math.floor(Math.log(e)/Math.LN2),e*(c=Math.pow(2,-o))<1&&(o--,c*=2),e+=o+d>=1?h/c:h*Math.pow(2,1-d),e*c>=2&&(o++,c/=2),o+d>=u?(s=0,o=u):o+d>=1?(s=(e*c-1)*Math.pow(2,r),o+=d):(s=e*Math.pow(2,d-1)*Math.pow(2,r),o=0));r>=8;t[i+l]=255&s,l+=p,s/=256,r-=8);for(o=o<<r|s,f+=r;f>0;t[i+l]=255&o,l+=p,o/=256,f-=8);t[i+l-p]|=128*b}},"93e6":function(t,e,i){"use strict";var n=i("8707").Buffer,r=i("e372").Transform,a=i("3fb5");function o(t){r.call(this),this._block=n.allocUnsafe(t),this._blockSize=t,this._blockOffset=0,this._length=[0,0,0,0],this._finalized=!1}a(o,r),o.prototype._transform=function(t,e,i){var n=null;try{this.update(t,e)}catch(r){n=r}i(n)},o.prototype._flush=function(t){var e=null;try{this.push(this.digest())}catch(i){e=i}t(e)},o.prototype.update=function(t,e){if(function(t,e){if(!n.isBuffer(t)&&"string"!==typeof t)throw new TypeError(e+" must be a string or a buffer")}(t,"Data"),this._finalized)throw new Error("Digest already called");n.isBuffer(t)||(t=n.from(t,e));var i=this._block,r=0;while(this._blockOffset+t.length-r>=this._blockSize){for(var a=this._blockOffset;a<this._blockSize;)i[a++]=t[r++];this._update(),this._blockOffset=0}while(r<t.length)i[this._blockOffset++]=t[r++];for(var o=0,s=8*t.length;s>0;++o)this._length[o]+=s,s=this._length[o]/4294967296|0,s>0&&(this._length[o]-=4294967296*s);return this},o.prototype._update=function(){throw new Error("_update is not implemented")},o.prototype.digest=function(t){if(this._finalized)throw new Error("Digest already called");this._finalized=!0;var e=this._digest();void 0!==t&&(e=e.toString(t)),this._block.fill(0),this._blockOffset=0;for(var i=0;i<4;++i)this._length[i]=0;return e},o.prototype._digest=function(){throw new Error("_digest is not implemented")},t.exports=o},"945d":function(t,e,i){"use strict";var n=i("7d92"),r=i("0cbb"),a=i("f3a3"),o=a.assert,s=a.parseBytes,c=i("380f"),f=i("44a3");function u(t){if(o("ed25519"===t,"only tested with ed25519 so far"),!(this instanceof u))return new u(t);t=r[t].curve,this.curve=t,this.g=t.g,this.g.precompute(t.n.bitLength()+1),this.pointClass=t.point().constructor,this.encodingLength=Math.ceil(t.n.bitLength()/8),this.hash=n.sha512}t.exports=u,u.prototype.sign=function(t,e){t=s(t);var i=this.keyFromSecret(e),n=this.hashInt(i.messagePrefix(),t),r=this.g.mul(n),a=this.encodePoint(r),o=this.hashInt(a,i.pubBytes(),t).mul(i.priv()),c=n.add(o).umod(this.curve.n);return this.makeSignature({R:r,S:c,Rencoded:a})},u.prototype.verify=function(t,e,i){t=s(t),e=this.makeSignature(e);var n=this.keyFromPublic(i),r=this.hashInt(e.Rencoded(),n.pubBytes(),t),a=this.g.mul(e.S()),o=e.R().add(n.pub().mul(r));return o.eq(a)},u.prototype.hashInt=function(){for(var t=this.hash(),e=0;e<arguments.length;e++)t.update(arguments[e]);return a.intFromLE(t.digest()).umod(this.curve.n)},u.prototype.keyFromPublic=function(t){return c.fromPublic(this,t)},u.prototype.keyFromSecret=function(t){return c.fromSecret(this,t)},u.prototype.makeSignature=function(t){return t instanceof f?t:new f(this,t)},u.prototype.encodePoint=function(t){var e=t.getY().toArray("le",this.encodingLength);return e[this.encodingLength-1]|=t.getX().isOdd()?128:0,e},u.prototype.decodePoint=function(t){t=a.parseBytes(t);var e=t.length-1,i=t.slice(0,e).concat(-129&t[e]),n=0!==(128&t[e]),r=a.intFromLE(i);return this.curve.pointFromY(r,n)},u.prototype.encodeInt=function(t){return t.toArray("le",this.encodingLength)},u.prototype.decodeInt=function(t){return a.intFromLE(t)},u.prototype.isPoint=function(t){return t instanceof this.pointClass}},"949a":function(t,e,i){"use strict";i.r(e);var n=i("7622"),r=i("9f19");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"4b86c70c",null,!1,n["a"],void 0);e["default"]=s.exports},"956a":function(t,e,i){var n=i("1e3c"),r=i("fda6"),a=i("bac2"),o=i("0be83"),s=i("ae84");function c(t,e,i){if(t=t.toLowerCase(),a[t])return r.createCipheriv(t,e,i);if(o[t])return new n({key:e,iv:i,mode:t});throw new TypeError("invalid suite type")}function f(t,e,i){if(t=t.toLowerCase(),a[t])return r.createDecipheriv(t,e,i);if(o[t])return new n({key:e,iv:i,mode:t,decrypt:!0});throw new TypeError("invalid suite type")}e.createCipher=e.Cipher=function(t,e){var i,n;if(t=t.toLowerCase(),a[t])i=a[t].key,n=a[t].iv;else{if(!o[t])throw new TypeError("invalid suite type");i=8*o[t].key,n=o[t].iv}var r=s(e,!1,i,n);return c(t,r.key,r.iv)},e.createCipheriv=e.Cipheriv=c,e.createDecipher=e.Decipher=function(t,e){var i,n;if(t=t.toLowerCase(),a[t])i=a[t].key,n=a[t].iv;else{if(!o[t])throw new TypeError("invalid suite type");i=8*o[t].key,n=o[t].iv}var r=s(e,!1,i,n);return f(t,r.key,r.iv)},e.createDecipheriv=e.Decipheriv=f,e.listCiphers=e.getCiphers=function(){return Object.keys(o).concat(r.getCiphers())}},"966d":function(t,e,i){"use strict";(function(e){"undefined"===typeof e||!e.version||0===e.version.indexOf("v0.")||0===e.version.indexOf("v1.")&&0!==e.version.indexOf("v1.8.")?t.exports={nextTick:function(t,i,n,r){if("function"!==typeof t)throw new TypeError('"callback" argument must be a function');var a,o,s=arguments.length;switch(s){case 0:case 1:return e.nextTick(t);case 2:return e.nextTick((function(){t.call(null,i)}));case 3:return e.nextTick((function(){t.call(null,i,n)}));case 4:return e.nextTick((function(){t.call(null,i,n,r)}));default:a=new Array(s-1),o=0;while(o<a.length)a[o++]=arguments[o];return e.nextTick((function(){t.apply(null,a)}))}}}:t.exports=e}).call(this,i("4362"))},"980c":function(t,e,i){var n=i("8707").Buffer,r=i("399f"),a=i("3337").ec,o=i("2aee"),s=i("cd91");function c(t,e){if(t.cmpn(0)<=0)throw new Error("invalid sig");if(t.cmp(e)>=e)throw new Error("invalid sig")}t.exports=function(t,e,i,f,u){var d=o(i);if("ec"===d.type){if("ecdsa"!==f&&"ecdsa/rsa"!==f)throw new Error("wrong public key type");return function(t,e,i){var n=s[i.data.algorithm.curve.join(".")];if(!n)throw new Error("unknown curve "+i.data.algorithm.curve.join("."));var r=new a(n),o=i.data.subjectPrivateKey.data;return r.verify(e,t,o)}(t,e,d)}if("dsa"===d.type){if("dsa"!==f)throw new Error("wrong public key type");return function(t,e,i){var n=i.data.p,a=i.data.q,s=i.data.g,f=i.data.pub_key,u=o.signature.decode(t,"der"),d=u.s,h=u.r;c(d,a),c(h,a);var l=r.mont(n),p=d.invm(a),b=s.toRed(l).redPow(new r(e).mul(p).mod(a)).fromRed().mul(f.toRed(l).redPow(h.mul(p).mod(a)).fromRed()).mod(n).mod(a);return 0===b.cmp(h)}(t,e,d)}if("rsa"!==f&&"ecdsa/rsa"!==f)throw new Error("wrong public key type");e=n.concat([u,e]);var h=d.modulus.byteLength(),l=[1],p=0;while(e.length+l.length+2<h)l.push(255),p++;l.push(0);var b=-1;while(++b<e.length)l.push(e[b]);l=n.from(l);var v=r.mont(d.modulus);t=new r(t).toRed(v),t=t.redPow(new r(d.publicExponent)),t=n.from(t.fromRed().toArray());var m=p<8?1:0;h=Math.min(t.length,l.length),t.length!==l.length&&(m=1),b=-1;while(++b<h)m|=t[b]^l[b];return 0===m}},"98e6":function(t,e,i){"use strict";var n=i("3fb5"),r=i("f576"),a=i("b5ca"),o=i("69f2"),s=i("6430");function c(t){s.call(this,"digest"),this._hash=t}n(c,s),c.prototype._update=function(t){this._hash.update(t)},c.prototype._final=function(){return this._hash.digest()},t.exports=function(t){return t=t.toLowerCase(),"md5"===t?new r:"rmd160"===t||"ripemd160"===t?new a:new c(o(t))}},"9bfd":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={vhNavbar:i("12c6").default,vhSwiper:i("5307").default,AuctionBidList:i("5a03").default,AuctionGoodsSetting:i("e3aa").default,AuctionGoodsParams:i("c236").default,AuctionGoodsDesc:i("d948").default,AuctionGoodsTips:i("8e2e").default,AuctionGoodsComments:i("c5d9").default,AuctionRecommendGoodsList:i("e17a").default,AuctionWarnPopup:i("949a").default,AuctionEarnestPayPopup:i("5756").default,AuctionEarnestPaySelectPopup:i("74c5").default,AuctionEarnestPaySuccessPopup:i("23df").default,AuctionBidPopup:i("c8ac").default,AuctionBidSuccessPopup:i("43ea").default,AuctionPAWarnPopup:i("03ac").default,AEUseCouponPopup:i("863d").default,AuctionFreeShipmentPopup:i("08de").default,AuctionReversePricePopup:i("50476").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{attrs:{id:"goods-detail"}},[i("vh-navbar",{attrs:{title:"详情页",height:"46"}},[t.$app?i("v-uni-view",{staticClass:"d-flex",attrs:{slot:"right"},slot:"right"},[i("v-uni-image",{staticClass:"p-24 w-44 h-44",attrs:{src:t.ossIcon("/auction/icon_share.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onShare.apply(void 0,arguments)}}})],1):t._e()],1),t.loading?t._e():i("v-uni-view",{class:t.isPendPutaway?"":"pb-104"},[i("v-uni-view",{staticClass:"auction-gdetail__swiper"},[i("vh-swiper",{attrs:{"loading-type":2,list:t.goodsDetail.$swiperList,height:"464","border-radius":"0","img-mode":"aspectFit"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.onSwiperChange.apply(void 0,arguments)},click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSwiperClick.apply(void 0,arguments)}}})],1),i("v-uni-view",{staticClass:"p-rela"},[i("v-uni-image",{staticClass:"p-abso top-0 w-p100 h-70",attrs:{src:t.ossIcon(t.goodsStatusBg)}}),i("v-uni-view",{staticClass:"p-rela flex-sb-c pl-50 pr-24 h-70"},[i("v-uni-text",{staticClass:"font-wei-500 font-28 text-ffffff l-h-42"},[t._v(t._s(t.goodsStatusText))]),t.isPendPutaway?t._e():i("v-uni-view",{staticClass:"flex-c-c"},[i("v-uni-text",{staticClass:"font-24 l-h-34",class:t.goodsStatusTimeTextClazz},[t._v(t._s(t.goodsStatusTimeText))]),i("v-uni-view",{staticClass:"flex-c-c ml-06"},[i("v-uni-text",{staticClass:"font-wei-500 font-24 l-h-34",class:t.goodsStatusTimeTextClazz},[t._v(t._s(t._f("date")(t.goodsStatusTimeValue,"mm月dd日hh:MM:ss")))])],1)],1)],1)],1),t.isShowFixedTime?i("v-uni-view",{staticClass:"p-fixed left-0 flex-c-c w-p100 h-70 font-24 text-ffffff l-h-34 z-9999",class:t.goodsStatusTimeTextBgClazz,style:{top:46+t.$appStatusBarHeight+"px"}},[i("v-uni-text",[t._v(t._s(t.goodsStatusTimeText))]),i("v-uni-text",{staticClass:"ml-06 font-wei-500"},[t._v(t._s(t._f("date")(t.goodsStatusTimeValue,"mm月dd日hh:MM:ss")))])],1):t._e(),i("v-uni-view",{staticClass:"ptb-32-plr-24"},[i("v-uni-view",{staticClass:"flex-sb-c"},[i("v-uni-view",[i("v-uni-text",{staticClass:"font-28 text-e80404"},[t._v(t._s(t.goodsStatusObj.pricePrefixText))]),i("v-uni-text",{staticClass:"ml-06 font-52 text-e80404"},[i("v-uni-text",{staticClass:"font-28"},[t._v("¥")]),t._v(t._s(t.goodsDetail.final_auction_price))],1)],1),i("v-uni-view",{staticClass:"flex-c-c font-wei-500 font-28 text-3"},[i("v-uni-text",[t._v("卖家报价")]),i("v-uni-text",{staticClass:"ml-06"},[t._v("¥"+t._s(t.goodsDetail.quote))])],1)],1),i("v-uni-view",{staticClass:"flex-s-c mt-06"},[i("v-uni-view",{staticClass:"flex-c-c w-84 h-40 font-wei-500 font-26 text-3 b-s-02-d8d8d8 b-rad-06",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.freeShipmentPopupVisible=!0}}},[t._v("包邮")]),i("v-uni-view",{staticClass:"flex-c-c ml-20 w-166 h-40 font-wei-500 font-26 b-rad-06",class:t.isExistReservePrice?"text-e80404 b-s-02-fce4e3":"text-ff9127 b-s-02-ffe7cf",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.reversePricePopupVisible=!0}}},[i("span",[t._v(t._s(t.isExistReservePrice?"有保留价":"无保留价"))]),i("v-uni-image",{staticClass:"ml-06 w-24 h-24",attrs:{src:t.ossIcon("/auction/tips_28.png")}})],1)],1),i("v-uni-view",{staticClass:"mt-16 font-wei-500 font-30 text-3 l-h-44"},[t._v(t._s(t.goodsDetail.title))]),i("v-uni-view",{staticClass:"flex-sb-c mt-30 font-wei-500 font-28 text-9"},[i("v-uni-text",[t._v("围观"),i("v-uni-text",{staticClass:"text-3"},[t._v(t._s(" ")+t._s(t.goodsDetail.pageviews||0)+" 次")])],1),i("v-uni-text",[t._v("设置提醒"),i("v-uni-text",{staticClass:"text-3"},[t._v(t._s(" ")+t._s(t.goodsDetail.msg_count||0)+t._s(" "))]),t._v("人")],1)],1)],1),i("v-uni-view",{staticClass:"ptb-20-plr-24 bg-f5f5f5"},[t.bidRecords.length?i("v-uni-view",{staticClass:"mb-20 ptb-28-plr-24 pb-08 bg-ffffff b-rad-10"},[i("v-uni-view",{staticClass:"flex-sb-c",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.navigateTo(t.routeTable.pHAuctionBidRecords+"?id="+t.id+"&onsale_status="+t.goodsDetail.onsale_status)}}},[i("v-uni-text",{staticClass:"font-wei-500 font-32 text-3"},[t._v("拍卖记录")]),i("v-uni-view",{staticClass:"flex-c-c"},[i("v-uni-text",{staticClass:"font-24 text-3"},[t._v(t._s(t.allBidRecordsLength)+"次")]),i("v-uni-image",{staticClass:"ml-04 w-12 h-20",attrs:{src:t.ossIcon("/after_sale_detail/arrow_right_12x20.png")}})],1)],1),i("AuctionBidList",{staticClass:"mt-16",attrs:{list:t.bidRecords,uid:t.userInfo.uid,onsaleStatus:t.goodsDetail.onsale_status,isFromDetail:!0}})],1):t._e(),i("v-uni-view",{staticClass:"ptb-28-plr-24 bg-ffffff b-rad-10"},[i("AuctionGoodsSetting",{attrs:{goods:t.goodsDetail,isShowNonsupport:!0,earnestStatus:t.earnestStatus,earnestOrderNo:t.earnestOrderNo}})],1),i("v-uni-view",{staticClass:"mt-20 ptb-28-plr-24 bg-ffffff b-rad-10"},[i("AuctionGoodsParams",{attrs:{goods:t.goodsDetail}})],1),1===t.goodsDetail.label?[t.goodsDetail.brief?i("v-uni-view",{staticClass:"mt-20 ptb-28-plr-24 bg-ffffff b-rad-10"},[i("v-uni-view",{staticClass:"font-wei-500 font-32 text-3"},[t._v("拍品详情")]),i("v-uni-view",{staticClass:"mt-12 font-28 text-3 l-h-40"},[t._v(t._s(t.goodsDetail.brief))])],1):t._e()]:i("v-uni-view",{staticClass:"mt-20 ptb-28-plr-24 bg-ffffff b-rad-10"},[i("AuctionGoodsDesc",{attrs:{goods:t.goodsDetail}})],1),1===t.goodsDetail.label?i("v-uni-view",{staticClass:"mt-20 ptb-28-plr-24 bg-ffffff b-rad-10"},[i("AuctionGoodsTips",{attrs:{goods:t.goodsDetail}})],1):t._e(),i("v-uni-view",{staticClass:"mt-20 ptb-32-plr-24 pt-40 bg-ffffff b-rad-10",attrs:{id:"auction-goods-comments"}},[i("AuctionGoodsComments",{ref:"auctionGoodsCommentsRef",attrs:{goodsId:t.id}})],1),i("v-uni-view",{attrs:{id:"auction-goods-recommends"}},[i("AuctionRecommendGoodsList",{ref:"auctionGoodsRecommendsRef",staticClass:"mt-70"})],1),i("v-uni-view",{staticClass:"pt-60 h-118 font-wei-500 font-18 text-9 l-h-26 text-center"},[t._v("哼！谁还没有点底线")])],2),t.isPendPutaway?t._e():i("v-uni-view",{directives:[{name:"safeBeautyBottom",rawName:"v-safeBeautyBottom",value:t.$safeBeautyBottom,expression:"$safeBeautyBottom"}],staticClass:"p-fixed left-0 bottom-0 flex-sb-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-9999"},[i("v-uni-view",{staticClass:"d-flex ml-24 h-p100"},[i("v-uni-view",{staticClass:"flex-c-c flex-column mr-30 w-84 h-p100",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onEnjoy.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"w-40 h-40",attrs:{src:t.ossIcon("/auction/enjoy"+(t.goodsDetail.is_like?"_h":"")+"_40.png")}}),i("v-uni-text",{staticClass:"font-22 l-h-32",class:t.goodsDetail.is_like?"text-ff9127":"text-9"},[t._v("收藏")])],1),i("v-uni-view",{staticClass:"flex-c-c flex-column mr-30 w-84 h-p100",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onRemind.apply(void 0,arguments)}}},[t.goodsDetail.is_msg?i("v-uni-image",{staticClass:"w-40 h-40",attrs:{src:t.ossIcon("/auction/remind_h_40.png")}}):i("v-uni-image",{staticClass:"w-40 h-40",attrs:{src:t.ossIcon("/auction/remind_66.gif")}}),i("v-uni-text",{staticClass:"font-22 l-h-32",class:t.goodsDetail.is_msg?"text-ff9127":"text-9"},[t._v("提醒")])],1),i("v-uni-view",{staticClass:"flex-c-c flex-column w-84 h-p100",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onComment.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"w-40 h-40",attrs:{src:t.ossIcon("/auction/comment_40.png")}}),i("v-uni-text",{staticClass:"font-22 text-9 l-h-32"},[t._v("评论")])],1)],1),i("v-uni-button",{staticClass:"vh-btn flex-c-c mr-24 w-352 h-64 font-wei-500 font-28 b-rad-32",class:t.goodsDetail.onsale_status!==t.MAuctionGoodsStatus.OnAuction?"text-d8d8d8 bg-ffffff b-s-02-d8d8d8":"text-ffffff bg-e80404",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleBid.apply(void 0,arguments)}}},[t._v(t._s(t.goodsDetail.onsale_status===t.MAuctionGoodsStatus.AuctionAbort?"拍卖已结束":"我要出价"))])],1)],1),i("AuctionWarnPopup",{on:{agree:function(e){arguments[0]=e=t.$handleEvent(e),t.onAuctionWarnAgree.apply(void 0,arguments)}},model:{value:t.warnPopupVisible,callback:function(e){t.warnPopupVisible=e},expression:"warnPopupVisible"}}),i("AuctionEarnestPayPopup",{ref:"auctionEarnestPayPopupRef",attrs:{goods:t.goodsDetail,earnestCouponInfo:t.earnestCouponInfo},on:{orderCreateSuccess:function(e){arguments[0]=e=t.$handleEvent(e),t.onEarnestOrderCreateSuccess.apply(void 0,arguments)},orderCreateError:function(e){arguments[0]=e=t.$handleEvent(e),t.onEarnestOrderCreateError.apply(void 0,arguments)}},model:{value:t.earnestPayPopupVisible,callback:function(e){t.earnestPayPopupVisible=e},expression:"earnestPayPopupVisible"}}),i("AuctionEarnestPaySelectPopup",{ref:"auctionEarnestPaySelectPopupRef",attrs:{orderInfo:t.earnestOrderInfo},on:{paySuccess:function(e){arguments[0]=e=t.$handleEvent(e),t.onEarnestOrderPaySuccess.apply(void 0,arguments)}},model:{value:t.earnestPaySelectPopupVisible,callback:function(e){t.earnestPaySelectPopupVisible=e},expression:"earnestPaySelectPopupVisible"}}),i("AuctionEarnestPaySuccessPopup",{model:{value:t.earnestPaySuccessPopupVisible,callback:function(e){t.earnestPaySuccessPopupVisible=e},expression:"earnestPaySuccessPopupVisible"}}),i("AuctionBidPopup",{attrs:{goods:t.goodsDetail,isBidPrice:!!t.allBidRecordsLength,anonymousName:t.anonymousName,provinceName:t.provinceName},model:{value:t.bidPopupVisible,callback:function(e){t.bidPopupVisible=e},expression:"bidPopupVisible"}}),i("AuctionBidSuccessPopup",{model:{value:t.bidSuccessPopupVisible,callback:function(e){t.bidSuccessPopupVisible=e},expression:"bidSuccessPopupVisible"}}),i("AuctionPAWarnPopup",{attrs:{isBuyer:!0},model:{value:t.PAWarnPopupVisible,callback:function(e){t.PAWarnPopupVisible=e},expression:"PAWarnPopupVisible"}}),i("AEUseCouponPopup",{on:{cancel:function(e){arguments[0]=e=t.$handleEvent(e),t.onAEUseCouponCanel.apply(void 0,arguments)},use:function(e){arguments[0]=e=t.$handleEvent(e),t.onAEUseCouponUse.apply(void 0,arguments)}},model:{value:t.EUseCouponPopupVisible,callback:function(e){t.EUseCouponPopupVisible=e},expression:"EUseCouponPopupVisible"}}),i("AuctionFreeShipmentPopup",{model:{value:t.freeShipmentPopupVisible,callback:function(e){t.freeShipmentPopupVisible=e},expression:"freeShipmentPopupVisible"}}),i("AuctionReversePricePopup",{model:{value:t.reversePricePopupVisible,callback:function(e){t.reversePricePopupVisible=e},expression:"reversePricePopupVisible"}})],1)},a=[]},"9c27":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("99af"),i("d3b7");var r=n(i("d0af")),a=n(i("f3f3")),o=n(i("c992")),s=i("26cb"),c={props:{value:{type:Boolean,default:!0},goods:{type:Object,default:function(){return{}}},isBidPrice:{type:Boolean,default:!1},anonymousName:{type:String,defualt:""},provinceName:{type:String,default:""}},data:function(){return{isAnonymous:!1,bidPrice:"",bidFailPopupVisible:!1,bidFailMsg:"",bidBtnLoading:!1}},computed:(0,a.default)((0,a.default)({},(0,s.mapState)(["userInfo"])),{},{bidPriceSizeValid:function(t){var e=t.bidPrice,i=t.goods,n=t.isBidPrice,r=+e,a=i.final_auction_price,o=+a;return!n&&r>=o||n&&r>o},subBtnStatus:function(t){var e=t.bidPriceSizeValid,i=t.goods,n=t.bidPrice,r=t.isBidPrice;if(!e)return!1;var a=i.final_auction_price,s=i.markup,c=+a,f=+s,u=+n;return!r&&u>c||r&&u>=(0,o.default)(f,"*",2).next("+",c).result}}),watch:{value:function(){var t=this.goods,e=t.final_auction_price,i=t.markup;this.isBidPrice?this.bidPrice=this.formatPrice((0,o.default)(+e,"+",+i).result):this.bidPrice=e},"goods.final_auction_price":function(){var t=this.goods,e=t.final_auction_price,i=t.markup;this.bidPriceSizeValid||(this.bidPrice=this.formatPrice((0,o.default)(+e,"+",+i).result))}},methods:{onInput:function(t){this.$emit("input",t)},onCalcBidPrice:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.bidPriceSizeValid&&("sub"!==t||this.subBtnStatus)){var e=+this.bidPrice,i=+this.goods.markup;if("sub"!==t||e!==+this.goods.final_auction_price){var n=e;switch(t){case"sub":n=(0,o.default)(e,"-",i).result;break;case"add":n=(0,o.default)(e,"+",i).result;break}this.bidPrice=this.formatPrice(n)}}},formatPrice:function(t){var e="".concat(t).split("."),i=(0,r.default)(e,2),n=i[0],a=void 0===n?"":n,o=i[1],s=void 0===o?"":o,c=s.length;switch(c){case 0:return"".concat(a,".00");case 1:return"".concat(a,".").concat(s,"0");case 2:return t}},onBid:function(){var t=this;if(!this.bidBtnLoading){this.bidBtnLoading=!0;var e=this.userInfo,i=e.nickname,n=e.avatar_image,r={id:this.goods.id,bid_price:this.bidPrice,is_anonymous:+this.isAnonymous,nickname:this.isAnonymous?this.anonymousName:i,avatar_image:n,province_name:this.provinceName};this.$u.api.auctionBid(r).catch((function(e){var i;t.bidFailMsg=(null===e||void 0===e||null===(i=e.data)||void 0===i?void 0:i.error_msg)||"",t.bidFailPopupVisible=!0})).finally((function(){t.bidBtnLoading=!1,t.onInput(!1)}))}}}};e.default=c},"9c98":function(t,e,i){"use strict";i.r(e);var n=i("5712"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"9f19":function(t,e,i){"use strict";i.r(e);var n=i("d220"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"9f45":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"flex-sb-c"},[i("v-uni-text",{staticClass:"font-24 text-9 l-h-34"},[t._v(t._s(t.item.comment_time))]),i("v-uni-view",{staticClass:"flex-c-c"},[i("v-uni-view",{staticClass:"w-138"},[i("v-uni-view",{staticClass:"flex-s-c",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.$emit("enjoy",t.item)}}},[i("v-uni-image",{staticClass:"w-26 h-26",attrs:{src:t.ossIcon("/comm/zan"+(t.item.like_status?"_h":"")+"_26.png")}}),i("v-uni-text",{staticClass:"ml-06 font-28 text-9 l-h-40"},[t._v(t._s(t.item.like_nums))])],1)],1),i("v-uni-view",{staticClass:"flex-c-c",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.$emit("reply",t.item)}}},[i("v-uni-image",{staticClass:"w-26 h-26",attrs:{src:t.ossIcon("/comm/reply_26.png")}}),i("v-uni-text",{staticClass:"ml-06 font-28 text-9 l-h-40"},[t._v("回复")])],1)],1)],1)},r=[]},"9f9d":function(t,e,i){(function(e,i){var n;if(e.process&&e.process.browser)n="utf-8";else if(e.process&&e.process.version){var r=parseInt(i.version.split(".")[0].slice(1),10);n=r>=6?"utf-8":"binary"}else n="utf-8";t.exports=n}).call(this,i("c8ba"),i("4362"))},a099:function(t,e,i){e.pbkdf2=i("206d"),e.pbkdf2Sync=i("e07b")},a255:function(t,e,i){var n=i("3fb5"),r=i("b672"),a=i("8707").Buffer,o=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],s=new Array(64);function c(){this.init(),this._w=s,r.call(this,64,56)}function f(t,e,i){return i^t&(e^i)}function u(t,e,i){return t&e|i&(t|e)}function d(t){return(t>>>2|t<<30)^(t>>>13|t<<19)^(t>>>22|t<<10)}function h(t){return(t>>>6|t<<26)^(t>>>11|t<<21)^(t>>>25|t<<7)}function l(t){return(t>>>7|t<<25)^(t>>>18|t<<14)^t>>>3}function p(t){return(t>>>17|t<<15)^(t>>>19|t<<13)^t>>>10}n(c,r),c.prototype.init=function(){return this._a=1779033703,this._b=3144134277,this._c=1013904242,this._d=2773480762,this._e=1359893119,this._f=2600822924,this._g=528734635,this._h=1541459225,this},c.prototype._update=function(t){for(var e=this._w,i=0|this._a,n=0|this._b,r=0|this._c,a=0|this._d,s=0|this._e,c=0|this._f,b=0|this._g,v=0|this._h,m=0;m<16;++m)e[m]=t.readInt32BE(4*m);for(;m<64;++m)e[m]=p(e[m-2])+e[m-7]+l(e[m-15])+e[m-16]|0;for(var g=0;g<64;++g){var y=v+h(s)+f(s,c,b)+o[g]+e[g]|0,w=d(i)+u(i,n,r)|0;v=b,b=c,c=s,s=a+y|0,a=r,r=n,n=i,i=y+w|0}this._a=i+this._a|0,this._b=n+this._b|0,this._c=r+this._c|0,this._d=a+this._d|0,this._e=s+this._e|0,this._f=c+this._f|0,this._g=b+this._g|0,this._h=v+this._h|0},c.prototype._hash=function(){var t=a.allocUnsafe(32);return t.writeInt32BE(this._a,0),t.writeInt32BE(this._b,4),t.writeInt32BE(this._c,8),t.writeInt32BE(this._d,12),t.writeInt32BE(this._e,16),t.writeInt32BE(this._f,20),t.writeInt32BE(this._g,24),t.writeInt32BE(this._h,28),t},t.exports=c},a49d:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={props:{value:{type:Boolean,default:!0},delayTime:{type:[Number,String],default:5}},methods:{onInput:function(t){this.$emit("input",t)}}};e.default=n},a4fa:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d81d"),i("cb29"),i("fb6a");var n=i("d8be"),r={props:{goods:{type:Object,default:function(){return{}}}},computed:{category:function(t){var e=t.goods;return(null===e||void 0===e?void 0:e.category_arr)||{}},rows:function(t){var e=t.category,i=e.category_id,r=e.brand,a=e.country,o=e.odor_type,s=e.net_content,c=e.alcoholic_strength,f=e.years,u=e.production_year,d=[{title:"产国",desc:a||"未知"},{title:"度数(仅供参考)",desc:c?"".concat(c,"%vol"):"未知"},{title:"容量",desc:s?"".concat(s,"ml"):"未知"},{title:"年份",desc:f?"".concat(f):"未知"}];return i===n.MAuctionGoodsCategory.WhiteSpirits&&(d=[{title:"品牌",desc:r||"未知"},{title:"香型",desc:o||"未知"},{title:"容量",desc:s?"".concat(s,"ml"):"未知"},{title:"度数",desc:c?"".concat(c,"%vol"):"未知"},{title:"年份",desc:f?"".concat(f):"未知"},{title:"生产年份",desc:u?"".concat(u,"年"):"未知"}]),new Array(Math.ceil(d.length/2)).fill("").map((function(t,e){return d.slice(2*e,2*e+2)}))}}};e.default=r},a958:function(t,e,i){(function(e){var n=i("399f"),r=i("11dc");function a(t){var e,i=t.modulus.byteLength();do{e=new n(r(i))}while(e.cmp(t.modulus)>=0||!e.umod(t.prime1)||!e.umod(t.prime2));return e}function o(t,i){var r=function(t){var e=a(t),i=e.toRed(n.mont(t.modulus)).redPow(new n(t.publicExponent)).fromRed();return{blinder:i,unblinder:e.invm(t.modulus)}}(i),o=i.modulus.byteLength(),s=new n(t).mul(r.blinder).umod(i.modulus),c=s.toRed(n.mont(i.prime1)),f=s.toRed(n.mont(i.prime2)),u=i.coefficient,d=i.prime1,h=i.prime2,l=c.redPow(i.exponent1).fromRed(),p=f.redPow(i.exponent2).fromRed(),b=l.isub(p).imul(u).umod(d).imul(h);return p.iadd(b).imul(r.unblinder).umod(i.modulus).toArrayLike(e,"be",o)}o.getr=a,t.exports=o}).call(this,i("b639").Buffer)},a9e0:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},i("a4d3"),i("e01a"),i("d3b7"),i("d28b"),i("3ca3"),i("ddb0"),i("a630")},aa56:function(t,e,i){"use strict";var n=i("c3c0"),r=n.rotr32;function a(t,e,i){return t&e^~t&i}function o(t,e,i){return t&e^t&i^e&i}function s(t,e,i){return t^e^i}e.ft_1=function(t,e,i,n){return 0===t?a(e,i,n):1===t||3===t?s(e,i,n):2===t?o(e,i,n):void 0},e.ch32=a,e.maj32=o,e.p32=s,e.s0_256=function(t){return r(t,2)^r(t,13)^r(t,22)},e.s1_256=function(t){return r(t,6)^r(t,11)^r(t,25)},e.g0_256=function(t){return r(t,7)^r(t,18)^t>>>3},e.g1_256=function(t){return r(t,17)^r(t,19)^t>>>10}},aa7d:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(i("d0ff"));i("d3b7"),i("159b"),i("99af");var a={props:{isInit:{type:Boolean,default:!1}},data:function(){return{list:[]}},methods:{load:function(){var t=this;this.$u.api.getAuctionHotRecommendGoodsList().then((function(e){var i=(null===e||void 0===e?void 0:e.data)||{},n=i.auction,a=void 0===n?[]:n,o=i.second_periods,s=void 0===o?[]:o;a.forEach((function(t){t.$isShowPageviews=!0})),s.forEach((function(t){t.banner_img=t.banner_img&&t.banner_img[0]})),t.list=[].concat((0,r.default)(a),(0,r.default)(s))}))}},created:function(){this.isInit&&this.load()}};e.default=a},ad25:function(t,e,i){var n=i("2aee"),r=i("11dc"),a=i("98e6"),o=i("f460"),s=i("83d5"),c=i("399f"),f=i("5291"),u=i("a958"),d=i("8707").Buffer;t.exports=function(t,e,i){var h;h=t.padding?t.padding:i?1:4;var l,p=n(t);if(4===h)l=function(t,e){var i=t.modulus.byteLength(),n=e.length,f=a("sha1").update(d.alloc(0)).digest(),u=f.length,h=2*u;if(n>i-h-2)throw new Error("message too long");var l=d.alloc(i-n-h-2),p=i-u-1,b=r(u),v=s(d.concat([f,l,d.alloc(1,1),e],p),o(b,p)),m=s(b,o(v,u));return new c(d.concat([d.alloc(1),m,v],i))}(p,e);else if(1===h)l=function(t,e,i){var n,a=e.length,o=t.modulus.byteLength();if(a>o-11)throw new Error("message too long");n=i?d.alloc(o-a-3,255):function(t){var e,i=d.allocUnsafe(t),n=0,a=r(2*t),o=0;while(n<t)o===a.length&&(a=r(2*t),o=0),e=a[o++],e&&(i[n++]=e);return i}(o-a-3);return new c(d.concat([d.from([0,i?1:2]),n,d.alloc(1),e],o))}(p,e,i);else{if(3!==h)throw new Error("unknown padding");if(l=new c(e),l.cmp(p.modulus)>=0)throw new Error("data too long for modulus")}return i?u(l,p):f(l,p)}},ad66:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i("d8be"),r={props:{list:{type:Array,default:function(){return[]}},uid:{required:!0},onsaleStatus:{required:!0},isFromDetail:{type:Boolean,defualt:!1}},data:function(){return{MAuctionGoodsStatus:n.MAuctionGoodsStatus}},methods:{getStatusClazz:function(t){return this.isFromDetail?t?"w-58 h-34":"w-60 h-36":"w-58 h-34"},getStatusIcon:function(t){return this.isFromDetail?t?"/auction/abl_out_58_34.png":n.MAuctionGoodsStatus.AuctionAbort===this.onsaleStatus?"/auction/abl_success_60_36.png":"/auction/abl_lead_60_36.png":"/auction/abl_out_58_34.png"}}};e.default=r},ad71:function(t,e,i){"use strict";(function(e,n){var r=i("966d");t.exports=y;var a,o=i("e3db");y.ReadableState=g;i("faa1").EventEmitter;var s=function(t,e){return t.listeners(e).length},c=i("429b"),f=i("8707").Buffer,u=e.Uint8Array||function(){};var d=Object.create(i("3a7c"));d.inherits=i("3fb5");var h=i(1),l=void 0;l=h&&h.debuglog?h.debuglog("stream"):function(){};var p,b=i("5e1a"),v=i("4681");d.inherits(y,c);var m=["error","close","destroy","pause","resume"];function g(t,e){a=a||i("b19a"),t=t||{};var n=e instanceof a;this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.readableObjectMode);var r=t.highWaterMark,o=t.readableHighWaterMark,s=this.objectMode?16:16384;this.highWaterMark=r||0===r?r:n&&(o||0===o)?o:s,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new b,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(p||(p=i("7d72").StringDecoder),this.decoder=new p(t.encoding),this.encoding=t.encoding)}function y(t){if(a=a||i("b19a"),!(this instanceof y))return new y(t);this._readableState=new g(t,this),this.readable=!0,t&&("function"===typeof t.read&&(this._read=t.read),"function"===typeof t.destroy&&(this._destroy=t.destroy)),c.call(this)}function w(t,e,i,n,r){var a,o=t._readableState;null===e?(o.reading=!1,function(t,e){if(e.ended)return;if(e.decoder){var i=e.decoder.end();i&&i.length&&(e.buffer.push(i),e.length+=e.objectMode?1:i.length)}e.ended=!0,S(t)}(t,o)):(r||(a=function(t,e){var i;(function(t){return f.isBuffer(t)||t instanceof u})(e)||"string"===typeof e||void 0===e||t.objectMode||(i=new TypeError("Invalid non-string/buffer chunk"));return i}(o,e)),a?t.emit("error",a):o.objectMode||e&&e.length>0?("string"===typeof e||o.objectMode||Object.getPrototypeOf(e)===f.prototype||(e=function(t){return f.from(t)}(e)),n?o.endEmitted?t.emit("error",new Error("stream.unshift() after end event")):_(t,o,e,!0):o.ended?t.emit("error",new Error("stream.push() after EOF")):(o.reading=!1,o.decoder&&!i?(e=o.decoder.write(e),o.objectMode||0!==e.length?_(t,o,e,!1):A(t,o)):_(t,o,e,!1))):n||(o.reading=!1));return function(t){return!t.ended&&(t.needReadable||t.length<t.highWaterMark||0===t.length)}(o)}function _(t,e,i,n){e.flowing&&0===e.length&&!e.sync?(t.emit("data",i),t.read(0)):(e.length+=e.objectMode?1:i.length,n?e.buffer.unshift(i):e.buffer.push(i),e.needReadable&&S(t)),A(t,e)}Object.defineProperty(y.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),y.prototype.destroy=v.destroy,y.prototype._undestroy=v.undestroy,y.prototype._destroy=function(t,e){this.push(null),e(t)},y.prototype.push=function(t,e){var i,n=this._readableState;return n.objectMode?i=!0:"string"===typeof t&&(e=e||n.defaultEncoding,e!==n.encoding&&(t=f.from(t,e),e=""),i=!0),w(this,t,e,!1,i)},y.prototype.unshift=function(t){return w(this,t,null,!0,!1)},y.prototype.isPaused=function(){return!1===this._readableState.flowing},y.prototype.setEncoding=function(t){return p||(p=i("7d72").StringDecoder),this._readableState.decoder=new p(t),this._readableState.encoding=t,this};function x(t,e){return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!==t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=function(t){return t>=8388608?t=8388608:(t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++),t}(t)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0))}function S(t){var e=t._readableState;e.needReadable=!1,e.emittedReadable||(l("emitReadable",e.flowing),e.emittedReadable=!0,e.sync?r.nextTick(k,t):k(t))}function k(t){l("emit readable"),t.emit("readable"),P(t)}function A(t,e){e.readingMore||(e.readingMore=!0,r.nextTick(E,t,e))}function E(t,e){var i=e.length;while(!e.reading&&!e.flowing&&!e.ended&&e.length<e.highWaterMark){if(l("maybeReadMore read 0"),t.read(0),i===e.length)break;i=e.length}e.readingMore=!1}function M(t){l("readable nexttick read 0"),t.read(0)}function C(t,e){e.reading||(l("resume read 0"),t.read(0)),e.resumeScheduled=!1,e.awaitDrain=0,t.emit("resume"),P(t),e.flowing&&!e.reading&&t.read(0)}function P(t){var e=t._readableState;l("flow",e.flowing);while(e.flowing&&null!==t.read());}function I(t,e){return 0===e.length?null:(e.objectMode?i=e.buffer.shift():!t||t>=e.length?(i=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.head.data:e.buffer.concat(e.length),e.buffer.clear()):i=function(t,e,i){var n;t<e.head.data.length?(n=e.head.data.slice(0,t),e.head.data=e.head.data.slice(t)):n=t===e.head.data.length?e.shift():i?function(t,e){var i=e.head,n=1,r=i.data;t-=r.length;while(i=i.next){var a=i.data,o=t>a.length?a.length:t;if(o===a.length?r+=a:r+=a.slice(0,t),t-=o,0===t){o===a.length?(++n,i.next?e.head=i.next:e.head=e.tail=null):(e.head=i,i.data=a.slice(o));break}++n}return e.length-=n,r}(t,e):function(t,e){var i=f.allocUnsafe(t),n=e.head,r=1;n.data.copy(i),t-=n.data.length;while(n=n.next){var a=n.data,o=t>a.length?a.length:t;if(a.copy(i,i.length-t,0,o),t-=o,0===t){o===a.length?(++r,n.next?e.head=n.next:e.head=e.tail=null):(e.head=n,n.data=a.slice(o));break}++r}return e.length-=r,i}(t,e);return n}(t,e.buffer,e.decoder),i);var i}function B(t){var e=t._readableState;if(e.length>0)throw new Error('"endReadable()" called on non-empty stream');e.endEmitted||(e.ended=!0,r.nextTick(R,e,t))}function R(t,e){t.endEmitted||0!==t.length||(t.endEmitted=!0,e.readable=!1,e.emit("end"))}function O(t,e){for(var i=0,n=t.length;i<n;i++)if(t[i]===e)return i;return-1}y.prototype.read=function(t){l("read",t),t=parseInt(t,10);var e=this._readableState,i=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&(e.length>=e.highWaterMark||e.ended))return l("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?B(this):S(this),null;if(t=x(t,e),0===t&&e.ended)return 0===e.length&&B(this),null;var n,r=e.needReadable;return l("need readable",r),(0===e.length||e.length-t<e.highWaterMark)&&(r=!0,l("length less than watermark",r)),e.ended||e.reading?(r=!1,l("reading or ended",r)):r&&(l("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=x(i,e))),n=t>0?I(t,e):null,null===n?(e.needReadable=!0,t=0):e.length-=t,0===e.length&&(e.ended||(e.needReadable=!0),i!==t&&e.ended&&B(this)),null!==n&&this.emit("data",n),n},y.prototype._read=function(t){this.emit("error",new Error("_read() is not implemented"))},y.prototype.pipe=function(t,e){var i=this,a=this._readableState;switch(a.pipesCount){case 0:a.pipes=t;break;case 1:a.pipes=[a.pipes,t];break;default:a.pipes.push(t);break}a.pipesCount+=1,l("pipe count=%d opts=%j",a.pipesCount,e);var c=(!e||!1!==e.end)&&t!==n.stdout&&t!==n.stderr,f=c?d:w;function u(e,n){l("onunpipe"),e===i&&n&&!1===n.hasUnpiped&&(n.hasUnpiped=!0,function(){l("cleanup"),t.removeListener("close",g),t.removeListener("finish",y),t.removeListener("drain",h),t.removeListener("error",m),t.removeListener("unpipe",u),i.removeListener("end",d),i.removeListener("end",w),i.removeListener("data",v),p=!0,!a.awaitDrain||t._writableState&&!t._writableState.needDrain||h()}())}function d(){l("onend"),t.end()}a.endEmitted?r.nextTick(f):i.once("end",f),t.on("unpipe",u);var h=function(t){return function(){var e=t._readableState;l("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&s(t,"data")&&(e.flowing=!0,P(t))}}(i);t.on("drain",h);var p=!1;var b=!1;function v(e){l("ondata"),b=!1;var n=t.write(e);!1!==n||b||((1===a.pipesCount&&a.pipes===t||a.pipesCount>1&&-1!==O(a.pipes,t))&&!p&&(l("false write response, pause",i._readableState.awaitDrain),i._readableState.awaitDrain++,b=!0),i.pause())}function m(e){l("onerror",e),w(),t.removeListener("error",m),0===s(t,"error")&&t.emit("error",e)}function g(){t.removeListener("finish",y),w()}function y(){l("onfinish"),t.removeListener("close",g),w()}function w(){l("unpipe"),i.unpipe(t)}return i.on("data",v),function(t,e,i){if("function"===typeof t.prependListener)return t.prependListener(e,i);t._events&&t._events[e]?o(t._events[e])?t._events[e].unshift(i):t._events[e]=[i,t._events[e]]:t.on(e,i)}(t,"error",m),t.once("close",g),t.once("finish",y),t.emit("pipe",i),a.flowing||(l("pipe resume"),i.resume()),t},y.prototype.unpipe=function(t){var e=this._readableState,i={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes||(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,i)),this;if(!t){var n=e.pipes,r=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var a=0;a<r;a++)n[a].emit("unpipe",this,i);return this}var o=O(e.pipes,t);return-1===o||(e.pipes.splice(o,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,i)),this},y.prototype.on=function(t,e){var i=c.prototype.on.call(this,t,e);if("data"===t)!1!==this._readableState.flowing&&this.resume();else if("readable"===t){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&S(this):r.nextTick(M,this))}return i},y.prototype.addListener=y.prototype.on,y.prototype.resume=function(){var t=this._readableState;return t.flowing||(l("resume"),t.flowing=!0,function(t,e){e.resumeScheduled||(e.resumeScheduled=!0,r.nextTick(C,t,e))}(this,t)),this},y.prototype.pause=function(){return l("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(l("pause"),this._readableState.flowing=!1,this.emit("pause")),this},y.prototype.wrap=function(t){var e=this,i=this._readableState,n=!1;for(var r in t.on("end",(function(){if(l("wrapped end"),i.decoder&&!i.ended){var t=i.decoder.end();t&&t.length&&e.push(t)}e.push(null)})),t.on("data",(function(r){if(l("wrapped data"),i.decoder&&(r=i.decoder.write(r)),(!i.objectMode||null!==r&&void 0!==r)&&(i.objectMode||r&&r.length)){var a=e.push(r);a||(n=!0,t.pause())}})),t)void 0===this[r]&&"function"===typeof t[r]&&(this[r]=function(e){return function(){return t[e].apply(t,arguments)}}(r));for(var a=0;a<m.length;a++)t.on(m[a],this.emit.bind(this,m[a]));return this._read=function(e){l("wrapped _read",e),n&&(n=!1,t.resume())},this},Object.defineProperty(y.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),y._fromList=I}).call(this,i("c8ba"),i("4362"))},ae84:function(t,e,i){var n=i("8707").Buffer,r=i("f576");t.exports=function(t,e,i,a){if(n.isBuffer(t)||(t=n.from(t,"binary")),e&&(n.isBuffer(e)||(e=n.from(e,"binary")),8!==e.length))throw new RangeError("salt should be Buffer with 8 byte length");var o=i/8,s=n.alloc(o),c=n.alloc(a||0),f=n.alloc(0);while(o>0||a>0){var u=new r;u.update(f),u.update(t),e&&u.update(e),f=u.digest();var d=0;if(o>0){var h=s.length-o;d=Math.min(o,f.length),f.copy(s,h,0,d),o-=d}if(d<f.length&&a>0){var l=c.length-a,p=Math.min(a,f.length-d);f.copy(c,l,d,d+p),a-=p}}return f.fill(0),{key:s,iv:c}}},aea1:function(t,e,i){"use strict";i.r(e);var n=i("90fe"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},b149:function(t,e,i){"use strict";i.r(e);var n=i("9f45"),r=i("d854");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"742677d7",null,!1,n["a"],void 0);e["default"]=s.exports},b19a:function(t,e,i){"use strict";var n=i("966d"),r=Object.keys||function(t){var e=[];for(var i in t)e.push(i);return e};t.exports=d;var a=Object.create(i("3a7c"));a.inherits=i("3fb5");var o=i("ad71"),s=i("dc14");a.inherits(d,o);for(var c=r(s.prototype),f=0;f<c.length;f++){var u=c[f];d.prototype[u]||(d.prototype[u]=s.prototype[u])}function d(t){if(!(this instanceof d))return new d(t);o.call(this,t),s.call(this,t),t&&!1===t.readable&&(this.readable=!1),t&&!1===t.writable&&(this.writable=!1),this.allowHalfOpen=!0,t&&!1===t.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",h)}function h(){this.allowHalfOpen||this._writableState.ended||n.nextTick(l,this)}function l(t){t.end()}Object.defineProperty(d.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(d.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}}),d.prototype._destroy=function(t,e){this.push(null),this.end(),n.nextTick(e,t)}},b2cf:function(t,e,i){"use strict";i.r(e);var n=i("8907"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},b4e8:function(t){t.exports=JSON.parse('{"sha224WithRSAEncryption":{"sign":"rsa","hash":"sha224","id":"302d300d06096086480165030402040500041c"},"RSA-SHA224":{"sign":"ecdsa/rsa","hash":"sha224","id":"302d300d06096086480165030402040500041c"},"sha256WithRSAEncryption":{"sign":"rsa","hash":"sha256","id":"3031300d060960864801650304020105000420"},"RSA-SHA256":{"sign":"ecdsa/rsa","hash":"sha256","id":"3031300d060960864801650304020105000420"},"sha384WithRSAEncryption":{"sign":"rsa","hash":"sha384","id":"3041300d060960864801650304020205000430"},"RSA-SHA384":{"sign":"ecdsa/rsa","hash":"sha384","id":"3041300d060960864801650304020205000430"},"sha512WithRSAEncryption":{"sign":"rsa","hash":"sha512","id":"3051300d060960864801650304020305000440"},"RSA-SHA512":{"sign":"ecdsa/rsa","hash":"sha512","id":"3051300d060960864801650304020305000440"},"RSA-SHA1":{"sign":"rsa","hash":"sha1","id":"3021300906052b0e03021a05000414"},"ecdsa-with-SHA1":{"sign":"ecdsa","hash":"sha1","id":""},"sha256":{"sign":"ecdsa","hash":"sha256","id":""},"sha224":{"sign":"ecdsa","hash":"sha224","id":""},"sha384":{"sign":"ecdsa","hash":"sha384","id":""},"sha512":{"sign":"ecdsa","hash":"sha512","id":""},"DSA-SHA":{"sign":"dsa","hash":"sha1","id":""},"DSA-SHA1":{"sign":"dsa","hash":"sha1","id":""},"DSA":{"sign":"dsa","hash":"sha1","id":""},"DSA-WITH-SHA224":{"sign":"dsa","hash":"sha224","id":""},"DSA-SHA224":{"sign":"dsa","hash":"sha224","id":""},"DSA-WITH-SHA256":{"sign":"dsa","hash":"sha256","id":""},"DSA-SHA256":{"sign":"dsa","hash":"sha256","id":""},"DSA-WITH-SHA384":{"sign":"dsa","hash":"sha384","id":""},"DSA-SHA384":{"sign":"dsa","hash":"sha384","id":""},"DSA-WITH-SHA512":{"sign":"dsa","hash":"sha512","id":""},"DSA-SHA512":{"sign":"dsa","hash":"sha512","id":""},"DSA-RIPEMD160":{"sign":"dsa","hash":"rmd160","id":""},"ripemd160WithRSA":{"sign":"rsa","hash":"rmd160","id":"3021300906052b2403020105000414"},"RSA-RIPEMD160":{"sign":"rsa","hash":"rmd160","id":"3021300906052b2403020105000414"},"md5WithRSAEncryption":{"sign":"rsa","hash":"md5","id":"3020300c06082a864886f70d020505000410"},"RSA-MD5":{"sign":"rsa","hash":"md5","id":"3020300c06082a864886f70d020505000410"}}')},b525:function(t,e,i){"use strict";var n=i("c3c0"),r=i("edc9"),a=i("da3e"),o=n.rotr64_hi,s=n.rotr64_lo,c=n.shr64_hi,f=n.shr64_lo,u=n.sum64,d=n.sum64_hi,h=n.sum64_lo,l=n.sum64_4_hi,p=n.sum64_4_lo,b=n.sum64_5_hi,v=n.sum64_5_lo,m=r.BlockHash,g=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function y(){if(!(this instanceof y))return new y;m.call(this),this.h=[1779033703,4089235720,3144134277,2227873595,1013904242,4271175723,2773480762,1595750129,1359893119,2917565137,2600822924,725511199,528734635,4215389547,1541459225,327033209],this.k=g,this.W=new Array(160)}function w(t,e,i,n,r){var a=t&i^~t&r;return a<0&&(a+=4294967296),a}function _(t,e,i,n,r,a){var o=e&n^~e&a;return o<0&&(o+=4294967296),o}function x(t,e,i,n,r){var a=t&i^t&r^i&r;return a<0&&(a+=4294967296),a}function S(t,e,i,n,r,a){var o=e&n^e&a^n&a;return o<0&&(o+=4294967296),o}function k(t,e){var i=o(t,e,28),n=o(e,t,2),r=o(e,t,7),a=i^n^r;return a<0&&(a+=4294967296),a}function A(t,e){var i=s(t,e,28),n=s(e,t,2),r=s(e,t,7),a=i^n^r;return a<0&&(a+=4294967296),a}function E(t,e){var i=o(t,e,14),n=o(t,e,18),r=o(e,t,9),a=i^n^r;return a<0&&(a+=4294967296),a}function M(t,e){var i=s(t,e,14),n=s(t,e,18),r=s(e,t,9),a=i^n^r;return a<0&&(a+=4294967296),a}function C(t,e){var i=o(t,e,1),n=o(t,e,8),r=c(t,e,7),a=i^n^r;return a<0&&(a+=4294967296),a}function P(t,e){var i=s(t,e,1),n=s(t,e,8),r=f(t,e,7),a=i^n^r;return a<0&&(a+=4294967296),a}function I(t,e){var i=o(t,e,19),n=o(e,t,29),r=c(t,e,6),a=i^n^r;return a<0&&(a+=4294967296),a}function B(t,e){var i=s(t,e,19),n=s(e,t,29),r=f(t,e,6),a=i^n^r;return a<0&&(a+=4294967296),a}n.inherits(y,m),t.exports=y,y.blockSize=1024,y.outSize=512,y.hmacStrength=192,y.padLength=128,y.prototype._prepareBlock=function(t,e){for(var i=this.W,n=0;n<32;n++)i[n]=t[e+n];for(;n<i.length;n+=2){var r=I(i[n-4],i[n-3]),a=B(i[n-4],i[n-3]),o=i[n-14],s=i[n-13],c=C(i[n-30],i[n-29]),f=P(i[n-30],i[n-29]),u=i[n-32],d=i[n-31];i[n]=l(r,a,o,s,c,f,u,d),i[n+1]=p(r,a,o,s,c,f,u,d)}},y.prototype._update=function(t,e){this._prepareBlock(t,e);var i=this.W,n=this.h[0],r=this.h[1],o=this.h[2],s=this.h[3],c=this.h[4],f=this.h[5],l=this.h[6],p=this.h[7],m=this.h[8],g=this.h[9],y=this.h[10],C=this.h[11],P=this.h[12],I=this.h[13],B=this.h[14],R=this.h[15];a(this.k.length===i.length);for(var O=0;O<i.length;O+=2){var T=B,j=R,L=E(m,g),D=M(m,g),N=w(m,g,y,C,P),U=_(m,g,y,C,P,I),z=this.k[O],$=this.k[O+1],q=i[O],F=i[O+1],V=b(T,j,L,D,N,U,z,$,q,F),H=v(T,j,L,D,N,U,z,$,q,F);T=k(n,r),j=A(n,r),L=x(n,r,o,s,c),D=S(n,r,o,s,c,f);var K=d(T,j,L,D),G=h(T,j,L,D);B=P,R=I,P=y,I=C,y=m,C=g,m=d(l,p,V,H),g=h(p,p,V,H),l=c,p=f,c=o,f=s,o=n,s=r,n=d(V,H,K,G),r=h(V,H,K,G)}u(this.h,0,n,r),u(this.h,2,o,s),u(this.h,4,c,f),u(this.h,6,l,p),u(this.h,8,m,g),u(this.h,10,y,C),u(this.h,12,P,I),u(this.h,14,B,R)},y.prototype._digest=function(t){return"hex"===t?n.toHex32(this.h,"big"):n.split32(this.h,"big")}},b5ca:function(t,e,i){"use strict";var n=i("b639").Buffer,r=i("3fb5"),a=i("93e6"),o=new Array(16),s=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],c=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],f=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],u=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11],d=[0,1518500249,1859775393,2400959708,2840853838],h=[1352829926,1548603684,1836072691,2053994217,0];function l(){a.call(this,64),this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878,this._e=3285377520}function p(t,e){return t<<e|t>>>32-e}function b(t,e,i,n,r,a,o,s){return p(t+(e^i^n)+a+o|0,s)+r|0}function v(t,e,i,n,r,a,o,s){return p(t+(e&i|~e&n)+a+o|0,s)+r|0}function m(t,e,i,n,r,a,o,s){return p(t+((e|~i)^n)+a+o|0,s)+r|0}function g(t,e,i,n,r,a,o,s){return p(t+(e&n|i&~n)+a+o|0,s)+r|0}function y(t,e,i,n,r,a,o,s){return p(t+(e^(i|~n))+a+o|0,s)+r|0}r(l,a),l.prototype._update=function(){for(var t=o,e=0;e<16;++e)t[e]=this._block.readInt32LE(4*e);for(var i=0|this._a,n=0|this._b,r=0|this._c,a=0|this._d,l=0|this._e,w=0|this._a,_=0|this._b,x=0|this._c,S=0|this._d,k=0|this._e,A=0;A<80;A+=1){var E,M;A<16?(E=b(i,n,r,a,l,t[s[A]],d[0],f[A]),M=y(w,_,x,S,k,t[c[A]],h[0],u[A])):A<32?(E=v(i,n,r,a,l,t[s[A]],d[1],f[A]),M=g(w,_,x,S,k,t[c[A]],h[1],u[A])):A<48?(E=m(i,n,r,a,l,t[s[A]],d[2],f[A]),M=m(w,_,x,S,k,t[c[A]],h[2],u[A])):A<64?(E=g(i,n,r,a,l,t[s[A]],d[3],f[A]),M=v(w,_,x,S,k,t[c[A]],h[3],u[A])):(E=y(i,n,r,a,l,t[s[A]],d[4],f[A]),M=b(w,_,x,S,k,t[c[A]],h[4],u[A])),i=l,l=a,a=p(r,10),r=n,n=E,w=k,k=S,S=p(x,10),x=_,_=M}var C=this._b+r+S|0;this._b=this._c+a+k|0,this._c=this._d+l+w|0,this._d=this._e+i+_|0,this._e=this._a+n+x|0,this._a=C},l.prototype._digest=function(){this._block[this._blockOffset++]=128,this._blockOffset>56&&(this._block.fill(0,this._blockOffset,64),this._update(),this._blockOffset=0),this._block.fill(0,this._blockOffset,56),this._block.writeUInt32LE(this._length[0],56),this._block.writeUInt32LE(this._length[1],60),this._update();var t=n.alloc?n.alloc(20):new n(20);return t.writeInt32LE(this._a,0),t.writeInt32LE(this._b,4),t.writeInt32LE(this._c,8),t.writeInt32LE(this._d,12),t.writeInt32LE(this._e,16),t},t.exports=l},b639:function(t,e,i){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var n=i("1fb5"),r=i("9152"),a=i("e3db");function o(){return c.TYPED_ARRAY_SUPPORT?**********:**********}function s(t,e){if(o()<e)throw new RangeError("Invalid typed array length");return c.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e),t.__proto__=c.prototype):(null===t&&(t=new c(e)),t.length=e),t}function c(t,e,i){if(!c.TYPED_ARRAY_SUPPORT&&!(this instanceof c))return new c(t,e,i);if("number"===typeof t){if("string"===typeof e)throw new Error("If encoding is specified then the first argument must be a string");return d(this,t)}return f(this,t,e,i)}function f(t,e,i,n){if("number"===typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!==typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,i,n){if(e.byteLength,i<0||e.byteLength<i)throw new RangeError("'offset' is out of bounds");if(e.byteLength<i+(n||0))throw new RangeError("'length' is out of bounds");e=void 0===i&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,i):new Uint8Array(e,i,n);c.TYPED_ARRAY_SUPPORT?(t=e,t.__proto__=c.prototype):t=h(t,e);return t}(t,e,i,n):"string"===typeof e?function(t,e,i){"string"===typeof i&&""!==i||(i="utf8");if(!c.isEncoding(i))throw new TypeError('"encoding" must be a valid string encoding');var n=0|p(e,i);t=s(t,n);var r=t.write(e,i);r!==n&&(t=t.slice(0,r));return t}(t,e,i):function(t,e){if(c.isBuffer(e)){var i=0|l(e.length);return t=s(t,i),0===t.length?t:(e.copy(t,0,0,i),t)}if(e){if("undefined"!==typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!==typeof e.length||function(t){return t!==t}(e.length)?s(t,0):h(t,e);if("Buffer"===e.type&&a(e.data))return h(t,e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function u(t){if("number"!==typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function d(t,e){if(u(e),t=s(t,e<0?0:0|l(e)),!c.TYPED_ARRAY_SUPPORT)for(var i=0;i<e;++i)t[i]=0;return t}function h(t,e){var i=e.length<0?0:0|l(e.length);t=s(t,i);for(var n=0;n<i;n+=1)t[n]=255&e[n];return t}function l(t){if(t>=o())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o().toString(16)+" bytes");return 0|t}function p(t,e){if(c.isBuffer(t))return t.length;if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!==typeof t&&(t=""+t);var i=t.length;if(0===i)return 0;for(var n=!1;;)switch(e){case"ascii":case"latin1":case"binary":return i;case"utf8":case"utf-8":case void 0:return z(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*i;case"hex":return i>>>1;case"base64":return $(t).length;default:if(n)return z(t).length;e=(""+e).toLowerCase(),n=!0}}function b(t,e,i){var n=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===i||i>this.length)&&(i=this.length),i<=0)return"";if(i>>>=0,e>>>=0,i<=e)return"";t||(t="utf8");while(1)switch(t){case"hex":return P(this,e,i);case"utf8":case"utf-8":return E(this,e,i);case"ascii":return M(this,e,i);case"latin1":case"binary":return C(this,e,i);case"base64":return A(this,e,i);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return I(this,e,i);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}function v(t,e,i){var n=t[e];t[e]=t[i],t[i]=n}function m(t,e,i,n,r){if(0===t.length)return-1;if("string"===typeof i?(n=i,i=0):i>**********?i=**********:i<-2147483648&&(i=-2147483648),i=+i,isNaN(i)&&(i=r?0:t.length-1),i<0&&(i=t.length+i),i>=t.length){if(r)return-1;i=t.length-1}else if(i<0){if(!r)return-1;i=0}if("string"===typeof e&&(e=c.from(e,n)),c.isBuffer(e))return 0===e.length?-1:g(t,e,i,n,r);if("number"===typeof e)return e&=255,c.TYPED_ARRAY_SUPPORT&&"function"===typeof Uint8Array.prototype.indexOf?r?Uint8Array.prototype.indexOf.call(t,e,i):Uint8Array.prototype.lastIndexOf.call(t,e,i):g(t,[e],i,n,r);throw new TypeError("val must be string, number or Buffer")}function g(t,e,i,n,r){var a,o=1,s=t.length,c=e.length;if(void 0!==n&&(n=String(n).toLowerCase(),"ucs2"===n||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return-1;o=2,s/=2,c/=2,i/=2}function f(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}if(r){var u=-1;for(a=i;a<s;a++)if(f(t,a)===f(e,-1===u?0:a-u)){if(-1===u&&(u=a),a-u+1===c)return u*o}else-1!==u&&(a-=a-u),u=-1}else for(i+c>s&&(i=s-c),a=i;a>=0;a--){for(var d=!0,h=0;h<c;h++)if(f(t,a+h)!==f(e,h)){d=!1;break}if(d)return a}return-1}function y(t,e,i,n){i=Number(i)||0;var r=t.length-i;n?(n=Number(n),n>r&&(n=r)):n=r;var a=e.length;if(a%2!==0)throw new TypeError("Invalid hex string");n>a/2&&(n=a/2);for(var o=0;o<n;++o){var s=parseInt(e.substr(2*o,2),16);if(isNaN(s))return o;t[i+o]=s}return o}function w(t,e,i,n){return q(z(e,t.length-i),t,i,n)}function _(t,e,i,n){return q(function(t){for(var e=[],i=0;i<t.length;++i)e.push(255&t.charCodeAt(i));return e}(e),t,i,n)}function x(t,e,i,n){return _(t,e,i,n)}function S(t,e,i,n){return q($(e),t,i,n)}function k(t,e,i,n){return q(function(t,e){for(var i,n,r,a=[],o=0;o<t.length;++o){if((e-=2)<0)break;i=t.charCodeAt(o),n=i>>8,r=i%256,a.push(r),a.push(n)}return a}(e,t.length-i),t,i,n)}function A(t,e,i){return 0===e&&i===t.length?n.fromByteArray(t):n.fromByteArray(t.slice(e,i))}function E(t,e,i){i=Math.min(t.length,i);var n=[],r=e;while(r<i){var a,o,s,c,f=t[r],u=null,d=f>239?4:f>223?3:f>191?2:1;if(r+d<=i)switch(d){case 1:f<128&&(u=f);break;case 2:a=t[r+1],128===(192&a)&&(c=(31&f)<<6|63&a,c>127&&(u=c));break;case 3:a=t[r+1],o=t[r+2],128===(192&a)&&128===(192&o)&&(c=(15&f)<<12|(63&a)<<6|63&o,c>2047&&(c<55296||c>57343)&&(u=c));break;case 4:a=t[r+1],o=t[r+2],s=t[r+3],128===(192&a)&&128===(192&o)&&128===(192&s)&&(c=(15&f)<<18|(63&a)<<12|(63&o)<<6|63&s,c>65535&&c<1114112&&(u=c))}null===u?(u=65533,d=1):u>65535&&(u-=65536,n.push(u>>>10&1023|55296),u=56320|1023&u),n.push(u),r+=d}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);var i="",n=0;while(n<e)i+=String.fromCharCode.apply(String,t.slice(n,n+=4096));return i}(n)}e.Buffer=c,e.SlowBuffer=function(t){+t!=t&&(t=0);return c.alloc(+t)},e.INSPECT_MAX_BYTES=50,c.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"===typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(e){return!1}}(),e.kMaxLength=o(),c.poolSize=8192,c._augment=function(t){return t.__proto__=c.prototype,t},c.from=function(t,e,i){return f(null,t,e,i)},c.TYPED_ARRAY_SUPPORT&&(c.prototype.__proto__=Uint8Array.prototype,c.__proto__=Uint8Array,"undefined"!==typeof Symbol&&Symbol.species&&c[Symbol.species]===c&&Object.defineProperty(c,Symbol.species,{value:null,configurable:!0})),c.alloc=function(t,e,i){return function(t,e,i,n){return u(e),e<=0?s(t,e):void 0!==i?"string"===typeof n?s(t,e).fill(i,n):s(t,e).fill(i):s(t,e)}(null,t,e,i)},c.allocUnsafe=function(t){return d(null,t)},c.allocUnsafeSlow=function(t){return d(null,t)},c.isBuffer=function(t){return!(null==t||!t._isBuffer)},c.compare=function(t,e){if(!c.isBuffer(t)||!c.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var i=t.length,n=e.length,r=0,a=Math.min(i,n);r<a;++r)if(t[r]!==e[r]){i=t[r],n=e[r];break}return i<n?-1:n<i?1:0},c.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},c.concat=function(t,e){if(!a(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return c.alloc(0);var i;if(void 0===e)for(e=0,i=0;i<t.length;++i)e+=t[i].length;var n=c.allocUnsafe(e),r=0;for(i=0;i<t.length;++i){var o=t[i];if(!c.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(n,r),r+=o.length}return n},c.byteLength=p,c.prototype._isBuffer=!0,c.prototype.swap16=function(){var t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)v(this,e,e+1);return this},c.prototype.swap32=function(){var t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)v(this,e,e+3),v(this,e+1,e+2);return this},c.prototype.swap64=function(){var t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)v(this,e,e+7),v(this,e+1,e+6),v(this,e+2,e+5),v(this,e+3,e+4);return this},c.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?E(this,0,t):b.apply(this,arguments)},c.prototype.equals=function(t){if(!c.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===c.compare(this,t)},c.prototype.inspect=function(){var t="",i=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,i).match(/.{2}/g).join(" "),this.length>i&&(t+=" ... ")),"<Buffer "+t+">"},c.prototype.compare=function(t,e,i,n,r){if(!c.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===i&&(i=t?t.length:0),void 0===n&&(n=0),void 0===r&&(r=this.length),e<0||i>t.length||n<0||r>this.length)throw new RangeError("out of range index");if(n>=r&&e>=i)return 0;if(n>=r)return-1;if(e>=i)return 1;if(e>>>=0,i>>>=0,n>>>=0,r>>>=0,this===t)return 0;for(var a=r-n,o=i-e,s=Math.min(a,o),f=this.slice(n,r),u=t.slice(e,i),d=0;d<s;++d)if(f[d]!==u[d]){a=f[d],o=u[d];break}return a<o?-1:o<a?1:0},c.prototype.includes=function(t,e,i){return-1!==this.indexOf(t,e,i)},c.prototype.indexOf=function(t,e,i){return m(this,t,e,i,!0)},c.prototype.lastIndexOf=function(t,e,i){return m(this,t,e,i,!1)},c.prototype.write=function(t,e,i,n){if(void 0===e)n="utf8",i=this.length,e=0;else if(void 0===i&&"string"===typeof e)n=e,i=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(i)?(i|=0,void 0===n&&(n="utf8")):(n=i,i=void 0)}var r=this.length-e;if((void 0===i||i>r)&&(i=r),t.length>0&&(i<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var a=!1;;)switch(n){case"hex":return y(this,t,e,i);case"utf8":case"utf-8":return w(this,t,e,i);case"ascii":return _(this,t,e,i);case"latin1":case"binary":return x(this,t,e,i);case"base64":return S(this,t,e,i);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return k(this,t,e,i);default:if(a)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),a=!0}},c.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function M(t,e,i){var n="";i=Math.min(t.length,i);for(var r=e;r<i;++r)n+=String.fromCharCode(127&t[r]);return n}function C(t,e,i){var n="";i=Math.min(t.length,i);for(var r=e;r<i;++r)n+=String.fromCharCode(t[r]);return n}function P(t,e,i){var n=t.length;(!e||e<0)&&(e=0),(!i||i<0||i>n)&&(i=n);for(var r="",a=e;a<i;++a)r+=U(t[a]);return r}function I(t,e,i){for(var n=t.slice(e,i),r="",a=0;a<n.length;a+=2)r+=String.fromCharCode(n[a]+256*n[a+1]);return r}function B(t,e,i){if(t%1!==0||t<0)throw new RangeError("offset is not uint");if(t+e>i)throw new RangeError("Trying to access beyond buffer length")}function R(t,e,i,n,r,a){if(!c.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>r||e<a)throw new RangeError('"value" argument is out of bounds');if(i+n>t.length)throw new RangeError("Index out of range")}function O(t,e,i,n){e<0&&(e=65535+e+1);for(var r=0,a=Math.min(t.length-i,2);r<a;++r)t[i+r]=(e&255<<8*(n?r:1-r))>>>8*(n?r:1-r)}function T(t,e,i,n){e<0&&(e=4294967295+e+1);for(var r=0,a=Math.min(t.length-i,4);r<a;++r)t[i+r]=e>>>8*(n?r:3-r)&255}function j(t,e,i,n,r,a){if(i+n>t.length)throw new RangeError("Index out of range");if(i<0)throw new RangeError("Index out of range")}function L(t,e,i,n,a){return a||j(t,0,i,4),r.write(t,e,i,n,23,4),i+4}function D(t,e,i,n,a){return a||j(t,0,i,8),r.write(t,e,i,n,52,8),i+8}c.prototype.slice=function(t,e){var i,n=this.length;if(t=~~t,e=void 0===e?n:~~e,t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),e<t&&(e=t),c.TYPED_ARRAY_SUPPORT)i=this.subarray(t,e),i.__proto__=c.prototype;else{var r=e-t;i=new c(r,void 0);for(var a=0;a<r;++a)i[a]=this[a+t]}return i},c.prototype.readUIntLE=function(t,e,i){t|=0,e|=0,i||B(t,e,this.length);var n=this[t],r=1,a=0;while(++a<e&&(r*=256))n+=this[t+a]*r;return n},c.prototype.readUIntBE=function(t,e,i){t|=0,e|=0,i||B(t,e,this.length);var n=this[t+--e],r=1;while(e>0&&(r*=256))n+=this[t+--e]*r;return n},c.prototype.readUInt8=function(t,e){return e||B(t,1,this.length),this[t]},c.prototype.readUInt16LE=function(t,e){return e||B(t,2,this.length),this[t]|this[t+1]<<8},c.prototype.readUInt16BE=function(t,e){return e||B(t,2,this.length),this[t]<<8|this[t+1]},c.prototype.readUInt32LE=function(t,e){return e||B(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},c.prototype.readUInt32BE=function(t,e){return e||B(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},c.prototype.readIntLE=function(t,e,i){t|=0,e|=0,i||B(t,e,this.length);var n=this[t],r=1,a=0;while(++a<e&&(r*=256))n+=this[t+a]*r;return r*=128,n>=r&&(n-=Math.pow(2,8*e)),n},c.prototype.readIntBE=function(t,e,i){t|=0,e|=0,i||B(t,e,this.length);var n=e,r=1,a=this[t+--n];while(n>0&&(r*=256))a+=this[t+--n]*r;return r*=128,a>=r&&(a-=Math.pow(2,8*e)),a},c.prototype.readInt8=function(t,e){return e||B(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},c.prototype.readInt16LE=function(t,e){e||B(t,2,this.length);var i=this[t]|this[t+1]<<8;return 32768&i?4294901760|i:i},c.prototype.readInt16BE=function(t,e){e||B(t,2,this.length);var i=this[t+1]|this[t]<<8;return 32768&i?4294901760|i:i},c.prototype.readInt32LE=function(t,e){return e||B(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},c.prototype.readInt32BE=function(t,e){return e||B(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},c.prototype.readFloatLE=function(t,e){return e||B(t,4,this.length),r.read(this,t,!0,23,4)},c.prototype.readFloatBE=function(t,e){return e||B(t,4,this.length),r.read(this,t,!1,23,4)},c.prototype.readDoubleLE=function(t,e){return e||B(t,8,this.length),r.read(this,t,!0,52,8)},c.prototype.readDoubleBE=function(t,e){return e||B(t,8,this.length),r.read(this,t,!1,52,8)},c.prototype.writeUIntLE=function(t,e,i,n){if(t=+t,e|=0,i|=0,!n){var r=Math.pow(2,8*i)-1;R(this,t,e,i,r,0)}var a=1,o=0;this[e]=255&t;while(++o<i&&(a*=256))this[e+o]=t/a&255;return e+i},c.prototype.writeUIntBE=function(t,e,i,n){if(t=+t,e|=0,i|=0,!n){var r=Math.pow(2,8*i)-1;R(this,t,e,i,r,0)}var a=i-1,o=1;this[e+a]=255&t;while(--a>=0&&(o*=256))this[e+a]=t/o&255;return e+i},c.prototype.writeUInt8=function(t,e,i){return t=+t,e|=0,i||R(this,t,e,1,255,0),c.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},c.prototype.writeUInt16LE=function(t,e,i){return t=+t,e|=0,i||R(this,t,e,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):O(this,t,e,!0),e+2},c.prototype.writeUInt16BE=function(t,e,i){return t=+t,e|=0,i||R(this,t,e,2,65535,0),c.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):O(this,t,e,!1),e+2},c.prototype.writeUInt32LE=function(t,e,i){return t=+t,e|=0,i||R(this,t,e,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):T(this,t,e,!0),e+4},c.prototype.writeUInt32BE=function(t,e,i){return t=+t,e|=0,i||R(this,t,e,4,4294967295,0),c.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):T(this,t,e,!1),e+4},c.prototype.writeIntLE=function(t,e,i,n){if(t=+t,e|=0,!n){var r=Math.pow(2,8*i-1);R(this,t,e,i,r-1,-r)}var a=0,o=1,s=0;this[e]=255&t;while(++a<i&&(o*=256))t<0&&0===s&&0!==this[e+a-1]&&(s=1),this[e+a]=(t/o>>0)-s&255;return e+i},c.prototype.writeIntBE=function(t,e,i,n){if(t=+t,e|=0,!n){var r=Math.pow(2,8*i-1);R(this,t,e,i,r-1,-r)}var a=i-1,o=1,s=0;this[e+a]=255&t;while(--a>=0&&(o*=256))t<0&&0===s&&0!==this[e+a+1]&&(s=1),this[e+a]=(t/o>>0)-s&255;return e+i},c.prototype.writeInt8=function(t,e,i){return t=+t,e|=0,i||R(this,t,e,1,127,-128),c.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},c.prototype.writeInt16LE=function(t,e,i){return t=+t,e|=0,i||R(this,t,e,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):O(this,t,e,!0),e+2},c.prototype.writeInt16BE=function(t,e,i){return t=+t,e|=0,i||R(this,t,e,2,32767,-32768),c.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):O(this,t,e,!1),e+2},c.prototype.writeInt32LE=function(t,e,i){return t=+t,e|=0,i||R(this,t,e,4,**********,-2147483648),c.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):T(this,t,e,!0),e+4},c.prototype.writeInt32BE=function(t,e,i){return t=+t,e|=0,i||R(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),c.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):T(this,t,e,!1),e+4},c.prototype.writeFloatLE=function(t,e,i){return L(this,t,e,!0,i)},c.prototype.writeFloatBE=function(t,e,i){return L(this,t,e,!1,i)},c.prototype.writeDoubleLE=function(t,e,i){return D(this,t,e,!0,i)},c.prototype.writeDoubleBE=function(t,e,i){return D(this,t,e,!1,i)},c.prototype.copy=function(t,e,i,n){if(i||(i=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<i&&(n=i),n===i)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(i<0||i>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-i&&(n=t.length-e+i);var r,a=n-i;if(this===t&&i<e&&e<n)for(r=a-1;r>=0;--r)t[r+e]=this[r+i];else if(a<1e3||!c.TYPED_ARRAY_SUPPORT)for(r=0;r<a;++r)t[r+e]=this[r+i];else Uint8Array.prototype.set.call(t,this.subarray(i,i+a),e);return a},c.prototype.fill=function(t,e,i,n){if("string"===typeof t){if("string"===typeof e?(n=e,e=0,i=this.length):"string"===typeof i&&(n=i,i=this.length),1===t.length){var r=t.charCodeAt(0);r<256&&(t=r)}if(void 0!==n&&"string"!==typeof n)throw new TypeError("encoding must be a string");if("string"===typeof n&&!c.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"===typeof t&&(t&=255);if(e<0||this.length<e||this.length<i)throw new RangeError("Out of range index");if(i<=e)return this;var a;if(e>>>=0,i=void 0===i?this.length:i>>>0,t||(t=0),"number"===typeof t)for(a=e;a<i;++a)this[a]=t;else{var o=c.isBuffer(t)?t:z(new c(t,n).toString()),s=o.length;for(a=0;a<i-e;++a)this[a+e]=o[a%s]}return this};var N=/[^+\/0-9A-Za-z-_]/g;function U(t){return t<16?"0"+t.toString(16):t.toString(16)}function z(t,e){var i;e=e||1/0;for(var n=t.length,r=null,a=[],o=0;o<n;++o){if(i=t.charCodeAt(o),i>55295&&i<57344){if(!r){if(i>56319){(e-=3)>-1&&a.push(239,191,189);continue}if(o+1===n){(e-=3)>-1&&a.push(239,191,189);continue}r=i;continue}if(i<56320){(e-=3)>-1&&a.push(239,191,189),r=i;continue}i=65536+(r-55296<<10|i-56320)}else r&&(e-=3)>-1&&a.push(239,191,189);if(r=null,i<128){if((e-=1)<0)break;a.push(i)}else if(i<2048){if((e-=2)<0)break;a.push(i>>6|192,63&i|128)}else if(i<65536){if((e-=3)<0)break;a.push(i>>12|224,i>>6&63|128,63&i|128)}else{if(!(i<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;a.push(i>>18|240,i>>12&63|128,i>>6&63|128,63&i|128)}}return a}function $(t){return n.toByteArray(function(t){if(t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(N,""),t.length<2)return"";while(t.length%4!==0)t+="=";return t}(t))}function q(t,e,i,n){for(var r=0;r<n;++r){if(r+i>=e.length||r>=t.length)break;e[r+i]=t[r]}return r}}).call(this,i("c8ba"))},b672:function(t,e,i){var n=i("8707").Buffer;function r(t,e){this._block=n.alloc(t),this._finalSize=e,this._blockSize=t,this._len=0}r.prototype.update=function(t,e){"string"===typeof t&&(e=e||"utf8",t=n.from(t,e));for(var i=this._block,r=this._blockSize,a=t.length,o=this._len,s=0;s<a;){for(var c=o%r,f=Math.min(a-s,r-c),u=0;u<f;u++)i[c+u]=t[s+u];o+=f,s+=f,o%r===0&&this._update(i)}return this._len+=a,this},r.prototype.digest=function(t){var e=this._len%this._blockSize;this._block[e]=128,this._block.fill(0,e+1),e>=this._finalSize&&(this._update(this._block),this._block.fill(0));var i=8*this._len;if(i<=4294967295)this._block.writeUInt32BE(i,this._blockSize-4);else{var n=(4294967295&i)>>>0,r=(i-n)/4294967296;this._block.writeUInt32BE(r,this._blockSize-8),this._block.writeUInt32BE(n,this._blockSize-4)}this._update(this._block);var a=this._hash();return t?a.toString(t):a},r.prototype._update=function(){throw new Error("_update must be implemented by subclass")},t.exports=r},b692:function(t,e,i){var n=i("8707").Buffer,r=i("98e6"),a=i("e372"),o=i("3fb5"),s=i("6fe7"),c=i("980c"),f=i("b4e8");function u(t){a.Writable.call(this);var e=f[t];if(!e)throw new Error("Unknown message digest");this._hashType=e.hash,this._hash=r(e.hash),this._tag=e.id,this._signType=e.sign}function d(t){a.Writable.call(this);var e=f[t];if(!e)throw new Error("Unknown message digest");this._hash=r(e.hash),this._tag=e.id,this._signType=e.sign}function h(t){return new u(t)}function l(t){return new d(t)}Object.keys(f).forEach((function(t){f[t].id=n.from(f[t].id,"hex"),f[t.toLowerCase()]=f[t]})),o(u,a.Writable),u.prototype._write=function(t,e,i){this._hash.update(t),i()},u.prototype.update=function(t,e){return"string"===typeof t&&(t=n.from(t,e)),this._hash.update(t),this},u.prototype.sign=function(t,e){this.end();var i=this._hash.digest(),n=s(i,t,this._hashType,this._signType,this._tag);return e?n.toString(e):n},o(d,a.Writable),d.prototype._write=function(t,e,i){this._hash.update(t),i()},d.prototype.update=function(t,e){return"string"===typeof t&&(t=n.from(t,e)),this._hash.update(t),this},d.prototype.verify=function(t,e,i){"string"===typeof e&&(e=n.from(e,i)),this.end();var r=this._hash.digest();return c(e,r,t,this._signType,this._tag)},t.exports={Sign:h,Verify:l,createSign:h,createVerify:l}},b73f:function(t,e,i){"use strict";var n=i("399f"),r=i("f3a3"),a=r.assert;function o(t,e){if(t instanceof o)return t;this._importDER(t,e)||(a(t.r&&t.s,"Signature without r or s"),this.r=new n(t.r,16),this.s=new n(t.s,16),void 0===t.recoveryParam?this.recoveryParam=null:this.recoveryParam=t.recoveryParam)}function s(){this.place=0}function c(t,e){var i=t[e.place++];if(!(128&i))return i;var n=15&i;if(0===n||n>4)return!1;for(var r=0,a=0,o=e.place;a<n;a++,o++)r<<=8,r|=t[o],r>>>=0;return!(r<=127)&&(e.place=o,r)}function f(t){var e=0,i=t.length-1;while(!t[e]&&!(128&t[e+1])&&e<i)e++;return 0===e?t:t.slice(e)}function u(t,e){if(e<128)t.push(e);else{var i=1+(Math.log(e)/Math.LN2>>>3);t.push(128|i);while(--i)t.push(e>>>(i<<3)&255);t.push(e)}}t.exports=o,o.prototype._importDER=function(t,e){t=r.toArray(t,e);var i=new s;if(48!==t[i.place++])return!1;var a=c(t,i);if(!1===a)return!1;if(a+i.place!==t.length)return!1;if(2!==t[i.place++])return!1;var o=c(t,i);if(!1===o)return!1;var f=t.slice(i.place,o+i.place);if(i.place+=o,2!==t[i.place++])return!1;var u=c(t,i);if(!1===u)return!1;if(t.length!==u+i.place)return!1;var d=t.slice(i.place,u+i.place);if(0===f[0]){if(!(128&f[1]))return!1;f=f.slice(1)}if(0===d[0]){if(!(128&d[1]))return!1;d=d.slice(1)}return this.r=new n(f),this.s=new n(d),this.recoveryParam=null,!0},o.prototype.toDER=function(t){var e=this.r.toArray(),i=this.s.toArray();128&e[0]&&(e=[0].concat(e)),128&i[0]&&(i=[0].concat(i)),e=f(e),i=f(i);while(!i[0]&&!(128&i[1]))i=i.slice(1);var n=[2];u(n,e.length),n=n.concat(e),n.push(2),u(n,i.length);var a=n.concat(i),o=[48];return u(o,a.length),o=o.concat(a),r.encode(o,t)}},b7d1:function(t,e,i){(function(e){function i(t){try{if(!e.localStorage)return!1}catch(n){return!1}var i=e.localStorage[t];return null!=i&&"true"===String(i).toLowerCase()}t.exports=function(t,e){if(i("noDeprecation"))return t;var n=!1;return function(){if(!n){if(i("throwDeprecation"))throw new Error(e);i("traceDeprecation")?console.trace(e):console.warn(e),n=!0}return t.apply(this,arguments)}}}).call(this,i("c8ba"))},b837:function(t,e,i){var n=i("3fb5"),r=i("4fd1"),a=i("b672"),o=i("8707").Buffer,s=new Array(160);function c(){this.init(),this._w=s,a.call(this,128,112)}n(c,r),c.prototype.init=function(){return this._ah=3418070365,this._bh=1654270250,this._ch=2438529370,this._dh=355462360,this._eh=1731405415,this._fh=2394180231,this._gh=3675008525,this._hh=1203062813,this._al=3238371032,this._bl=914150663,this._cl=812702999,this._dl=4144912697,this._el=4290775857,this._fl=1750603025,this._gl=1694076839,this._hl=3204075428,this},c.prototype._hash=function(){var t=o.allocUnsafe(48);function e(e,i,n){t.writeInt32BE(e,n),t.writeInt32BE(i,n+4)}return e(this._ah,this._al,0),e(this._bh,this._bl,8),e(this._ch,this._cl,16),e(this._dh,this._dl,24),e(this._eh,this._el,32),e(this._fh,this._fl,40),t},t.exports=c},b949:function(t,e,i){"use strict";i.r(e);var n=i("424f"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},b9a8:function(t,e,i){"use strict";var n=i("399f"),r=i("6aa2"),a=i("f3a3"),o=i("0cbb"),s=i("fdac"),c=a.assert,f=i("bb34"),u=i("b73f");function d(t){if(!(this instanceof d))return new d(t);"string"===typeof t&&(c(Object.prototype.hasOwnProperty.call(o,t),"Unknown curve "+t),t=o[t]),t instanceof o.PresetCurve&&(t={curve:t}),this.curve=t.curve.curve,this.n=this.curve.n,this.nh=this.n.ushrn(1),this.g=this.curve.g,this.g=t.curve.g,this.g.precompute(t.curve.n.bitLength()+1),this.hash=t.hash||t.curve.hash}t.exports=d,d.prototype.keyPair=function(t){return new f(this,t)},d.prototype.keyFromPrivate=function(t,e){return f.fromPrivate(this,t,e)},d.prototype.keyFromPublic=function(t,e){return f.fromPublic(this,t,e)},d.prototype.genKeyPair=function(t){t||(t={});for(var e=new r({hash:this.hash,pers:t.pers,persEnc:t.persEnc||"utf8",entropy:t.entropy||s(this.hash.hmacStrength),entropyEnc:t.entropy&&t.entropyEnc||"utf8",nonce:this.n.toArray()}),i=this.n.byteLength(),a=this.n.sub(new n(2));;){var o=new n(e.generate(i));if(!(o.cmp(a)>0))return o.iaddn(1),this.keyFromPrivate(o)}},d.prototype._truncateToN=function(t,e){var i=8*t.byteLength()-this.n.bitLength();return i>0&&(t=t.ushrn(i)),!e&&t.cmp(this.n)>=0?t.sub(this.n):t},d.prototype.sign=function(t,e,i,a){"object"===typeof i&&(a=i,i=null),a||(a={}),e=this.keyFromPrivate(e,i),t=this._truncateToN(new n(t,16));for(var o=this.n.byteLength(),s=e.getPrivate().toArray("be",o),c=t.toArray("be",o),f=new r({hash:this.hash,entropy:s,nonce:c,pers:a.pers,persEnc:a.persEnc||"utf8"}),d=this.n.sub(new n(1)),h=0;;h++){var l=a.k?a.k(h):new n(f.generate(this.n.byteLength()));if(l=this._truncateToN(l,!0),!(l.cmpn(1)<=0||l.cmp(d)>=0)){var p=this.g.mul(l);if(!p.isInfinity()){var b=p.getX(),v=b.umod(this.n);if(0!==v.cmpn(0)){var m=l.invm(this.n).mul(v.mul(e.getPrivate()).iadd(t));if(m=m.umod(this.n),0!==m.cmpn(0)){var g=(p.getY().isOdd()?1:0)|(0!==b.cmp(v)?2:0);return a.canonical&&m.cmp(this.nh)>0&&(m=this.n.sub(m),g^=1),new u({r:v,s:m,recoveryParam:g})}}}}}},d.prototype.verify=function(t,e,i,r){t=this._truncateToN(new n(t,16)),i=this.keyFromPublic(i,r),e=new u(e,"hex");var a=e.r,o=e.s;if(a.cmpn(1)<0||a.cmp(this.n)>=0)return!1;if(o.cmpn(1)<0||o.cmp(this.n)>=0)return!1;var s,c=o.invm(this.n),f=c.mul(t).umod(this.n),d=c.mul(a).umod(this.n);return this.curve._maxwellTrick?(s=this.g.jmulAdd(f,i.getPublic(),d),!s.isInfinity()&&s.eqXToP(a)):(s=this.g.mulAdd(f,i.getPublic(),d),!s.isInfinity()&&0===s.getX().umod(this.n).cmp(a))},d.prototype.recoverPubKey=function(t,e,i,r){c((3&i)===i,"The recovery param is more than two bits"),e=new u(e,r);var a=this.n,o=new n(t),s=e.r,f=e.s,d=1&i,h=i>>1;if(s.cmp(this.curve.p.umod(this.curve.n))>=0&&h)throw new Error("Unable to find sencond key candinate");s=h?this.curve.pointFromX(s.add(this.curve.n),d):this.curve.pointFromX(s,d);var l=e.r.invm(a),p=a.sub(o).mul(l).umod(a),b=f.mul(l).umod(a);return this.g.mulAdd(p,s,b)},d.prototype.getKeyRecoveryParam=function(t,e,i,n){if(e=new u(e,n),null!==e.recoveryParam)return e.recoveryParam;for(var r=0;r<4;r++){var a;try{a=this.recoverPubKey(t,e,r)}catch(t){continue}if(a.eq(i))return r}throw new Error("Unable to find valid recovery factor")}},bac2:function(t,e,i){var n={ECB:i("0145"),CBC:i("c119"),CFB:i("3505"),CFB8:i("62c9"),CFB1:i("5239"),OFB:i("5165"),CTR:i("6ade"),GCM:i("6ade")},r=i("e85f");for(var a in r)r[a].module=n[r[a].mode];t.exports=r},bb34:function(t,e,i){"use strict";var n=i("399f"),r=i("f3a3"),a=r.assert;function o(t,e){this.ec=t,this.priv=null,this.pub=null,e.priv&&this._importPrivate(e.priv,e.privEnc),e.pub&&this._importPublic(e.pub,e.pubEnc)}t.exports=o,o.fromPublic=function(t,e,i){return e instanceof o?e:new o(t,{pub:e,pubEnc:i})},o.fromPrivate=function(t,e,i){return e instanceof o?e:new o(t,{priv:e,privEnc:i})},o.prototype.validate=function(){var t=this.getPublic();return t.isInfinity()?{result:!1,reason:"Invalid public key"}:t.validate()?t.mul(this.ec.curve.n).isInfinity()?{result:!0,reason:null}:{result:!1,reason:"Public key * N != O"}:{result:!1,reason:"Public key is not a point"}},o.prototype.getPublic=function(t,e){return"string"===typeof t&&(e=t,t=null),this.pub||(this.pub=this.ec.g.mul(this.priv)),e?this.pub.encode(e,t):this.pub},o.prototype.getPrivate=function(t){return"hex"===t?this.priv.toString(16,2):this.priv},o.prototype._importPrivate=function(t,e){this.priv=new n(t,e||16),this.priv=this.priv.umod(this.ec.curve.n)},o.prototype._importPublic=function(t,e){if(t.x||t.y)return"mont"===this.ec.curve.type?a(t.x,"Need x coordinate"):"short"!==this.ec.curve.type&&"edwards"!==this.ec.curve.type||a(t.x&&t.y,"Need both x and y coordinate"),void(this.pub=this.ec.curve.point(t.x,t.y));this.pub=this.ec.curve.decodePoint(t,e)},o.prototype.derive=function(t){return t.validate()||a(t.validate(),"public point not validated"),t.mul(this.priv).getX()},o.prototype.sign=function(t,e,i){return this.ec.sign(t,this,e,i)},o.prototype.verify=function(t,e){return this.ec.verify(t,e,this)},o.prototype.inspect=function(){return"<Key priv: "+(this.priv&&this.priv.toString(16,2))+" pub: "+(this.pub&&this.pub.inspect())+" >"}},bb44:function(t,e,i){"use strict";var n=i("c3c0"),r=i("edc9"),a=n.rotl32,o=n.sum32,s=n.sum32_3,c=n.sum32_4,f=r.BlockHash;function u(){if(!(this instanceof u))return new u;f.call(this),this.h=[1732584193,4023233417,2562383102,271733878,3285377520],this.endian="little"}function d(t,e,i,n){return t<=15?e^i^n:t<=31?e&i|~e&n:t<=47?(e|~i)^n:t<=63?e&n|i&~n:e^(i|~n)}function h(t){return t<=15?0:t<=31?1518500249:t<=47?1859775393:t<=63?2400959708:2840853838}function l(t){return t<=15?1352829926:t<=31?1548603684:t<=47?1836072691:t<=63?2053994217:0}n.inherits(u,f),e.ripemd160=u,u.blockSize=512,u.outSize=160,u.hmacStrength=192,u.padLength=64,u.prototype._update=function(t,e){for(var i=this.h[0],n=this.h[1],r=this.h[2],f=this.h[3],u=this.h[4],g=i,y=n,w=r,_=f,x=u,S=0;S<80;S++){var k=o(a(c(i,d(S,n,r,f),t[p[S]+e],h(S)),v[S]),u);i=u,u=f,f=a(r,10),r=n,n=k,k=o(a(c(g,d(79-S,y,w,_),t[b[S]+e],l(S)),m[S]),x),g=x,x=_,_=a(w,10),w=y,y=k}k=s(this.h[1],r,_),this.h[1]=s(this.h[2],f,x),this.h[2]=s(this.h[3],u,g),this.h[3]=s(this.h[4],i,y),this.h[4]=s(this.h[0],n,w),this.h[0]=k},u.prototype._digest=function(t){return"hex"===t?n.toHex32(this.h,"little"):n.split32(this.h,"little")};var p=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],b=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],v=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],m=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]},bd9d:function(t,e){t.exports=function(t){var e,i=t.length;while(i--){if(e=t.readUInt8(i),255!==e){e++,t.writeUInt8(e,i);break}t.writeUInt8(0,i)}}},bfb8:function(t,e,i){var n=i("3dbb");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("7cf101e2",n,!0,{sourceMap:!1,shadowMode:!1})},c119:function(t,e,i){var n=i("8c8a");e.encrypt=function(t,e){var i=n(e,t._prev);return t._prev=t._cipher.encryptBlock(i),t._prev},e.decrypt=function(t,e){var i=t._prev;t._prev=e;var r=t._cipher.decryptBlock(e);return n(r,i)}},c236:function(t,e,i){"use strict";i.r(e);var n=i("8e2b"),r=i("7bc2");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"2bb44918",null,!1,n["a"],void 0);e["default"]=s.exports},c24d:function(t){t.exports=JSON.parse('{"modp1":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a63a3620ffffffffffffffff"},"modp2":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece65381ffffffffffffffff"},"modp5":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca237327ffffffffffffffff"},"modp14":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aacaa68ffffffffffffffff"},"modp15":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aaac42dad33170d04507a33a85521abdf1cba64ecfb850458dbef0a8aea71575d060c7db3970f85a6e1e4c7abf5ae8cdb0933d71e8c94e04a25619dcee3d2261ad2ee6bf12ffa06d98a0864d87602733ec86a64521f2b18177b200cbbe117577a615d6c770988c0bad946e208e24fa074e5ab3143db5bfce0fd108e4b82d120a93ad2caffffffffffffffff"},"modp16":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aaac42dad33170d04507a33a85521abdf1cba64ecfb850458dbef0a8aea71575d060c7db3970f85a6e1e4c7abf5ae8cdb0933d71e8c94e04a25619dcee3d2261ad2ee6bf12ffa06d98a0864d87602733ec86a64521f2b18177b200cbbe117577a615d6c770988c0bad946e208e24fa074e5ab3143db5bfce0fd108e4b82d120a92108011a723c12a787e6d788719a10bdba5b2699c327186af4e23c1a946834b6150bda2583e9ca2ad44ce8dbbbc2db04de8ef92e8efc141fbecaa6287c59474e6bc05d99b2964fa090c3a2233ba186515be7ed1f612970cee2d7afb81bdd762170481cd0069127d5b05aa993b4ea988d8fddc186ffb7dc90a6c08f4df435c934063199ffffffffffffffff"},"modp17":{"gen":"02","prime":"ffffffffffffffffc90fdaa22168c234c4c6628b80dc1cd129024e088a67cc74020bbea63b139b22514a08798e3404ddef9519b3cd3a431b302b0a6df25f14374fe1356d6d51c245e485b576625e7ec6f44c42e9a637ed6b0bff5cb6f406b7edee386bfb5a899fa5ae9f24117c4b1fe649286651ece45b3dc2007cb8a163bf0598da48361c55d39a69163fa8fd24cf5f83655d23dca3ad961c62f356208552bb9ed529077096966d670c354e4abc9804f1746c08ca18217c32905e462e36ce3be39e772c180e86039b2783a2ec07a28fb5c55df06f4c52c9de2bcbf6955817183995497cea956ae515d2261898fa051015728e5a8aaac42dad33170d04507a33a85521abdf1cba64ecfb850458dbef0a8aea71575d060c7db3970f85a6e1e4c7abf5ae8cdb0933d71e8c94e04a25619dcee3d2261ad2ee6bf12ffa06d98a0864d87602733ec86a64521f2b18177b200cbbe117577a615d6c770988c0bad946e208e24fa074e5ab3143db5bfce0fd108e4b82d120a92108011a723c12a787e6d788719a10bdba5b2699c327186af4e23c1a946834b6150bda2583e9ca2ad44ce8dbbbc2db04de8ef92e8efc141fbecaa6287c59474e6bc05d99b2964fa090c3a2233ba186515be7ed1f612970cee2d7afb81bdd762170481cd0069127d5b05aa993b4ea988d8fddc186ffb7dc90a6c08f4df435c93402849236c3fab4d27c7026c1d4dcb2602646dec9751e763dba37bdf8ff9406ad9e530ee5db382f413001aeb06a53ed9027d831179727b0865a8918da3edbebcf9b14ed44ce6cbaced4bb1bdb7f1447e6cc254b332051512bd7af426fb8f401378cd2bf5983ca01c64b92ecf032ea15d1721d03f482d7ce6e74fef6d55e702f46980c82b5a84031900b1c9e59e7c97fbec7e8f323a97a7e36cc88be0f1d45b7ff585ac54bd407b22b4154aacc8f6d7ebf48e1d814cc5ed20f8037e0a79715eef29be32806a1d58bb7c5da76f550aa3d8a1fbff0eb19ccb1a313d55cda56c9ec2ef29632387fe8d76e3c0468043e8f663f4860ee12bf2d5b0b7474d6e694f91e6dcc4024ffffffffffffffff"},"modp18":{"gen":"02","prime":"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"}}')},c2ae:function(t,e,i){t.exports=i("e372").PassThrough},c3c0:function(t,e,i){"use strict";var n=i("da3e"),r=i("3fb5");function a(t,e){return 55296===(64512&t.charCodeAt(e))&&(!(e<0||e+1>=t.length)&&56320===(64512&t.charCodeAt(e+1)))}function o(t){var e=t>>>24|t>>>8&65280|t<<8&16711680|(255&t)<<24;return e>>>0}function s(t){return 1===t.length?"0"+t:t}function c(t){return 7===t.length?"0"+t:6===t.length?"00"+t:5===t.length?"000"+t:4===t.length?"0000"+t:3===t.length?"00000"+t:2===t.length?"000000"+t:1===t.length?"0000000"+t:t}e.inherits=r,e.toArray=function(t,e){if(Array.isArray(t))return t.slice();if(!t)return[];var i=[];if("string"===typeof t)if(e){if("hex"===e)for(t=t.replace(/[^a-z0-9]+/gi,""),t.length%2!==0&&(t="0"+t),r=0;r<t.length;r+=2)i.push(parseInt(t[r]+t[r+1],16))}else for(var n=0,r=0;r<t.length;r++){var o=t.charCodeAt(r);o<128?i[n++]=o:o<2048?(i[n++]=o>>6|192,i[n++]=63&o|128):a(t,r)?(o=65536+((1023&o)<<10)+(1023&t.charCodeAt(++r)),i[n++]=o>>18|240,i[n++]=o>>12&63|128,i[n++]=o>>6&63|128,i[n++]=63&o|128):(i[n++]=o>>12|224,i[n++]=o>>6&63|128,i[n++]=63&o|128)}else for(r=0;r<t.length;r++)i[r]=0|t[r];return i},e.toHex=function(t){for(var e="",i=0;i<t.length;i++)e+=s(t[i].toString(16));return e},e.htonl=o,e.toHex32=function(t,e){for(var i="",n=0;n<t.length;n++){var r=t[n];"little"===e&&(r=o(r)),i+=c(r.toString(16))}return i},e.zero2=s,e.zero8=c,e.join32=function(t,e,i,r){var a=i-e;n(a%4===0);for(var o=new Array(a/4),s=0,c=e;s<o.length;s++,c+=4){var f;f="big"===r?t[c]<<24|t[c+1]<<16|t[c+2]<<8|t[c+3]:t[c+3]<<24|t[c+2]<<16|t[c+1]<<8|t[c],o[s]=f>>>0}return o},e.split32=function(t,e){for(var i=new Array(4*t.length),n=0,r=0;n<t.length;n++,r+=4){var a=t[n];"big"===e?(i[r]=a>>>24,i[r+1]=a>>>16&255,i[r+2]=a>>>8&255,i[r+3]=255&a):(i[r+3]=a>>>24,i[r+2]=a>>>16&255,i[r+1]=a>>>8&255,i[r]=255&a)}return i},e.rotr32=function(t,e){return t>>>e|t<<32-e},e.rotl32=function(t,e){return t<<e|t>>>32-e},e.sum32=function(t,e){return t+e>>>0},e.sum32_3=function(t,e,i){return t+e+i>>>0},e.sum32_4=function(t,e,i,n){return t+e+i+n>>>0},e.sum32_5=function(t,e,i,n,r){return t+e+i+n+r>>>0},e.sum64=function(t,e,i,n){var r=t[e],a=t[e+1],o=n+a>>>0,s=(o<n?1:0)+i+r;t[e]=s>>>0,t[e+1]=o},e.sum64_hi=function(t,e,i,n){var r=e+n>>>0,a=(r<e?1:0)+t+i;return a>>>0},e.sum64_lo=function(t,e,i,n){var r=e+n;return r>>>0},e.sum64_4_hi=function(t,e,i,n,r,a,o,s){var c=0,f=e;f=f+n>>>0,c+=f<e?1:0,f=f+a>>>0,c+=f<a?1:0,f=f+s>>>0,c+=f<s?1:0;var u=t+i+r+o+c;return u>>>0},e.sum64_4_lo=function(t,e,i,n,r,a,o,s){var c=e+n+a+s;return c>>>0},e.sum64_5_hi=function(t,e,i,n,r,a,o,s,c,f){var u=0,d=e;d=d+n>>>0,u+=d<e?1:0,d=d+a>>>0,u+=d<a?1:0,d=d+s>>>0,u+=d<s?1:0,d=d+f>>>0,u+=d<f?1:0;var h=t+i+r+o+c+u;return h>>>0},e.sum64_5_lo=function(t,e,i,n,r,a,o,s,c,f){var u=e+n+a+s+f;return u>>>0},e.rotr64_hi=function(t,e,i){var n=e<<32-i|t>>>i;return n>>>0},e.rotr64_lo=function(t,e,i){var n=t<<32-i|e>>>i;return n>>>0},e.shr64_hi=function(t,e,i){return t>>>i},e.shr64_lo=function(t,e,i){var n=t<<32-i|e>>>i;return n>>>0}},c3d3:function(t,e,i){"use strict";i.r(e);var n=i("07dd"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},c4b4:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={vhImage:i("ce7c").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"auction-bid-list",class:{"is-from-detail":t.isFromDetail}},t._l(t.list,(function(e,n){return i("v-uni-view",{key:n},[i("v-uni-view",{staticClass:"flex-c-c h-132 text-9"},[i("v-uni-view",{staticClass:"p-rela flex-c-c w-60 h-60"},[i("vh-image",{attrs:{"loading-type":4,src:+e.is_anonymous?"https://images.vinehoo.com/avatars/rabbit.png":e.avatar_image,width:60,height:60,shape:"circle"}}),!n&&t.isFromDetail?i("v-uni-image",{staticClass:"p-abso w-60 h-60",attrs:{src:t.ossIcon("/auction/circle_60.png")}}):t._e()],1),i("v-uni-view",{staticClass:"ml-08"},[i("v-uni-view",{staticClass:"ml-12 w-382 text-hidden",class:{"text-e80404":!n&&t.isFromDetail}},[i("v-uni-text",{staticClass:"font-20 l-h-26"},[i("v-uni-text",{staticClass:"font-18"},[t._v("NO.")]),t._v(t._s(e.code))],1),i("v-uni-text",{staticClass:"ml-06 font-24 l-h-34"},[t._v(t._s(e.nickname))])],1),i("v-uni-view",{staticClass:"mt-10 font-20 text-9 l-h-28"},[t._v("（地区："+t._s(e.province_name||"未知")+"）")])],1),i("v-uni-view",{staticClass:"flex-1 text-right"},[i("v-uni-view",{staticClass:"flex-e-c"},[i("v-uni-image",{class:t.getStatusClazz(n),attrs:{src:t.ossIcon(t.getStatusIcon(n))}}),i("v-uni-text",{staticClass:"ml-06 font-32 l-h-44",class:{"text-e80404":!n&&t.isFromDetail}},[t._v("¥"+t._s(e.bid_price))])],1),i("v-uni-view",{staticClass:"font-24 l-h-34"},[t._v(t._s(t._f("date")(e.create_time,"mm.dd hh:MM:ss")))])],1)],1)],1)})),1)},a=[]},c591:function(t,e,i){"use strict";(function(e){var n,r=i("b639"),a=r.Buffer,o={};for(n in r)r.hasOwnProperty(n)&&"SlowBuffer"!==n&&"Buffer"!==n&&(o[n]=r[n]);var s=o.Buffer={};for(n in a)a.hasOwnProperty(n)&&"allocUnsafe"!==n&&"allocUnsafeSlow"!==n&&(s[n]=a[n]);if(o.Buffer.prototype=a.prototype,s.from&&s.from!==Uint8Array.from||(s.from=function(t,e,i){if("number"===typeof t)throw new TypeError('The "value" argument must not be of type number. Received type '+typeof t);if(t&&"undefined"===typeof t.length)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);return a(t,e,i)}),s.alloc||(s.alloc=function(t,e,i){if("number"!==typeof t)throw new TypeError('The "size" argument must be of type number. Received type '+typeof t);if(t<0||t>=2*(1<<30))throw new RangeError('The value "'+t+'" is invalid for option "size"');var n=a(t);return e&&0!==e.length?"string"===typeof i?n.fill(e,i):n.fill(e):n.fill(0),n}),!o.kStringMaxLength)try{o.kStringMaxLength=e.binding("buffer").kStringMaxLength}catch(c){}o.constants||(o.constants={MAX_LENGTH:o.kMaxLength},o.kStringMaxLength&&(o.constants.MAX_STRING_LENGTH=o.kStringMaxLength)),t.exports=o}).call(this,i("4362"))},c5d9:function(t,e,i){"use strict";i.r(e);var n=i("43a3"),r=i("90d9");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"5013205d",null,!1,n["a"],void 0);e["default"]=s.exports},c6cc:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uPopup:i("c4b0").default,vhCheck:i("2036").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-popup",{attrs:{value:t.value,mode:"bottom",width:"100%",height:"1068rpx","border-radius":"20"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"flex-s-c ptb-00-plr-24 h-148"},[i("v-uni-text",{staticClass:"font-wei-500 font-32 text-3"},[t._v("支付保证金")])],1),i("v-uni-view",{staticClass:"h-08 bg-f7f7f7"}),i("v-uni-view",{staticClass:"ptb-32-plr-24"},[i("v-uni-view",{staticClass:"d-flex a-baseline"},[i("v-uni-text",{staticClass:"font-wei-500 font-32 text-6"},[t._v("金额")]),i("v-uni-text",{staticClass:"ml-40 font-wei-500 font-52 text-3"},[i("v-uni-text",{staticClass:"font-28"},[t._v("¥")]),t._v(t._s(t.money))],1),t.earnestCouponInfo&&t.earnestCouponInfo.id?i("v-uni-view",{staticClass:"ml-10 flex-c-c ptb-00-plr-10 h-40 font-20 text-e80404 bg-fff2f2 b-rad-20"},[i("v-uni-text",[t._v("已减")]),i("v-uni-text",{staticClass:"ml-06"},[t._v("¥"),i("v-uni-text",{staticClass:"font-20"},[t._v(t._s(parseFloat(t.earnestCouponInfo.coupon_face_value)))])],1)],1):t._e()],1),i("v-uni-view",{staticClass:"mt-20"},t._l(t.list,(function(e,n){return i("v-uni-view",{key:n,staticClass:"mb-10 font-26 text-6 l-h-36"},[t._v(t._s(n+1)+"."),i("v-uni-text",{domProps:{innerHTML:t._s(e)}})],1)})),1)],1),i("v-uni-view",{staticClass:"h-08 bg-f7f7f7"}),i("v-uni-view",{staticClass:"mt-40 ptb-00-plr-24 h-188",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.navigateTo(t.routeTable.pEAddressManagement+"?comeFrom=8")}}},[t.addressInfo.id?i("v-uni-view",{staticClass:"flex-sb-c ptb-00-plr-24 h-p100 bg-f9f9f9 b-rad-10"},[i("v-uni-view",[i("v-uni-view",[i("v-uni-text",{staticClass:"font-wei-500 font-32 text-3"},[t._v(t._s(t.addressInfo.consignee))]),i("v-uni-text",{staticClass:"ml-20 font-28 text-9"},[t._v(t._s(t.addressInfo.consignee_phone))])],1),i("v-uni-view",{staticClass:"flex-s-c"},[t.addressInfo.is_default?i("v-uni-view",{staticClass:"flex-c-c mr-08 w-50 h-24 font-wei-500 font-18 text-ffffff bg-e80404 b-rad-04"},[t._v("默认")]):t._e(),t.addressInfo.label?i("v-uni-view",{staticClass:"flex-c-c mr-08 w-50 h-24 font-wei-500 font-18 text-ffffff bg-2e7bff b-rad-04"},[t._v(t._s(t.addressInfo.label))]):t._e(),i("v-uni-text",{staticClass:"font-24 text-3 l-h-34"},[t._v(t._s(t.addressInfo.province_name)+" "+t._s(t.addressInfo.city_name)+" "+t._s(t.addressInfo.town_name))])],1),i("v-uni-view",{staticClass:"font-24 text-3 l-h-34"},[t._v(t._s(t.addressInfo.address))])],1),i("v-uni-image",{staticClass:"w-12 h-20",attrs:{src:t.ossIcon("/after_sale_detail/arrow_right_12x20.png")}})],1):i("v-uni-view",{staticClass:"h-p100"},[i("v-uni-image",{staticClass:"p-abso w-702 h-188",attrs:{src:t.ossIcon("/order-confirm/add_bg.png")}}),i("v-uni-view",{staticClass:"p-rela flex-c-c flex-column h-p100"},[i("v-uni-image",{staticClass:"w-84 h-84",attrs:{src:t.ossIcon("/order-confirm/add_ico.png")}}),i("v-uni-text",{staticClass:"mt-14 font-28 text-3 l-h-40"},[t._v("新建收货地址")])],1)],1)],1),i("v-uni-view",{staticClass:"flex-c-c mt-40"},[i("v-uni-button",{staticClass:"vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32",class:t.addressInfo.id&&t.checked?"bg-e80404":"bg-fce4e3",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSubmit.apply(void 0,arguments)}}},[t._v("提交")])],1),i("v-uni-view",{staticClass:"flex-c-c mt-24"},[i("vh-check",{attrs:{checked:t.checked,width:26,height:26},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.checked=!t.checked}}}),i("v-uni-view",{staticClass:"ml-10 font-24 text-9"},[i("v-uni-text",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.checked=!t.checked}}},[t._v("阅读并接受")]),i("v-uni-text",{staticClass:"text-83a6cc",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.jump.jumpH5Agreement(t.agreementPrefix+"/auctionEarnestRules",t.$vhFrom)}}},[t._v("《保证金规则》")]),i("v-uni-text",{staticClass:"text-83a6cc",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.jump.jumpH5Agreement(t.agreementPrefix+"/auctionRules",t.$vhFrom)}}},[t._v("《拍卖规则》")]),i("v-uni-text",{staticClass:"text-83a6cc",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.jump.jumpH5Agreement(t.agreementPrefix+"/auctionBiddingServiceProtocol",t.$vhFrom)}}},[t._v("《竞买协议》")])],1)],1)],1)},a=[]},c86c:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(i("f3f3"));i("a9e3"),i("99af");var a=i("26cb"),o={props:{goods:{type:Object,default:function(){return{}}},isShowNonsupport:{type:Boolean,default:!1},earnestStatus:{type:Number,default:0},earnestOrderNo:{type:String,default:""}},data:function(){return{delayPopupVisible:!1}},computed:(0,r.default)({},(0,a.mapState)(["routeTable"])),methods:{jumpNewFundsDetail:function(){this.earnestOrderNo&&this.jump.navigateTo("".concat(this.routeTable.pHAuctionFundsDetailNew,"?orderNo=").concat(this.earnestOrderNo,"&isFromAuctionGoods=1"))}}};e.default=o},c8ac:function(t,e,i){"use strict";i.r(e);var n=i("f499"),r=i("05f3");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"25d7e95e",null,!1,n["a"],void 0);e["default"]=s.exports},c992:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d401"),i("d3b7"),i("25f0"),i("acd8"),i("a9e3");var n=function t(e,i,n){function r(t){var e=t.toString().split(".")[1];return e?e.length:0}var a=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:15;return parseFloat(Number(t).toPrecision(e))},o=Math.pow(10,Math.max(r(e),r(n))),s=0;switch(e=a(e*o),n=a(n*o),i){case"+":s=(e+n)/o;break;case"-":s=(e-n)/o;break;case"*":s=e*n/(o*o);break;case"/":s=e/n;break;case"%":s=e%n;break}return s=a(s),{result:s,next:function(e,i){return t(s,e,i)}}};e.default=n},cb29:function(t,e,i){"use strict";var n=i("23e7"),r=i("81d5"),a=i("44d2");n({target:"Array",proto:!0},{fill:r}),a("fill")},cd91:function(t){t.exports=JSON.parse('{"1.3.132.0.10":"secp256k1","1.3.132.0.33":"p224","1.2.840.10045.3.1.1":"p192","1.2.840.10045.3.1.7":"p256","1.3.132.0.34":"p384","1.3.132.0.35":"p521"}')},cfbd:function(t,e,i){"use strict";const n=i("3fb5"),r=i("399f"),a=i("6283").DecoderBuffer,o=i("8360"),s=i("8b71");function c(t){this.enc="der",this.name=t.name,this.entity=t,this.tree=new f,this.tree._init(t.body)}function f(t){o.call(this,"der",t)}function u(t,e){let i=t.readUInt8(e);if(t.isError(i))return i;const n=s.tagClass[i>>6],r=0===(32&i);if(31===(31&i)){let n=i;i=0;while(128===(128&n)){if(n=t.readUInt8(e),t.isError(n))return n;i<<=7,i|=127&n}}else i&=31;const a=s.tag[i];return{cls:n,primitive:r,tag:i,tagStr:a}}function d(t,e,i){let n=t.readUInt8(i);if(t.isError(n))return n;if(!e&&128===n)return null;if(0===(128&n))return n;const r=127&n;if(r>4)return t.error("length octect is too long");n=0;for(let a=0;a<r;a++){n<<=8;const e=t.readUInt8(i);if(t.isError(e))return e;n|=e}return n}t.exports=c,c.prototype.decode=function(t,e){return a.isDecoderBuffer(t)||(t=new a(t,e)),this.tree._decode(t,e)},n(f,o),f.prototype._peekTag=function(t,e,i){if(t.isEmpty())return!1;const n=t.save(),r=u(t,'Failed to peek tag: "'+e+'"');return t.isError(r)?r:(t.restore(n),r.tag===e||r.tagStr===e||r.tagStr+"of"===e||i)},f.prototype._decodeTag=function(t,e,i){const n=u(t,'Failed to decode tag of "'+e+'"');if(t.isError(n))return n;let r=d(t,n.primitive,'Failed to get length of "'+e+'"');if(t.isError(r))return r;if(!i&&n.tag!==e&&n.tagStr!==e&&n.tagStr+"of"!==e)return t.error('Failed to match tag: "'+e+'"');if(n.primitive||null!==r)return t.skip(r,'Failed to match body of: "'+e+'"');const a=t.save(),o=this._skipUntilEnd(t,'Failed to skip indefinite length body: "'+this.tag+'"');return t.isError(o)?o:(r=t.offset-a.offset,t.restore(a),t.skip(r,'Failed to match body of: "'+e+'"'))},f.prototype._skipUntilEnd=function(t,e){for(;;){const i=u(t,e);if(t.isError(i))return i;const n=d(t,i.primitive,e);if(t.isError(n))return n;let r;if(r=i.primitive||null!==n?t.skip(n):this._skipUntilEnd(t,e),t.isError(r))return r;if("end"===i.tagStr)break}},f.prototype._decodeList=function(t,e,i,n){const r=[];while(!t.isEmpty()){const e=this._peekTag(t,"end");if(t.isError(e))return e;const a=i.decode(t,"der",n);if(t.isError(a)&&e)break;r.push(a)}return r},f.prototype._decodeStr=function(t,e){if("bitstr"===e){const e=t.readUInt8();return t.isError(e)?e:{unused:e,data:t.raw()}}if("bmpstr"===e){const e=t.raw();if(e.length%2===1)return t.error("Decoding of string type: bmpstr length mismatch");let i="";for(let t=0;t<e.length/2;t++)i+=String.fromCharCode(e.readUInt16BE(2*t));return i}if("numstr"===e){const e=t.raw().toString("ascii");return this._isNumstr(e)?e:t.error("Decoding of string type: numstr unsupported characters")}if("octstr"===e)return t.raw();if("objDesc"===e)return t.raw();if("printstr"===e){const e=t.raw().toString("ascii");return this._isPrintstr(e)?e:t.error("Decoding of string type: printstr unsupported characters")}return/str$/.test(e)?t.raw().toString():t.error("Decoding of string type: "+e+" unsupported")},f.prototype._decodeObjid=function(t,e,i){let n;const r=[];let a=0,o=0;while(!t.isEmpty())o=t.readUInt8(),a<<=7,a|=127&o,0===(128&o)&&(r.push(a),a=0);128&o&&r.push(a);const s=r[0]/40|0,c=r[0]%40;if(n=i?r:[s,c].concat(r.slice(1)),e){let t=e[n.join(" ")];void 0===t&&(t=e[n.join(".")]),void 0!==t&&(n=t)}return n},f.prototype._decodeTime=function(t,e){const i=t.raw().toString();let n,r,a,o,s,c;if("gentime"===e)n=0|i.slice(0,4),r=0|i.slice(4,6),a=0|i.slice(6,8),o=0|i.slice(8,10),s=0|i.slice(10,12),c=0|i.slice(12,14);else{if("utctime"!==e)return t.error("Decoding "+e+" time is not supported yet");n=0|i.slice(0,2),r=0|i.slice(2,4),a=0|i.slice(4,6),o=0|i.slice(6,8),s=0|i.slice(8,10),c=0|i.slice(10,12),n=n<70?2e3+n:1900+n}return Date.UTC(n,r-1,a,o,s,c,0)},f.prototype._decodeNull=function(){return null},f.prototype._decodeBool=function(t){const e=t.readUInt8();return t.isError(e)?e:0!==e},f.prototype._decodeInt=function(t,e){const i=t.raw();let n=new r(i);return e&&(n=e[n.toString(10)]||n),n},f.prototype._use=function(t,e){return"function"===typeof t&&(t=t(e)),t._getDecoder("der").tree}},d0fd:function(t,e,i){var n=i("1007");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("7c84d548",n,!0,{sourceMap:!1,shadowMode:!1})},d0ff:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,n.default)(t)||(0,r.default)(t)||(0,a.default)(t)||(0,o.default)()};var n=s(i("4053")),r=s(i("a9e0")),a=s(i("dde1")),o=s(i("10eb"));function s(t){return t&&t.__esModule?t:{default:t}}},d17b:function(t,e,i){t.exports=i("e372").Transform},d1c8:function(t,e,i){"use strict";const n=i("3fb5");function r(t){this._reporterState={obj:null,path:[],options:t||{},errors:[]}}function a(t,e){this.path=t,this.rethrow(e)}e.Reporter=r,r.prototype.isError=function(t){return t instanceof a},r.prototype.save=function(){const t=this._reporterState;return{obj:t.obj,pathLen:t.path.length}},r.prototype.restore=function(t){const e=this._reporterState;e.obj=t.obj,e.path=e.path.slice(0,t.pathLen)},r.prototype.enterKey=function(t){return this._reporterState.path.push(t)},r.prototype.exitKey=function(t){const e=this._reporterState;e.path=e.path.slice(0,t-1)},r.prototype.leaveKey=function(t,e,i){const n=this._reporterState;this.exitKey(t),null!==n.obj&&(n.obj[e]=i)},r.prototype.path=function(){return this._reporterState.path.join("/")},r.prototype.enterObject=function(){const t=this._reporterState,e=t.obj;return t.obj={},e},r.prototype.leaveObject=function(t){const e=this._reporterState,i=e.obj;return e.obj=t,i},r.prototype.error=function(t){let e;const i=this._reporterState,n=t instanceof a;if(e=n?t:new a(i.path.map((function(t){return"["+JSON.stringify(t)+"]"})).join(""),t.message||t,t.stack),!i.options.partial)throw e;return n||i.errors.push(e),e},r.prototype.wrapResult=function(t){const e=this._reporterState;return e.options.partial?{result:this.isError(t)?null:t,errors:e.errors}:t},n(a,Error),a.prototype.rethrow=function(t){if(this.message=t+" at: "+(this.path||"(shallow)"),Error.captureStackTrace&&Error.captureStackTrace(this,a),!this.stack)try{throw new Error(this.message)}catch(e){this.stack=e.stack}return this}},d220:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{value:{type:Boolean,default:!0}},data:function(){return{time:3,interval:null}},watch:{value:function(){this.value?this.countDown():(this.interval&&clearInterval(this.interval),this.time=this.$options.data().time)}},methods:{onInput:function(t){this.$emit("input",t)},onAgree:function(){this.time||this.$emit("agree")},countDown:function(){var t=this;this.interval=setInterval((function(){t.time--,t.time<=0&&t.interval&&clearInterval(t.interval)}),1e3)}}};e.default=n},d424:function(t,e,i){"use strict";var n=i("3fb5"),r=i("8707").Buffer,a=i("6430"),o=r.alloc(128);function s(t,e){a.call(this,"digest"),"string"===typeof e&&(e=r.from(e)),this._alg=t,this._key=e,e.length>64?e=t(e):e.length<64&&(e=r.concat([e,o],64));for(var i=this._ipad=r.allocUnsafe(64),n=this._opad=r.allocUnsafe(64),s=0;s<64;s++)i[s]=54^e[s],n[s]=92^e[s];this._hash=[i]}n(s,a),s.prototype._update=function(t){this._hash.push(t)},s.prototype._final=function(){var t=this._alg(r.concat(this._hash));return this._alg(r.concat([this._opad,t]))},t.exports=s},d485:function(t,e,i){t.exports=a;var n=i("faa1").EventEmitter,r=i("3fb5");function a(){n.call(this)}r(a,n),a.Readable=i("e372"),a.Writable=i("2c63"),a.Duplex=i("0960"),a.Transform=i("d17b"),a.PassThrough=i("c2ae"),a.Stream=a,a.prototype.pipe=function(t,e){var i=this;function r(e){t.writable&&!1===t.write(e)&&i.pause&&i.pause()}function a(){i.readable&&i.resume&&i.resume()}i.on("data",r),t.on("drain",a),t._isStdio||e&&!1===e.end||(i.on("end",s),i.on("close",c));var o=!1;function s(){o||(o=!0,t.end())}function c(){o||(o=!0,"function"===typeof t.destroy&&t.destroy())}function f(t){if(u(),0===n.listenerCount(this,"error"))throw t}function u(){i.removeListener("data",r),t.removeListener("drain",a),i.removeListener("end",s),i.removeListener("close",c),i.removeListener("error",f),t.removeListener("error",f),i.removeListener("end",u),i.removeListener("close",u),t.removeListener("close",u)}return i.on("error",f),t.on("error",f),i.on("end",u),i.on("close",u),t.on("close",u),t.emit("pipe",i),t}},d4f3:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"d-flex"},[i("v-uni-image",{staticClass:"fade-in",style:[t.checkStyle],attrs:{src:t.checked?t.checkedImg:t.unCheckedImg,mode:t.mode},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click()}}})],1)},r=[]},d57a:function(t,e,i){"use strict";i.r(e);var n=i("4105"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},d6e4:function(t,e,i){"use strict";i.r(e);var n=i("fe0d"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},d70e:function(t){t.exports=JSON.parse('{"2.16.840.*********.1.1":"aes-128-ecb","2.16.840.*********.1.2":"aes-128-cbc","2.16.840.*********.1.3":"aes-128-ofb","2.16.840.*********.1.4":"aes-128-cfb","2.16.840.*********.1.21":"aes-192-ecb","2.16.840.*********.1.22":"aes-192-cbc","2.16.840.*********.1.23":"aes-192-ofb","2.16.840.*********.1.24":"aes-192-cfb","2.16.840.*********.1.41":"aes-256-ecb","2.16.840.*********.1.42":"aes-256-cbc","2.16.840.*********.1.43":"aes-256-ofb","2.16.840.*********.1.44":"aes-256-cfb"}')},d758:function(t,e,i){"use strict";i.r(e);var n=i("c86c"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},d854:function(t,e,i){"use strict";i.r(e);var n=i("901d"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},d948:function(t,e,i){"use strict";i.r(e);var n=i("7c29"),r=i("53f0");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"6a3d84ea",null,!1,n["a"],void 0);e["default"]=s.exports},d97b:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[this.nodes.length?this._e():this._t("default"),e("v-uni-view",{style:this.showAm+(this.selectable?";user-select:text;-webkit-user-select:text":""),attrs:{id:"_top"}},[e("div",{attrs:{id:"rtf"+this.uid}})])],2)},r=[]},da3e:function(t,e){function i(t,e){if(!t)throw new Error(e||"Assertion failed")}t.exports=i,i.equal=function(t,e,i){if(t!=e)throw new Error(i||"Assertion failed: "+t+" != "+e)}},db4c:function(t,e,i){"use strict";i.r(e);var n=i("a49d"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},dc14:function(t,e,i){"use strict";(function(e,n){var r=i("966d");function a(t){var e=this;this.next=null,this.entry=null,this.finish=function(){(function(t,e,i){var n=t.entry;t.entry=null;while(n){var r=n.callback;e.pendingcb--,r(i),n=n.next}e.corkedRequestsFree?e.corkedRequestsFree.next=t:e.corkedRequestsFree=t})(e,t)}}t.exports=m;var o,s=!e.browser&&["v0.10","v0.9."].indexOf(e.version.slice(0,5))>-1?setImmediate:r.nextTick;m.WritableState=v;var c=Object.create(i("3a7c"));c.inherits=i("3fb5");var f={deprecate:i("b7d1")},u=i("429b"),d=i("8707").Buffer,h=n.Uint8Array||function(){};var l,p=i("4681");function b(){}function v(t,e){o=o||i("b19a"),t=t||{};var n=e instanceof o;this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.writableObjectMode);var c=t.highWaterMark,f=t.writableHighWaterMark,u=this.objectMode?16:16384;this.highWaterMark=c||0===c?c:n&&(f||0===f)?f:u,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var d=!1===t.decodeStrings;this.decodeStrings=!d,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){(function(t,e){var i=t._writableState,n=i.sync,a=i.writecb;if(function(t){t.writing=!1,t.writecb=null,t.length-=t.writelen,t.writelen=0}(i),e)(function(t,e,i,n,a){--e.pendingcb,i?(r.nextTick(a,n),r.nextTick(S,t,e),t._writableState.errorEmitted=!0,t.emit("error",n)):(a(n),t._writableState.errorEmitted=!0,t.emit("error",n),S(t,e))})(t,i,n,e,a);else{var o=_(i);o||i.corked||i.bufferProcessing||!i.bufferedRequest||w(t,i),n?s(y,t,i,o,a):y(t,i,o,a)}})(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new a(this)}function m(t){if(o=o||i("b19a"),!l.call(m,this)&&!(this instanceof o))return new m(t);this._writableState=new v(t,this),this.writable=!0,t&&("function"===typeof t.write&&(this._write=t.write),"function"===typeof t.writev&&(this._writev=t.writev),"function"===typeof t.destroy&&(this._destroy=t.destroy),"function"===typeof t.final&&(this._final=t.final)),u.call(this)}function g(t,e,i,n,r,a,o){e.writelen=n,e.writecb=o,e.writing=!0,e.sync=!0,i?t._writev(r,e.onwrite):t._write(r,a,e.onwrite),e.sync=!1}function y(t,e,i,n){i||function(t,e){0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain"))}(t,e),e.pendingcb--,n(),S(t,e)}function w(t,e){e.bufferProcessing=!0;var i=e.bufferedRequest;if(t._writev&&i&&i.next){var n=e.bufferedRequestCount,r=new Array(n),o=e.corkedRequestsFree;o.entry=i;var s=0,c=!0;while(i)r[s]=i,i.isBuf||(c=!1),i=i.next,s+=1;r.allBuffers=c,g(t,e,!0,e.length,r,"",o.finish),e.pendingcb++,e.lastBufferedRequest=null,o.next?(e.corkedRequestsFree=o.next,o.next=null):e.corkedRequestsFree=new a(e),e.bufferedRequestCount=0}else{while(i){var f=i.chunk,u=i.encoding,d=i.callback,h=e.objectMode?1:f.length;if(g(t,e,!1,h,f,u,d),i=i.next,e.bufferedRequestCount--,e.writing)break}null===i&&(e.lastBufferedRequest=null)}e.bufferedRequest=i,e.bufferProcessing=!1}function _(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function x(t,e){t._final((function(i){e.pendingcb--,i&&t.emit("error",i),e.prefinished=!0,t.emit("prefinish"),S(t,e)}))}function S(t,e){var i=_(e);return i&&(function(t,e){e.prefinished||e.finalCalled||("function"===typeof t._final?(e.pendingcb++,e.finalCalled=!0,r.nextTick(x,t,e)):(e.prefinished=!0,t.emit("prefinish")))}(t,e),0===e.pendingcb&&(e.finished=!0,t.emit("finish"))),i}c.inherits(m,u),v.prototype.getBuffer=function(){var t=this.bufferedRequest,e=[];while(t)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(v.prototype,"buffer",{get:f.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}(),"function"===typeof Symbol&&Symbol.hasInstance&&"function"===typeof Function.prototype[Symbol.hasInstance]?(l=Function.prototype[Symbol.hasInstance],Object.defineProperty(m,Symbol.hasInstance,{value:function(t){return!!l.call(this,t)||this===m&&(t&&t._writableState instanceof v)}})):l=function(t){return t instanceof this},m.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},m.prototype.write=function(t,e,i){var n=this._writableState,a=!1,o=!n.objectMode&&function(t){return d.isBuffer(t)||t instanceof h}(t);return o&&!d.isBuffer(t)&&(t=function(t){return d.from(t)}(t)),"function"===typeof e&&(i=e,e=null),o?e="buffer":e||(e=n.defaultEncoding),"function"!==typeof i&&(i=b),n.ended?function(t,e){var i=new Error("write after end");t.emit("error",i),r.nextTick(e,i)}(this,i):(o||function(t,e,i,n){var a=!0,o=!1;return null===i?o=new TypeError("May not write null values to stream"):"string"===typeof i||void 0===i||e.objectMode||(o=new TypeError("Invalid non-string/buffer chunk")),o&&(t.emit("error",o),r.nextTick(n,o),a=!1),a}(this,n,t,i))&&(n.pendingcb++,a=function(t,e,i,n,r,a){if(!i){var o=function(t,e,i){t.objectMode||!1===t.decodeStrings||"string"!==typeof e||(e=d.from(e,i));return e}(e,n,r);n!==o&&(i=!0,r="buffer",n=o)}var s=e.objectMode?1:n.length;e.length+=s;var c=e.length<e.highWaterMark;c||(e.needDrain=!0);if(e.writing||e.corked){var f=e.lastBufferedRequest;e.lastBufferedRequest={chunk:n,encoding:r,isBuf:i,callback:a,next:null},f?f.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else g(t,e,!1,s,n,r,a);return c}(this,n,o,t,e,i)),a},m.prototype.cork=function(){var t=this._writableState;t.corked++},m.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.finished||t.bufferProcessing||!t.bufferedRequest||w(this,t))},m.prototype.setDefaultEncoding=function(t){if("string"===typeof t&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+t);return this._writableState.defaultEncoding=t,this},Object.defineProperty(m.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),m.prototype._write=function(t,e,i){i(new Error("_write() is not implemented"))},m.prototype._writev=null,m.prototype.end=function(t,e,i){var n=this._writableState;"function"===typeof t?(i=t,t=null,e=null):"function"===typeof e&&(i=e,e=null),null!==t&&void 0!==t&&this.write(t,e),n.corked&&(n.corked=1,this.uncork()),n.ending||n.finished||function(t,e,i){e.ending=!0,S(t,e),i&&(e.finished?r.nextTick(i):t.once("finish",i));e.ended=!0,t.writable=!1}(this,n,i)},Object.defineProperty(m.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),m.prototype.destroy=p.destroy,m.prototype._undestroy=p.undestroy,m.prototype._destroy=function(t,e){this.end(),e(t)}}).call(this,i("4362"),i("c8ba"))},e07b:function(t,e,i){var n=i("5a76"),r=i("b5ca"),a=i("69f2"),o=i("8707").Buffer,s=i("7d2a"),c=i("9f9d"),f=i("8be6"),u=o.alloc(128),d={md5:16,sha1:20,sha224:28,sha256:32,sha384:48,sha512:64,rmd160:20,ripemd160:20};function h(t,e,i){var s=function(t){function e(e){return a(t).update(e).digest()}return"rmd160"===t||"ripemd160"===t?function(t){return(new r).update(t).digest()}:"md5"===t?n:e}(t),c="sha512"===t||"sha384"===t?128:64;e.length>c?e=s(e):e.length<c&&(e=o.concat([e,u],c));for(var f=o.allocUnsafe(c+d[t]),h=o.allocUnsafe(c+d[t]),l=0;l<c;l++)f[l]=54^e[l],h[l]=92^e[l];var p=o.allocUnsafe(c+i+4);f.copy(p,0,0,c),this.ipad1=p,this.ipad2=f,this.opad=h,this.alg=t,this.blocksize=c,this.hash=s,this.size=d[t]}h.prototype.run=function(t,e){t.copy(e,this.blocksize);var i=this.hash(e);return i.copy(this.opad,this.blocksize),this.hash(this.opad)},t.exports=function(t,e,i,n,r){s(i,n),t=f(t,c,"Password"),e=f(e,c,"Salt"),r=r||"sha1";var a=new h(r,t,e.length),u=o.allocUnsafe(n),l=o.allocUnsafe(e.length+4);e.copy(l,0,0,e.length);for(var p=0,b=d[r],v=Math.ceil(n/b),m=1;m<=v;m++){l.writeUInt32BE(m,e.length);for(var g=a.run(l,a.ipad1),y=g,w=1;w<i;w++){y=a.run(y,a.ipad2);for(var _=0;_<b;_++)g[_]^=y[_]}g.copy(u,p),p+=b}return u}},e12f:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={vhImage:i("ce7c").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("v-uni-view",{class:t.isHiddenText?"h-max-80 o-hid":""},[i("v-uni-view",{staticClass:"font-28 text-3 l-h-40",class:t.isHiddenText?"text-hidden-2":"",attrs:{id:t.id}},[t._v(t._s(t.item.content))])],1),t.isHiddenText?i("v-uni-view",{staticClass:"flex-e-c mt-10"},[i("v-uni-view",{staticClass:"flex-c-c",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onExpand.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"w-42 h-02 bg-cccccc"}),i("v-uni-view",{staticClass:"ml-06 font-28 text-2e7bff l-h-40"},[t._v("展开")])],1)],1):t._e(),t.item.emoji_image?i("v-uni-view",{staticClass:"mt-10"},[i("vh-image",{attrs:{"loading-type":"2",src:t.item.emoji_image,width:120,height:120}})],1):t._e()],1)},a=[]},e17a:function(t,e,i){"use strict";i.r(e);var n=i("e6b7"),r=i("58e9");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"c901b7a8",null,!1,n["a"],void 0);e["default"]=s.exports},e1d3:function(t,e,i){(function(e){var n=i("3337"),r=i("399f");t.exports=function(t){return new o(t)};var a={secp256k1:{name:"secp256k1",byteLength:32},secp224r1:{name:"p224",byteLength:28},prime256v1:{name:"p256",byteLength:32},prime192v1:{name:"p192",byteLength:24},ed25519:{name:"ed25519",byteLength:32},secp384r1:{name:"p384",byteLength:48},secp521r1:{name:"p521",byteLength:66}};function o(t){this.curveType=a[t],this.curveType||(this.curveType={name:t}),this.curve=new n.ec(this.curveType.name),this.keys=void 0}function s(t,i,n){Array.isArray(t)||(t=t.toArray());var r=new e(t);if(n&&r.length<n){var a=new e(n-r.length);a.fill(0),r=e.concat([a,r])}return i?r.toString(i):r}a.p224=a.secp224r1,a.p256=a.secp256r1=a.prime256v1,a.p192=a.secp192r1=a.prime192v1,a.p384=a.secp384r1,a.p521=a.secp521r1,o.prototype.generateKeys=function(t,e){return this.keys=this.curve.genKeyPair(),this.getPublicKey(t,e)},o.prototype.computeSecret=function(t,i,n){i=i||"utf8",e.isBuffer(t)||(t=new e(t,i));var r=this.curve.keyFromPublic(t).getPublic(),a=r.mul(this.keys.getPrivate()).getX();return s(a,n,this.curveType.byteLength)},o.prototype.getPublicKey=function(t,e){var i=this.keys.getPublic("compressed"===e,!0);return"hybrid"===e&&(i[i.length-1]%2?i[0]=7:i[0]=6),s(i,t)},o.prototype.getPrivateKey=function(t){return s(this.keys.getPrivate(),t)},o.prototype.setPublicKey=function(t,i){return i=i||"utf8",e.isBuffer(t)||(t=new e(t,i)),this.keys._importPublic(t),this},o.prototype.setPrivateKey=function(t,i){i=i||"utf8",e.isBuffer(t)||(t=new e(t,i));var n=new r(t);return n=n.toString(16),this.keys=this.curve.genKeyPair(),this.keys._importPrivate(n),this}}).call(this,i("b639").Buffer)},e372:function(t,e,i){e=t.exports=i("ad71"),e.Stream=e,e.Readable=e,e.Writable=i("dc14"),e.Duplex=i("b19a"),e.Transform=i("27bf"),e.PassThrough=i("780f")},e3aa:function(t,e,i){"use strict";i.r(e);var n=i("0240"),r=i("d758");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"765a720b",null,!1,n["a"],void 0);e["default"]=s.exports},e3db:function(t,e){var i={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==i.call(t)}},e6b7:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={AuctionWGoodsList:i("1494").default},r=function(){var t=this.$createElement,e=this._self._c||t;return this.list.length?e("v-uni-view",[e("v-uni-view",{staticClass:"flex-c-c"},[e("v-uni-image",{staticClass:"w-36 h-38",attrs:{src:this.ossIcon("/auction/recommend_36_38.png")}}),e("v-uni-text",{staticClass:"ml-12 font-wei-600 font-36 text-3"},[this._v("热门推荐")])],1),e("v-uni-view",{staticClass:"mt-50"},[e("AuctionWGoodsList",{attrs:{list:this.list,addTime:50}})],1)],1):this._e()},a=[]},e85f:function(t){t.exports=JSON.parse('{"aes-128-ecb":{"cipher":"AES","key":128,"iv":0,"mode":"ECB","type":"block"},"aes-192-ecb":{"cipher":"AES","key":192,"iv":0,"mode":"ECB","type":"block"},"aes-256-ecb":{"cipher":"AES","key":256,"iv":0,"mode":"ECB","type":"block"},"aes-128-cbc":{"cipher":"AES","key":128,"iv":16,"mode":"CBC","type":"block"},"aes-192-cbc":{"cipher":"AES","key":192,"iv":16,"mode":"CBC","type":"block"},"aes-256-cbc":{"cipher":"AES","key":256,"iv":16,"mode":"CBC","type":"block"},"aes128":{"cipher":"AES","key":128,"iv":16,"mode":"CBC","type":"block"},"aes192":{"cipher":"AES","key":192,"iv":16,"mode":"CBC","type":"block"},"aes256":{"cipher":"AES","key":256,"iv":16,"mode":"CBC","type":"block"},"aes-128-cfb":{"cipher":"AES","key":128,"iv":16,"mode":"CFB","type":"stream"},"aes-192-cfb":{"cipher":"AES","key":192,"iv":16,"mode":"CFB","type":"stream"},"aes-256-cfb":{"cipher":"AES","key":256,"iv":16,"mode":"CFB","type":"stream"},"aes-128-cfb8":{"cipher":"AES","key":128,"iv":16,"mode":"CFB8","type":"stream"},"aes-192-cfb8":{"cipher":"AES","key":192,"iv":16,"mode":"CFB8","type":"stream"},"aes-256-cfb8":{"cipher":"AES","key":256,"iv":16,"mode":"CFB8","type":"stream"},"aes-128-cfb1":{"cipher":"AES","key":128,"iv":16,"mode":"CFB1","type":"stream"},"aes-192-cfb1":{"cipher":"AES","key":192,"iv":16,"mode":"CFB1","type":"stream"},"aes-256-cfb1":{"cipher":"AES","key":256,"iv":16,"mode":"CFB1","type":"stream"},"aes-128-ofb":{"cipher":"AES","key":128,"iv":16,"mode":"OFB","type":"stream"},"aes-192-ofb":{"cipher":"AES","key":192,"iv":16,"mode":"OFB","type":"stream"},"aes-256-ofb":{"cipher":"AES","key":256,"iv":16,"mode":"OFB","type":"stream"},"aes-128-ctr":{"cipher":"AES","key":128,"iv":16,"mode":"CTR","type":"stream"},"aes-192-ctr":{"cipher":"AES","key":192,"iv":16,"mode":"CTR","type":"stream"},"aes-256-ctr":{"cipher":"AES","key":256,"iv":16,"mode":"CTR","type":"stream"},"aes-128-gcm":{"cipher":"AES","key":128,"iv":12,"mode":"GCM","type":"auth"},"aes-192-gcm":{"cipher":"AES","key":192,"iv":12,"mode":"GCM","type":"auth"},"aes-256-gcm":{"cipher":"AES","key":256,"iv":12,"mode":"GCM","type":"auth"}}')},ea52:function(t,e,i){"use strict";var n=i("d0fd"),r=i.n(n);r.a},ea537:function(t,e,i){"use strict";var n=i("399f"),r=i("f3a3"),a=r.getNAF,o=r.getJSF,s=r.assert;function c(t,e){this.type=t,this.p=new n(e.p,16),this.red=e.prime?n.red(e.prime):n.mont(this.p),this.zero=new n(0).toRed(this.red),this.one=new n(1).toRed(this.red),this.two=new n(2).toRed(this.red),this.n=e.n&&new n(e.n,16),this.g=e.g&&this.pointFromJSON(e.g,e.gRed),this._wnafT1=new Array(4),this._wnafT2=new Array(4),this._wnafT3=new Array(4),this._wnafT4=new Array(4),this._bitLength=this.n?this.n.bitLength():0;var i=this.n&&this.p.div(this.n);!i||i.cmpn(100)>0?this.redN=null:(this._maxwellTrick=!0,this.redN=this.n.toRed(this.red))}function f(t,e){this.curve=t,this.type=e,this.precomputed=null}t.exports=c,c.prototype.point=function(){throw new Error("Not implemented")},c.prototype.validate=function(){throw new Error("Not implemented")},c.prototype._fixedNafMul=function(t,e){s(t.precomputed);var i=t._getDoubles(),n=a(e,1,this._bitLength),r=(1<<i.step+1)-(i.step%2===0?2:1);r/=3;var o,c,f=[];for(o=0;o<n.length;o+=i.step){c=0;for(var u=o+i.step-1;u>=o;u--)c=(c<<1)+n[u];f.push(c)}for(var d=this.jpoint(null,null,null),h=this.jpoint(null,null,null),l=r;l>0;l--){for(o=0;o<f.length;o++)c=f[o],c===l?h=h.mixedAdd(i.points[o]):c===-l&&(h=h.mixedAdd(i.points[o].neg()));d=d.add(h)}return d.toP()},c.prototype._wnafMul=function(t,e){var i=4,n=t._getNAFPoints(i);i=n.wnd;for(var r=n.points,o=a(e,i,this._bitLength),c=this.jpoint(null,null,null),f=o.length-1;f>=0;f--){for(var u=0;f>=0&&0===o[f];f--)u++;if(f>=0&&u++,c=c.dblp(u),f<0)break;var d=o[f];s(0!==d),c="affine"===t.type?d>0?c.mixedAdd(r[d-1>>1]):c.mixedAdd(r[-d-1>>1].neg()):d>0?c.add(r[d-1>>1]):c.add(r[-d-1>>1].neg())}return"affine"===t.type?c.toP():c},c.prototype._wnafMulAdd=function(t,e,i,n,r){var s,c,f,u=this._wnafT1,d=this._wnafT2,h=this._wnafT3,l=0;for(s=0;s<n;s++){f=e[s];var p=f._getNAFPoints(t);u[s]=p.wnd,d[s]=p.points}for(s=n-1;s>=1;s-=2){var b=s-1,v=s;if(1===u[b]&&1===u[v]){var m=[e[b],null,null,e[v]];0===e[b].y.cmp(e[v].y)?(m[1]=e[b].add(e[v]),m[2]=e[b].toJ().mixedAdd(e[v].neg())):0===e[b].y.cmp(e[v].y.redNeg())?(m[1]=e[b].toJ().mixedAdd(e[v]),m[2]=e[b].add(e[v].neg())):(m[1]=e[b].toJ().mixedAdd(e[v]),m[2]=e[b].toJ().mixedAdd(e[v].neg()));var g=[-3,-1,-5,-7,0,7,5,1,3],y=o(i[b],i[v]);for(l=Math.max(y[0].length,l),h[b]=new Array(l),h[v]=new Array(l),c=0;c<l;c++){var w=0|y[0][c],_=0|y[1][c];h[b][c]=g[3*(w+1)+(_+1)],h[v][c]=0,d[b]=m}}else h[b]=a(i[b],u[b],this._bitLength),h[v]=a(i[v],u[v],this._bitLength),l=Math.max(h[b].length,l),l=Math.max(h[v].length,l)}var x=this.jpoint(null,null,null),S=this._wnafT4;for(s=l;s>=0;s--){var k=0;while(s>=0){var A=!0;for(c=0;c<n;c++)S[c]=0|h[c][s],0!==S[c]&&(A=!1);if(!A)break;k++,s--}if(s>=0&&k++,x=x.dblp(k),s<0)break;for(c=0;c<n;c++){var E=S[c];0!==E&&(E>0?f=d[c][E-1>>1]:E<0&&(f=d[c][-E-1>>1].neg()),x="affine"===f.type?x.mixedAdd(f):x.add(f))}}for(s=0;s<n;s++)d[s]=null;return r?x:x.toP()},c.BasePoint=f,f.prototype.eq=function(){throw new Error("Not implemented")},f.prototype.validate=function(){return this.curve.validate(this)},c.prototype.decodePoint=function(t,e){t=r.toArray(t,e);var i=this.p.byteLength();if((4===t[0]||6===t[0]||7===t[0])&&t.length-1===2*i){6===t[0]?s(t[t.length-1]%2===0):7===t[0]&&s(t[t.length-1]%2===1);var n=this.point(t.slice(1,1+i),t.slice(1+i,1+2*i));return n}if((2===t[0]||3===t[0])&&t.length-1===i)return this.pointFromX(t.slice(1,1+i),3===t[0]);throw new Error("Unknown point format")},f.prototype.encodeCompressed=function(t){return this.encode(t,!0)},f.prototype._encode=function(t){var e=this.curve.p.byteLength(),i=this.getX().toArray("be",e);return t?[this.getY().isEven()?2:3].concat(i):[4].concat(i,this.getY().toArray("be",e))},f.prototype.encode=function(t,e){return r.encode(this._encode(e),t)},f.prototype.precompute=function(t){if(this.precomputed)return this;var e={doubles:null,naf:null,beta:null};return e.naf=this._getNAFPoints(8),e.doubles=this._getDoubles(4,t),e.beta=this._getBeta(),this.precomputed=e,this},f.prototype._hasDoubles=function(t){if(!this.precomputed)return!1;var e=this.precomputed.doubles;return!!e&&e.points.length>=Math.ceil((t.bitLength()+1)/e.step)},f.prototype._getDoubles=function(t,e){if(this.precomputed&&this.precomputed.doubles)return this.precomputed.doubles;for(var i=[this],n=this,r=0;r<e;r+=t){for(var a=0;a<t;a++)n=n.dbl();i.push(n)}return{step:t,points:i}},f.prototype._getNAFPoints=function(t){if(this.precomputed&&this.precomputed.naf)return this.precomputed.naf;for(var e=[this],i=(1<<t)-1,n=1===i?null:this.dbl(),r=1;r<i;r++)e[r]=e[r-1].add(n);return{wnd:t,points:e}},f.prototype._getBeta=function(){return null},f.prototype.dblp=function(t){for(var e=this,i=0;i<t;i++)e=e.dbl();return e}},edc9:function(t,e,i){"use strict";var n=i("c3c0"),r=i("da3e");function a(){this.pending=null,this.pendingTotal=0,this.blockSize=this.constructor.blockSize,this.outSize=this.constructor.outSize,this.hmacStrength=this.constructor.hmacStrength,this.padLength=this.constructor.padLength/8,this.endian="big",this._delta8=this.blockSize/8,this._delta32=this.blockSize/32}e.BlockHash=a,a.prototype.update=function(t,e){if(t=n.toArray(t,e),this.pending?this.pending=this.pending.concat(t):this.pending=t,this.pendingTotal+=t.length,this.pending.length>=this._delta8){t=this.pending;var i=t.length%this._delta8;this.pending=t.slice(t.length-i,t.length),0===this.pending.length&&(this.pending=null),t=n.join32(t,0,t.length-i,this.endian);for(var r=0;r<t.length;r+=this._delta32)this._update(t,r,r+this._delta32)}return this},a.prototype.digest=function(t){return this.update(this._pad()),r(null===this.pending),this._digest(t)},a.prototype._pad=function(){var t=this.pendingTotal,e=this._delta8,i=e-(t+this.padLength)%e,n=new Array(i+this.padLength);n[0]=128;for(var r=1;r<i;r++)n[r]=0;if(t<<=3,"big"===this.endian){for(var a=8;a<this.padLength;a++)n[r++]=0;n[r++]=0,n[r++]=0,n[r++]=0,n[r++]=0,n[r++]=t>>>24&255,n[r++]=t>>>16&255,n[r++]=t>>>8&255,n[r++]=255&t}else for(n[r++]=255&t,n[r++]=t>>>8&255,n[r++]=t>>>16&255,n[r++]=t>>>24&255,n[r++]=0,n[r++]=0,n[r++]=0,n[r++]=0,a=8;a<this.padLength;a++)n[r++]=0;return n}},eefe:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uPopup:i("c4b0").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("u-popup",{attrs:{value:t.value,mode:"center",width:"552rpx",height:"414rpx","border-radius":"20"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"p-rela wh-p100"},[i("v-uni-image",{staticClass:"p-abso wh-p100",attrs:{src:t.ossIcon("/auction/popup_bg_552_414.png")}}),i("v-uni-view",{staticClass:"p-rela pt-84 pb-20 h-p100"},[i("v-uni-view",{staticClass:"font-wei-500 font-32 text-3 text-center"},[t._v("保留价说明")]),i("v-uni-view",{staticClass:"mt-24 ptb-00-plr-84 font-26 text-3 l-h-36 text-center"},[t._v("该拍品设置了保留价，若结拍价小于保留价，则竞拍无效。")]),i("v-uni-view",{staticClass:"p-abso bottom-20 w-p100",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput(!1)}}},[i("v-uni-view",{staticClass:"w-p100 h-01 bg-dedede"}),i("v-uni-view",{staticClass:"flex-c-c h-104 font-wei-500 font-28 text-e80404"},[t._v("知道了")])],1)],1)],1)],1)},a=[]},ef3a:function(t,e,i){"use strict";const n=i("343e"),r=i("20f6"),a=i("3fb5"),o=e;function s(t,e){this.name=t,this.body=e,this.decoders={},this.encoders={}}o.define=function(t,e){return new s(t,e)},s.prototype._createNamed=function(t){const e=this.name;function i(t){this._initNamed(t,e)}return a(i,t),i.prototype._initNamed=function(e,i){t.call(this,e,i)},new i(this)},s.prototype._getDecoder=function(t){return t=t||"der",this.decoders.hasOwnProperty(t)||(this.decoders[t]=this._createNamed(r[t])),this.decoders[t]},s.prototype.decode=function(t,e,i){return this._getDecoder(e).decode(t,i)},s.prototype._getEncoder=function(t){return t=t||"der",this.encoders.hasOwnProperty(t)||(this.encoders[t]=this._createNamed(n[t])),this.encoders[t]},s.prototype.encode=function(t,e,i){return this._getEncoder(e).encode(t,i)}},f157:function(t,e,i){var n=i("8987");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("e49b1058",n,!0,{sourceMap:!1,shadowMode:!1})},f2d3:function(t,e,i){"use strict";i.r(e);var n=i("3f63"),r=i("aea1");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);i("ea52");var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"8ea5eff0",null,!1,n["a"],void 0);e["default"]=s.exports},f3a3:function(t,e,i){"use strict";var n=e,r=i("399f"),a=i("da3e"),o=i("7658");n.assert=a,n.toArray=o.toArray,n.zero2=o.zero2,n.toHex=o.toHex,n.encode=o.encode,n.getNAF=function(t,e,i){var n=new Array(Math.max(t.bitLength(),i)+1);n.fill(0);for(var r=1<<e+1,a=t.clone(),o=0;o<n.length;o++){var s,c=a.andln(r-1);a.isOdd()?(s=c>(r>>1)-1?(r>>1)-c:c,a.isubn(s)):s=0,n[o]=s,a.iushrn(1)}return n},n.getJSF=function(t,e){var i=[[],[]];t=t.clone(),e=e.clone();var n,r=0,a=0;while(t.cmpn(-r)>0||e.cmpn(-a)>0){var o,s,c=t.andln(3)+r&3,f=e.andln(3)+a&3;3===c&&(c=-1),3===f&&(f=-1),0===(1&c)?o=0:(n=t.andln(7)+r&7,o=3!==n&&5!==n||2!==f?c:-c),i[0].push(o),0===(1&f)?s=0:(n=e.andln(7)+a&7,s=3!==n&&5!==n||2!==c?f:-f),i[1].push(s),2*r===o+1&&(r=1-r),2*a===s+1&&(a=1-a),t.iushrn(1),e.iushrn(1)}return i},n.cachedProperty=function(t,e,i){var n="_"+e;t.prototype[e]=function(){return void 0!==this[n]?this[n]:this[n]=i.call(this)}},n.parseBytes=function(t){return"string"===typeof t?n.toArray(t,"hex"):t},n.intFromLE=function(t){return new r(t,"hex","le")}},f460:function(t,e,i){var n=i("98e6"),r=i("8707").Buffer;function a(t){var e=r.allocUnsafe(4);return e.writeUInt32BE(t,0),e}t.exports=function(t,e){var i,o=r.alloc(0),s=0;while(o.length<e)i=a(s++),o=r.concat([o,n("sha1").update(t).update(i).digest()]);return o.slice(0,e)}},f499:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uPopup:i("c4b0").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",[i("u-popup",{attrs:{value:t.value,mode:"bottom",width:"100%",height:"698rpx","border-radius":"20"},on:{input:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"flex-s-c ptb-00-plr-24 h-152"},[i("v-uni-image",{staticClass:"w-44 h-44",attrs:{src:t.ossIcon("/auction/close_44.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onInput(!1)}}}),i("v-uni-text",{staticClass:"ml-10 font-wei-500 font-32 text-6"},[t._v("常规出价")])],1),i("v-uni-view",{staticClass:"h-08 bg-f7f7f7"}),i("v-uni-view",{staticClass:"ptb-20-plr-00"},[i("v-uni-view",{staticClass:"flex-c-c h-40 font-28 text-3 l-h-40"},[i("v-uni-view",{staticClass:"w-158"},[t._v("¥"+t._s(t.goods.price)+"起拍")]),i("v-uni-view",{staticClass:"w-02 h-20 bg-d8d8d8"}),i("v-uni-view",{staticClass:"w-304 text-center"},[i("v-uni-text",[t._v("当前价格")]),i("v-uni-text",{staticClass:"ml-22 text-e80404"},[t._v("¥"+t._s(t.goods.final_auction_price))])],1),i("v-uni-view",{staticClass:"w-02 h-20 bg-d8d8d8"}),i("v-uni-view",{staticClass:"w-236 text-right"},[i("v-uni-text",[t._v("加价幅度")]),i("v-uni-text",{staticClass:"ml-22 text-e80404"},[t._v("¥"+t._s(t.goods.markup))])],1)],1),i("v-uni-view",{staticClass:"flex-c-c mt-60"},[i("v-uni-image",{staticClass:"w-96 h-96",attrs:{src:t.ossIcon("/auction/icon_sub"+(t.subBtnStatus?"_h":"")+".png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onCalcBidPrice("sub")}}}),i("v-uni-view",{staticClass:"w-382 font-wei-500 font-52 text-3 text-center"},[i("v-uni-text",{staticClass:"font-32"},[t._v("¥")]),t._v(t._s(t.bidPrice))],1),i("v-uni-image",{staticClass:"w-96 h-96",attrs:{src:t.ossIcon("/auction/icon_add_h.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onCalcBidPrice("add")}}})],1),i("v-uni-view",{staticClass:"mt-32 text-9 font-22 text-center"},[t._v("参与拍卖竞拍时，需要输入“当前价格+至少一个加价幅度”的金额。")])],1),i("v-uni-view",{staticClass:"h-08 bg-f7f7f7"}),i("v-uni-view",{staticClass:"mt-20 ptb-00-plr-48 flex-sb-c"},[i("v-uni-text",{staticClass:"font-wei-500 font-28 text-3"},[t._v("匿名购买")]),i("v-uni-image",{staticClass:"w-56 h-26",attrs:{src:t.ossIcon("/auction/switch"+(t.isAnonymous?"_h":"")+"_56_26.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.isAnonymous=!t.isAnonymous}}})],1),i("v-uni-button",{staticClass:"vh-btn flex-c-c mtb-00-mlr-auto mt-40 w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onBid.apply(void 0,arguments)}}},[t._v("提交")])],1)],1)},a=[]},f576:function(t,e,i){"use strict";var n=i("3fb5"),r=i("93e6"),a=i("8707").Buffer,o=new Array(16);function s(){r.call(this,64),this._a=1732584193,this._b=4023233417,this._c=2562383102,this._d=271733878}function c(t,e){return t<<e|t>>>32-e}function f(t,e,i,n,r,a,o){return c(t+(e&i|~e&n)+r+a|0,o)+e|0}function u(t,e,i,n,r,a,o){return c(t+(e&n|i&~n)+r+a|0,o)+e|0}function d(t,e,i,n,r,a,o){return c(t+(e^i^n)+r+a|0,o)+e|0}function h(t,e,i,n,r,a,o){return c(t+(i^(e|~n))+r+a|0,o)+e|0}n(s,r),s.prototype._update=function(){for(var t=o,e=0;e<16;++e)t[e]=this._block.readInt32LE(4*e);var i=this._a,n=this._b,r=this._c,a=this._d;i=f(i,n,r,a,t[0],3614090360,7),a=f(a,i,n,r,t[1],3905402710,12),r=f(r,a,i,n,t[2],606105819,17),n=f(n,r,a,i,t[3],3250441966,22),i=f(i,n,r,a,t[4],4118548399,7),a=f(a,i,n,r,t[5],1200080426,12),r=f(r,a,i,n,t[6],2821735955,17),n=f(n,r,a,i,t[7],4249261313,22),i=f(i,n,r,a,t[8],1770035416,7),a=f(a,i,n,r,t[9],2336552879,12),r=f(r,a,i,n,t[10],4294925233,17),n=f(n,r,a,i,t[11],2304563134,22),i=f(i,n,r,a,t[12],1804603682,7),a=f(a,i,n,r,t[13],4254626195,12),r=f(r,a,i,n,t[14],2792965006,17),n=f(n,r,a,i,t[15],1236535329,22),i=u(i,n,r,a,t[1],4129170786,5),a=u(a,i,n,r,t[6],3225465664,9),r=u(r,a,i,n,t[11],643717713,14),n=u(n,r,a,i,t[0],3921069994,20),i=u(i,n,r,a,t[5],3593408605,5),a=u(a,i,n,r,t[10],38016083,9),r=u(r,a,i,n,t[15],3634488961,14),n=u(n,r,a,i,t[4],3889429448,20),i=u(i,n,r,a,t[9],568446438,5),a=u(a,i,n,r,t[14],3275163606,9),r=u(r,a,i,n,t[3],4107603335,14),n=u(n,r,a,i,t[8],1163531501,20),i=u(i,n,r,a,t[13],2850285829,5),a=u(a,i,n,r,t[2],4243563512,9),r=u(r,a,i,n,t[7],1735328473,14),n=u(n,r,a,i,t[12],2368359562,20),i=d(i,n,r,a,t[5],4294588738,4),a=d(a,i,n,r,t[8],2272392833,11),r=d(r,a,i,n,t[11],1839030562,16),n=d(n,r,a,i,t[14],4259657740,23),i=d(i,n,r,a,t[1],2763975236,4),a=d(a,i,n,r,t[4],1272893353,11),r=d(r,a,i,n,t[7],4139469664,16),n=d(n,r,a,i,t[10],3200236656,23),i=d(i,n,r,a,t[13],681279174,4),a=d(a,i,n,r,t[0],3936430074,11),r=d(r,a,i,n,t[3],3572445317,16),n=d(n,r,a,i,t[6],76029189,23),i=d(i,n,r,a,t[9],3654602809,4),a=d(a,i,n,r,t[12],3873151461,11),r=d(r,a,i,n,t[15],530742520,16),n=d(n,r,a,i,t[2],3299628645,23),i=h(i,n,r,a,t[0],4096336452,6),a=h(a,i,n,r,t[7],1126891415,10),r=h(r,a,i,n,t[14],2878612391,15),n=h(n,r,a,i,t[5],4237533241,21),i=h(i,n,r,a,t[12],1700485571,6),a=h(a,i,n,r,t[3],2399980690,10),r=h(r,a,i,n,t[10],4293915773,15),n=h(n,r,a,i,t[1],2240044497,21),i=h(i,n,r,a,t[8],1873313359,6),a=h(a,i,n,r,t[15],4264355552,10),r=h(r,a,i,n,t[6],2734768916,15),n=h(n,r,a,i,t[13],1309151649,21),i=h(i,n,r,a,t[4],4149444226,6),a=h(a,i,n,r,t[11],3174756917,10),r=h(r,a,i,n,t[2],718787259,15),n=h(n,r,a,i,t[9],3951481745,21),this._a=this._a+i|0,this._b=this._b+n|0,this._c=this._c+r|0,this._d=this._d+a|0},s.prototype._digest=function(){this._block[this._blockOffset++]=128,this._blockOffset>56&&(this._block.fill(0,this._blockOffset,64),this._update(),this._blockOffset=0),this._block.fill(0,this._blockOffset,56),this._block.writeUInt32LE(this._length[0],56),this._block.writeUInt32LE(this._length[1],60),this._update();var t=a.allocUnsafe(16);return t.writeInt32LE(this._a,0),t.writeInt32LE(this._b,4),t.writeInt32LE(this._c,8),t.writeInt32LE(this._d,12),t},t.exports=s},f99f:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{value:{type:Boolean,default:!0}},data:function(){return{}},computed:{},methods:{onInput:function(t){this.$emit("input",t)}}};e.default=n},faa1:function(t,e,i){"use strict";var n,r="object"===typeof Reflect?Reflect:null,a=r&&"function"===typeof r.apply?r.apply:function(t,e,i){return Function.prototype.apply.call(t,e,i)};n=r&&"function"===typeof r.ownKeys?r.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var o=Number.isNaN||function(t){return t!==t};function s(){s.init.call(this)}t.exports=s,t.exports.once=function(t,e){return new Promise((function(i,n){function r(i){t.removeListener(e,a),n(i)}function a(){"function"===typeof t.removeListener&&t.removeListener("error",r),i([].slice.call(arguments))}m(t,e,a,{once:!0}),"error"!==e&&function(t,e,i){"function"===typeof t.on&&m(t,"error",e,i)}(t,r,{once:!0})}))},s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var c=10;function f(t){if("function"!==typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function u(t){return void 0===t._maxListeners?s.defaultMaxListeners:t._maxListeners}function d(t,e,i,n){var r,a,o;if(f(i),a=t._events,void 0===a?(a=t._events=Object.create(null),t._eventsCount=0):(void 0!==a.newListener&&(t.emit("newListener",e,i.listener?i.listener:i),a=t._events),o=a[e]),void 0===o)o=a[e]=i,++t._eventsCount;else if("function"===typeof o?o=a[e]=n?[i,o]:[o,i]:n?o.unshift(i):o.push(i),r=u(t),r>0&&o.length>r&&!o.warned){o.warned=!0;var s=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");s.name="MaxListenersExceededWarning",s.emitter=t,s.type=e,s.count=o.length,function(t){console&&console.warn&&console.warn(t)}(s)}return t}function h(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function l(t,e,i){var n={fired:!1,wrapFn:void 0,target:t,type:e,listener:i},r=h.bind(n);return r.listener=i,n.wrapFn=r,r}function p(t,e,i){var n=t._events;if(void 0===n)return[];var r=n[e];return void 0===r?[]:"function"===typeof r?i?[r.listener||r]:[r]:i?function(t){for(var e=new Array(t.length),i=0;i<e.length;++i)e[i]=t[i].listener||t[i];return e}(r):v(r,r.length)}function b(t){var e=this._events;if(void 0!==e){var i=e[t];if("function"===typeof i)return 1;if(void 0!==i)return i.length}return 0}function v(t,e){for(var i=new Array(e),n=0;n<e;++n)i[n]=t[n];return i}function m(t,e,i,n){if("function"===typeof t.on)n.once?t.once(e,i):t.on(e,i);else{if("function"!==typeof t.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t);t.addEventListener(e,(function r(a){n.once&&t.removeEventListener(e,r),i(a)}))}}Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return c},set:function(t){if("number"!==typeof t||t<0||o(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");c=t}}),s.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(t){if("number"!==typeof t||t<0||o(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},s.prototype.getMaxListeners=function(){return u(this)},s.prototype.emit=function(t){for(var e=[],i=1;i<arguments.length;i++)e.push(arguments[i]);var n="error"===t,r=this._events;if(void 0!==r)n=n&&void 0===r.error;else if(!n)return!1;if(n){var o;if(e.length>0&&(o=e[0]),o instanceof Error)throw o;var s=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw s.context=o,s}var c=r[t];if(void 0===c)return!1;if("function"===typeof c)a(c,this,e);else{var f=c.length,u=v(c,f);for(i=0;i<f;++i)a(u[i],this,e)}return!0},s.prototype.addListener=function(t,e){return d(this,t,e,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(t,e){return d(this,t,e,!0)},s.prototype.once=function(t,e){return f(e),this.on(t,l(this,t,e)),this},s.prototype.prependOnceListener=function(t,e){return f(e),this.prependListener(t,l(this,t,e)),this},s.prototype.removeListener=function(t,e){var i,n,r,a,o;if(f(e),n=this._events,void 0===n)return this;if(i=n[t],void 0===i)return this;if(i===e||i.listener===e)0===--this._eventsCount?this._events=Object.create(null):(delete n[t],n.removeListener&&this.emit("removeListener",t,i.listener||e));else if("function"!==typeof i){for(r=-1,a=i.length-1;a>=0;a--)if(i[a]===e||i[a].listener===e){o=i[a].listener,r=a;break}if(r<0)return this;0===r?i.shift():function(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}(i,r),1===i.length&&(n[t]=i[0]),void 0!==n.removeListener&&this.emit("removeListener",t,o||e)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(t){var e,i,n;if(i=this._events,void 0===i)return this;if(void 0===i.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==i[t]&&(0===--this._eventsCount?this._events=Object.create(null):delete i[t]),this;if(0===arguments.length){var r,a=Object.keys(i);for(n=0;n<a.length;++n)r=a[n],"removeListener"!==r&&this.removeAllListeners(r);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(e=i[t],"function"===typeof e)this.removeListener(t,e);else if(void 0!==e)for(n=e.length-1;n>=0;n--)this.removeListener(t,e[n]);return this},s.prototype.listeners=function(t){return p(this,t,!0)},s.prototype.rawListeners=function(t){return p(this,t,!1)},s.listenerCount=function(t,e){return"function"===typeof t.listenerCount?t.listenerCount(e):b.call(t,e)},s.prototype.listenerCount=b,s.prototype.eventNames=function(){return this._eventsCount>0?n(this._events):[]}},fda6:function(t,e,i){var n=i("8947"),r=i("4228"),a=i("e85f");e.createCipher=e.Cipher=n.createCipher,e.createCipheriv=e.Cipheriv=n.createCipheriv,e.createDecipher=e.Decipher=r.createDecipher,e.createDecipheriv=e.Decipheriv=r.createDecipheriv,e.listCiphers=e.getCiphers=function(){return Object.keys(a)}},fdac:function(t,e,i){var n;function r(t){this.rand=t}if(t.exports=function(t){return n||(n=new r(null)),n.generate(t)},t.exports.Rand=r,r.prototype.generate=function(t){return this._rand(t)},r.prototype._rand=function(t){if(this.rand.getBytes)return this.rand.getBytes(t);for(var e=new Uint8Array(t),i=0;i<e.length;i++)e[i]=this.rand.getByte();return e},"object"===typeof self)self.crypto&&self.crypto.getRandomValues?r.prototype._rand=function(t){var e=new Uint8Array(t);return self.crypto.getRandomValues(e),e}:self.msCrypto&&self.msCrypto.getRandomValues?r.prototype._rand=function(t){var e=new Uint8Array(t);return self.msCrypto.getRandomValues(e),e}:"object"===typeof window&&(r.prototype._rand=function(){throw new Error("Not implemented yet")});else try{var a=i(4);if("function"!==typeof a.randomBytes)throw new Error("Not supported");r.prototype._rand=function(t){return a.randomBytes(t)}}catch(o){}},fe0d:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={props:{goods:{type:Object,default:function(){return{}}}},computed:{tips:function(){return[{title:"关于拍品",list:["我司就标的的瑕疵、品质、价值及真伪不承担担保责任。所有拍品均由委托人提供，拍卖标的均以拍卖时的状态出售，竟买人可在预展期间与拍卖人 (客服) 联系查看标的情况。","所有拍品均经严格审核，但受保存条件、批次等因素影响，".concat(1===this.goods.product_type?"老酒均存在一定程度的品相瑕疵(如:附件缺失、破损、污渍、水位较低等)，":"","瑕疵描述以图片为主，文字仅作参考。"),"因标的资料及版本众多，平台会尽力进行准确描述，但不对标的物的任何口头或书面描述，或当中任何错误或缺失负责，所有标的信息以展示图片为主，文字仅作参考。","竞买人既参与竞拍，则视为对拍卖标的的信息内容、瑕疵、品质价值、真伪已有充分了解、认可、接受，并对自己的竞买行为负责，一经拍出，概不退换!"]},{title:"关于验收",list:1===this.goods.product_type?["由于老酒具有一定的特殊性，运输过程中的轻微漏液、脱胶、封口破裂等属正常情况。收货请当面验收，如外在包装、标签、税条等轻微破损，且漏液在5%内，不予退赔。","如有严重破损可拒签，因运输不当造成的破损且漏液超过5%，或酒瓶有较明显破损的，可予以退赔，签收即代表确认无误，如未验货而直接签收的，损失由客户自行承担，后续概不负责。"]:["因运输不当造成的损坏，或者外观破损严重的可拒签，签收即代表确认无误如未验货而直接签收的，损失由客户自行承担，后续概不负责。"]}]}}};e.default=n}}]);