(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageE-pages-address-add-address-add~packageE-pages-invoice-head-add-invoice-head-add~packageE-pag~89bc228d"],{"062a":function(e,t,n){var a=n("aab3");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("2db15654",a,!0,{sourceMap:!1,shadowMode:!1})},"0727":function(e,t,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("99af"),n("14d9"),n("d81d"),n("a434");var r=n("26cb"),o={name:"vh-region",props:{maskCloseAble:{type:Boolean,default:!0},safeAreaInsetBottom:{type:Boolean,default:!1},cancelColor:{type:String,default:"#606266"},cancelText:{type:String,default:"取消"},title:{type:String,default:""},confirmColor:{type:String,default:"#E80404"},confirmText:{type:String,default:"确认"},params:{type:Object,default:function(){return{province:!0,city:!0,area:!0}}},value:{type:Boolean,default:!1},zIndex:{type:[String,Number],default:0}},data:function(){return{reset:!1,valueArr:[],provinces:[],citys:"",areas:"",province:0,city:0,area:0,moving:!1}},created:function(){this.provinces=this.regionInfo.provinces,this.citys=this.regionInfo.citys[0],this.areas=this.regionInfo.areas[0][0]},mounted:function(){this.init()},computed:(0,i.default)((0,i.default)({},(0,r.mapState)(["regionInfo"])),{},{regionChange:function(){return"".concat(this.province,"-").concat(this.city)},uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}}),watch:{propsChange:function(){var e=this;this.reset=!0,setTimeout((function(){return e.init()}),10)},regionChange:function(e){this.citys=this.regionInfo.citys[this.province],this.areas=this.regionInfo.areas[this.province][this.city]},value:function(e){var t=this;e&&(this.reset=!0,setTimeout((function(){return t.init()}),10))}},methods:{pickstart:function(){},pickend:function(){},init:function(){this.valueArr=[],this.reset=!1,this.params.province&&(this.valueArr.push(0),this.setProvinces()),this.params.city&&(this.valueArr.push(0),this.setCitys()),this.params.area&&(this.valueArr.push(0),this.setAreas()),this.$forceUpdate()},setProvinces:function(){if(this.params.province){var e=0;this.regionInfo.provinces.map((function(t,n){t.label==e&&(e=n)})),this.province=e,this.provinces=this.regionInfo.provinces,this.valueArr.splice(0,1,this.province)}},setCitys:function(){if(this.params.city){var e=0;this.regionInfo.citys[this.province].map((function(t,n){t.label==e&&(e=n)})),this.city=e,this.citys=this.regionInfo.citys[this.province],this.valueArr.splice(1,1,this.city)}},setAreas:function(){if(this.params.area){var e=0;this.regionInfo.areas[this.province][this.city].map((function(t,n){t.label==e&&(e=n)})),this.area=e,this.areas=this.regionInfo.areas[this.province][this.city],this.valueArr.splice(2,1,this.area)}},close:function(){this.$emit("input",!1)},change:function(e){this.valueArr=e.detail.value;var t=0;this.params.province&&(this.province=this.valueArr[t++]),this.params.city&&(this.city=this.valueArr[t++]),this.params.area&&(this.area=this.valueArr[t++])},getResult:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t={};this.params.province&&(t.province=this.regionInfo.provinces[this.province]),this.params.city&&(t.city=this.regionInfo.citys[this.province][this.city]),this.params.area&&(t.area=this.regionInfo.areas[this.province][this.city][this.area]),e&&this.$emit(e,t),this.close()}}};t.default=o},"408f":function(e,t,n){"use strict";var a=n("9751"),i=n.n(a);i.a},"4f1b":function(e,t,n){"use strict";n.r(t);var a=n("825d"),i=n("8e1d");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("fa94");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"4ed92bb2",null,!1,a["a"],void 0);t["default"]=s.exports},"825d":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+e.size,e.plain?"u-btn--"+e.type+"--plain":"",e.loading?"u-loading":"","circle"==e.shape?"u-round-circle":"",e.hairLine?e.showHairLineBorder:"u-btn--bold-border","u-btn--"+e.type,e.disabled?"u-btn--"+e.type+"--disabled":""],style:[e.customStyle,{overflow:e.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(e.hoverStartTime),"hover-stay-time":Number(e.hoverStayTime),disabled:e.disabled,"form-type":e.formType,"open-type":e.openType,"app-parameter":e.appParameter,"hover-stop-propagation":e.hoverStopPropagation,"send-message-title":e.sendMessageTitle,"send-message-path":"sendMessagePath",lang:e.lang,"data-name":e.dataName,"session-from":e.sessionFrom,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"hover-class":e.getHoverClass,loading:e.loading},on:{getphonenumber:function(t){arguments[0]=t=e.$handleEvent(t),e.getphonenumber.apply(void 0,arguments)},getuserinfo:function(t){arguments[0]=t=e.$handleEvent(t),e.getuserinfo.apply(void 0,arguments)},error:function(t){arguments[0]=t=e.$handleEvent(t),e.error.apply(void 0,arguments)},opensetting:function(t){arguments[0]=t=e.$handleEvent(t),e.opensetting.apply(void 0,arguments)},launchapp:function(t){arguments[0]=t=e.$handleEvent(t),e.launchapp.apply(void 0,arguments)},click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.click(t)}}},[e._t("default"),e.ripple?n("v-uni-view",{staticClass:"u-wave-ripple",class:[e.waveActive?"u-wave-active":""],style:{top:e.rippleTop+"px",left:e.rippleLeft+"px",width:e.fields.targetWidth+"px",height:e.fields.targetWidth+"px","background-color":e.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):e._e()],2)},i=[]},"8e1d":function(e,t,n){"use strict";n.r(t);var a=n("9476"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},9476:function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("a9e3"),n("c975"),n("d3b7"),n("ac1f");var a={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var e;return e=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",e},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(e){var t=this;this.$u.throttle((function(){!0!==t.loading&&!0!==t.disabled&&(t.ripple&&(t.waveActive=!1,t.$nextTick((function(){this.getWaveQuery(e)}))),t.$emit("click",e))}),this.throttleTime)},getWaveQuery:function(e){var t=this;this.getElQuery().then((function(n){var a=n[0];if(a.width&&a.width&&(a.targetWidth=a.height>a.width?a.height:a.width,a.targetWidth)){t.fields=a;var i,r;i=e.touches[0].clientX,r=e.touches[0].clientY,t.rippleTop=r-a.top-a.targetWidth/2,t.rippleLeft=i-a.left-a.targetWidth/2,t.$nextTick((function(){t.waveActive=!0}))}}))},getElQuery:function(){var e=this;return new Promise((function(t){var n="";n=uni.createSelectorQuery().in(e),n.select(".u-btn").boundingClientRect(),n.exec((function(e){t(e)}))}))},getphonenumber:function(e){this.$emit("getphonenumber",e)},getuserinfo:function(e){this.$emit("getuserinfo",e)},error:function(e){this.$emit("error",e)},opensetting:function(e){this.$emit("opensetting",e)},launchapp:function(e){this.$emit("launchapp",e)}}};t.default=a},9751:function(e,t,n){var a=n("d25e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("c5a9f45e",a,!0,{sourceMap:!1,shadowMode:!1})},aab3:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),e.exports=t},b94e:function(e,t,n){"use strict";n.r(t);var a=n("f2dd"),i=n("fdb6");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("408f");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"1dc592e8",null,!1,a["a"],void 0);t["default"]=s.exports},d25e:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.region-header[data-v-1dc592e8]{width:100%;height:%?90?%;padding:0 %?40?%;display:flex;justify-content:space-between;align-items:center;box-sizing:border-box;font-size:%?30?%;background:#fff;position:relative}.region-header[data-v-1dc592e8]::after{content:"";position:absolute;border-bottom:%?1?% solid #eaeef1;-webkit-transform:scaleY(.5);transform:scaleY(.5);bottom:0;right:0;left:0}',""]),e.exports=t},f2dd:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var a={uPopup:n("c4b0").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("u-popup",{attrs:{maskCloseAble:e.maskCloseAble,mode:"bottom",popup:!1,length:"auto",safeAreaInsetBottom:e.safeAreaInsetBottom,"z-index":e.uZIndex},on:{close:function(t){arguments[0]=t=e.$handleEvent(t),e.close.apply(void 0,arguments)}},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},[n("v-uni-view",{staticClass:"p-rela z-999"},[n("v-uni-view",{staticClass:"region-header",on:{touchmove:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t)}}},[n("v-uni-view",{staticClass:"bs-bb p-16 text-center",style:{color:e.cancelColor},attrs:{"hover-class":"op-050","hover-stay-time":150},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getResult("cancel")}}},[e._v(e._s(e.cancelText))]),n("v-uni-view",{staticClass:"text-3"},[e._v(e._s(e.title))]),n("v-uni-view",{staticClass:"bs-bb p-16 text-center",style:{color:e.moving?e.cancelColor:e.confirmColor},attrs:{"hover-class":"op-050","hover-stay-time":150},on:{touchmove:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t)},click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.getResult("confirm")}}},[e._v(e._s(e.confirmText))])],1),n("v-uni-view",{staticClass:"w-p100 h-500 bg-ffffff o-hid"},[n("v-uni-picker-view",{staticClass:"h-p100 bs-bb",attrs:{value:e.valueArr},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.change.apply(void 0,arguments)},pickstart:function(t){arguments[0]=t=e.$handleEvent(t),e.pickstart.apply(void 0,arguments)},pickend:function(t){arguments[0]=t=e.$handleEvent(t),e.pickend.apply(void 0,arguments)}}},[!e.reset&&e.params.province?n("v-uni-picker-view-column",e._l(e.provinces,(function(t,a){return n("v-uni-view",{key:a,staticClass:"d-flex j-center a-center ptb-00-plr-08 font-32 text-3"},[n("v-uni-view",{staticClass:"o-hid w-s-now t-o-ell"},[e._v(e._s(t.label))])],1)})),1):e._e(),!e.reset&&e.params.city?n("v-uni-picker-view-column",e._l(e.citys,(function(t,a){return n("v-uni-view",{key:a,staticClass:"d-flex j-center a-center ptb-00-plr-08 font-32 text-3"},[n("v-uni-view",{staticClass:"o-hid w-s-now t-o-ell"},[e._v(e._s(t.label))])],1)})),1):e._e(),!e.reset&&e.params.area?n("v-uni-picker-view-column",e._l(e.areas,(function(t,a){return n("v-uni-view",{key:a,staticClass:"d-flex j-center a-center ptb-00-plr-08 font-32 text-3"},[n("v-uni-view",{staticClass:"o-hid w-s-now t-o-ell"},[e._v(e._s(t.label))])],1)})),1):e._e()],1)],1)],1)],1)},r=[]},fa94:function(e,t,n){"use strict";var a=n("062a"),i=n.n(a);i.a},fdb6:function(e,t,n){"use strict";n.r(t);var a=n("0727"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a}}]);