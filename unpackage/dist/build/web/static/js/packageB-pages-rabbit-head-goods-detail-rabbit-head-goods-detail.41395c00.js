(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-rabbit-head-goods-detail-rabbit-head-goods-detail"],{"062a":function(t,e,i){var a=i("aab3");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("2db15654",a,!0,{sourceMap:!1,shadowMode:!1})},"0792":function(t,e){var i={errorImg:null,filter:null,highlight:null,onText:null,entities:{quot:'"',apos:"'",semi:";",nbsp:" ",ensp:" ",emsp:" ",ndash:"–",mdash:"—",middot:"·",lsquo:"‘",rsquo:"’",ldquo:"“",rdquo:"”",bull:"•",hellip:"…"},blankChar:a(" , ,\t,\r,\n,\f"),boolAttrs:a("allowfullscreen,autoplay,autostart,controls,ignore,loop,muted"),blockTags:a("address,article,aside,body,caption,center,cite,footer,header,html,nav,pre,section"),ignoreTags:a("area,base,canvas,frame,iframe,input,link,map,meta,param,script,source,style,svg,textarea,title,track,wbr"),richOnlyTags:a("a,colgroup,fieldset,legend"),selfClosingTags:a("area,base,br,col,circle,ellipse,embed,frame,hr,img,input,line,link,meta,param,path,polygon,rect,source,track,use,wbr"),trustTags:a("a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video"),userAgentStyles:{address:"font-style:italic",big:"display:inline;font-size:1.2em",blockquote:"background-color:#f6f6f6;border-left:3px solid #dbdbdb;color:#6c6c6c;padding:5px 0 5px 10px",caption:"display:table-caption;text-align:center",center:"text-align:center",cite:"font-style:italic",dd:"margin-left:40px",mark:"background-color:yellow",pre:"font-family:monospace;white-space:pre;overflow:scroll",s:"text-decoration:line-through",small:"display:inline;font-size:0.8em",u:"text-decoration:underline"}};function a(t){for(var e=Object.create(null),i=t.split(","),a=i.length;a--;)e[i[a]]=!0;return e}t.exports=i},"0efb":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"vh-image-con",class:[t.isMf?"mf-card":""],style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():i("v-uni-image",{staticClass:"vh-image",style:{backgroundColor:t.bgColor,borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.getImage,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?i("v-uni-view",{staticClass:"vh-image-loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[i("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e(),t.showError&&t.isError&&!t.loading?i("v-uni-view",{staticClass:"u-image-error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[i("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e()],1)},n=[]},"12c6":function(t,e,i){"use strict";i.r(e);var a=i("51bd"),n=i("f074");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("f2f9");var o=i("f0c5"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);e["default"]=s.exports},1677:function(t,e,i){"use strict";i.r(e);var a=i("f440"),n=i("724e");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);var o=i("f0c5"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"4efb453b",null,!1,a["a"],void 0);e["default"]=s.exports},"2c89":function(t,e,i){"use strict";i.r(e);var a=i("d97b"),n=i("b2cf");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("8d13");var o=i("f0c5"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"5189efcc",null,!1,a["a"],void 0);e["default"]=s.exports},"3dc3":function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("d0af")),r=a(i("f3f3"));i("a9e3"),i("d3b7"),i("159b"),i("e25e"),i("c975");var o=i("26cb"),s={name:"u-image",props:{loadingType:{type:[String,Number],default:1},isMf:{type:Boolean,default:!1},src:{default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!1},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:100},bgColor:{type:String,default:"transparent"},isResize:{type:Boolean,default:!1},resizeRatio:{type:Object,default:function(){return{wratio:16,hratio:9}}},resizeUsePx:{type:Boolean,default:!1},opacityProp:{type:Number,default:1},backgroundImage:{type:String,default:""}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:(0,r.default)((0,r.default)({},(0,o.mapState)(["ossPrefix"])),{},{wrapStyle:function(){var t={};if(t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),this.backgroundImage&&(t.backgroundImage="url(".concat(this.backgroundImage,")"),t.backgroundSize="100% 100%",t.backgroundRepeat="no-repeat",t.backgroundPosition="center"),this.isResize){var e,i,a=(null===this||void 0===this||null===(e=this.src)||void 0===e||null===(i=e.split("?"))||void 0===i?void 0:i[1])||"",r=a.split("&"),o={};r.forEach((function(t){var e=t.split("="),i=(0,n.default)(e,2),a=i[0],r=i[1];o[a]=r}));var s=+((null===o||void 0===o?void 0:o.w)||""),l=+((null===o||void 0===o?void 0:o.h)||"");if(!isNaN(s)&&!isNaN(l)&&s&&l){var c=parseInt(this.width),d=c/s*l,u=this.resizeRatio,f=u.wratio,p=u.hratio;if("auto"!==f&&"auto"!==p){var h=c*f/p,b=c*p/f;d>h?d=h:d<b&&(d=b)}this.resizeUsePx?t.height="".concat(d,"px"):t.height=this.$u.addUnit(d)}}return t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0||"circle"==this.shape?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t},getLoadingImage:function(){return"https://images.vinehoo.com/vinehoomini/v3/occupy_img/new_img".concat(this.loadingType,".png")},getImage:function(){return"string"!==typeof this.src?"":-1==this.src.indexOf("http")?this.ossPrefix+this.src:this.src}}),methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=t.opacityProp,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=s},"430e":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={vhNavbar:i("12c6").default,uIcon:i("e5e1").default,vhGap:i("1677").default,uParse:i("2c89").default,uButton:i("4f1b").default,uPopup:i("c4b0").default,vhImage:i("ce7c").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"content"},[i("v-uni-view",{},[""==t.from?i("vh-navbar",{attrs:{title:"商品详情"}}):i("v-uni-view",{staticClass:"p-fixed z-980 top-0 w-p100 bg-ffffff"},[i("v-uni-view",{style:{height:t.appStatusBarHeight+"px"}}),i("v-uni-view",{staticClass:"p-rela h-px-48 d-flex j-center a-center"},[i("v-uni-view",{staticClass:"p-abso left-24 h-p100 d-flex a-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.appJumpBack()}}},[i("u-icon",{attrs:{name:"nav-back",color:"#333",size:44}})],1),i("v-uni-view",{staticClass:"font-36 font-wei text-3"},[t._v("商品详情")]),i("v-uni-image",{directives:[{name:"show",rawName:"v-show",value:"1"==t.from||"2"==t.from,expression:"from == '1' || from == '2'"}],staticClass:"p-abso right-0 w-56 h-56 p-14",attrs:{src:t.ossIcon("/goods_detail/nav_sha_bla.png")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onShare.apply(void 0,arguments)}}})],1)],1)],1),i("v-uni-image",{staticClass:"w-p100 h-462",style:{paddingTop:""==t.from?"0px":parseInt(t.appStatusBarHeight)+48+"px"},attrs:{src:t.rabbitGoodsInfo.banner_img&&t.rabbitGoodsInfo.banner_img[0],mode:"aspectFill"}}),i("v-uni-view",{staticClass:"pt-26 pr-24 pb-32 pl-24"},[i("v-uni-view",{staticClass:"d-flex j-sb a-center"},[i("v-uni-view",{},[i("v-uni-text",{staticClass:"font-52 font-wei text-e80404"},[t._v(t._s(t.rabbitGoodsInfo.rabbit))]),i("v-uni-image",{staticClass:"ml-04 w-26 h-28",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png",mode:"widthFix"}})],1),i("v-uni-view",{staticClass:"font-28 text-6"},[t._v("已兑"),i("v-uni-text",{staticClass:"font-40 text-e80404"},[t._v(t._s(t.limitInfo.aleradyBuy))]),t._v("份")],1)],1),i("v-uni-view",{staticClass:"mt-38 font-30 font-wei text-3"},[t._v(t._s(t.rabbitGoodsInfo.title))]),i("v-uni-view",{staticClass:"mt-12 font-28 text-6"},[t._v(t._s(t.rabbitGoodsInfo.brief))])],1),i("vh-gap",{attrs:{height:"20","bg-color":"#F5F5F5"}}),i("v-uni-view",{staticClass:"d-flex a-center b-rad-10 pt-40 pb-34 pl-24"},[i("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("预计发货时间")]),i("v-uni-text",{staticClass:"ml-24 font-28 text-3"},[t._v(t._s(t.estimatedDeliveryTime))]),i("v-uni-text",{staticClass:"font-28 text-6"},[t._v("（不支持7天无理由退货）")])],1),i("vh-gap",{attrs:{height:"20","bg-color":"#F5F5F5"}}),i("v-uni-view",{staticClass:"pt-32 pl-24 pb-104 pr-24"},[i("v-uni-view",{staticClass:"text-center font-32 font-wei text-3"},[t._v("商品详情")]),i("v-uni-view",{staticClass:"mt-32 w-b-b-w"},[i("u-parse",{attrs:{html:t.rabbitGoodsInfo.detail,"show-with-animation":!0}})],1)],1),i("v-uni-view",{},["3"==t.from?i("v-uni-view",{staticClass:"p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff d-flex j-center a-center b-sh-00021200-022 font-32 font-wei text-e80404"},[t._v("文案预览")]):i("v-uni-view",{directives:[{name:"safeBeautyBottom",rawName:"v-safeBeautyBottom",value:t.$safeBeautyBottom,expression:"$safeBeautyBottom"}],staticClass:"bg-ffffff p-fixed z-999 bottom-0 w-p100 h-104 d-flex j-center a-center b-sh-00021200-022"},[2!=t.packageAllInfo.onsale_status?i("v-uni-view",{},[i("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#DDDDDD",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.feedback.toast({title:t.packageAllInfo.onsale_status<2?"亲，该商品仅限兑换~":"亲，该商品已经兑换结束啦~"})}}},[t._v(t._s(t.packageAllInfo.onsale_status<2?"仅限兑换":"兑换结束"))])],1):i("v-uni-view",{},[i("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.exchangeNow.apply(void 0,arguments)}}},[t._v("立即兑换")])],1)],1)],1),i("v-uni-view",{},[i("u-popup",{attrs:{mode:"bottom","border-radius":20},model:{value:t.showCollPop,callback:function(e){t.showCollPop=e},expression:"showCollPop"}},[i("v-uni-view",{staticClass:"pt-32 pr-24 pb-20 pl-24"},[i("v-uni-view",{staticClass:"d-flex"},[i("v-uni-view",{staticClass:"w-246 h-152 b-rad-06 o-hid"},[i("vh-image",{attrs:{"loading-type":2,src:t.rabbitGoodsInfo.banner_img&&t.rabbitGoodsInfo.banner_img[0],height:152}})],1),i("v-uni-view",{staticClass:"flex-1 d-flex flex-column j-sb ml-16"},[i("v-uni-view",{staticClass:"font-28 text-3 o-hid text-hidden-2"},[t._v(t._s(t.rabbitGoodsInfo.title))]),i("v-uni-view",{staticClass:"d-flex a-center"},[i("v-uni-text",{staticClass:"font-44 font-wei text-e80404"},[t._v(t._s(t.packageInfo.rabbit))]),i("v-uni-image",{staticClass:"w-26 h-28 ml-04",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png",mode:"widthFix"}})],1)],1)],1),i("v-uni-view",{staticClass:"mt-48 font-32 font-wei text-3"},[t._v("规格")]),i("v-uni-view",{staticClass:"d-flex flex-wrap ml-n-24"},t._l(t.packageList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3",class:t.packageIndex==a?"bg-fce4e3 b-s-01-e80404 text-e80404":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectPackage(a)}}},[t._v(t._s(e.package_name))])})),1),i("v-uni-view",{staticClass:"mt-92 d-flex j-center a-center"},[i("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmExchangeNow.apply(void 0,arguments)}}},[t._v("立即兑换")])],1)],1)],1)],1)],1)},r=[]},"4f1b":function(t,e,i){"use strict";i.r(e);var a=i("825d"),n=i("8e1d");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("fa94");var o=i("f0c5"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"4ed92bb2",null,!1,a["a"],void 0);e["default"]=s.exports},"51bd":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return a}));var a={uIcon:i("e5e1").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{},[i("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[i("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),i("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?i("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?i("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[i("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?i("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?i("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[i("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),i("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),i("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?i("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},r=[]},"54f8":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var i="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=(0,a.default)(t))||e&&t&&"number"===typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,l=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return s=t.done,t},e:function(t){l=!0,o=t},f:function(){try{s||null==i["return"]||i["return"]()}finally{if(l)throw o}}}},i("a4d3"),i("e01a"),i("d3b7"),i("d28b"),i("3ca3"),i("ddb0"),i("d9e2"),i("d401");var a=function(t){return t&&t.__esModule?t:{default:t}}(i("dde1"))},"6ab5":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-image-con[data-v-89d76102]{position:relative;transition:opacity .5s ease-in}.vh-image[data-v-89d76102]{width:100%;height:100%}.vh-image-loading[data-v-89d76102],\n.u-image-error[data-v-89d76102]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#fff;color:#909399;font-size:%?46?%}.mf-card[data-v-89d76102]{background-color:#f7f7f7!important;padding:5px}',""]),t.exports=e},"724e":function(t,e,i){"use strict";i.r(e);var a=i("820d"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},"7f1a":function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("f3f3"));i("a9e3"),i("caad6"),i("2532");var r=i("26cb"),o=uni.getSystemInfoSync(),s={},l={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,n.default)((0,n.default)({},(0,r.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,i=e.pEAddressAdd,a=e.pEAddressManagement,n=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(i)||t.includes(a)||t.includes(n))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=l},"820d":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var a={name:"vh-gap",props:{bgColor:{type:String,default:"transparent"},height:{type:[String,Number],default:30}},computed:{gapStyle:function(){return{backgroundColor:this.bgColor,height:this.height+"rpx"}}}};e.default=a},"825d":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?i("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},n=[]},8907:function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(i("54f8"));i("c975"),i("caad6"),i("2532"),i("ac1f"),i("466d"),i("841c"),i("5319"),i("e25e"),i("14d9"),i("99af"),i("acd8");var r=uni.getSystemInfoSync(),o=r.windowWidth,s=(r.platform,i("0792")),l={name:"parser",data:function(){return{uid:this._uid,showAm:"",nodes:[]}},props:{html:String,autopause:{type:Boolean,default:!0},autoscroll:Boolean,autosetTitle:{type:Boolean,default:!0},domain:String,lazyLoad:Boolean,selectable:Boolean,tagStyle:Object,showWithAnimation:Boolean,useAnchor:Boolean},watch:{html:function(t){this.setContent(t)}},created:function(){this.imgList=[],this.imgList.each=function(t){for(var e=0,i=this.length;e<i;e++)this.setItem(e,t(this[e],e,this))},this.imgList.setItem=function(t,e){if(void 0!=t&&e){if(0==e.indexOf("http")&&this.includes(e)){for(var i,a=e.split("://")[0],n=a.length;i=e[n];n++){if("/"==i&&"/"!=e[n-1]&&"/"!=e[n+1])break;a+=Math.random()>.5?i.toUpperCase():i}return a+=e.substr(n),this[t]=a}if(this[t]=e,e.includes("data:image")){var r=e.match(/data:image\/(\S+?);(\S+?),(.+)/);if(!r)return}}}},mounted:function(){this.document=document.getElementById("rtf"+this._uid),this.html&&this.setContent(this.html)},beforeDestroy:function(){this._observer&&this._observer.disconnect(),this.imgList.each((function(t){})),clearInterval(this._timer)},methods:{setContent:function(t,e){var i=this;if(t){var a=document.createElement("div");e?this.rtf?this.rtf.appendChild(a):this.rtf=a:(this.rtf&&this.rtf.parentNode.removeChild(this.rtf),this.rtf=a),a.innerHTML=this._handleHtml(t,e);for(var r,l=this.rtf.getElementsByTagName("style"),c=0;r=l[c++];)r.innerHTML=r.innerHTML.replace(/body/g,"#rtf"+this._uid),r.setAttribute("scoped","true");!this._observer&&this.lazyLoad&&IntersectionObserver&&(this._observer=new IntersectionObserver((function(t){for(var e,a=0;e=t[a++];)e.isIntersecting&&(e.target.src=e.target.getAttribute("data-src"),e.target.removeAttribute("data-src"),i._observer.unobserve(e.target))}),{rootMargin:"500px 0px 500px 0px"}));var d=this,u=this.rtf.getElementsByTagName("title");u.length&&this.autosetTitle&&uni.setNavigationBarTitle({title:u[0].innerText});var f=function(t){var e=t.getAttribute("src");i.domain&&e&&("/"==e[0]?"/"==e[1]?t.src=(i.domain.includes("://")?i.domain.split("://")[0]:"")+":"+e:t.src=i.domain+e:e.includes("://")||0==e.indexOf("data:")||(t.src=i.domain+"/"+e))};this.imgList.length=0;for(var p,h=this.rtf.getElementsByTagName("img"),b=0,v=0;p=h[b];b++)parseInt(p.style.width||p.getAttribute("width"))>o&&(p.style.height="auto"),f(p),p.hasAttribute("ignore")||"A"==p.parentElement.nodeName||(p.i=v++,d.imgList.push(p.getAttribute("original-src")||p.src||p.getAttribute("data-src")),p.onclick=function(t){t.stopPropagation();var e=!0;this.ignore=function(){return e=!1},d.$emit("imgtap",this),e&&uni.previewImage({current:this.i,urls:d.imgList})}),p.onerror=function(){s.errorImg&&(d.imgList[this.i]=this.src=s.errorImg),d.$emit("error",{source:"img",target:this})},d.lazyLoad&&this._observer&&p.src&&0!=p.i&&(p.setAttribute("data-src",p.src),p.removeAttribute("src"),this._observer.observe(p));var g,m=this.rtf.getElementsByTagName("a"),y=(0,n.default)(m);try{for(y.s();!(g=y.n()).done;){var w=g.value;w.onclick=function(t){t.stopPropagation();var e=!0,i=this.getAttribute("href");if(d.$emit("linkpress",{href:i,ignore:function(){return e=!1}}),e&&i)if("#"==i[0])d.useAnchor&&d.navigateTo({id:i.substr(1)});else{if(0==i.indexOf("http")||0==i.indexOf("//"))return!0;uni.navigateTo({url:i})}return!1}}}catch(L){y.e(L)}finally{y.f()}var x=this.rtf.getElementsByTagName("video");d.videoContexts=x;for(var k,_=0;k=x[_++];)f(k),k.style.maxWidth="100%",k.onerror=function(){d.$emit("error",{source:"video",target:this})},k.onplay=function(){if(d.autopause)for(var t,e=0;t=d.videoContexts[e++];)t!=this&&t.pause()};var S,C,B=this.rtf.getElementsByTagName("audio"),I=(0,n.default)(B);try{for(I.s();!(S=I.n()).done;){var T=S.value;f(T),T.onerror=function(){d.$emit("error",{source:"audio",target:this})}}}catch(L){I.e(L)}finally{I.f()}if(this.autoscroll){var E,$=this.rtf.getElementsByTagName("table"),z=(0,n.default)($);try{for(z.s();!(E=z.n()).done;){var O=E.value,A=document.createElement("div");A.style.overflow="scroll",O.parentNode.replaceChild(A,O),A.appendChild(O)}}catch(L){z.e(L)}finally{z.f()}}e||this.document.appendChild(this.rtf),this.$nextTick((function(){i.nodes=[1],i.$emit("load")})),setTimeout((function(){return i.showAm=""}),500),clearInterval(this._timer),this._timer=setInterval((function(){i.rect=i.rtf.getBoundingClientRect(),i.rect.height==C&&(i.$emit("ready",i.rect),clearInterval(i._timer)),C=i.rect.height}),350),this.showWithAnimation&&!e&&(this.showAm="animation:_show .5s")}else this.rtf&&!e&&this.rtf.parentNode.removeChild(this.rtf)},getText:function(){arguments.length>0&&void 0!==arguments[0]||this.nodes;var t="";return t=this.rtf.innerText,t},in:function(t){t.page&&t.selector&&t.scrollTop&&(this._in=t)},navigateTo:function(t){var e=this;if(!this.useAnchor)return t.fail&&t.fail("Anchor is disabled");var i=uni.createSelectorQuery().in(this._in?this._in.page:this).select((this._in?this._in.selector:"#_top")+(t.id?"".concat(" ","#").concat(t.id,",").concat(this._in?this._in.selector:"#_top").concat(" ",".").concat(t.id):"")).boundingClientRect();this._in?i.select(this._in.selector).scrollOffset().select(this._in.selector).boundingClientRect():i.selectViewport().scrollOffset(),i.exec((function(i){if(!i[0])return t.fail&&t.fail("Label not found");var a=i[1].scrollTop+i[0].top-(i[2]?i[2].top:0)+(t.offset||0);e._in?e._in.page[e._in.scrollTop]=a:uni.pageScrollTo({scrollTop:a,duration:300}),t.success&&t.success()}))},getVideoContext:function(t){if(!t)return this.videoContexts;for(var e=this.videoContexts.length;e--;)if(this.videoContexts[e].id==t)return this.videoContexts[e]},_handleHtml:function(t,e){if(!e){var i="<style scoped>@keyframes _show{0%{opacity:0}100%{opacity:1}}img{max-width:100%}";for(var a in s.userAgentStyles)i+="".concat(a,"{").concat(s.userAgentStyles[a],"}");for(a in this.tagStyle)i+="".concat(a,"{").concat(this.tagStyle[a],"}");i+="</style>",t=i+t}return t.includes("rpx")&&(t=t.replace(/[0-9.]+\s*rpx/g,(function(t){return parseFloat(t)*o/750+"px"}))),t}}};e.default=l},8987:function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,"@-webkit-keyframes _show-data-v-5189efcc{0%{opacity:0}100%{opacity:1}}@keyframes _show-data-v-5189efcc{0%{opacity:0}100%{opacity:1}}\n\n\n\n",""]),t.exports=e},"8d13":function(t,e,i){"use strict";var a=i("f157"),n=i.n(a);n.a},"8e1d":function(t,e,i){"use strict";i.r(e);var a=i("9476"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},9476:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("c975"),i("d3b7"),i("ac1f");var a={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(i){var a=i[0];if(a.width&&a.width&&(a.targetWidth=a.height>a.width?a.height:a.width,a.targetWidth)){e.fields=a;var n,r;n=t.touches[0].clientX,r=t.touches[0].clientY,e.rippleTop=r-a.top-a.targetWidth/2,e.rippleLeft=n-a.left-a.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var i="";i=uni.createSelectorQuery().in(t),i.select(".u-btn").boundingClientRect(),i.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=a},a126:function(t,e,i){var a=i("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},aab3:function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},b252:function(t,e,i){var a=i("6ab5");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("0c30beb4",a,!0,{sourceMap:!1,shadowMode:!1})},b2cf:function(t,e,i){"use strict";i.r(e);var a=i("8907"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},b506:function(t,e,i){"use strict";i.r(e);var a=i("430e"),n=i("e2ed");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);var o=i("f0c5"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"ec1b8d62",null,!1,a["a"],void 0);e["default"]=s.exports},bbdc:function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},ce7c:function(t,e,i){"use strict";i.r(e);var a=i("0efb"),n=i("ea26");for(var r in n)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(r);i("eb5f");var o=i("f0c5"),s=Object(o["a"])(n["default"],a["b"],a["c"],!1,null,"89d76102",null,!1,a["a"],void 0);e["default"]=s.exports},d97b:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[this.nodes.length?this._e():this._t("default"),e("v-uni-view",{style:this.showAm+(this.selectable?";user-select:text;-webkit-user-select:text":""),attrs:{id:"_top"}},[e("div",{attrs:{id:"rtf"+this.uid}})])],2)},n=[]},e2ed:function(t,e,i){"use strict";i.r(e);var a=i("e51b"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},e51b:function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("e25e"),i("4de4"),i("d3b7"),i("ac1f"),i("5319"),i("14d9"),i("e9c4"),i("99af");var n=a(i("f07e")),r=a(i("c964")),o=a(i("f3f3")),s=i("26cb"),l={name:"rabbit-head-goods-detail",data:function(){return{loading:!0,from:"",appStatusBarHeight:"",goodsId:"",rabbitGoodsInfo:{},packageAllInfo:{},limitInfo:{},estimatedDeliveryTime:"",packageList:[],packageIndex:0,packageInfo:{},showCollPop:!1}},computed:(0,o.default)({},(0,s.mapState)(["rabbitOrderInfo","routeTable"])),onLoad:function(t){this.goodsId=parseInt(t.id),t.from&&t.statusBarHeight&&(this.from=t.from,this.appStatusBarHeight=t.statusBarHeight,this.muFrom(this.from)),this.init()},methods:(0,o.default)((0,o.default)({},(0,s.mapMutations)(["muRabbitOrderInfo","muFrom"])),{},{init:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.getRabbitGoodsDetail();case 3:return e.next=5,t.getRabbitPackageDetail();case 5:t.loading=!1,uni.stopPullDownRefresh(),e.next=14;break;case 9:e.prev=9,e.t0=e["catch"](0),console.log("----我报异常了"),console.log(e.t0),t.goBack();case 14:case"end":return e.stop()}}),e,null,[[0,9]])})))()},getRabbitGoodsDetail:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){var i,a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i="3"===t.from?Date.now():1,e.next=3,t.$u.api.goodsDetailJson({t:i,isJson:!0,id:t.goodsId});case 3:a=e.sent,t.rabbitGoodsInfo=a.data;case 5:case"end":return e.stop()}}),e)})))()},getRabbitPackageDetail:function(){var t=this;return(0,r.default)((0,n.default)().mark((function e(){var i,a,r,o,s,l;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i={},i.period=t.goodsId,i.periods_type=t.rabbitGoodsInfo.periods_type,e.next=5,t.$u.api.packageDetail(i);case 5:a=e.sent,r=a.data,o=r.purchased,s=r.limit_number,l=r.packageList,t.packageAllInfo=a.data,t.estimatedDeliveryTime=t.calEstimateTime(),t.packageList=a.data.packageList.filter((function(t){return 0==t.is_hidden})),t.limitInfo={aleradyBuy:o,limitNumber:s},t.packageInfo=l[t.packageIndex];case 12:case"end":return e.stop()}}),e)})))()},goBack:function(){var t=this;setTimeout((function(){t.comes.isFromApp(t.from)?wineYunJsBridge.openAppPage({client_path:{ios_path:"goBack",android_path:"goBack"}}):t.jump.navigateBack()}),1500)},appJumpBack:function(){wineYunJsBridge.openAppPage({client_path:{ios_path:"goBack",android_path:"goBack"}})},calEstimateTime:function(){var t=this.packageAllInfo.predict_shipment_time,e=new Date,i=new Date(t.replace(/-/g,"/")),a=Math.floor((i.getTime()-e.getTime())/1e3);if(a>=0)return console.log("---------预计发货时间大于等于当前时间"),t.substr(0,10);console.log("---------预计发货时间小于当前时间");var n=this.$u.timeFormat(new Date(e.getTime()+864e5).getTime(),"yyyy-mm-dd");return console.log(n),n},selectPackage:function(t){console.log(t),this.packageIndex=t,this.packageInfo=this.packageList[this.packageIndex]},exchangeNow:function(){this.login.isLogin(this.from)&&(this.showCollPop=!0)},confirmExchangeNow:function(){console.log("-----------------确认立即兑换");var t=this.rabbitGoodsInfo,e=t.banner_img,i=t.title,a=this.packageInfo,n=a.rabbit,r=a.package_name,o={rabbitGoodsList:[]},s={};s.period=this.rabbitGoodsInfo.id,s.banner_img=e[0],s.title=i,s.package_id=this.packageInfo.id,s.nums=1,s.package_name=r,s.rabbit=n,s.predict_time=this.estimatedDeliveryTime,o.rabbitGoodsList.push(s),console.log("--------------------------这是确认立即兑换需要的数据"),console.log(o),this.muRabbitOrderInfo(o),this.comes.isFromApp(this.from)?"next"==this.from?(uni.setStorageSync("nextrabbitOrderInfo",o),this.jump.appAndMiniJump(0,"/packageB/pages/rabbit-head-order-confirm/rabbit-head-order-confirm",this.from)):wineYunJsBridge.openAppPage({client_path:{ios_path:"rabbitConfirmOrder",android_path:"rabbitConfirmOrder"},ad_path_param:[{ios_key:"info",ios_val:JSON.stringify(o),android_key:"info",android_val:JSON.stringify(o)}]}):this.jump.navigateTo("../rabbit-head-order-confirm/rabbit-head-order-confirm")},onShare:function(){var t=this.rabbitGoodsInfo,e=t.id,i=t.title,a=t.brief,n=t.banner_img;this.jump.appShare({title:i,des:a,img:n[0],path:"".concat(this.routeTable.pBRabbitHeadGoodsDetail,"?id=").concat(e)})}}),onPullDownRefresh:function(){this.init()}};e.default=l},ea26:function(t,e,i){"use strict";i.r(e);var a=i("3dc3"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},eb5f:function(t,e,i){"use strict";var a=i("b252"),n=i.n(a);n.a},f074:function(t,e,i){"use strict";i.r(e);var a=i("7f1a"),n=i.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(r);e["default"]=n.a},f157:function(t,e,i){var a=i("8987");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("e49b1058",a,!0,{sourceMap:!1,shadowMode:!1})},f2f9:function(t,e,i){"use strict";var a=i("a126"),n=i.n(a);n.a},f440:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{style:[this.gapStyle]})},n=[]},fa94:function(t,e,i){"use strict";var a=i("062a"),n=i.n(a);n.a}}]);