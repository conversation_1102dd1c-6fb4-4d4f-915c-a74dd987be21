(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageE-pages-address-management-address-management"],{"062a":function(e,t,n){var a=n("aab3");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("2db15654",a,!0,{sourceMap:!1,shadowMode:!1})},"0e01":function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("a9e3");var a={name:"vh-check",props:{width:{type:[String,Number],default:32},height:{type:[String,Number],default:32},checked:{type:Boolean,default:!1},checkedImg:{type:String,default:"https://images.vinehoo.com/vinehoomini/v3/comm/cir_sel.png"},unCheckedImg:{type:String,default:"https://images.vinehoo.com/vinehoomini/v3/comm/cir_unsel.png"},mode:{type:String,default:"aspectFill"},marginTop:{type:[String,Number],default:0}},computed:{checkStyle:function(){var e={};return e.marginTop=this.marginTop+"rpx",e.width=this.width+"rpx",e.height=this.height+"rpx",e}},methods:{click:function(){this.$emit("click")}}};t.default=a},"12c6":function(e,t,n){"use strict";n.r(t);var a=n("51bd"),i=n("f074");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("f2f9");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);t["default"]=s.exports},"144f":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var a=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"d-flex j-center",style:[this.outerEmpConStyle]},[t("v-uni-view",{staticClass:"p-rela",style:[this.innEmpConStyle]},[t("v-uni-image",{staticClass:"w-p100 h-p100",attrs:{src:this.imageSrc,mode:"aspectFill"}}),t("v-uni-view",{staticClass:"p-abso w-p100 d-flex j-center font-28 text-9",style:[this.textStyle]},[this._v(this._s(this.text))])],1)],1)},i=[]},2036:function(e,t,n){"use strict";n.r(t);var a=n("d4f3"),i=n("28ff");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"0c73edd7",null,!1,a["a"],void 0);t["default"]=s.exports},"25bf":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var a={vhNavbar:n("12c6").default,vhCheck:n("2036").default,vhEmpty:n("5ba4").default,uButton:n("4f1b").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"content bg-ffffff"},[n("vh-navbar",{attrs:{title:"地址管理","show-border":!0}},[n("v-uni-view",{staticClass:"d-flex a-center ml-10 w-s-now font-30 text-3",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.isShowEdit=!e.isShowEdit}}},[e._v(e._s(e.isShowEdit?"完成":"编辑"))])],1),n("v-uni-view",{},[e.addressList.length>0?n("v-uni-view",{},e._l(e.addressList,(function(t,a){return n("v-uni-view",{key:t.id,staticClass:"p-rela",on:{longpress:function(n){arguments[0]=n=e.$handleEvent(n),e.openMaskOperation(t)},click:function(n){n.stopPropagation(),arguments[0]=n=e.$handleEvent(n),e.jumpPage(t)}}},[n("v-uni-view",{staticClass:"d-flex j-sb a-center ml-36 mr-36 pt-40 pb-32 bb-s-01-eeeeee"},[e.isShowEdit?n("v-uni-view",{staticClass:"mr-10"},[n("vh-check",{attrs:{checked:e.addressSelectedList.indexOf(t.id)>-1},on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.selectSingle(t)}}})],1):e._e(),n("v-uni-view",{},[n("v-uni-view",{},[t.is_default?n("v-uni-text",{staticClass:"bg-ff0013 b-rad-04 ptb-02-plr-12 text-ffffff font-20 font-wei l-h-28"},[e._v("默认")]):e._e(),t.label?n("v-uni-text",{staticClass:"bg-2e7bff b-rad-04 ptb-02-plr-12 text-ffffff font-20 font-wei l-h-28",class:t.is_default?"ml-16":""},[e._v(e._s(t.label))]):e._e(),n("v-uni-text",{staticClass:"font-28 font-wei text-3 l-h-40",class:t.label||t.is_default?"ml-20":""},[e._v(e._s(t.consignee))]),n("v-uni-text",{staticClass:"ml-18 font-28 text-9 l-h-40"},[e._v(e._s(t.consignee_phone))])],1),n("v-uni-view",{staticClass:"mt-08 w-528 font-24 text-6"},[e._v(e._s(t.province_name)+" "+e._s(t.city_name)+" "+e._s(t.town_name)+" "+e._s(t.address))])],1),n("v-uni-image",{staticClass:"w-40 h-40 pt-24 pb-24 pl-24",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/edit_bla.png"},on:{click:function(n){n.stopPropagation(),arguments[0]=n=e.$handleEvent(n),e.editAddress(t)}}})],1),t.showMask?n("v-uni-view",{staticClass:"fade-in p-abso z-100 top-0 left-0 w-p100 h-p100 bg-000-000-000-030 d-flex j-sa a-center pl-30 pr-30",on:{click:function(n){n.stopPropagation(),arguments[0]=n=e.$handleEvent(n),e.closeMaskOperation(t)}}},[n("v-uni-view",{staticClass:"w-92 h-92 bg-ffffff b-rad-p50 d-flex flex-column j-center a-center",on:{click:function(n){n.stopPropagation(),arguments[0]=n=e.$handleEvent(n),e.copyAddress(t)}}},[n("v-uni-view",{staticClass:"font-22 text-3 font-wei l-h-26"},[e._v("复制")]),n("v-uni-view",{staticClass:"font-22 text-3 font-wei l-h-26"},[e._v("地址")])],1),t.is_default?e._e():n("v-uni-view",{staticClass:"w-92 h-92 bg-ff9127 b-rad-p50 d-flex flex-column j-center a-center",on:{click:function(n){n.stopPropagation(),arguments[0]=n=e.$handleEvent(n),e.setDefaultAddress(t.id)}}},[n("v-uni-view",{staticClass:"font-22 text-ffffff font-wei l-h-26"},[e._v("设置")]),n("v-uni-view",{staticClass:"font-22 text-ffffff font-wei l-h-26"},[e._v("默认")])],1),n("v-uni-view",{staticClass:"w-92 h-92 bg-eb0404 b-rad-p50 d-flex flex-column j-center a-center",on:{click:function(n){n.stopPropagation(),arguments[0]=n=e.$handleEvent(n),e.deleteAddressItem(t.id)}}},[n("v-uni-view",{staticClass:"font-22 text-ffffff font-wei l-h-26"},[e._v("删除")]),n("v-uni-view",{staticClass:"font-22 text-ffffff font-wei l-h-26"},[e._v("地址")])],1)],1):e._e()],1)})),1):n("v-uni-view",{},[n("vh-empty",{attrs:{"padding-top":270,"padding-bottom":100,"image-src":"https://images.vinehoo.com/vinehoomini/v3/empty/emp_addr.png","text-bottom":0,text:"您还没有添加收货地址"}})],1)],1),n("v-uni-view",{staticClass:"p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022"},[e.isShowEdit?n("v-uni-view",{staticClass:"fade-in-up-medium w-p100 h-p100 d-flex j-sb a-center pl-32 pr-24"},[n("v-uni-view",{staticClass:"d-flex a-center"},[n("vh-check",{attrs:{checked:e.isSelectAllAddress()},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectAll()}}}),n("v-uni-text",{staticClass:"ml-16 font-32 text-3"},[e._v("全选")])],1),n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.deleteAddress.apply(void 0,arguments)}}},[e._v("删除")])],1)],1):n("v-uni-view",{staticClass:"fade-in-down w-p100 h-p100 d-flex j-center a-center"},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"64rpx",fontSize:"28rpx",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.jump.navigateTo("../address-add/address-add")}}},[e._v("新建地址")])],1)],1)],1)},r=[]},"28ff":function(e,t,n){"use strict";n.r(t);var a=n("0e01"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},"2f6e":function(e,t,n){"use strict";n.r(t);var a=n("25bf"),i=n("52a1");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"3b85d4ac",null,!1,a["a"],void 0);t["default"]=s.exports},"4f1b":function(e,t,n){"use strict";n.r(t);var a=n("825d"),i=n("8e1d");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);n("fa94");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"4ed92bb2",null,!1,a["a"],void 0);t["default"]=s.exports},"51bd":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":e.isFixed},e.navbarClass],style:[e.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":e.newYearTheme},style:[e.navbarInnerStyle]},[e.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.goBack.apply(void 0,arguments)}}},[e.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:e.backIconColor,size:e.backIconSize}}):[n("u-icon",{attrs:{name:e.pageLength<=1?"home":e.backIconName,color:e.backIconColor,size:e.backIconSize}}),e.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[e.backTextStyle]},[e._v(e._s(e.backText))]):e._e()]],2):e._e(),e.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[e.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:e.titleColor,fontSize:e.titleSize+"rpx",fontWeight:e.titleBold?"bold":"normal"}},[e._v(e._s(e.title))])],1):e._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[e._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[e._t("right")],2)],1)],1),e.isFixed&&!e.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(e.navbarHeight)+(e.appStatusBarHeight||e.customStatusBarHeight||e.statusBarHeight)+"px"}}):e._e()],1)},r=[]},"52a1":function(e,t,n){"use strict";n.r(t);var a=n("fbfa"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},"55c2":function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("a9e3");var a={name:"vh-empty",props:{bgColor:{type:String,default:"#FFF"},paddingTop:{type:[String,Number],default:0},paddingBottom:{type:[String,Number],default:0},width:{type:[String,Number],default:440},height:{type:[String,Number],default:360},imageSrc:{type:String,default:""},textBottom:{type:[String,Number],default:46},text:{type:String,default:""}},computed:{outerEmpConStyle:function(){return{backgroundColor:this.bgColor,paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},innEmpConStyle:function(){return{width:this.width+"rpx",height:this.height+"rpx"}},textStyle:function(){return{bottom:this.textBottom+"rpx"}}}};t.default=a},"5ba4":function(e,t,n){"use strict";n.r(t);var a=n("144f"),i=n("a58c");for(var r in i)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(r);var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"55488dce",null,!1,a["a"],void 0);t["default"]=s.exports},"7f1a":function(e,t,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var r=n("26cb"),o=uni.getSystemInfoSync(),s={},d={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,r.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var e={};return e.height=this.navbarHeight+"px",e},navbarStyle:function(){var e={};return e.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(e,this.background),this.showBorder&&Object.assign(e,this.borderStyle),e},titleStyle:function(){var e={};return e.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",e.width=uni.upx2px(this.titleWidth)+"px",e},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var e=this.pages.getPageFullPath(),t=this.routeTable,n=t.pEAddressAdd,a=t.pEAddressManagement,i=t.pBOrderDepositDetail;(e.includes("/packageH")||e.includes(n)||e.includes(a)||e.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};t.default=d},"825d":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+e.size,e.plain?"u-btn--"+e.type+"--plain":"",e.loading?"u-loading":"","circle"==e.shape?"u-round-circle":"",e.hairLine?e.showHairLineBorder:"u-btn--bold-border","u-btn--"+e.type,e.disabled?"u-btn--"+e.type+"--disabled":""],style:[e.customStyle,{overflow:e.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(e.hoverStartTime),"hover-stay-time":Number(e.hoverStayTime),disabled:e.disabled,"form-type":e.formType,"open-type":e.openType,"app-parameter":e.appParameter,"hover-stop-propagation":e.hoverStopPropagation,"send-message-title":e.sendMessageTitle,"send-message-path":"sendMessagePath",lang:e.lang,"data-name":e.dataName,"session-from":e.sessionFrom,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"hover-class":e.getHoverClass,loading:e.loading},on:{getphonenumber:function(t){arguments[0]=t=e.$handleEvent(t),e.getphonenumber.apply(void 0,arguments)},getuserinfo:function(t){arguments[0]=t=e.$handleEvent(t),e.getuserinfo.apply(void 0,arguments)},error:function(t){arguments[0]=t=e.$handleEvent(t),e.error.apply(void 0,arguments)},opensetting:function(t){arguments[0]=t=e.$handleEvent(t),e.opensetting.apply(void 0,arguments)},launchapp:function(t){arguments[0]=t=e.$handleEvent(t),e.launchapp.apply(void 0,arguments)},click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.click(t)}}},[e._t("default"),e.ripple?n("v-uni-view",{staticClass:"u-wave-ripple",class:[e.waveActive?"u-wave-active":""],style:{top:e.rippleTop+"px",left:e.rippleLeft+"px",width:e.fields.targetWidth+"px",height:e.fields.targetWidth+"px","background-color":e.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):e._e()],2)},i=[]},"8e1d":function(e,t,n){"use strict";n.r(t);var a=n("9476"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},9476:function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("a9e3"),n("c975"),n("d3b7"),n("ac1f");var a={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var e;return e=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",e},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(e){var t=this;this.$u.throttle((function(){!0!==t.loading&&!0!==t.disabled&&(t.ripple&&(t.waveActive=!1,t.$nextTick((function(){this.getWaveQuery(e)}))),t.$emit("click",e))}),this.throttleTime)},getWaveQuery:function(e){var t=this;this.getElQuery().then((function(n){var a=n[0];if(a.width&&a.width&&(a.targetWidth=a.height>a.width?a.height:a.width,a.targetWidth)){t.fields=a;var i,r;i=e.touches[0].clientX,r=e.touches[0].clientY,t.rippleTop=r-a.top-a.targetWidth/2,t.rippleLeft=i-a.left-a.targetWidth/2,t.$nextTick((function(){t.waveActive=!0}))}}))},getElQuery:function(){var e=this;return new Promise((function(t){var n="";n=uni.createSelectorQuery().in(e),n.select(".u-btn").boundingClientRect(),n.exec((function(e){t(e)}))}))},getphonenumber:function(e){this.$emit("getphonenumber",e)},getuserinfo:function(e){this.$emit("getuserinfo",e)},error:function(e){this.$emit("error",e)},opensetting:function(e){this.$emit("opensetting",e)},launchapp:function(e){this.$emit("launchapp",e)}}};t.default=a},a126:function(e,t,n){var a=n("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},a58c:function(e,t,n){"use strict";n.r(t);var a=n("55c2"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},aab3:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),e.exports=t},bbdc:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),e.exports=t},d4f3:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"d-flex"},[n("v-uni-image",{staticClass:"fade-in",style:[e.checkStyle],attrs:{src:e.checked?e.checkedImg:e.unCheckedImg,mode:e.mode},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.click()}}})],1)},i=[]},f074:function(e,t,n){"use strict";n.r(t);var a=n("7f1a"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(r);t["default"]=i.a},f2f9:function(e,t,n){"use strict";var a=n("a126"),i=n.n(a);i.a},fa94:function(e,t,n){"use strict";var a=n("062a"),i=n.n(a);i.a},fbfa:function(e,t,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("caad6"),n("99af"),n("d81d"),n("d3b7"),n("159b"),n("14d9"),n("a630"),n("3ca3"),n("6062"),n("ddb0"),n("c975"),n("a434");var i=a(n("f07e")),r=a(n("c964")),o=a(n("f3f3")),s=n("26cb"),d={name:"address-management",data:function(){return{comeFrom:"",isShowEdit:!1,addressList:[],addressSelectedList:[],orderInfo:{}}},computed:(0,o.default)({},(0,s.mapState)(["routeTable","labelList","addressInfoState"])),onLoad:function(e){if(this.comeFrom=e.comeFrom,["6","7","8","9"].includes(this.comeFrom)&&1===this.pages.getPageLength())this.jump.redirectTo(this.routeTable.pHAuctionIndex);else{var t=e.subOrderNo,n=e.orderType;this.orderInfo={subOrderNo:t,orderType:n},["11","12"].includes(this.comeFrom)&&1===this.pages.getPageLength()&&(t&&n?this.jump.redirectTo("".concat(this.$routeTable["11"===this.comeFrom?"pBOrderDetail":"pHAuctionOrderDetail"],"?orderNo=").concat(t)):this.jump.redirectTo("".concat(this.$routeTable.pEMyOrder,"?status=0")))}},onShow:function(){this.getAddressList()},methods:(0,o.default)((0,o.default)({},(0,s.mapMutations)(["muAddressInfoState","muLabelList"])),{},{getAddressList:function(){var e=this;return(0,r.default)((0,i.default)().mark((function t(){var n;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$u.api.addressList();case 2:n=t.sent,e.addressList=n.data.list.map((function(e){return e.showMask=!1,e}));case 4:case"end":return t.stop()}}),t)})))()},openMaskOperation:function(e){this.addressList=this.addressList.map((function(t){return e.id==t.id?t.showMask=!0:t.showMask=!1,t}))},jumpPage:function(e){switch(this.comeFrom){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":case"10":case"99":case"100":this.muAddressInfoState(e),this.jump.navigateBack(),console.log("跳转页面后的打印");break;case"11":case"12":this.updateOrderAddress(e);break}},copyAddress:function(e){var t=this;uni.setClipboardData({data:e.consignee+e.consignee_phone+e.province_name+e.city_name+e.town_name+e.address,success:function(e){t.feedback.toast({title:"复制成功",icon:"success"})}})},setDefaultAddress:function(e){var t=this;return(0,r.default)((0,i.default)().mark((function n(){return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return console.log(e),n.next=3,t.$u.api.setDefaultAddress({id:e,is_default:1});case 3:t.feedback.toast({title:"设置成功",icon:"success"}),t.getAddressList();case 5:case"end":return n.stop()}}),n)})))()},deleteAddressItem:function(e){var t=this;return(0,r.default)((0,i.default)().mark((function n(){return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,t.$u.api.deleteAddress({id:[e]});case 2:t.feedback.toast({title:"删除成功",icon:"success"}),t.muAddressInfoState({}),t.getAddressList();case 5:case"end":return n.stop()}}),n)})))()},closeMaskOperation:function(e){this.addressList=this.addressList.map((function(t){return e.id==t.id&&(t.showMask=!1),t}))},editAddress:function(e){var t=this;this.muAddressInfoState(e),this.labelList.forEach((function(n){e.label&&n!=e.label&&(t.labelList.push(e.label),t.muLabelList(Array.from(new Set(t.labelList))))})),this.jump.navigateTo("".concat(this.routeTable.pEAddressAdd,"?isEdit=1"))},isSelectAllAddress:function(){return this.addressList.length===this.addressSelectedList.length&&this.addressSelectedList.length},selectAll:function(){this.isSelectAllAddress()?(this.addressList.forEach((function(e){e.checked=!1})),this.addressSelectedList=[]):this.addressSelectedList=this.addressList.map((function(e){return e.checked=!0,e.id}))},selectSingle:function(e){this.addressSelectedList.indexOf(e.id)>-1?(this.addressList.forEach((function(t){t.id===e.id&&(t.checked=!1)})),this.addressSelectedList.splice(this.addressSelectedList.indexOf(e.id),1)):(this.addressList.forEach((function(t){t.id===e.id&&(t.checked=!0)})),this.addressSelectedList.push(e.id))},deleteAddress:function(){var e=this;return(0,r.default)((0,i.default)().mark((function t(){return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0!=e.addressList.length){t.next=2;break}return t.abrupt("return",e.feedback.toast({title:"亲，您还没有收货地址喔！"}));case 2:if(!(e.addressList.length>0&&0==e.addressSelectedList.length)){t.next=4;break}return t.abrupt("return",e.feedback.toast({title:"请选择需要删除的地址"}));case 4:return t.next=6,e.$u.api.deleteAddress({id:e.addressSelectedList});case 6:e.feedback.toast({title:"删除成功",icon:"success"}),e.muAddressInfoState({}),e.getAddressList();case 9:case"end":return t.stop()}}),t)})))()},updateOrderAddress:function(e){var t=this,n=e.province_id,a=e.city_id,i=e.town_id,r=e.province_name,o=e.city_name,s=e.town_name,d=e.address,c=e.consignee,l=e.consignee_phone,u=this.orderInfo,f=u.subOrderNo,p=u.orderType,h={sub_order_no:f,order_type:p,type:4,refund_reason:"修改地址",receive_province_id:n,receive_city_id:a,receive_area_id:i,receive_province:r,receive_city:o,receive_area:s,receive_address:d,receive_name:c,receive_phone:l};this.$u.api.updateOrderAddress(h).then((function(){t.feedback.toast({title:"修改成功",icon:"success"}),t.jump.navigateBack()}))}})};t.default=d}}]);