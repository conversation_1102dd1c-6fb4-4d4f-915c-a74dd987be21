(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-real-name-auction-real-name"],{"12c6":function(t,e,n){"use strict";n.r(e);var a=n("51bd"),i=n("f074");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);n("f2f9");var o=n("f0c5"),r=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);e["default"]=r.exports},"2afd":function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("b64b"),n("d3b7");var i=a(n("f07e")),s=a(n("c964")),o=a(n("f3f3")),r=n("d8be"),u=n("26cb"),c={name:"auctionRealName",data:function(){return{loading:!0,list:[],companyRNInfo:null,popupVisible:!1}},computed:(0,o.default)({},(0,u.mapState)(["routeTable"])),methods:{onJump:function(t){var e=this;return(0,s.default)((0,i.default)().mark((function n(){var a,s,o;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=t.type,s=t.path,a!==r.MAuctionRNType.PersonalRN||!e.list[1].status){n.next=4;break}return e.feedback.toast({title:"您已完成商户认证，无需个人认证了"}),n.abrupt("return");case 4:if(a!==r.MAuctionRNType.CompanyRN||!e.list[0].status){n.next=13;break}if(e.companyRNInfo){n.next=10;break}return n.next=8,e.$u.api.getAuctionRNInfo({type:r.MAuctionRNType.CompanyRN});case 8:o=n.sent,e.companyRNInfo=(null===o||void 0===o?void 0:o.data)||{};case 10:if(e.companyRNInfo&&Object.keys(e.companyRNInfo).length){n.next=13;break}return e.popupVisible=!0,n.abrupt("return");case 13:e.jump.navigateTo(s);case 14:case"end":return n.stop()}}),n)})))()},jumpRNCompany:function(){this.popupVisible=!1,this.jump.navigateTo(this.routeTable.pHAuctionRNCompany)},loadAuctionUserInfo:function(){var t=this;return(0,s.default)((0,i.default)().mark((function e(){var n,a,s,o,u,c,l,f,d;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.routeTable,s=a.pHAuctionRNPersonal,o=a.pHAuctionRNCompany,u=[{name:"个人认证",desc:"个人真实身份认证，完成后可发布拍品",icon:"/auction/rn_personal_72.png",path:s,status:!1,type:r.MAuctionRNType.PersonalRN},{name:"商户认证",desc:"通过商户认证后，可申请商户发布拍品",icon:"/auction/rn_company_72.png",path:o,status:!1,type:r.MAuctionRNType.CompanyRN}],e.next=4,t.$u.api.getAuctionUserInfo();case 4:c=e.sent,l=(null===c||void 0===c||null===(n=c.data)||void 0===n?void 0:n.info)||{},f=l.ac_type,d=void 0===f?0:f,e.t0=d,e.next=e.t0===r.MAuctionRNType.PersonalRN?9:e.t0===r.MAuctionRNType.CompanyRN?11:13;break;case 9:return u[0].status=!0,e.abrupt("break",13);case 11:return u[1].status=!0,e.abrupt("break",13);case 13:t.list=u;case 14:case"end":return e.stop()}}),e)})))()}},onShow:function(){var t=this;this.login.isLoginV3(this.$vhFrom).then((function(e){e&&t.loadAuctionUserInfo().finally((function(){t.loading=!1}))}))}};e.default=c},"2d37":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return a}));var a={vhNavbar:n("12c6").default,uPopup:n("c4b0").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("vh-navbar",{attrs:{title:"实名认证",height:"46",background:{background:"#e80404"},"back-icon-color":"#FFF","title-color":"#FFF"}}),t.loading?t._e():n("v-uni-view",{staticClass:"pt-28"},t._l(t.list,(function(e,a){return n("v-uni-view",{key:a,on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.onJump(e)}}},[a?n("v-uni-view",{staticClass:"mtb-00-mlr-auto w-686 h-02 bg-eeeeee"}):t._e(),n("v-uni-view",{staticClass:"flex-sb-c p-32"},[n("v-uni-view",{staticClass:"flex-c-c"},[n("v-uni-image",{staticClass:"w-72 h-72",attrs:{src:t.ossIcon(e.icon)}}),n("v-uni-view",{staticClass:"ml-24"},[n("v-uni-view",{staticClass:"flex-s-c"},[n("v-uni-view",{staticClass:"font-wei-500 font-28 text-3 l-h-40"},[t._v(t._s(e.name))]),e.status?n("v-uni-view",{staticClass:"flex-c-c ml-20 w-112 h-38 font-24 text-ff9127 l-h-34 b-s-02-ff9127 b-rad-10"},[t._v("已认证")]):t._e()],1),n("v-uni-view",{staticClass:"mt-06 font-24 text-9 l-h-34"},[t._v(t._s(e.desc))])],1)],1),n("v-uni-image",{staticClass:"w-12 h-20",attrs:{src:t.ossIcon("/about/arrow_r_12_20.png")}})],1)],1)})),1),n("u-popup",{attrs:{mode:"center","mask-close-able":!1,width:"552",height:"414","border-radius":"20"},model:{value:t.popupVisible,callback:function(e){t.popupVisible=e},expression:"popupVisible"}},[n("v-uni-view",{staticClass:"p-rela w-552 h-414"},[n("v-uni-image",{staticClass:"p-abso w-552 h-414",attrs:{src:t.ossIcon("/auction/popup_bg_552_414.png")}}),n("v-uni-view",{staticClass:"p-rela pt-24"},[n("v-uni-view",{staticClass:"font-wei-600 font-32 text-3 l-h-44 text-center"},[t._v("温馨提示")]),n("v-uni-view",{staticClass:"mt-20 ptb-00-plr-36 font-26 text-6 l-h-48 text-center"},[t._v("您当前已通过实名认证。如更改认证类型并通过审核，将会"),n("v-uni-text",{staticClass:"text-e80404"},[t._v("注销您已通过认证的实名信息（该操作不可撤销）")]),t._v("。您确认要继续更改认证类型吗？")],1),n("v-uni-view",{staticClass:"flex-sb-c ptb-00-plr-76 mt-40"},[n("v-uni-button",{staticClass:"vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.popupVisible=!1}}},[t._v("再想想")]),n("v-uni-button",{staticClass:"vh-btn flex-c-c w-180 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumpRNCompany.apply(void 0,arguments)}}},[t._v("确定")])],1)],1)],1)],1)],1)},s=[]},"51bd":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[n("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},s=[]},"7f1a":function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var s=n("26cb"),o=uni.getSystemInfoSync(),r={},u={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:r,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,s.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,n=e.pEAddressAdd,a=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(n)||t.includes(a)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=u},a126:function(t,e,n){var a=n("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},bbdc:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},d45e:function(t,e,n){"use strict";n.r(e);var a=n("2d37"),i=n("fe41");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);var o=n("f0c5"),r=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"180eb5f2",null,!1,a["a"],void 0);e["default"]=r.exports},f074:function(t,e,n){"use strict";n.r(e);var a=n("7f1a"),i=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=i.a},f2f9:function(t,e,n){"use strict";var a=n("a126"),i=n.n(a);i.a},fe41:function(t,e,n){"use strict";n.r(e);var a=n("2afd"),i=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=i.a}}]);