(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageI-pages-school-test-questionDetail"],{"55d0":function(t,e,a){var n=a("97517");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("193843ec",n,!0,{sourceMap:!1,shadowMode:!1})},"902e":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("f07e")),o=n(a("c964")),s=n(a("1349")),r={components:{answerItem:s.default},data:function(){return{paper_id:"",info:{},id:"",background:{backgroundColor:"#D30808"}}},onLoad:function(t){var e=this;this.id=t.id,this.paper_id=t.paper_id,uni.getSystemInfo({success:function(t){e.appStatusBarHeight=t.statusBarHeight?t.statusBarHeight:48}})},onShow:function(){this.getQuestionDetails()},methods:{getQuestionDetails:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){var a,n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={id:t.id,score:0,paper_id:t.paper_id},e.next=3,t.$u.api.getExamQuestionList(a);case 3:n=e.sent,console.log(n.data.list),t.info=n.data.list[0]?n.data.list[0]:{};case 6:case"end":return e.stop()}}),e)})))()}}};e.default=r},97517:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.answer[data-v-782368da]{padding:%?32?%}.error-question-list-box[data-v-782368da]{min-height:100vh;background-color:#f5f5f5}.error-question-list-box .error-list-item[data-v-782368da]{margin-top:%?160?%;padding:%?32?%}.error-question-list-box .error-list-item .item-list[data-v-782368da]{margin-bottom:%?20?%;padding:%?24?% %?20?%;background-color:#fff;border-radius:%?10?%}.error-question-list-box .error-list-item .item-list .item-list-title[data-v-782368da]{font-weight:500;font-size:%?28?%;color:#555;line-height:%?40?%;text-align:left;font-style:normal}.footer[data-v-782368da]{margin-top:%?30?%;display:flex;justify-content:space-between}.view-details[data-v-782368da]{color:#666;font-size:%?24?%;line-height:%?34?%}.answer-bottom[data-v-782368da]{display:flex;justify-content:flex-start;align-items:center}.answer-bottom .success-answer-title[data-v-782368da]{color:#666;font-size:%?24?%;line-height:%?34?%}.answer-bottom .success-answer-option[data-v-782368da]{font-size:%?24?%;font-weight:700;margin:0 %?16?% 0 %?4?%;color:#41cc8e}.answer-bottom .error-answer-option[data-v-782368da]{font-size:%?24?%;font-weight:700;margin:0 %?16?% 0 %?4?%;color:#e80404}.header[data-v-782368da]{position:fixed;top:0;width:100%;left:0;z-index:111}.header .title[data-v-782368da]{position:absolute;top:%?90?%;left:40.3%;font-weight:600;font-size:%?32?%;color:#fff;line-height:%?44?%;text-align:center;font-style:normal;z-index:111}.header .icon[data-v-782368da]{position:absolute;top:40px;left:%?14?%;z-index:111}.header img[data-v-782368da]{width:100%}',""]),t.exports=e},ad51:function(t,e,a){"use strict";a.r(e);var n=a("902e"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},b099:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={vhNavbar:a("12c6").default,uIcon:a("e5e1").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"error-question-list-box"},[a("v-uni-view",[t.$appStatusBarHeight?a("v-uni-view",[a("v-uni-view",{staticClass:"p-fixed z-980 top-0 w-p100",style:{background:"#D60C0C"}},[a("v-uni-view",{style:{height:t.$appStatusBarHeight+"px"}}),a("v-uni-view",{staticClass:"p-rela h-px-48 d-flex j-center a-center"},[a("v-uni-view",{staticClass:"p-abso left-24 h-p100 d-flex a-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.navigateBack()}}},[a("u-icon",{attrs:{name:"nav-back",color:"#fff",size:44}})],1),a("v-uni-view",{staticClass:"font-36 font-wei text-ffffff"},[t._v("错题详情")])],1)],1)],1):a("vh-navbar",{attrs:{background:t.background,title:"错题详情","back-icon-color":"#fff","title-color":"#fff"}})],1),1==t.info.category_id?a("answerItem",{ref:"answerItem",staticClass:"answer",style:{paddingTop:t.$appStatusBarHeight?parseInt(t.$appStatusBarHeight)+58+"px":"0px"},attrs:{hiddenIndex:2,info:t.info}}):t._e()],1)},o=[]},e839:function(t,e,a){"use strict";var n=a("55d0"),i=a.n(n);i.a},f204:function(t,e,a){"use strict";a.r(e);var n=a("b099"),i=a("ad51");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("e839");var s=a("f0c5"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"782368da",null,!1,n["a"],void 0);e["default"]=r.exports}}]);