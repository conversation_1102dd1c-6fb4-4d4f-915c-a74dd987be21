(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-after-sale-apply-after-sale-apply~packageE-pages-certification-detail-certification-d~c2f0056c"],{"062a":function(e,t,i){var a=i("aab3");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=i("4f06").default;o("2db15654",a,!0,{sourceMap:!1,shadowMode:!1})},1609:function(e,t,i){"use strict";i.r(t);var a=i("790b"),o=i("4b16");for(var n in o)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(n);i("9943");var r=i("f0c5"),s=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"168c94f5",null,!1,a["a"],void 0);t["default"]=s.exports},"4b16":function(e,t,i){"use strict";i.r(t);var a=i("ec18"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},"4f1b":function(e,t,i){"use strict";i.r(t);var a=i("825d"),o=i("8e1d");for(var n in o)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return o[e]}))}(n);i("fa94");var r=i("f0c5"),s=Object(r["a"])(o["default"],a["b"],a["c"],!1,null,"4ed92bb2",null,!1,a["a"],void 0);t["default"]=s.exports},"705c":function(e,t,i){var a=i("c35b");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=i("4f06").default;o("398a1a67",a,!0,{sourceMap:!1,shadowMode:!1})},"790b":function(e,t,i){"use strict";i.d(t,"b",(function(){return o})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){return a}));var a={uLineProgress:i("f064").default},o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.disabled?e._e():i("v-uni-view",{class:e.showUploadList?"vh-upload":""},[e._l(e.lists,(function(t,a){return e.showUploadList?i("v-uni-view",{key:a,staticClass:"vh-preview-wrap",class:e.uploadConClass,style:{width:e.$u.addUnit(e.width),height:e.$u.addUnit(e.height)}},[e.deletable&&1==e.plate?i("v-uni-view",{},[i("v-uni-view",{staticClass:"vh-cer-delete-icon",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.deleteItem(a)}}},[i("v-uni-image",{attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/certification_detail/del_img.png",mode:"aspectFill"}})],1),"idCardImg"==t.cerType?i("v-uni-view",{staticClass:"vh-cer-re-upload-icon",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.selectFile("image","idCardImg",a)}}},[i("v-uni-image",{attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/certification_detail/re_upload_img.png",mode:"aspectFill"}})],1):e._e(),"cerQuaImg"==t.cerType?i("v-uni-view",{staticClass:"vh-cer-re-upload-icon",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.selectFile("image","cerQuaImg",a)}}},[i("v-uni-image",{attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/certification_detail/re_upload_img.png",mode:"aspectFill"}})],1):e._e()],1):e._e(),e.showProgress&&t.progress>0&&!t.error?i("u-line-progress",{staticClass:"vh-progress",attrs:{"show-percent":!1,height:"6","active-color":"#E80404",percent:t.progress}}):e._e(),t.error?i("v-uni-view",{staticClass:"vh-error-btn",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.retry(a)}}},[e._v("点击重试")]):e._e(),0==e.plate?[e.deletable?i("v-uni-image",{staticClass:"vh-rig-top-after-sale-delete-icon",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/up_del.png",mode:"aspectFill"},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.deleteItem(a)}}}):e._e(),"image"==t.fileType?i("v-uni-image",{staticClass:"vh-sale-preview",attrs:{src:t.url||t.path,mode:e.imageMode},on:{click:function(i){i.stopPropagation(),arguments[0]=i=e.$handleEvent(i),e.doPreviewImage(t.url||t.path,a)}}}):e._e(),"video"==t.fileType?i("v-uni-view",{staticClass:"vh-sale-preview-video-con",on:{click:function(i){i.stopPropagation(),arguments[0]=i=e.$handleEvent(i),e.doPreviewVideo(t)}}},[i("v-uni-image",{staticClass:"vh-sale-preview",attrs:{src:t.videoCoverImg,mode:e.imageMode}}),i("v-uni-view",{staticClass:"p-abso z-02 top-0 left-0 d-flex j-center a-center w-p100 h-p100"},[i("v-uni-image",{staticClass:"w-50 h-50",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/goods_detail/video_play.png"}})],1)],1):e._e()]:e._e(),1==e.plate?[t.response?i("v-uni-image",{staticClass:"vh-cer-preview",attrs:{src:e.ossPrefix+t.response,mode:e.imageMode},on:{click:function(i){i.stopPropagation(),arguments[0]=i=e.$handleEvent(i),e.doPreviewImage(e.ossPrefix+t.response,a)}}}):i("v-uni-image",{staticClass:"vh-cer-preview",attrs:{src:t.url,mode:e.imageMode},on:{click:function(i){i.stopPropagation(),arguments[0]=i=e.$handleEvent(i),e.doPreviewImage(t.url,a)}}})]:e._e()],2):e._e()})),e._t("file",null,{file:e.lists}),0==e.plate?[e.canUploadImage&&e.afterSaleImageList.length<e.maxCount?i("v-uni-view",{staticStyle:{display:"inline-block"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectFile("image")}}},[e._t("addImageBtn"),e.customImageBtn?e._e():i("v-uni-view",{staticClass:"vh-sale-list-item-border",class:e.uploadConClass,style:{width:e.$u.addUnit(e.width),height:e.$u.addUnit(e.height)},attrs:{"hover-class":"vh-add-wrap__hover","hover-stay-time":"150"}},[i("v-uni-image",{staticClass:"vh-sale-add-img",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/cam_red.png",mode:"aspectFill"}}),i("v-uni-view",{staticClass:"vh-sale-add-tips-container"},[i("v-uni-text",{staticClass:"vh-sale-add-tips"},[e._v("上传凭证")]),i("v-uni-text",{staticClass:"vh-sale-add-tips"},[e._v("（最多3张）")])],1)],1)],2):e._e(),e.canUploadVideo&&0==e.afterSaleVideoList.length?i("v-uni-view",{staticStyle:{display:"inline-block"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectFile("video")}}},[e._t("addVideoBtn"),e.customVideoBtn?e._e():i("v-uni-view",{staticClass:"vh-sale-list-item-border",class:e.uploadConClass,style:{width:e.$u.addUnit(e.width),height:e.$u.addUnit(e.height)},attrs:{"hover-class":"vh-add-wrap__hover","hover-stay-time":"150"}},[i("v-uni-image",{staticClass:"vh-sale-add-img",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/vie_red.png",mode:"aspectFill"}}),i("v-uni-view",{staticClass:"vh-sale-add-tips-container"},[i("v-uni-text",{staticClass:"vh-sale-add-tips"},[e._v("上传视频")]),i("v-uni-text",{staticClass:"vh-sale-add-tips"},[e._v("（最多1个）")])],1)],1)],2):e._e()]:e._e(),1==e.plate?[e.canUploadImage&&e.maxCount>e.lists.length&&!e.hasIdCardImg?i("v-uni-view",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectFile("image","idCardImg")}}},[e.customImageBtn?e._e():i("v-uni-view",{class:e.uploadConClass,style:{width:e.$u.addUnit(e.width),height:e.$u.addUnit(e.height)},attrs:{"hover-class":"vh-add-wrap__hover","hover-stay-time":"150"}},[i("v-uni-image",{staticClass:"vh-cer-add-img",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/cam_red.png",mode:"aspectFill"}}),i("v-uni-view",{staticClass:"vh-cer-add-tips-container"},[i("v-uni-text",{staticClass:"vh-cer-add-tips"},[e._v("上传手持身份证照片")])],1)],1)],1):e._e(),e.canUploadImage&&e.maxCount>e.lists.length&&!e.hasCerQuaImg?i("v-uni-view",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectFile("image","cerQuaImg")}}},[e.customImageBtn?e._e():i("v-uni-view",{class:e.uploadConClass,style:{width:e.$u.addUnit(e.width),height:e.$u.addUnit(e.height)},attrs:{"hover-class":"vh-add-wrap__hover","hover-stay-time":"150"}},[i("v-uni-image",{staticClass:"vh-cer-add-img",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/cam_red.png",mode:"aspectFill"}}),i("v-uni-view",{staticClass:"vh-cer-add-tips-container"},[i("v-uni-text",{staticClass:"vh-cer-add-tips"},[e._v("上传认证信息资质图片")])],1)],1)],1):e._e()]:e._e(),2==e.plate?[i("v-uni-view",{staticClass:"mt-74 mb-74 d-flex j-sa a-center pl-68 pr-68"},[i("v-uni-image",{staticClass:"w-180 h-180",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/user_info/cam.png",mode:"aspectFill"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleCameraClick.apply(void 0,arguments)}}}),i("v-uni-image",{staticClass:"w-180 h-180",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/user_info/alb.png",mode:"aspectFill"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.selectFile("image",null,null,["album"])}}})],1)]:e._e()],2)},n=[]},"825d":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+e.size,e.plain?"u-btn--"+e.type+"--plain":"",e.loading?"u-loading":"","circle"==e.shape?"u-round-circle":"",e.hairLine?e.showHairLineBorder:"u-btn--bold-border","u-btn--"+e.type,e.disabled?"u-btn--"+e.type+"--disabled":""],style:[e.customStyle,{overflow:e.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(e.hoverStartTime),"hover-stay-time":Number(e.hoverStayTime),disabled:e.disabled,"form-type":e.formType,"open-type":e.openType,"app-parameter":e.appParameter,"hover-stop-propagation":e.hoverStopPropagation,"send-message-title":e.sendMessageTitle,"send-message-path":"sendMessagePath",lang:e.lang,"data-name":e.dataName,"session-from":e.sessionFrom,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"hover-class":e.getHoverClass,loading:e.loading},on:{getphonenumber:function(t){arguments[0]=t=e.$handleEvent(t),e.getphonenumber.apply(void 0,arguments)},getuserinfo:function(t){arguments[0]=t=e.$handleEvent(t),e.getuserinfo.apply(void 0,arguments)},error:function(t){arguments[0]=t=e.$handleEvent(t),e.error.apply(void 0,arguments)},opensetting:function(t){arguments[0]=t=e.$handleEvent(t),e.opensetting.apply(void 0,arguments)},launchapp:function(t){arguments[0]=t=e.$handleEvent(t),e.launchapp.apply(void 0,arguments)},click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.click(t)}}},[e._t("default"),e.ripple?i("v-uni-view",{staticClass:"u-wave-ripple",class:[e.waveActive?"u-wave-active":""],style:{top:e.rippleTop+"px",left:e.rippleLeft+"px",width:e.fields.targetWidth+"px",height:e.fields.targetWidth+"px","background-color":e.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):e._e()],2)},o=[]},"8e1d":function(e,t,i){"use strict";i.r(t);var a=i("9476"),o=i.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},9476:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3"),i("c975"),i("d3b7"),i("ac1f");var a={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var e;return e=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",e},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(e){var t=this;this.$u.throttle((function(){!0!==t.loading&&!0!==t.disabled&&(t.ripple&&(t.waveActive=!1,t.$nextTick((function(){this.getWaveQuery(e)}))),t.$emit("click",e))}),this.throttleTime)},getWaveQuery:function(e){var t=this;this.getElQuery().then((function(i){var a=i[0];if(a.width&&a.width&&(a.targetWidth=a.height>a.width?a.height:a.width,a.targetWidth)){t.fields=a;var o,n;o=e.touches[0].clientX,n=e.touches[0].clientY,t.rippleTop=n-a.top-a.targetWidth/2,t.rippleLeft=o-a.left-a.targetWidth/2,t.$nextTick((function(){t.waveActive=!0}))}}))},getElQuery:function(){var e=this;return new Promise((function(t){var i="";i=uni.createSelectorQuery().in(e),i.select(".u-btn").boundingClientRect(),i.exec((function(e){t(e)}))}))},getphonenumber:function(e){this.$emit("getphonenumber",e)},getuserinfo:function(e){this.$emit("getuserinfo",e)},error:function(e){this.$emit("error",e)},opensetting:function(e){this.$emit("opensetting",e)},launchapp:function(e){this.$emit("launchapp",e)}}};t.default=a},9943:function(e,t,i){"use strict";var a=i("705c"),o=i.n(a);o.a},aab3:function(e,t,i){var a=i("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),e.exports=t},c35b:function(e,t,i){var a=i("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-upload[data-v-168c94f5]{display:flex;flex-wrap:wrap;align-items:center}.vh-preview-wrap[data-v-168c94f5]{border:1px solid #ebecee}.vh-rig-top-after-sale-delete-icon[data-v-168c94f5]{position:absolute;top:0;right:0;z-index:10;width:%?32?%;height:%?32?%;padding-bottom:%?24?%;padding-left:%?24?%}.vh-progress[data-v-168c94f5]{position:absolute;bottom:0;left:%?8?%;right:%?8?%;z-index:9;width:auto}.vh-error-btn[data-v-168c94f5]{color:#fff;background-color:#fa3534;font-size:%?20?%;padding:4px 0;text-align:center;position:absolute;bottom:0;left:0;right:0;z-index:9;line-height:1}.vh-add-wrap__hover[data-v-168c94f5]{background-color:#ebecee}.vh-sale-preview-video-con[data-v-168c94f5]{position:relative;width:100%;height:100%}.vh-sale-preview[data-v-168c94f5]{display:block;width:100%;height:100%;border-radius:%?10?%}.vh-sale-list-item[data-v-168c94f5]{position:relative;width:%?190?%;height:%?190?%;border-radius:%?6?%;display:flex;flex-direction:column;align-items:center;justify-content:center;overflow:hidden;margin-top:%?12?%;margin-right:%?12?%}.vh-sale-list-item-border[data-v-168c94f5]{border:.5px dashed #999}.vh-sale-add-img[data-v-168c94f5]{width:%?76?%;height:%?60?%}.vh-sale-add-tips-container[data-v-168c94f5]{display:flex;flex-direction:column;justify-content:center;align-items:center;margin-top:%?20?%}.vh-sale-add-tips[data-v-168c94f5]{font-size:%?24?%;color:#999;line-height:%?34?%}.vh-cer-delete-icon[data-v-168c94f5]{position:absolute;top:%?80?%;left:%?76?%;z-index:10;width:%?50?%;height:%?50?%}.vh-cer-delete-icon uni-image[data-v-168c94f5]{width:100%;height:100%}.vh-cer-re-upload-icon[data-v-168c94f5]{position:absolute;top:%?80?%;right:%?76?%;z-index:10;width:%?50?%;height:%?50?%}.vh-cer-re-upload-icon uni-image[data-v-168c94f5]{width:100%;height:100%}.vh-cer-preview[data-v-168c94f5]{width:%?308?%;height:%?196?%;border-radius:%?6?%}.vh-cer-list-item[data-v-168c94f5]{position:relative;width:%?328?%;height:%?228?%;background-color:#f7f7f7;display:flex;flex-direction:column;align-items:center;justify-content:center;overflow:hidden;border-radius:%?10?%;margin-left:%?30?%}.vh-cer-add-img[data-v-168c94f5]{width:%?72?%;height:%?60?%}.vh-cer-add-tips-container[data-v-168c94f5]{display:flex;flex-direction:column;justify-content:center;align-items:center;margin-top:%?24?%}.vh-cer-add-tips[data-v-168c94f5]{font-size:%?24?%;color:#666;line-height:%?34?%}',""]),e.exports=t},ec18:function(e,t,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(i("f07e")),n=a(i("c964")),r=a(i("f3f3"));i("a9e3"),i("d3b7"),i("d81d"),i("14d9"),i("4de4"),i("d401"),i("25f0"),i("baa5"),i("ac1f"),i("00b4"),i("caad6"),i("99af"),i("a434"),i("5319");var s=i("26cb"),l={name:"vh-upload",props:{plate:{type:Number,default:0},showUploadList:{type:Boolean,default:!0},canUploadImage:{type:Boolean,default:!0},canUploadVideo:{type:Boolean,default:!1},directory:{type:String,default:""},maxCount:{type:[String,Number],default:52},showProgress:{type:Boolean,default:!0},disabled:{type:Boolean,default:!1},imageMode:{type:String,default:"aspectFill"},header:{type:Object,default:function(){return{}}},name:{type:String,default:"file"},sizeType:{type:Array,default:function(){return["original","compressed"]}},sourceType:{type:Array,default:function(){return["album","camera"]}},previewFullImage:{type:Boolean,default:!0},multiple:{type:Boolean,default:!0},deletable:{type:Boolean,default:!0},maxSize:{type:[String,Number],default:Number.MAX_VALUE},fileList:{type:Array,default:function(){return[]}},uploadText:{type:String,default:"选择图片"},autoUpload:{type:Boolean,default:!0},showTips:{type:Boolean,default:!0},customImageBtn:{type:Boolean,default:!1},customVideoBtn:{type:Boolean,default:!1},width:{type:[String,Number],default:190},height:{type:[String,Number],default:190},delColor:{type:String,default:"#ffffff"},delIcon:{type:String,default:"close"},toJson:{type:Boolean,default:!0},beforeUpload:{type:Function,default:null},beforeRemove:{type:Function,default:null},limitType:{type:Array,default:function(){return["png","jpg","jpeg","webp","gif","image"]}},index:{type:[Number,String],default:""}},data:function(){return{afterSaleImageList:[],afterSaleVideoList:[],hasIdCardImg:!0,hasCerQuaImg:!0,lists:[],uploadInfo:{},uploading:!1}},computed:(0,r.default)((0,r.default)({},(0,s.mapState)(["ossPrefix"])),{},{uploadConClass:function(){return["vh-sale-list-item","vh-cer-list-item"][this.plate]}}),watch:{fileList:{immediate:!0,handler:function(e){this.lists=e}},lists:{handler:function(e){console.log("--------------------------这是深度监听数据变化"),console.log(e),this.$emit("on-list-change",e,this.index),this.hasChoosedCerImg(e),this.getAfterSaleUploadInfo(e)}}},created:function(){console.log("----------------------------我是子组件的created"),this.hasChoosedCerImg(this.lists)},methods:{getOssUploadInfo:function(){var e=this;return(0,n.default)((0,o.default)().mark((function t(){var i;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return uni.showLoading({title:"获取上传信息中...",mask:!0}),t.next=3,e.$u.api.ossUpload({dir:e.directory});case 3:i=t.sent,e.uploadInfo=i.data;case 5:case"end":return t.stop()}}),t)})))()},clear:function(){this.lists=[]},reUpload:function(){this.uploadFile()},selectFile:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if(console.log("------------------我是上传来源"),console.log(o),!this.disabled){var n=this.plate,r=(this.name,this.maxCount),s=this.multiple,l=this.maxSize,d=this.sizeType,c=this.lists,u=(this.camera,this.compressed,this.maxDuration,this.sourceType);console.log(n);var p=null,f=r-c.length;p=new Promise((function(t,i){"image"==e?uni.chooseImage({count:s?f>9?9:f:1,sourceType:2==n?o:u,sizeType:d,success:t,fail:i}):"video"==e&&uni.chooseVideo({sourceType:u,success:t,fail:i})})),p.then((function(o){var n=t.lists.length;"image"==e?o.tempFiles.map((function(e,o){if(t.checkFileExt(e)&&(s||!(o>=1)))if(e.size>l)t.$emit("on-oversize",e,t.lists,t.index),t.showToast("超出允许的文件大小");else{if(0==t.plate&&r<=t.afterSaleImageList.length)return t.$emit("on-exceed",e,t.lists,t.index),void t.showToast("超出最大允许的文件个数");if(0!==t.plate&&r<=c.length)return t.$emit("on-exceed",e,t.lists,t.index),void t.showToast("超出最大允许的文件个数");if(null!=a){console.log("------------------进入了列表替换");var n={fileType:"image",cerType:i,videoCoverImg:"",url:e.path,progress:0,error:!1,file:e};t.$set(c,a,n),console.log(c)}else console.log("------------------进入了列表追加"),c.push({fileType:"image",cerType:i,videoCoverImg:"",url:e.path,progress:0,error:!1,file:e})}})):"video"==e&&c.push({fileType:"video",cerType:i,videoCoverImg:o.thumbTempFilePath,url:o.tempFilePath,progress:0,error:!1,file:{path:o.tempFilePath,size:o.size}}),t.$emit("on-choose-complete",t.lists,t.index),t.autoUpload&&t.uploadFile(n)})).catch((function(e){t.$emit("on-choose-fail",e)}))}},hasChoosedCerImg:function(e){1==this.plate&&(e.length?(console.log("-----------我是过滤"),this.hasIdCardImg=e.some((function(e){return"idCardImg"==e.cerType})),this.hasCerQuaImg=e.some((function(e){return"cerQuaImg"==e.cerType}))):(this.hasIdCardImg=!1,this.hasCerQuaImg=!1),console.log(this.hasIdCardImg),console.log(this.hasCerQuaImg))},getAfterSaleUploadInfo:function(e){0==this.plate&&(console.log("--------------------------我是申请认证"),this.afterSaleImageList=e.filter((function(e){return"image"==e.fileType})),this.afterSaleVideoList=e.filter((function(e){return"video"==e.fileType})),console.log("申请售后图片列表",this.afterSaleImageList),console.log("申请售后视频列表",this.afterSaleVideoList))},showToast:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];(this.showTips||t)&&uni.showToast({title:e,icon:"none"})},upload:function(){this.uploadFile()},retry:function(e){this.lists[e].progress=0,this.lists[e].error=!1,this.lists[e].response=null,uni.showLoading({title:"重新上传"}),this.uploadFile(e)},uploadFile:function(){var e=arguments,t=this;return(0,n.default)((0,o.default)().mark((function i(){var a,n,r,s,l,d,c;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return a=e.length>0&&void 0!==e[0]?e[0]:0,i.next=3,t.getOssUploadInfo();case 3:if(uni.showLoading({title:"上传中...",mask:!0}),!t.disabled){i.next=6;break}return i.abrupt("return");case 6:if(!t.uploading){i.next=8;break}return i.abrupt("return");case 8:if(console.log(t.lists[0].file),!(a>=t.lists.length)){i.next=12;break}return t.$emit("on-uploaded",t.lists,t.index),i.abrupt("return");case 12:if(100!=t.lists[a].progress){i.next=15;break}return 0==t.autoUpload&&t.uploadFile(a+1),i.abrupt("return");case 15:if(!t.beforeUpload||"function"!==typeof t.beforeUpload){i.next=26;break}if(n=t.beforeUpload.bind(t.$u.$parent.call(t))(a,t.lists),!n||"function"!==typeof n.then){i.next=22;break}return i.next=20,n.then((function(e){})).catch((function(e){return t.uploadFile(a+1)}));case 20:i.next=26;break;case 22:if(!1!==n){i.next=26;break}return i.abrupt("return",t.uploadFile(a+1));case 26:if(t.directory||t.uploadInfo.host){i.next=29;break}return t.showToast("请配置上传地址",!0),i.abrupt("return");case 29:if(t.lists[a].error=!1,t.uploading=!0,r="",s="",2==t.plate)try{l=uni.getStorageSync("loginInfo"),console.log(l),r=l.uid,s="png"}catch(o){t.feedback.toast({title:"获取用户信息失败"})}else r=Math.random().toString(36).substr(2,4)+"_"+(new Date).getTime(),s=t.lists[a].url.substring(t.lists[a].url.lastIndexOf(".")+1);d={key:t.uploadInfo.dir+r+"."+s,policy:t.uploadInfo.policy,OSSAccessKeyId:t.uploadInfo.accessid,signature:t.uploadInfo.signature,success_action_status:200},console.warn(d),c=uni.uploadFile({url:t.uploadInfo.host,filePath:t.lists[a].url,name:t.name,formData:d,success:function(e){console.log(e);var i=t.toJson&&t.$u.test.jsonString(e.data)?JSON.parse(e.data):e.data;[200,201,204].includes(e.statusCode)?(console.log("----------------------------------我是获取上传列表信息"),console.log(t.lists[a]),"image"==t.lists[a].fileType?(console.warn(t.lists[a]),uni.getImageInfo({src:t.lists[a].url,success:function(e){console.log("----------------------------我是获取图片信息"),console.log(e);var o=e.width,n=e.height;t.lists[a].response="/".concat(t.uploadInfo.dir).concat(r,".").concat(s,"?w=").concat(o,"&h=").concat(n),t.lists[a].progress=100,t.lists[a].error=!1,t.$emit("on-success",i,a,t.lists,t.index),console.log("-------------------上传图片成功")},fail:function(e){console.log(e)}})):(t.lists[a].response="/"+t.uploadInfo.dir+r+"."+s,t.lists[a].progress=100,t.lists[a].error=!1,t.$emit("on-success",i,a,t.lists,t.index),console.log(e),console.log("-------------------上传非图片成功"))):t.uploadError(a,i)},fail:function(e){t.uploadError(a,e)},complete:function(e){uni.hideLoading(),t.uploading=!1,t.uploadFile(a+1),t.$emit("on-change",e,a,t.lists,t.index)}}),c.onProgressUpdate((function(e){e.progress>0&&(t.lists[a].progress=e.progress,t.$emit("on-progress",e,a,t.lists,t.index))}));case 38:case"end":return i.stop()}}),i)})))()},uploadError:function(e,t){this.lists[e].progress=0,this.lists[e].error=!0,this.lists[e].response=null,this.$emit("on-error",t,e,this.lists,this.index),console.log(e,t),this.showToast("上传失败，请重试")},deleteItem:function(e){var t=this;uni.showModal({title:"提示",content:"您确定要删除此项吗？",success:function(){var i=(0,n.default)((0,o.default)().mark((function i(a){var n;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!a.confirm){i.next=12;break}if(!t.beforeRemove||"function"!==typeof t.beforeRemove){i.next=11;break}if(n=t.beforeRemove.bind(t.$u.$parent.call(t))(e,t.lists),!n||"function"!==typeof n.then){i.next=8;break}return i.next=6,n.then((function(i){t.handlerDeleteItem(e)})).catch((function(e){t.showToast("已终止移除")}));case 6:i.next=9;break;case 8:!1===n?t.showToast("已终止移除"):t.handlerDeleteItem(e);case 9:i.next=12;break;case 11:t.handlerDeleteItem(e);case 12:case"end":return i.stop()}}),i)})));return function(e){return i.apply(this,arguments)}}()})},handlerDeleteItem:function(e){this.lists[e].process<100&&this.lists[e].process>0&&"undefined"!=typeof this.lists[e].uploadTask&&this.lists[e].uploadTask.abort(),this.lists.splice(e,1),this.$forceUpdate(),this.$emit("on-remove",e,this.lists,this.index),this.showToast("移除成功")},remove:function(e){e>=0&&e<this.lists.length&&(this.lists.splice(e,1),this.$emit("on-list-change",this.lists,this.index))},doPreviewImage:function(e,t){var i=this;if(this.previewFullImage){var a=this.lists.map((function(e){return e.url||e.path}));uni.previewImage({urls:a,current:e,success:function(){i.$emit("on-preview",e,i.lists,i.index)},fail:function(){uni.showToast({title:"预览图片失败",icon:"none"})}})}},doPreviewVideo:function(e){this.jump.navigateTo("/packageF/pages/full-screen-video/full-screen-video?videoLink=".concat(e.url))},checkFileExt:function(e){var t,i;return i=e.name.replace(/.+\./,"").toLowerCase(),t=this.limitType.some((function(e){return e.toLowerCase()===i})),t||this.showToast("不允许选择".concat(i,"格式的文件")),t},handleCameraClick:function(){console.log(this.$vhFrom),"next"===this.$vhFrom?this.$emit("takePhoto"):this.selectFile("image",null,null,["camera"])}}};t.default=l},fa94:function(e,t,i){"use strict";var a=i("062a"),o=i.n(a);o.a}}]);