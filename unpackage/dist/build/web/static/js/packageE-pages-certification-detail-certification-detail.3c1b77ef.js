(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageE-pages-certification-detail-certification-detail"],{1677:function(t,e,i){"use strict";i.r(e);var a=i("f440"),n=i("724e");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);var r=i("f0c5"),c=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"4efb453b",null,!1,a["a"],void 0);e["default"]=c.exports},"303c":function(t,e,i){var a=i("7f75");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var n=i("4f06").default;n("51a58769",a,!0,{sourceMap:!1,shadowMode:!1})},"5e7a":function(t,e,i){"use strict";i.r(e);var a=i("70d5"),n=i("b9e0");for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);i("c6d1");var r=i("f0c5"),c=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"4e527454",null,!1,a["a"],void 0);e["default"]=c.exports},"70d5":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return a}));var a={vhNavbar:i("12c6").default,vhGap:i("1677").default,uIcon:i("e5e1").default,vhUpload:i("1609").default,uButton:i("4f1b").default,uPopup:i("c4b0").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"content"},[i("vh-navbar",{attrs:{title:"认证详情"}}),t.submitSucc?t._e():i("v-uni-view",{},[i("v-uni-view",{staticClass:"ptb-60-plr-00"},[i("v-uni-view",{staticClass:"d-flex j-center a-center"},[i("v-uni-image",{staticClass:"w-44 h-44",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/certification_detail/pro_red.png",mode:"aspectFill"}}),i("v-uni-view",{staticClass:"w-250 h-04 bg-ffc8c8"}),i("v-uni-image",{staticClass:"w-44 h-44",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/certification_detail/"+(null!=t.status&&t.status>=0?"pro_red":"pro_gray")+".png",mode:"aspectFill"}}),i("v-uni-view",{staticClass:"w-250 h-04",class:t.status>=1?"bg-ffc8c8":"bg-e1e1e1"}),i("v-uni-image",{staticClass:"w-44 h-44",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/certification_detail/"+(1==t.status?"pro_red":2==t.status?"pro_fail":"pro_gray")+".png",mode:"aspectFill"}})],1),i("v-uni-view",{staticClass:"d-flex j-sb a-center mt-32 ptb-00-plr-32"},[i("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("填写资料")]),i("v-uni-text",{staticClass:"font-28",class:t.status>=0?"font-wei text-3":"text-9"},[t._v("等待审核")]),i("v-uni-text",{staticClass:"font-28",class:t.status>=1?"font-wei text-3":"text-9"},[t._v(t._s(2==t.status?"认证失败":"完成认证"))])],1)],1),i("vh-gap",{attrs:{height:"20","bg-color":"#f7f7f7"}}),null!==t.status&&t.recertification?i("v-uni-view",{},[i("v-uni-view",{staticClass:"ptb-00-plr-32"},[i("v-uni-view",{staticClass:"d-flex bb-s-01-eeeeee pt-60 pb-32"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3"},[t._v("真实姓名")]),i("v-uni-view",{staticClass:"w-480 ml-46 font-32 text-3 text-hidden-1"},[t._v(t._s(t.realName))])],1),i("v-uni-view",{staticClass:"d-flex bb-s-01-eeeeee pt-32 pb-16"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3"},[t._v("身份证号")]),i("v-uni-view",{staticClass:"w-480 ml-46 font-32 text-3 text-hidden-1"},[t._v(t._s(t.IdNumber))])],1),i("v-uni-view",{staticClass:"d-flex bb-s-01-eeeeee pt-48 pb-32"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3"},[t._v("认证信息")]),i("v-uni-view",{staticClass:"w-480 ml-46 font-32 text-3 text-hidden-1"},[t._v(t._s(t.cerName))])],1),i("v-uni-view",{staticClass:"pt-32 pb-32"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3"},[t._v("上传照片")]),i("v-uni-view",{staticClass:"mt-32 d-flex j-sb a-center"},[i("v-uni-view",{staticClass:"bg-f7f7f7 w-328 h-228 d-flex j-center a-center b-rad-10"},[i("v-uni-image",{staticClass:"w-308 h-196 b-rad-10",attrs:{src:t.ossPrefix+t.idCardImg,mode:"aspectFill"}})],1),i("v-uni-view",{staticClass:"bg-f7f7f7 w-328 h-228 d-flex j-center a-center b-rad-10"},[i("v-uni-image",{staticClass:"w-308 h-196 b-rad-10",attrs:{src:t.ossPrefix+t.cerQuaImg,mode:"aspectFill"}})],1)],1)],1)],1)],1):i("v-uni-view",{},[i("v-uni-view",{staticClass:"ptb-00-plr-32"},[i("v-uni-view",{staticClass:"d-flex bb-s-01-eeeeee pt-60 pb-32"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3"},[t._v("真实姓名")]),i("v-uni-input",{staticClass:"w-480 ml-46 font-32 text-3",attrs:{type:"text",placeholder:"请输入您的真实姓名","placeholder-style":"color:#999;font-size:32rpx;"},model:{value:t.realName,callback:function(e){t.realName=e},expression:"realName"}})],1),i("v-uni-view",{staticClass:"d-flex bb-s-01-eeeeee pt-32 pb-16"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3"},[t._v("身份证号")]),i("v-uni-input",{staticClass:"w-480 ml-46 font-32 text-3",attrs:{type:"idcard",maxlength:18,placeholder:"请输入您的身份证号码","placeholder-style":"color:#999;font-size:32rpx;"},model:{value:t.IdNumber,callback:function(e){t.IdNumber=e},expression:"IdNumber"}})],1),i("v-uni-view",{staticClass:"d-flex bb-s-01-eeeeee pt-48 pb-32"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3"},[t._v("认证信息")]),i("v-uni-view",{staticClass:"flex-1 d-flex j-sb ml-46",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showSelectPop=!0}}},[i("v-uni-view",{staticClass:"w-480 font-32",class:t.cerName?"text-3":"text-9"},[t._v(t._s(t.cerName?t.cerName:"请选择认证信息请选择"))]),i("u-icon",{attrs:{name:"arrow-right",size:20,color:"#333"}})],1)],1)],1),i("v-uni-view",{staticClass:"ptb-32-plr-00"},[i("v-uni-view",{staticClass:"font-32 pl-32 font-wei text-3"},[t._v("上传照片")]),i("v-uni-view",{staticClass:"mt-32"},[i("vh-upload",{ref:"uUpload",attrs:{plate:1,width:328,height:228,multiple:!1,directory:"vinehoo/client/certification/","file-list":t.uploadFileList,"auto-upload":!1},on:{"on-list-change":function(e){arguments[0]=e=t.$handleEvent(e),t.onListChange.apply(void 0,arguments)},"on-uploaded":function(e){arguments[0]=e=t.$handleEvent(e),t.onUploaded.apply(void 0,arguments)}}})],1)],1)],1),i("v-uni-view",{staticClass:"ptb-00-plr-32"},[i("v-uni-view",{staticClass:"mt-176"},[2===t.status&&t.rejectionReason?i("v-uni-view",{},[i("v-uni-view",{staticClass:"font-24 text-3 l-h-40"},[t._v("温馨提示：")]),i("v-uni-view",{staticClass:"font-24 text-3 l-h-40"},[t._v("驳回原因："+t._s(t.rejectionReason))])],1):t._e(),t.recertification?t._e():i("v-uni-view",{},[i("v-uni-view",{staticClass:"font-24 text-9 l-h-40"},[t._v("温馨提示：")]),i("v-uni-view",{staticClass:"font-24 text-9 l-h-40"},[t._v("为了确保您的认证信息快速认证，请您认真的填写登记信息，并将您的身份证和专业证书以张片的形式提交，我们将在3个工作日内进行审核，认证成功后，您将获得对应的认证徽章。")])],1)],1),i("v-uni-view",{staticClass:"d-flex j-center a-center mt-30 pb-62"},[0==t.status&&t.recertification?i("v-uni-view",[i("u-button",{attrs:{shape:"circle",ripple:!0,"ripple-bg-color":"#ffffff","custom-style":{width:"646rpx",color:"#fff",backgroundColor:"#FCE4E3",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.feedback.toast({title:"您提交的信息正在审核中..."})}}},[t._v("审核中...")])],1):t.status>0&&t.recertification?i("v-uni-view",[i("u-button",{attrs:{shape:"circle",ripple:!0,"ripple-bg-color":"#ffffff","custom-style":{width:"646rpx",color:"#fff",backgroundColor:"#E80404",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleRecertification.apply(void 0,arguments)}}},[t._v("重新认证")])],1):i("v-uni-view",[i("u-button",{attrs:{disabled:!t.canSubmit,shape:"circle",ripple:!0,"ripple-bg-color":"#ffffff","custom-style":{width:"646rpx",color:"#fff",backgroundColor:t.canSubmit?"#E80404":"#FCE4E3",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submit.apply(void 0,arguments)}}},[t._v("提交申请")])],1)],1)],1)],1),t.submitSucc?i("v-uni-view",{staticClass:"d-flex flex-column a-center"},[i("v-uni-image",{staticClass:"mt-196 w-266 h-184",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/certification_detail/cer_succ.png",mode:"aspectFill"}}),i("v-uni-view",{staticClass:"mt-106 font-40 font-wei text-3"},[t._v("提交成功")]),i("v-uni-view",{staticClass:"mt-12 font-28 text-9"},[t._v("我们将在七个工作日内将结果短信通知您")]),i("v-uni-view",{staticClass:"p-fixed bottom-60 w-p100 d-flex j-center"},[i("u-button",{attrs:{shape:"circle",ripple:!0,"ripple-bg-color":"#ffffff","custom-style":{width:"646rpx",color:"#fff",backgroundColor:"#E80404",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.back.apply(void 0,arguments)}}},[t._v("返回")])],1)],1):t._e(),i("v-uni-view",{},[i("u-popup",{attrs:{mode:"bottom",height:811,"border-radius":20},model:{value:t.showSelectPop,callback:function(e){t.showSelectPop=e},expression:"showSelectPop"}},[i("v-uni-view",{staticClass:"p-rela"},[i("v-uni-view",{staticClass:"p-stic top-0 bg-ffffff d-flex j-center a-center bb-s-01-f7f7f7 ptb-40-plr-00 font-36 font-wei text-3"},[t._v("认证选择")]),i("v-uni-view",{staticClass:"fade-in"},[i("v-uni-view",{staticClass:"h-592 d-flex"},[i("v-uni-view",{staticClass:"w-284 h-p100 bg-f7f7f7 o-scr-y"},t._l(t.certList,(function(e,a){return i("v-uni-view",{key:a,staticClass:"p-rela ptb-32-plr-24 text-6",class:a==t.firCerIndex?"active bg-ffffff font-wei text-3":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selFirCerList(a)}}},[t._v(t._s(e.name))])})),1),i("v-uni-view",{staticClass:"w-466 h-p100 bg-ffffff o-scr-y"},[i("v-uni-view",{staticClass:"d-flex flex-column pb-48 pl-24 pr-24"},t._l(t.certList[t.firCerIndex].list,(function(e,a){return i("v-uni-view",{key:a,staticClass:"mt-40 font-28 text-3",class:e.info==t.cerName?"bg-fce0e0 b-rad-36 ptb-16-plr-40 text-e80404":"",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.selSecCerList(e.info)}}},[t._v(t._s(e.info))])})),1)],1)],1),i("v-uni-view",{staticClass:"p-fixed bottom-0 z-999 bg-ffffff w-p100 h-98 d-flex j-sa a-center b-sh-00021200-022 pl-32 pr-32"},[i("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"308rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#999",backgroundColor:"#EEEEEE",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancelSelect.apply(void 0,arguments)}}},[t._v("取消")]),i("u-button",{attrs:{disabled:!t.cerName,shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"308rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:t.cerName?"#E80404":"#FCE4E3",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showSelectPop=!1}}},[t._v("确定")])],1)],1)],1)],1)],1)],1)},s=[]},"724e":function(t,e,i){"use strict";i.r(e);var a=i("820d"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},"7f75":function(t,e,i){var a=i("24fb");e=a(!1),e.push([t.i,'.active[data-v-4e527454]::after{content:"";position:absolute;top:%?32?%;left:0;height:%?36?%;width:%?8?%;background:linear-gradient(180deg,#e70000,#ff8383);border-radius:%?4?%}',""]),t.exports=e},"820d":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var a={name:"vh-gap",props:{bgColor:{type:String,default:"transparent"},height:{type:[String,Number],default:30}},computed:{gapStyle:function(){return{backgroundColor:this.bgColor,height:this.height+"rpx"}}}};e.default=a},b9e0:function(t,e,i){"use strict";i.r(e);var a=i("c63c"),n=i.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);e["default"]=n.a},c63c:function(t,e,i){"use strict";i("7a82");var a=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("498a"),i("ac1f"),i("00b4"),i("b64b"),i("d81d"),i("4de4"),i("d3b7");var n=a(i("f07e")),s=a(i("c964")),r=a(i("f3f3")),c=i("26cb"),o={name:"certification-detail",data:function(){return{status:null,rejectionReason:"",recertification:!0,certificationInfo:{},realName:"",IdNumber:"",uploadFileList:[],idCardImg:"",cerQuaImg:"",cerName:"",submitSucc:!1,showSelectPop:!1,certList:[],firCerIndex:0}},onLoad:function(){this.getCertificationInfo(),this.getCertificationList()},computed:(0,r.default)((0,r.default)({},(0,c.mapState)(["ossPrefix"])),{},{canSubmit:function(){return!!(this.$u.trim(this.realName,"all")&&this.$u.trim(this.IdNumber,"all")&&this.$u.test.idCard(this.IdNumber)&&this.cerName&&2==this.uploadFileList.length)}}),methods:{getCertificationInfo:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i,a,s,r,c,o,l,u,f;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.certificationInfo();case 2:i=e.sent,Object.keys(i.data).length?(a=i.data,s=a.status,r=a.real_name,c=a.id_card,o=a.info,l=a.remarks,u=a.idcard_img,f=a.quali_img,t.certificationInfo=i.data,t.status=s,t.rejectionReason=l,t.realName=r,t.IdNumber=c,t.cerName=o,t.idCardImg=u,t.cerQuaImg=f):t.recertification=!1;case 4:case"end":return e.stop()}}),e)})))()},initUploadFileList:function(){this.uploadFileList=[{fileType:"image",cerType:"idCardImg",videoCoverImg:"",url:this.idCardImg,response:this.idCardImg,progress:100,error:!1,file:{}},{fileType:"image",cerType:"cerQuaImg",videoCoverImg:"",url:this.cerQuaImg,response:this.cerQuaImg,progress:100,error:!1,file:{}}]},getCertificationList:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.certificationList();case 2:i=e.sent,t.certList=i.data.list;case 4:case"end":return e.stop()}}),e)})))()},selFirCerList:function(t){this.firCerIndex=t},selSecCerList:function(t){this.cerName=t},cancelSelect:function(){this.cerName="",this.firCerIndex=0,this.showSelectPop=!1},onListChange:function(t){console.log("-----------------------上传列表发生改变"),this.uploadFileList=t,this.handleUploadFiles(t)},onUploaded:function(t,e){console.log("-------上传所有文件成功"),console.log(t),console.log(e),this.handleUploadFiles(t),this.applyCertification()},handleUploadFiles:function(t){var e=t.filter((function(t){return"idCardImg"==t.cerType})).map((function(t){return t.response})),i=t.filter((function(t){return"cerQuaImg"==t.cerType})).map((function(t){return t.response}));this.idCardImg=e.join(","),this.cerQuaImg=i.join(",")},applyCertification:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i={},i.real_name=t.realName,i.id_card=t.IdNumber,i.info=t.cerName,i.idcard_img=t.idCardImg,i.quali_img=t.cerQuaImg,console.log("这是要上传的数据"),console.log(i),e.prev=8,e.next=11,t.$u.api.submitCertification(i);case 11:e.sent,t.submitSucc=!0,t.feedback.toast({title:"提交成功！"}),e.next=19;break;case 16:e.prev=16,e.t0=e["catch"](8),console.log(e.t0);case 19:case"end":return e.stop()}}),e,null,[[8,16]])})))()},handleRecertification:function(){this.recertification=!1,this.initUploadFileList(),this.status=0},submit:function(){this.feedback.loading({title:"提交中..."}),this.$refs.uUpload.upload()},back:function(){this.recertification=!0,this.submitSucc=!1,this.getCertificationInfo()}}};e.default=o},c6d1:function(t,e,i){"use strict";var a=i("303c"),n=i.n(a);n.a},f440:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var a=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{style:[this.gapStyle]})},n=[]}}]);