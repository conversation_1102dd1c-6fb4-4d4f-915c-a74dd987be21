(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageJ-pages-gift-card-select-gift-card"],{"12c6":function(t,e,a){"use strict";a.r(e);var n=a("51bd"),i=a("f074");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("f2f9");var o=a("f0c5"),r=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"519170ec",null,!1,n["a"],void 0);e["default"]=r.exports},"3bf4":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,".select-gift-card-container[data-v-6820ac95]{min-height:100vh;padding-bottom:%?120?%}.card-display[data-v-6820ac95]{width:100%}.card-preview[data-v-6820ac95]{width:100%;border-radius:%?12?%;position:relative;overflow:hidden;display:flex;justify-content:flex-end;align-items:flex-start;aspect-ratio:503/320;; /* 控制比例 */background-size:contain;background-position:50%}.card-amount[data-v-6820ac95]{position:absolute;bottom:%?20?%;right:%?20?%}.quantity-control[data-v-6820ac95]{margin-top:%?16?%}.number-control[data-v-6820ac95]{display:flex;align-items:center}.btn-minus[data-v-6820ac95], .btn-plus[data-v-6820ac95]{width:%?60?%;height:%?60?%;border:%?1?% solid #ddd;background-color:#f8f8f8}.btn-minus[data-v-6820ac95]{border-radius:%?6?% 0 0 %?6?%}.btn-plus[data-v-6820ac95]{border-radius:0 %?6?% %?6?% 0}.btn-disabled[data-v-6820ac95]{color:#ccc}.quantity-input[data-v-6820ac95]{width:%?80?%;height:%?60?%;border-top:%?1?% solid #ddd;border-bottom:%?1?% solid #ddd}.divider[data-v-6820ac95]{height:%?20?%;background-color:#f5f5f5;margin:%?20?% 0}.physical-card-section[data-v-6820ac95]{margin-bottom:%?100?%}.checkbox[data-v-6820ac95]{width:%?36?%;height:%?36?%;border:%?1?% solid #ddd;border-radius:%?4?%;display:flex;justify-content:center;align-items:center}.checkbox-checked[data-v-6820ac95]{border-color:#e80404;background-color:#e80404}.checkbox-inner[data-v-6820ac95]{width:%?20?%;height:%?20?%;border-radius:%?2?%;background-color:#fff}.address-info[data-v-6820ac95]{padding:%?10?% 0 %?30?%}.address-item[data-v-6820ac95]{width:100%}.input-field[data-v-6820ac95]{width:100%}.select-from-address[data-v-6820ac95]{padding:%?20?% 0;text-align:center;border-radius:%?8?%;border:%?1?% solid #ddd}.bottom-bar[data-v-6820ac95]{position:fixed;bottom:0;left:0;width:100%;height:%?100?%;background-color:#fff;border-top:%?1?% solid #eee;padding:0 %?30?%;z-index:100}.buy-button[data-v-6820ac95]{width:%?200?%;height:%?70?%;background-color:#e80404;border-radius:%?35?%}",""]),t.exports=e},"3f14":function(t,e,a){"use strict";a.r(e);var n=a("6f9d"),i=a("e433");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("a8d2");var o=a("f0c5"),r=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"6820ac95",null,!1,n["a"],void 0);e["default"]=r.exports},4932:function(t,e,a){var n=a("3bf4");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("2577266a",n,!0,{sourceMap:!1,shadowMode:!1})},"4be1":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("acd8"),a("b64b"),a("99af");var i=n(a("f07e")),s=n(a("c964")),o=n(a("f3f3")),r=a("26cb"),d={data:function(){return{cardAmount:200,background:"",cardId:0,quantity:1,isPhysicalCard:!1,addressInfo:{consignee:"",consignee_phone:"",region:"",province_id:"",province_name:"",city_id:"",city_name:"",town_id:"",town_name:"",address:"",delivery_note:""}}},computed:(0,o.default)((0,o.default)({},(0,r.mapState)(["routeTable","addressInfoState"])),{},{totalPrice:function(){return this.cardAmount*this.quantity}}),onLoad:function(t){t&&t.amount&&(this.cardAmount=parseFloat(t.amount),this.cardId=t.id),this.requestGiftCardList(),uni.setNavigationBarTitle({title:"选择礼品卡"})},onShow:function(){Object.keys(this.addressInfoState).length&&(console.log("addressInfoState",this.addressInfoState),this.setAddressInfo(this.addressInfoState))},methods:(0,o.default)((0,o.default)({},(0,r.mapMutations)(["muPayInfo"])),{},{increaseQuantity:function(){this.quantity++},requestGiftCardList:function(){var t=this;return(0,s.default)((0,i.default)().mark((function e(){var a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.giftCardsInfo({id:t.cardId});case 2:a=e.sent,t.background=a.data.background;case 4:case"end":return e.stop()}}),e)})))()},decreaseQuantity:function(){this.quantity>1&&this.quantity--},togglePhysicalCard:function(){this.isPhysicalCard=!this.isPhysicalCard},openRegionPicker:function(){uni.showToast({title:"打开地区选择器",icon:"none"})},selectFromAddressList:function(){this.jump.navigateTo("".concat(this.$routeTable.pEAddressManagement,"?comeFrom=100"))},setAddressInfo:function(t){t&&(this.addressInfo={consignee:t.consignee||"",consignee_phone:t.consignee_phone||"",region:"".concat(t.province_name||""," ").concat(t.city_name||""," ").concat(t.town_name||""),province_id:t.province_id||"",province_name:t.province_name||"",city_id:t.city_id||"",city_name:t.city_name||"",town_id:t.town_id||"",town_name:t.town_name||"",address:t.address||"",delivery_note:""})},onBuyNow:function(){var t=this;return(0,s.default)((0,i.default)().mark((function e(){var a,n,s,r;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.feedback.loading({title:"正在下单..."}),e.prev=1,a={goods_id:t.cardId,order_from:t.$client,type:2,order_qty:t.quantity},e.next=5,t.$u.api.giftCardOrderCreate(a);case 5:n=e.sent,s=(0,o.default)({payPlate:60},n.data),t.$app?(console.log("111111111111111-----"),r={paylmfor:s,type:60,priceString:s.payment_amount,androidMainOrderNo:s.order_no,androidFrom:"60"},t.jump.jumpAppPayment(t.$vhFrom,r),"next"!=t.$vhFrom&&wineYunJsBridge.openAppPage({client_path:{ios_path:"finish",android_path:"finish"}})):(t.muPayInfo(s),t.jump.appAndMiniJump(1,t.routeTable.pBPayment,t.$vhFrom,1)),e.next=13;break;case 10:e.prev=10,e.t0=e["catch"](1),console.log(e.t0);case 13:case"end":return e.stop()}}),e,null,[[1,10]])})))()}})};e.default=d},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return n}));var n={uIcon:a("e5e1").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},s=[]},"6f9d":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return n}));var n={vhNavbar:a("12c6").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"select-gift-card-container bg-ffffff"},[""==t.$vhFrom?a("v-uni-view",{},[a("vh-navbar",{attrs:{title:"选择礼品卡"}})],1):t._e(),a("v-uni-view",{staticClass:"card-display ptb-20-plr-24"},[a("v-uni-view",{staticClass:"card-preview ",style:{backgroundImage:"url("+t.background+")",backgroundRepeat:"no-repeat"}}),a("v-uni-view",{staticClass:"quantity-control flex-sb-c pt-20 ptb-00-plr-06"},[a("v-uni-text",{staticClass:"text-e80404 font-28"},[t._v(t._s(t.cardAmount)+"元储值卡")]),a("v-uni-view",{staticClass:"number-control flex-s-c"},[a("v-uni-view",{staticClass:"btn-minus flex-c-c",class:t.quantity<=1?"btn-disabled":"",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.decreaseQuantity.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"font-36"},[t._v("-")])],1),a("v-uni-view",{staticClass:"quantity-input flex-c-c"},[a("v-uni-text",{staticClass:"text-3 font-28"},[t._v(t._s(t.quantity))])],1),a("v-uni-view",{staticClass:"btn-plus flex-c-c",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.increaseQuantity.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"font-36"},[t._v("+")])],1)],1)],1),a("v-uni-view",{staticClass:"card-tips mt-20 ptb-00-plr-06"},[a("v-uni-text",{staticClass:"font-26 text-9"},[t._v("储值卡金额不折现、不找零、不退换、全场可用(跨境等特殊商品除外)")])],1)],1),a("v-uni-view",{staticClass:"physical-card-section ptb-20-plr-24"},[t.isPhysicalCard?a("v-uni-view",{staticClass:"address-info"},[a("v-uni-view",{staticClass:"address-item ptb-16-plr-0 bb-s-01-eeeeee"},[a("v-uni-input",{staticClass:"input-field font-28 text-3",attrs:{type:"text",placeholder:"收件人"},model:{value:t.addressInfo.consignee,callback:function(e){t.$set(t.addressInfo,"consignee",e)},expression:"addressInfo.consignee"}})],1),a("v-uni-view",{staticClass:"address-item ptb-16-plr-0 bb-s-01-eeeeee"},[a("v-uni-input",{staticClass:"input-field font-28 text-3",attrs:{type:"number",maxlength:"11",placeholder:"联系电话"},model:{value:t.addressInfo.consignee_phone,callback:function(e){t.$set(t.addressInfo,"consignee_phone",e)},expression:"addressInfo.consignee_phone"}})],1),a("v-uni-view",{staticClass:"address-item ptb-16-plr-0 bb-s-01-eeeeee flex-sb-c",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openRegionPicker.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"font-28",class:t.addressInfo.region?"text-3":"text-9"},[t._v(t._s(t.addressInfo.region||"所在省市区"))]),a("v-uni-text",{staticClass:"font-28 text-9"},[t._v(">")])],1),a("v-uni-view",{staticClass:"address-item ptb-16-plr-0 bb-s-01-eeeeee"},[a("v-uni-input",{staticClass:"input-field font-28 text-3",attrs:{type:"text",placeholder:"详细地址"},model:{value:t.addressInfo.address,callback:function(e){t.$set(t.addressInfo,"address",e)},expression:"addressInfo.address"}})],1),a("v-uni-view",{staticClass:"address-item ptb-16-plr-0"},[a("v-uni-input",{staticClass:"input-field font-28 text-3",attrs:{type:"text",placeholder:"便于日后投递"},model:{value:t.addressInfo.delivery_note,callback:function(e){t.$set(t.addressInfo,"delivery_note",e)},expression:"addressInfo.delivery_note"}})],1),a("v-uni-view",{staticClass:"select-from-address mt-16",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectFromAddressList.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"font-28 text-3"},[t._v("从地址库选择")])],1)],1):t._e()],1),a("v-uni-view",{staticClass:"bottom-bar flex-sb-c"},[a("v-uni-view",{staticClass:"price-section"},[a("v-uni-text",{staticClass:"font-28 text-3"},[t._v("合计：")]),a("v-uni-text",{staticClass:"font-32 text-e80404 font-wei"},[t._v(t._s(t.totalPrice.toFixed(2))+"元")])],1),a("v-uni-view",{staticClass:"buy-button flex-c-c",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onBuyNow.apply(void 0,arguments)}}},[a("v-uni-text",{staticClass:"font-28 text-ffffff"},[t._v("购买")])],1)],1)],1)},s=[]},"7f1a":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var s=a("26cb"),o=uni.getSystemInfoSync(),r={},d={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:r,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,s.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,n=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(n)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=d},a126:function(t,e,a){var n=a("bbdc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("703d0287",n,!0,{sourceMap:!1,shadowMode:!1})},a8d2:function(t,e,a){"use strict";var n=a("4932"),i=a.n(n);i.a},bbdc:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},e433:function(t,e,a){"use strict";a.r(e);var n=a("4be1"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},f074:function(t,e,a){"use strict";a.r(e);var n=a("7f1a"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},f2f9:function(t,e,a){"use strict";var n=a("a126"),i=a.n(n);i.a}}]);