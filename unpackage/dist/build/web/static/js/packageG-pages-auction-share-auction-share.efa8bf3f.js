(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageG-pages-auction-share-auction-share"],{"2c80":function(t,n,e){var a=e("6274");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=e("4f06").default;i("5b7d0056",a,!0,{sourceMap:!1,shadowMode:!1})},"36e8":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",{staticClass:"content"},[e("v-uni-image",{staticClass:"w-p100",attrs:{src:t.ossIcon("/auction_share/bg.jpg"),mode:"widthFix"}}),e("v-uni-button",{staticClass:"share",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.feedback.toast({title:t.isWxEnv?"请点击右上角...分享":"请在微信浏览器中打开"})}}},[t._v("分享")]),e("v-uni-button",{staticClass:"auction",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.jump.reLaunch(t.routeTable.pgIndex)}}},[t._v("去拍卖")])],1)},i=[]},5580:function(t,n,e){"use strict";e("7a82");var a=e("ee27").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("ac1f"),e("00b4");var i=a(e("f3f3")),s=e("26cb"),o={data:function(){return{isWxEnv:!1}},computed:(0,i.default)({},(0,s.mapState)(["routeTable"])),onLoad:function(){this.init()},methods:{init:function(){var t=window.navigator.userAgent.toLowerCase();if(this.isWxEnv=/micromessenger/.test(t),this.isWxEnv){var n={link:window.location.href,imgUrl:this.ossIcon("/auction_share/share.jpg")};this.share.h5ShareWeixin((0,i.default)((0,i.default)({},{title:"春日微醺 专场拍卖",desc:"酒云网春日快拍活动浪漫邀约！快来酒云拍卖将春日美景填满酒杯！"}),n),(0,i.default)((0,i.default)({},{title:"酒云网春日美酒专场拍卖！微醺美酒等你捡漏~",desc:""}),n))}}}};n.default=o},6274:function(t,n,e){var a=e("24fb");n=a(!1),n.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.share[data-v-bbe6f2dc], .auction[data-v-bbe6f2dc]{position:fixed;top:%?40?%;right:%?-24?%;width:%?150?%;height:%?52?%;display:flex;justify-content:flex-start;align-items:center;background-color:rgba(0,0,0,.5);border-radius:%?25?%;font-family:OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,PingFang SC,Hiragino Sans GB,Microsoft Yahei,sans-serif;font-weight:500;font-size:%?24?%;color:#fff}.share[data-v-bbe6f2dc]{padding-left:%?40?%}.auction[data-v-bbe6f2dc]{top:%?120?%}',""]),t.exports=n},"6f3c":function(t,n,e){"use strict";var a=e("2c80"),i=e.n(a);i.a},"76c6":function(t,n,e){"use strict";e.r(n);var a=e("36e8"),i=e("a165");for(var s in i)["default"].indexOf(s)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(s);e("6f3c");var o=e("f0c5"),c=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"bbe6f2dc",null,!1,a["a"],void 0);n["default"]=c.exports},a165:function(t,n,e){"use strict";e.r(n);var a=e("5580"),i=e.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(s);n["default"]=i.a}}]);