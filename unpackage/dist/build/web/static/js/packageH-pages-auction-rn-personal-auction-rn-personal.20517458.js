(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-rn-personal-auction-rn-personal"],{"12c6":function(t,e,n){"use strict";n.r(e);var a=n("51bd"),i=n("f074");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);n("f2f9");var r=n("f0c5"),c=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);e["default"]=c.exports},"21ac":function(t,e,n){"use strict";n.r(e);var a=n("ac36"),i=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=i.a},"2f2a":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return a}));var a={vhNavbar:n("12c6").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("vh-navbar",{attrs:{title:"实名认证",height:"46"}}),t.loading?t._e():n("v-uni-view",[t.rnInfo.status===t.MAuctionRNStatus.OnCheck?n("v-uni-view",{staticClass:"pt-332"},[n("v-uni-view",{staticClass:"flex-c-c"},[n("v-uni-image",{staticClass:"w-440 h-400",attrs:{src:t.ossIcon("/auction/rn_on_check_440_400.png")}})],1),n("v-uni-view",{staticClass:"mt-60 font-wei-500 font-36 text-3 l-h-66 text-center"},[t._v("审核中")]),n("v-uni-view",{staticClass:"mt-20 font-28 text-6 l-h-44 text-center"},[t._v("实名认证审核中, 请兔子君耐心等待哦～")])],1):t.rnInfo.status===t.MAuctionRNStatus.Passed?n("v-uni-view",{staticClass:"pt-200"},[n("v-uni-view",{staticClass:"flex-c-c"},[n("v-uni-image",{staticClass:"w-442 h-400",attrs:{src:t.ossIcon("/auction/rn_passed_442_400.png")}})],1),n("v-uni-view",{staticClass:"mt-72 font-wei-500 font-36 text-3 l-h-50 text-center"},[t._v("您已完成实名认证")]),n("v-uni-view",{staticClass:"mt-28 font-28 text-6 l-h-40 text-center"},[t._v(t._s(t._f("toAsteriskStr")(t.rnInfo.name))+"  "+t._s(t._f("toAsteriskStr")(t.rnInfo.id_card)))])],1):t.rnInfo.status===t.MAuctionRNStatus.Rejected?n("v-uni-view",{staticClass:"pt-332"},[n("v-uni-view",{staticClass:"flex-c-c"},[n("v-uni-image",{staticClass:"w-440 h-400",attrs:{src:t.ossIcon("/auction/rn_reject_440_400.png")}})],1),n("v-uni-view",{staticClass:"mt-60 font-wei-500 font-26 text-3 l-h-50 text-center"},[t._v("很抱歉，实名认证未通过")]),n("v-uni-view",{staticClass:"mt-20 font-28 text-6 l-h-40 text-center"},[t._v(t._s(t.rnInfo.reject_reason))]),n("v-uni-view",{staticClass:"flex-c-c mt-230"},[n("v-uni-button",{staticClass:"vh-btn flex-c-c w-646 h-80 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-40",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.rnInfo.status=t.MAuctionRNStatus.NotSubmit}}},[t._v("重新认证")])],1)],1):n("v-uni-view",[n("v-uni-view",{staticClass:"p-32 font-28 text-6 l-h-40"},[t._v("为保障平台交易安全和对您进行合法有效的保护，根据《中华人民共和国网络安全法》《中华人民共和国未成年人保护法》等相关法律法规，以确保您实名认证信息与您本人真实身份信息的一致性，身份认证由阿里云提供的API认证接口实现对您身份的肴效核定。")]),n("v-uni-view",{staticClass:"mt-40 ptb-00-plr-32"},[n("v-uni-image",{staticClass:"w-34 h-34",attrs:{src:t.ossIcon("/auction/editor_34.png")}}),n("v-uni-view",{staticClass:"flex-s-c h-110 bb-s-02-eeeeee"},[n("v-uni-view",{staticClass:"w-174 font-wei-500 font-32 text-3"},[t._v("真实姓名")]),n("v-uni-input",{staticClass:"flex-1 font-32 text-3 text-right",attrs:{placeholder:"*请输入您的真实姓名","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.rnInfo.name,callback:function(e){t.$set(t.rnInfo,"name",e)},expression:"rnInfo.name"}})],1),n("v-uni-view",{staticClass:"flex-s-c h-110 bb-s-02-eeeeee"},[n("v-uni-view",{staticClass:"w-174 font-wei-500 font-32 text-3"},[t._v("身份证号")]),n("v-uni-input",{staticClass:"flex-1 font-32 text-3 text-right",attrs:{placeholder:"*请输入您的身份证号码","placeholder-style":"font-size:28rpx; color:#999"},model:{value:t.rnInfo.id_card,callback:function(e){t.$set(t.rnInfo,"id_card",e)},expression:"rnInfo.id_card"}})],1),n("v-uni-view",{staticClass:"p-fixed left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-9999"},[n("v-uni-button",{staticClass:"vh-btn flex-c-c w-646 h-80 font-wei-500 font-28 text-ffffff b-rad-40",class:t.disabled?"bg-fce4e3":"bg-e80404",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSubmit.apply(void 0,arguments)}}},[t._v("提交审核")])],1)],1)],1)],1)],1)},s=[]},"51bd":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[n("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},s=[]},"7f1a":function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var s=n("26cb"),r=uni.getSystemInfoSync(),c={},o={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:c,statusBarHeight:r.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,s.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,n=e.pEAddressAdd,a=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(n)||t.includes(a)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=o},a126:function(t,e,n){var a=n("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},ac36:function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("f07e")),s=a(n("c964"));n("99af"),n("cb29"),n("ac1f"),n("00b4"),n("d3b7");var r=n("d8be"),c={name:"auctionRNPersional",data:function(){return{MAuctionRNStatus:r.MAuctionRNStatus,loading:!0,rnInfo:{type:r.MAuctionRNType.PersonalRN,name:"",id_card:""}}},computed:{disabled:function(t){var e=t.rnInfo;return!(e.name&&e.id_card)}},filters:{toAsteriskStr:function(t){var e=t.length;return 2===e?"*".concat(t[e-1]):"".concat(t[0]).concat(Array(e-2).fill("*").join("")).concat(t[e-1])}},methods:{load:function(){var t=this;return(0,s.default)((0,i.default)().mark((function e(){var n,a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.getAuctionRNInfo({type:r.MAuctionRNType.PersonalRN});case 2:n=e.sent,a=(null===n||void 0===n?void 0:n.data)||{},t.rnInfo=Object.assign({},t.rnInfo,a);case 5:case"end":return e.stop()}}),e)})))()},onSubmit:function(){var t=this;return(0,s.default)((0,i.default)().mark((function e(){var n,a,s,r,c,o;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.disabled){e.next=2;break}return e.abrupt("return");case 2:if(n=t.rnInfo,a=n.id,s=n.type,r=n.name,c=n.id_card,t.$u.test.idCard(c)){e.next=6;break}return t.feedback.toast({title:"请填写正确的身份证号码"}),e.abrupt("return");case 6:if(t.feedback.loading({title:"提交审核中..."}),o={type:s,name:r,id_card:c},a&&(o.id=a),!a){e.next=14;break}return e.next=12,t.$u.api.editAuctionRNInfo(o);case 12:e.next=16;break;case 14:return e.next=16,t.$u.api.addAuctionRNInfo(o);case 16:t.load();case 17:case"end":return e.stop()}}),e)})))()}},onLoad:function(){var t=this;this.login.isLoginV3(this.$vhFrom).then((function(e){e&&t.load().finally((function(){t.loading=!1}))}))}};e.default=c},acfb:function(t,e,n){"use strict";n.r(e);var a=n("2f2a"),i=n("21ac");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);var r=n("f0c5"),c=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"0822e5f8",null,!1,a["a"],void 0);e["default"]=c.exports},bbdc:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},cb29:function(t,e,n){"use strict";var a=n("23e7"),i=n("81d5"),s=n("44d2");a({target:"Array",proto:!0},{fill:i}),s("fill")},f074:function(t,e,n){"use strict";n.r(e);var a=n("7f1a"),i=n.n(a);for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);e["default"]=i.a},f2f9:function(t,e,n){"use strict";var a=n("a126"),i=n.n(a);i.a}}]);