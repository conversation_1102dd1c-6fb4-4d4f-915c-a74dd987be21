(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-miaofa-cardDetail"],{"12c6":function(n,t,e){"use strict";e.r(t);var i=e("51bd"),s=e("f074");for(var a in s)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return s[n]}))}(a);e("f2f9");var o=e("f0c5"),c=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,"519170ec",null,!1,i["a"],void 0);t["default"]=c.exports},"144f":function(n,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return s})),e.d(t,"a",(function(){}));var i=function(){var n=this.$createElement,t=this._self._c||n;return t("v-uni-view",{staticClass:"d-flex j-center",style:[this.outerEmpConStyle]},[t("v-uni-view",{staticClass:"p-rela",style:[this.innEmpConStyle]},[t("v-uni-image",{staticClass:"w-p100 h-p100",attrs:{src:this.imageSrc,mode:"aspectFill"}}),t("v-uni-view",{staticClass:"p-abso w-p100 d-flex j-center font-28 text-9",style:[this.textStyle]},[this._v(this._s(this.text))])],1)],1)},s=[]},2293:function(n,t,e){var i=e("24fb");t=i(!1),t.push([n.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 一级分类 */.fir-cla-bg[data-v-772bf543]{background-image:url(https://images.vinehoo.com/vinehoomini/v3/second_hair_second/cla_bg.png);background-size:cover}\n/* .fir-cla-list view:first-child{\n          margin-left: 0;\n      }\n      .fir-cla-list view:last-child{\n          margin-right: 0;\n      } */",""]),n.exports=t},3671:function(n,t,e){var i=e("2293");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[n.i,i,""]]),i.locals&&(n.exports=i.locals);var s=e("4f06").default;s("53c806e6",i,!0,{sourceMap:!1,shadowMode:!1})},"51bd":function(n,t,e){"use strict";e.d(t,"b",(function(){return s})),e.d(t,"c",(function(){return a})),e.d(t,"a",(function(){return i}));var i={uIcon:e("e5e1").default},s=function(){var n=this,t=n.$createElement,e=n._self._c||t;return e("v-uni-view",{},[e("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":n.isFixed},n.navbarClass],style:[n.navbarStyle]},[e("v-uni-view",{staticClass:"vh-status-bar",style:{height:(n.appStatusBarHeight||n.customStatusBarHeight||n.statusBarHeight)+"px"}}),e("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":n.newYearTheme},style:[n.navbarInnerStyle]},[n.isBack?e("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.goBack.apply(void 0,arguments)}}},[n.vhFrom?e("u-icon",{attrs:{name:"nav-back",color:n.backIconColor,size:n.backIconSize}}):[e("u-icon",{attrs:{name:n.pageLength<=1?"home":n.backIconName,color:n.backIconColor,size:n.backIconSize}}),n.backText?e("v-uni-view",{staticClass:"vh-back-text",style:[n.backTextStyle]},[n._v(n._s(n.backText))]):n._e()]],2):n._e(),n.title?e("v-uni-view",{staticClass:"vh-navbar-content-title",style:[n.titleStyle]},[e("v-uni-view",{staticClass:"vh-title",style:{color:n.titleColor,fontSize:n.titleSize+"rpx",fontWeight:n.titleBold?"bold":"normal"}},[n._v(n._s(n.title))])],1):n._e(),e("v-uni-view",{staticClass:"vh-slot-content"},[n._t("default")],2),e("v-uni-view",{staticClass:"vh-slot-right"},[n._t("right")],2)],1)],1),n.isFixed&&!n.immersive?e("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(n.navbarHeight)+(n.appStatusBarHeight||n.customStatusBarHeight||n.statusBarHeight)+"px"}}):n._e()],1)},a=[]},5572:function(n,t,e){var i=e("24fb");t=i(!1),t.push([n.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.unumberbox[data-v-772bf543]  .u-numberbox{border:1px solid #eee;border-radius:%?11?%}.unumberbox[data-v-772bf543]  .u-number-input{margin:0;padding:0 3px}.unumberbox[data-v-772bf543]  .u-icon-minus,\n.unumberbox[data-v-772bf543]  .u-icon-plus{width:%?50?%!important;background-color:#fff!important}.unumberbox[data-v-772bf543]  .u-icon-minus.u-icon-disabled .uicon-minus,\n.unumberbox[data-v-772bf543]  .u-icon-minus.u-icon-disabled .uicon-plus,\n.unumberbox[data-v-772bf543]  .u-icon-plus.u-icon-disabled .uicon-minus,\n.unumberbox[data-v-772bf543]  .u-icon-plus.u-icon-disabled .uicon-plus{color:#ddd!important}.unumberbox[data-v-772bf543]  .uicon-minus,\n.unumberbox[data-v-772bf543]  .uicon-plus{font-size:%?24?%!important;color:#666!important}.hair-title[data-v-772bf543]{font-weight:600;font-size:%?32?%;color:#333;margin-left:%?24?%;padding-top:%?40?%;line-height:%?44?%;text-align:left;font-style:normal}',""]),n.exports=t},"55c2":function(n,t,e){"use strict";e("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("a9e3");var i={name:"vh-empty",props:{bgColor:{type:String,default:"#FFF"},paddingTop:{type:[String,Number],default:0},paddingBottom:{type:[String,Number],default:0},width:{type:[String,Number],default:440},height:{type:[String,Number],default:360},imageSrc:{type:String,default:""},textBottom:{type:[String,Number],default:46},text:{type:String,default:""}},computed:{outerEmpConStyle:function(){return{backgroundColor:this.bgColor,paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},innEmpConStyle:function(){return{width:this.width+"rpx",height:this.height+"rpx"}},textStyle:function(){return{bottom:this.textBottom+"rpx"}}}};t.default=i},"5ba4":function(n,t,e){"use strict";e.r(t);var i=e("144f"),s=e("a58c");for(var a in s)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return s[n]}))}(a);var o=e("f0c5"),c=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,"55488dce",null,!1,i["a"],void 0);t["default"]=c.exports},"784b":function(n,t,e){"use strict";e.r(t);var i=e("9e3f"),s=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(a);t["default"]=s.a},"7f1a":function(n,t,e){"use strict";e("7a82");var i=e("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=i(e("f3f3"));e("a9e3"),e("caad6"),e("2532");var a=e("26cb"),o=uni.getSystemInfoSync(),c={},l={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:c,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,s.default)((0,s.default)({},(0,a.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var n={};return n.height=this.navbarHeight+"px",n},navbarStyle:function(){var n={};return n.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(n,this.background),this.showBorder&&Object.assign(n,this.borderStyle),n},titleStyle:function(){var n={};return n.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",n.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",n.width=uni.upx2px(this.titleWidth)+"px",n},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var n=this.pages.getPageFullPath(),t=this.routeTable,e=t.pEAddressAdd,i=t.pEAddressManagement,s=t.pBOrderDepositDetail;(n.includes("/packageH")||n.includes(e)||n.includes(i)||n.includes(s))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};t.default=l},9176:function(n,t,e){"use strict";e.d(t,"b",(function(){return s})),e.d(t,"c",(function(){return a})),e.d(t,"a",(function(){return i}));var i={vhNavbar:e("12c6").default,vhImage:e("ce7c").default,vhEmpty:e("5ba4").default,uPopup:e("c4b0").default,uIcon:e("e5e1").default,uNumberBox:e("3bd6").default,uButton:e("4f1b").default},s=function(){var n=this,t=n.$createElement,e=n._self._c||t;return e("v-uni-view",{staticClass:"content"},[e("vh-navbar",[e("v-uni-view",{staticClass:"flex-sb-c pr-14 w-p100"},[e("v-uni-view",{staticClass:"flex-1 mr-22"},[e("v-uni-view",{staticClass:"flex-sb-c h-60 bg-f5f5f5 b-rad-30",on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.jump.navigateTo(n.routeTable.pFGlobalSearch+"?type=4")}}},[e("v-uni-text",{staticClass:"ml-24 font-24 text-9"},[n._v("大家都在搜")]),e("v-uni-view",{staticClass:"flex-c-c"},[e("v-uni-view",{staticClass:"w-02 h-36 bg-e4e4e4"}),e("v-uni-button",{staticClass:"vh-btn flex-c-c w-100 h-60 font-wei-500 font-24 text-3 bg-transp"},[n._v("搜索")])],1)],1)],1),e("v-uni-view",{staticClass:"p-rela d-flex",on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.openCar.apply(void 0,arguments)}}},[e("v-uni-image",{staticClass:"wh-36 p-10",attrs:{src:n.ossIcon("/second_hair/s_car.png")}}),n.shoppingCartNum?e("v-uni-view",{staticClass:"p-abso top-n-02 right-n-02 w-26 h-26 bg-e80404 b-rad-p50 flex-c-c font-16 text-ffffff"},[n._v(n._s(n.shoppingCartNum))]):n._e()],1)],1)],1),e("v-uni-view",{staticStyle:{"overflow-x":"hidden"}},[e("v-uni-view",{staticClass:"classify-con p-rela z-01 bg-ffffff d-flex o-hid",style:{height:n.classifyContainerRestHeight+"px"}},[e("v-uni-scroll-view",{staticClass:"w-192 h-p100 bg-f5f5f5",attrs:{"scroll-y":!0}},[n._l(n.secondClassifyList,(function(t,i){return e("v-uni-view",{key:i,staticClass:"p-rela w-192 bg-ffffff",on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.handleCategoryClick(i)}}},[e("v-uni-view",{staticClass:"tran-2 ptb-24-plr-20 w-192 d-flex j-center a-center",class:n.secondClassifyIndex==i?"bg-ffffff":n.secondClassifyIndex-1==i?"bg-f5f5f5 b-br-rad-28":n.secondClassifyIndex+1==i?"bg-f5f5f5 b-tr-rad-28":"bg-f5f5f5"},[e("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:n.secondClassifyIndex==i,expression:"secondClassifyIndex == index"}],staticClass:"p-abso left-0 w-06 h-48 b-rad-04 bg-e80404"}),e("v-uni-text",{staticClass:"font-28 text-center",class:n.secondClassifyIndex==i?"font-wei text-e80404":"text-3",staticStyle:{"min-height":"80rpx","line-height":"80rpx"}},[n._v(n._s(t.name))])],1)],1)})),e("v-uni-view",{staticClass:"w-192 h-128 bg-ffffff"},[e("v-uni-view",{staticClass:"wh-p100 bg-f5f5f5",class:n.secondClassifyIndex+1===n.secondClassifyList.length?"b-tr-rad-28":"",staticStyle:{height:"130px"}})],1)],2),e("v-uni-view",{staticClass:"flex-1 h-p100 d-flex flex-column"},[n.secondClassifyInfo.list&&n.secondClassifyInfo.list.length?e("v-uni-view",{staticClass:"w-p100 bg-ffffff pt-12 pb-26 pl-24 pr-24"},[e("v-uni-view",{staticClass:"d-flex flex-wrap ml-n-20"},[e("v-uni-view",{staticClass:"flex-c-c w-88 h-38 bg-f6f6f6 mt-20 ml-20 ptb-02-plr-20 b-rad-08 font-24 text-6",class:-1==n.secondClassifySubIndex?"bg-fce4e3 text-ed2317":"",on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.selectSecondClassifySubIndex(-1)}}},[n._v("全部")]),n._l(n.secondClassifyInfo.list.slice(0,n.max),(function(t,i){return e("v-uni-view",{key:i,on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.selectSecondClassifySubIndex(i)}}},[e("v-uni-view",{staticClass:"flex-c-c h-38 bg-f6f6f6 mt-20 ml-20 ptb-00-plr-20 b-rad-08 font-24 text-6",class:i==n.secondClassifySubIndex?"bg-fce4e3 text-ed2317":""},[n._v(n._s(t))])],1)})),n.secondClassifyInfo.list.length>n.count?e("v-uni-view",{staticClass:"flex-c-c w-112 h-38 mt-20",on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.onExpand.apply(void 0,arguments)}}},[e("v-uni-text",{staticClass:"font-24 text-9"},[n._v(n._s(n.isExpand?"收起":"展开"))]),e("v-uni-image",{staticClass:"ml-04 w-20 h-12",class:n.isExpand?"t-ro-n-180 tran-2":"tran-2",attrs:{src:n.ossIcon("/invoices/arrow_d_20_12.png")}})],1):n._e()],2)],1):n._e(),e("v-uni-view",{staticClass:"w-p100 flex-1 o-hid"},[e("v-uni-scroll-view",{staticClass:"h-p100",attrs:{"scroll-y":!0,"scroll-top":n.scrollTop,"scroll-with-animation":!0},on:{scrolltolower:function(t){arguments[0]=t=n.$handleEvent(t),n.scrollEnd()},scroll:function(t){arguments[0]=t=n.$handleEvent(t),n.handleScroll.apply(void 0,arguments)}}},[e("v-uni-view",{staticStyle:{"padding-bottom":"120px"}},[n.secondHairList.length?e("v-uni-view",{},n._l(n.secondHairList,(function(t,i){return e("v-uni-view",{key:i,attrs:{id:"section-"+i}},[e("v-uni-view",{staticClass:"hair-title"},[n._v(n._s(t.hairTitle))]),n._l(t.goods,(function(t,i){return e("v-uni-view",{key:i,staticClass:"bb-s-01-f0f0f0 ml-24 mr-24 pt-24 pb-24",on:{click:function(e){arguments[0]=e=n.$handleEvent(e),n.jump.navigateTo("/pages/goods-detail/goods-detail?id="+t.id)}}},[e("v-uni-view",{staticClass:"d-flex"},[e("vh-image",{attrs:{src:t.product_img,width:176,height:176,"border-radius":6}}),e("v-uni-view",{staticClass:"flex-1 d-flex flex-column j-sb ml-12"},[e("v-uni-view",{},[e("v-uni-view",{staticClass:"font-28 font-wei-700 text-3 text-hidden-3"},[n._v(n._s(t.title))]),e("v-uni-view",{staticClass:"font-24 mt-10"},[e("v-uni-text",{staticClass:"text-6"},[n._v("已售")]),e("v-uni-text",{staticClass:"text-e80404"},[n._v(n._s(t.vest_purchased+t.purchased))])],1)],1),e("v-uni-view",{staticClass:"d-flex j-sb a-center"},[1==t.is_hidden_price||[3,4].includes(t.onsale_status)?e("v-uni-view",{staticClass:"font-32 font-wei text-e80404"},[n._v("价格保密")]):e("v-uni-view",{},[e("v-uni-text",{staticClass:"font-36 font-wei text-e80404"},[e("v-uni-text",{staticClass:"font-18"},[n._v("¥")]),n._v(n._s(t.price))],1)],1),e("v-uni-image",{staticClass:"w-52 h-52",attrs:{src:n.ossIcon("/second_hair_second/car_gray.png"),mode:"aspectFill"},on:{click:function(e){e.stopPropagation(),arguments[0]=e=n.$handleEvent(e),n.openPackPop(t)}}})],1)],1)],1)],1)}))],2)})),1):e("vh-empty",{attrs:{"padding-top":200,"image-src":n.osip+"/empty/emp_goods.png",text:"暂无秒发商品~"}})],1)],1)],1)],1),n.shoppingCartNum&&n.shoppingCartMoney?e("v-uni-view",{staticClass:"p-fixed bottom-120 ptb-00-plr-32 w-p100"},[e("v-uni-view",{staticClass:"d-flex h-100 b-rad-100 o-hid",on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.jump.loginNavigateTo(n.routeTable.pBShoppingCart)}}},[e("v-uni-view",{staticClass:"flex-1 d-flex a-center ptb-00-plr-48 bg-333333"},[e("v-uni-view",{staticClass:"p-rela d-flex"},[e("v-uni-image",{staticClass:"wh-36",attrs:{src:n.ossIcon("/second_hair/s_car_white_36.png")}}),n.shoppingCartNum?e("v-uni-view",{staticClass:"p-abso top-n-16 right-n-14 w-26 h-26 bg-e80404 b-rad-p50 flex-c-c font-16 text-ffffff"},[n._v(n._s(n.shoppingCartNum))]):n._e()],1),e("v-uni-view",{staticClass:"ml-36 mr-30 w-02 h-44 bg-666666"}),e("v-uni-view",{staticClass:"text-ffffff font-36"},[e("v-uni-text",{staticClass:"font-26"},[n._v("¥")]),n._v(n._s(n.shoppingCartMoney))],1)],1),e("v-uni-view",{staticClass:"flex-c-c w-160 font-28 text-ffffff bg-e80404"},[n._v("去结算")])],1)],1):n._e()],1),e("v-uni-view",{},[e("u-popup",{attrs:{mode:"top",height:"710","border-radius":20,"z-index":979,"custom-style":{top:n.system.navigationBarHeight()+"px"}},model:{value:n.showClassifyPop,callback:function(t){n.showClassifyPop=t},expression:"showClassifyPop"}},[e("v-uni-view",{staticClass:"p-rela"},[e("v-uni-view",{staticClass:"p-stic top-0 bg-ffffff pt-32 pb-32 pl-40 font-32 text-3"},[n._v("全部分类")]),e("v-uni-view",{staticClass:"d-flex flex-wrap ml-10 mr-10 pb-104"},n._l(n.firstClassifyList,(function(t,i){return e("v-uni-view",{key:i,staticClass:"d-flex flex-column j-center a-center mb-40 ml-30 mr-28",on:{click:function(e){arguments[0]=e=n.$handleEvent(e),n.selectFirstClassifyId(t.id)}}},[e("vh-image",{attrs:{"loading-type":2,src:t.image,width:88,height:88,"border-radius":20}}),e("v-uni-view",{staticClass:"tran-2 mt-10 font-22 l-h-32",class:n.firstClassifyId==t.id?"text-ff9127":"text-3"},[n._v(n._s(t.second_name))])],1)})),1),e("v-uni-view",{staticClass:"p-fixed bottom-0 z-100 bg-ffffff w-p100 h-104 d-flex j-center a-center b-sh-00021200-022",on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.showClassifyPop=!1}}},[e("v-uni-text",{staticClass:"mr-12 font-28 text-6"},[n._v("点击收起")]),e("u-icon",{attrs:{name:"arrow-up-fill",size:10,color:"#666"}})],1)],1)],1),e("u-popup",{attrs:{mode:"bottom",duration:150,"border-radius":20},model:{value:n.showGoodsPackPop,callback:function(t){n.showGoodsPackPop=t},expression:"showGoodsPackPop"}},[e("v-uni-view",{staticClass:"pt-32 pr-24 pb-48 pl-24"},[e("v-uni-view",{staticClass:"d-flex"},[e("vh-image",{attrs:{"loading-type":2,src:n.secondHairGoodsInfo.product_img,width:152,height:152,"bg-color":"#F9F9F9","border-radius":6}}),e("v-uni-view",{staticClass:"d-flex flex-1 flex-column j-sb ml-16"},[e("v-uni-view",{staticClass:"font-28 text-3 l-h-40 o-hid text-hidden-2"},[n._v(n._s(n.secondHairGoodsInfo.title))]),e("v-uni-view",{staticClass:"d-flex a-center mt-12"},[n.secondHairGoodsInfo.is_hidden_price||[3,4].includes(n.secondHairGoodsInfo.onsale_status)?e("v-uni-text",{staticClass:"font-44 font-wei text-e80404"},[n._v("价格保密")]):e("v-uni-view",{staticClass:"d-flex a-start"},[e("v-uni-view",{staticClass:"h-60 font-44 font-wei text-e80404 l-h-60"},[e("v-uni-text",{staticClass:"font-24"},[n._v("¥"+n._s(" "))]),n._v(n._s(n.packageInfo.price))],1)],1)],1)],1)],1),e("v-uni-view",{staticClass:"mt-48 font-32 font-wei-500 text-3 l-h-44"},[n._v("规格")]),e("v-uni-view",{staticClass:"d-flex flex-wrap ml-n-24"},n._l(n.packageList,(function(t,i){return e("v-uni-view",{key:i},[e("v-uni-view",{staticClass:"bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3",class:n.packageIndex!=i?"":n.clickSelectPackage?"skew-top bg-fce4e3 b-s-01-e80404 text-e80404":"bg-fce4e3 b-s-01-e80404 text-e80404",on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.selectPackage(i)}}},[n._v(n._s(t.package_name))])],1)})),1),e("v-uni-view",{staticClass:"d-flex j-sb a-center mt-52"},[e("v-uni-view",{staticClass:"font-32 font-wei-500 text-3 l-h-44"},[n._v("数量")]),e("v-uni-view",{staticClass:"unumberbox"},[e("u-number-box",{attrs:{min:1,"input-width":64,"input-height":50,size:28},model:{value:n.purchaseNumbers,callback:function(t){n.purchaseNumbers=t},expression:"purchaseNumbers"}})],1)],1),e("v-uni-view",{staticClass:"mt-92 d-flex j-center a-center"},[e("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.addCar.apply(void 0,arguments)}}},[n._v("确定")])],1)],1)],1)],1)],1)],1)},a=[]},"9c51":function(n,t,e){var i=e("5572");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[n.i,i,""]]),i.locals&&(n.exports=i.locals);var s=e("4f06").default;s("8009469a",i,!0,{sourceMap:!1,shadowMode:!1})},"9e3f":function(n,t,e){"use strict";e("7a82");var i=e("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,e("e25e"),e("ac1f"),e("d81d"),e("14d9"),e("99af"),e("4de4"),e("d3b7"),e("159b");var s=i(e("d0ff")),a=i(e("d0af")),o=i(e("f07e")),c=i(e("c964")),l=i(e("f3f3")),r=i(e("fc11")),u=e("26cb"),f=i(e("6f67")),d={name:"second-hair-second",mixins:[f.default],data:function(){var n;return n={isGetChannelKeyword:!1,isGetMessageUnreadNum:!1,osip:"https://images.vinehoo.com/vinehoomini/v3",classifyContainerRestHeight:0,firstClassifyId:0,firstClassifyList:[],hairTitle:"",secondClassifyIndex:0,secondClassifyList:[],secondClassifyInfo:{},secondClassifySubIndex:-1,secondHairList:[],secondHairGoodsInfo:{},packageIndex:0,clickSelectPackage:0,packageInfo:{},showClassifyPop:!1,showGoodsPackPop:!1,packageList:[]},(0,r.default)(n,"packageInfo",{}),(0,r.default)(n,"limitInfo",{}),(0,r.default)(n,"purchaseNumbers",1),(0,r.default)(n,"page",1),(0,r.default)(n,"limit",10),(0,r.default)(n,"totalPage",1),(0,r.default)(n,"loadStatus","loadmore"),(0,r.default)(n,"shoppingCartMoney",0),(0,r.default)(n,"max",5),(0,r.default)(n,"count",5),(0,r.default)(n,"isExpand",!1),(0,r.default)(n,"cid",""),(0,r.default)(n,"type","card"),(0,r.default)(n,"scrollIntoView",""),(0,r.default)(n,"scrollDirection","down"),(0,r.default)(n,"lastScrollTop",0),(0,r.default)(n,"isUserScrolling",!1),(0,r.default)(n,"lastClickTime",0),(0,r.default)(n,"scrollTop",0),(0,r.default)(n,"scrollOffset",100),(0,r.default)(n,"sectionPositions",[]),(0,r.default)(n,"scrollThreshold",50),n},onLoad:function(n){n.cid&&(this.cid=parseInt(n.cid)),this.type=n.type,this.init()},onShow:function(){this.getShoppingCartMoney()},onReady:function(){this.getRestHeight()},computed:(0,l.default)({},(0,u.mapState)(["routeTable"])),mounted:function(){var n=this;this.$nextTick((function(){n.calculateSectionPositions()}))},watch:{secondHairList:{handler:function(){var n=this;this.$nextTick((function(){n.calculateSectionPositions()}))},deep:!0}},methods:{scrollToSection:function(n){console.log("Scrolling to section:",n),console.log("Section positions:",this.sectionPositions),this.sectionPositions&&this.sectionPositions.length>n&&void 0!==this.sectionPositions[n]?this.scrollTop=Math.max(0,this.sectionPositions[n]-this.scrollOffset):console.warn("Invalid section index or section positions not calculated yet")},handleScroll:function(n){var t=n.detail.scrollTop,e=Date.now();if(!(e-this.lastClickTime<500)){this.isUserScrolling=!0;var i=this.findActiveIndex(t);this.isUserScrolling&&i!==this.secondClassifyIndex&&(console.log("Updating active index to:",i),this.secondClassifyIndex=i)}},findActiveIndex:function(n){for(var t=n+this.scrollOffset,e=0,i=0;i<this.sectionPositions.length;i++){if(!(t>=this.sectionPositions[i]-this.scrollThreshold))break;e=i}return t+this.scrollThreshold>=this.sectionPositions[this.sectionPositions.length-1]&&(e=this.sectionPositions.length-1),e},getRestHeight:function(){var n=this;uni.getSystemInfo({success:function(t){uni.createSelectorQuery().in(n).select(".classify-con").boundingClientRect((function(e){n.classifyContainerRestHeight=t.windowHeight-e.top})).exec()}})},init:function(){var n=this;return(0,c.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,n.getSecondClassifyList();case 2:case"end":return t.stop()}}),t)})))()},getFirstClassifyList:function(){var n=this;return(0,c.default)((0,o.default)().mark((function t(){var e,i;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,n.$u.api.secondGoldAreaList();case 2:e=t.sent,n.firstClassifyList=e.data.list,n.firstClassifyList.length&&0==n.firstClassifyId&&(n.firstClassifyId=null===(i=n.firstClassifyList[0])||void 0===i?void 0:i.id);case 5:case"end":return t.stop()}}),t)})))()},selectFirstClassifyId:function(n){this.firstClassifyId!=n&&(this.firstClassifyId=n,this.getSecondClassifyList(),this.showClassifyPop=!1)},openCar:function(){this.login.isLogin(this.$vhFrom)&&this.jump.loginNavigateTo("".concat(this.routeTable.pBShoppingCart))},getSecondClassifyList:function(){var n=this;return(0,c.default)((0,o.default)().mark((function t(){var e;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e={},"card"!==n.type){t.next=8;break}return t.next=5,n.$u.api.filtergoodslist({cid:n.cid});case 5:e=t.sent,t.next=11;break;case 8:return t.next=10,n.$u.api.filtergoodslistColum({cid:n.cid});case 10:e=t.sent;case 11:n.secondClassifyList=e.data.list,n.secondClassifyList.map((function(t){var e={goods:t.goods,hairTitle:t.name};n.secondHairList.push(e)})),t.next=18;break;case 15:t.prev=15,t.t0=t["catch"](0),uni.showModal({title:"提示",content:"数据加载失败，是否重试？",success:function(t){var e=t.confirm;e&&n.getSecondClassifyList()}});case 18:case"end":return t.stop()}}),t,null,[[0,15]])})))()},selectSecondClassifyInfo:function(n){var t=this,e=this.secondClassifyList[n];this.secondClassifyInfo=e||this.secondClassifyList[0],this.secondClassifySubIndex=-1,this.scrollIntoView="section-".concat(n),this.$nextTick((function(){var e=uni.createSelectorQuery().in(t);e.select(".content").boundingClientRect(),e.select("#section-".concat(n)).boundingClientRect(),e.exec((function(e){var i=(0,a.default)(e,2),s=i[0],o=i[1];o&&s&&(t.secondClassifyIndex=n)}))}))},selectSecondClassifySubIndex:function(n){this.secondClassifySubIndex=n,this.page=1,this.totalPage=1,this.getSecondHairList()},getSecondHairList:function(){var n=this;return(0,c.default)((0,o.default)().mark((function t(){var e,i,a,c,l,r,u,f;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n.feedback.loading(),e={},e.second_id=n.firstClassifyId,e.page=n.page,e.limit=n.limit,n.secondClassifySubIndex>-1&&(i=n.secondClassifyInfo,a=i.key,c=i.list,e.key=a,e.value=c[n.secondClassifySubIndex]),t.next=8,n.$u.api.secondHairSonPageList(e);case 8:l=t.sent,r=l.data,u=r.total,f=r.list,1==n.page?n.secondHairList=f:n.secondHairList=[].concat((0,s.default)(n.secondHairList),(0,s.default)(f)),n.totalPage=Math.ceil(u/n.limit),n.loadStatus=n.page==n.totalPage?"nomore":"loadmore",n.feedback.hideLoading();case 14:case"end":return t.stop()}}),t)})))()},openPackPop:function(n){var t=this;return(0,c.default)((0,o.default)().mark((function e(){var i,s,a,c,l,r;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.login.isLogin(t.$vhFrom)){e.next=16;break}return t.feedback.loading(),t.packageIndex=0,t.secondHairGoodsInfo=n,i={},i.period=n.id,i.periods_type=n.periods_type,e.next=9,t.$u.api.packageDetail(i);case 9:s=e.sent,a=s.data,c=a.purchased,l=a.limit_number,r=a.packageList,t.packageList=r.filter((function(n){return 0==n.is_hidden})),t.limitInfo={aleradyBuy:c,limitNumber:l},t.packageInfo=r[t.packageIndex],t.showGoodsPackPop=!0,t.purchaseNumbers=1;case 16:case"end":return e.stop()}}),e)})))()},selectPackage:function(n){this.clickSelectPackage=1,this.packageIndex=n,this.packageInfo=this.packageList[this.packageIndex]},addCar:function(){var n=this;return(0,c.default)((0,o.default)().mark((function t(){var e,i,s;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0!=n.packageList.length){t.next=2;break}return t.abrupt("return",n.feedback.toast({title:"请选择套餐~"}));case 2:return t.prev=2,e=n.secondHairGoodsInfo,i=e.id,s=e.periods_type,t.next=6,n.$u.api.addShoppingCart({period:i,package_id:n.packageInfo.id,periods_type:s,nums:n.purchaseNumbers});case 6:t.sent,n.feedback.toast({title:"加入成功",icon:"success"}),n.showGoodsPackPop=!1,n.shoppingCartNum++,n.getShoppingCartMoney(),t.next=16;break;case 13:t.prev=13,t.t0=t["catch"](2),console.log(t.t0);case 16:case"end":return t.stop()}}),t,null,[[2,13]])})))()},scrollEnd:function(){console.log("---------到底啦")},getShoppingCartMoney:function(){var n=this;this.login.isLogin("",0)&&this.$u.api.shoppingCartMoneyCalclute().then((function(t){var e=(null===t||void 0===t?void 0:t.data)||{},i=e.total_money,s=void 0===i?0:i;n.shoppingCartMoney=s}))},onExpand:function(){this.isExpand=!this.isExpand,this.max=this.isExpand?this.secondClassifyInfo.list.length:this.$options.data().max},handleCategoryClick:function(n){var t=this;console.log("Category clicked:",n),this.isUserScrolling=!1,this.lastClickTime=Date.now(),this.secondClassifyIndex=n,this.$nextTick((function(){t.scrollToSection(n)}))},calculateSectionPositions:function(){var n=this;console.log("Calculating section positions");var t=uni.createSelectorQuery().in(this);this.secondHairList.forEach((function(n,e){t.select("#section-".concat(e)).boundingClientRect()})),t.exec((function(t){n.sectionPositions=t.filter((function(n){return n})).map((function(n){return n.top})),console.log("Section positions:",n.sectionPositions)}))}}};t.default=d},"9f26":function(n,t,e){"use strict";var i=e("3671"),s=e.n(i);s.a},a126:function(n,t,e){var i=e("bbdc");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[n.i,i,""]]),i.locals&&(n.exports=i.locals);var s=e("4f06").default;s("703d0287",i,!0,{sourceMap:!1,shadowMode:!1})},a58c:function(n,t,e){"use strict";e.r(t);var i=e("55c2"),s=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(a);t["default"]=s.a},bbdc:function(n,t,e){var i=e("24fb");t=i(!1),t.push([n.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),n.exports=t},c945:function(n,t,e){"use strict";var i=e("9c51"),s=e.n(i);s.a},f074:function(n,t,e){"use strict";e.r(t);var i=e("7f1a"),s=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(a);t["default"]=s.a},f2f9:function(n,t,e){"use strict";var i=e("a126"),s=e.n(i);s.a},f8c95:function(n,t,e){"use strict";e.r(t);var i=e("9176"),s=e("784b");for(var a in s)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return s[n]}))}(a);e("9f26"),e("c945");var o=e("f0c5"),c=Object(o["a"])(s["default"],i["b"],i["c"],!1,null,"772bf543",null,!1,i["a"],void 0);t["default"]=c.exports}}]);