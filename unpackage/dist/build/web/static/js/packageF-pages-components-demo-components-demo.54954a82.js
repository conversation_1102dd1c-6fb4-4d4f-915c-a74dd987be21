(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageF-pages-components-demo-components-demo"],{"11c2":function(e,t,n){"use strict";n.r(t);var o=n("94ed"),i=n("ce84");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);var s=n("f0c5"),r=Object(s["a"])(i["default"],o["b"],o["c"],!1,null,"7ac06213",null,!1,o["a"],void 0);t["default"]=r.exports},2071:function(e,t,n){"use strict";var o=n("35ca"),i=n.n(o);i.a},"35ca":function(e,t,n){var o=n("552d");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);var i=n("4f06").default;i("175b7730",o,!0,{sourceMap:!1,shadowMode:!1})},"39d1":function(e,t,n){"use strict";n("7a82");var o=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("b64b");var i=o(n("f07e")),a=o(n("c964")),s=o(n("f3f3")),r=n("1e48"),u=n("26cb"),c={name:"NewPeopleMixin",data:function(){return{newPeopleCouponActivityInfo:{},newPeopleGoodsList:[],newPeopleCouponPackage:{},isNewUser:0,showNewPeopleIndexMask:!1,receivedBenefits:!1,receiveBenefitsLoading:!1,getNPCAILoading:!1}},computed:(0,s.default)((0,s.default)({},(0,u.mapState)("newPeopleArea",["showNewPeopleFloatingFrame"])),{},{newPeopleAreaVisible:function(e){var t=e.isNewUser,n=e.newPeopleCouponActivityInfo,o=e.newPeopleCouponPackage;return!!(t&&Object.keys(n).length&&Object.keys(o).length)},newPeopleFloatingFrameVisible:function(e){var t=e.newPeopleAreaVisible,n=e.showNewPeopleFloatingFrame;return!(!t||!n)},newPeopleIndexMaskVisible:function(e){var t=e.newPeopleAreaVisible,n=e.newPeopleCouponPackage,o=e.receivedBenefits,i=e.showNewPeopleIndexMask,a=n.collect_status;return!(!t||!(0===a||1===a&&o))&&i}}),onShow:function(){this.getNewPeopleCouponActivityInfo()},methods:{getIsNewUser:function(){var e=this;return(0,a.default)((0,i.default)().mark((function t(){var n,o,a;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.login.isLogin(e.$vhFrom,0)){t.next=9;break}return t.next=3,e.$u.api.userSpecifiedData({field:"is_new_user"});case 3:n=t.sent,o=n.data.is_new_user,a=void 0===o?0:o,e.isNewUser=+a,t.next=10;break;case 9:e.isNewUser=1;case 10:case"end":return t.stop()}}),t)})))()},getNewPeopleCouponActivityInfo:function(){var e=this;return(0,a.default)((0,i.default)().mark((function t(){var n,o;return(0,i.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,!e.getNPCAILoading){t.next=3;break}return t.abrupt("return");case 3:return e.getNPCAILoading=!0,t.next=6,e.$u.api.newPeopleCouponActivityInfo({bection:1});case 6:n=t.sent,o=n.data,e.newPeopleCouponActivityInfo=o||{},e.newPeopleGoodsList=o.goods_list||[],e.newPeopleCouponPackage=o.coupon_package||{},uni.getStorage({key:"newPeopleIndexMaskCountDown",success:function(t){var n=t.data,o=e.$u.timeFormat(Date.now(),"yyyy-mm-dd");e.showNewPeopleIndexMask=!(n===o),console.log(n),console.log(o)},fail:function(){e.showNewPeopleIndexMask=!0}});case 12:return t.prev=12,e.getNPCAILoading=!1,e.receiveBenefitsLoading=!1,t.finish(12);case 16:case"end":return t.stop()}}),t,null,[[0,,12,16]])})))()},getNewPeopleReceiveBenefits:function(){var e=this;if(this.login.isLogin(this.$vhFrom)){if(this.getNPCAILoading||this.receiveBenefitsLoading)return;this.feedback.loading({title:"领取中"}),this.receiveBenefitsLoading=!0,this.$u.api.newPeopleReceiveBenefits().then((function(t){e.receivedBenefits=!0,e.getNewPeopleCouponActivityInfo()})).catch((function(){e.receiveBenefitsLoading=!1}))}},onJumpActivityPage:function(){this.jump.h5Jump((0,r.NEW_PEOPLE_ACTIVITY_URL)(),this.$vhFrom,4)},closeNewPeopleIndexMask:function(){uni.setStorageSync("newPeopleIndexMaskCountDown",this.$u.timeFormat(Date.now(),"yyyy-mm-dd")),this.showNewPeopleIndexMask=!1}}};t.default=c},"552d":function(e,t,n){var o=n("24fb");t=o(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-mask[data-v-f63a3092]{position:fixed;top:0;left:0;right:0;bottom:0;opacity:0;transition:-webkit-transform .3s;transition:transform .3s;transition:transform .3s,-webkit-transform .3s}.u-mask-show[data-v-f63a3092]{opacity:1}.u-mask-zoom[data-v-f63a3092]{-webkit-transform:scale(1.2);transform:scale(1.2)}',""]),e.exports=t},"56dc":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var o={uMask:n("e710").default},i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("u-mask",{attrs:{show:e.show,zoom:!1}},[n("v-uni-view",{staticClass:"h-p100 flex-c-c-c"},[n("v-uni-view",{staticClass:"p-rela w-540 h-380"},[n("v-uni-image",{staticClass:"p-abso top-0 left-0 w-p100 h-p100",attrs:{src:e.ossIcon("/order_confirm/de_tips_bg.png")}}),n("v-uni-view",{staticClass:"p-rela z-02 w-p100 h-p100 flex-sb-n-c"},[n("v-uni-view",{},[n("v-uni-view",{staticClass:"w-p100 h-150 flex-c-c mt-n-48"},[n("v-uni-image",{staticClass:"w-154 h-150",attrs:{src:e.ossIcon("/order_confirm/de_tips_cou.png")}})],1),n("v-uni-view",{staticClass:"flex-c-c mt-14"},[n("v-uni-view",{staticClass:"w-396 font-30 text-3 l-h-42"},[n("v-uni-view",{},[e._v("预交订金即可享受"),n("v-uni-text",{staticClass:"text-e80404"},[e._v("额外优惠")]),e._v("，")],1),n("v-uni-view",{},[e._v("若您改变主意，尾款未支付，")]),n("v-uni-view",{},[e._v("订金将在24小时内自动退还。")])],1)],1)],1),n("v-uni-view",{staticClass:"flex-c-c bt-s-01-eeeeee ptb-24-plr-00 font-30 font-wei text-e80404",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.$emit("close")}}},[e._v("我知道了")])],1)],1)],1)],1)},a=[]},5726:function(e,t,n){"use strict";n.r(t);var o=n("fa36"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},6608:function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={props:{show:{type:Boolean,default:!1}}};t.default=o},"94ed":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var o={OrderConfirmDepositPromptMask:n("ad01").default},i=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"mt-120"},[t("OrderConfirmDepositPromptMask",{attrs:{show:this.showMask}})],1)},a=[]},"97a7":function(e,t,n){"use strict";n("7a82");var o=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n("39d1")),a={mixins:[i.default],data:function(){return{secCouponPopupVisible:!0,showMask:!0}},methods:{}};t.default=a},a850:function(e,t,n){"use strict";n.r(t);var o=n("6608"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},ad01:function(e,t,n){"use strict";n.r(t);var o=n("56dc"),i=n("a850");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);var s=n("f0c5"),r=Object(s["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=r.exports},ce84:function(e,t,n){"use strict";n.r(t);var o=n("97a7"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=i.a},dc86:function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-mask",class:{"u-mask-zoom":e.zoom,"u-mask-show":e.show},style:[e.maskStyle,e.zoomStyle],attrs:{"hover-stop-propagation":!0},on:{touchmove:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t),function(){}.apply(void 0,arguments)},click:function(t){arguments[0]=t=e.$handleEvent(t),e.click.apply(void 0,arguments)}}},[e._t("default")],2)},i=[]},e710:function(e,t,n){"use strict";n.r(t);var o=n("dc86"),i=n("5726");for(var a in i)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(a);n("2071");var s=n("f0c5"),r=Object(s["a"])(i["default"],o["b"],o["c"],!1,null,"f63a3092",null,!1,o["a"],void 0);t["default"]=r.exports},fa36:function(e,t,n){"use strict";n("7a82");var o=n("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(n("f3f3"));n("a9e3"),n("b64b");var a={name:"u-mask",props:{show:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:""},customStyle:{type:Object,default:function(){return{}}},zoom:{type:Boolean,default:!0},duration:{type:[Number,String],default:300},maskClickAble:{type:Boolean,default:!0}},data:function(){return{zoomStyle:{transform:""},scale:"scale(1.2, 1.2)"}},watch:{show:function(e){e&&this.zoom?this.zoomStyle.transform="scale(1, 1)":!e&&this.zoom&&(this.zoomStyle.transform=this.scale)}},computed:{maskStyle:function(){var e={backgroundColor:"rgba(0, 0, 0, 0.6)"};return this.show?e.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.mask:e.zIndex=-1,e.transition="all ".concat(this.duration/1e3,"s ease-in-out"),Object.keys(this.customStyle).length&&(e=(0,i.default)((0,i.default)({},e),this.customStyle)),e}},methods:{click:function(){this.maskClickAble&&this.$emit("click")}}};t.default=a}}]);