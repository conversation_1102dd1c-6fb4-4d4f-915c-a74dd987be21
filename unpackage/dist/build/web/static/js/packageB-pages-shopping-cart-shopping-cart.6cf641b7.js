(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-shopping-cart-shopping-cart"],{"0481":function(t,e,i){"use strict";var n=i("23e7"),a=i("a2bf"),s=i("7b0b"),o=i("07fa"),l=i("5926"),c=i("65f0");n({target:"Array",proto:!0},{flat:function(){var t=arguments.length?arguments[0]:void 0,e=s(this),i=o(e),n=c(e,0);return n.length=a(n,e,e,i,0,void 0===t?1:l(t)),n}})},"0773":function(t,e,i){var n=i("2e2a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("6502c66a",n,!0,{sourceMap:!1,shadowMode:!1})},"0e01":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"vh-check",props:{width:{type:[String,Number],default:32},height:{type:[String,Number],default:32},checked:{type:Boolean,default:!1},checkedImg:{type:String,default:"https://images.vinehoo.com/vinehoomini/v3/comm/cir_sel.png"},unCheckedImg:{type:String,default:"https://images.vinehoo.com/vinehoomini/v3/comm/cir_unsel.png"},mode:{type:String,default:"aspectFill"},marginTop:{type:[String,Number],default:0}},computed:{checkStyle:function(){var t={};return t.marginTop=this.marginTop+"rpx",t.width=this.width+"rpx",t.height=this.height+"rpx",t}},methods:{click:function(){this.$emit("click")}}};e.default=n},"10eb":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},i("d9e2"),i("d401")},"12c6":function(t,e,i){"use strict";i.r(e);var n=i("51bd"),a=i("f074");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);i("f2f9");var o=i("f0c5"),l=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"519170ec",null,!1,n["a"],void 0);e["default"]=l.exports},"144f":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"d-flex j-center",style:[this.outerEmpConStyle]},[e("v-uni-view",{staticClass:"p-rela",style:[this.innEmpConStyle]},[e("v-uni-image",{staticClass:"w-p100 h-p100",attrs:{src:this.imageSrc,mode:"aspectFill"}}),e("v-uni-view",{staticClass:"p-abso w-p100 d-flex j-center font-28 text-9",style:[this.textStyle]},[this._v(this._s(this.text))])],1)],1)},a=[]},2036:function(t,e,i){"use strict";i.r(e);var n=i("d4f3"),a=i("28ff");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);var o=i("f0c5"),l=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"0c73edd7",null,!1,n["a"],void 0);e["default"]=l.exports},"28ff":function(t,e,i){"use strict";i.r(e);var n=i("0e01"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a},"2b27":function(t,e,i){var n=i("5823");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("0c12586a",n,!0,{sourceMap:!1,shadowMode:!1})},"2e2a":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,".list-con>.list-item[data-v-18e81f3f]:first-child{margin-top:0}[data-v-18e81f3f] .u-numberbox{border:1px solid #eee;border-radius:%?8?%}[data-v-18e81f3f] .u-icon-minus,[data-v-18e81f3f] .u-icon-plus{width:%?46?%!important;background-color:#fff!important}[data-v-18e81f3f] .uicon-minus,[data-v-18e81f3f] .uicon-plus{font-size:%?24?%!important;color:#666!important}",""]),t.exports=e},"33ee":function(t,e,i){"use strict";var n=i("2b27"),a=i.n(n);a.a},3819:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("0481"),i("4069"),i("d81d"),i("4de4"),i("d3b7"),i("99af"),i("13d5"),i("3ca3"),i("ddb0"),i("159b"),i("cb29"),i("c740"),i("14d9"),i("e9c4"),i("a434"),i("c975");var a=n(i("f07e")),s=n(i("c964")),o=n(i("d0ff")),l=n(i("f3f3")),c=i("26cb"),r={name:"shopping-cart",data:function(){return{loading:!0,isShowEdit:!0,activityList:[],activitySelectedList:[],normalList:[],normalSelectedList:[],invalidList:[],invalidSelectedList:[],bottomReductionAndFullGiftTipsList:[],settlementMoneyInfo:{}}},computed:(0,l.default)((0,l.default)((0,l.default)({},(0,c.mapState)(["routeTable"])),(0,c.mapState)("shoppingCart",["addShoppingCartStatus"])),{},{getBottomReductionAndFullGiftTipsList:function(){var t,e;return null!==(t=this.settlementMoneyInfo)&&void 0!==t&&null!==(e=t.full_discount)&&void 0!==e&&e.length?this.settlementMoneyInfo.full_discount:this.bottomReductionAndFullGiftTipsList.length?this.bottomReductionAndFullGiftTipsList:[]},getSubmitType:function(){var t=0;return this.activitySelectedList.length>0&&this.normalSelectedList.length>0?t=4:this.activitySelectedList.length>0&&0==this.normalSelectedList.length?t=0:this.normalSelectedList.length>0&&0==this.activitySelectedList.length&&(t=1),t},canSubmit:function(){return!!(this.activitySelectedList.flat().length||this.normalSelectedList.length||this.invalidSelectedList.length)},settlementQuantity:function(t){var e=t.activityList,i=t.normalList,n=e.map((function(t){return t.goods_info})).flat(),a=[].concat((0,o.default)(n),(0,o.default)(i)).filter((function(t){return t.checked})),s=a.reduce((function(t,e){return t+(e.nums||0)}),0);return s?"（".concat(s,"）"):""}}),onLoad:function(){this.system.setNavigationBarBlack(),this.init(),this.modifyAddShoppingCartStatus(!1)},onShow:function(){this.addShoppingCartStatus&&this.init(),this.modifyAddShoppingCartStatus(!1)},methods:(0,l.default)((0,l.default)((0,l.default)({},(0,c.mapMutations)(["muOrderInfo"])),(0,c.mapMutations)("shoppingCart",["modifyAddShoppingCartStatus"])),{},{init:function(){var t=this;return(0,s.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.settlementMoneyInfo={},e.next=3,Promise.all([t.getShoppingCartList()]);case 3:t.loading=!1;case 4:case"end":return e.stop()}}),e)})))()},getShoppingCartList:function(){var t=this;return(0,s.default)((0,a.default)().mark((function e(){var i,n,s,o,l,c,r,u,d;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.activitySelectedList=[],t.normalSelectedList=[],e.next=4,t.$u.api.shoppingCartList();case 4:i=e.sent,n=(null===i||void 0===i?void 0:i.data)||{},s=n.activity,o=void 0===s?[]:s,l=n.nomal,c=void 0===l?[]:l,r=n.invalid,u=void 0===r?[]:r,d=n.full_discount,o.forEach((function(t){return t.goods_info.forEach((function(t){return t.checked=!1}))})),c.forEach((function(t){return t.checked=!1})),u.forEach((function(t){return t.checked=!1})),t.activityList=o,t.normalList=c,t.invalidList=u,t.bottomReductionAndFullGiftTipsList=d,t.getActivityCount();case 14:case"end":return e.stop()}}),e)})))()},calculateSelectedTotalMoney:function(t,e){"normal"!=t&&this.activityList.length&&(this.activityList.map((function(t,e){t.reduction_tips="",t.fullgift_tips="",t.select_money=0,t.goods_info.map((function(e){e.checked&&e.is_support_reduction&&(t.select_money+=e.nums*e.price)}))})),this.calculateReduction(t,e),this.calculateFullGift(t,e))},calculateReduction:function(t,e){var i=this.activityList[e],n=i.select_money,a=(i.reduction_tips,i.reduction),s=a.length;if(s)if(void 0!==a[0].stacking_numb){var o=a[0],l=o.fill,c=o.decrease,r=o.stacking_numb;a[0].stacking_numb>0?(console.log("-----------------我是叠加次数有限满减"),this.activityList[e].reduction_tips=n>=r*l?"已减".concat((r*c).toFixed(2),"元"):n<l?"满".concat(l.toFixed(2),"减").concat(c.toFixed(2),"元"):"已减".concat((Math.floor(n/l)*c).toFixed(2),"元（还差").concat(((Math.floor(n/l)+1)*l-n).toFixed(2),"再减").concat(c.toFixed(2),"）")):(console.log("-----------------我是叠加次数无限满减"),this.activityList[e].reduction_tips="已减".concat((Math.floor(n/l)*c).toFixed(2),"元（还差").concat(((Math.floor(n/l)+1)*l-n).toFixed(2),"再减").concat(c.toFixed(2),"）"))}else if(n<a[0].fill){console.log("----------------------我比数组第一项都要小");var u=a[0],d=u.fill,f=u.decrease;this.activityList[e].reduction_tips="满".concat(d.toFixed(2),"减").concat(f.toFixed(2),"元")}else if(n>=a[s-1].fill){console.log("----------------------我比数组最后一项都要大");var h=a[s-1],v=(h.fill,h.decrease);this.activityList[e].reduction_tips="已减".concat(v.toFixed(2),"元")}else{var p=a.findIndex((function(t){return t.fill>n}));console.log("----------------------我比数组第一项要大比最后一项要小"),console.log(p);var g=a[p],m=g.fill,b=g.decrease,x=a[p-1],y=(x.fill,x.decrease);this.activityList[e].reduction_tips="已减".concat(y.toFixed(2),"元（还差").concat((m-n).toFixed(2),"再减").concat((b-y).toFixed(2),"）")}},calculateFullGift:function(t,e){console.log("--------------------我是计算满赠");var i=this.activityList[e],n=i.select_money,a=(i.fullgift_tips,i.fullgift),s=a.length;if(s){if(n<a[0].fill){console.log("--------------------选中的金额比满赠第一项都小");var o=a[0],l=o.fill,c=o.goods_name;this.activityList[e].fullgift_tips="满".concat(l.toFixed(2),"赠").concat(c)}else if(n>=a[s-1].fill){console.log("--------------------选中的金额比满赠最后一项都大");var r=a[a.length-1],u=r.fill,d=r.goods_name;this.activityList[e].fullgift_tips="满".concat(u.toFixed(2),"赠").concat(d)}else{console.log("--------------------选中的金额在第一项和最后一项之间");var f=a.findIndex((function(t){return t.fill>n}));console.log(f);var h=a[f],v=h.fill,p=h.goods_name;this.activityList[e].fullgift_tips="满".concat(v.toFixed(2),"赠").concat(p)}console.log("---------------------end")}},getActivityCount:function(){var t=this;this.activitySelectedList=[],this.activityList.forEach((function(){t.activitySelectedList.push([])}))},shoppingCartMoneyCalculate:function(){var t=this;return(0,s.default)((0,a.default)().mark((function e(){var i,n,s,l;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(i=[],t.activityList.forEach((function(t){t.goods_info.forEach((function(t){1==t.checked&&i.push(t)}))})),n=t.normalList.filter((function(t){return 1==t.checked})),s=[].concat(i,(0,o.default)(n)),console.log(s),!(s.length>0)){e.next=12;break}return e.next=8,t.$u.api.shoppingCartMoneyCalclute({items_info:JSON.stringify(s)});case 8:l=e.sent,t.settlementMoneyInfo=l.data,e.next=15;break;case 12:return e.next=14,{};case 14:t.settlementMoneyInfo=e.sent;case 15:case"end":return e.stop()}}),e)})))()},isSelectAllGoods:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;switch(t){case"all":var i=0,n=0;return this.activityList.forEach((function(t){t.goods_info.forEach((function(t){i++}))})),this.activitySelectedList.forEach((function(t){t.forEach((function(t){n++}))})),this.isShowEdit?i+this.normalList.length===n+this.normalSelectedList.length&&(n||this.normalSelectedList.length):i+this.normalList.length+this.invalidList.length===n+this.normalSelectedList.length+this.invalidSelectedList.length&&(n||this.normalSelectedList.length||this.invalidSelectedList.length);case"activity":return this.activityList[e].goods_info.length===this.activitySelectedList[e].length;case"normal":return this.normalList.length===this.normalSelectedList.length;case"invalid":return this.invalidList.length===this.invalidSelectedList.length}},selectAll:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this.normalList.length||this.activityList.length||!this.isShowEdit){switch(console.log("全选/取消全选"),console.log(t),console.log(i),t){case"all":this.isSelectAllGoods("all")?(this.activityList.forEach((function(t){t.goods_info.forEach((function(t){t.checked=!1}))})),this.normalList.forEach((function(t){t.checked=!1})),this.normalSelectedList=[],this.isShowEdit||(this.invalidList.forEach((function(t){t.checked=!1})),this.invalidSelectedList=[]),this.getActivityCount()):(console.log("------------我是商品全选"),this.getActivityCount(),this.activityList.map((function(t,i){t.goods_info.map((function(t){t.checked=!0,e.activitySelectedList[i].push(t.id)}))})),this.normalSelectedList=this.normalList.map((function(t){return t.checked=!0,t.id})),this.isShowEdit||(this.invalidSelectedList=this.invalidList.map((function(t){return t.checked=!0,t.id}))));break;case"activity":if(this.isSelectAllGoods("activity",i))console.log("---------------------我需要活动不全选"),this.activityList[i].goods_info.forEach((function(t){t.checked=!1})),this.activitySelectedList[i].splice(0,this.activitySelectedList[i].length);else{console.log("---------------------我需要活动全选");var n=this.activityList[i].goods_info.map((function(t){return t.checked=!0,t.id}));n.forEach((function(t){-1==e.activitySelectedList[i].indexOf(t)&&e.activitySelectedList[i].push(t)}))}break;case"normal":this.isSelectAllGoods("normal")?(this.normalList.forEach((function(t){t.checked=!1})),this.normalSelectedList=[]):this.normalSelectedList=this.normalList.map((function(t){return t.checked=!0,t.id}));break;case"invalid":this.isSelectAllGoods("invalid")?(this.invalidList.forEach((function(t){t.checked=!1})),this.invalidSelectedList=[]):this.invalidSelectedList=this.invalidList.map((function(t){return t.checked=!0,t.id}));break}this.calculateSelectedTotalMoney(t,i),this.shoppingCartMoneyCalculate()}},selectSingle:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;switch(e){case"activity":this.activitySelectedList[i].indexOf(t.id)>-1?(this.activityList[i].goods_info.forEach((function(e){e.id===t.id&&(e.checked=!1)})),this.activitySelectedList[i].splice(this.activitySelectedList[i].indexOf(t.id),1)):(this.activityList[i].goods_info.forEach((function(e){e.id===t.id&&(e.checked=!0)})),this.activitySelectedList[i].push(t.id));break;case"normal":this.normalSelectedList.indexOf(t.id)>-1?(this.normalList.forEach((function(e){e.id===t.id&&(e.checked=!1)})),this.normalSelectedList.splice(this.normalSelectedList.indexOf(t.id),1)):(this.normalList.forEach((function(e){e.id===t.id&&(e.checked=!0)})),this.normalSelectedList.push(t.id));case"invalid":this.invalidSelectedList.indexOf(t.id)>-1?(this.invalidList.forEach((function(e){e.id===t.id&&(e.checked=!1)})),this.invalidSelectedList.splice(this.invalidSelectedList.indexOf(t.id),1)):(this.invalidList.forEach((function(e){e.id===t.id&&(e.checked=!0)})),this.invalidSelectedList.push(t.id));break}this.calculateSelectedTotalMoney(e,i),this.shoppingCartMoneyCalculate()},changeCartNumbers:function(t,e,i){var n=arguments,o=this;return(0,s.default)((0,a.default)().mark((function s(){var l;return(0,a.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(l=n.length>3&&void 0!==n[3]?n[3]:0,!(t.value<=0)){a.next=3;break}return a.abrupt("return",o.feedback.toast({title:"商品数量必须大于0~"}));case 3:return a.prev=3,console.log(e,t.value,i,l),a.next=7,o.$u.api.changeShoppingCartNums({id:e,nums:t.value});case 7:a.t0=i,a.next="activity"===a.t0?10:"normal"===a.t0?13:15;break;case 10:return o.activityList[l].goods_info.forEach((function(i){i.id==e&&(i.nums=t.value)})),o.calculateSelectedTotalMoney(i,l),a.abrupt("break",15);case 13:return o.normalList.forEach((function(i){i.id==e&&(i.nums=t.value)})),a.abrupt("break",15);case 15:o.shoppingCartMoneyCalculate(),a.next=20;break;case 18:a.prev=18,a.t1=a["catch"](3);case 20:case"end":return a.stop()}}),s,null,[[3,18]])})))()},collectOrders:function(){switch(this.settlementMoneyInfo.gaps.type){case 2:this.$app?wineYunJsBridge.openAppPage({client_path:{ios_path:"goMain",android_path:"goMain"},ad_path_param:[{ios_key:"type",ios_val:"1",android_key:"type",android_val:"1"}]}):this.jump.reLaunch(this.routeTable.pgFlashPurchase);break;case 3:this.$app?wineYunJsBridge.openAppPage({client_path:{ios_path:"goMain",android_path:"goMain"},ad_path_param:[{ios_key:"type",ios_val:"3",android_key:"type",android_val:"3"}]}):this.jump.reLaunch(this.routeTable.pgMiaoFa);break;default:this.$app?wineYunJsBridge.openAppPage({client_path:{ios_path:"goMain",android_path:"goMain"}}):this.jump.reLaunch(this.$routeTable.pgIndex)}},settlement:function(){var t=[];this.activityList.forEach((function(e){e.goods_info.forEach((function(e){1==e.checked&&t.push(e)}))}));var e=this.normalList.filter((function(t){return 1==t.checked})),i=[].concat(t,(0,o.default)(e));if(console.log(i),i.length>0){var n=i.some((function(t){return 0==t.periods_type})),a=i.some((function(t){return 1==t.periods_type})),s=n&&!a?0:!n&&a?1:4,l={is_cart:1,submit_type:s,coupons_id:0,special_type:0,orderGoodsList:[]};i.forEach((function(t){var e={};e.periods_type=t.periods_type,e.is_cart=1,e.period=t.period,e.banner_img=t.banner_img,e.title=t.title,e.package_name=t.package_name,e.price=t.price,e.package_id=t.package_id,e.nums=t.nums,e.express_type=0,e.predict_time=t.predict_shipment_time,e.goods_is_ts=t.goods_is_ts,e.is_checked_ts=!1,e.is_ts=0,e.is_cold_chain=t.is_cold_chain,e.$is_checked_cold_chain=!1,e.is_original_package=1===t.is_original_package?t.is_original_package:2,console.log(e),l.orderGoodsList.push(e)})),this.muOrderInfo(l),uni.setStorageSync("nextorderGoodsInfo",l),this.jump.appAndMiniJump(0,this.routeTable.pBOrderConfirm,this.$vhFrom)}else this.feedback.toast({title:"请选择商品",icon:"error"})},deleteShoppingCartGoods:function(t){var e=this,i=[];if(i=1==t?this.invalidList.map((function(t){return t.id})):[].concat((0,o.default)(this.activitySelectedList),(0,o.default)(this.normalSelectedList),(0,o.default)(this.invalidSelectedList)),console.log(i),i.length>0)this.feedback.showModal({content:1===t?"是否清空失效商品":"删除之后数据无法恢复，确认删除吗？",confirm:function(){var t=(0,s.default)((0,a.default)().mark((function t(){return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$u.api.shoppingCatDelete({ids:i.join(",")});case 3:e.feedback.toast({title:"删除成功",icon:"success"}),e.getShoppingCartList(),e.settlementMoneyInfo={},t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),e.feedback.toast({title:"删除失败，请重试！"});case 11:case"end":return t.stop()}}),t,null,[[0,8]])})));return function(){return t.apply(this,arguments)}}()});else{var n="";n=1==t?"暂无失效商品":"请选择删除项",this.feedback.toast({title:n,icon:"error"})}},bottomGoAndCollectTheBill:function(t){switch(console.log(t),t.activity_range){case 2:this.$app?wineYunJsBridge.openAppPage({client_path:{ios_path:"goMain",android_path:"goMain"},ad_path_param:[{ios_key:"type",ios_val:"1",android_key:"type",android_val:"1"}]}):this.jump.reLaunch(this.routeTable.pgFlashPurchase);break;case 3:this.$app?wineYunJsBridge.openAppPage({client_path:{ios_path:"goMain",android_path:"goMain"},ad_path_param:[{ios_key:"type",ios_val:"3",android_key:"type",android_val:"3"}]}):this.jump.reLaunch(this.routeTable.pgMiaoFa);break;default:this.$app?wineYunJsBridge.openAppPage({client_path:{ios_path:"goMain",android_path:"goMain"}}):this.jump.reLaunch(this.$routeTable.pgIndex)}},handleGoodsJump:function(t){this.jump.appAndMiniJump(1,"".concat(this.routeTable.pgGoodsDetail,"?id=").concat(t),this.$vhFrom)},onSwitch:function(){this.isShowEdit=!this.isShowEdit,this.getActivityCount();var t=this.$options.data(),e=t.normalSelectedList,i=t.invalidSelectedList;this.normalCheckList=e,this.invalidSelectedList=i,this.activityList.forEach((function(t){t.goods_info.forEach((function(t){t.checked=!1}))})),this.normalList.forEach((function(t){t.checked=!1})),this.normalSelectedList=[],this.invalidList.forEach((function(t){t.checked=!1})),this.invalidSelectedList=[]}})};e.default=r},4053:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,n.default)(t)};var n=function(t){return t&&t.__esModule?t:{default:t}}(i("b680"))},4069:function(t,e,i){"use strict";var n=i("44d2");n("flat")},5047:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"vh-split-line",props:{paddingTop:{type:[String,Number],default:40},paddingBottom:{type:[String,Number],default:40},marginLeft:{type:[String,Number],default:40},marginRight:{type:[String,Number],default:40},text:{type:String,default:"已浏览"},fontSize:{type:[String,Number],default:28},fontBold:{type:Boolean,default:!1},textColor:{type:String,default:"#666666"},isTran:{type:Boolean,default:!1},lineWidth:{type:[String,Number],default:200},lineHeight:{type:[String,Number],default:10},lineColor:{type:String,default:"#E0E0E0"},showImage:{type:Boolean,default:!1},imageSrc:{type:String,default:""},imageWidth:{type:[String,Number],default:36},imageHeight:{type:[String,Number],default:38}},data:function(){return{}},computed:{upDownStyle:function(){return{paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},lineStyle:function(){var t={};return t.width=this.lineWidth+"rpx",t.height=this.lineHeight+"rpx",this.isTran&&(t.transform="scaleY(0.5)"),t.backgroundColor=this.lineColor,t},imageStyle:function(){return{width:this.imageWidth+"rpx",height:this.imageHeight+"rpx"}},textStyle:function(){var t={};return t.marginLeft=this.marginLeft+"rpx",t.marginRight=this.marginRight+"rpx",this.fontBold&&(t.fontWeight="bold"),t.fontSize=this.fontSize+"rpx",t.color=this.textColor,t}}};e.default=n},"51bd":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return n}));var n={uIcon:i("e5e1").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{},[i("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[i("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),i("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?i("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?i("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[i("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?i("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?i("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[i("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),i("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),i("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?i("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},s=[]},"55c2":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3");var n={name:"vh-empty",props:{bgColor:{type:String,default:"#FFF"},paddingTop:{type:[String,Number],default:0},paddingBottom:{type:[String,Number],default:0},width:{type:[String,Number],default:440},height:{type:[String,Number],default:360},imageSrc:{type:String,default:""},textBottom:{type:[String,Number],default:46},text:{type:String,default:""}},computed:{outerEmpConStyle:function(){return{backgroundColor:this.bgColor,paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},innEmpConStyle:function(){return{width:this.width+"rpx",height:this.height+"rpx"}},textStyle:function(){return{bottom:this.textBottom+"rpx"}}}};e.default=n},"55e7":function(t,e,i){"use strict";i.r(e);var n=i("5047"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a},5823:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,"uni-page-body[data-v-18e81f3f]{background-color:#f5f5f5}body.?%PAGE?%[data-v-18e81f3f]{background-color:#f5f5f5}",""]),t.exports=e},"5ba4":function(t,e,i){"use strict";i.r(e);var n=i("144f"),a=i("a58c");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);var o=i("f0c5"),l=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"55488dce",null,!1,n["a"],void 0);e["default"]=l.exports},6473:function(t,e,i){"use strict";i.r(e);var n=i("db26"),a=i("b69e");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);var o=i("f0c5"),l=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"ce683c6a",null,!1,n["a"],void 0);e["default"]=l.exports},"68a8":function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return n}));var n={vhImage:i("ce7c").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{style:[t.outerRecommendListConStyle]},[i("v-uni-view",{staticClass:"bg-ffffff p-24 b-rad-10",style:[t.innerRecommendListConStyle]},t._l(t.recommendList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"d-flex j-sb",class:0==n?"":"mt-24",on:{click:function(i){i.stopPropagation(),arguments[0]=i=t.$handleEvent(i),t.click(e)}}},[i("vh-image",{attrs:{"loading-type":2,src:e.banner_img[0],width:288,height:180,"border-radius":6}}),i("v-uni-view",{staticClass:"flex-1 d-flex flex-column j-sb ml-20"},[i("v-uni-view",{staticClass:"text-hidden-3"},[i("v-uni-text",{staticClass:"ml-06 font-24 text-3 l-h-36"},[t._v(t._s(e.title))])],1),i("v-uni-view",{staticClass:"mt-22 d-flex j-sb"},[1==e.is_hidden_price||[3,4].includes(e.onsale_status)?i("v-uni-text",{staticClass:"font-32 text-ff0013 l-h-28"},[t._v("价格保密")]):i("v-uni-text",{staticClass:"font-32 text-ff0013 l-h-28"},[i("v-uni-text",{staticClass:"font-20"},[t._v("¥")]),t._v(t._s(e.price))],1),i("v-uni-text",{staticClass:"font-22 text-9 l-h-34"},[t._v("已售"+t._s(e.purchased+e.vest_purchased)+"份")])],1)],1)],1)})),1)],1)},s=[]},"6d37":function(t,e,i){"use strict";i.r(e);var n=i("68a8"),a=i("8437");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);var o=i("f0c5"),l=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"35cb9d80",null,!1,n["a"],void 0);e["default"]=l.exports},"713d":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f07e")),s=n(i("c964"));i("a9e3"),i("caad6"),i("2532"),i("4ec9"),i("d3b7"),i("3ca3"),i("ddb0");var o=n(i("e75f")),l={name:"vh-channel-title-icon",props:{is_seckill:{type:[Number],default:0},channel:{type:[String,Number],default:0},marketingAttribute:{type:String,default:"0"},warehouseType:{type:[String,Number],default:"0"},showTitle:{type:Boolean,default:!1},borderRadius:{type:[String,Number],default:6},marginLeft:{type:[String,Number],default:0},padding:{type:String,default:"0 10rpx"},fontSize:{type:[String,Number],default:24},fontBold:{type:Boolean,default:!0},isNewYearTheme:{type:Boolean,default:!1},textColor:{type:String,default:"#FFFFFF"},plate:{type:String,default:""}},computed:{getChannel:function(){return this.marketingAttribute.includes("1")?101:9==this.channel?1==this.warehouseType?102:2==this.warehouseType?103:1:this.channel},iconName:function(){var t=!0;this.$android?t=(0,o.default)("9.1.8"):this.$ios&&(t=(0,o.default)("9.24"));var e=new Map([[0,{title:this.is_seckill?"秒杀":"闪购",iconText:this.is_seckill?"秒杀":"闪购",bgColor:this.is_seckill?"#FDE451":"#E80404",textColor:this.is_seckill?"#E80404":"#FFF"}],[1,{title:this.isNewYear?"年货节":"现货速发",iconText:this.isNewYear?"年货节":"现货速发",bgColor:"#FF9127",textColor:"#FFF"}],[2,{title:"跨境",iconText:"跨境",bgColor:"#734cd2",textColor:"#FFF"}],[3,{title:"尾货",iconText:"尾货",bgColor:"#FF9127",textColor:"#FFF"}],[4,{title:"兔头",iconText:"兔头",bgColor:"#FF9127",textColor:"#FFF"}],[11,{title:"拍卖",iconText:"拍卖",bgColor:"#F6B869",textColor:"#FFF"}],[9,{title:this.isNewYear?"年货节":"现货速发",iconText:this.isNewYear?"年货节":"现货速发",bgColor:"#FF9127",textColor:"#FFF"}],[101,{title:"拼团",iconText:"拼团",bgColor:"#FF9127",textColor:"#FFF"}],[102,{title:9===this.channel?this.isNewYear?"年货节":"现货速发":"闪购",iconText:"3小时达",bgColor:"#17E6A1",textColor:"#fff"}],[103,{title:9===this.channel?this.isNewYear?"年货节":"现货速发":"闪购",iconText:t?"次日达":"本地仓",bgColor:"#FAB005",textColor:"#fff"}]]);return e},iconStyle:function(){var t={};console.log("909988---",this.getChannel,this.iconName.get(this.getChannel));var e=this.iconName.get(this.getChannel),i=e.bgColor,n=e.textColor;return t.backgroundColor=i,t.borderRadius=this.borderRadius+"rpx",t.marginLeft=this.marginLeft+"rpx",t.padding=this.padding,t.fontSize=this.fontSize+"rpx",this.fontBold&&(t.fontWeight="bold"),t.color=n,1==this.warehouseType&&9==this.channel&&(t.color="#000",t.fontWeight="bold"),t}},mounted:function(){this.isNewYearTheme||this.secondConfig()},data:function(){return{isNewYear:!1}},methods:{secondConfig:function(){var t=this;return(0,s.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.secondConfig();case 2:i=e.sent,i.data.isopen&&(t.isNewYear=!0);case 4:case"end":return e.stop()}}),e)})))()}}};e.default=l},"7f1a":function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f3f3"));i("a9e3"),i("caad6"),i("2532");var s=i("26cb"),o=uni.getSystemInfoSync(),l={},c={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:l,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,a.default)((0,a.default)({},(0,s.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,i=e.pEAddressAdd,n=e.pEAddressManagement,a=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(i)||t.includes(n)||t.includes(a))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=c},8437:function(t,e,i){"use strict";i.r(e);var n=i("e997"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a},9629:function(t,e,i){"use strict";i.r(e);var n=i("3819"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a},a126:function(t,e,i){var n=i("bbdc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("4f06").default;a("703d0287",n,!0,{sourceMap:!1,shadowMode:!1})},a2bf:function(t,e,i){"use strict";var n=i("e8b5"),a=i("07fa"),s=i("3511"),o=i("0366"),l=function(t,e,i,c,r,u,d,f){var h,v,p=r,g=0,m=!!d&&o(d,f);while(g<c)g in i&&(h=m?m(i[g],g,e):i[g],u>0&&n(h)?(v=a(h),p=l(t,e,h,v,p,u-1)-1):(s(p+1),t[p]=h),p++),g++;return p};t.exports=l},a58c:function(t,e,i){"use strict";i.r(e);var n=i("55c2"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a},a8bc6:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"d-flex j-center a-center",style:[t.upDownStyle]},[t.showImage?[i("v-uni-image",{style:[t.imageStyle],attrs:{src:t.imageSrc,mode:"aspectFill"}}),i("v-uni-view",{style:[t.textStyle]},[t._v(t._s(t.text))])]:[i("v-uni-view",{style:[t.lineStyle]}),i("v-uni-view",{style:[t.textStyle]},[t._v(t._s(t.text))]),i("v-uni-view",{style:[t.lineStyle]})]],2)},a=[]},a9e0:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},i("a4d3"),i("e01a"),i("d3b7"),i("d28b"),i("3ca3"),i("ddb0"),i("a630")},aba6:function(t,e,i){"use strict";i.r(e);var n=i("d7c2"),a=i("9629");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);i("e41c"),i("33ee");var o=i("f0c5"),l=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"18e81f3f",null,!1,n["a"],void 0);e["default"]=l.exports},b69e:function(t,e,i){"use strict";i.r(e);var n=i("713d"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a},bbdc:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},cb29:function(t,e,i){"use strict";var n=i("23e7"),a=i("81d5"),s=i("44d2");n({target:"Array",proto:!0},{fill:a}),s("fill")},cbda:function(t,e,i){"use strict";i.r(e);var n=i("a8bc6"),a=i("55e7");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);var o=i("f0c5"),l=Object(o["a"])(a["default"],n["b"],n["c"],!1,null,"0b206e9a",null,!1,n["a"],void 0);e["default"]=l.exports},d0ff:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,n.default)(t)||(0,a.default)(t)||(0,s.default)(t)||(0,o.default)()};var n=l(i("4053")),a=l(i("a9e0")),s=l(i("dde1")),o=l(i("10eb"));function l(t){return t&&t.__esModule?t:{default:t}}},d4f3:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"d-flex"},[i("v-uni-image",{staticClass:"fade-in",style:[t.checkStyle],attrs:{src:t.checked?t.checkedImg:t.unCheckedImg,mode:t.mode},on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click()}}})],1)},a=[]},d7c2:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return n}));var n={vhNavbar:i("12c6").default,vhCheck:i("2036").default,uIcon:i("e5e1").default,vhImage:i("ce7c").default,vhChannelTitleIcon:i("6473").default,uNumberBox:i("3bd6").default,vhEmpty:i("5ba4").default,vhSplitLine:i("cbda").default,vhGoodsRecommendList:i("6d37").default,uButton:i("4f1b").default,vhSkeleton:i("591b").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"content"},[i("vh-navbar",{attrs:{title:"购物车"}},[t.loading?t._e():i("v-uni-view",{staticClass:"d-flex a-center ml-10 w-s-now font-30 text-3",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSwitch.apply(void 0,arguments)}}},[t._v(t._s(t.isShowEdit?"编辑":"完成"))])],1),t.loading?i("vh-skeleton",{attrs:{type:13,"loading-color":"#2E7BFF"}}):i("v-uni-view",{staticClass:"fade-in"},[i("v-uni-view",{},[t._l(t.activityList,(function(e,n){return t.activityList.length?i("v-uni-view",{key:n,staticClass:"pt-20"},[i("v-uni-view",{staticClass:"list-con bg-ffffff ml-24 mr-24 ptb-40-plr-24 b-rad-10"},[i("v-uni-view",{staticClass:"d-flex j-sb a-center mb-36"},[i("v-uni-view",{staticClass:"d-flex a-center"},[i("vh-check",{attrs:{checked:t.isSelectAllGoods("activity",n)},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectAll("activity",n)}}}),i("v-uni-text",{staticClass:"ml-24 font-32 font-wei text-3"},[t._v(t._s(e.activity_name))])],1)],1),e.reduction.length?i("v-uni-view",{staticClass:"d-flex j-sb a-center mb-36"},[i("v-uni-view",{staticClass:"ml-56 d-flex a-center"},[i("v-uni-text",{staticClass:"bg-fce4e3 b-rad-04 ptb-02-plr-12 font-24 text-e80404"},[t._v("满减")]),e.select_money?i("v-uni-view",{staticClass:"w-400 ml-12 font-26 text-3"},[t._v(t._s(e.reduction_tips.replaceAll(".00","")))]):i("v-uni-view",{staticClass:"w-400 ml-12 font-26 text-3"},[t._v("满"+t._s((""+e.reduction[0].fill.toFixed(2)).replace(".00",""))+"减"+t._s((""+e.reduction[0].decrease.toFixed(2)).replace(".00",""))+"元")])],1),i("v-uni-view",{staticClass:"d-flex a-center",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.jump.h5Jump(e.activity_url,"",1)}}},[i("v-uni-text",{staticClass:"mr-04 font-24 text-3"},[t._v("去凑单")]),i("u-icon",{attrs:{name:"arrow-right",size:20,color:"#333"}})],1)],1):t._e(),e.fullgift.length?i("v-uni-view",{staticClass:"d-flex j-sb a-center mb-36"},[i("v-uni-view",{staticClass:"ml-56 d-flex a-center"},[i("v-uni-text",{staticClass:"bg-fce4e3 b-rad-04 ptb-02-plr-12 font-24 text-e80404"},[t._v("满赠")]),e.select_money?i("v-uni-view",{staticClass:"w-400 ml-12 font-26 text-3"},[t._v(t._s(e.fullgift_tips.replaceAll(".00","")))]):i("v-uni-view",{staticClass:"w-400 ml-12 font-26 text-3"},[t._v("满"+t._s((""+e.fullgift[0].fill.toFixed(2)).replace(".00",""))+"赠"+t._s((""+e.fullgift[0].goods_name).replace(".00","")))])],1),i("v-uni-view",{staticClass:"d-flex a-center",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.jump.h5Jump(e.activity_url,"",1)}}},[i("v-uni-text",{staticClass:"mr-04 font-24 text-3"},[t._v("去凑单")]),i("u-icon",{attrs:{name:"arrow-right",size:20,color:"#333"}})],1)],1):t._e(),t._l(e.goods_info,(function(e,a){return i("v-uni-view",{key:a,staticClass:"list-item d-flex j-sb a-center mt-20"},[i("vh-check",{attrs:{checked:t.activitySelectedList[n].indexOf(e.id)>-1},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.selectSingle(e,"activity",n)}}}),i("v-uni-view",{staticClass:"p-rela w-230 h-144 b-rad-08 o-hid ml-24",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleGoodsJump(e.period)}}},[i("vh-image",{attrs:{"loading-type":2,src:e.banner_img,width:230,height:144}})],1),i("v-uni-view",{staticClass:"ml-16 flex-1",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleGoodsJump(e.period)}}},[i("v-uni-view",{staticClass:"d-flex a-center text-hidden-1 o-hid"},[i("vh-channel-title-icon",{attrs:{channel:e.periods_type,"border-radius":4,padding:"2rpx 6rpx","font-size":18}}),i("v-uni-text",{staticClass:"ml-04 font-24 text-0 l-h-34"},[t._v(t._s(e.title))])],1),i("v-uni-view",{staticClass:"mt-10"},[i("v-uni-text",{staticClass:"bg-eeeeee b-rad-06 mt-12 ptb-02-plr-12 font-20 text-9 l-h-28"},[t._v(t._s(e.package_name))])],1),i("v-uni-view",{staticClass:"mt-28 d-flex j-sb a-center"},[e.is_hidden_price||[3,4].includes(e.onsale_status)?i("v-uni-text",{staticClass:"font-28 font-wei text-e80404"},[t._v("价格保密")]):i("v-uni-text",{staticClass:"font-28 font-wei text-e80404"},[i("v-uni-text",{staticClass:"font-16"},[t._v("¥")]),t._v(t._s(e.price))],1),i("v-uni-view",{on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("u-number-box",{attrs:{value:e.available_stock>e.nums?e.nums:e.available_stock,"input-width":52,min:1,max:e.available_stock,"input-height":40,size:28},on:{change:function(i){arguments[0]=i=t.$handleEvent(i),t.changeCartNumbers(i,e.id,"activity",n)}}})],1)],1)],1)],1)}))],2)],1):t._e()})),t.normalList.length?i("v-uni-view",{staticClass:"pt-20"},[i("v-uni-view",{staticClass:"list-con bg-ffffff ml-24 mr-24 ptb-40-plr-24 b-rad-10"},[i("v-uni-view",{staticClass:"d-flex j-sb a-center mb-40"},[i("v-uni-view",{staticClass:"d-flex a-center"},[i("vh-check",{attrs:{checked:t.isSelectAllGoods("normal")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectAll("normal")}}}),i("v-uni-text",{staticClass:"ml-24 font-32 font-wei text-3"},[t._v("酒云商品")])],1)],1),t._l(t.normalList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"list-item d-flex j-sb a-center mt-20"},[i("vh-check",{attrs:{checked:t.normalSelectedList.indexOf(e.id)>-1},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.selectSingle(e,"normal")}}}),i("v-uni-view",{staticClass:"w-230 b-rad-08 o-hid ml-24",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleGoodsJump(e.period)}}},[i("vh-image",{attrs:{"loading-type":2,src:e.banner_img,height:144}})],1),i("v-uni-view",{staticClass:"ml-16 flex-1",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleGoodsJump(e.period)}}},[i("v-uni-view",{staticClass:"d-flex a-center text-hidden-1 o-hid"},[i("vh-channel-title-icon",{attrs:{channel:e.periods_type,"border-radius":4,padding:"2rpx 6rpx","font-size":18}}),i("v-uni-text",{staticClass:"ml-04 font-24 text-0 l-h-34"},[t._v(t._s(e.title))])],1),i("v-uni-view",{staticClass:"mt-10"},[i("v-uni-text",{staticClass:"bg-eeeeee b-rad-06 mt-12 ptb-02-plr-12 font-20 text-9 l-h-28"},[t._v(t._s(e.package_name))])],1),i("v-uni-view",{staticClass:"mt-28 d-flex j-sb a-center"},[e.is_hidden_price||[3,4].includes(e.onsale_status)?i("v-uni-text",{staticClass:"font-28 font-wei text-e80404"},[t._v("价格保密")]):i("v-uni-text",{staticClass:"font-28 font-wei text-e80404"},[i("v-uni-text",{staticClass:"font-16"},[t._v("¥")]),t._v(t._s(e.price))],1),i("v-uni-view",{on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e)}}},[i("u-number-box",{attrs:{value:e.available_stock>e.nums?e.nums:e.available_stock,min:1,max:e.available_stock,"input-width":52,"input-height":40,size:28},on:{change:function(i){arguments[0]=i=t.$handleEvent(i),t.changeCartNumbers(i,e.id,"normal")}}})],1)],1)],1)],1)}))],2)],1):t._e(),t.invalidList.length?i("v-uni-view",{staticClass:"pt-20"},[i("v-uni-view",{staticClass:"list-con bg-ffffff ml-24 mr-24 ptb-40-plr-24 b-rad-10"},[i("v-uni-view",{staticClass:"d-flex j-sb a-center mb-40"},[i("v-uni-view",{staticClass:"d-flex a-center"},[t.isShowEdit?i("v-uni-image",{staticClass:"w-32 h-32",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/shopping_cart/cir_dis.png",mode:"aspectFill"}}):i("vh-check",{attrs:{checked:t.isSelectAllGoods("invalid")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectAll("invalid")}}}),i("v-uni-text",{staticClass:"ml-24 font-32 font-wei text-3"},[t._v("失效商品")])],1)],1),t._l(t.invalidList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"list-item d-flex j-sb a-center mt-20"},[t.isShowEdit?i("v-uni-image",{staticClass:"w-32 h-32",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/shopping_cart/cir_dis.png",mode:"aspectFill"}}):i("vh-check",{attrs:{checked:t.invalidSelectedList.indexOf(e.id)>-1},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.selectSingle(e,"invalid")}}}),i("v-uni-view",{staticClass:"w-230 op-040 b-rad-08 o-hid ml-24"},[i("vh-image",{attrs:{"loading-type":2,src:e.banner_img,height:144}})],1),i("v-uni-view",{staticClass:"ml-16 flex-1"},[i("v-uni-view",{staticClass:"font-24 text-6 l-h-34 o-hid text-hidden-1"},[t._v(t._s(e.title))]),i("v-uni-view",{staticClass:"mt-70 d-flex j-end a-center"},[i("u-number-box",{attrs:{"input-width":52,"input-height":40,size:28,disabled:!0}})],1)],1)],1)}))],2)],1):t._e()],2),0===t.activityList.length&&0===t.normalList.length&&0===t.invalidList.length?i("vh-empty",{attrs:{"padding-top":40,"padding-bottom":100,"image-src":"https://images.vinehoo.com/vinehoomini/v3/empty/emp_cart.png",text:"您的购物车空空如也"}}):t._e(),i("v-uni-view",{},[i("vh-split-line",{attrs:{"padding-top":52,"padding-bottom":32,"margin-left":10,"margin-right":10,text:"猜你喜欢","font-bold":!0,"font-size":36,"text-color":"#333333","show-image":!0,"image-src":"https://images.vinehoo.com/vinehoomini/v3/comm/guess_love.png"}}),i("v-uni-view",{class:t.getBottomReductionAndFullGiftTipsList.length?"pb-"+(124+76*t.getBottomReductionAndFullGiftTipsList.length):"pb-124"},[i("vh-goods-recommend-list")],1)],1),i("v-uni-view",{},[i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isShowEdit,expression:"isShowEdit"}],staticClass:"fade-in p-fixed bottom-0 z-999 w-p100"},[i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.getBottomReductionAndFullGiftTipsList.length,expression:"getBottomReductionAndFullGiftTipsList.length"}]},t._l(t.getBottomReductionAndFullGiftTipsList,(function(e,n){return i("v-uni-view",{key:n,staticClass:"fade-in bg-fdfaea d-flex j-sb a-center ptb-18-plr-24"},[i("v-uni-view",{staticClass:"w-584 d-flex a-center"},[i("v-uni-view",{staticClass:"p-rela w-max-264 bg-f79101 d-flex j-center a-center b-rad-06 ptb-00-plr-12"},[i("v-uni-text",{staticClass:"font-22 font-wei text-ffffff text-hidden-2"},[t._v(t._s(e.name))]),i("v-uni-view",{staticClass:"p-abso right-n-12 w-20 h-20 b-rad-p50 bg-fdfaea"})],1),i("v-uni-view",{staticClass:"flex-1 ml-12 font-24 text-d87f19"},[i("v-uni-text",{staticClass:"text-hidden-2"},[t._v(t._s(e.information))])],1)],1),i("v-uni-view",{on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.bottomGoAndCollectTheBill(e)}}},[i("v-uni-text",{staticClass:"mr-08 font-24 font-wei text-d87f19"},[t._v("去凑单")]),i("u-icon",{attrs:{name:"arrow-right",size:20,color:"#D87F19"}})],1)],1)})),1),i("v-uni-view",{staticClass:"h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-28 pr-24"},[i("v-uni-view",{staticClass:"d-flex a-center"},[i("v-uni-view",{staticClass:"d-flex a-center"},[i("vh-check",{attrs:{checked:t.isSelectAllGoods("all")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectAll("all")}}}),i("v-uni-text",{staticClass:"ml-08 font-32 text-3"},[t._v("全选")])],1),i("v-uni-view",{staticClass:"ml-28"},[i("v-uni-view",{staticClass:"d-flex a-center"},[i("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("合计：")]),i("v-uni-text",{staticClass:"font-40 font-wei text-3"},[i("v-uni-text",{staticClass:"font-28"},[t._v("¥")]),t._v(t._s(t.settlementMoneyInfo.total_money?t.settlementMoneyInfo.total_money:0))],1)],1),i("v-uni-view",{staticClass:"d-flex a-center font-20 text-e80404 l-h-28"},[t._v("优惠减:¥"+t._s(t.settlementMoneyInfo.money_off_value?t.settlementMoneyInfo.money_off_value:0))])],1)],1),i("v-uni-view",{},[i("u-button",{attrs:{disabled:!t.canSubmit,shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:t.canSubmit?"#E80404":"#FCE4E3",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.settlement.apply(void 0,arguments)}}},[t._v("结算"+t._s(t.settlementQuantity))])],1)],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!t.isShowEdit,expression:"!isShowEdit"}],staticClass:"fade-in p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-32 pr-24"},[i("v-uni-view",{staticClass:"d-flex a-center"},[i("vh-check",{attrs:{checked:t.isSelectAllGoods("all")},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectAll("all")}}}),i("v-uni-text",{staticClass:"ml-16 font-32 text-3"},[t._v("全选")])],1),i("v-uni-view",{staticClass:"d-flex a-center"},[i("v-uni-view",{},[i("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"500",color:"#999",backgroundColor:"#FFF",border:"2rpx solid #999"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteShoppingCartGoods(1)}}},[t._v("清空失效商品")])],1),i("v-uni-view",{staticClass:"ml-24"},[i("u-button",{attrs:{disabled:!t.canSubmit,shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"500",color:"#FFF",backgroundColor:t.canSubmit?"#E80404":"#FCE4E3",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.deleteShoppingCartGoods(2)}}},[t._v("删除")])],1)],1)],1)],1)],1)],1)},s=[]},db26:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return""!==t.getChannel&&"orderConfirm"==t.plate?i("v-uni-view",{staticClass:"flex-s-c"},[t.showTitle?i("v-uni-view",{staticClass:"font-32 font-wei text-3 l-h-40"},[t._v(t._s(t.iconName.get(t.getChannel).title)+"商品")]):t._e(),i("v-uni-view",{staticClass:"mt-04",style:[t.iconStyle]},[t._v(t._s(t.iconName.get(t.getChannel).iconText))])],1):""!==t.getChannel?i("v-uni-text",{},[t.showTitle?i("v-uni-text",{staticClass:"font-32 font-wei text-3 l-h-44"},[t._v(t._s(t.iconName.get(t.getChannel).title)+"商品")]):t._e(),i("v-uni-text",{style:[t.iconStyle]},[t._v(t._s(t.iconName.get(t.getChannel).iconText))])],1):t._e()},a=[]},e41c:function(t,e,i){"use strict";var n=i("0773"),a=i.n(n);a.a},e75f:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d81d");var a=n(i("e143"));e.default=function(t){var e,i=(null===(e=a.default.prototype)||void 0===e?void 0:e.$vhVersion)||"",n=function(t){return t.split(".").map((function(t){return+t}))},s=n(i),o=s.length,l=n(t),c=l.length;if(console.log(s,l),o>c)return!0;if(o<c)return!1;var r=0;while(r<o){if(s[r]>l[r])return!0;if(s[r]<l[r])return!1;r++}return!1}},e997:function(t,e,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("f07e")),s=n(i("c964")),o=n(i("f3f3"));i("a9e3"),i("99af");var l=i("26cb"),c={name:"vh-goods-recommend-list",props:{from:{type:[String,Number],default:""},outerPaddingBottom:{type:[String,Number],default:24},innMarginLeft:{type:[String,Number],default:24},innMarginRight:{type:[String,Number],default:24},customClick:{type:Boolean,default:!1},jumpType:{type:Number,default:0},isInit:{type:Boolean,default:!0}},data:function(){return{recommendList:[]}},computed:(0,o.default)((0,o.default)({},(0,l.mapState)(["routeTable"])),{},{outerRecommendListConStyle:function(){return{paddingBottom:this.outerPaddingBottom+"rpx"}},innerRecommendListConStyle:function(){var t={};return t.marginLeft=this.innMarginLeft+"rpx",t.marginRight=this.innMarginRight+"rpx",t}}),created:function(){this.isInit&&this.getRecommendList()},methods:{getRecommendList:function(){var t=this;return(0,s.default)((0,a.default)().mark((function e(){var i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.recommendList();case 2:i=e.sent,t.recommendList=i.data;case 4:case"end":return e.stop()}}),e)})))()},click:function(t){this.customClick&&this.$emit("click",t),1===this.jumpType?this.jump.appAndMiniJump(0,"".concat(this.$routeTable.pgGoodsDetail,"?id=").concat(t.id),this.$vhFrom,1):this.jump.appAndMiniJump(0,"".concat(this.routeTable.pgGoodsDetail,"?id=").concat(t.id),this.$vhFrom,0)}}};e.default=c},f074:function(t,e,i){"use strict";i.r(e);var n=i("7f1a"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a},f2f9:function(t,e,i){"use strict";var n=i("a126"),a=i.n(n);a.a}}]);