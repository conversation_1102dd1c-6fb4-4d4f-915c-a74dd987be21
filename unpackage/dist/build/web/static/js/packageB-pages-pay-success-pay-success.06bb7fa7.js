(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-pay-success-pay-success"],{"00d9":function(t,n,e){var i=e("24fb");n=i(!1),n.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-model[data-v-acf792f8]{height:auto;overflow:hidden;font-size:%?32?%;background-color:#fff}.u-model__btn--hover[data-v-acf792f8]{background-color:#e6e6e6}.u-model__title[data-v-acf792f8]{padding-top:%?48?%;font-weight:500;text-align:center;color:#303133}.u-model__content__message[data-v-acf792f8]{padding:%?48?%;font-size:%?30?%;text-align:center;color:#606266}.u-model__footer[data-v-acf792f8]{display:flex;flex-direction:row}.u-model__footer__button[data-v-acf792f8]{flex:1;height:%?100?%;line-height:%?100?%;font-size:%?32?%;box-sizing:border-box;cursor:pointer;text-align:center;border-radius:%?4?%}',""]),t.exports=n},"0402":function(t,n,e){"use strict";e.r(n);var i=e("21e4"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},"062a":function(t,n,e){var i=e("aab3");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=e("4f06").default;a("2db15654",i,!0,{sourceMap:!1,shadowMode:!1})},"0d81":function(t,n,e){var i=e("24fb");n=i(!1),n.push([t.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 九宫格抽奖滚动板块定位(八块) */.luck-draw .luck-draw-item[data-v-6f31d510]:nth-child(1){left:0;top:0}.luck-draw .luck-draw-item[data-v-6f31d510]:nth-child(2){left:%?220?%;top:0}.luck-draw .luck-draw-item[data-v-6f31d510]:nth-child(3){right:0;top:0}.luck-draw .luck-draw-item[data-v-6f31d510]:nth-child(4){right:0;top:%?194?%}.luck-draw .luck-draw-item[data-v-6f31d510]:nth-child(5){right:0;bottom:0}.luck-draw .luck-draw-item[data-v-6f31d510]:nth-child(6){right:%?220?%;bottom:0}.luck-draw .luck-draw-item[data-v-6f31d510]:nth-child(7){left:0;bottom:0}.luck-draw .luck-draw-item[data-v-6f31d510]:nth-child(8){left:0;top:%?194?%}",""]),t.exports=n},1595:function(t,n,e){"use strict";e.r(n);var i=e("98e0"),a=e("6a18");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);var r=e("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);n["default"]=s.exports},"21e4":function(t,n,e){"use strict";e("7a82"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("a9e3");var i=uni.getSystemInfoSync(),a={},o={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"44"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:a,statusBarHeight:i.statusBarHeight}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():uni.navigateBack()}}};n.default=o},3644:function(t,n,e){"use strict";e.r(n);var i=e("80b9"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},"36f7":function(t,n,e){"use strict";e.r(n);var i=e("95b6"),a=e("0402");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("b10b");var r=e("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"2920cc37",null,!1,i["a"],void 0);n["default"]=s.exports},"430f":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return i}));var i={uPopup:e("c4b0").default,uLoading:e("301a").default},a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",[e("u-popup",{attrs:{zoom:t.zoom,mode:"center",popup:!1,"z-index":t.uZIndex,length:t.width,"mask-close-able":t.maskCloseAble,"border-radius":t.borderRadius,"negative-top":t.negativeTop},on:{close:function(n){arguments[0]=n=t.$handleEvent(n),t.popupClose.apply(void 0,arguments)}},model:{value:t.value,callback:function(n){t.value=n},expression:"value"}},[e("v-uni-view",{staticClass:"u-model"},[t.showTitle?e("v-uni-view",{staticClass:"u-model__title u-line-1",style:[t.titleStyle]},[t._v(t._s(t.title))]):t._e(),e("v-uni-view",{staticClass:"u-model__content"},[t.$slots.default||t.$slots.$default?e("v-uni-view",{style:[t.contentStyle]},[t._t("default")],2):e("v-uni-view",{staticClass:"u-model__content__message",style:[t.contentStyle]},[t._v(t._s(t.content))])],1),t.showCancelButton||t.showConfirmButton?e("v-uni-view",{staticClass:"u-model__footer u-border-top"},[t.showCancelButton?e("v-uni-view",{staticClass:"u-model__footer__button",style:[t.cancelBtnStyle],attrs:{"hover-stay-time":100,"hover-class":"u-model__btn--hover"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.cancel.apply(void 0,arguments)}}},[t._v(t._s(t.cancelText))]):t._e(),t.showConfirmButton||t.$slots["confirm-button"]?e("v-uni-view",{staticClass:"u-model__footer__button hairline-left",style:[t.confirmBtnStyle],attrs:{"hover-stay-time":100,"hover-class":t.asyncClose?"none":"u-model__btn--hover"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.confirm.apply(void 0,arguments)}}},[t.$slots["confirm-button"]?t._t("confirm-button"):[t.loading?e("u-loading",{attrs:{mode:"circle",color:t.confirmColor}}):[t._v(t._s(t.confirmText))]]],2):t._e()],1):t._e()],1)],1)],1)},o=[]},"4f1b":function(t,n,e){"use strict";e.r(n);var i=e("825d"),a=e("8e1d");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("fa94");var r=e("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"4ed92bb2",null,!1,i["a"],void 0);n["default"]=s.exports},5047:function(t,n,e){"use strict";e("7a82"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("a9e3");var i={name:"vh-split-line",props:{paddingTop:{type:[String,Number],default:40},paddingBottom:{type:[String,Number],default:40},marginLeft:{type:[String,Number],default:40},marginRight:{type:[String,Number],default:40},text:{type:String,default:"已浏览"},fontSize:{type:[String,Number],default:28},fontBold:{type:Boolean,default:!1},textColor:{type:String,default:"#666666"},isTran:{type:Boolean,default:!1},lineWidth:{type:[String,Number],default:200},lineHeight:{type:[String,Number],default:10},lineColor:{type:String,default:"#E0E0E0"},showImage:{type:Boolean,default:!1},imageSrc:{type:String,default:""},imageWidth:{type:[String,Number],default:36},imageHeight:{type:[String,Number],default:38}},data:function(){return{}},computed:{upDownStyle:function(){return{paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},lineStyle:function(){var t={};return t.width=this.lineWidth+"rpx",t.height=this.lineHeight+"rpx",this.isTran&&(t.transform="scaleY(0.5)"),t.backgroundColor=this.lineColor,t},imageStyle:function(){return{width:this.imageWidth+"rpx",height:this.imageHeight+"rpx"}},textStyle:function(){var t={};return t.marginLeft=this.marginLeft+"rpx",t.marginRight=this.marginRight+"rpx",this.fontBold&&(t.fontWeight="bold"),t.fontSize=this.fontSize+"rpx",t.color=this.textColor,t}}};n.default=i},"55e7":function(t,n,e){"use strict";e.r(n);var i=e("5047"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},5761:function(t,n,e){"use strict";e.r(n);var i=e("430f"),a=e("70e8");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("6f69");var r=e("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"acf792f8",null,!1,i["a"],void 0);n["default"]=s.exports},"59df":function(t,n,e){var i=e("24fb");n=i(!1),n.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-navbar[data-v-2920cc37]{width:100%}.u-navbar-fixed[data-v-2920cc37]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-2920cc37]{width:100%}.u-navbar-inner[data-v-2920cc37]{display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-2920cc37]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-2920cc37]{flex:1}.u-title[data-v-2920cc37]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center}',""]),t.exports=n},"5a50":function(t,n,e){"use strict";e("7a82"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i={props:{show:{type:Boolean,default:!1},info:{type:Object,default:function(){return{}}}},computed:{stageText:function(t){var n=t.info;switch(n.stage){case 2:return"成功解锁第二单";case 3:return"成功解锁第三单";default:return"新人首单礼"}}}};n.default=i},"5cb6":function(t,n,e){"use strict";var i=e("d3a7"),a=e.n(i);a.a},6113:function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return i}));var i={uNavbar:e("36f7").default,uButton:e("4f1b").default,vhSplitLine:e("cbda").default,vhGoodsRecommendList:e("6d37").default,uModal:e("5761").default,uMask:e("e710").default,TopThreeOrderMask:e("1595").default,vhSkeleton:e("591b").default},a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",{staticClass:"content",class:t.loading||t.showRuleMask?"h-vh-100 o-hid":""},[e("v-uni-view",{},[""==t.from?e("v-uni-view",{},[e("u-navbar",{attrs:{"is-back":!1,background:{background:t.navBackgroundColor}}},[e("v-uni-image",{staticClass:"ml-24 w-44 h-44",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/pay_success/back_arr.png",mode:"aspectFill"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.showLeaveModal(0)}}})],1)],1):e("v-uni-view",[e("v-uni-view",{staticClass:"p-fixed z-980 top-0 w-p100",style:{background:t.navBackgroundColor}},[e("v-uni-view",{style:{height:t.appStatusBarHeight+"px"}}),e("v-uni-view",{staticClass:"h-px-46 d-flex j-sb a-center"},[e("v-uni-image",{staticClass:"ml-24 w-44 h-44",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/pay_success/back_arr.png",mode:"aspectFill"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.showLeaveModal(0)}}})],1)],1)],1)],1),t.loading?e("v-uni-view",{staticClass:"fade-in",style:{paddingTop:t.from?t.appStatusBarHeight+"px":""}},[e("vh-skeleton",{attrs:{type:8}})],1):e("v-uni-view",{staticClass:"bg-f5f5f5"},[e("v-uni-view",{staticClass:"p-abso top-0 w-p100 h-820"},[e("v-uni-image",{staticClass:"w-p100 h-820",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/pay_success/banner.png",mode:"widthFix"}})],1),e("v-uni-view",{staticClass:"p-rela z-02 d-flex flex-column j-center a-center",style:{paddingTop:t.from?t.appStatusBarHeight+30+"px":"-20rpx"}},[e("v-uni-image",{staticClass:"w-264 h-184",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/pay_success/pay_succ.png",mode:"aspectFill"}}),e("v-uni-view",{staticClass:"mt-20 font-36 font-wei text-ffffff"},[t._v("支付成功")]),t.isColl?e("v-uni-view",{staticClass:"mt-10"},[3===t.isCollSucc?e("v-uni-view",{staticClass:"font-28 text-ffffff"},[t._v("拼团失败，钱款将按照原支付账户退回。")]):2===t.isCollSucc?e("v-uni-view",{staticClass:"font-28 text-ffffff"},[t._v("恭喜您！已拼团成功啦！")]):1===t.isCollSucc?e("v-uni-view",{staticClass:"d-flex flex-column j-center a-center"},[e("v-uni-view",{staticClass:"font-28 text-ffffff"},[e("v-uni-text",[t._v("还差")]),e("v-uni-text",{staticClass:"text-ffc900"},[t._v(t._s(t.CollNumber)+"人")]),e("v-uni-text",[t._v("，赶快邀请好友来拼团吧")])],1),e("v-uni-view",{staticClass:"mt-14 font-24 text-ffbbbb"},[t._v("拼团规则：好友拼团·人满发货·人不满退款")])],1):t._e()],1):t._e()],1),e("v-uni-view",{staticClass:"p-rela z-02 ml-72 mr-72",class:t.isColl?"mt-58":"mt-106"},[t.isColl&&1===t.isCollSucc?e("v-uni-view",{staticClass:"d-flex j-sb a-center"},[e("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"278rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#D60D0D",border:"2rpx solid #FFF"}},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.showLeaveModal(1)}}},[t._v("查看拼团详情")]),e("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"278rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#D60D0D",backgroundColor:"#FFF",border:"none"}},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.inviteGroup.apply(void 0,arguments)}}},[t._v("邀请好友拼团")])],1):e("v-uni-view",{staticClass:"d-flex j-sb a-center"},[e("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"278rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#D60D0D",border:"2rpx solid #FFF"}},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.showLeaveModal(1)}}},[t._v("查看我的订单")]),e("v-uni-view",{staticClass:"w-278 h-64 bg-ffffff d-flex j-center a-center b-rad-32 font-28 font-wei text-e80404",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.showLeaveModal(2)}}},[t._v("返回首页")])],1)],1),e("v-uni-view",{staticClass:"p-rela z-02 bg-ffffff b-rad-20 mr-24 ml-24 mt-60 p-32"},[e("v-uni-view",{staticClass:"d-flex j-center a-center font-32 font-wei text-8e591f"},[t._v("下单抽抽乐")]),e("v-uni-view",{staticClass:"p-abso top-38 right-32 font-24 text-9",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.showRuleMask=!0}}},[t._v("规则")]),e("v-uni-view",{staticClass:"luck-draw p-rela h-560 d-flex j-center a-center flex-wrap mt-32"},[t._l(t.luckyDrawList,(function(n,i){return e("v-uni-view",{key:i,staticClass:"luck-draw-item p-abso w-198 h-172 bg-fde8c7 d-flex flex-column j-center a-center b-rad-10",class:t.canLuckDraw?t.currentIndex==i+1&&t.luckyDrawing?"bg-ffd761":"bg-fde8c7":"bg-f4f4f4"},[e("v-uni-image",{staticClass:"w-60 h-60",class:t.canLuckDraw?"":"fil-gray100-opa40",attrs:{src:n.image,mode:"aspectFill"}}),e("v-uni-text",{staticClass:"mt-12 font-22 font-wei",class:t.canLuckDraw?"text-c87924":"text-9"},[t._v(t._s(n.name))]),n.sub_name?e("v-uni-text",{staticClass:"font-22 font-wei",class:t.canLuckDraw?"text-c87924":"text-9"},[t._v(t._s(n.sub_name))]):t._e()],1)})),e("v-uni-view",{staticClass:"p-rela w-198 h-172",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.$u.throttle(t.luckDraw,3e3)}}},[e("v-uni-image",{staticClass:"w-198 h-172",attrs:{src:t.ossIcon("/pay_success/click_"+t.luckDrawImgSuffArr[t.canLuckDraw]+".png"),mode:"aspectFill"}}),e("v-uni-view",{staticClass:"p-abso right-n-52 top-n-44 w-156 h-80 flex-c-c pb-10 font-24 text-e00701",style:{backgroundImage:"url("+t.ossIcon("/pay_success/draw_count.png")+")",backgroundSize:"cover"}},[t._v("剩余"+t._s(t.drawCount)+"次")])],1)],2),Object.keys(t.prizeInfo).length?e("v-uni-view",{},[0==t.prizeInfo.type?e("v-uni-view",{staticClass:"h-92 bg-feefd7 d-flex j-center a-center b-rad-10 mt-36 ptb-00-plr-24 font-28 text-c87924"},[t._v(t._s(t.prizeInfo.msg))]):e("v-uni-view",{staticClass:"h-92 bg-feefd7 d-flex j-sb a-center b-rad-10 mt-36 ptb-00-plr-24"},[e("v-uni-view",{staticClass:"font-28 text-c87924"},[t._v(t._s(t.prizeInfo.msg))]),e("v-uni-view",{staticClass:"flex-1 d-flex j-end"},[e("v-uni-view",{},[e("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"146rpx",height:"44rpx",fontSize:"28rpx",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.toExchange.apply(void 0,arguments)}}},[t._v(t._s(1==t.prizeInfo.type?"去兑换":"去使用"))])],1)],1)],1)],1):t._e()],1),e("v-uni-view",{},[e("vh-split-line",{attrs:{"padding-top":52,"padding-bottom":30,"margin-left":10,"margin-right":10,text:"猜你喜欢","font-bold":!0,"font-size":36,"text-color":"#333333","show-image":!0,"image-src":"https://images.vinehoo.com/vinehoomini/v3/comm/guess_love.png"}}),e("vh-goods-recommend-list",{attrs:{"custom-click":!0},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.openGoodsDetail(n,5)}}})],1),e("v-uni-view",{},[e("u-modal",{attrs:{"show-title":!1,content:"",width:490,"confirm-text":"知道了","confirm-style":{fontSize:"28rpx",color:"#999"}},on:{confirm:function(n){arguments[0]=n=t.$handleEvent(n),t.iKnow.apply(void 0,arguments)}},model:{value:t.showDrawSuccMod,callback:function(n){t.showDrawSuccMod=n},expression:"showDrawSuccMod"}},[e("v-uni-view",{staticClass:"pt-86 pb-64"},[e("v-uni-view",{staticClass:"d-flex j-center a-center"},[e("v-uni-image",{staticClass:"w-264 h-184",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/succ_red.png",mode:"aspectFill"}})],1),e("v-uni-view",{staticClass:"d-flex flex-column j-center a-center mt-30 l-h-44"},[e("v-uni-view",{staticClass:"font-28 text-3"},[t._v("中奖提示")]),e("v-uni-view",{staticClass:"pl-24 pr-24 text-center font-28 text-3"},[t._v(t._s(t.prizeContent))])],1)],1)],1),e("u-mask",{attrs:{show:t.showRuleMask,zoom:!1}},[e("v-uni-view",{staticClass:"h-p100 d-flex j-center a-center"},[e("v-uni-view",{staticClass:"p-rela w-582 h-988 bg-ffffff d-flex flex-column a-center b-rad-10"},[e("v-uni-view",{staticClass:"d-flex j-center mt-40 font-32 font-wei text-3"},[t._v("规则")]),e("v-uni-view",{staticClass:"w-502 h-730 mt-24"},[e("v-uni-scroll-view",{staticClass:"h-p100",attrs:{"scroll-y":"true"}},[e("v-uni-view",{staticClass:"font-30 font-wei text-3"},[t._v("抽奖规则：")]),e("v-uni-view",{staticClass:"font-28 mt-20 text-6 l-h-50"},[t._v("1.用户通过手机酒云网APP(APP版本需在8.30及以上)或酒云网微信小程序，购买符合要求的普通实物商品（不含酒会、课程、兔头商店商品等），下单并支付成功后将会获得一次抽取机会，未抽奖即离开该页面视为放弃，后续不可补抽；")]),e("v-uni-view",{staticClass:"font-28 text-6 l-h-50"},[t._v("2.任何参与者通过不正当手段（包括但不限于侵犯其他人合法权益、作弊、干扰系统、实施网络攻击、批量注册账户、用机器注册账户等方式）获得本活动奖品，酒云网有权撤销奖品；")]),e("v-uni-view",{staticClass:"font-28 text-6 l-h-50"},[t._v("3.若用户曾经存在，出现或经酒云网合理怀疑或判定的不当行为，用户将可能面临无法使用优惠权益；")]),e("v-uni-view",{staticClass:"font-28 text-6 l-h-50"},[t._v("4.如遇不可抗力（包括但不限于活动存在大面积作弊行为、活动遭受严重网络攻击或因系统故障导致活动资格大批量出错，活动不能正常进行时），酒云网可取消、修改或暂停本活动。")]),e("v-uni-view",{staticClass:"mt-24 font-30 font-wei text-3"},[t._v("奖品使用规则：")]),e("v-uni-view",{staticClass:"font-28 mt-20 text-6 l-h-50"},[t._v("1.优惠券可在“我的”优惠券中查看，具体使用规则和有效时间详见券面信息；")]),e("v-uni-view",{staticClass:"font-28 text-6 l-h-50"},[t._v("2.兔头在“我的”-兔头商店-兔头记录中查看，可在兔头商店中进行使用。")])],1)],1),e("v-uni-view",{staticClass:"p-abso bottom-0 w-p100 h-104 d-flex j-center a-center b-sh-00021200-022"},[e("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"440rpx",height:"64rpx",fontSize:"28rpx",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.showRuleMask=!1}}},[t._v("我知道了")])],1)],1)],1)],1),e("u-modal",{attrs:{width:504,"show-title":!1,"show-cancel-button":!0,"cancel-text":"残忍离开","confirm-text":"继续抽奖","cancel-style":{fontSize:"28rpx",color:"#999"},"confirm-style":{fontSize:"28rpx",color:"#E80404"},"content-style":{fontSize:"28rpx",fontWeight:"bold",color:"#333"}},on:{cancel:function(n){arguments[0]=n=t.$handleEvent(n),t.cruelLeave.apply(void 0,arguments)},confirm:function(n){arguments[0]=n=t.$handleEvent(n),t.continueToDraw.apply(void 0,arguments)}},model:{value:t.showLeaveMod,callback:function(n){t.showLeaveMod=n},expression:"showLeaveMod"}},[e("v-uni-view",{staticClass:"ptb-60-plr-00"},[e("v-uni-view",{staticClass:"text-center"},[t._v("离开将会失去本次抽奖机会哦~")]),e("v-uni-view",{staticClass:"text-center"},[t._v("确认离开吗？")])],1)],1),t.topThreeOrderInfo.is_show?e("TopThreeOrderMask",{attrs:{show:t.topThreeOrderMaskVisible,info:t.topThreeOrderInfo},on:{jump:function(n){arguments[0]=n=t.$handleEvent(n),t.topThreeJump.apply(void 0,arguments)},close:function(n){arguments[0]=n=t.$handleEvent(n),t.topThreeOrderMaskVisible=!1}}}):t._e()],1)],1)],1)},o=[]},"68a8":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return i}));var i={vhImage:e("ce7c").default},a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",{style:[t.outerRecommendListConStyle]},[e("v-uni-view",{staticClass:"bg-ffffff p-24 b-rad-10",style:[t.innerRecommendListConStyle]},t._l(t.recommendList,(function(n,i){return e("v-uni-view",{key:i,staticClass:"d-flex j-sb",class:0==i?"":"mt-24",on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(n)}}},[e("vh-image",{attrs:{"loading-type":2,src:n.banner_img[0],width:288,height:180,"border-radius":6}}),e("v-uni-view",{staticClass:"flex-1 d-flex flex-column j-sb ml-20"},[e("v-uni-view",{staticClass:"text-hidden-3"},[e("v-uni-text",{staticClass:"ml-06 font-24 text-3 l-h-36"},[t._v(t._s(n.title))])],1),e("v-uni-view",{staticClass:"mt-22 d-flex j-sb"},[1==n.is_hidden_price||[3,4].includes(n.onsale_status)?e("v-uni-text",{staticClass:"font-32 text-ff0013 l-h-28"},[t._v("价格保密")]):e("v-uni-text",{staticClass:"font-32 text-ff0013 l-h-28"},[e("v-uni-text",{staticClass:"font-20"},[t._v("¥")]),t._v(t._s(n.price))],1),e("v-uni-text",{staticClass:"font-22 text-9 l-h-34"},[t._v("已售"+t._s(n.purchased+n.vest_purchased)+"份")])],1)],1)],1)})),1)],1)},o=[]},"6a18":function(t,n,e){"use strict";e.r(n);var i=e("5a50"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},"6bdb":function(t,n,e){var i=e("59df");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=e("4f06").default;a("3dc5b1f9",i,!0,{sourceMap:!1,shadowMode:!1})},"6d37":function(t,n,e){"use strict";e.r(n);var i=e("68a8"),a=e("8437");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);var r=e("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"35cb9d80",null,!1,i["a"],void 0);n["default"]=s.exports},"6f69":function(t,n,e){"use strict";var i=e("9945"),a=e.n(i);a.a},"70e8":function(t,n,e){"use strict";e.r(n);var i=e("aa64"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},"80b9":function(t,n,e){"use strict";e("7a82");var i=e("ee27").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("e25e"),e("a9e3"),e("d3b7"),e("3ca3"),e("ddb0"),e("d81d"),e("caad6"),e("2532"),e("99af"),e("159b");var a=i(e("f07e")),o=i(e("c964")),r=i(e("f3f3")),s=e("26cb"),c={name:"pay-success",data:function(){return{share_image:"",main_title:"",CollPeriod:0,group_id:0,from:"",plate:"",loading:!0,appStatusBarHeight:"",navBackgroundColor:"rgba(224, 20, 31, 0)",scrollTop:0,isColl:!1,isCollSucc:1,eventType:0,luckyDrawList:[],canLuckDraw:1,luckDrawImgSuffArr:["gra","ora"],currentIndex:1,priceIndex:0,count:8,timer:0,speed:200,times:0,cycle:60,prize:-1,luckyDrawing:!1,prizeContent:"",originPrizeInfo:{},prizeInfo:{},goodsInfo:{},drawCount:0,showDrawSuccMod:!1,showRuleMask:!1,showLeaveMod:!1,topThreeOrderMaskVisible:!0,topThreeOrderInfo:{}}},computed:(0,r.default)({},(0,s.mapState)(["routeTable"])),onLoad:function(t){console.log("options.from",t.from),this.from=t.from||"",this.plate=t.plate||"",this.appStatusBarHeight=parseInt(t.statusBarHeight),this.searchIsColl(),this.initOnLoad()},methods:{searchIsColl:function(){var t=this;return(0,o.default)((0,a.default)().mark((function n(){var e;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,t.$u.api.lastOrderDetail({});case 2:e=n.sent,1===e.data.special_type&&(t.isColl=!0,t.CollNumber=e.data.group_last_num,t.group_id=e.data.group_id,t.CollPeriod=e.data.period,t.main_title=e.data.main_title,t.share_image=e.data.share_image,Number(t.CollNumber)?t.isCollSucc=e.data.group_status:t.isCollSucc=2);case 4:case"end":return n.stop()}}),n)})))()},initOnLoad:function(){var t=this;return(0,o.default)((0,a.default)().mark((function n(){var e;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,t.login.isLoginV3(t.$vhFrom);case 3:if(e=n.sent,!e){n.next=8;break}return n.next=7,Promise.all([t.getLuckyDrawList(),t.getLuckyDrawCount(),t.getTopThreeOrderNums()]);case 7:t.loading=!1;case 8:n.next=13;break;case 10:n.prev=10,n.t0=n["catch"](0),setTimeout((function(){t.comes.isFromApp(t.from)?"next"==t.from?t.jump.appAndMiniJump(0,t.routeTable.pEMyOrder,t.$vhFrom,1):wineYunJsBridge.openAppPage({client_path:{ios_path:"MyOrderViewController",android_path:"com.stg.rouge.activity.MyOrderActivity"}}):t.jump.redirectTo(t.routeTable.pEMyOrder)}),1500);case 13:case"end":return n.stop()}}),n,null,[[0,10]])})))()},getLuckyDrawList:function(){var t=this;return(0,o.default)((0,a.default)().mark((function n(){var e;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,t.$u.api.payLuckyDrawList();case 2:e=n.sent,e.data.list.map((function(t,n){t.name.includes("指定酒款")&&(t.sub_name=t.name.split("指定酒款")[1],t.name="指定酒款"),t.name.includes("满300减15")&&(t.sub_name=t.name.split("满300减15")[1],t.name="满300减15")})),t.luckyDrawList=e.data.list;case 5:case"end":return n.stop()}}),n)})))()},getLuckyDrawCount:function(){var t=this;return(0,o.default)((0,a.default)().mark((function n(){var e,i;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,t.$u.api.payLuckyDrawCount();case 3:e=n.sent,i=e.data,t.drawCount=i,t.drawCount>0?t.canLuckDraw=1:t.canLuckDraw=0,t.appGoBack(),n.next=12;break;case 10:n.prev=10,n.t0=n["catch"](0);case 12:case"end":return n.stop()}}),n,null,[[0,10]])})))()},getTopThreeOrderNums:function(){var t=this;return(0,o.default)((0,a.default)().mark((function n(){var e;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("orderDetail"!==t.plate){n.next=2;break}return n.abrupt("return");case 2:return n.next=4,t.$u.api.topThreeOrderNums();case 4:e=n.sent,t.topThreeOrderInfo=(null===e||void 0===e?void 0:e.data)||{};case 6:case"end":return n.stop()}}),n)})))()},appGoBack:function(){var t=this;if(this.comes.isFromApp(this.from)){if(0!=this.times)return this.feedback.toast({title:"正在抽奖中，请稍后"});window.interceptBack=function(){t.eventType=1,t.canLuckDraw?t.showLeaveMod=!0:t.cruelLeave()}}},showLeaveModal:function(t){if(0!=this.times)return this.feedback.toast({title:"正在抽奖中，请稍后"});this.eventType=t,this.canLuckDraw?this.showLeaveMod=!0:this.cruelLeave()},cruelLeave:function(){switch(this.eventType){case 0:case 1:this.comes.isFromApp(this.from)?"orderDetail"===this.plate?this.$customBack():"next"==this.from?this.jump.appAndMiniJump(0,this.routeTable.pEMyOrder,this.$vhFrom,1):wineYunJsBridge.openAppPage({client_path:{ios_path:"MyOrderViewController",android_path:"com.stg.rouge.activity.MyOrderActivity"}}):"orderDetail"===this.plate?this.jump.navigateBack():this.jump.redirectTo(this.routeTable.pEMyOrder);break;case 2:this.comes.isFromApp(this.from)?wineYunJsBridge.openAppPage({client_path:{ios_path:"goMain",android_path:"goMain"}}):this.jump.reLaunch("/pages/index/index");break;case 3:console.log("查看拼团详情");break;case 4:var t="".concat(this.$routeTable.pgGoodsDetail,"?id=").concat(this.CollPeriod,"&groupId=").concat(this.group_id);this.jump.appShare({title:this.main_title,img:this.share_image,path:t});break;case 5:console.log("跳转商品详情"),this.jump.appAndMiniJump(1,"/pages/goods-detail/goods-detail?id=".concat(this.goodsInfo.id),this.from,1);break}},inviteGroup:function(){if(this.from){var t="".concat(this.$routeTable.pgGoodsDetail,"?id=").concat(this.CollPeriod,"&groupId=").concat(this.group_id);this.jump.appShare({title:this.main_title,img:this.share_image,path:t})}else this.feedback.toast({title:"请前往APP或小程序查看此功能~"})},continueToDraw:function(){console.log("----------我是继续抽奖")},luckDraw:function(){return 0==this.canLuckDraw?this.feedback.toast({title:"亲，您的抽奖机会已经用完"}):0!=this.times?this.feedback.toast({title:"正在抽奖中，请勿重复点击"}):(this.luckyDrawing=!0,void this.startDraw())},startDraw:function(){var t=this;return(0,o.default)((0,a.default)().mark((function n(){var e;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,{},n.next=4,t.$u.api.payLuckyDraw();case 4:e=n.sent,t.luckyDrawList.forEach((function(n,i){n.id===e.data.id&&(t.priceIndex=i+1)})),t.originPrizeInfo=e.data,t.prizeContent=e.data.msg,t.startRoll(),n.next=14;break;case 11:n.prev=11,n.t0=n["catch"](0),t.getLuckyDrawCount();case 14:case"end":return n.stop()}}),n,null,[[0,11]])})))()},startRoll:function(){var t=this;return(0,o.default)((0,a.default)().mark((function n(){return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:t.times+=1,t.oneRoll(),t.times>t.cycle+10&&t.prize===t.currentIndex?(clearTimeout(t.timer),t.prize=-1,t.priceIndex=0,t.times=0,t.speed=200,setTimeout((function(n){t.showDrawSuccMod=!0}),500)):(t.times<t.cycle?t.speed-=10:t.times===t.cycle?(t.prize=t.priceIndex,t.prize>8&&(t.prize=8)):t.times>t.cycle+10&&(0===t.prize&&8===t.currentIndex||t.prize===t.currentIndex+1)?t.speed+=110:t.speed+=20,t.speed<40&&(t.speed=40),t.timer=setTimeout(t.startRoll,t.speed));case 3:case"end":return n.stop()}}),n)})))()},oneRoll:function(){var t=this.currentIndex,n=this.count;t+=1,t>n&&(t=1),this.currentIndex=t},iKnow:function(){this.prizeInfo=this.originPrizeInfo,this.getLuckyDrawCount()},toExchange:function(){console.log("------------------------我是去兑换");var t=this.prizeInfo.period,n=void 0===t?0:t;switch(this.prizeInfo.type){case 1:console.log("------------------我是去兔头商店"),this.jump.appAndMiniJump(0,this.routeTable.pBRabbitHeadShop,this.from,1);break;case 2:n?(console.log("------------------period",n),this.jump.appAndMiniJump(0,"".concat(this.routeTable.pgGoodsDetail,"?id=").concat(n),this.$vhFrom)):this.comes.isFromApp(this.from)?wineYunJsBridge.openAppPage({client_path:{ios_path:"goMain",android_path:"goMain"}}):this.jump.reLaunch(this.routeTable.pgIndex);break}},openGoodsDetail:function(t,n){if(0!=this.times)return this.feedback.toast({title:"正在抽奖中，请稍后"});this.eventType=n,this.goodsInfo=t,this.canLuckDraw?this.showLeaveMod=!0:this.cruelLeave()},topThreeJump:function(){var t=this;this.feedback.loading(),this.$u.api.userSpecifiedData({field:"home_select"}).then((function(n){var e=n.data.home_select,i=void 0===e?"1":e;"0"===i?t.$app?wineYunJsBridge.openAppPage({client_path:{ios_path:"goMain",android_path:"goMain"},ad_path_param:[{ios_key:"type",ios_val:"3",android_key:"type",android_val:"3"}]}):t.jump.reLaunch(t.routeTable.pgMiaoFa):t.$app?wineYunJsBridge.openAppPage({client_path:{ios_path:"goMain",android_path:"goMain"}}):t.jump.reLaunch(t.$routeTable.pgIndex)}))}},onPageScroll:function(t){this.scrollTop=t.scrollTop,t.scrollTop<=100?this.navBackgroundColor="rgba(224, 20, 31, ".concat(t.scrollTop/100,")"):this.navBackgroundColor="rgba(224, 20, 31, 1)"}};n.default=c},"825d":function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var i=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(n){arguments[0]=n=t.$handleEvent(n),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(n){arguments[0]=n=t.$handleEvent(n),t.getuserinfo.apply(void 0,arguments)},error:function(n){arguments[0]=n=t.$handleEvent(n),t.error.apply(void 0,arguments)},opensetting:function(n){arguments[0]=n=t.$handleEvent(n),t.opensetting.apply(void 0,arguments)},launchapp:function(n){arguments[0]=n=t.$handleEvent(n),t.launchapp.apply(void 0,arguments)},click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.click(n)}}},[t._t("default"),t.ripple?e("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},a=[]},8437:function(t,n,e){"use strict";e.r(n);var i=e("e997"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},"8e1d":function(t,n,e){"use strict";e.r(n);var i=e("9476"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},9476:function(t,n,e){"use strict";e("7a82"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("a9e3"),e("c975"),e("d3b7"),e("ac1f");var i={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var n=this;this.$u.throttle((function(){!0!==n.loading&&!0!==n.disabled&&(n.ripple&&(n.waveActive=!1,n.$nextTick((function(){this.getWaveQuery(t)}))),n.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var n=this;this.getElQuery().then((function(e){var i=e[0];if(i.width&&i.width&&(i.targetWidth=i.height>i.width?i.height:i.width,i.targetWidth)){n.fields=i;var a,o;a=t.touches[0].clientX,o=t.touches[0].clientY,n.rippleTop=o-i.top-i.targetWidth/2,n.rippleLeft=a-i.left-i.targetWidth/2,n.$nextTick((function(){n.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(n){var e="";e=uni.createSelectorQuery().in(t),e.select(".u-btn").boundingClientRect(),e.exec((function(t){n(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};n.default=i},"95b6":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return i}));var i={uIcon:e("e5e1").default},a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",{},[e("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[e("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),e("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?e("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.goBack.apply(void 0,arguments)}}},[e("v-uni-view",{staticClass:"u-icon-wrap"},[e("u-icon",{attrs:{name:t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?e("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?e("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[e("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),e("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),e("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?e("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},o=[]},"98e0":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return i}));var i={uMask:e("e710").default},a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("u-mask",{attrs:{show:t.show,zoom:!1},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.$emit("close")}}},[e("v-uni-view",{staticClass:"h-p100 flex-c-c-c"},[e("v-uni-view",{staticClass:"p-rela z-01 w-526 h-710",on:{click:function(n){n.stopPropagation(),arguments[0]=n=t.$handleEvent(n),t.$emit("jump")}}},[e("v-uni-image",{staticClass:"p-abso w-p100 h-p100",attrs:{src:t.ossIcon("/pay_success/cou_bg4.png")}}),e("v-uni-view",{staticClass:"p-abso font-wei-600 font-200",staticStyle:{top:"84rpx",right:"226rpx",color:"#CA101A","line-height":"280rpx","text-shadow":"6rpx 0 0 #000"}},[t._v(t._s(t.info.coupon_money))]),e("v-uni-view",{staticClass:"p-abso font-32 text-3",staticStyle:{top:"460rpx",left:"50%",transform:"translateX(-50%)"}},[t._v(t._s(t.stageText))])],1),e("v-uni-image",{staticClass:"mt-60 w-54 h-54",attrs:{src:t.ossIcon("/pay_success/del_gray.png"),mode:"widthFix"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.$emit("close")}}})],1)],1)},o=[]},9945:function(t,n,e){var i=e("00d9");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=e("4f06").default;a("2b69c872",i,!0,{sourceMap:!1,shadowMode:!1})},"9fdf":function(t,n,e){"use strict";e.r(n);var i=e("6113"),a=e("3644");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("5cb6");var r=e("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"6f31d510",null,!1,i["a"],void 0);n["default"]=s.exports},a8bc6:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var i=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",{staticClass:"d-flex j-center a-center",style:[t.upDownStyle]},[t.showImage?[e("v-uni-image",{style:[t.imageStyle],attrs:{src:t.imageSrc,mode:"aspectFill"}}),e("v-uni-view",{style:[t.textStyle]},[t._v(t._s(t.text))])]:[e("v-uni-view",{style:[t.lineStyle]}),e("v-uni-view",{style:[t.textStyle]},[t._v(t._s(t.text))]),e("v-uni-view",{style:[t.lineStyle]})]],2)},a=[]},aa64:function(t,n,e){"use strict";e("7a82"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("a9e3");var i={name:"u-modal",props:{value:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:""},title:{type:[String],default:"提示"},width:{type:[Number,String],default:600},content:{type:String,default:"内容"},showTitle:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0},showCancelButton:{type:Boolean,default:!1},confirmText:{type:String,default:"确认"},cancelText:{type:String,default:"取消"},confirmColor:{type:String,default:"#2979ff"},cancelColor:{type:String,default:"#606266"},borderRadius:{type:[Number,String],default:16},titleStyle:{type:Object,default:function(){return{}}},contentStyle:{type:Object,default:function(){return{}}},cancelStyle:{type:Object,default:function(){return{}}},confirmStyle:{type:Object,default:function(){return{}}},zoom:{type:Boolean,default:!0},asyncClose:{type:Boolean,default:!1},maskCloseAble:{type:Boolean,default:!1},negativeTop:{type:[String,Number],default:0}},data:function(){return{loading:!1}},computed:{cancelBtnStyle:function(){return Object.assign({color:this.cancelColor},this.cancelStyle)},confirmBtnStyle:function(){return Object.assign({color:this.confirmColor},this.confirmStyle)},uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},watch:{value:function(t){!0===t&&(this.loading=!1)}},methods:{confirm:function(){this.asyncClose?this.loading=!0:this.$emit("input",!1),this.$emit("confirm")},cancel:function(){var t=this;this.$emit("cancel"),this.$emit("input",!1),setTimeout((function(){t.loading=!1}),300)},popupClose:function(){this.$emit("input",!1)},clearLoading:function(){this.loading=!1}}};n.default=i},aab3:function(t,n,e){var i=e("24fb");n=i(!1),n.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=n},b10b:function(t,n,e){"use strict";var i=e("6bdb"),a=e.n(i);a.a},cbda:function(t,n,e){"use strict";e.r(n);var i=e("a8bc6"),a=e("55e7");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);var r=e("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"0b206e9a",null,!1,i["a"],void 0);n["default"]=s.exports},d3a7:function(t,n,e){var i=e("0d81");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=e("4f06").default;a("262b0a3a",i,!0,{sourceMap:!1,shadowMode:!1})},e997:function(t,n,e){"use strict";e("7a82");var i=e("ee27").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=i(e("f07e")),o=i(e("c964")),r=i(e("f3f3"));e("a9e3"),e("99af");var s=e("26cb"),c={name:"vh-goods-recommend-list",props:{from:{type:[String,Number],default:""},outerPaddingBottom:{type:[String,Number],default:24},innMarginLeft:{type:[String,Number],default:24},innMarginRight:{type:[String,Number],default:24},customClick:{type:Boolean,default:!1},jumpType:{type:Number,default:0},isInit:{type:Boolean,default:!0}},data:function(){return{recommendList:[]}},computed:(0,r.default)((0,r.default)({},(0,s.mapState)(["routeTable"])),{},{outerRecommendListConStyle:function(){return{paddingBottom:this.outerPaddingBottom+"rpx"}},innerRecommendListConStyle:function(){var t={};return t.marginLeft=this.innMarginLeft+"rpx",t.marginRight=this.innMarginRight+"rpx",t}}),created:function(){this.isInit&&this.getRecommendList()},methods:{getRecommendList:function(){var t=this;return(0,o.default)((0,a.default)().mark((function n(){var e;return(0,a.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,t.$u.api.recommendList();case 2:e=n.sent,t.recommendList=e.data;case 4:case"end":return n.stop()}}),n)})))()},click:function(t){this.customClick&&this.$emit("click",t),1===this.jumpType?this.jump.appAndMiniJump(0,"".concat(this.$routeTable.pgGoodsDetail,"?id=").concat(t.id),this.$vhFrom,1):this.jump.appAndMiniJump(0,"".concat(this.routeTable.pgGoodsDetail,"?id=").concat(t.id),this.$vhFrom,0)}}};n.default=c},fa94:function(t,n,e){"use strict";var i=e("062a"),a=e.n(i);a.a}}]);