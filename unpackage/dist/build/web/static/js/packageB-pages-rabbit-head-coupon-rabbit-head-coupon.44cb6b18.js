(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-rabbit-head-coupon-rabbit-head-coupon"],{"062a":function(t,e,a){var n=a("aab3");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("2db15654",n,!0,{sourceMap:!1,shadowMode:!1})},"0b9b":function(t,e,a){"use strict";var n=a("5468"),i=a.n(n);i.a},"12c6":function(t,e,a){"use strict";a.r(e);var n=a("51bd"),i=a("f074");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("f2f9");var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"519170ec",null,!1,n["a"],void 0);e["default"]=s.exports},"4f1b":function(t,e,a){"use strict";a.r(e);var n=a("825d"),i=a("8e1d");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("fa94");var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"4ed92bb2",null,!1,n["a"],void 0);e["default"]=s.exports},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={uIcon:a("e5e1").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},o=[]},5468:function(t,e,a){var n=a("7a94");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("059504a8",n,!0,{sourceMap:!1,shadowMode:!1})},5723:function(t,e,a){"use strict";a.r(e);var n=a("bc7c"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},"60de":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={vhNavbar:a("12c6").default,uButton:a("4f1b").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"content"},[""==t.from?a("v-uni-view",{},[a("vh-navbar",{attrs:{title:"兔头商店"}})],1):t._e(),a("v-uni-view",{staticClass:"p-rela w-p100 h-vh-100"},[a("v-uni-image",{staticClass:"p-abso w-p100 h-p100",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_cou_bann.png",mode:"widthFix"}}),a("v-uni-view",{staticClass:"p-rela z-1 pt-100 d-flex j-center a-center"},[a("v-uni-view",{staticClass:"cou-bg w-718 h-1050"},[t.hasExchanged?a("v-uni-view",{staticClass:"d-flex flex-column"},[a("v-uni-view",{staticClass:"d-flex j-center a-center mt-102"},[a("v-uni-image",{staticClass:"w-26 h-26",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/cir_sel.png",mode:"widthFix"}}),a("v-uni-text",{staticClass:"ml-10 font-36 font-wei text-3"},[t._v("兑换成功")])],1),a("v-uni-view",{staticClass:"mt-40 d-flex j-center a-center"},[a("v-uni-view",{staticClass:"cou-mon w-600 h-318 d-flex j-center a-center"},[a("v-uni-view",{staticClass:"font-180 font-wei text-e80404"},[t._v(t._s(t.exchangeSuccInfo.price))]),a("v-uni-view",{staticClass:"bg-e80404 w-01 h-124 ml-28 mr-34"}),a("v-uni-view",{staticClass:"font-56 font-wei text-e80404"},[a("v-uni-view",{staticClass:"l-h-48"},[t._v("RMB")]),a("v-uni-view",{staticClass:"mt-20 l-h-48"},[t._v("优惠券")])],1)],1)],1),a("v-uni-view",{staticClass:"mt-22 text-center font-28 text-9"},[t._v("使用有效期至"),a("v-uni-text",{staticClass:"font-wei ml-06"},[t._v(t._s(t.exchangeSuccInfo.expire_time))])],1),a("v-uni-view",{staticClass:"mt-130 ml-58 mr-54"},[a("v-uni-view",{staticClass:"d-flex j-sb a-center pb-32 bb-s-01-eeeeee"},[a("v-uni-view",{staticClass:"font-30 font-wei text-3"},[t._v("兑换条件")]),a("v-uni-view",{staticClass:"d-flex a-center"},[a("v-uni-image",{staticClass:"w-32 h-34",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png",mode:"widthFix"}}),a("v-uni-text",{staticClass:"ml-10 font-36 text-e80404"},[t._v(t._s(t.exchangeSuccInfo.rabbit_price))])],1)],1),a("v-uni-view",{staticClass:"d-flex j-sb a-center pt-32 pb-32 bb-s-01-eeeeee"},[a("v-uni-text",{staticClass:"font-30 font-wei text-3"},[t._v("兑换单号")]),a("v-uni-text",{staticClass:"font-24 text-3"},[t._v(t._s(t.exchangeSuccInfo.exchange))])],1),a("v-uni-view",{staticClass:"d-flex j-sb a-center pt-32 pb-32"},[a("v-uni-text",{staticClass:"font-30 font-wei text-3"},[t._v("兑换时间")]),a("v-uni-text",{staticClass:"font-24 text-3"},[t._v(t._s(t.exchangeSuccInfo.exchange_time))])],1)],1)],1):a("v-uni-view",{staticClass:"d-flex flex-column a-center"},[a("v-uni-view",{staticClass:"h-650 d-flex flex-column j-center a-center ptb-00-plr-48"},[a("v-uni-view",{staticClass:"mt-50 text-center font-36 font-wei text-3 text-hidden-2"},[t._v(t._s(t.rabbitCoupon.title))]),a("v-uni-view",{staticClass:"mt-40 text-center"},[a("v-uni-text",{staticClass:"font-60 font-wei text-e80404"},[t._v("¥")]),a("v-uni-text",{staticClass:"ml-10 font-240 font-wei text-e80404 l-h-240"},[t._v(t._s(t.rabbitCoupon.price))])],1),a("v-uni-view",{staticClass:"mt-10 text-center font-30 font-wei text-6 text-hidden-2"},[t._v(t._s(t.rabbitCoupon.brief))]),t.rabbitCoupon.coupon_info?[t.rabbitCoupon.coupon_info.validity_days?a("v-uni-view",{staticClass:"mt-40 text-center font-28 text-9"},[t._v("注：兑换后"+t._s(t.rabbitCoupon.coupon_info.validity_days)+"日内有效，请及时使用。")]):a("v-uni-view",{staticClass:"mt-40 text-center font-28 text-9"},[t._v("注：兑换后"+t._s(t.rabbitCoupon.coupon_info.effected_time)+" 至 "+t._s(t.rabbitCoupon.coupon_info.invalidate_time)+"有效")])]:t._e()],2),a("v-uni-view",{staticClass:"w-p100 pt-40 pl-54 pr-52"},[a("v-uni-view",{staticClass:"d-flex j-sb a-center pb-32 bb-s-01-eeeeee"},[a("v-uni-view",{staticClass:"font-30 font-wei text-3"},[t._v("兑换条件")]),a("v-uni-view",{staticClass:"d-flex a-center"},[a("v-uni-image",{staticClass:"w-32 h-34",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png",mode:"widthFix"}}),a("v-uni-text",{staticClass:"ml-10 font-36 text-e80404"},[t._v(t._s(t.rabbitCoupon.rabbit_price))])],1)],1),a("v-uni-view",{staticClass:"mt-32"},[a("v-uni-view",{staticClass:"font-30 font-wei text-3"},[t._v("商品详情")]),a("v-uni-view",{staticClass:"mt-24 font-28 text-9 l-h-40 text-hidden-3",domProps:{innerHTML:t._s(t.rabbitCoupon.detail)}})],1)],1)],1)],1)],1),t.hasExchanged?a("v-uni-view",{staticClass:"d-flex j-center a-center mt-50"},[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"80rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onJump.apply(void 0,arguments)}}},[t._v("去使用")])],1):a("v-uni-view",{staticClass:"d-flex j-center a-center mt-50"},[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"80rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.exchangeNow.apply(void 0,arguments)}}},[t._v("立即兑换")])],1)],1)],1)},o=[]},"7a94":function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,".cou-bg[data-v-6de7bc32]{background-image:url(https://images.vinehoo.com/vinehoomini/v3/mine/rab_cou.png);background-size:cover}.cou-mon[data-v-6de7bc32]{background-image:url(https://images.vinehoo.com/vinehoomini/v3/mine/rab_cou_mon.png);background-size:cover}",""]),t.exports=e},"7f1a":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var o=a("26cb"),r=uni.getSystemInfoSync(),s={},c={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:r.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,o.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,n=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(n)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=c},"825d":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?a("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},i=[]},"8e1d":function(t,e,a){"use strict";a.r(e);var n=a("9476"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},9476:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3"),a("c975"),a("d3b7"),a("ac1f");var n={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(a){var n=a[0];if(n.width&&n.width&&(n.targetWidth=n.height>n.width?n.height:n.width,n.targetWidth)){e.fields=n;var i,o;i=t.touches[0].clientX,o=t.touches[0].clientY,e.rippleTop=o-n.top-n.targetWidth/2,e.rippleLeft=i-n.left-n.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var a="";a=uni.createSelectorQuery().in(t),a.select(".u-btn").boundingClientRect(),a.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=n},a126:function(t,e,a){var n=a("bbdc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("703d0287",n,!0,{sourceMap:!1,shadowMode:!1})},aab3:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},bbdc:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},bc7c:function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("f07e")),o=n(a("c964")),r=n(a("f3f3"));a("e25e"),a("99af"),a("caad6"),a("2532");var s=a("26cb"),c={name:"rabbit-head-coupon",data:function(){return{from:"",rabbitCouponId:"",jumpGoodsId:"",rabbitCoupon:{},hasExchanged:0,exchangeSuccInfo:{}}},onLoad:function(t){this.rabbitCouponId=parseInt(t.id),this.jumpGoodsId=t.jumpGoodsId,t.from&&(this.from=t.from,this.muFrom(this.from)),this.getRabbitCouponDetail()},methods:(0,r.default)((0,r.default)({},(0,s.mapMutations)(["muFrom"])),{},{getRabbitCouponDetail:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){var a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.rabbitCouponDetail({id:t.rabbitCouponId});case 2:a=e.sent,console.log(a),t.rabbitCoupon=a.data;case 5:case"end":return e.stop()}}),e)})))()},exchangeNow:function(){var t=this;console.log("----------------我是立即兑换");var e=this.rabbitCoupon,a=e.rabbit_price,n=e.price;this.feedback.showModal({content:"您确认花费".concat(a,"兔头去兑换").concat(n,"元优惠券吗？"),confirm:function(){var e=(0,o.default)((0,i.default)().mark((function e(){var a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log("-------您点击了确定"),console.log(t.rabbitCoupon.id),t.feedback.loading({title:"兑换中"}),e.prev=3,e.next=6,t.$u.api.rabbitExchangeCoupon({id:t.rabbitCoupon.id});case 6:a=e.sent,console.log(a),t.exchangeSuccInfo=a.data,t.hasExchanged=1,t.feedback.hideLoading(),e.next=15;break;case 13:e.prev=13,e.t0=e["catch"](3);case 15:case"end":return e.stop()}}),e,null,[[3,13]])})));return function(){return e.apply(this,arguments)}}(),cancel:function(){console.log("-------您点击了取消")}})},onJump:function(){var t=this;if(this.jumpGoodsId){var e=this.pages.getPageLength();if(e>=2){var a,n=getCurrentPages()[e-2-1],i=(null===n||void 0===n||null===(a=n.$page)||void 0===a?void 0:a.fullPath)||"";if(i.includes(this.$routeTable.pgGoodsDetail))return void this.jump.navigateBack(2)}this.jump.appAndMiniJump(2,"".concat(this.$routeTable.pgGoodsDetail,"?id=").concat(this.jumpGoodsId),this.$vhFrom)}else this.$u.api.userSpecifiedData({field:"home_select"}).then((function(e){var a=e.data.home_select,n=void 0===a?"1":a,i="pgIndex",o="0";"0"===n&&(t.$app?o="3":i="pgMiaoFa"),t.$app?wineYunJsBridge.openAppPage({client_path:{ios_path:"goMain",android_path:"goMain"},ad_path_param:[{ios_key:"type",ios_val:o},{android_key:"type",android_val:o}]}):t.jump.switchTab(t.$routeTable[i])}))}})};e.default=c},c860:function(t,e,a){"use strict";a.r(e);var n=a("60de"),i=a("5723");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("0b9b");var r=a("f0c5"),s=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"6de7bc32",null,!1,n["a"],void 0);e["default"]=s.exports},f074:function(t,e,a){"use strict";a.r(e);var n=a("7f1a"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},f2f9:function(t,e,a){"use strict";var n=a("a126"),i=a.n(n);i.a},fa94:function(t,e,a){"use strict";var n=a("062a"),i=a.n(n);i.a}}]);