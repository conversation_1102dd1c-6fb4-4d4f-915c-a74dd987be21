(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-miaofa-miaofa","pages-miaofa-cardDetail~pages-miaofa-cardDetailtemporarily"],{"03d4":function(e,t,i){var n=i("1cda");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("b5839bf0",n,!0,{sourceMap:!1,shadowMode:!1})},"0567":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return n}));var n={vhNavbar:i("12c6").default,vhImage:i("ce7c").default,vhEmpty:i("5ba4").default,uLoadmore:i("776f").default,uPopup:i("c4b0").default,vhTabbar:i("43d5").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{class:e.newYearTheme?"bg-B21605":""},[i("v-uni-scroll-view",{staticStyle:{height:"100vh"},attrs:{"scroll-y":!0,"scroll-top":e.scrollTop},on:{scroll:function(t){arguments[0]=t=e.$handleEvent(t),e.handleGoodsScroll.apply(void 0,arguments)},scrolltolower:function(t){arguments[0]=t=e.$handleEvent(t),e.handleScrollToLower.apply(void 0,arguments)}}},[i("vh-navbar",{attrs:{height:"46","is-back":!1,newYearTheme:e.newYearTheme}},[i("v-uni-view",{staticClass:"p-rela pl-24 pr-24 w-p100",class:e.isShowStoreTabs?"flex-e-c":"flex-sb-c"},[i("v-uni-view",{staticClass:"flex-1"},[i("v-uni-view",{staticClass:"flex-sb-c h-60 bg-f5f5f5 b-rad-30",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.jump.navigateTo(e.routeTable.pFGlobalSearch+"?type=2")}}},[i("v-uni-text",{staticClass:"ml-24 font-24 text-9"},[e._v(e._s(e.channelKeyword))]),i("v-uni-view",{staticClass:"d-flex a-center"},[i("v-uni-view",{staticStyle:{width:"2rpx",height:"36rpx",background:"#e4e4e4"}}),i("v-uni-button",{staticClass:"vh-btn bg-f5f5f5 flex-c-c w-96 h-60 font-24 font-wei-450 text-333 search-miaofa b-rad-30",class:{"text-e80404":e.newYearTheme}},[e._v("搜索")])],1)],1)],1),i("v-uni-view",{staticClass:"flex-c-c ml-32"},[i("v-uni-view",{staticClass:"flex-c-c mr-26 wh-36"},[i("v-uni-view",{staticClass:"p-rela d-flex",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.jump.loginNavigateTo(""+e.routeTable.pBShoppingCart)}}},[e.newYearTheme?i("v-uni-image",{staticClass:"wh-36 p-10",attrs:{src:e.osip+"/theme/newyear/nav-car.png"}}):i("v-uni-image",{staticClass:"wh-36 p-10",attrs:{src:e.ossIcon("/second_hair/s_car_36.png")}}),e.shoppingCartNum?i("v-uni-view",{staticClass:"p-abso top-n-02 right-n-02 w-26 h-26 bg-e80404 b-rad-p50 flex-c-c font-16 text-ffffff"},[e._v(e._s(e.shoppingCartNum))]):e._e()],1)],1),i("v-uni-view",{staticClass:"flex-c-c wh-36",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.jump.loginNavigateTo(""+e.routeTable.pEMessageCenter)}}},[i("v-uni-view",{staticClass:"p-rela d-flex"},[e.newYearTheme?i("v-uni-image",{staticClass:"w-36 h-38 p-10",attrs:{src:e.osip+"/theme/newyear/nav-message.png"}}):i("v-uni-image",{staticClass:"w-36 h-38 p-10",attrs:{src:e.ossIcon("/second_hair/s_not_36_38.png")}}),e.unReadTotalNum?i("v-uni-view",{staticClass:"p-abso top-10 right-10 wh-10 bg-e80404 b-rad-p50"}):e._e()],1)],1)],1)],1)],1),i("v-uni-view",{class:e.newYearTheme&&e.goldAreaList.length&&e.goldAreaCols.length?"new-year-glodarea-bg  ptb-18-plr-24 ":""},[e.goldAreaList.length&&e.goldAreaCols.length?i("v-uni-view",{staticClass:"d-flex j-sb pt-20 pb-20 bg-ffffff",class:[e.goldAreaCols.length?"ptb-00-plr-30":"ptb-00-plr-40",e.newYearTheme?"new-year-glodarea b-rad-24":""],staticStyle:{overflow:"hidden","white-space":"nowrap"}},e._l(e.goldAreaCols,(function(t,n){return i("v-uni-view",{key:n},e._l(t,(function(t,n){return i("v-uni-view",{key:n,staticClass:"flex-c-c flex-column",class:[n?"mt-20":""],on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.JumpColumn(t)}}},[i("vh-image",{attrs:{"loading-type":2,src:t.icon,width:92,height:92,"border-radius":"12rpx"}}),i("v-uni-view",{staticClass:"mt-14 font-22 l-h-32 font-wei-450 text-6",class:e.newYearTheme?"text-e80404":""},[e._v(e._s(t.name))])],1)})),1)})),1):e._e()],1),e._l(e.cardList,(function(t,n){return i("v-uni-view",{key:n,on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.jumpCardDetail(t)}}},[t.card_extend_detail.length?i("v-uni-view",{staticClass:"bg-ffffff miaofa-card b-rad-12",class:e.newYearTheme?"new-year-card-bg b-rad-24":""},[i("v-uni-view",{staticClass:"card-title",class:e.newYearTheme?"card-title-newYear w-p80":""},[e._v(e._s(t.card_name))]),i("v-uni-view",{staticClass:"card-price",class:e.newYearTheme?"card-title-newYear":""},[i("v-uni-text",{staticClass:"price-unit",class:e.newYearTheme?"card-title-newYear":""},[e._v("¥")]),e._v(e._s(t.min_price)+"-"+e._s(t.max_price))],1),i("v-uni-view",{staticClass:"d-flex j-sb"},e._l(t.card_extend_detail,(function(t,n){return i("vh-image",{key:n,attrs:{isMf:!0,borderRadius:10,width:210,height:210,src:t.product_img,"background-image":e.newYearTheme?"http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/goods-bg.png":""}})})),1)],1):e._e()],1)})),i("v-uni-view",{staticClass:"p-stic z-978",staticStyle:{top:"46px"}},[i("v-uni-view",{staticClass:"d-flex j-sb a-center ptb-20-plr-44",class:e.newYearTheme?"bg-B21605":"bg-f7f7f7"},[i("v-uni-view",{staticClass:"font-28",class:e.newYearTheme?0==e.filterIndex?"text-ff9127 font-wei":"text-ffffff":0==e.filterIndex?"text-e80404 font-wei":"text-6",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.changeSort(0)}}},[e._v("默认")]),e._l(e.sortList,(function(t,n){return i("v-uni-view",{key:n,staticClass:"d-flex a-center",on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.changeSort(t.index)}}},[i("v-uni-text",{staticClass:"font-28",class:e.newYearTheme?e.filterIndex==t.index?"text-ff9127 font-wei":"text-ffffff":e.filterIndex==t.index?"text-e80404 font-wei":"text-6"},[e._v(e._s(t.name))]),1!==t.index||e.newYearTheme?e._e():i("v-uni-view",[e.sortType==t.type?i("v-uni-image",{staticClass:"ml-04 w-14 h-14",attrs:{src:e.osip+"/flash_purchase/sort_"+e.sort+".png",mode:"aspectFill"}}):i("v-uni-image",{staticClass:"ml-04 w-14 h-14",attrs:{src:e.osip+"/flash_purchase/sort.png",mode:"aspectFill"}})],1),1===t.index&&e.newYearTheme?i("v-uni-view",[e.sortType==t.type?i("v-uni-image",{staticClass:"ml-04 w-14 h-14",attrs:{src:e.osip+"/theme/newyear/sort_"+e.sort+".png",mode:"aspectFill"}}):i("v-uni-image",{staticClass:"ml-04 w-14 h-14",attrs:{src:e.osip+"/theme/newyear/sort.png",mode:"aspectFill"}})],1):e._e()],1)})),i("v-uni-view",{staticClass:"flex-shrink flex-c-c",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getFilterList.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"w-02 h-28",class:e.newYearTheme?"bg-ffffff":"bg-cccccc"}),e.newYearTheme?i("v-uni-view",{staticClass:"ptb-00-plr-16 font-28 pl-40 l-h-40",class:e.doFilter?"text-ff9127":"text-ffffff"},[e._v("筛选")]):i("v-uni-view",{staticClass:"ptb-00-plr-16 font-28 pl-40 l-h-40",class:e.doFilter?"text-e80404":"text-6"},[e._v("筛选")]),e.newYearTheme?i("v-uni-image",{staticClass:"w-28 h-28",attrs:{src:e.osip+"/theme/newyear/funnel"+(e.doFilter?"_sel":"")+".png"}}):i("v-uni-image",{staticClass:"w-28 h-28",attrs:{src:e.ossIcon("/comm/funnel"+(e.doFilter?"_sel":"")+".png")}})],1)],2)],1),0===e.goodsList.length?i("v-uni-view",{staticStyle:{padding:"31vh 0"}},[i("vh-empty",{attrs:{bgColor:"transparent","padding-top":0,"padding-bottom":0,"image-src":e.ossIcon("/empty/emp_goods.png"),text:"暂无数据","text-bottom":0}})],1):i("v-uni-view",{staticClass:"ptb-00-plr-24"},e._l(e.goodsList,(function(t,n){return i("v-uni-view",{key:n,staticClass:"bg-ffffff p-20 d-flex mb-20 b-rad-12",class:e.newYearTheme?"new-goods-list-bg ":"",attrs:{id:"goods_"+n},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.handleJump(e.routeTable.pgGoodsDetail+"?id="+t.id,"")}}},[i("v-uni-view",{staticClass:"miaofa-product-image"},[i("vh-image",{attrs:{"background-image":e.newYearTheme?"http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/goods-bg.png":"",width:210,height:210,borderRadius:10,src:t.product_img}})],1),i("v-uni-view",{staticClass:"d-flex flex-column j-sb ml-20 w-p100"},[i("v-uni-view",[i("v-uni-view",{staticClass:"product-title"},[e._v(e._s(t.title))]),i("v-uni-view",{staticClass:"product-brief"},[e._v(e._s(t.brief))])],1),i("v-uni-view",{staticClass:"miaofa-product-bottom d-flex a-center j-sb"},[i("v-uni-view",{staticClass:"font-wei-500 text-e80404"},[i("v-uni-text",{staticClass:"font-24"},[e._v("¥")]),i("v-uni-text",{staticClass:"font-36"},[e._v(e._s(t.price))])],1),i("v-uni-view",{staticClass:"font-24",staticStyle:{color:"#999"}},[e._v("已售"),i("v-uni-text",{staticStyle:{color:"#e80404"}},[e._v(e._s(t.vest_purchased+t.purchased))])],1)],1)],1)],1)})),1),0!==e.goodsList.length?i("u-loadmore",{attrs:{status:e.loadStatus}}):e._e(),i("v-uni-view",{},[i("u-popup",{attrs:{"safe-area-inset-bottom":!0,mode:"bottom","border-radius":20},model:{value:e.showScreenPop,callback:function(t){e.showScreenPop=t},expression:"showScreenPop"}},[i("v-uni-view",{staticClass:"p-rela",style:[{maxHeight:e.screenPopMaxHeight}]},[i("v-uni-view",{staticClass:"p-fixed top-0 z-100 w-p100 bg-ffffff d-flex j-sb a-center ptb-32-plr-30"},[i("v-uni-view",{}),i("v-uni-view",{staticClass:"text-center font-32 font-wei text-0"},[e._v("全部筛选")]),i("v-uni-image",{staticClass:"w-44 h-44",attrs:{src:e.osip+"/flash_purchase/clo_gray.png",mode:"widthFix"},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.showScreenPop=!1}}})],1),i("v-uni-view",{staticClass:"fade-in"},[i("v-uni-view",{staticClass:"pt-100 mt-10 pr-28 pl-28"},[i("v-uni-view",{staticClass:"font-32 font-wei text-3"},[e._v("价格区间（元）")]),i("v-uni-view",{staticClass:"d-flex j-sb a-center mt-20"},[i("v-uni-input",{staticClass:"bg-f6f6f6 w-308 h-70 text-center text-3",class:["font-26 b-rad-36"],attrs:{type:"number",placeholder:"最低价","placeholder-style":"color: #999; font-size: 26rpx;"},model:{value:e.minPrice,callback:function(t){e.minPrice=t},expression:"minPrice"}}),i("v-uni-view",{staticClass:"bg-999999 w-32 h-02"}),i("v-uni-input",{staticClass:"bg-f6f6f6 w-308 h-70 text-center text-3",class:["font-26 b-rad-36"],attrs:{type:"number",placeholder:"最高价","placeholder-style":"color: #999; font-size:26rpx;"},model:{value:e.maxPrice,callback:function(t){e.maxPrice=t},expression:"maxPrice"}})],1)],1),i("v-uni-view",{staticClass:"pt-60 pr-28 pb-40 pl-28"},e._l([{title:"关键词",list:e.keywordList,typeName:"product_keyword",typeId:"keywordId"},{title:"类型",list:e.wineTypeList,typeName:"product_category",typeId:"categoryId"},{title:"国家",list:e.countryList,typeName:"country",typeId:"countryId"}].concat([]).filter((function(e){return e.list.length})),(function(t,n){return i("v-uni-view",{key:t.title,class:[n?"mt-60":"mt-02"]},[i("v-uni-view",{staticClass:"font-32 font-wei text-3"},[e._v(e._s(t.title))]),i("v-uni-view",{staticClass:"d-flex j-sb flex-wrap"},[e._l(t.list,(function(n){return i("v-uni-view",{key:n.id,staticClass:"bg-f6f6f6 w-216 h-70 d-flex j-center a-center mt-20 font-26 text-3",class:["b-rad-36",e._data[t.typeId]==n.id?"fade-in bg-fce0e0 b-s-01-e80404 text-e04040":"fade-in"],on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.selectFilterItem(t.typeName,t.typeId,n)}}},[e._v(e._s(n.name))])})),i("v-uni-view",{staticClass:"w-216"}),i("v-uni-view",{staticClass:"w-216"})],2)],1)})),1),i("v-uni-view",{staticClass:"p-stic bottom-0 z-999 bg-feffff w-p100 h-104 d-flex j-sa a-center b-sh-00021200-022 ptb-00-plr-28"},[i("v-uni-button",{staticClass:"vh-btn flex-c-c w-308 h-64 font-28 text-9 bg-eeeeee b-rad-32",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.resetFilter.apply(void 0,arguments)}}},[e._v("重置")]),i("v-uni-button",{staticClass:"vh-btn flex-c-c w-308 h-64 font-28 b-rad-32",class:["text-ffffff bg-e80404"],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.confirmFilter.apply(void 0,arguments)}}},[e._v("确定")])],1)],1)],1)],1)],1),i("vh-tabbar",{attrs:{loading:e.initLoading},on:{topRefresh:function(t){arguments[0]=t=e.$handleEvent(t),e.topRefresh.apply(void 0,arguments)},recordTabbarPages:function(t){arguments[0]=t=e.$handleEvent(t),e.recordPages.apply(void 0,arguments)}}})],2)],1)},o=[]},"0efb":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"vh-image-con",class:[e.isMf?"mf-card":""],style:[e.wrapStyle,e.backgroundStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClick.apply(void 0,arguments)}}},[e.isError?e._e():i("v-uni-image",{staticClass:"vh-image",style:{backgroundColor:e.bgColor,borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius)},attrs:{src:e.getImage,mode:e.mode,"lazy-load":e.lazyLoad,"show-menu-by-longpress":e.showMenuByLongpress},on:{error:function(t){arguments[0]=t=e.$handleEvent(t),e.onErrorHandler.apply(void 0,arguments)},load:function(t){arguments[0]=t=e.$handleEvent(t),e.onLoadHandler.apply(void 0,arguments)}}}),e.showLoading&&e.loading?i("v-uni-view",{staticClass:"vh-image-loading",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius),backgroundColor:this.bgColor}},[i("v-uni-image",{style:[e.wrapStyle],attrs:{src:e.getLoadingImage,mode:e.mode}})],1):e._e(),e.showError&&e.isError&&!e.loading?i("v-uni-view",{staticClass:"u-image-error",style:{borderRadius:"circle"==e.shape?"50%":e.$u.addUnit(e.borderRadius)}},[i("v-uni-image",{style:[e.wrapStyle],attrs:{src:e.getLoadingImage,mode:e.mode}})],1):e._e()],1)},a=[]},"10eb":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},i("d9e2"),i("d401")},"12dc":function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(i("f07e")),o=n(i("c964")),r=n(i("f3f3")),s=n(i("fc11"));i("a9e3"),i("e9c4");var l,c=i("26cb"),u={name:"vh-tabbar",props:{loading:{type:Boolean,default:!0},show:{type:Boolean,default:!0},value:{type:[String,Number],default:0},height:{type:[String,Number],default:116},iconSize:{type:[String,Number],default:52},midButtonSize:{type:[String,Number],default:110},activeColor:{type:String,default:"#E80404"},inactiveColor:{type:String,default:"#333"},midButton:{type:Boolean,default:!0},list:{type:Array,default:function(){return[{iconPath:"https://images.vinehoo.com/vinehoomini/v3/tab_bar/index_black.png",selectedIconPath:"https://images.vinehoo.com/vinehoomini/v3/tab_bar/index_red.png",text:"首页",customIcon:!1,pagePath:"/pages/index/index"},{iconPath:"https://images.vinehoo.com/vinehoomini/v3/tab_bar/fla_pur_black.png",selectedIconPath:"https://images.vinehoo.com/vinehoomini/v3/tab_bar/fla_pur_red.png",text:"闪购",customIcon:!1,pagePath:"/pages/flash-purchase/flash-purchase"},{iconPath:"https://images.vinehoo.com/vinehoomini/v3/tab_bar/new_sec_hair_black.png",selectedIconPath:"https://images.vinehoo.com/vinehoomini/v3/tab_bar/new_sec_hair_red.png",text:"现货速发",customIcon:!1,pagePath:"/pages/miaofa/miaofa"},{iconPath:"https://images.vinehoo.com/vinehoomini/v3/tab_bar/mine_black.png",selectedIconPath:"https://images.vinehoo.com/vinehoomini/v3/tab_bar/mine_red.png",text:"我的",customIcon:!1,pagePath:"/pages/mine/mine"}]}},beforeSwitch:{type:Function,default:null},hideTabBar:{type:Boolean,default:!0},tabbarClass:{type:String,default:""}},data:function(){var e;return e={midButtonLeft:"50%",pageUrl:"",showPopup:!1,lastClickTime:0,topRefreshTimer:null,tabbarType:1},(0,s.default)(e,"showPopup",!1),(0,s.default)(e,"isNewYear",!1),e},created:function(){this.hideTabBar&&uni.hideTabBar();var e=getCurrentPages();this.pageUrl=e[e.length-1].route},computed:(0,r.default)((0,r.default)({},(0,c.mapState)("startupPageOptions",["indexStartupPageCount","miaofaStartupPageCount"])),{},{isMiaofaPage:function(){return"pages/miaofa/miaofa"===this.pageUrl},displayList:function(){if(!this.list||!Array.isArray(this.list))return[];var e=JSON.parse(JSON.stringify(this.list));if(this.isNewYear&&e[3]){var t=uni.getStorageSync("tabbarThemeConfig");t&&t.isopen&&(e[3]=(0,r.default)((0,r.default)({},e[3]),{},{iconPath:t.iconPath,selectedIconPath:t.selectedIconPath,text:t.text}))}return e},elIconPath:function(){var e=this;return function(t){var i=e.displayList[t].pagePath;return i?i==e.pageUrl||i=="/"+e.pageUrl?e.displayList[t].selectedIconPath:e.displayList[t].iconPath:t==e.value?e.displayList[t].selectedIconPath:e.displayList[t].iconPath}},elColor:function(){var e=this;return function(t){var i=e.displayList[t].pagePath;return i?i==e.pageUrl||i=="/"+e.pageUrl?e.activeColor:e.inactiveColor:t==e.value?e.activeColor:e.inactiveColor}}}),mounted:function(){var e=this;console.log(this.list),this.midButton&&this.getMidButtonLeft();var t=uni.getStorageSync("tabbarThemeConfig");t&&t.isopen?this.isNewYear=!0:setTimeout((function(){e.initTabbarConfig()}),500)},methods:(0,r.default)((0,r.default)({},(0,c.mapMutations)("startupPageOptions",["UPDATE_PREFIX_TABBAR_PAGE","UPDATE_INDEX_STARTUP_PAGE_COUNT","UPDATE_MIAOFA_STARTUP_PAGE_COUNT"])),{},(l={initTabbarConfig:function(){try{var e=uni.getStorageSync("tabbarThemeConfig");e&&e.isopen&&(this.isNewYear=!0)}catch(t){console.error("初始化 tabbar 配置失败:",t)}},openPopup:function(){console.log("打开弹窗"),this.showPopup=!0},handlePostClick:function(){this.jump.loginNavigateTo(this.$routeTable.pCSendPost),this.closePopup()},handleWineReviewClick:function(){this.jump.loginNavigateTo(this.$routeTable.pCWineComment),this.closePopup()},closePopup:function(){console.log("关闭弹窗"),this.showPopup=!1},switchTab:function(e){this.$emit("change",e),this.displayList[e].pagePath?this.displayList[e].pagePath==="/".concat(this.pageUrl)?(this.displayList[e].midButton&&this.midButton&&(console.log(this.displayList[e].midButton,this.midButton),this.openPopup()),this.handleDoubleClick()):(uni.switchTab({url:this.displayList[e].pagePath}),this.recordTabbarPages(e)):this.$emit("input",e)},recordTabbarPages:function(e){var t="/".concat(this.pageUrl),i=this.displayList[e].pagePath,n=this.$routeTable,a=n.pgIndex,o=n.pgMiaoFa;t===a&&i===o?this.UPDATE_MIAOFA_STARTUP_PAGE_COUNT(this.miaofaStartupPageCount+1):t===o&&i===a?this.UPDATE_INDEX_STARTUP_PAGE_COUNT(this.indexStartupPageCount+1):(this.UPDATE_INDEX_STARTUP_PAGE_COUNT(0),this.UPDATE_MIAOFA_STARTUP_PAGE_COUNT(0))},handleDoubleClick:function(){var e=this;if(!this.loading){var t=(new Date).getTime(),i=this.lastClickTime;this.lastClickTime=t,t-i<300&&(this.topRefreshTimer&&clearTimeout(this.topRefreshTimer),this.topRefreshTimer=setTimeout((function(){e.$emit("topRefresh")}),300))}},getOffsetRight:function(e,t){return t?-20:e>9?-40:-30},getMidButtonLeft:function(){var e=this.$u.sys().windowWidth;this.midButtonLeft=e/2+"px"},clickHandler:function(e){var t=this;return(0,o.default)((0,a.default)().mark((function i(){var n;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!t.beforeSwitch||"function"!==typeof t.beforeSwitch){i.next=10;break}if(n=t.beforeSwitch.bind(t.$u.$parent.call(t))(e),!n||"function"!==typeof n.then){i.next=7;break}return i.next=5,n.then((function(){t.switchTab(e)})).catch((function(){}));case 5:i.next=8;break;case 7:!0===n&&t.switchTab(e);case 8:i.next=11;break;case 10:t.switchTab(e);case 11:case"end":return i.stop()}}),i)})))()}},(0,s.default)(l,"openPopup",(function(){this.showPopup=!0})),(0,s.default)(l,"closePopup",(function(){this.showPopup=!1})),(0,s.default)(l,"handlePostClick",(function(){this.jump.loginNavigateTo(this.$routeTable.pCSendPost),this.closePopup()})),(0,s.default)(l,"handleWineReviewClick",(function(){this.jump.loginNavigateTo(this.$routeTable.pCWineComment),this.closePopup()})),l))};t.default=u},"144f":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){}));var n=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view",{staticClass:"d-flex j-center",style:[this.outerEmpConStyle]},[t("v-uni-view",{staticClass:"p-rela",style:[this.innEmpConStyle]},[t("v-uni-image",{staticClass:"w-p100 h-p100",attrs:{src:this.imageSrc,mode:"aspectFill"}}),t("v-uni-view",{staticClass:"p-abso w-p100 d-flex j-center font-28 text-9",style:[this.textStyle]},[this._v(this._s(this.text))])],1)],1)},a=[]},"19d1":function(e,t,i){"use strict";i.r(t);var n=i("913e"),a=i("94c5");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("cfe0");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"5fc86f2e",null,!1,n["a"],void 0);t["default"]=s.exports},"1cda":function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-badge[data-v-5fc86f2e]{display:inline-flex;justify-content:center;align-items:center;line-height:%?24?%;padding:%?4?% %?8?%;border-radius:%?100?%;z-index:9}.u-badge--bg--primary[data-v-5fc86f2e]{background-color:#2979ff}.u-badge--bg--error[data-v-5fc86f2e]{background-color:#fa3534}.u-badge--bg--success[data-v-5fc86f2e]{background-color:#19be6b}.u-badge--bg--info[data-v-5fc86f2e]{background-color:#909399}.u-badge--bg--warning[data-v-5fc86f2e]{background-color:#f90}.u-badge-dot[data-v-5fc86f2e]{height:%?16?%;width:%?16?%;border-radius:%?100?%;line-height:1}.u-badge-mini[data-v-5fc86f2e]{-webkit-transform:scale(.8);transform:scale(.8);-webkit-transform-origin:center center;transform-origin:center center}.u-info[data-v-5fc86f2e]{background-color:#909399;color:#fff}',""]),e.exports=t},"2da4":function(e,t,i){var n=i("39e4");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("64a51bd6",n,!0,{sourceMap:!1,shadowMode:!1})},"363e":function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-fixed-placeholder[data-v-f8ff4db6]{box-sizing:initial}.vh-tabbar__content[data-v-f8ff4db6]{display:flex;align-items:center;position:fixed;bottom:0;left:0;width:100%;z-index:998;background-color:#fff;box-shadow:0 %?2?% %?12?% 0 rgba(0,0,0,.22);box-sizing:initial}.vh-tabbar__content.miaofa-bg[data-v-f8ff4db6]{background:linear-gradient(180deg,#ffe4ba,#fffaf0)}.vh-tabbar__content__circle__border[data-v-f8ff4db6]{border-radius:100%;width:%?130?%;height:%?130?%;top:%?-26?%;position:absolute;z-index:4;background-color:#fff;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.vh-tabbar__content__item[data-v-f8ff4db6]{flex:1;display:flex;justify-content:center;height:100%;padding:%?12?% 0;display:flex;flex-direction:column;align-items:center;position:relative}.vh-tabbar__content__item__button[data-v-f8ff4db6]{position:absolute;top:%?14?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.vh-tabbar__content__item__text[data-v-f8ff4db6]{font-size:%?24?%;line-height:%?40?%;position:absolute;bottom:%?14?%;left:50%;-webkit-transform:translateX(-50%);transform:translateX(-50%);width:100%;text-align:center;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.vh-tabbar__content__circle[data-v-f8ff4db6]{position:relative;display:flex;flex-direction:column;justify-content:space-between;z-index:10;height:calc(100% - 1px)}.vh-tabbar__content__circle__button[data-v-f8ff4db6]{width:%?130?%;height:%?130?%;border-radius:100%;display:flex;justify-content:center;align-items:center;position:absolute;background-color:#fff;top:%?-26?%;left:50%;z-index:6;-webkit-transform:translateX(-50%);transform:translateX(-50%)}.popup-mask[data-v-f8ff4db6]{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5);z-index:9998}.popup-content[data-v-f8ff4db6]{position:fixed;left:0;right:0;bottom:0;background-color:#fff;height:%?600?%;z-index:9999;border-radius:%?24?% %?24?% 0 0;-webkit-transform:translateY(100%);transform:translateY(100%);transition:-webkit-transform .3s ease-out;transition:transform .3s ease-out;transition:transform .3s ease-out,-webkit-transform .3s ease-out;display:flex;flex-direction:column}.popup-content.popup-show[data-v-f8ff4db6]{-webkit-transform:translateY(0);transform:translateY(0)}.popup-inner[data-v-f8ff4db6]{flex:1;padding:%?60?% %?52?% 0 %?52?%;overflow-y:auto}.popup-button[data-v-f8ff4db6]{height:%?112?%;display:flex;justify-content:center;align-items:center;padding-bottom:%?100?%}.button-image[data-v-f8ff4db6]{width:%?112?%;height:%?112?%}.popup-item[data-v-f8ff4db6]{width:100%;height:%?136?%;margin-bottom:%?24?%;position:relative;z-index:1}.popup-image[data-v-f8ff4db6]{width:100%;height:100%;position:relative;z-index:1}.popup-title-wrapper[data-v-f8ff4db6]{display:flex;align-items:center;gap:%?8?%;position:relative;z-index:3}.title-icon[data-v-f8ff4db6]{width:%?12?%;height:%?20?%;position:relative;z-index:3}.popup-text-wrapper[data-v-f8ff4db6]{position:absolute;top:0;left:0;right:0;bottom:0;padding:%?30?% %?40?%;display:flex;flex-direction:column;justify-content:center;gap:%?6?%;z-index:2}.popup-title[data-v-f8ff4db6]{font-weight:500;font-size:%?32?%;color:#333;text-align:left;position:relative;z-index:31111}.popup-desc[data-v-f8ff4db6]{font-size:%?20?%;color:#666;text-align:left;position:relative;z-index:31111}',""]),e.exports=t},"39d1":function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("b64b");var a=n(i("f07e")),o=n(i("c964")),r=n(i("f3f3")),s=i("1e48"),l=i("26cb"),c={name:"NewPeopleMixin",data:function(){return{newPeopleCouponActivityInfo:{},newPeopleGoodsList:[],newPeopleCouponPackage:{},isNewUser:0,showNewPeopleIndexMask:!1,receivedBenefits:!1,receiveBenefitsLoading:!1,getNPCAILoading:!1}},computed:(0,r.default)((0,r.default)({},(0,l.mapState)("newPeopleArea",["showNewPeopleFloatingFrame"])),{},{newPeopleAreaVisible:function(e){var t=e.isNewUser,i=e.newPeopleCouponActivityInfo,n=e.newPeopleCouponPackage;return!!(t&&Object.keys(i).length&&Object.keys(n).length)},newPeopleFloatingFrameVisible:function(e){var t=e.newPeopleAreaVisible,i=e.showNewPeopleFloatingFrame;return!(!t||!i)},newPeopleIndexMaskVisible:function(e){var t=e.newPeopleAreaVisible,i=e.newPeopleCouponPackage,n=e.receivedBenefits,a=e.showNewPeopleIndexMask,o=i.collect_status;return!(!t||!(0===o||1===o&&n))&&a}}),onShow:function(){this.getNewPeopleCouponActivityInfo()},methods:{getIsNewUser:function(){var e=this;return(0,o.default)((0,a.default)().mark((function t(){var i,n,o;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.login.isLogin(e.$vhFrom,0)){t.next=9;break}return t.next=3,e.$u.api.userSpecifiedData({field:"is_new_user"});case 3:i=t.sent,n=i.data.is_new_user,o=void 0===n?0:n,e.isNewUser=+o,t.next=10;break;case 9:e.isNewUser=1;case 10:case"end":return t.stop()}}),t)})))()},getNewPeopleCouponActivityInfo:function(){var e=this;return(0,o.default)((0,a.default)().mark((function t(){var i,n;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,!e.getNPCAILoading){t.next=3;break}return t.abrupt("return");case 3:return e.getNPCAILoading=!0,t.next=6,e.$u.api.newPeopleCouponActivityInfo({bection:1});case 6:i=t.sent,n=i.data,e.newPeopleCouponActivityInfo=n||{},e.newPeopleGoodsList=n.goods_list||[],e.newPeopleCouponPackage=n.coupon_package||{},uni.getStorage({key:"newPeopleIndexMaskCountDown",success:function(t){var i=t.data,n=e.$u.timeFormat(Date.now(),"yyyy-mm-dd");e.showNewPeopleIndexMask=!(i===n),console.log(i),console.log(n)},fail:function(){e.showNewPeopleIndexMask=!0}});case 12:return t.prev=12,e.getNPCAILoading=!1,e.receiveBenefitsLoading=!1,t.finish(12);case 16:case"end":return t.stop()}}),t,null,[[0,,12,16]])})))()},getNewPeopleReceiveBenefits:function(){var e=this;if(this.login.isLogin(this.$vhFrom)){if(this.getNPCAILoading||this.receiveBenefitsLoading)return;this.feedback.loading({title:"领取中"}),this.receiveBenefitsLoading=!0,this.$u.api.newPeopleReceiveBenefits().then((function(t){e.receivedBenefits=!0,e.getNewPeopleCouponActivityInfo()})).catch((function(){e.receiveBenefitsLoading=!1}))}},onJumpActivityPage:function(){this.jump.h5Jump((0,s.NEW_PEOPLE_ACTIVITY_URL)(),this.$vhFrom,4)},closeNewPeopleIndexMask:function(){uni.setStorageSync("newPeopleIndexMaskCountDown",this.$u.timeFormat(Date.now(),"yyyy-mm-dd")),this.showNewPeopleIndexMask=!1}}};t.default=c},"39e4":function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-load-more-wrap[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center}.u-load-more-inner[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:0 %?12?%}.u-more[data-v-15067509]{position:relative;display:flex;flex-direction:row;justify-content:center}.u-dot-text[data-v-15067509]{font-size:%?28?%}.u-loadmore-icon-wrap[data-v-15067509]{margin-right:%?8?%}.u-loadmore-icon[data-v-15067509]{display:flex;flex-direction:row;align-items:center;justify-content:center}',""]),e.exports=t},"3dc3":function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(i("d0af")),o=n(i("f3f3"));i("a9e3"),i("d3b7"),i("159b"),i("e25e"),i("c975");var r=i("26cb"),s={name:"u-image",props:{loadingType:{type:[String,Number],default:1},isMf:{type:Boolean,default:!1},src:{default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!1},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:100},bgColor:{type:String,default:"transparent"},isResize:{type:Boolean,default:!1},resizeRatio:{type:Object,default:function(){return{wratio:16,hratio:9}}},resizeUsePx:{type:Boolean,default:!1},opacityProp:{type:Number,default:1},backgroundImage:{type:String,default:""}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(e){e?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:(0,o.default)((0,o.default)({},(0,r.mapState)(["ossPrefix"])),{},{wrapStyle:function(){var e={};if(e.width=this.$u.addUnit(this.width),e.height=this.$u.addUnit(this.height),this.backgroundImage&&(e.backgroundImage="url(".concat(this.backgroundImage,")"),e.backgroundSize="100% 100%",e.backgroundRepeat="no-repeat",e.backgroundPosition="center"),this.isResize){var t,i,n=(null===this||void 0===this||null===(t=this.src)||void 0===t||null===(i=t.split("?"))||void 0===i?void 0:i[1])||"",o=n.split("&"),r={};o.forEach((function(e){var t=e.split("="),i=(0,a.default)(t,2),n=i[0],o=i[1];r[n]=o}));var s=+((null===r||void 0===r?void 0:r.w)||""),l=+((null===r||void 0===r?void 0:r.h)||"");if(!isNaN(s)&&!isNaN(l)&&s&&l){var c=parseInt(this.width),u=c/s*l,d=this.resizeRatio,f=d.wratio,p=d.hratio;if("auto"!==f&&"auto"!==p){var h=c*f/p,g=c*p/f;u>h?u=h:u<g&&(u=g)}this.resizeUsePx?e.height="".concat(u,"px"):e.height=this.$u.addUnit(u)}}return e.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),e.overflow=this.borderRadius>0||"circle"==this.shape?"hidden":"visible",this.fade&&(e.opacity=this.opacity,e.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),e},getLoadingImage:function(){return"https://images.vinehoo.com/vinehoomini/v3/occupy_img/new_img".concat(this.loadingType,".png")},getImage:function(){return"string"!==typeof this.src?"":-1==this.src.indexOf("http")?this.ossPrefix+this.src:this.src}}),methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(e){this.loading=!1,this.isError=!0,this.$emit("error",e)},onLoadHandler:function(){var e=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){e.durationTime=e.duration,e.opacity=e.opacityProp,setTimeout((function(){e.removeBgColor()}),e.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};t.default=s},"3dfa":function(e,t,i){var n=i("363e");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("23f50300",n,!0,{sourceMap:!1,shadowMode:!1})},4053:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(Array.isArray(e))return(0,n.default)(e)};var n=function(e){return e&&e.__esModule?e:{default:e}}(i("b680"))},"43d5":function(e,t,i){"use strict";i.r(t);var n=i("a021"),a=i("6424");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("d1d0");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"f8ff4db6",null,!1,n["a"],void 0);t["default"]=s.exports},"4bc0":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={name:"longpressCopyMixin",data:function(){return{jumpStatus:!1,jumpTimeout:null}},methods:{handleJump:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.jumpStatus||(i?location.href=e:this.jump.appAndMiniJump(1,e,t))},handleLongpress:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0,i="2"===t;i?(this.jumpStatus=!0,this.copy.appCopy(e)):this.copy.appCopy(e)},handleTouched:function(e){var t=this,i="2"===e;i&&(this.jumpTimeout=setTimeout((function(){t.jumpStatus=!1,t.jumpTimeout&&clearTimeout(t.jumpTimeout)}),200))}}};t.default=n},"55c2":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3");var n={name:"vh-empty",props:{bgColor:{type:String,default:"#FFF"},paddingTop:{type:[String,Number],default:0},paddingBottom:{type:[String,Number],default:0},width:{type:[String,Number],default:440},height:{type:[String,Number],default:360},imageSrc:{type:String,default:""},textBottom:{type:[String,Number],default:46},text:{type:String,default:""}},computed:{outerEmpConStyle:function(){return{backgroundColor:this.bgColor,paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},innEmpConStyle:function(){return{width:this.width+"rpx",height:this.height+"rpx"}},textStyle:function(){return{bottom:this.textBottom+"rpx"}}}};t.default=n},"5b9a":function(e,t,i){"use strict";var n=i("7779"),a=i.n(n);a.a},"5ba4":function(e,t,i){"use strict";i.r(t);var n=i("144f"),a=i("a58c");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"55488dce",null,!1,n["a"],void 0);t["default"]=s.exports},6424:function(e,t,i){"use strict";i.r(t);var n=i("12dc"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},"6ab5":function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-image-con[data-v-89d76102]{position:relative;transition:opacity .5s ease-in}.vh-image[data-v-89d76102]{width:100%;height:100%}.vh-image-loading[data-v-89d76102],\n.u-image-error[data-v-89d76102]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#fff;color:#909399;font-size:%?46?%}.mf-card[data-v-89d76102]{background-color:#f7f7f7!important;padding:5px}',""]),e.exports=t},"6d4e":function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("99af"),i("caad6"),i("d81d"),i("fb6a"),i("d3b7"),i("159b"),i("ac1f"),i("a9e3"),i("14d9"),i("13d5"),i("4e82"),i("acd8"),i("4ec9"),i("3ca3"),i("ddb0"),i("a630");var a,o=n(i("f07e")),r=n(i("c964")),s=n(i("f3f3")),l=n(i("d0ff")),c=n(i("fc11")),u=n(i("6f67")),d=n(i("c715")),f=n(i("39d1")),p=n(i("f4c6")),h=n(i("ca79")),g=n(i("d113")),v=i("26cb"),m=(i("d8be"),i("1e48"),n(i("4bc0"))),b=(null===(a=uni.getSystemInfoSync())||void 0===a?void 0:a.screenWidth)||0,w=b>=390;console.log("$isBigScreen",b,w);var y="".concat(b-2*uni.upx2px(344)-2*uni.upx2px(24),"px");console.log("WfitemMarginRight",y);var x={$wfitemIdKey:"$wfitemId",$isRecommended:!1,$wfitemWidth:344,$imgIsResize:!0,$isBigScreen:w,$reportDisabled:!0},_={mixins:[u.default,d.default,f.default,p.default,h.default,g.default,m.default],data:function(){var e;return e={query:{uuid:uni.getStorageSync("uniqueId")||"",identifier:""},initLoading:!0,osip:"https://images.vinehoo.com/vinehoomini/v3",renderLoading:!0,wfitemStyle:{width:344,marginRight:y,marginBottom:16},isShowStoreTabs:!1,storeInfo:null,filterList:[],newYearTheme:!1,secStoreRangeMaskVisible:!1,pendInsertId:"",wineTypeList:[],categoryId:"",product_category:[0],countryList:[],countryId:"",country:[0],regionsList:[],regionsId:"",regions:[0],keywordList:[],keywordId:"",product_keyword:[0],minPrice:"",maxPrice:"",doFilter:0,filterIndex:0,visibleGoods:[],filterInfo:{},isEmpty:!1,page:1,limit:8,totalPage:0,scrollLeft:0,oldScrollLeft:0,goldAreaList:[],cardList:[]},(0,c.default)(e,"filterIndex",0),(0,c.default)(e,"scrollTop",0),(0,c.default)(e,"sortType","sort"),(0,c.default)(e,"sort","desc"),(0,c.default)(e,"doFilter",0),(0,c.default)(e,"screenPopMaxHeight",0),(0,c.default)(e,"showScreenPop",!1),(0,c.default)(e,"countryList",[]),(0,c.default)(e,"wineTypeList",[]),(0,c.default)(e,"goodsList",[]),(0,c.default)(e,"keywordList",[]),(0,c.default)(e,"regionsList",[]),(0,c.default)(e,"loadStatus","loadmore"),e},mounted:function(){var e=this;setInterval((function(){var t=uni.getStorageSync("visibleGoods")||[],i=[].concat((0,l.default)(t),(0,l.default)(e.visibleGoods));uni.setStorageSync("visibleGoods",i),e.visibleGoods=[]}),3e3)},computed:(0,s.default)((0,s.default)((0,s.default)({},(0,v.mapState)(["routeTable","dev"])),(0,v.mapState)("startupPageOptions",["miaofaStartupPageCount"])),{},{sortList:function(){return[{index:1,name:"价格",type:"price"},{index:2,name:"上新",type:"onsale_time"},{index:3,name:"销量",type:"purchased"}]},goldAreaCols:function(e){var t=e.goldAreaList;if(![4,5,8,10].includes(t.length))return[];switch(t.length){case 8:return t.slice(0,4).map((function(e,i){return[e,t[i+4]]}));case 10:return t.slice(0,5).map((function(e,i){return[e,t[i+5]]}));default:return t.map((function(e){return[e]}))}},$wfitemIdKey:function(){return x.$wfitemIdKey}}),methods:(0,s.default)((0,s.default)({},(0,v.mapActions)("newcomerCoupon",["initNewcomerCouponInfo"])),{},{secondConfig:function(){var e=this;return(0,r.default)((0,o.default)().mark((function t(){var i;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$u.api.secondConfig();case 2:i=t.sent,e.newYearTheme=i.data.isopen;case 4:case"end":return t.stop()}}),t)})))()},handleScrollToLower:function(){console.log("触发了 scrolltolower 事件",this.page,this.totalPage),this.page<this.totalPage&&"loading"!==this.loadStatus?(console.log("开始加载更多数据"),this.loadStatus="loading",this.page++,this.getGoodsList()):console.log("没有更多数据了 或 正在加载中")},handleGoodsScroll:function(e){var t=this,i=e.detail.scrollTop;this.navBackgroundColor=i<=100?"rgba(224, 20, 31, ".concat(i/100,")"):"rgba(224, 20, 31, 1)",this.$nextTick((function(){var e=uni.createSelectorQuery().in(t);t.goodsList.forEach((function(i,n){e.select("#goods_".concat(n)).boundingClientRect((function(e){if(e&&e.top<=window.innerHeight&&e.bottom>=0){var n="",a="",o=uni.getStorageSync("loginInfo")||"{}";n=o&&o.uid?o.uid:uni.getStorageSync("uniqueId"),a="next"==t.$vhFrom?"hm":"1"==t.$vhFrom?"android":"2"==t.$vhFrom?"ios":"h5";var r={uid:String(n),created_time:(new Date).getTime(),metric_name:"period_exposure",device:a,period:Number(i.id),period_type:Number(i.periods_type)},s=t.visibleGoods.some((function(e){return e.period===i.id}));s||t.visibleGoods.push(r)}})).exec()}))}))},getFilterList:function(){var e=this;return(0,r.default)((0,o.default)().mark((function t(){var i,n,a,r,u,d,f,p,h,g,v;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.feedback.loading(),i=null,t.next=5,e.$u.api.secondHairFilterList();case 5:i=t.sent,n=i.data.list,a=n.reduce((function(e,t){return(0,s.default)((0,s.default)({},e),{},(0,c.default)({},t.type,[].concat((0,l.default)(e[t.type]||[]),[t])))}),{}),r=a[1],u=void 0===r?[]:r,d=a[2],f=void 0===d?[]:d,p=a[3],h=void 0===p?[]:p,g=a[5],v=void 0===g?[]:g,e.countryList=u,e.wineTypeList=f,e.keywordList=h,e.regionsList=v,e.showScreenPop=!0,t.next=19;break;case 16:t.prev=16,t.t0=t["catch"](0),e.feedback.hideLoading();case 19:case"end":return t.stop()}}),t,null,[[0,16]])})))()},changeSort:function(e){var t=this;return(0,r.default)((0,o.default)().mark((function i(){return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:t.page=1,t.totalPage=0,i.t0=e,i.next=0===i.t0?5:1===i.t0||2===i.t0||3===i.t0||4===i.t0?10:16;break;case 5:return t.filterIndex=e,t.sortType="sort",t.sort="desc",t.getGoodsList(),i.abrupt("break",16);case 10:return t.sortType=t.sortList[e-1].type,1!==e?t.sort="desc":t.filterIndex===e?"desc"==t.sort?t.sort="asc":t.sort="desc":t.sort="asc",t.filterIndex=e,i.next=15,t.getGoodsList();case 15:return i.abrupt("break",16);case 16:case"end":return i.stop()}}),i)})))()},scrollToTop:function(){var e=this;this.$nextTick((function(){e.system.pageScrollTo(0,0)}))},jumpCardDetail:function(e){console.log(e);var t={channel:4,genre:3,region_id:307e3,button_id:e.id};this.$u.api.reportBuryDot({data:[t]}),this.handleJump("".concat(this.routeTable.pgMiaoFaCard,"?cid=").concat(e.id,"&type=card"),"")},getGoodsList:function(){var e=this;return(0,r.default)((0,o.default)().mark((function t(){var i,n,a,r,s,l;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return console.log(e.page),e.feedback.loading(),i={},i.page=e.page,i.limit=e.limit,i.periods_type=[1],i.sort_type=e.sortType,i.order=e.sort,i.filters=e.filterInfo,e.minPrice&&(i.price_gte=parseFloat(e.minPrice)),e.maxPrice&&(i.price_lte=parseFloat(e.maxPrice)),e.minPrice&&e.maxPrice&&i.price_gte>i.price_lte&&(n=[i.price_lte,i.price_gte],i.price_gte=n[0],i.price_lte=n[1]),t.next=14,e.$u.api.flashGoodsList(i);case 14:a=t.sent,r=a.data,s=r.total,l=r.list,1==e.page?e.goodsList=l:e.goodsList=e.mergeUnique(e.goodsList,l),uni.stopPullDownRefresh(),e.totalPage=Math.ceil(s/e.limit),console.log(e.totalPage,s,e.limit),e.loadStatus=e.page!=e.totalPage&&e.totalPage?"loadmore":"nomore",e.feedback.hideLoading();case 22:case"end":return t.stop()}}),t)})))()},mergeUnique:function(e,t){var i=new Map;return e.forEach((function(e){i.set(e.id,e)})),t.forEach((function(e){i.has(e.id)||i.set(e.id,e)})),Array.from(i.values())},confirmFilter:function(){var e=this;return(0,r.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.categoryId||e.countryId||e.keywordId||e.regionsId||e.minPrice||e.maxPrice?e.doFilter=1:e.doFilter=0,e.page=1,e.totalPage=1,t.next=5,e.getGoodsList();case 5:e.showScreenPop=!1;case 6:case"end":return t.stop()}}),t)})))()},getAggregateData:function(){var e=this;this.goldAreaList=[],this.$u.api.secondHomeCard({client:3}).then((function(t){console.warn(t.data);var i=t.data,n=i.column,a=void 0===n?[]:n,o=i.card,r=void 0===o?[]:o;e.goldAreaList=a,e.cardList=r}))},init:function(){var e=this;this.initLoading=!0,this.getAggregateData(),this.getGoodsList(),setTimeout((function(){e.initLoading=!1}),1e3)},resetFilter:function(){var e=this;return(0,r.default)((0,o.default)().mark((function t(){return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.categoryId="",e.product_category=[0],e.countryId="",e.country=[0],e.keywordId="",e.product_keyword=[0],e.regionsId="",e.regions=[0],e.filterInfo={},e.minPrice="",e.maxPrice="";case 11:case"end":return t.stop()}}),t)})))()},selectFilterItem:function(e,t,i){console.log(e,t,i),this[t]==i.id?(this[e]=[0],this[t]="",delete this.filterInfo[e]):(this[e]=[i.name],this[t]=i.id,this.filterInfo[e]=[i.name])},JumpColumn:function(e){var t={channel:4,genre:3,region_id:306e3,button_id:e.id};if(this.$u.api.reportBuryDot({data:[t]}),console.log(e),e.page_mode)this.handleJump("".concat(this.routeTable.pgMiaoFaCard,"?cid=").concat(e.id,"&type=colum"),"");else{var i="";i=this.dev?"https://test-activity.wineyun.com":"https://activity-cdn.vinehoo.com",this.jump.h5Jump(i+e.path,this.$vhFrom,4)}},pullDownRefresh:function(){this.init()}}),onLoad:function(){this.init();var e=this.system.getSysInfo(),t=e.windowHeight,i=e.screenHeight;this.screenPopMaxHeight="".concat(.8*(t||i),"px"),this.secondConfig()},onShow:function(){},onHide:function(){var e,t;null===this||void 0===this||null===(e=this.$refs)||void 0===e||null===(t=e.vhWaterfallRef)||void 0===t||t.stopRender()},onUnload:function(){var e,t;null===this||void 0===this||null===(e=this.$refs)||void 0===e||null===(t=e.vhWaterfallRef)||void 0===t||t.stopRender()},onPullDownRefresh:function(){this.initLoading||this.pullDownRefresh()},onReachBottom:function(){this.page!=this.totalPage&&0!=this.totalPage&&(this.loadStatus="loading",this.page++,this.getGoodsList())}};t.default=_},"6f67":function(e,t,i){"use strict";i("7a82");var n=i("ee27").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(i("f07e")),o=n(i("c964")),r={name:"navMsgMixin",data:function(){return{isGetChannelKeyword:!0,isGetShoppingCartNum:!0,isGetMessageUnreadNum:!0,channelKeyword:"",shoppingCartNum:0,unReadTotalNum:0,channelSection:2}},onShow:function(){this.getShoppingCartNum(),this.getMessageUnreadNum()},onLoad:function(){this.getChannelKeyword()},methods:{getChannelKeyword:function(){var e=this;return(0,o.default)((0,a.default)().mark((function t(){var i,n;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isGetChannelKeyword){t.next=2;break}return t.abrupt("return");case 2:return t.next=4,e.$u.api.channelKeyword({type:e.channelSection});case 4:n=t.sent,e.channelKeyword=(null===n||void 0===n||null===(i=n.data)||void 0===i?void 0:i.keyword)||"大家都在搜";case 6:case"end":return t.stop()}}),t)})))()},getShoppingCartNum:function(){var e=this;return(0,o.default)((0,a.default)().mark((function t(){var i,n;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isGetShoppingCartNum){t.next=2;break}return t.abrupt("return");case 2:if(!e.login.isLogin(e.from,0)){t.next=8;break}return t.next=5,e.$u.api.shoppingCartNum();case 5:i=t.sent,n=(null===i||void 0===i?void 0:i.data)||0,e.shoppingCartNum=n>99?99:n;case 8:case"end":return t.stop()}}),t)})))()},getMessageUnreadNum:function(){var e=this;return(0,o.default)((0,a.default)().mark((function t(){var i,n,o;return(0,a.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.isGetMessageUnreadNum){t.next=2;break}return t.abrupt("return");case 2:if(!e.login.isLogin(e.from,0)){t.next=8;break}return t.next=5,e.$u.api.messageUnreadNum();case 5:n=t.sent,o=(null===n||void 0===n||null===(i=n.data)||void 0===i?void 0:i.total_num)||0,e.unReadTotalNum=o>99?99:o;case 8:case"end":return t.stop()}}),t)})))()}}};t.default=r},7326:function(e,t,i){var n=i("24fb");t=n(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */uni-page-body[data-v-e60e944e]{font-family:PingFang SC,OpenSans,apple-system,BlinkMacSystemFont,Helvetica Neue,Helvetica,Segoe UI,Arial,Roboto,Hiragino Sans GB,Microsoft Yahei,sans-serif;background:#f9f9f9}body.?%PAGE?%[data-v-e60e944e]{background:#f9f9f9}.miaofa-card[data-v-e60e944e]{width:94%;margin:0 auto;padding:%?20?% %?22?%;margin-top:%?20?%}.miaofa-card .card-title[data-v-e60e944e]{font-family:PingFangSC,PingFang SC;font-weight:700;font-size:%?32?%;color:#333;text-align:left;font-style:normal;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;\n  /* 显示两行 */-webkit-box-orient:vertical;line-height:%?42?%;max-height:%?84?%\n  /* 行高 * 行数 */}.miaofa-card .card-price[data-v-e60e944e]{margin:%?20?% 0 %?26?% 0;height:%?44?%;font-family:PingFangSC,PingFang SC;font-weight:450;font-size:%?42?%;color:#e80404;line-height:%?44?%;text-align:left;font-style:normal}.miaofa-card .card-price .price-unit[data-v-e60e944e]{font-family:PingFangSC,PingFang SC;font-weight:500;font-size:%?26?%;color:#e80404;line-height:%?44?%;text-align:left;font-style:normal}.product-title[data-v-e60e944e]{font-family:PingFangSC,PingFang SC;font-weight:400;font-size:%?24?%;color:#333;line-height:%?34?%;text-align:left;font-style:normal;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;\n  /* 显示两行 */-webkit-box-orient:vertical;line-height:%?34?%;\n  /* 根据你的字体和设计调整行高 */max-height:%?68?%}.product-brief[data-v-e60e944e]{font-weight:400;font-size:%?22?%;color:#999;margin-top:%?4?%;line-height:%?32?%;text-align:left;font-style:normal;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:1;\n  /* 显示两行 */-webkit-box-orient:vertical;line-height:%?32?%;\n  /* 根据你的字体和设计调整行高 */max-height:%?32?%}.new-year-glodarea[data-v-e60e944e]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-goldarea.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}.new-year-glodarea-bg[data-v-e60e944e]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-goldarea-bg.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}.new-year-card-bg[data-v-e60e944e]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-card-bg.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}.new-goods-list-bg[data-v-e60e944e]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/goods-list-bg.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}.bg-B21605[data-v-e60e944e]{background-color:#b21605}.card-title-newYear[data-v-e60e944e]{color:#fdf7e5!important}',""]),e.exports=t},"776f":function(e,t,i){"use strict";i.r(t);var n=i("e643"),a=i("e4d5");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("afb6");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"15067509",null,!1,n["a"],void 0);t["default"]=s.exports},7779:function(e,t,i){var n=i("7326");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("86d56470",n,!0,{sourceMap:!1,shadowMode:!1})},"8a04":function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3");var n={name:"u-loadmore",props:{bgColor:{type:String,default:"transparent"},icon:{type:Boolean,default:!0},fontSize:{type:String,default:"28"},color:{type:String,default:"#606266"},status:{type:String,default:"loadmore"},iconType:{type:String,default:"circle"},loadText:{type:Object,default:function(){return{loadmore:"加载更多",loading:"正在加载...",nomore:"没有更多了"}}},isDot:{type:Boolean,default:!1},iconColor:{type:String,default:"#b7b7b7"},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},height:{type:[String,Number],default:"auto"}},data:function(){return{dotText:"●"}},computed:{loadTextStyle:function(){return{color:this.color,fontSize:this.fontSize+"rpx",position:"relative",zIndex:1,backgroundColor:this.bgColor}},cricleStyle:function(){return{borderColor:"#e5e5e5 #e5e5e5 #e5e5e5 ".concat(this.circleColor)}},flowerStyle:function(){return{}},showText:function(){var e="";return e="loadmore"==this.status?this.loadText.loadmore:"loading"==this.status?this.loadText.loading:"nomore"==this.status&&this.isDot?this.dotText:this.loadText.nomore,e}},methods:{loadMore:function(){"loadmore"==this.status&&this.$emit("loadmore")}}};t.default=n},"913e":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){}));var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.show?i("v-uni-view",{staticClass:"u-badge",class:[e.isDot?"u-badge-dot":"","mini"==e.size?"u-badge-mini":"",e.type?"u-badge--bg--"+e.type:""],style:[{top:e.offset[0]+"rpx",right:e.offset[1]+"rpx",fontSize:e.fontSize+"rpx",position:e.absolute?"absolute":"static",color:e.color,backgroundColor:e.bgColor},e.boxStyle]},[e._v(e._s(e.showText))]):e._e()},a=[]},"94c5":function(e,t,i){"use strict";i.r(t);var n=i("e5df"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},a021:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return n}));var n={uIcon:i("e5e1").default,uBadge:i("19d1").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.show?i("v-uni-view",{on:{touchmove:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t),function(){}.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"vh-tabbar__content p-b-safe-area",class:[e.tabbarClass,{"miaofa-bg":e.isMiaofaPage&&e.isNewYear}],style:{height:e.$u.addUnit(e.height)}},e._l(e.displayList,(function(t,n){return i("v-uni-view",{key:n,staticClass:"vh-tabbar__content__item",class:{"vh-tabbar__content__circle":e.midButton&&t.midButton},on:{click:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t),e.clickHandler(n)}}},[i("v-uni-view",{class:[e.midButton&&t.midButton?"vh-tabbar__content__circle__button":"vh-tabbar__content__item__button"]},[i("u-icon",{attrs:{size:t.text?e.midButton&&t.midButton?e.midButtonSize:e.iconSize:84,name:e.elIconPath(n),"img-mode":"scaleToFill",color:e.elColor(n),"custom-prefix":t.customIcon?"custom-icon":"uicon"}}),t.count||t.isDot?i("u-badge",{attrs:{count:t.count,"is-dot":t.isDot,offset:[-2,e.getOffsetRight(t.count,t.isDot)]}}):e._e()],1),i("v-uni-view",{staticClass:"vh-tabbar__content__item__text",style:{color:e.elColor(n)}},[e._v(e._s(t.text))])],1)})),1),i("v-uni-view",{staticClass:"vh-fixed-placeholder p-b-safe-area",style:{height:"calc( "+e.$u.addUnit(e.height)+" + "+(e.midButton?48:0)+"rpx )"}}),e.showPopup?i("v-uni-view",{staticClass:"popup-mask",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup.apply(void 0,arguments)}}}):e._e(),i("v-uni-view",{staticClass:"popup-content",class:{"popup-show":e.showPopup}},[i("v-uni-view",{staticClass:"popup-inner"},[i("v-uni-view",{staticClass:"popup-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleWineReviewClick.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"popup-image",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/post-wine-talk-banner-x2.png",mode:"aspectFill"}})],1),i("v-uni-view",{staticClass:"popup-item",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handlePostClick.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"popup-image",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/post-list-banner-x2.png",mode:"aspectFill"}})],1)],1),i("v-uni-view",{staticClass:"popup-button",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.closePopup.apply(void 0,arguments)}}},[i("v-uni-image",{staticClass:"button-image",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/com-close.png",mode:"aspectFit"}})],1)],1)],1):e._e()},o=[]},a58c:function(e,t,i){"use strict";i.r(t);var n=i("55c2"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},a599:function(e,t,i){"use strict";i.r(t);var n=i("0567"),a=i("c882");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("5b9a");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"e60e944e",null,!1,n["a"],void 0);t["default"]=s.exports},a9e0:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if("undefined"!==typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},i("a4d3"),i("e01a"),i("d3b7"),i("d28b"),i("3ca3"),i("ddb0"),i("a630")},afb6:function(e,t,i){"use strict";var n=i("2da4"),a=i.n(n);a.a},b252:function(e,t,i){var n=i("6ab5");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=i("4f06").default;a("0c30beb4",n,!0,{sourceMap:!1,shadowMode:!1})},c882:function(e,t,i){"use strict";i.r(t);var n=i("6d4e"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},ca79:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={name:"topRefreshMixin",methods:{topRefresh:function(){this.system.pageScrollTo(0,200);var e=setTimeout((function(){e&&clearTimeout(e),uni.startPullDownRefresh()}),300)}}};t.default=n},ce7c:function(e,t,i){"use strict";i.r(t);var n=i("0efb"),a=i("ea26");for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);i("eb5f");var r=i("f0c5"),s=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"89d76102",null,!1,n["a"],void 0);t["default"]=s.exports},cfe0:function(e,t,i){"use strict";var n=i("03d4"),a=i.n(n);a.a},d0ff:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return(0,n.default)(e)||(0,a.default)(e)||(0,o.default)(e)||(0,r.default)()};var n=s(i("4053")),a=s(i("a9e0")),o=s(i("dde1")),r=s(i("10eb"));function s(e){return e&&e.__esModule?e:{default:e}}},d113:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;i("26cb");var n={data:function(){return{tabBarType:1,startupOptionsVisible:!1}},methods:{recordPages:function(e){var t=e.type,i=e.count;if(console.log(this.userIsLogin),console.log(t),console.log(i),this.userIsLogin){var n=uni.getStorageSync("startupOptionsRecord")||"";!n&&i>=2&&(this.tabBarType=t,this.startupOptionsVisible=!0,uni.setStorageSync("startupOptionsRecord",1))}}}};t.default=n},d1d0:function(e,t,i){"use strict";var n=i("3dfa"),a=i.n(n);a.a},e4d5:function(e,t,i){"use strict";i.r(t);var n=i("8a04"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},e5df:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("a9e3");var n={name:"u-badge",props:{type:{type:String,default:"error"},size:{type:String,default:"default"},isDot:{type:Boolean,default:!1},count:{type:[Number,String]},overflowCount:{type:Number,default:99},showZero:{type:Boolean,default:!1},offset:{type:Array,default:function(){return[20,20]}},absolute:{type:Boolean,default:!0},fontSize:{type:[String,Number],default:"24"},color:{type:String,default:"#ffffff"},bgColor:{type:String,default:""},isCenter:{type:Boolean,default:!1}},computed:{boxStyle:function(){var e={};return this.isCenter?(e.top=0,e.right=0,e.transform="translateY(-50%) translateX(50%)"):(e.top=this.offset[0]+"rpx",e.right=this.offset[1]+"rpx",e.transform="translateY(0) translateX(0)"),"mini"==this.size&&(e.transform=e.transform+" scale(0.8)"),e},showText:function(){return this.isDot?"":this.count>this.overflowCount?"".concat(this.overflowCount,"+"):this.count},show:function(){return 0!=this.count||0!=this.showZero}}};t.default=n},e643:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return n}));var n={uLine:i("9ff7").default,uLoading:i("301a").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"u-load-more-wrap",style:{backgroundColor:e.bgColor,marginBottom:e.marginBottom+"rpx",marginTop:e.marginTop+"rpx",height:e.$u.addUnit(e.height)}},[i("u-line",{attrs:{color:"#d4d4d4",length:"50"}}),i("v-uni-view",{staticClass:"u-load-more-inner",class:"loadmore"==e.status||"nomore"==e.status?"u-more":""},[i("v-uni-view",{staticClass:"u-loadmore-icon-wrap"},[i("u-loading",{staticClass:"u-loadmore-icon",attrs:{color:e.iconColor,mode:"circle"==e.iconType?"circle":"flower",show:"loading"==e.status&&e.icon}})],1),i("v-uni-view",{staticClass:"u-line-1",class:["nomore"==e.status&&1==e.isDot?"u-dot-text":"u-more-text"],style:[e.loadTextStyle],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.loadMore.apply(void 0,arguments)}}},[e._v(e._s(e.showText))])],1),i("u-line",{attrs:{color:"#d4d4d4",length:"50"}})],1)},o=[]},ea26:function(e,t,i){"use strict";i.r(t);var n=i("3dc3"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);t["default"]=a.a},eb5f:function(e,t,i){"use strict";var n=i("b252"),a=i.n(n);a.a},f4c6:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={name:"UserMixin",data:function(){return{userIsLogin:!1}}}}}]);