(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-order-deposit-detail-order-deposit-detail~packageB-pages-order-deposit-order-deposit~~18c3eca0"],{"062a":function(e,t,a){var r=a("aab3");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);var o=a("4f06").default;o("2db15654",r,!0,{sourceMap:!1,shadowMode:!1})},"274e":function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3");var r={name:"vh-count-down",props:{plateName:{type:String,default:""},timestamp:{type:[Number,String],default:0},autoplay:{type:Boolean,default:!0},allFontBold:{type:Boolean,default:!1},hasDayMarginRight:{type:Boolean,default:!0},showDays:{type:Boolean,default:!0},showHours:{type:Boolean,default:!0},showMinutes:{type:Boolean,default:!0},showSeconds:{type:Boolean,default:!0},hideZeroDay:{type:Boolean,default:!1},dayColor:{type:String,default:"#E80404"},height:{type:[Number,String],default:"auto"},bgColor:{type:String,default:"#E80404"},showBorder:{type:Boolean,default:!1},borderColor:{type:String,default:"#303133"},hasSeparatorDistance:{type:Boolean,default:!0},separator:{type:String,default:"colon"},separatorColonPadding:{type:String,default:"0 2rpx 4rpx 2rpx"},separatorSize:{type:[Number,String],default:24},separatorColor:{type:String,default:"#E80404"},fontSize:{type:[Number,String],default:24},color:{type:String,default:"#FFFFFF"}},data:function(){return{d:"00",h:"00",i:"00",s:"00",timer:null,seconds:0}},computed:{countDownContainerStyle:function(){var e={};return e.fontWeight=this.allFontBold?"bold":"normal",e},itemContainerStyle:function(){var e={};return this.height&&(e.height=this.height+"rpx",e.width=this.height+"rpx"),this.showBorder&&(e.borderStyle="solid",e.borderColor=this.borderColor,e.borderWidth="1px"),this.bgColor&&(e.backgroundColor=this.bgColor),e},itemStyle:function(){var e={};return e.padding=this.hasSeparatorDistance&&"colon"==this.separator?"0 4rpx 0 4rpx":0,e.fontSize=this.fontSize+"rpx",e.color=this.color,e},separatorStyle:function(e){var t=e.separatorColonPadding,a={};return a.fontSize=this.separatorSize+"rpx",a.color=this.separatorColor,a.padding=this.hasSeparatorDistance&&"colon"==this.separator?t:0,a},letterStyle:function(){var e={};return this.fontSize&&(e.fontSize=this.fontSize+"rpx"),this.color&&(e.color=this.color),e}},watch:{timestamp:function(e,t){this.clearTimer(),this.start()}},mounted:function(){this.autoplay&&this.timestamp&&this.start()},methods:{start:function(){var e=this;this.clearTimer(),this.timestamp<=0||(this.seconds=Number(this.timestamp),this.formatTime(this.seconds),this.timer=setInterval((function(){if(e.seconds--,e.$emit("change",e.seconds<=9?"0"+e.seconds:e.seconds),e.seconds<0)return e.end();e.formatTime(e.seconds)}),1e3))},formatTime:function(e){e<=0&&this.end();var t,a=0,r=0,o=0;a=Math.floor(e/86400),t=Math.floor(e/3600)-24*a;var n=null;n=this.showDays?t:Math.floor(e/3600),r=Math.floor(e/60)-60*t-24*a*60,o=Math.floor(e)-24*a*60*60-60*t*60-60*r,n=n<10?"0"+n:n,r=r<10?"0"+r:r,o=o<10?"0"+o:o,a=a<10?"0"+a:a,this.d=a,this.h=n,this.i=r,this.s=o},end:function(){this.clearTimer(),this.$emit("end",{})},clearTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}},beforeDestroy:function(){clearInterval(this.timer),this.timer=null}};t.default=r},"4f1b":function(e,t,a){"use strict";a.r(t);var r=a("825d"),o=a("8e1d");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);a("fa94");var i=a("f0c5"),s=Object(i["a"])(o["default"],r["b"],r["c"],!1,null,"4ed92bb2",null,!1,r["a"],void 0);t["default"]=s.exports},"825d":function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+e.size,e.plain?"u-btn--"+e.type+"--plain":"",e.loading?"u-loading":"","circle"==e.shape?"u-round-circle":"",e.hairLine?e.showHairLineBorder:"u-btn--bold-border","u-btn--"+e.type,e.disabled?"u-btn--"+e.type+"--disabled":""],style:[e.customStyle,{overflow:e.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(e.hoverStartTime),"hover-stay-time":Number(e.hoverStayTime),disabled:e.disabled,"form-type":e.formType,"open-type":e.openType,"app-parameter":e.appParameter,"hover-stop-propagation":e.hoverStopPropagation,"send-message-title":e.sendMessageTitle,"send-message-path":"sendMessagePath",lang:e.lang,"data-name":e.dataName,"session-from":e.sessionFrom,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"hover-class":e.getHoverClass,loading:e.loading},on:{getphonenumber:function(t){arguments[0]=t=e.$handleEvent(t),e.getphonenumber.apply(void 0,arguments)},getuserinfo:function(t){arguments[0]=t=e.$handleEvent(t),e.getuserinfo.apply(void 0,arguments)},error:function(t){arguments[0]=t=e.$handleEvent(t),e.error.apply(void 0,arguments)},opensetting:function(t){arguments[0]=t=e.$handleEvent(t),e.opensetting.apply(void 0,arguments)},launchapp:function(t){arguments[0]=t=e.$handleEvent(t),e.launchapp.apply(void 0,arguments)},click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.click(t)}}},[e._t("default"),e.ripple?a("v-uni-view",{staticClass:"u-wave-ripple",class:[e.waveActive?"u-wave-active":""],style:{top:e.rippleTop+"px",left:e.rippleLeft+"px",width:e.fields.targetWidth+"px",height:e.fields.targetWidth+"px","background-color":e.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):e._e()],2)},o=[]},"846f":function(e,t,a){"use strict";a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){}));var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{},["miaofaPeoplePlate"===e.plateName?a("v-uni-view",{staticClass:"flex-c-c-c"},[a("v-uni-view",{staticClass:"font-18 w-s-now text-ffffff"},[e._v("距离失效还剩"+e._s(e.d)+"天")]),a("v-uni-view",{staticClass:"flex-c-c mt-04"},[a("v-uni-view",{staticClass:"d-flex a-center"},[a("v-uni-view",{staticClass:"w-28 h-28 bg-fff6e4 flex-c-c b-rad-04 font-16 text-3"},[e._v(e._s(e.h))]),a("v-uni-view",{staticClass:"w-18 flex-c-c text-ffffff"},[e._v(":")])],1),a("v-uni-view",{staticClass:"d-flex a-center"},[a("v-uni-view",{staticClass:"w-28 h-28 bg-fff6e4 flex-c-c b-rad-04 font-16 text-3"},[e._v(e._s(e.i))]),a("v-uni-view",{staticClass:"w-18 flex-c-c text-ffffff"},[e._v(":")])],1),a("v-uni-view",{staticClass:"d-flex a-center"},[a("v-uni-view",{staticClass:"w-28 h-28 bg-fff6e4 flex-c-c b-rad-04 font-16 text-3"},[e._v(e._s(e.s))])],1)],1)],1):a("v-uni-view",{staticClass:"d-flex j-center a-center",style:[e.countDownContainerStyle]},[e.showDays?a("v-uni-view",{staticClass:"d-flex a-center",class:e.hasDayMarginRight?"mr-06":""},[(e.hideZeroDay||!e.hideZeroDay&&e.d,a("v-uni-view",{staticClass:"d-flex j-center a-center b-rad-06 p-02 w-s-now"},[a("v-uni-view",{staticClass:"m-0 p-0",style:{fontSize:e.fontSize+"rpx",color:e.dayColor}},[e._v(e._s(e.d)+"天")])],1))],1):e._e(),e.showHours?a("v-uni-view",{staticClass:"d-flex a-center"},[a("v-uni-view",{staticClass:"d-flex j-center a-center b-rad-06 p-02 w-s-now",style:[e.itemContainerStyle]},[a("v-uni-view",{staticClass:"m-0",style:[e.itemStyle]},[e._v(e._s(e.h))])],1),a("v-uni-view",{staticClass:"d-flex j-center a-center ptb-00-plr-02 pb-04",style:[e.separatorStyle]},[e._v(e._s("colon"==e.separator?":":"小时"))])],1):e._e(),e.showMinutes?a("v-uni-view",{staticClass:"d-flex a-center"},[a("v-uni-view",{staticClass:"d-flex j-center a-center b-rad-06 p-02 w-s-now",style:[e.itemContainerStyle]},[a("v-uni-view",{staticClass:"m-0",style:[e.itemStyle]},[e._v(e._s(e.i))])],1),a("v-uni-view",{staticClass:"d-flex j-center a-center pb-04",style:[e.separatorStyle]},[e._v(e._s("colon"==e.separator?":":"分"))])],1):e._e(),e.showSeconds?a("v-uni-view",{staticClass:"d-flex a-center"},[a("v-uni-view",{staticClass:"d-flex j-center a-center b-rad-06 p-02 w-s-now",style:[e.itemContainerStyle]},[a("v-uni-view",{staticClass:"m-0",style:[e.itemStyle]},[e._v(e._s(e.s))])],1),a("v-uni-view",{staticClass:"d-flex j-center a-center ptb-00-plr-02 pb-04",style:[e.separatorStyle]},[e._v(e._s("colon"==e.separator?"":"秒"))])],1):e._e()],1)],1)},o=[]},"8e1d":function(e,t,a){"use strict";a.r(t);var r=a("9476"),o=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=o.a},9476:function(e,t,a){"use strict";a("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("a9e3"),a("c975"),a("d3b7"),a("ac1f");var r={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var e;return e=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",e},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(e){var t=this;this.$u.throttle((function(){!0!==t.loading&&!0!==t.disabled&&(t.ripple&&(t.waveActive=!1,t.$nextTick((function(){this.getWaveQuery(e)}))),t.$emit("click",e))}),this.throttleTime)},getWaveQuery:function(e){var t=this;this.getElQuery().then((function(a){var r=a[0];if(r.width&&r.width&&(r.targetWidth=r.height>r.width?r.height:r.width,r.targetWidth)){t.fields=r;var o,n;o=e.touches[0].clientX,n=e.touches[0].clientY,t.rippleTop=n-r.top-r.targetWidth/2,t.rippleLeft=o-r.left-r.targetWidth/2,t.$nextTick((function(){t.waveActive=!0}))}}))},getElQuery:function(){var e=this;return new Promise((function(t){var a="";a=uni.createSelectorQuery().in(e),a.select(".u-btn").boundingClientRect(),a.exec((function(e){t(e)}))}))},getphonenumber:function(e){this.$emit("getphonenumber",e)},getuserinfo:function(e){this.$emit("getuserinfo",e)},error:function(e){this.$emit("error",e)},opensetting:function(e){this.$emit("opensetting",e)},launchapp:function(e){this.$emit("launchapp",e)}}};t.default=r},aab3:function(e,t,a){var r=a("24fb");t=r(!1),t.push([e.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),e.exports=t},e7b7:function(e,t,a){"use strict";a.r(t);var r=a("846f"),o=a("f94f");for(var n in o)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return o[e]}))}(n);var i=a("f0c5"),s=Object(i["a"])(o["default"],r["b"],r["c"],!1,null,"5cf4d69f",null,!1,r["a"],void 0);t["default"]=s.exports},f94f:function(e,t,a){"use strict";a.r(t);var r=a("274e"),o=a.n(r);for(var n in r)["default"].indexOf(n)<0&&function(e){a.d(t,e,(function(){return r[e]}))}(n);t["default"]=o.a},fa94:function(e,t,a){"use strict";var r=a("062a"),o=a.n(r);o.a}}]);