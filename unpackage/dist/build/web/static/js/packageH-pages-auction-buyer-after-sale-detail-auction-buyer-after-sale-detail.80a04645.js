(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageH-pages-auction-buyer-after-sale-detail-auction-buyer-after-sale-detail"],{"0481":function(t,e,n){"use strict";var a=n("23e7"),i=n("a2bf"),r=n("7b0b"),o=n("07fa"),s=n("5926"),c=n("65f0");a({target:"Array",proto:!0},{flat:function(){var t=arguments.length?arguments[0]:void 0,e=r(this),n=o(e),a=c(e,0);return a.length=i(a,e,e,n,0,void 0===t?1:s(t)),a}})},"062a":function(t,e,n){var a=n("aab3");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("2db15654",a,!0,{sourceMap:!1,shadowMode:!1})},"06b6":function(t,e,n){"use strict";n.r(e);var a=n("bca1"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"12c6":function(t,e,n){"use strict";n.r(e);var a=n("51bd"),i=n("f074");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("f2f9");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);e["default"]=s.exports},"24bc":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={vhNavbar:n("12c6").default,vhImage:n("ce7c").default,ActionAfterSaleOrderDetail:n("3667").default,uIcon:n("e5e1").default,AuctionAfterSaleDetail:n("45ae").default,uButton:n("4f1b").default,uPopup:n("c4b0").default,vhSkeleton:n("591b").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"content pb-124",class:t.loading?"h-vh-100 o-hid":""},[n("vh-navbar",{attrs:{title:"售后详情"}}),t.loading?n("vh-skeleton",{attrs:{bgColor:"#FFF",showLoading:!1}}):n("v-uni-view",{staticClass:"fade-in"},[n("v-uni-view",{staticClass:"bg-ffffff b-rad-10 o-hid mt-20 ml-24 mr-24"},[n("v-uni-view",{staticClass:"w-p100 h-222 bg-li-31 flex-sb-c pl-24 pr-48"},[n("v-uni-view",{},[n("v-uni-view",{staticClass:"font-36 font-wei text-ffffff"},[t._v(t._s(t.afterSaleStatus[t.afterSaleInfo.status].text))]),n("v-uni-view",{staticClass:"mt-14 font-24 text-ffffff"},[t._v(t._s(t.afterSaleStatus[t.afterSaleInfo.status].subText))])],1),n("vh-image",{attrs:{"loading-type":2,src:t.ossIcon("/auction_buyer_after_sale_detail/icon"+t.afterSaleInfo.status+".png"),width:112,height:112}})],1),n("ActionAfterSaleOrderDetail",{attrs:{afterSaleInfo:t.afterSaleInfo}})],1),t.showPickUpInfo?n("v-uni-view",{staticClass:"bg-ffffff b-rad-10 o-hid mt-20 ml-24 mr-24 ptb-00-plr-24"},[n("v-uni-view",{staticClass:"flex-sb-c bb-s-01-f8f8f8 pt-44 pb-32"},[n("v-uni-view",{staticClass:"font-28 font-wei text-3"},[t._v("选择退货方式")]),n("v-uni-view",{staticClass:"font-28 text-3"},[t._v("上门取件")])],1),n("v-uni-view",{staticClass:"flex-sb-c bb-s-01-f8f8f8 ptb-32-plr-00",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.navigateTo(t.routeTable.pEAddressManagement+"?comeFrom=7")}}},[n("v-uni-view",{staticClass:"font-30 text-3"},[t._v("取件地址")]),n("v-uni-view",{staticClass:"flex-c-c"},[n("v-uni-view",{staticClass:"w-474 mr-16"},[n("v-uni-view",{staticClass:"text-right"},[t._v(t._s(t.orderInfo.province_name)+" "+t._s(t.orderInfo.city_name)+" "+t._s(t.orderInfo.district_name)+" "+t._s(t.orderInfo.address))]),n("v-uni-view",{staticClass:"text-right font-24 text-9"},[t._v(t._s(t.orderInfo.consignee)+" "+t._s(t.orderInfo.consignee_phone))])],1),n("u-icon",{attrs:{name:"arrow-right",size:24,color:"#333"}})],1)],1),n("v-uni-view",{staticClass:"flex-sb-c ptb-32-plr-00",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openPickUpTimePopup.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"font-30 text-3"},[t._v("上门时间")]),n("v-uni-view",{staticClass:"flex-c-c"},[n("v-uni-view",{staticClass:"mr-16 font-30 text-3"},[t._v(t._s(t.findPickUpDate.day+" "+t.findPickUpDate.timeRange))]),n("u-icon",{attrs:{name:"arrow-right",size:24,color:"#333"}})],1)],1)],1):t._e(),n("AuctionAfterSaleDetail",{attrs:{afterSaleInfo:t.afterSaleInfo}}),n("v-uni-view",{staticClass:"p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff flex-e-c b-sh-00021200-022 flex-c-c ptb-00-plr-24"},[t.showPickUpInfo?n("v-uni-view",{staticClass:"flex-e-c"},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",backgroundColor:"#E80404",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submitPickupInfo.apply(void 0,arguments)}}},[t._v("提交")])],1):n("v-uni-view",{staticClass:"flex-e-c"},[n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#666",border:"1rpx solid #666"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.navigateTo(t.routeTable.pHAuctionAfterSaleProgress+"?refundOrderNo="+t.afterSaleInfo.refund_order_no)}}},[t._v("售后进度")])],1),[0].includes(t.afterSaleInfo.status)?n("v-uni-view",{staticClass:"ml-20"},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#E80404",border:"1rpx solid #E80404"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.reminderHandle.apply(void 0,arguments)}}},[t._v("提醒处理")])],1):t._e(),[1].includes(t.afterSaleInfo.status)?n("v-uni-view",{staticClass:"ml-20"},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"208rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#E80404",border:"1rpx solid #E80404"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.showDeliveryMethodPop=!0}}},[t._v("去寄件")])],1):t._e()],1)],1),n("v-uni-view",{},[n("u-popup",{attrs:{mode:"bottom",height:"588","border-radius":20},model:{value:t.showDeliveryMethodPop,callback:function(e){t.showDeliveryMethodPop=e},expression:"showDeliveryMethodPop"}},[n("v-uni-view",{},[n("v-uni-view",{staticClass:"ptb-00-plr-48"},[n("v-uni-view",{staticClass:"mt-36 text-center font-36 font-wei text-3"},[t._v("寄件方式")]),n("v-uni-view",{staticClass:"mt-60"},[n("v-uni-view",{staticClass:"font-28 font-wei text-3"},[t._v("上门取件")]),n("v-uni-view",{staticClass:"mt-10 font-24 text-ff9127 l-h-34"},[t._v("请选择上门取件时间，我们将为您上门取件，可随时修改或取消时间")]),n("v-uni-view",{staticClass:"mt-24"},[t.pickUpDateList.length?n("v-uni-view",{staticClass:"flex-sb-c"},[n("v-uni-view",{staticClass:"d-flex"},t._l(t.pickUpDateList.slice(0,t.pickUpDateShowNum),(function(e,a){return n("v-uni-view",{key:a,staticClass:"w-260 h-94 d-flex flex-column j-center a-center b-rad-10 font-24 text-9 l-h-40 mr-20",class:t.pickUpDateId===e.pickUpDateId?"bg-ffffff b-s-02-ff9127 text-ff9127":"bg-f7f7f7",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.pickUpDateId=e.pickUpDateId}}},[n("v-uni-text",[t._v(t._s(e.day))]),n("v-uni-text",[t._v(t._s(e.timeRange))])],1)})),1),t.pickUpDateList.length>t.pickUpDateShowNum?n("v-uni-view",{staticClass:"d-flex a-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openPickUpTimePopup.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"mr-10 font-28 text-9 l-h-34"},[t._v("更多")]),n("u-icon",{attrs:{name:"arrow-right",size:24,color:"#999"}})],1):t._e()],1):n("v-uni-view",{staticClass:"font-28 text-9"},[t._v("暂无上门取件时间")])],1)],1)],1),t.pickUpDateList.length?n("v-uni-view",{staticClass:"p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff flex-c-c b-sh-00021200-022"},[n("u-button",{attrs:{disabled:!t.pickUpDateId,shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"64rpx",backgroundColor:t.pickUpDateId?"#E80404":"#FCE4E3",fontSize:"28rpx",fontWeight:"bold",color:"#FFF"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmDeliveryMethod.apply(void 0,arguments)}}},[t._v("确认")])],1):t._e()],1)],1),n("u-popup",{attrs:{mode:"bottom",height:"614","border-radius":20},model:{value:t.showPickerDatePop,callback:function(e){t.showPickerDatePop=e},expression:"showPickerDatePop"}},[n("v-uni-view",{staticClass:"d-flex j-center a-center h-122 font-wei-600 font-36 text-3"},[t._v("选择上门时间段")]),n("v-uni-view",{staticClass:"h-388"},[n("v-uni-picker-view",{staticClass:"h-p100",attrs:{"indicator-class":"picker-view-padding",value:t.pickUpDatePicker},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.handlePickerChange.apply(void 0,arguments)}}},[n("v-uni-picker-view-column",t._l(t.pickUpDayList,(function(e,a){return n("v-uni-view",{key:a,staticClass:"d-flex j-center a-center ptb-00-plr-08 font-32 text-3"},[n("v-uni-view",{staticClass:"o-hid w-s-now t-o-ell"},[t._v(t._s(e))])],1)})),1),n("v-uni-picker-view-column",t._l(t.pickUpTimeList,(function(e,a){return n("v-uni-view",{key:a,staticClass:"d-flex j-center a-center ptb-00-plr-08 font-32 text-3"},[n("v-uni-view",{staticClass:"o-hid w-s-now t-o-ell"},[t._v(t._s(e))])],1)})),1)],1)],1),n("v-uni-view",{staticClass:"d-flex j-center a-center h-104 b-sh-00021200-022",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirmPickerDate.apply(void 0,arguments)}}},[n("v-uni-button",{staticClass:"d-flex j-center a-center w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32"},[t._v(t._s(t.pickUpPickerBtnText))])],1)],1)],1)],1)],1)},r=[]},"2f28":function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,"uni-page-body[data-v-0c60192a]{background-color:#f5f5f5}body.?%PAGE?%[data-v-0c60192a]{background-color:#f5f5f5}",""]),t.exports=e},3667:function(t,e,n){"use strict";n.r(e);var a=n("d5d0"),i=n("8eb1");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"f99db442",null,!1,a["a"],void 0);e["default"]=s.exports},"38f1":function(t,e,n){"use strict";var a=n("d373"),i=n.n(a);i.a},4069:function(t,e,n){"use strict";var a=n("44d2");a("flat")},"45ae":function(t,e,n){"use strict";n.r(e);var a=n("aab7"),i=n("06b6");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"1fee2ea5",null,!1,a["a"],void 0);e["default"]=s.exports},"4f1b":function(t,e,n){"use strict";n.r(e);var a=n("825d"),i=n("8e1d");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("fa94");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"4ed92bb2",null,!1,a["a"],void 0);e["default"]=s.exports},"51bd":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[n("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},r=[]},"7f1a":function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var r=n("26cb"),o=uni.getSystemInfoSync(),s={},c={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,r.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,n=e.pEAddressAdd,a=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(n)||t.includes(a)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=c},"7fad":function(t,e,n){"use strict";var a=n("c473"),i=n.n(a);i.a},"825d":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?n("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},i=[]},"8e1d":function(t,e,n){"use strict";n.r(e);var a=n("9476"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"8eb1":function(t,e,n){"use strict";n.r(e);var a=n("e51b3"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},9476:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("c975"),n("d3b7"),n("ac1f");var a={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(n){var a=n[0];if(a.width&&a.width&&(a.targetWidth=a.height>a.width?a.height:a.width,a.targetWidth)){e.fields=a;var i,r;i=t.touches[0].clientX,r=t.touches[0].clientY,e.rippleTop=r-a.top-a.targetWidth/2,e.rippleLeft=i-a.left-a.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var n="";n=uni.createSelectorQuery().in(t),n.select(".u-btn").boundingClientRect(),n.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=a},a126:function(t,e,n){var a=n("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},a2bf:function(t,e,n){"use strict";var a=n("e8b5"),i=n("07fa"),r=n("3511"),o=n("0366"),s=function(t,e,n,c,l,f,u,d){var p,v,b=l,h=0,g=!!u&&o(u,d);while(h<c)h in n&&(p=g?g(n[h],h,e):n[h],f>0&&a(p)?(v=i(p),b=s(t,e,p,v,b,f-1)-1):(r(b+1),t[b]=p),b++),h++;return b};t.exports=s},aab3:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},aab7:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={vhImage:n("ce7c").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"bg-ffffff b-rad-10 o-hid mt-20 ml-24 mr-24 ptb-00-plr-20"},[n("v-uni-view",{staticClass:"flex-sb-c bt-s-01-eeeeee ptb-32-plr-00"},[n("v-uni-text",{staticClass:"font-28 font-wei text-6"},[t._v("发起时间")]),n("v-uni-text",{staticClass:"font-28 text-3"},[t._v(t._s(t.afterSaleInfo.created_time))])],1),n("v-uni-view",{staticClass:"flex-sb-c bt-s-01-eeeeee ptb-32-plr-00"},[n("v-uni-text",{staticClass:"font-28 font-wei text-6"},[t._v("售后类型")]),n("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v(t._s(0===t.afterSaleInfo.service_type?"仅退款":"退货退款"))])],1),t.afterSaleInfo.refund_time?n("v-uni-view",{staticClass:"flex-sb-c bt-s-01-eeeeee ptb-32-plr-00"},[n("v-uni-text",{staticClass:"font-28 font-wei text-6"},[t._v("退款时间")]),n("v-uni-text",{staticClass:"font-28 text-3"},[t._v(t._s(t.afterSaleInfo.refund_time))])],1):t._e(),n("v-uni-view",{staticClass:"flex-sb-c bt-s-01-eeeeee ptb-32-plr-00"},[n("v-uni-text",{staticClass:"font-28 font-wei text-6"},[t._v("退回方向")]),n("v-uni-text",{staticClass:"font-28 text-3"},[t._v("原路退回")])],1),n("v-uni-view",{staticClass:"flex-sb-c bt-s-01-eeeeee ptb-32-plr-00"},[n("v-uni-view",{staticClass:"font-28 font-wei text-6"},[t._v("退货原因")]),n("v-uni-view",{staticClass:"w-480 flex-e-c font-28 text-3"},[t._v(t._s(t.afterSaleInfo.refund_reason))])],1),t.afterSaleInfo.describe?n("v-uni-view",{staticClass:"bt-s-01-eeeeee ptb-32-plr-00"},[n("v-uni-view",{staticClass:"font-28 font-wei text-6"},[t._v("退款描述")]),n("v-uni-view",{staticClass:"bg-f6f6f6 b-rad-10 mt-20 p-24"},[t._v(t._s(t.afterSaleInfo.describe))])],1):t._e(),t.afterSaleInfo.voucher.length?n("v-uni-view",{staticClass:"pb-32"},[n("v-uni-view",{staticClass:"font-28 font-wei text-3"},[t._v("图片")]),n("v-uni-view",{staticClass:"d-flex flex-nowrap a-center"},t._l(t.afterSaleInfo.voucher,(function(e,a){return n("v-uni-view",{key:a,staticClass:"w-210 h-210 b-rad-10 mt-20 mr-16",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.image.previewImageList(t.afterSaleInfo.voucher,a)}}},[n("vh-image",{attrs:{"loading-type":2,src:e,width:210,height:210,"border-radius":10}})],1)})),1)],1):t._e()],1)},r=[]},b3b9:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */[data-v-0c60192a] .picker-view-padding{padding:%?10?% 0}',""]),t.exports=e},bbdc:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},bca1:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var a={name:"AuctionAfterSaleDetail",props:{type:{type:[Number,String],default:0},afterSaleInfo:{type:Object,default:function(){return{}}}}};e.default=a},c473:function(t,e,n){var a=n("b3b9");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("e4905d2a",a,!0,{sourceMap:!1,shadowMode:!1})},ca25:function(t,e,n){"use strict";n.r(e);var a=n("d1b6"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},d1b6:function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("caad6"),n("7db0"),n("d3b7"),n("d81d"),n("99af"),n("b64b"),n("4de4"),n("0481"),n("4069"),n("c740");var i=a(n("f07e")),r=a(n("c964")),o=a(n("d0af")),s=a(n("f3f3")),c=n("26cb"),l={name:"auction-buyer-after-sale-detail",data:function(){return{loading:!0,refundOrderNo:"",toSend:"",afterSaleInfo:{},orderInfo:{},pickUpDateId:"",pickUpDate:[],pickUpDateList:[],pickUpDateShowNum:2,pickUpDatePicker:[0,0],pickUpDateListLoading:!1,showDeliveryMethodPop:!1,showPickerDatePop:!1,confirmPickupInfo:!1}},computed:(0,s.default)((0,s.default)({},(0,c.mapState)(["routeTable","addressInfoState"])),{},{afterSaleStatus:function(){return{0:{text:"卖家待处理"},1:{text:"卖家同意退货"},2:{text:"待卖家收货"},3:{text:"退款成功"},5:{text:"卖家拒绝退款",subText:"不符合退款条件"},6:{text:"卖家拒绝收货",subText:"已同意退货"}}},showPickUpInfo:function(){return!(!([1].includes(this.afterSaleInfo.status)&&this.pickUpDateId&&this.confirmPickupInfo)||this.pickUpDateListLoading)},findPickUpDate:function(t){var e=t.pickUpDateId,n=t.pickUpDateList;return n.find((function(t){return t.pickUpDateId===e}))},pickUpDayList:function(t){var e=t.pickUpDate;return e.map((function(t){return t.day}))},pickUpTimeList:function(t){var e=t.pickUpDate,n=t.pickUpDatePicker,a=e[n[0]];return a?a.timeList.map((function(t){return t.timeRange})):[]},pickUpPickerBtnText:function(t){var e=t.pickUpDayList,n=t.pickUpTimeList,a=t.pickUpDatePicker,i=(0,o.default)(a,2),r=i[0],s=i[1];return"".concat(e[r]," ").concat(n[s])}}),onLoad:function(t){var e=this;this.refundOrderNo=t.refundOrderNo,t.toSend&&(this.toSend=t.toSend),this.login.isLoginV3(this.$vhFrom).then((function(t){t&&e.initOnLoad()}))},onShow:function(){this.initOnShow()},methods:{initOnLoad:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.getAfterSaleDetail();case 3:return e.next=5,t.getOrderDetail();case 5:return e.next=7,t.getPickupPeriod();case 7:t.loading=!1,e.next=12;break;case 10:e.prev=10,e.t0=e["catch"](0);case 12:case"end":return e.stop()}}),e,null,[[0,10]])})))()},initOnShow:function(){this.changeAddress()},changeAddress:function(){if(Object.keys(this.addressInfoState).length&&Object.keys(this.afterSaleInfo).length&&1===this.afterSaleInfo.status){var t=this.addressInfoState,e=t.province_id,n=t.province_name,a=t.city_id,i=t.city_name,r=t.town_id,o=t.town_name,s=t.address,c=t.consignee,l=t.consignee_phone;this.orderInfo.province_id=e,this.orderInfo.province_name=n,this.orderInfo.city_id=a,this.orderInfo.city_name=i,this.orderInfo.district_id=r,this.orderInfo.district_name=o,this.orderInfo.address=s,this.orderInfo.consignee=c,this.orderInfo.consignee_phone=l}},getAfterSaleDetail:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.auctionAfterSaleDetail({refund_order_no:t.refundOrderNo});case 2:n=e.sent,t.afterSaleInfo=n.data;case 4:case"end":return e.stop()}}),e)})))()},getOrderDetail:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.auctionOrderDetail({order_no:t.afterSaleInfo.order_no});case 2:n=e.sent,t.orderInfo=n.data;case 4:case"end":return e.stop()}}),e)})))()},getPickupPeriod:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var n,a,r,o,s,c,l,f,u;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(e.prev=0,!Object.keys(t.orderInfo).length||!Object.keys(t.afterSaleInfo).length||1!==t.afterSaleInfo.status){e.next=23;break}return t.pickUpDateListLoading=!0,n=t.orderInfo,a=n.order_no,r=n.province_name,o=n.city_name,s=n.district_name,c=n.address,l={},l.type=0,l.order_no=a,l.senderProvince=r,l.senderCity=o,l.senderDistrict=s,l.senderDetailAddress=c,e.next=13,t.$u.api.auctionPickupPeriod(l);case 13:if(f=e.sent,f.data.length){e.next=16;break}return e.abrupt("return");case 16:u=f.data,t.pickUpDate=u.filter((function(t){return t.timeList.length})),t.pickUpDateList=u.map((function(t){var e=t.day;return t.timeList.map((function(t){var n=t.startTime,a=t.endTime,i=t.timeRange;return{pickUpDateId:"".concat(e,"&").concat(i),timeRange:i,day:e,startTime:n,endTime:a}}))})).flat(),t.pickUpDateList.some((function(e){return e.pickUpDateId===t.pickUpDateId}))||(t.pickUpDateId=""),t.pickUpDateListLoading=!1,t.toSend&&(t.showDeliveryMethodPop=!0),console.log(f);case 23:e.next=27;break;case 25:e.prev=25,e.t0=e["catch"](0);case 27:case"end":return e.stop()}}),e,null,[[0,25]])})))()},reminderHandle:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$u.api.auctionRemindNotice({order_no:t.afterSaleInfo.order_no,type:2});case 3:t.feedback.toast({title:"提醒处理成功~"}),e.next=8;break;case 6:e.prev=6,e.t0=e["catch"](0);case 8:case"end":return e.stop()}}),e,null,[[0,6]])})))()},openPickUpTimePopup:function(){if(this.pickUpDateList.length){if(this.pickUpDateId){var t=this.pickUpDateId.split("&"),e=(0,o.default)(t,2),n=e[0],a=e[1],i=this.pickUpDayList.findIndex((function(t){return t===n})),r=this.pickUpTimeList.findIndex((function(t){return t===a}));this.pickUpDatePicker=[i,r]}this.showPickerDatePop=!0}},handlePickerChange:function(t){this.pickUpDatePicker=t.detail.value},confirmDeliveryMethod:function(){console.log("============我是确认寄件方式"),this.afterSaleStatus[this.afterSaleInfo.status].text="请确认上门取件信息",this.confirmPickupInfo=!0,this.showDeliveryMethodPop=!1},confirmPickerDate:function(){console.log("============我是确认确认选择时间"),this.afterSaleStatus[this.afterSaleInfo.status].text="请确认上门取件信息";var t=(0,o.default)(this.pickUpDatePicker,2),e=t[0],n=t[1];this.pickUpDateId="".concat(this.pickUpDayList[e],"&").concat(this.pickUpTimeList[n]),this.confirmPickupInfo=!0,this.showDeliveryMethodPop=!1,this.showPickerDatePop=!1},submitPickupInfo:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var n,a,r,o,s,c,l,f,u,d,p,v,b,h,g,m;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,n=t.orderInfo,a=n.order_no,r=n.province_name,o=n.province_id,s=n.city_name,c=n.city_id,l=n.district_name,f=n.district_id,u=n.address,d=n.consignee,p=n.consignee_phone,v=t.pickUpDateList.find((function(e){return e.pickUpDateId===t.pickUpDateId})),b=v.day,h=v.startTime,g=v.endTime,m={},m.type=1,m.order_no=a,m.senderProvince=r,m.senderProvinceId=o,m.senderCity=s,m.senderCityId=c,m.senderDistrict=l,m.senderDistrictId=f,m.senderDetailAddress=u,m.consignee=d,m.consignee_phone=p,m.pick_up_start_time="".concat(b," ").concat(h),m.pick_up_end_time="".concat(b," ").concat(g),console.log(m),e.next=20,t.$u.api.auctionSubmitPickupInfo(m);case 20:t.getAfterSaleDetail(),e.next=25;break;case 23:e.prev=23,e.t0=e["catch"](0);case 25:case"end":return e.stop()}}),e,null,[[0,23]])})))()}}};e.default=l},d373:function(t,e,n){var a=n("2f28");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("5e3313c5",a,!0,{sourceMap:!1,shadowMode:!1})},d5d0:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"ptb-32-plr-24"},[n("v-uni-view",{staticClass:"flex-sb-c"},[n("v-uni-text",{staticClass:"font-32 font-wei text-6"},[t._v("订单编号：")]),n("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.copy.copyText(t.afterSaleInfo.order_no)}}},[n("v-uni-text",{staticClass:"font-24 text-6"},[t._v(t._s(t.afterSaleInfo.order_no))]),n("v-uni-text",{staticClass:"bg-eeeeee b-rad-18 ml-10 ptb-02-plr-10 font-18 text-3"},[t._v("复制")])],1)],1),n("v-uni-view",{staticClass:"flex-sb-c mt-20"},[n("v-uni-text",{staticClass:"font-32 font-wei text-6"},[t._v("退款金额")]),n("v-uni-text",{staticClass:"font-32 font-wei text-e80404"},[n("v-uni-text",{staticClass:"font-22"},[t._v("¥")]),t._v(t._s(t.afterSaleInfo.refund_money))],1)],1),t.afterSaleInfo.express_number?n("v-uni-view",{staticClass:"flex-sb-c mt-20"},[n("v-uni-text",{staticClass:"font-32 font-wei text-6"},[t._v("快递编号：")]),n("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.copy.copyText(t.afterSaleInfo.express_number)}}},[n("v-uni-text",{staticClass:"font-24 text-6"},[t._v(t._s(t.afterSaleInfo.express_number))]),n("v-uni-text",{staticClass:"bg-eeeeee b-rad-18 ml-10 ptb-02-plr-10 font-18 text-3"},[t._v("复制")])],1)],1):t._e()],1)},i=[]},e51b3:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var a={name:"ActionAfterSaleOrderDetail",props:{type:{type:[Number,String],default:0},afterSaleInfo:{type:Object,default:function(){return{}}}}};e.default=a},eaeb:function(t,e,n){"use strict";n.r(e);var a=n("24bc"),i=n("ca25");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("7fad"),n("38f1");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"0c60192a",null,!1,a["a"],void 0);e["default"]=s.exports},f074:function(t,e,n){"use strict";n.r(e);var a=n("7f1a"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},f2f9:function(t,e,n){"use strict";var a=n("a126"),i=n.n(a);i.a},fa94:function(t,e,n){"use strict";var a=n("062a"),i=n.n(a);i.a}}]);