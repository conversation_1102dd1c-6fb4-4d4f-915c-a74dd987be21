(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageG-pages-large-turntable-large-turntable"],{"00d9":function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-model[data-v-acf792f8]{height:auto;overflow:hidden;font-size:%?32?%;background-color:#fff}.u-model__btn--hover[data-v-acf792f8]{background-color:#e6e6e6}.u-model__title[data-v-acf792f8]{padding-top:%?48?%;font-weight:500;text-align:center;color:#303133}.u-model__content__message[data-v-acf792f8]{padding:%?48?%;font-size:%?30?%;text-align:center;color:#606266}.u-model__footer[data-v-acf792f8]{display:flex;flex-direction:row}.u-model__footer__button[data-v-acf792f8]{flex:1;height:%?100?%;line-height:%?100?%;font-size:%?32?%;box-sizing:border-box;cursor:pointer;text-align:center;border-radius:%?4?%}',""]),t.exports=e},"12c6":function(t,e,n){"use strict";n.r(e);var a=n("51bd"),i=n("f074");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("f2f9");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"519170ec",null,!1,a["a"],void 0);e["default"]=s.exports},"25f4":function(t,e,n){"use strict";n.r(e);var a=n("e20d"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},2713:function(t,e,n){var a=n("d10c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("a22853d4",a,!0,{sourceMap:!1,shadowMode:!1})},"2d58":function(t,e,n){"use strict";n.r(e);var a=n("ace5"),i=n("25f4");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("7ca1");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"44bf2aa8",null,!1,a["a"],void 0);e["default"]=s.exports},"430f":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={uPopup:n("c4b0").default,uLoading:n("301a").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",[n("u-popup",{attrs:{zoom:t.zoom,mode:"center",popup:!1,"z-index":t.uZIndex,length:t.width,"mask-close-able":t.maskCloseAble,"border-radius":t.borderRadius,"negative-top":t.negativeTop},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.popupClose.apply(void 0,arguments)}},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},[n("v-uni-view",{staticClass:"u-model"},[t.showTitle?n("v-uni-view",{staticClass:"u-model__title u-line-1",style:[t.titleStyle]},[t._v(t._s(t.title))]):t._e(),n("v-uni-view",{staticClass:"u-model__content"},[t.$slots.default||t.$slots.$default?n("v-uni-view",{style:[t.contentStyle]},[t._t("default")],2):n("v-uni-view",{staticClass:"u-model__content__message",style:[t.contentStyle]},[t._v(t._s(t.content))])],1),t.showCancelButton||t.showConfirmButton?n("v-uni-view",{staticClass:"u-model__footer u-border-top"},[t.showCancelButton?n("v-uni-view",{staticClass:"u-model__footer__button",style:[t.cancelBtnStyle],attrs:{"hover-stay-time":100,"hover-class":"u-model__btn--hover"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.cancel.apply(void 0,arguments)}}},[t._v(t._s(t.cancelText))]):t._e(),t.showConfirmButton||t.$slots["confirm-button"]?n("v-uni-view",{staticClass:"u-model__footer__button hairline-left",style:[t.confirmBtnStyle],attrs:{"hover-stay-time":100,"hover-class":t.asyncClose?"none":"u-model__btn--hover"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm.apply(void 0,arguments)}}},[t.$slots["confirm-button"]?t._t("confirm-button"):[t.loading?n("u-loading",{attrs:{mode:"circle",color:t.confirmColor}}):[t._v(t._s(t.confirmText))]]],2):t._e()],1):t._e()],1)],1)],1)},r=[]},"51bd":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={uIcon:n("e5e1").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[n("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},r=[]},5761:function(t,e,n){"use strict";n.r(e);var a=n("430f"),i=n("70e8");for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);n("6f69");var o=n("f0c5"),s=Object(o["a"])(i["default"],a["b"],a["c"],!1,null,"acf792f8",null,!1,a["a"],void 0);e["default"]=s.exports},"6f69":function(t,e,n){"use strict";var a=n("9945"),i=n.n(a);i.a},"70e8":function(t,e,n){"use strict";n.r(e);var a=n("aa64"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},"7ca1":function(t,e,n){"use strict";var a=n("2713"),i=n.n(a);i.a},"7f1a":function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=a(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var r=n("26cb"),o=uni.getSystemInfoSync(),s={},c={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,r.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,n=e.pEAddressAdd,a=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(n)||t.includes(a)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=c},9945:function(t,e,n){var a=n("00d9");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("2b69c872",a,!0,{sourceMap:!1,shadowMode:!1})},a126:function(t,e,n){var a=n("bbdc");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("703d0287",a,!0,{sourceMap:!1,shadowMode:!1})},aa64:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var a={name:"u-modal",props:{value:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:""},title:{type:[String],default:"提示"},width:{type:[Number,String],default:600},content:{type:String,default:"内容"},showTitle:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0},showCancelButton:{type:Boolean,default:!1},confirmText:{type:String,default:"确认"},cancelText:{type:String,default:"取消"},confirmColor:{type:String,default:"#2979ff"},cancelColor:{type:String,default:"#606266"},borderRadius:{type:[Number,String],default:16},titleStyle:{type:Object,default:function(){return{}}},contentStyle:{type:Object,default:function(){return{}}},cancelStyle:{type:Object,default:function(){return{}}},confirmStyle:{type:Object,default:function(){return{}}},zoom:{type:Boolean,default:!0},asyncClose:{type:Boolean,default:!1},maskCloseAble:{type:Boolean,default:!1},negativeTop:{type:[String,Number],default:0}},data:function(){return{loading:!1}},computed:{cancelBtnStyle:function(){return Object.assign({color:this.cancelColor},this.cancelStyle)},confirmBtnStyle:function(){return Object.assign({color:this.confirmColor},this.confirmStyle)},uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},watch:{value:function(t){!0===t&&(this.loading=!1)}},methods:{confirm:function(){this.asyncClose?this.loading=!0:this.$emit("input",!1),this.$emit("confirm")},cancel:function(){var t=this;this.$emit("cancel"),this.$emit("input",!1),setTimeout((function(){t.loading=!1}),300)},popupClose:function(){this.$emit("input",!1)},clearLoading:function(){this.loading=!1}}};e.default=a},ace5:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){return a}));var a={vhNavbar:n("12c6").default,uIcon:n("e5e1").default,uModal:n("5761").default,vhSkeleton:n("591b").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"content"},[n("v-uni-view",{},[t.from?n("v-uni-view",{},[n("v-uni-view",{staticClass:"p-fixed z-980 top-0 w-p100 bg-ffffff"},[n("v-uni-view",{style:{height:t.appStatusBarHeight+"px"}}),n("v-uni-view",{staticClass:"p-rela h-px-48 d-flex j-center a-center"},[n("v-uni-view",{staticClass:"p-abso left-24 h-p100 d-flex a-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack()}}},[n("u-icon",{attrs:{name:"nav-back",color:"#333",size:44}})],1),n("v-uni-view",{staticClass:"font-36 font-wei text-3"},[t._v("大转盘")])],1)],1)],1):n("v-uni-view",{},[n("vh-navbar",{attrs:{title:"大转盘","title-size":"36","title-bold":!0,"title-color":"#333"}})],1)],1),t.loading?n("v-uni-view",{staticClass:"fade-in"},[n("vh-skeleton",{attrs:{type:1e4,"loading-mode":"flower","bg-color":"#f5f5f5"}})],1):n("v-uni-view",{staticClass:"fade-in",style:{paddingTop:""==t.from?0:parseInt(t.appStatusBarHeight)+48+"px"}},[n("v-uni-view",{staticClass:"p-abso top-0 w-p100 h-1830"},[n("v-uni-image",{staticClass:"w-p100 h-p100",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/large_turntable/rab_bg.jpg",mode:"aspectFill"}})],1),n("v-uni-view",{staticClass:"p-abso top-120 right-0 bg-000-000-000-030 w-86 h-38 b-tl-bl-rad-200 font-24 text-ffffff text-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.newcomerShare.apply(void 0,arguments)}}},[t._v("分享")]),n("v-uni-view",{staticClass:"p-rela z-02 d-flex j-center mt-84"},[n("v-uni-image",{staticClass:"w-442 h-210",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/large_turntable/new_title.png",mode:"aspectFill"}})],1),n("v-uni-view",{staticClass:"luck-draw-bg p-rela z-02 w-750 h-854 mt-n-80"},[n("v-uni-view",{staticClass:"p-abso bottom-222 w-p100 d-flex j-center"},[n("v-uni-view",{staticClass:"luck-draw p-rela z-04 d-flex j-center a-center w-510 h-462"},[t._l(t.luckyDrawList,(function(e,a){return n("v-uni-view",{key:a,staticClass:"luck-draw-item p-abso w-170 h-152 d-flex flex-column j-center a-center",class:t.currentIndex==a+1?"sel-luck-draw-item":""},[n("v-uni-image",{staticClass:"w-44 h-44",attrs:{src:e.image,mode:"aspectFill"}}),n("v-uni-text",{staticClass:"mt-06 font-20 text-d2773f"},[t._v(t._s(e.name))]),e.sub_name?n("v-uni-text",{staticClass:"font-20 text-d2773f"},[t._v(t._s(e.sub_name))]):t._e()],1)})),n("v-uni-view",{staticClass:"w-170 h-152",class:t.luckyDrawing?"drawing-btn fil-gray100-opa100":"draw-btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$u.throttle(t.luckDraw,3e3)}}})],2)],1),n("v-uni-view",{staticClass:"p-abso bottom-140 w-p100 d-flex j-center"},[n("v-uni-view",{staticClass:"d-flex j-center a-center w-300 h-50"},[n("v-uni-text",{staticClass:"font-22 text-ffffff"},[t._v("今日免费抽奖次数：")]),n("v-uni-text",{staticClass:"font-24 font-wei text-ffd23e"},[t._v(t._s(t.luckyDrawInfo.usable_count||0))])],1)],1)],1),n("v-uni-view",{staticClass:"p-rela z-02 d-flex j-sb a-center mt-n-70 ptb-00-plr-42"},[n("v-uni-view",{staticClass:"btn-bg w-312 h-102 pt-20 text-center font-30 font-wei text-ffffff"},[t._v("当前兔头："+t._s(t.getRabbitNum))]),n("v-uni-view",{staticClass:"btn-bg w-312 h-102 pt-20 text-center font-30 font-wei text-ffffff",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumpPriceRecord.apply(void 0,arguments)}}},[t._v("中奖记录（"+t._s(t.luckyDrawInfo.share_draw_count||0)+"）")])],1),n("v-uni-view",{staticClass:"tips-bg p-rela z-02 w-750 h-360 d-flex j-center mt-44"},[n("v-uni-view",{staticClass:"w-630 h-316 d-flex flex-column j-center mt-44 pl-36"},[n("v-uni-view",{staticClass:"d-flex"},[n("v-uni-view",{staticClass:"w-08 h-08 bg-b44b33 b-rad-p50 mt-10"}),n("v-uni-view",{staticClass:"w-max-552 ml-10 font-24 text-6 l-h-34"},[t._v("每日首次抽奖免费，之后10兔头即可参与抽奖1次。")])],1),n("v-uni-view",{staticClass:"d-flex mt-10"},[n("v-uni-view",{staticClass:"w-08 h-08 bg-b44b33 b-rad-p50 mt-10"}),n("v-uni-view",{staticClass:"w-max-552 ml-10 font-24 text-6 l-h-34"},[t._v("分享抽奖链接即可增加一次抽奖机会，每天最多可分享5次")])],1),"2"===t.from?n("v-uni-view",{staticClass:"d-flex"},[n("v-uni-view",{staticClass:"w-08 h-08 bg-b44b33 b-rad-p50 mt-10"}),n("v-uni-view",{staticClass:"w-max-552 ml-10 font-24 text-6 l-h-34"},[t._v("在此活动中Apple不是赞助者，也没有以任何形式参与活动。")])],1):t._e()],1)],1),n("v-uni-view",{staticClass:"p-rela z-02 mt-48 text-center font-24 text-ffd6d3"},[t._v("本活动最终解释权归酒云网所有")]),n("v-uni-view",{},[n("u-modal",{attrs:{"show-title":!1,content:"",width:490,"show-cancel-button":!0,"show-confirm-button":0!==t.winPrize.type,"cancel-text":"知道了","cancel-style":{fontSize:"28rpx",color:"#999"},"confirm-text":1===t.winPrize.type?"前往兔头商店":"去使用","confirm-style":{fontSize:"28rpx",color:"#E80404"}},on:{cancel:function(e){arguments[0]=e=t.$handleEvent(e),t.knowIt()},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.jumpWinPrize()}},model:{value:t.showDrawSuccMod,callback:function(e){t.showDrawSuccMod=e},expression:"showDrawSuccMod"}},[n("v-uni-view",{staticClass:"pt-86 pb-64"},[n("v-uni-view",{staticClass:"d-flex j-center a-center"},[n("v-uni-image",{staticClass:"w-264 h-184",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/succ_red.png",mode:"aspectFill"}})],1),n("v-uni-view",{staticClass:"d-flex flex-column j-center a-center mt-30 l-h-44"},[n("v-uni-view",{staticClass:"font-28 text-3"},[t._v("中奖提示")]),n("v-uni-view",{staticClass:"pl-24 pr-24 text-center font-28 text-3"},[t._v(t._s(t.winPrize.msg))])],1)],1)],1)],1)],1)],1)},r=[]},bbdc:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},d10c:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,".luck-draw-bg[data-v-44bf2aa8]{background-image:url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/draw_box.png);background-size:cover}\n\n/* 九宫格抽奖滚动板块定位(八块) */.luck-draw .luck-draw-item[data-v-44bf2aa8]{background-image:url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/usel.png);background-size:cover}.luck-draw .sel-luck-draw-item[data-v-44bf2aa8]{background-image:url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/sel.png);background-size:cover}.luck-draw .luck-draw-item[data-v-44bf2aa8]:nth-child(1){left:0;top:0}.luck-draw .luck-draw-item[data-v-44bf2aa8]:nth-child(2){left:%?170?%;top:0}.luck-draw .luck-draw-item[data-v-44bf2aa8]:nth-child(3){right:0;top:0}.luck-draw .luck-draw-item[data-v-44bf2aa8]:nth-child(4){right:0;top:%?154?%}.luck-draw .luck-draw-item[data-v-44bf2aa8]:nth-child(5){right:0;bottom:0}.luck-draw .luck-draw-item[data-v-44bf2aa8]:nth-child(6){right:%?170?%;bottom:0}.luck-draw .luck-draw-item[data-v-44bf2aa8]:nth-child(7){left:0;bottom:0}.luck-draw .luck-draw-item[data-v-44bf2aa8]:nth-child(8){left:0;top:%?154?%}.draw-btn[data-v-44bf2aa8]{background-image:url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/draw1.png);background-size:cover}.drawing-btn[data-v-44bf2aa8]{background-image:url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/draw.png);background-size:cover}.btn-bg[data-v-44bf2aa8]{background-image:url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/rab_but.png);background-size:cover}.tips-bg[data-v-44bf2aa8]{background-image:url(https://images.vinehoo.com/vinehoomini/v3/large_turntable/rab_bg.png);background-size:cover}",""]),t.exports=e},e20d:function(t,e,n){"use strict";n("7a82");var a=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("d81d"),n("caad6"),n("2532"),n("d3b7"),n("159b"),n("99af");var i=a(n("f07e")),r=a(n("c964")),o=a(n("f3f3")),s=n("26cb"),c={name:"large-turntable",data:function(){return{from:"",loading:!0,appStatusBarHeight:"",luckyDrawInfo:{},luckyDrawList:[],currentIndex:1,priceIndex:0,count:8,timer:0,speed:200,times:0,cycle:60,prize:-1,luckyDrawing:!1,winPrize:{},showDrawSuccMod:!1}},computed:(0,o.default)((0,o.default)({},(0,s.mapState)(["routeTable"])),{},{getRabbitNum:function(){return this.luckyDrawInfo.rabbit?this.format.numberFormat(this.luckyDrawInfo.rabbit):0}}),onLoad:function(t){this.from=t.from,this.appStatusBarHeight=t.statusBarHeight,this.system.setNavigationBarBlack(),this.appGoBack()},onShow:function(){this.init()},onPullDownRefresh:function(){this.init()},methods:{init:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.getLuckyDrawList();case 2:return e.next=4,t.login.isLoginV3(t.from,0);case 4:if(n=e.sent,!n){e.next=8;break}return e.next=8,t.getLuckyDrawInfo();case 8:t.loading=!1,uni.stopPullDownRefresh();case 10:case"end":return e.stop()}}),e)})))()},getLuckyDrawList:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.getNewRabbitRotaryDraw();case 2:n=e.sent,n.data.list.map((function(t,e){t.name.includes("指定酒款")&&(t.sub_name=t.name.split("指定酒款")[1],t.name="指定酒款"),t.name.includes("满300减15")&&(t.sub_name=t.name.split("满300减15")[1],t.name="满300减15")})),t.luckyDrawList=n.data.list;case 5:case"end":return e.stop()}}),e)})))()},getLuckyDrawInfo:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.getNewcomerLotteryInfo();case 2:n=e.sent,t.luckyDrawInfo=n.data;case 4:case"end":return e.stop()}}),e)})))()},appGoBack:function(){var t=this;this.comes.isFromApp(this.from)&&(window.interceptBack=function(){if(0!=t.times||t.luckyDrawing)return t.feedback.toast({title:"正在抽奖中，请稍后"});wineYunJsBridge.openAppPage({client_path:{ios_path:"goBack",android_path:"goBack"}})})},goBack:function(){if(0!=this.times||this.luckyDrawing)return this.feedback.toast({title:"正在抽奖中，请稍后"});console.log("-----------------------我是兔头数"),console.log(this.luckyDrawInfo.rabbit),this.comes.isFromApp(this.from)?wineYunJsBridge.openAppPage({client_path:{ios_path:"goBack",android_path:"goBack"}}):this.jump.navigateBack()},luckDraw:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.login.isLoginV3(t.from);case 2:if(n=e.sent,!n){e.next=10;break}if(!(t.luckyDrawInfo.rabbit<10&&0==t.luckyDrawInfo.usable_count)){e.next=6;break}return e.abrupt("return",t.feedback.toast({title:"兔头不足~",icon:"error"}));case 6:if(0==t.times&&!t.luckyDrawing){e.next=8;break}return e.abrupt("return",t.feedback.toast({title:"正在抽奖中，请勿重复点击"}));case 8:t.luckyDrawing=!0,t.startDraw();case 10:case"end":return e.stop()}}),e)})))()},startDraw:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var n,a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,n={},0==t.luckyDrawInfo.usable_count&&(n.is_rabbit=1),e.next=5,t.$u.api.newRabbitLuckDraw(n);case 5:a=e.sent,t.luckyDrawList.forEach((function(e,n){e.id===a.data.id&&(t.priceIndex=n+1)})),console.log("----------------------------------------我是中奖信息"),console.log(a),t.winPrize=a.data,t.startRoll(),e.next=17;break;case 13:e.prev=13,e.t0=e["catch"](0),console.log("--------------我进入了抽奖异常的分支"),t.luckyDrawing=!1;case 17:case"end":return e.stop()}}),e,null,[[0,13]])})))()},startRoll:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.times+=1,t.oneRoll(),t.times>t.cycle+10&&t.prize===t.currentIndex?(clearTimeout(t.timer),t.prize=-1,t.priceIndex=0,t.times=0,t.speed=200,setTimeout((function(e){t.showDrawSuccMod=!0}),1e3)):(t.times<t.cycle?t.speed-=10:t.times===t.cycle?(t.prize=t.priceIndex,t.prize>8&&(console.log("------------------------------------我的中奖位置大于8"),t.prize=8)):t.times>t.cycle+10&&(0===t.prize&&8===t.currentIndex||t.prize===t.currentIndex+1)?t.speed+=110:t.speed+=20,t.speed<40&&(t.speed=40),t.timer=setTimeout(t.startRoll,t.speed));case 3:case"end":return e.stop()}}),e)})))()},oneRoll:function(){var t=this.currentIndex,e=this.count;t+=1,t>e&&(t=1),this.currentIndex=t},jumpPriceRecord:function(){if(0!=this.times||this.luckyDrawing)return this.feedback.toast({title:"正在抽奖中，请稍后"});this.jump.appAndMiniJump(2,"".concat(this.routeTable.pGLargeTurntablePrizeRecord),this.from)},jumpWinPrize:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var n,a,r,o,s,c,u,l;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.luckyDrawing=!1,t.getLuckyDrawInfo(),n=t.winPrize,a=n.type,r=n.prize,o=void 0===r?"":r,e.t0=a,e.next=1===e.t0?6:2===e.t0?8:14;break;case 6:return t.jump.appAndMiniJump(0,"".concat(t.routeTable.pBRabbitHeadShop),t.from),e.abrupt("break",14);case 8:return e.next=10,t.$u.api.couponDetail({coupon_id:o.split(",")[0]});case 10:return s=e.sent,c=s.data,u=c.coupon_type,l=c.relation_id,[1007,1008].includes(u)?t.jump.appAndMiniJump(1,"".concat(t.routeTable.pgGoodsDetail,"?id=").concat(l),t.from):t.jump.appAndMiniJump(0,t.routeTable.pECouponList,t.from),e.abrupt("break",14);case 14:case"end":return e.stop()}}),e)})))()},knowIt:function(){this.luckyDrawing=!1,this.getLuckyDrawInfo()},newcomerShare:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.feedback.toast({title:"请点击右上角分享"}),e.next=3,t.$u.api.newcomerShare();case 3:e.sent;case 4:case"end":return e.stop()}}),e)})))()}}};e.default=c},f074:function(t,e,n){"use strict";n.r(e);var a=n("7f1a"),i=n.n(a);for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);e["default"]=i.a},f2f9:function(t,e,n){"use strict";var a=n("a126"),i=n.n(a);i.a}}]);