(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-rabbit-head-shop-rabbit-head-shop"],{"062a":function(t,e,a){var i=a("aab3");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("2db15654",i,!0,{sourceMap:!1,shadowMode:!1})},"10eb":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},a("d9e2"),a("d401")},"12c6":function(t,e,a){"use strict";a.r(e);var i=a("51bd"),n=a("f074");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("f2f9");var r=a("f0c5"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"519170ec",null,!1,i["a"],void 0);e["default"]=s.exports},"144f":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"d-flex j-center",style:[this.outerEmpConStyle]},[e("v-uni-view",{staticClass:"p-rela",style:[this.innEmpConStyle]},[e("v-uni-image",{staticClass:"w-p100 h-p100",attrs:{src:this.imageSrc,mode:"aspectFill"}}),e("v-uni-view",{staticClass:"p-abso w-p100 d-flex j-center font-28 text-9",style:[this.textStyle]},[this._v(this._s(this.text))])],1)],1)},n=[]},"1c6d":function(t,e,a){"use strict";a.r(e);var i=a("39b6"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},2836:function(t,e,a){"use strict";var i=a("5d28"),n=a.n(i);n.a},"2b7f":function(t,e,a){"use strict";a.r(e);var i=a("b71b"),n=a("1c6d");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("b1a6"),a("2836");var r=a("f0c5"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"2fdad5ca",null,!1,i["a"],void 0);e["default"]=s.exports},"2da4":function(t,e,a){var i=a("39e4");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("64a51bd6",i,!0,{sourceMap:!1,shadowMode:!1})},"39b6":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("99af");var n=i(a("d0ff")),o=i(a("f07e")),r=i(a("c964")),s=i(a("f3f3")),u=a("26cb"),d={name:"rabbit-head-shop",data:function(){return{loading:!0,pageLength:0,from:"",appStatusBarHeight:"",navBackgroundColor:"rgba(224, 20, 31, 1)",hasGotRabbitCombine:0,rabbitInfo:{},rabbitNumber:"",capsuleList:[],goodsList:[],couponList:[],current:0,tabsList:[{name:"商品"},{name:"优惠券"}],page:1,limit:10,totalPage:1,loadStatus:"loadmore",loadType:"",jumpGoodsId:"",jumpRabbitHeadCoupon:!1}},computed:{navigationBarHeight:function(){return this.system.getSysInfo().statusBarHeight+47},getClient:function(){var t=null;return t="1"==this.$vhFrom?1:"2"==this.$vhFrom?0:"next"==this.$vhFrom?5:2,t}},onLoad:function(t){this.pageLength=this.pages.getPageLength(),t.from&&t.statusBarHeight&&(this.from=t.from,this.appStatusBarHeight=t.statusBarHeight,this.muFrom(this.from)),t.loadType&&(this.loadType=t.loadType),t.jumpGoodsId&&(this.jumpGoodsId=t.jumpGoodsId)},onShow:function(){var t=this;this.login.isLoginV2(this.$vhFrom).then((function(){t.init()}))},methods:(0,s.default)((0,s.default)({},(0,u.mapMutations)(["muRabbitNum","muFrom"])),{},{init:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,t.page=1,e.next=4,t.getRabbitCombine();case 4:uni.stopPullDownRefresh(),t.loading=!1,e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](0),t.goBack();case 11:case"end":return e.stop()}}),e,null,[[0,8]])})))()},goBack:function(){var t=this;setTimeout((function(){t.comes.isFromApp(t.$vhFrom)?wineYunJsBridge.openAppPage({client_path:{ios_path:"goBack",android_path:"goBack"}}):t.jump.navigateBack()}),1500)},jumpBack:function(){this.pageLength<=1&&""==this.$vhFrom?(console.log("-----------------------------------------------我的页面栈 <= 1"),this.jump.reLaunch("/pages/index/index")):this.comes.isFromApp(this.$vhFrom)?wineYunJsBridge.openAppPage({client_path:{ios_path:"goBack",android_path:"goBack"}}):this.jump.navigateBack()},getRabbitCombine:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var a,i,n,r,s;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.rabbitCombine({tab:"home",page:t.page,limit:t.limit,client:t.getClient});case 2:a=e.sent,t.rabbitInfo=a.data,t.hasGotRabbitCombine||(t.navBackgroundColor="rgba(224, 20, 31, 0)"),i=a.data,n=i.rabbitNumber,r=i.capsule,s=i.commodities,t.rabbitNumber=n||0,t.muRabbitNum(t.rabbitNumber),t.capsuleList=r.list,t.goodsList=s.list,t.totalPage=Math.ceil(s.total/t.limit),t.loadStatus=t.page==t.totalPage?"nomore":"loadmore",t.hasGotRabbitCombine=1,"coupon"===t.loadType&&t.changeTabs(1);case 13:case"end":return e.stop()}}),e)})))()},changeTabs:function(t){this.feedback.loading(),this.page=1,this.totalPage=1,this.current=t,0==this.current?this.getRabbitGoods():this.getCouonList()},getRabbitGoods:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var a,i,n,r;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.rabbitCombine({tab:"commodities",page:t.page,limit:t.limit});case 2:a=e.sent,i=a.data,n=i.list,r=i.total,t.handlePage("goodsList",n,r);case 5:case"end":return e.stop()}}),e)})))()},getCouonList:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var a,i,n,r;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.rabbitCombine({tab:"coupon",page:t.page,limit:t.limit});case 2:a=e.sent,i=a.data,n=i.list,r=i.total,t.handlePage("couponList",n,r);case 5:case"end":return e.stop()}}),e)})))()},handlePage:function(t,e,a){1==this.page?this[t]=e:this[t]=[].concat((0,n.default)(this[t]),(0,n.default)(e)),this.totalPage=Math.ceil(a/this.limit),this.loadStatus=this.page==this.totalPage?"nomore":"loadmore",this.feedback.hideLoading()},rabbitRecord:function(){this.login.isLogin(this.$vhFrom)&&this.jump.appAndMiniJump(1,"/packageB/pages/rabbit-head-record/rabbit-head-record",this.$vhFrom)},earnRabbit:function(){this.login.isLogin(this.$vhFrom)&&(this.comes.isFromApp(this.$vhFrom)?"next"==this.$vhFrom?this.jump.appAndMiniJump(0,"/packageE/pages/daily-tasks/daily-tasks",this.$vhFrom,0,!0):wineYunJsBridge.openAppPage({client_path:{ios_path:"DailyCheckViewController",android_path:"com.stg.rouge.activity.EveryTaskCenterActivity"},ad_path_param:[{ios_key:"login",ios_val:"1",android_key:"login",android_val:"1"}]}):this.jump.navigateTo("/packageE/pages/daily-tasks/daily-tasks"))},toExchange:function(t){console.log(t),this.login.isLogin(this.$vhFrom)&&(this.jumpRabbitHeadCoupon=!0,""===this.from?this.jump.navigateTo("/packageB/pages/rabbit-head-coupon/rabbit-head-coupon?id=".concat(t.id,"&jumpGoodsId=").concat(this.jumpGoodsId)):this.jump.appAndMiniJump(2,"/packageB/pages/rabbit-head-coupon/rabbit-head-coupon?id=".concat(t.id,"&jumpGoodsId=").concat(this.jumpGoodsId),this.from))}}),onPullDownRefresh:function(){this.page=1,this.init(),1==this.current&&this.getCouonList()},onPageScroll:function(t){t.scrollTop<=100?this.navBackgroundColor="rgba(224, 20, 31, ".concat(t.scrollTop/100,")"):this.navBackgroundColor="rgba(224, 20, 31, 1)"},onReachBottom:function(){this.page!=this.totalPage&&0!=this.totalPage&&(this.loadStatus="loading",this.page++,0==this.current?this.getRabbitGoods():this.getCouonList())}};e.default=d},"39e4":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-load-more-wrap[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center}.u-load-more-inner[data-v-15067509]{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:0 %?12?%}.u-more[data-v-15067509]{position:relative;display:flex;flex-direction:row;justify-content:center}.u-dot-text[data-v-15067509]{font-size:%?28?%}.u-loadmore-icon-wrap[data-v-15067509]{margin-right:%?8?%}.u-loadmore-icon[data-v-15067509]{display:flex;flex-direction:row;align-items:center;justify-content:center}',""]),t.exports=e},4053:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if(Array.isArray(t))return(0,i.default)(t)};var i=function(t){return t&&t.__esModule?t:{default:t}}(a("b680"))},"4f1b":function(t,e,a){"use strict";a.r(e);var i=a("825d"),n=a("8e1d");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("fa94");var r=a("f0c5"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"4ed92bb2",null,!1,i["a"],void 0);e["default"]=s.exports},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uIcon:a("e5e1").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},o=[]},5307:function(t,e,a){"use strict";a.r(e);var i=a("7193"),n=a("8c83");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("fb12");var r=a("f0c5"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"34b198da",null,!1,i["a"],void 0);e["default"]=s.exports},"55c2":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var i={name:"vh-empty",props:{bgColor:{type:String,default:"#FFF"},paddingTop:{type:[String,Number],default:0},paddingBottom:{type:[String,Number],default:0},width:{type:[String,Number],default:440},height:{type:[String,Number],default:360},imageSrc:{type:String,default:""},textBottom:{type:[String,Number],default:46},text:{type:String,default:""}},computed:{outerEmpConStyle:function(){return{backgroundColor:this.bgColor,paddingTop:this.paddingTop+"rpx",paddingBottom:this.paddingBottom+"rpx"}},innEmpConStyle:function(){return{width:this.width+"rpx",height:this.height+"rpx"}},textStyle:function(){return{bottom:this.textBottom+"rpx"}}}};e.default=i},5820:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-swiper-wrap[data-v-34b198da]{position:relative;overflow:hidden;-webkit-transform:translateY(0);transform:translateY(0)}.vh-swiper-item[data-v-34b198da]{display:flex;overflow:hidden;align-items:center}.vh-list-image-wrap[data-v-34b198da]{width:100%;height:100%;flex:1;transition:all .5s;overflow:hidden;box-sizing:initial;position:relative}.vh-list-scale[data-v-34b198da]{-webkit-transform-origin:center center;transform-origin:center center}.vh-swiper-image[data-v-34b198da]{width:100%;will-change:transform;height:100%;display:block;pointer-events:none}.vh-swiper-border[data-v-34b198da]{position:absolute;z-index:10;left:0;top:0;width:100%;height:100%}.vh-swiper-title[data-v-34b198da]{position:absolute;background-color:rgba(0,0,0,.3);bottom:0;left:0;width:100%;font-size:%?28?%;padding:%?12?% %?24?%;color:hsla(0,0%,100%,.9)}.vh-swiper-indicator[data-v-34b198da]{padding:0 %?24?%;position:absolute;display:flex;width:100%;z-index:1}.vh-indicator-item-rect[data-v-34b198da]{width:%?26?%;height:%?8?%;margin:0 %?6?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.vh-indicator-item-rect-active[data-v-34b198da]{background-color:hsla(0,0%,100%,.8)}.vh-indicator-item-dot[data-v-34b198da]{width:%?14?%;height:%?14?%;margin:0 %?6?%;border-radius:%?20?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.vh-indicator-item-dot-active[data-v-34b198da]{background-color:hsla(0,0%,100%,.8)}.vh-indicator-item-round[data-v-34b198da]{width:%?14?%;height:%?14?%;margin:0 %?6?%;border-radius:%?20?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.vh-indicator-item-round-active[data-v-34b198da]{width:%?34?%;background-color:hsla(0,0%,100%,.8)}.vh-indicator-item-number[data-v-34b198da]{padding:%?6?% %?16?%;line-height:1;background-color:rgba(0,0,0,.3);border-radius:%?100?%;font-size:%?26?%;color:hsla(0,0%,100%,.8)}',""]),t.exports=e},"5ba4":function(t,e,a){"use strict";a.r(e);var i=a("144f"),n=a("a58c");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);var r=a("f0c5"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"55488dce",null,!1,i["a"],void 0);e["default"]=s.exports},"5d28":function(t,e,a){var i=a("c999");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("44082ce4",i,!0,{sourceMap:!1,shadowMode:!1})},7099:function(t,e,a){var i=a("7b93");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("709914c8",i,!0,{sourceMap:!1,shadowMode:!1})},7193:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={vhImage:a("ce7c").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"vh-swiper-wrap",style:{borderRadius:t.borderRadius+"rpx"}},[a("v-uni-swiper",{style:{height:t.height+"rpx",backgroundColor:t.bgColor},attrs:{current:t.elCurrent,interval:t.interval,circular:t.circular,duration:t.duration,autoplay:t.autoplay,"previous-margin":t.effect3d?t.effect3dPreviousMargin+"rpx":"0","next-margin":t.effect3d?t.effect3dPreviousMargin+"rpx":"0"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)},animationfinish:function(e){arguments[0]=e=t.$handleEvent(e),t.animationfinish.apply(void 0,arguments)}}},t._l(t.list,(function(e,i){return a("v-uni-swiper-item",{key:i,staticClass:"vh-swiper-item"},[a("v-uni-view",{staticClass:"vh-list-image-wrap",class:[t.uCurrent!=i?"vh-list-scale":""],style:{borderRadius:t.borderRadius+"rpx",transform:t.effect3d&&t.uCurrent!=i?"scaleY(0.9)":"scaleY(1)",margin:t.effect3d&&t.uCurrent!=i?"0 20rpx":0},on:{click:function(a){a.stopPropagation(),a.preventDefault(),arguments[0]=a=t.$handleEvent(a),t.listClick(e)}}},[a("vh-image",{attrs:{"loading-type":t.loadingType,src:e[t.name]||e,height:t.height,mode:t.imgMode}}),t.borderImage&&0==i?a("v-uni-image",{staticClass:"vh-swiper-border",attrs:{src:t.borderImage,mode:t.imgMode}}):t._e(),t.title&&e.title?a("v-uni-view",{staticClass:"vh-swiper-title",style:[{"padding-bottom":t.titlePaddingBottom},t.titleStyle]},[t._v(t._s(e.title))]):t._e()],1)],1)})),1),t.isShowIndicator?a("v-uni-view",{staticClass:"vh-swiper-indicator",style:{top:"topLeft"==t.indicatorPos||"topCenter"==t.indicatorPos||"topRight"==t.indicatorPos?"12rpx":"auto",bottom:"bottomLeft"==t.indicatorPos||"bottomCenter"==t.indicatorPos||"bottomRight"==t.indicatorPos?t.indicatorBottom+"rpx":"auto",justifyContent:t.justifyContent,padding:"0 "+(t.effect3d?"74rpx":"24rpx")}},["rect"==t.mode?t._l(t.list,(function(e,i){return a("v-uni-view",{key:i,staticClass:"vh-indicator-item-rect",class:{"vh-indicator-item-rect-active":i==t.uCurrent}})})):t._e(),"dot"==t.mode?t._l(t.list,(function(e,i){return t.customDot?a("v-uni-view",{key:i,style:[i==t.uCurrent?t.customDotStyle.active:t.customDotStyle.default]}):t._l(t.list,(function(e,i){return a("v-uni-view",{key:i,staticClass:"vh-indicator-item-dot",class:{"vh-indicator-item-dot-active":i==t.uCurrent}})}))})):t._e(),"round"==t.mode?t._l(t.list,(function(e,i){return a("v-uni-view",{key:i,staticClass:"vh-indicator-item-round",class:{"vh-indicator-item-round-active":i==t.uCurrent}})})):t._e(),"number"==t.mode?[a("v-uni-view",{staticClass:"vh-indicator-item-number"},[t._v(t._s(t.uCurrent+1)+"/"+t._s(t.list.length))])]:t._e()],2):t._e()],1)},o=[]},"776f":function(t,e,a){"use strict";a.r(e);var i=a("e643"),n=a("e4d5");for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);a("afb6");var r=a("f0c5"),s=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"15067509",null,!1,i["a"],void 0);e["default"]=s.exports},"7b93":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,"uni-page-body[data-v-2fdad5ca]{background-color:#f5f5f5}body.?%PAGE?%[data-v-2fdad5ca]{background-color:#f5f5f5}",""]),t.exports=e},"7f1a":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var o=a("26cb"),r=uni.getSystemInfoSync(),s={},u={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:r.statusBarHeight,appStatusBarHeight:0}},computed:(0,n.default)((0,n.default)({},(0,o.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,i=e.pEAddressManagement,n=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(i)||t.includes(n))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=u},"825d":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-button",{staticClass:"u-btn u-line-1 u-fix-ios-appearance",class:["u-size-"+t.size,t.plain?"u-btn--"+t.type+"--plain":"",t.loading?"u-loading":"","circle"==t.shape?"u-round-circle":"",t.hairLine?t.showHairLineBorder:"u-btn--bold-border","u-btn--"+t.type,t.disabled?"u-btn--"+t.type+"--disabled":""],style:[t.customStyle,{overflow:t.ripple?"hidden":"visible"}],attrs:{id:"u-wave-btn","hover-start-time":Number(t.hoverStartTime),"hover-stay-time":Number(t.hoverStayTime),disabled:t.disabled,"form-type":t.formType,"open-type":t.openType,"app-parameter":t.appParameter,"hover-stop-propagation":t.hoverStopPropagation,"send-message-title":t.sendMessageTitle,"send-message-path":"sendMessagePath",lang:t.lang,"data-name":t.dataName,"session-from":t.sessionFrom,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"hover-class":t.getHoverClass,loading:t.loading},on:{getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.getphonenumber.apply(void 0,arguments)},getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.getuserinfo.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.error.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.opensetting.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.launchapp.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.click(e)}}},[t._t("default"),t.ripple?a("v-uni-view",{staticClass:"u-wave-ripple",class:[t.waveActive?"u-wave-active":""],style:{top:t.rippleTop+"px",left:t.rippleLeft+"px",width:t.fields.targetWidth+"px",height:t.fields.targetWidth+"px","background-color":t.rippleBgColor||"rgba(0, 0, 0, 0.15)"}}):t._e()],2)},n=[]},8927:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3"),a("c975");var i={name:"vh-swiper",props:{loadingType:{type:[String,Number],default:1},list:{type:Array,default:function(){return[]}},borderImage:{type:String,default:""},title:{type:Boolean,default:!1},indicator:{type:Object,default:function(){return{}}},borderRadius:{type:[Number,String],default:8},interval:{type:[String,Number],default:3e3},mode:{type:String,default:"round"},customDot:{type:Boolean,default:!1},customDotStyle:{type:Object,default:function(){return{default:{width:"10rpx",height:"10rpx",borderRadius:"10rpx",margin:"0 6rpx",transition:"all 0.5s",backgroundColor:"#DDDDDD"},active:{width:"10rpx",height:"10rpx",borderRadius:"10rpx",margin:"0 6rpx",transition:"all 0.5s",backgroundColor:"#E80404"}}}},height:{type:[Number,String],default:250},indicatorPos:{type:String,default:"bottomCenter"},indicatorBottom:{type:[String,Number],default:16},effect3d:{type:Boolean,default:!1},effect3dPreviousMargin:{type:[Number,String],default:50},autoplay:{type:Boolean,default:!0},duration:{type:[Number,String],default:500},circular:{type:Boolean,default:!0},imgMode:{type:String,default:"aspectFill"},name:{type:String,default:"image"},current:{type:[Number,String],default:0},bgColor:{type:String,default:"#FFFFFF"},titleStyle:{type:Object,default:function(){return{}}},isShowIndicator:{type:Boolean,default:!0}},watch:{list:function(t,e){t.length!==e.length&&(this.uCurrent=0)},current:function(t){this.uCurrent=t}},data:function(){return{uCurrent:this.current}},computed:{justifyContent:function(){return"topLeft"==this.indicatorPos||"bottomLeft"==this.indicatorPos?"flex-start":"topCenter"==this.indicatorPos||"bottomCenter"==this.indicatorPos?"center":"topRight"==this.indicatorPos||"bottomRight"==this.indicatorPos?"flex-end":void 0},titlePaddingBottom:function(){var t=0;return"none"==this.mode?"12rpx":(t=["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"==this.mode?"60rpx":["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"!=this.mode?"40rpx":"12rpx",t)},elCurrent:function(){return Number(this.current)}},methods:{listClick:function(t){this.$emit("click",t)},change:function(t){var e=t.detail.current;this.uCurrent=e,this.$emit("change",e)},animationfinish:function(t){}}};e.default=i},"8a04":function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3");var i={name:"u-loadmore",props:{bgColor:{type:String,default:"transparent"},icon:{type:Boolean,default:!0},fontSize:{type:String,default:"28"},color:{type:String,default:"#606266"},status:{type:String,default:"loadmore"},iconType:{type:String,default:"circle"},loadText:{type:Object,default:function(){return{loadmore:"加载更多",loading:"正在加载...",nomore:"没有更多了"}}},isDot:{type:Boolean,default:!1},iconColor:{type:String,default:"#b7b7b7"},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},height:{type:[String,Number],default:"auto"}},data:function(){return{dotText:"●"}},computed:{loadTextStyle:function(){return{color:this.color,fontSize:this.fontSize+"rpx",position:"relative",zIndex:1,backgroundColor:this.bgColor}},cricleStyle:function(){return{borderColor:"#e5e5e5 #e5e5e5 #e5e5e5 ".concat(this.circleColor)}},flowerStyle:function(){return{}},showText:function(){var t="";return t="loadmore"==this.status?this.loadText.loadmore:"loading"==this.status?this.loadText.loading:"nomore"==this.status&&this.isDot?this.dotText:this.loadText.nomore,t}},methods:{loadMore:function(){"loadmore"==this.status&&this.$emit("loadmore")}}};e.default=i},"8c83":function(t,e,a){"use strict";a.r(e);var i=a("8927"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},"8e1d":function(t,e,a){"use strict";a.r(e);var i=a("9476"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},9476:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("a9e3"),a("c975"),a("d3b7"),a("ac1f");var i={name:"u-button",props:{hairLine:{type:Boolean,default:!0},type:{type:String,default:"default"},size:{type:String,default:"default"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!1},hoverBgColor:{type:String,default:""},rippleBgColor:{type:String,default:""},ripple:{type:Boolean,default:!1},hoverClass:{type:String,default:""},customStyle:{type:Object,default:function(){return{}}},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:1e3},hoverStartTime:{type:[String,Number],default:20},hoverStayTime:{type:[String,Number],default:150}},computed:{getHoverClass:function(){if(this.loading||this.disabled||this.ripple||this.hoverClass)return"";var t;return t=this.plain?"u-"+this.type+"-plain-hover":"u-"+this.type+"-hover",t},showHairLineBorder:function(){return["primary","success","error","warning"].indexOf(this.type)>=0&&!this.plain?"":"u-hairline-border"}},data:function(){return{rippleTop:0,rippleLeft:0,fields:{},waveActive:!1}},methods:{click:function(t){var e=this;this.$u.throttle((function(){!0!==e.loading&&!0!==e.disabled&&(e.ripple&&(e.waveActive=!1,e.$nextTick((function(){this.getWaveQuery(t)}))),e.$emit("click",t))}),this.throttleTime)},getWaveQuery:function(t){var e=this;this.getElQuery().then((function(a){var i=a[0];if(i.width&&i.width&&(i.targetWidth=i.height>i.width?i.height:i.width,i.targetWidth)){e.fields=i;var n,o;n=t.touches[0].clientX,o=t.touches[0].clientY,e.rippleTop=o-i.top-i.targetWidth/2,e.rippleLeft=n-i.left-i.targetWidth/2,e.$nextTick((function(){e.waveActive=!0}))}}))},getElQuery:function(){var t=this;return new Promise((function(e){var a="";a=uni.createSelectorQuery().in(t),a.select(".u-btn").boundingClientRect(),a.exec((function(t){e(t)}))}))},getphonenumber:function(t){this.$emit("getphonenumber",t)},getuserinfo:function(t){this.$emit("getuserinfo",t)},error:function(t){this.$emit("error",t)},opensetting:function(t){this.$emit("opensetting",t)},launchapp:function(t){this.$emit("launchapp",t)}}};e.default=i},a126:function(t,e,a){var i=a("bbdc");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("703d0287",i,!0,{sourceMap:!1,shadowMode:!1})},a58c:function(t,e,a){"use strict";a.r(e);var i=a("55c2"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},a9e0:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)},a("a4d3"),a("e01a"),a("d3b7"),a("d28b"),a("3ca3"),a("ddb0"),a("a630")},aab3:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.u-btn[data-v-4ed92bb2]::after{border:none}.u-btn[data-v-4ed92bb2]{position:relative;border:0;display:inline-flex;overflow:visible;line-height:1;display:flex;flex-direction:row;align-items:center;justify-content:center;cursor:pointer;padding:0 %?40?%;z-index:1;box-sizing:border-box;transition:all .15s}.u-btn--bold-border[data-v-4ed92bb2]{border:1px solid #fff}.u-btn--default[data-v-4ed92bb2]{color:#606266;border-color:#c0c4cc;background-color:#fff}.u-btn--primary[data-v-4ed92bb2]{color:#fff;border-color:#2979ff;background-color:#2979ff}.u-btn--success[data-v-4ed92bb2]{color:#fff;border-color:#19be6b;background-color:#19be6b}.u-btn--error[data-v-4ed92bb2]{color:#fff;border-color:#fa3534;background-color:#fa3534}.u-btn--warning[data-v-4ed92bb2]{color:#fff;border-color:#f90;background-color:#f90}.u-btn--default--disabled[data-v-4ed92bb2]{color:#fff;border-color:#e4e7ed;background-color:#fff}.u-btn--primary--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#a0cfff!important;background-color:#a0cfff!important}.u-btn--success--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#71d5a1!important;background-color:#71d5a1!important}.u-btn--error--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fab6b6!important;background-color:#fab6b6!important}.u-btn--warning--disabled[data-v-4ed92bb2]{color:#fff!important;border-color:#fcbd71!important;background-color:#fcbd71!important}.u-btn--primary--plain[data-v-4ed92bb2]{color:#2979ff!important;border-color:#a0cfff!important;background-color:#ecf5ff!important}.u-btn--success--plain[data-v-4ed92bb2]{color:#19be6b!important;border-color:#71d5a1!important;background-color:#dbf1e1!important}.u-btn--error--plain[data-v-4ed92bb2]{color:#fa3534!important;border-color:#fab6b6!important;background-color:#fef0f0!important}.u-btn--warning--plain[data-v-4ed92bb2]{color:#f90!important;border-color:#fcbd71!important;background-color:#fdf6ec!important}.u-hairline-border[data-v-4ed92bb2]:after{content:" ";position:absolute;pointer-events:none;box-sizing:border-box;-webkit-transform-origin:0 0;transform-origin:0 0;left:0;top:0;width:199.8%;height:199.7%;-webkit-transform:scale(.5);transform:scale(.5);border:1px solid currentColor;z-index:1}.u-wave-ripple[data-v-4ed92bb2]{z-index:0;position:absolute;border-radius:100%;background-clip:padding-box;pointer-events:none;-webkit-user-select:none;user-select:none;-webkit-transform:scale(0);transform:scale(0);opacity:1;-webkit-transform-origin:center;transform-origin:center}.u-wave-ripple.u-wave-active[data-v-4ed92bb2]{opacity:0;-webkit-transform:scale(2);transform:scale(2);transition:opacity 1s linear,-webkit-transform .4s linear;transition:opacity 1s linear,transform .4s linear;transition:opacity 1s linear,transform .4s linear,-webkit-transform .4s linear}.u-round-circle[data-v-4ed92bb2]{border-radius:%?100?%}.u-round-circle[data-v-4ed92bb2]::after{border-radius:%?100?%}.u-loading[data-v-4ed92bb2]::after{background-color:hsla(0,0%,100%,.35)}.u-size-default[data-v-4ed92bb2]{font-size:%?30?%;height:%?80?%;line-height:%?80?%}.u-size-medium[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?26?%;height:%?70?%;line-height:%?70?%;padding:0 %?80?%}.u-size-mini[data-v-4ed92bb2]{display:inline-flex;width:auto;font-size:%?22?%;padding-top:1px;height:%?50?%;line-height:%?50?%;padding:0 %?20?%}.u-primary-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#2b85e4!important}.u-default-plain-hover[data-v-4ed92bb2]{color:#2b85e4!important;background:#ecf5ff!important}.u-success-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#18b566!important}.u-warning-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#f29100!important}.u-error-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#dd6161!important}.u-info-plain-hover[data-v-4ed92bb2]{color:#fff!important;background:#82848a!important}.u-default-hover[data-v-4ed92bb2]{color:#2b85e4!important;border-color:#2b85e4!important;background-color:#ecf5ff!important}.u-primary-hover[data-v-4ed92bb2]{background:#2b85e4!important;color:#fff}.u-success-hover[data-v-4ed92bb2]{background:#18b566!important;color:#fff}.u-info-hover[data-v-4ed92bb2]{background:#82848a!important;color:#fff}.u-warning-hover[data-v-4ed92bb2]{background:#f29100!important;color:#fff}.u-error-hover[data-v-4ed92bb2]{background:#dd6161!important;color:#fff}',""]),t.exports=e},afb6:function(t,e,a){"use strict";var i=a("2da4"),n=a.n(i);n.a},b1a6:function(t,e,a){"use strict";var i=a("7099"),n=a.n(i);n.a},b71b:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={vhNavbar:a("12c6").default,uIcon:a("e5e1").default,vhSwiper:a("5307").default,uTabs:a("b14f").default,vhImage:a("ce7c").default,uLoadmore:a("776f").default,vhEmpty:a("5ba4").default,uButton:a("4f1b").default,vhSkeleton:a("591b").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{},[""==t.from?a("vh-navbar",{attrs:{"back-icon-color":"#FFF",title:"兔头商店","title-color":"#FFF",background:{background:t.navBackgroundColor}}}):a("v-uni-view",[a("v-uni-view",{staticClass:"p-fixed z-980 top-0 w-p100",style:{background:t.navBackgroundColor}},[a("v-uni-view",{style:{height:t.$appStatusBarHeight+"px"}}),a("v-uni-view",{staticClass:"p-rela h-px-48 d-flex j-center a-center"},[a("v-uni-view",{staticClass:"p-abso left-24 h-p100 d-flex a-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jumpBack()}}},[a("u-icon",{attrs:{name:"nav-back",color:"#FFF",size:44}})],1),a("v-uni-view",{staticClass:"font-36 font-wei text-ffffff"},[t._v("兔头商店")])],1)],1)],1)],1),t.loading?a("vh-skeleton",{attrs:{type:1e4,"loading-mode":"flower"}}):a("v-uni-view",{staticClass:"fade-in"},[a("v-uni-view",{staticClass:"p-abso top-0 z-02 w-p100 h-400"},[a("v-uni-image",{staticClass:"w-p100 h-460",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_res_ban.png",mode:"widthFix"}})],1),a("v-uni-view",{staticClass:"p-abso z-01 w-p100 h-434 bg-ffffff",class:t.capsuleList.length?"top-430":"top-410"}),a("v-uni-view",{staticClass:"p-rela z-03 d-flex flex-column j-center a-center"},[a("v-uni-view",{staticClass:"font-84 font-wei text-ffffff",style:{paddingTop:""==t.$vhFrom?0:parseInt(t.$appStatusBarHeight)+48+"px"}},[t._v(t._s(t.rabbitNumber))]),a("v-uni-view",{staticClass:"d-flex j-center a-center mt-20"},[a("v-uni-image",{staticClass:"w-18 h-18",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png",mode:"widthFix"}}),a("v-uni-text",{staticClass:"ml-06 mr-06 font-24 text-ffffff"},[t._v("我的兔头")]),a("v-uni-image",{staticClass:"w-18 h-18",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png",mode:"widthFix"}})],1)],1),a("v-uni-view",{staticClass:"p-rela z-03 bg-ffffff b-rad-10 d-flex j-sb a-center b-sh-00042604-007 mt-44 mr-32 mb-36 ml-32 ptb-24-plr-56"},[a("v-uni-view",{staticClass:"d-flex a-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.rabbitRecord.apply(void 0,arguments)}}},[a("v-uni-image",{staticClass:"w-90 h-90",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_rec.png",mode:"widthFix"}}),a("v-uni-text",{staticClass:"ml-12 font-32 font-wei text-e04040"},[t._v("兔头记录")])],1),a("v-uni-view",{staticClass:"w-01 h-60 bg-e1e1e1 t-sc-x-h-1"}),a("v-uni-view",{staticClass:"d-flex a-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.earnRabbit.apply(void 0,arguments)}}},[a("v-uni-image",{staticClass:"w-90 h-90",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_get.png",mode:"widthFix"}}),a("v-uni-text",{staticClass:"ml-12 font-32 font-wei text-d79c36"},[t._v("赚取兔头")])],1)],1),t.capsuleList.length?a("v-uni-view",{staticClass:"p-rela z-03 mt-40 mr-32 mb-36 ml-32"},[a("vh-swiper",{attrs:{list:t.capsuleList,mode:"rect",loadingType:10,height:178,"bg-color":"transparent"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.pubConfJump(e,t.$vhFrom)}}})],1):t._e(),a("v-uni-view",{staticClass:"rabbit-head-shop__tabs p-stic z-980 bb-s-01-f7f7f7",style:{top:""==t.$vhFrom?t.navigationBarHeight+"px":parseInt(t.$appStatusBarHeight)+48+"px"}},[a("u-tabs",{ref:"tabs",attrs:{list:t.tabsList,current:t.current,"bar-width":"36","bar-height":"8","font-size":"28","inactive-color":"#999","active-color":"#333","bar-style":{background:"#e80404"}},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.changeTabs.apply(void 0,arguments)}}})],1),0==t.current?a("v-uni-view",{staticClass:"fade-in p-rela z-03"},[t.goodsList.length?a("v-uni-view",{staticClass:"bg-f7f7f7 pt-40 pb-20 pl-32 pr-32"},[a("v-uni-view",{staticClass:"d-flex flex-wrap j-sb"},t._l(t.goodsList,(function(e,i){return a("v-uni-view",{key:i,staticClass:"p-rela bg-ffffff w-334 b-rad-10 o-hid mb-18",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.jump.appAndMiniJump(1,"/packageB/pages/rabbit-head-goods-detail/rabbit-head-goods-detail?id="+e.id,t.$vhFrom)}}},[a("vh-image",{attrs:{src:e.banner_img,height:208}}),a("v-uni-view",{staticClass:"h-180 d-flex flex-column j-sb pt-20 pb-22 pl-16 pr-16"},[a("v-uni-view",{},[a("v-uni-view",{staticClass:"font-24 font-wei text-3 l-h-30 text-hidden-2"},[t._v(t._s(e.title))]),a("v-uni-view",{staticClass:"mt-04 font-20 text-9 l-h-34 text-hidden-1"},[t._v(t._s(e.brief))])],1),a("v-uni-view",{staticClass:"d-flex j-sb a-center"},[a("v-uni-view",{staticClass:"d-flex a-center"},[a("v-uni-text",{staticClass:"font-28 font-wei text-e80404 l-h-30"},[t._v(t._s(e.rabbit))]),a("v-uni-image",{staticClass:"w-32 h-34 ml-04",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_coi_gold.png",mode:"widthFix"}})],1),a("v-uni-view",{},[a("v-uni-text",{staticClass:"font-24 text-9"},[t._v("已兑")]),a("v-uni-text",{staticClass:"font-24 text-6"},[t._v(t._s(e.purchased))])],1)],1)],1)],1)})),1),a("u-loadmore",{attrs:{status:t.loadStatus}})],1):a("vh-empty",{attrs:{"padding-top":52,"padding-bottom":400,"image-src":"https://images.vinehoo.com/vinehoomini/v3/empty/emp_goods.png",text:"亲，暂无商品哦~","text-bottom":0}})],1):t._e(),1==t.current?a("v-uni-view",{staticClass:"fade-in p-rela z-03"},[t.couponList.length?a("v-uni-view",{staticClass:"bg-f7f7f7 d-flex flex-column j-center a-center pt-40 pb-20"},[t._l(t.couponList,(function(e,i){return a("v-uni-view",{key:i,staticClass:"p-rela w-686 h-184 d-flex j-sb a-center mb-20"},[a("v-uni-image",{staticClass:"p-abso w-686 h-184",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/mine/rab_cou_bg.png",mode:"aspectFill"}}),a("v-uni-view",{staticClass:"p-rela z-02 d-flex a-center"},[a("v-uni-view",{staticClass:"w-156 d-flex j-center"},[a("v-uni-text",{staticClass:"font-76 font-wei text-e80404"},[a("v-uni-text",{staticClass:"font-24"},[t._v("¥")]),t._v(t._s(e.price))],1)],1),a("v-uni-view",{staticClass:"w-324"},[a("v-uni-view",{staticClass:"font-36 font-wei text-3 text-hidden-1 l-h-40"},[t._v(t._s(e.title))]),a("v-uni-view",{staticClass:"font-22 text-9 text-hidden-1 l-h-36"},[t._v(t._s(e.brief))]),a("v-uni-view",{staticClass:"mt-08 font-24 l-h-36"},[a("v-uni-text",{staticClass:"text-9"},[t._v("已兑")]),a("v-uni-text",{staticClass:"text-6"},[t._v(t._s(e.purchased))])],1)],1)],1),a("v-uni-view",{staticClass:"p-rela z-02 w-202 d-flex flex-column a-center"},[a("v-uni-view",{staticClass:"d-flex a-center"},[a("v-uni-image",{staticClass:"w-32 h-34",attrs:{src:t.ossIcon("/mine/rab_coi_gold.png"),mode:"widthFix"}}),a("v-uni-text",{staticClass:"ml-10 font-36 text-e80404"},[t._v(t._s(e.rabbit_price))])],1),a("v-uni-view",{staticClass:"mt-10"},[a("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"144rpx",height:"58rpx",fontSize:"28rpx",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.toExchange(e)}}},[t._v("去兑换")])],1)],1)],1)})),a("u-loadmore",{attrs:{status:t.loadStatus}})],2):a("vh-empty",{attrs:{"padding-top":52,"padding-bottom":400,"image-src":"https://images.vinehoo.com/vinehoomini/v3/empty/emp_cou.png",text:"亲，暂无优惠券哦~","text-bottom":0}})],1):t._e()],1)],1)},o=[]},bbdc:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},c999:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.rabbit-head-shop__tabs[data-v-2fdad5ca]  .u-tab-item{font-weight:600!important;vertical-align:top}.rabbit-head-shop__tabs[data-v-2fdad5ca]  .u-tab-bar{bottom:auto;background:linear-gradient(214deg,#ff8383,#e70000)}',""]),t.exports=e},d0ff:function(t,e,a){"use strict";a("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return(0,i.default)(t)||(0,n.default)(t)||(0,o.default)(t)||(0,r.default)()};var i=s(a("4053")),n=s(a("a9e0")),o=s(a("dde1")),r=s(a("10eb"));function s(t){return t&&t.__esModule?t:{default:t}}},e4d5:function(t,e,a){"use strict";a.r(e);var i=a("8a04"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},e643:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return i}));var i={uLine:a("9ff7").default,uLoading:a("301a").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"u-load-more-wrap",style:{backgroundColor:t.bgColor,marginBottom:t.marginBottom+"rpx",marginTop:t.marginTop+"rpx",height:t.$u.addUnit(t.height)}},[a("u-line",{attrs:{color:"#d4d4d4",length:"50"}}),a("v-uni-view",{staticClass:"u-load-more-inner",class:"loadmore"==t.status||"nomore"==t.status?"u-more":""},[a("v-uni-view",{staticClass:"u-loadmore-icon-wrap"},[a("u-loading",{staticClass:"u-loadmore-icon",attrs:{color:t.iconColor,mode:"circle"==t.iconType?"circle":"flower",show:"loading"==t.status&&t.icon}})],1),a("v-uni-view",{staticClass:"u-line-1",class:["nomore"==t.status&&1==t.isDot?"u-dot-text":"u-more-text"],style:[t.loadTextStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.loadMore.apply(void 0,arguments)}}},[t._v(t._s(t.showText))])],1),a("u-line",{attrs:{color:"#d4d4d4",length:"50"}})],1)},o=[]},f074:function(t,e,a){"use strict";a.r(e);var i=a("7f1a"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);e["default"]=n.a},f2f9:function(t,e,a){"use strict";var i=a("a126"),n=a.n(i);n.a},fa94:function(t,e,a){"use strict";var i=a("062a"),n=a.n(i);n.a},faa5:function(t,e,a){var i=a("5820");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("54a38be0",i,!0,{sourceMap:!1,shadowMode:!1})},fb12:function(t,e,a){"use strict";var i=a("faa5"),n=a.n(i);n.a}}]);