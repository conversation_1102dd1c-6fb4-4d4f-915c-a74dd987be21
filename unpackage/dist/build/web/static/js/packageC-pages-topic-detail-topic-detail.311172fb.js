(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageC-pages-topic-detail-topic-detail"],{"12c6":function(t,e,a){"use strict";a.r(e);var n=a("51bd"),i=a("f074");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);a("f2f9");var o=a("f0c5"),r=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"519170ec",null,!1,n["a"],void 0);e["default"]=r.exports},"37b9":function(t,e,a){"use strict";a.r(e);var n=a("dcb7"),i=a("387b");for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);var o=a("f0c5"),r=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"7b6ffce2",null,!1,n["a"],void 0);e["default"]=r.exports},"387b":function(t,e,a){"use strict";a.r(e);var n=a("c290"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return n}));var n={uIcon:a("e5e1").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},s=[]},"7f1a":function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var s=a("26cb"),o=uni.getSystemInfoSync(),r={},c={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:r,statusBarHeight:o.statusBarHeight,appStatusBarHeight:0}},computed:(0,i.default)((0,i.default)({},(0,s.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(o.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,n=e.pEAddressManagement,i=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(n)||t.includes(i))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=c},a126:function(t,e,a){var n=a("bbdc");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("4f06").default;i("703d0287",n,!0,{sourceMap:!1,shadowMode:!1})},bbdc:function(t,e,a){var n=a("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},c290:function(t,e,a){"use strict";a("7a82");var n=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("d81d"),a("2ca0"),a("99af"),a("d3b7"),a("159b"),a("c740"),a("e9c4"),a("3ca3"),a("ddb0");var i=n(a("d0ff")),s=n(a("f07e")),o=n(a("c964")),r=n(a("f3f3")),c=a("26cb"),u=n(a("ec3e")),l={computed:(0,r.default)({},(0,c.mapState)(["routeTable","ossPrefix"])),components:{postItemList:u.default},name:"topic-detail",data:function(){return{topicId:"",topicDetail:{title:"",shortdesc:"",image:"",ishot:0,total_posts_num:0,posts_read_num:0,rank:0},postList:[],lastLoginInfo:null,page:1,isLoadMore:!1,hasMore:!0}},onLoad:function(t){var e=this;this.login.isLoginV3(this.$vhFrom,0).then(function(){var t=(0,o.default)((0,s.default)().mark((function t(a){return(0,s.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.lastLoginInfo=a;case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),t.id&&(this.topicId=t.id,this.getTopicDetail(),this.getTopicPosts())},onShow:function(){var t=this;this.login.isLoginV3(this.$vhFrom,0).then(function(){var e=(0,o.default)((0,s.default)().mark((function e(a){var n;return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:n=a,t.lastLoginInfo!==n&&(console.log("登录状态发生变化，刷新数据"),window.location.reload(),t.lastLoginInfo=n,t.topicId&&(t.page=1,t.postList=[],t.hasMore=!0,t.getTopicDetail(),t.getTopicPosts()));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),uni.$on("updateFollowStatus",(function(e){var a=e.uid,n=e.status;t.postList=t.postList.map((function(t){var e;return(null===(e=t.userinfo)||void 0===e?void 0:e.uid)===a?(0,r.default)((0,r.default)({},t),{},{is_attention:n}):t}))}))},onHide:function(){uni.$off("updateFollowStatus")},onReachBottom:function(){this.hasMore&&(this.page++,this.getTopicPosts(!0))},methods:{getTopicDetail:function(){var t=this;return(0,o.default)((0,s.default)().mark((function e(){var a;return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$u.api.getTopicDetail({id:t.topicId});case 3:a=e.sent,0===a.error_code?t.topicDetail=a.data:t.$u.toast(a.error_msg||"获取话题详情失败"),e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](0),console.error("获取话题详情失败:",e.t0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})))()},getTopicPosts:function(){var t=arguments,e=this;return(0,o.default)((0,s.default)().mark((function a(){var n,o,c;return(0,s.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(n=t.length>0&&void 0!==t[0]&&t[0],!e.isLoadMore&&e.hasMore){a.next=3;break}return a.abrupt("return");case 3:return a.prev=3,e.isLoadMore=!0,a.next=7,e.$u.api.topicForList({topic_id:e.topicId,page:e.page,limit:15});case 7:if(o=a.sent,0!==o.error_code){a.next=18;break}return c=o.data.list||[],c=c.map((function(t){return(0,r.default)((0,r.default)({},t),{},{type_data:t.data_image,content:t.data_content,userinfo:(0,r.default)((0,r.default)({},t.user_info),{},{avatar_image:t.user_info.avatar_image.startsWith("http")?t.user_info.avatar_image:e.ossPrefix+t.user_info.avatar_image}),is_attention:t.user_info.status})})),c=c.map((function(t){return t.vine&&(t.wine_data=t.vine),t})),e.postList=n?[].concat((0,i.default)(e.postList),(0,i.default)(c)):c,a.next=15,e.getPostsOtherData(c);case 15:e.hasMore=15===c.length,a.next=19;break;case 18:e.$u.toast(o.error_msg||"获取帖子列表失败");case 19:a.next=24;break;case 21:a.prev=21,a.t0=a["catch"](3),console.error("获取帖子列表失败:",a.t0);case 24:return a.prev=24,e.isLoadMore=!1,a.finish(24);case 27:case"end":return a.stop()}}),a,null,[[3,21,24,27]])})))()},getPostsOtherData:function(t){var e=this;return(0,o.default)((0,s.default)().mark((function a(){var n,o,c;return(0,s.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t&&0!==t.length){a.next=2;break}return a.abrupt("return");case 2:if(a.prev=2,n={data:t.map((function(t){return{id:t.id,source:t.source||(t.wine_data?6:2)}})),start_index:0,end_index:0},n.data[0].id){a.next=6;break}return a.abrupt("return");case 6:return a.next=8,e.$u.api.getOtherData(n);case 8:o=a.sent,0===o.error_code&&o.data.list&&(c=(0,i.default)(e.postList),o.data.list.forEach((function(t){var e=c.findIndex((function(e){return e.id===t.id&&(e.source||(e.wine_data?6:2))===t.source}));-1!==e&&(c[e]=(0,r.default)((0,r.default)({},c[e]),{},{commentnums:t.commentnums,is_digg:t.is_digg}))})),e.postList=c),a.next=15;break;case 12:a.prev=12,a.t0=a["catch"](2),console.error("获取评论数量和点赞状态失败:",a.t0);case 15:case"end":return a.stop()}}),a,null,[[2,12]])})))()},goToSendPost:function(){var t=this;return(0,o.default)((0,s.default)().mark((function e(){var a;return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a={id:t.topicId,title:t.topicDetail.title,allowEmoticonOnly:!0},t.login.isLoginV3(t.$vhFrom,1).then((function(e){e&&t.jump.appAndMiniJump(1,"".concat(t.routeTable.pCSendPost,"?topicData=").concat(encodeURIComponent(JSON.stringify(a))),t.$vhFrom)}));case 2:case"end":return e.stop()}}),e)})))()},onPullDownRefresh:function(){this.page=1,this.hasMore=!0,Promise.all([this.getTopicDetail(),this.getTopicPosts()]).finally((function(){uni.stopPullDownRefresh()}))}}};e.default=l},dcb7:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return n}));var n={vhNavbar:a("12c6").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"content bg-f5f5f5 pb-20"},[a("vh-navbar",{attrs:{"back-icon-color":"#333",title:"话题详情","title-color":"#333"}}),a("v-uni-view",{staticClass:"p-abso w-p100 h-256"},[a("v-uni-image",{staticClass:"w-p100 h-304",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/community/top_det_ban.png",mode:"aspectFill"}})],1),a("v-uni-view",{staticClass:"p-rela z-01 pt-40"},[a("v-uni-view",{staticClass:"bg-ffffff b-rad-20 ml-24 mr-24 pt-32 pr-24 pb-24 pl-24 h-min-240"},[a("v-uni-view",{},[a("v-uni-image",{staticClass:"w-40 h-36",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/top_ico_blu.png",mode:"aspectFill"}}),a("v-uni-text",{staticClass:"ml-10 font-40 font-wei text-3"},[t._v(t._s(t.topicDetail.title))])],1),a("v-uni-view",{staticClass:"mt-20 font-24 text-9 l-h-40"},[t._v("No."+t._s(t.topicDetail.rank)+" 话题榜 ｜ "+t._s(t.topicDetail.posts_read_num)+"阅读")]),a("v-uni-view",{staticClass:"mt-20 font-24 text-6 l-h-40 o-hid text-hidden-2"},[t._v(t._s(t.topicDetail.shortdesc))])],1)],1),a("postItemList",{attrs:{isFollowList:!1,list:t.postList}}),a("v-uni-view",{staticClass:"h-120"}),a("v-uni-view",{staticClass:"p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff d-flex j-center a-center b-sh-00021200-022",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goToSendPost.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"w-702 h-80 bg-f5f5f5 d-flex a-center b-rad-40 pl-24"},[a("v-uni-image",{staticClass:"w-34 h-34",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/comm/wri_gra.png",mode:"aspectFill"}}),a("v-uni-text",{staticClass:"ml-10 font-28 text-9"},[t._v("参与讨论…")])],1)],1)],1)},s=[]},f074:function(t,e,a){"use strict";a.r(e);var n=a("7f1a"),i=a.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);e["default"]=i.a},f2f9:function(t,e,a){"use strict";var n=a("a126"),i=a.n(n);i.a}}]);