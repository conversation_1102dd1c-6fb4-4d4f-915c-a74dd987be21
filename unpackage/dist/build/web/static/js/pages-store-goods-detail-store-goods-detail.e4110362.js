(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-store-goods-detail-store-goods-detail"],{"0792":function(t,e){var n={errorImg:null,filter:null,highlight:null,onText:null,entities:{quot:'"',apos:"'",semi:";",nbsp:" ",ensp:" ",emsp:" ",ndash:"–",mdash:"—",middot:"·",lsquo:"‘",rsquo:"’",ldquo:"“",rdquo:"”",bull:"•",hellip:"…"},blankChar:i(" , ,\t,\r,\n,\f"),boolAttrs:i("allowfullscreen,autoplay,autostart,controls,ignore,loop,muted"),blockTags:i("address,article,aside,body,caption,center,cite,footer,header,html,nav,pre,section"),ignoreTags:i("area,base,canvas,frame,iframe,input,link,map,meta,param,script,source,style,svg,textarea,title,track,wbr"),richOnlyTags:i("a,colgroup,fieldset,legend"),selfClosingTags:i("area,base,br,col,circle,ellipse,embed,frame,hr,img,input,line,link,meta,param,path,polygon,rect,source,track,use,wbr"),trustTags:i("a,abbr,ad,audio,b,blockquote,br,code,col,colgroup,dd,del,dl,dt,div,em,fieldset,h1,h2,h3,h4,h5,h6,hr,i,img,ins,label,legend,li,ol,p,q,source,span,strong,sub,sup,table,tbody,td,tfoot,th,thead,tr,title,ul,video"),userAgentStyles:{address:"font-style:italic",big:"display:inline;font-size:1.2em",blockquote:"background-color:#f6f6f6;border-left:3px solid #dbdbdb;color:#6c6c6c;padding:5px 0 5px 10px",caption:"display:table-caption;text-align:center",center:"text-align:center",cite:"font-style:italic",dd:"margin-left:40px",mark:"background-color:yellow",pre:"font-family:monospace;white-space:pre;overflow:scroll",s:"text-decoration:line-through",small:"display:inline;font-size:0.8em",u:"text-decoration:underline"}};function i(t){for(var e=Object.create(null),n=t.split(","),i=n.length;i--;)e[n[i]]=!0;return e}t.exports=n},"0efb":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"vh-image-con",class:[t.isMf?"mf-card":""],style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():n("v-uni-image",{staticClass:"vh-image",style:{backgroundColor:t.bgColor,borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.getImage,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?n("v-uni-view",{staticClass:"vh-image-loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[n("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e(),t.showError&&t.isError&&!t.loading?n("v-uni-view",{staticClass:"u-image-error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[n("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e()],1)},a=[]},"12c6":function(t,e,n){"use strict";n.r(e);var i=n("51bd"),a=n("f074");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("f2f9");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"519170ec",null,!1,i["a"],void 0);e["default"]=s.exports},1677:function(t,e,n){"use strict";n.r(e);var i=n("f440"),a=n("724e");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"4efb453b",null,!1,i["a"],void 0);e["default"]=s.exports},"2c89":function(t,e,n){"use strict";n.r(e);var i=n("d97b"),a=n("b2cf");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("8d13");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"5189efcc",null,!1,i["a"],void 0);e["default"]=s.exports},"3cdd":function(t,e,n){"use strict";n.r(e);var i=n("dd77"),a=n("b5c2");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("5fdf");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"286f1e63",null,!1,i["a"],void 0);e["default"]=s.exports},"3dc3":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("d0af")),o=i(n("f3f3"));n("a9e3"),n("d3b7"),n("159b"),n("e25e"),n("c975");var r=n("26cb"),s={name:"u-image",props:{loadingType:{type:[String,Number],default:1},isMf:{type:Boolean,default:!1},src:{default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!1},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:100},bgColor:{type:String,default:"transparent"},isResize:{type:Boolean,default:!1},resizeRatio:{type:Object,default:function(){return{wratio:16,hratio:9}}},resizeUsePx:{type:Boolean,default:!1},opacityProp:{type:Number,default:1},backgroundImage:{type:String,default:""}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:(0,o.default)((0,o.default)({},(0,r.mapState)(["ossPrefix"])),{},{wrapStyle:function(){var t={};if(t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),this.backgroundImage&&(t.backgroundImage="url(".concat(this.backgroundImage,")"),t.backgroundSize="100% 100%",t.backgroundRepeat="no-repeat",t.backgroundPosition="center"),this.isResize){var e,n,i=(null===this||void 0===this||null===(e=this.src)||void 0===e||null===(n=e.split("?"))||void 0===n?void 0:n[1])||"",o=i.split("&"),r={};o.forEach((function(t){var e=t.split("="),n=(0,a.default)(e,2),i=n[0],o=n[1];r[i]=o}));var s=+((null===r||void 0===r?void 0:r.w)||""),c=+((null===r||void 0===r?void 0:r.h)||"");if(!isNaN(s)&&!isNaN(c)&&s&&c){var l=parseInt(this.width),d=l/s*c,u=this.resizeRatio,f=u.wratio,p=u.hratio;if("auto"!==f&&"auto"!==p){var h=l*f/p,g=l*p/f;d>h?d=h:d<g&&(d=g)}this.resizeUsePx?t.height="".concat(d,"px"):t.height=this.$u.addUnit(d)}}return t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0||"circle"==this.shape?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t},getLoadingImage:function(){return"https://images.vinehoo.com/vinehoomini/v3/occupy_img/new_img".concat(this.loadingType,".png")},getImage:function(){return"string"!==typeof this.src?"":-1==this.src.indexOf("http")?this.ossPrefix+this.src:this.src}}),methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=t.opacityProp,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=s},"44c3":function(t,e,n){var i=n("e6a9");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("0228a067",i,!0,{sourceMap:!1,shadowMode:!1})},"51bd":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uIcon:n("e5e1").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),n("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?n("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[n("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?n("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?n("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},o=[]},5307:function(t,e,n){"use strict";n.r(e);var i=n("7193"),a=n("8c83");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("fb12");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"34b198da",null,!1,i["a"],void 0);e["default"]=s.exports},"54f8":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){var n="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=(0,i.default)(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var a=0,o=function(){};return{s:o,n:function(){return a>=t.length?{done:!0}:{done:!1,value:t[a++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,s=!0,c=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return s=t.done,t},e:function(t){c=!0,r=t},f:function(){try{s||null==n["return"]||n["return"]()}finally{if(c)throw r}}}},n("a4d3"),n("e01a"),n("d3b7"),n("d28b"),n("3ca3"),n("ddb0"),n("d9e2"),n("d401");var i=function(t){return t&&t.__esModule?t:{default:t}}(n("dde1"))},5820:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-swiper-wrap[data-v-34b198da]{position:relative;overflow:hidden;-webkit-transform:translateY(0);transform:translateY(0)}.vh-swiper-item[data-v-34b198da]{display:flex;overflow:hidden;align-items:center}.vh-list-image-wrap[data-v-34b198da]{width:100%;height:100%;flex:1;transition:all .5s;overflow:hidden;box-sizing:initial;position:relative}.vh-list-scale[data-v-34b198da]{-webkit-transform-origin:center center;transform-origin:center center}.vh-swiper-image[data-v-34b198da]{width:100%;will-change:transform;height:100%;display:block;pointer-events:none}.vh-swiper-border[data-v-34b198da]{position:absolute;z-index:10;left:0;top:0;width:100%;height:100%}.vh-swiper-title[data-v-34b198da]{position:absolute;background-color:rgba(0,0,0,.3);bottom:0;left:0;width:100%;font-size:%?28?%;padding:%?12?% %?24?%;color:hsla(0,0%,100%,.9)}.vh-swiper-indicator[data-v-34b198da]{padding:0 %?24?%;position:absolute;display:flex;width:100%;z-index:1}.vh-indicator-item-rect[data-v-34b198da]{width:%?26?%;height:%?8?%;margin:0 %?6?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.vh-indicator-item-rect-active[data-v-34b198da]{background-color:hsla(0,0%,100%,.8)}.vh-indicator-item-dot[data-v-34b198da]{width:%?14?%;height:%?14?%;margin:0 %?6?%;border-radius:%?20?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.vh-indicator-item-dot-active[data-v-34b198da]{background-color:hsla(0,0%,100%,.8)}.vh-indicator-item-round[data-v-34b198da]{width:%?14?%;height:%?14?%;margin:0 %?6?%;border-radius:%?20?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.vh-indicator-item-round-active[data-v-34b198da]{width:%?34?%;background-color:hsla(0,0%,100%,.8)}.vh-indicator-item-number[data-v-34b198da]{padding:%?6?% %?16?%;line-height:1;background-color:rgba(0,0,0,.3);border-radius:%?100?%;font-size:%?26?%;color:hsla(0,0%,100%,.8)}',""]),t.exports=e},"5fdf":function(t,e,n){"use strict";var i=n("44c3"),a=n.n(i);a.a},"62e7":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("e25e"),n("4de4"),n("d3b7"),n("c975"),n("159b"),n("14d9");var a=i(n("54f8")),o=i(n("f07e")),r=i(n("c964")),s=i(n("f3f3")),c=i(n("97d6")),l=n("26cb"),d={name:"store-goods-detail",data:function(){return{loading:!0,osip:"https://images.vinehoo.com/vinehoomini/v3",amapPlugin:null,key:"6b15b01a3a5ed85f0e66797201febf92",nearTheStore:!0,mSid:"",goodsId:"",userLatitude:"",userLongitude:"",goodsInfo:{},clickSelectPackage:0,packageIndex:0,packageList:[],packageInfo:{},purchaseNumbers:1,distributionId:0,hasSelfMention:0,hasFieldDrink:0,commentList:[],isAddShoppingCart:!1,showGoodsPackPop:!1}},computed:(0,s.default)({},(0,l.mapState)(["storeInfo","storeOrderInfo","routeTable"])),onLoad:function(t){this.init(t)},methods:(0,s.default)((0,s.default)({},(0,l.mapMutations)(["muStoreInfo","muStoreOrderInfo"])),{},{init:function(t){var e=this;return(0,r.default)((0,o.default)().mark((function n(){var i;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:console.log(t),t.gid?(console.log("------------小程序内部扫码"),e.goodsId=parseInt(t.gid),e.searchWifi()):t.q?(console.log("------------微信扫码"),i=decodeURIComponent(t.q),console.log(i),e.goodsId=parseInt(e.param.getUrlParam(i,"gid")),e.searchWifi()):(console.log("------------商品列表进入"),e.goodsId=parseInt(t.id),e.getStoreGoodsDetail());case 2:case"end":return n.stop()}}),n)})))()},searchWifi:function(){var t=this;wx.startWifi({success:function(e){wx.getConnectedWifi({success:function(e){t.getStoreList(0,e.wifi.SSID)},fail:function(e){t.feedback.toast({title:"连接wifi失败, 我需要走定位"}),t.position()}})},fail:function(e){t.feedback.toast({title:"我没有wifi, 我需要走定位"}),t.position()}})},position:function(){var t=this;uni.getSystemInfo({success:function(e){console.log(e),e.locationEnabled?uni.authorize({scope:"scope.userLocation",success:function(e){t.feedback.toast({title:"我允许了定位授权，开始获取经纬度"}),t.mGetLocation()},fail:function(e){uni.showModal({content:"检测到您没打开获取位置功能权限，无法获取更多服务，是否去设置打开？",confirmText:"确认",cancelText:"取消",success:function(e){e.confirm?uni.openSetting({success:function(e){t.feedback.toast({title:"我从不允许打开定位到允许了定位授权,获取经纬度"}),t.mGetLocation()}}):t.feedback.toast({title:"我弹框点击的还是不允许定位授权"})}})}}):t.feedback.toast({title:"请打开定位~"})}})},getStoreList:function(){var t=arguments,e=this;return(0,r.default)((0,o.default)().mark((function n(){var i,a,r,s;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return i=t.length>0&&void 0!==t[0]?t[0]:1,a=t.length>1&&void 0!==t[1]?t[1]:"",n.next=4,e.$u.api.storeList();case 4:r=n.sent,s=r.data.list,s.length?(e.mSid=0==i?e.mGetStoreIdByWifi(s,a):e.mGetStoreIdByLocation(s),e.mSid?e.mStoreGoodsInfo():(e.nearTheStore=!1,e.feedback.toast({title:"附近没有搜索到门店呀！我得显示无门店占位图"}))):e.feedback.toast({title:"请配置门店！"});case 7:case"end":return n.stop()}}),n)})))()},mGetLocation:function(){var t=this;try{this.amapPlugin=new c.default.AMapWX({key:this.key}),this.amapPlugin.getRegeo({success:function(e){var n=e[0],i=n.latitude,a=n.longitude;t.userLatitude=i,t.userLongitude=a,console.log("当前位置的经度（longitude）："+e[0].longitude),console.log("当前位置的纬度（latitude）："+e[0].latitude),t.getStoreList()},fail:function(e){t.feedback.toast({title:"amapPlugin.getRegeo失败！"})}})}catch(e){this.feedback.toast({title:"初始化amap失败，请排查错误！"})}},mGetStoreIdByWifi:function(t,e){console.log(t),console.log(e);for(var n="",i=0;i<t.length;i++)for(var a=0;a<t[i].wifilocation.length;a++)if(t[i].wifilocation[a]===e){n=t[i].id;break}return n},mGetStoreIdByLocation:function(t){var e="";console.log(t);var n,i=(0,a.default)(t);try{for(i.s();!(n=i.n()).done;){var o=n.value;if(this.gps.lonAndLatDis(29.59555201,106.48984702,o.latitude,o.longitude)<=o.radius){e=o.id;break}}}catch(r){i.e(r)}finally{i.f()}return e},mStoreGoodsInfo:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var n;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$u.api.storeInfo({sid:t.mSid});case 2:n=e.sent,t.muStoreInfo(n.data),t.getStoreGoodsDetail(),t.storeAddRegister();case 6:case"end":return e.stop()}}),e)})))()},storeAddRegister:function(){var t=this;uni.getStorage({key:"loginInfo",success:function(){var e=(0,r.default)((0,o.default)().mark((function e(n){var i,a,r,s,c,l;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,i=n.data,a=i.uid,r=i.telephone,s=i.nickname,c=i.avatar_image,l=i.openid,e.next=4,t.$u.api.storeAddRegister({uid:a,loginname:r,nickname:s,avatar_image:c,applet_openid:l,sid:t.storeInfo.id});case 4:e.next=8;break;case 6:e.prev=6,e.t0=e["catch"](0);case 8:case"end":return e.stop()}}),e,null,[[0,6]])})));return function(t){return e.apply(this,arguments)}}(),fail:function(t){console.log("-------------我没有用户信息")}})},getStoreGoodsDetail:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){var n,i;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t.$u.api.storeGoodsDetail({gid:t.goodsId});case 3:n=e.sent,i=n.data.collocation,t.goodsInfo=n.data,t.packageList=i.filter((function(t){return 1==t.is_enable})),t.packageInfo=t.packageList[t.packageIndex],t.judgeDistributionType(),t.loading=!1,console.log(n),e.next=15;break;case 13:e.prev=13,e.t0=e["catch"](0);case 15:case"end":return e.stop()}}),e,null,[[0,13]])})))()},openScan:function(){var t=this;uni.scanCode({success:function(e){if(e.result.indexOf("gid")>-1){var n=e.result;t.goodsId=parseInt(t.param.getUrlParam(n,"gid")),t.searchWifi()}else t.feedback.toast({title:"请扫描瓶身上的二维码喔~",duration:3e3})}})},judgeDistributionType:function(){var t=this;console.log("----------------------我是门店信息"),console.log(this.storeInfo),this.storeInfo.purchase_mode_list.forEach((function(e){2==e.id&&(t.hasSelfMention=1),3==e.id&&(t.hasFieldDrink=1)}))},addShoppingCart:function(){this.login.isLogin()&&(this.purchaseNumbers=1,this.isAddShoppingCart=!0,this.showGoodsPackPop=!0)},confirmAddShoppingCart:function(){var t=this;return(0,r.default)((0,o.default)().mark((function e(){return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log("-------------------我是确认加入购物车"),e.prev=1,e.next=4,t.$u.api.storeGoodsAddShoppingCart({goods_id:t.goodsId,pack_id:t.packageInfo.id,nums:t.purchaseNumbers});case 4:e.sent,t.feedback.toast({title:"加入成功",icon:"success"}),e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](1),console.log(e.t0);case 11:case"end":return e.stop()}}),e,null,[[1,8]])})))()},selectPackage:function(t){this.clickSelectPackage=1,this.packageIndex=t,this.packageInfo=this.packageList[this.packageIndex]},buyNow:function(t){this.login.isLogin()&&(this.purchaseNumbers=1,this.distributionId=t,this.packageIndex=0,this.isAddShoppingCart=!1,this.clickSelectPackage=0,this.showGoodsPackPop=!0)},confirmBuyNow:function(){console.log("-----------------------------我是立即购买");var t={order_source:2,distribution_id:this.distributionId,storeOrderGoodsList:[]},e=this.goodsInfo,n=e.goods_images,i=e.goods_name,a=e.is_cup,o=this.packageInfo,r=o.id,s=o.c_name,c=o.goods_id,l={};l.goods_name=i,l.goods_image=n[0],l.goods_id=c,l.pack_id=r,l.c_name=s,l.nums=this.purchaseNumbers,l.is_cup=a,t.storeOrderGoodsList.push(l),this.muStoreOrderInfo(t),this.jump.navigateTo("/packageB/pages/store-order-confirm/store-order-confirm")},confirm:function(){if(0==this.packageList.length)return this.feedback.toast({title:"请选择套餐~"});this.showGoodsPackPop=!1,this.isAddShoppingCart?this.confirmAddShoppingCart():this.confirmBuyNow()}})};e.default=d},"6ab5":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-image-con[data-v-89d76102]{position:relative;transition:opacity .5s ease-in}.vh-image[data-v-89d76102]{width:100%;height:100%}.vh-image-loading[data-v-89d76102],\n.u-image-error[data-v-89d76102]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#fff;color:#909399;font-size:%?46?%}.mf-card[data-v-89d76102]{background-color:#f7f7f7!important;padding:5px}',""]),t.exports=e},7193:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={vhImage:n("ce7c").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"vh-swiper-wrap",style:{borderRadius:t.borderRadius+"rpx"}},[n("v-uni-swiper",{style:{height:t.height+"rpx",backgroundColor:t.bgColor},attrs:{current:t.elCurrent,interval:t.interval,circular:t.circular,duration:t.duration,autoplay:t.autoplay,"previous-margin":t.effect3d?t.effect3dPreviousMargin+"rpx":"0","next-margin":t.effect3d?t.effect3dPreviousMargin+"rpx":"0"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)},animationfinish:function(e){arguments[0]=e=t.$handleEvent(e),t.animationfinish.apply(void 0,arguments)}}},t._l(t.list,(function(e,i){return n("v-uni-swiper-item",{key:i,staticClass:"vh-swiper-item"},[n("v-uni-view",{staticClass:"vh-list-image-wrap",class:[t.uCurrent!=i?"vh-list-scale":""],style:{borderRadius:t.borderRadius+"rpx",transform:t.effect3d&&t.uCurrent!=i?"scaleY(0.9)":"scaleY(1)",margin:t.effect3d&&t.uCurrent!=i?"0 20rpx":0},on:{click:function(n){n.stopPropagation(),n.preventDefault(),arguments[0]=n=t.$handleEvent(n),t.listClick(e)}}},[n("vh-image",{attrs:{"loading-type":t.loadingType,src:e[t.name]||e,height:t.height,mode:t.imgMode}}),t.borderImage&&0==i?n("v-uni-image",{staticClass:"vh-swiper-border",attrs:{src:t.borderImage,mode:t.imgMode}}):t._e(),t.title&&e.title?n("v-uni-view",{staticClass:"vh-swiper-title",style:[{"padding-bottom":t.titlePaddingBottom},t.titleStyle]},[t._v(t._s(e.title))]):t._e()],1)],1)})),1),t.isShowIndicator?n("v-uni-view",{staticClass:"vh-swiper-indicator",style:{top:"topLeft"==t.indicatorPos||"topCenter"==t.indicatorPos||"topRight"==t.indicatorPos?"12rpx":"auto",bottom:"bottomLeft"==t.indicatorPos||"bottomCenter"==t.indicatorPos||"bottomRight"==t.indicatorPos?t.indicatorBottom+"rpx":"auto",justifyContent:t.justifyContent,padding:"0 "+(t.effect3d?"74rpx":"24rpx")}},["rect"==t.mode?t._l(t.list,(function(e,i){return n("v-uni-view",{key:i,staticClass:"vh-indicator-item-rect",class:{"vh-indicator-item-rect-active":i==t.uCurrent}})})):t._e(),"dot"==t.mode?t._l(t.list,(function(e,i){return t.customDot?n("v-uni-view",{key:i,style:[i==t.uCurrent?t.customDotStyle.active:t.customDotStyle.default]}):t._l(t.list,(function(e,i){return n("v-uni-view",{key:i,staticClass:"vh-indicator-item-dot",class:{"vh-indicator-item-dot-active":i==t.uCurrent}})}))})):t._e(),"round"==t.mode?t._l(t.list,(function(e,i){return n("v-uni-view",{key:i,staticClass:"vh-indicator-item-round",class:{"vh-indicator-item-round-active":i==t.uCurrent}})})):t._e(),"number"==t.mode?[n("v-uni-view",{staticClass:"vh-indicator-item-number"},[t._v(t._s(t.uCurrent+1)+"/"+t._s(t.list.length))])]:t._e()],2):t._e()],1)},o=[]},"724e":function(t,e,n){"use strict";n.r(e);var i=n("820d"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"7f1a":function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("f3f3"));n("a9e3"),n("caad6"),n("2532");var o=n("26cb"),r=uni.getSystemInfoSync(),s={},c={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:s,statusBarHeight:r.statusBarHeight,appStatusBarHeight:0}},computed:(0,a.default)((0,a.default)({},(0,o.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,n=e.pEAddressAdd,i=e.pEAddressManagement,a=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(n)||t.includes(i)||t.includes(a))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=c},"820d":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"vh-gap",props:{bgColor:{type:String,default:"transparent"},height:{type:[String,Number],default:30}},computed:{gapStyle:function(){return{backgroundColor:this.bgColor,height:this.height+"rpx"}}}};e.default=i},8907:function(t,e,n){"use strict";n("7a82");var i=n("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("54f8"));n("c975"),n("caad6"),n("2532"),n("ac1f"),n("466d"),n("841c"),n("5319"),n("e25e"),n("14d9"),n("99af"),n("acd8");var o=uni.getSystemInfoSync(),r=o.windowWidth,s=(o.platform,n("0792")),c={name:"parser",data:function(){return{uid:this._uid,showAm:"",nodes:[]}},props:{html:String,autopause:{type:Boolean,default:!0},autoscroll:Boolean,autosetTitle:{type:Boolean,default:!0},domain:String,lazyLoad:Boolean,selectable:Boolean,tagStyle:Object,showWithAnimation:Boolean,useAnchor:Boolean},watch:{html:function(t){this.setContent(t)}},created:function(){this.imgList=[],this.imgList.each=function(t){for(var e=0,n=this.length;e<n;e++)this.setItem(e,t(this[e],e,this))},this.imgList.setItem=function(t,e){if(void 0!=t&&e){if(0==e.indexOf("http")&&this.includes(e)){for(var n,i=e.split("://")[0],a=i.length;n=e[a];a++){if("/"==n&&"/"!=e[a-1]&&"/"!=e[a+1])break;i+=Math.random()>.5?n.toUpperCase():n}return i+=e.substr(a),this[t]=i}if(this[t]=e,e.includes("data:image")){var o=e.match(/data:image\/(\S+?);(\S+?),(.+)/);if(!o)return}}}},mounted:function(){this.document=document.getElementById("rtf"+this._uid),this.html&&this.setContent(this.html)},beforeDestroy:function(){this._observer&&this._observer.disconnect(),this.imgList.each((function(t){})),clearInterval(this._timer)},methods:{setContent:function(t,e){var n=this;if(t){var i=document.createElement("div");e?this.rtf?this.rtf.appendChild(i):this.rtf=i:(this.rtf&&this.rtf.parentNode.removeChild(this.rtf),this.rtf=i),i.innerHTML=this._handleHtml(t,e);for(var o,c=this.rtf.getElementsByTagName("style"),l=0;o=c[l++];)o.innerHTML=o.innerHTML.replace(/body/g,"#rtf"+this._uid),o.setAttribute("scoped","true");!this._observer&&this.lazyLoad&&IntersectionObserver&&(this._observer=new IntersectionObserver((function(t){for(var e,i=0;e=t[i++];)e.isIntersecting&&(e.target.src=e.target.getAttribute("data-src"),e.target.removeAttribute("data-src"),n._observer.unobserve(e.target))}),{rootMargin:"500px 0px 500px 0px"}));var d=this,u=this.rtf.getElementsByTagName("title");u.length&&this.autosetTitle&&uni.setNavigationBarTitle({title:u[0].innerText});var f=function(t){var e=t.getAttribute("src");n.domain&&e&&("/"==e[0]?"/"==e[1]?t.src=(n.domain.includes("://")?n.domain.split("://")[0]:"")+":"+e:t.src=n.domain+e:e.includes("://")||0==e.indexOf("data:")||(t.src=n.domain+"/"+e))};this.imgList.length=0;for(var p,h=this.rtf.getElementsByTagName("img"),g=0,v=0;p=h[g];g++)parseInt(p.style.width||p.getAttribute("width"))>r&&(p.style.height="auto"),f(p),p.hasAttribute("ignore")||"A"==p.parentElement.nodeName||(p.i=v++,d.imgList.push(p.getAttribute("original-src")||p.src||p.getAttribute("data-src")),p.onclick=function(t){t.stopPropagation();var e=!0;this.ignore=function(){return e=!1},d.$emit("imgtap",this),e&&uni.previewImage({current:this.i,urls:d.imgList})}),p.onerror=function(){s.errorImg&&(d.imgList[this.i]=this.src=s.errorImg),d.$emit("error",{source:"img",target:this})},d.lazyLoad&&this._observer&&p.src&&0!=p.i&&(p.setAttribute("data-src",p.src),p.removeAttribute("src"),this._observer.observe(p));var m,b=this.rtf.getElementsByTagName("a"),y=(0,a.default)(b);try{for(y.s();!(m=y.n()).done;){var x=m.value;x.onclick=function(t){t.stopPropagation();var e=!0,n=this.getAttribute("href");if(d.$emit("linkpress",{href:n,ignore:function(){return e=!1}}),e&&n)if("#"==n[0])d.useAnchor&&d.navigateTo({id:n.substr(1)});else{if(0==n.indexOf("http")||0==n.indexOf("//"))return!0;uni.navigateTo({url:n})}return!1}}}catch(N){y.e(N)}finally{y.f()}var w=this.rtf.getElementsByTagName("video");d.videoContexts=w;for(var k,_=0;k=w[_++];)f(k),k.style.maxWidth="100%",k.onerror=function(){d.$emit("error",{source:"video",target:this})},k.onplay=function(){if(d.autopause)for(var t,e=0;t=d.videoContexts[e++];)t!=this&&t.pause()};var C,S,I=this.rtf.getElementsByTagName("audio"),F=(0,a.default)(I);try{for(F.s();!(C=F.n()).done;){var B=C.value;f(B),B.onerror=function(){d.$emit("error",{source:"audio",target:this})}}}catch(N){F.e(N)}finally{F.f()}if(this.autoscroll){var T,P=this.rtf.getElementsByTagName("table"),E=(0,a.default)(P);try{for(E.s();!(T=E.n()).done;){var L=T.value,M=document.createElement("div");M.style.overflow="scroll",L.parentNode.replaceChild(M,L),M.appendChild(L)}}catch(N){E.e(N)}finally{E.f()}}e||this.document.appendChild(this.rtf),this.$nextTick((function(){n.nodes=[1],n.$emit("load")})),setTimeout((function(){return n.showAm=""}),500),clearInterval(this._timer),this._timer=setInterval((function(){n.rect=n.rtf.getBoundingClientRect(),n.rect.height==S&&(n.$emit("ready",n.rect),clearInterval(n._timer)),S=n.rect.height}),350),this.showWithAnimation&&!e&&(this.showAm="animation:_show .5s")}else this.rtf&&!e&&this.rtf.parentNode.removeChild(this.rtf)},getText:function(){arguments.length>0&&void 0!==arguments[0]||this.nodes;var t="";return t=this.rtf.innerText,t},in:function(t){t.page&&t.selector&&t.scrollTop&&(this._in=t)},navigateTo:function(t){var e=this;if(!this.useAnchor)return t.fail&&t.fail("Anchor is disabled");var n=uni.createSelectorQuery().in(this._in?this._in.page:this).select((this._in?this._in.selector:"#_top")+(t.id?"".concat(" ","#").concat(t.id,",").concat(this._in?this._in.selector:"#_top").concat(" ",".").concat(t.id):"")).boundingClientRect();this._in?n.select(this._in.selector).scrollOffset().select(this._in.selector).boundingClientRect():n.selectViewport().scrollOffset(),n.exec((function(n){if(!n[0])return t.fail&&t.fail("Label not found");var i=n[1].scrollTop+n[0].top-(n[2]?n[2].top:0)+(t.offset||0);e._in?e._in.page[e._in.scrollTop]=i:uni.pageScrollTo({scrollTop:i,duration:300}),t.success&&t.success()}))},getVideoContext:function(t){if(!t)return this.videoContexts;for(var e=this.videoContexts.length;e--;)if(this.videoContexts[e].id==t)return this.videoContexts[e]},_handleHtml:function(t,e){if(!e){var n="<style scoped>@keyframes _show{0%{opacity:0}100%{opacity:1}}img{max-width:100%}";for(var i in s.userAgentStyles)n+="".concat(i,"{").concat(s.userAgentStyles[i],"}");for(i in this.tagStyle)n+="".concat(i,"{").concat(this.tagStyle[i],"}");n+="</style>",t=n+t}return t.includes("rpx")&&(t=t.replace(/[0-9.]+\s*rpx/g,(function(t){return parseFloat(t)*r/750+"px"}))),t}}};e.default=c},8927:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3"),n("c975");var i={name:"vh-swiper",props:{loadingType:{type:[String,Number],default:1},list:{type:Array,default:function(){return[]}},borderImage:{type:String,default:""},title:{type:Boolean,default:!1},indicator:{type:Object,default:function(){return{}}},borderRadius:{type:[Number,String],default:8},interval:{type:[String,Number],default:3e3},mode:{type:String,default:"round"},customDot:{type:Boolean,default:!1},customDotStyle:{type:Object,default:function(){return{default:{width:"10rpx",height:"10rpx",borderRadius:"10rpx",margin:"0 6rpx",transition:"all 0.5s",backgroundColor:"#DDDDDD"},active:{width:"10rpx",height:"10rpx",borderRadius:"10rpx",margin:"0 6rpx",transition:"all 0.5s",backgroundColor:"#E80404"}}}},height:{type:[Number,String],default:250},indicatorPos:{type:String,default:"bottomCenter"},indicatorBottom:{type:[String,Number],default:16},effect3d:{type:Boolean,default:!1},effect3dPreviousMargin:{type:[Number,String],default:50},autoplay:{type:Boolean,default:!0},duration:{type:[Number,String],default:500},circular:{type:Boolean,default:!0},imgMode:{type:String,default:"aspectFill"},name:{type:String,default:"image"},current:{type:[Number,String],default:0},bgColor:{type:String,default:"#FFFFFF"},titleStyle:{type:Object,default:function(){return{}}},isShowIndicator:{type:Boolean,default:!0}},watch:{list:function(t,e){t.length!==e.length&&(this.uCurrent=0)},current:function(t){this.uCurrent=t}},data:function(){return{uCurrent:this.current}},computed:{justifyContent:function(){return"topLeft"==this.indicatorPos||"bottomLeft"==this.indicatorPos?"flex-start":"topCenter"==this.indicatorPos||"bottomCenter"==this.indicatorPos?"center":"topRight"==this.indicatorPos||"bottomRight"==this.indicatorPos?"flex-end":void 0},titlePaddingBottom:function(){var t=0;return"none"==this.mode?"12rpx":(t=["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"==this.mode?"60rpx":["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"!=this.mode?"40rpx":"12rpx",t)},elCurrent:function(){return Number(this.current)}},methods:{listClick:function(t){this.$emit("click",t)},change:function(t){var e=t.detail.current;this.uCurrent=e,this.$emit("change",e)},animationfinish:function(t){}}};e.default=i},8987:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,"@-webkit-keyframes _show-data-v-5189efcc{0%{opacity:0}100%{opacity:1}}@keyframes _show-data-v-5189efcc{0%{opacity:0}100%{opacity:1}}\n\n\n\n",""]),t.exports=e},"8c83":function(t,e,n){"use strict";n.r(e);var i=n("8927"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},"8d13":function(t,e,n){"use strict";var i=n("f157"),a=n.n(i);a.a},"97d6":function(t,e,n){function i(t){this.key=t.key,this.requestConfig={key:t.key,s:"rsx",platform:"WXJS",appname:t.key,sdkversion:"1.2.0",logversion:"2.0"}}n("acd8"),n("14d9"),i.prototype.getWxLocation=function(t,e){wx.getLocation({type:"gcj02",success:function(t){var n=t.longitude+","+t.latitude;wx.setStorage({key:"userLocation",data:n}),e(n)},fail:function(n){wx.getStorage({key:"userLocation",success:function(t){t.data&&e(t.data)}}),t.fail({errCode:"0",errMsg:n.errMsg||""})}})},i.prototype.getRegeo=function(t){function e(e){var i=n.requestConfig;wx.request({url:"https://restapi.amap.com/v3/geocode/regeo",data:{key:n.key,location:e,extensions:"all",s:i.s,platform:i.platform,appname:n.key,sdkversion:i.sdkversion,logversion:i.logversion},method:"GET",header:{"content-type":"application/json"},success:function(n){var i,a,o,r,s,c,l,d,u;n.data.status&&"1"==n.data.status?(i=n.data.regeocode,a=i.addressComponent,o=[],r="",i&&i.roads[0]&&i.roads[0].name&&(r=i.roads[0].name+"附近"),s=e.split(",")[0],c=e.split(",")[1],i.pois&&i.pois[0]&&(r=i.pois[0].name+"附近",l=i.pois[0].location,l&&(s=parseFloat(l.split(",")[0]),c=parseFloat(l.split(",")[1]))),a.provice&&o.push(a.provice),a.city&&o.push(a.city),a.district&&o.push(a.district),a.streetNumber&&a.streetNumber.street&&a.streetNumber.number?(o.push(a.streetNumber.street),o.push(a.streetNumber.number)):(d="",i&&i.roads[0]&&i.roads[0].name&&(d=i.roads[0].name),o.push(d)),o=o.join(""),u=[{iconPath:t.iconPath,width:t.iconWidth,height:t.iconHeight,name:o,desc:r,longitude:s,latitude:c,id:0,regeocodeData:i}],t.success(u)):t.fail({errCode:n.data.infocode,errMsg:n.data.info})},fail:function(e){t.fail({errCode:"0",errMsg:e.errMsg||""})}})}var n=this;t.location?e(t.location):n.getWxLocation(t,(function(t){e(t)}))},i.prototype.getWeather=function(t){function e(e){var n="base";t.type&&"forecast"==t.type&&(n="all"),wx.request({url:"https://restapi.amap.com/v3/weather/weatherInfo",data:{key:i.key,city:e,extensions:n,s:a.s,platform:a.platform,appname:i.key,sdkversion:a.sdkversion,logversion:a.logversion},method:"GET",header:{"content-type":"application/json"},success:function(e){var n,i;e.data.status&&"1"==e.data.status?e.data.lives?(n=e.data.lives,n&&n.length>0&&(n=n[0],i=function(t){var e={city:{text:"城市",data:t.city},weather:{text:"天气",data:t.weather},temperature:{text:"温度",data:t.temperature},winddirection:{text:"风向",data:t.winddirection+"风"},windpower:{text:"风力",data:t.windpower+"级"},humidity:{text:"湿度",data:t.humidity+"%"}};return e}(n),i["liveData"]=n,t.success(i))):e.data.forecasts&&e.data.forecasts[0]&&t.success({forecast:e.data.forecasts[0]}):t.fail({errCode:e.data.infocode,errMsg:e.data.info})},fail:function(e){t.fail({errCode:"0",errMsg:e.errMsg||""})}})}function n(n){wx.request({url:"https://restapi.amap.com/v3/geocode/regeo",data:{key:i.key,location:n,extensions:"all",s:a.s,platform:a.platform,appname:i.key,sdkversion:a.sdkversion,logversion:a.logversion},method:"GET",header:{"content-type":"application/json"},success:function(n){var i,a;n.data.status&&"1"==n.data.status?(a=n.data.regeocode,a.addressComponent?i=a.addressComponent.adcode:a.aois&&a.aois.length>0&&(i=a.aois[0].adcode),e(i)):t.fail({errCode:n.data.infocode,errMsg:n.data.info})},fail:function(e){t.fail({errCode:"0",errMsg:e.errMsg||""})}})}var i=this,a=i.requestConfig;t.city?e(t.city):i.getWxLocation(t,(function(t){n(t)}))},i.prototype.getPoiAround=function(t){function e(e){var a={key:n.key,location:e,s:i.s,platform:i.platform,appname:n.key,sdkversion:i.sdkversion,logversion:i.logversion};t.querytypes&&(a["types"]=t.querytypes),t.querykeywords&&(a["keywords"]=t.querykeywords),wx.request({url:"https://restapi.amap.com/v3/place/around",data:a,method:"GET",header:{"content-type":"application/json"},success:function(e){var n,i,a,o;if(e.data.status&&"1"==e.data.status){if(e=e.data,e&&e.pois){for(n=[],i=0;i<e.pois.length;i++)a=0==i?t.iconPathSelected:t.iconPath,n.push({latitude:parseFloat(e.pois[i].location.split(",")[1]),longitude:parseFloat(e.pois[i].location.split(",")[0]),iconPath:a,width:22,height:32,id:i,name:e.pois[i].name,address:e.pois[i].address});o={markers:n,poisData:e.pois},t.success(o)}}else t.fail({errCode:e.data.infocode,errMsg:e.data.info})},fail:function(e){t.fail({errCode:"0",errMsg:e.errMsg||""})}})}var n=this,i=n.requestConfig;t.location?e(t.location):n.getWxLocation(t,(function(t){e(t)}))},i.prototype.getStaticmap=function(t){function e(e){a.push("location="+e),t.zoom&&a.push("zoom="+t.zoom),t.size&&a.push("size="+t.size),t.scale&&a.push("scale="+t.scale),t.markers&&a.push("markers="+t.markers),t.labels&&a.push("labels="+t.labels),t.paths&&a.push("paths="+t.paths),t.traffic&&a.push("traffic="+t.traffic);var n=o+a.join("&");t.success({url:n})}var n,i=this,a=[],o="https://restapi.amap.com/v3/staticmap?";a.push("key="+i.key),n=i.requestConfig,a.push("s="+n.s),a.push("platform="+n.platform),a.push("appname="+n.appname),a.push("sdkversion="+n.sdkversion),a.push("logversion="+n.logversion),t.location?e(t.location):i.getWxLocation(t,(function(t){e(t)}))},i.prototype.getInputtips=function(t){var e=this,n=e.requestConfig,i={key:e.key,s:n.s,platform:n.platform,appname:e.key,sdkversion:n.sdkversion,logversion:n.logversion};t.location&&(i["location"]=t.location),t.keywords&&(i["keywords"]=t.keywords),t.type&&(i["type"]=t.type),t.city&&(i["city"]=t.city),t.citylimit&&(i["citylimit"]=t.citylimit),wx.request({url:"https://restapi.amap.com/v3/assistant/inputtips",data:i,method:"GET",header:{"content-type":"application/json"},success:function(e){e&&e.data&&e.data.tips&&t.success({tips:e.data.tips})},fail:function(e){t.fail({errCode:"0",errMsg:e.errMsg||""})}})},i.prototype.getDrivingRoute=function(t){var e=this,n=e.requestConfig,i={key:e.key,s:n.s,platform:n.platform,appname:e.key,sdkversion:n.sdkversion,logversion:n.logversion};t.origin&&(i["origin"]=t.origin),t.destination&&(i["destination"]=t.destination),t.strategy&&(i["strategy"]=t.strategy),t.waypoints&&(i["waypoints"]=t.waypoints),t.avoidpolygons&&(i["avoidpolygons"]=t.avoidpolygons),t.avoidroad&&(i["avoidroad"]=t.avoidroad),wx.request({url:"https://restapi.amap.com/v3/direction/driving",data:i,method:"GET",header:{"content-type":"application/json"},success:function(e){e&&e.data&&e.data.route&&t.success({paths:e.data.route.paths,taxi_cost:e.data.route.taxi_cost||""})},fail:function(e){t.fail({errCode:"0",errMsg:e.errMsg||""})}})},i.prototype.getWalkingRoute=function(t){var e=this,n=e.requestConfig,i={key:e.key,s:n.s,platform:n.platform,appname:e.key,sdkversion:n.sdkversion,logversion:n.logversion};t.origin&&(i["origin"]=t.origin),t.destination&&(i["destination"]=t.destination),wx.request({url:"https://restapi.amap.com/v3/direction/walking",data:i,method:"GET",header:{"content-type":"application/json"},success:function(e){e&&e.data&&e.data.route&&t.success({paths:e.data.route.paths})},fail:function(e){t.fail({errCode:"0",errMsg:e.errMsg||""})}})},i.prototype.getTransitRoute=function(t){var e=this,n=e.requestConfig,i={key:e.key,s:n.s,platform:n.platform,appname:e.key,sdkversion:n.sdkversion,logversion:n.logversion};t.origin&&(i["origin"]=t.origin),t.destination&&(i["destination"]=t.destination),t.strategy&&(i["strategy"]=t.strategy),t.city&&(i["city"]=t.city),t.cityd&&(i["cityd"]=t.cityd),wx.request({url:"https://restapi.amap.com/v3/direction/transit/integrated",data:i,method:"GET",header:{"content-type":"application/json"},success:function(e){if(e&&e.data&&e.data.route){var n=e.data.route;t.success({distance:n.distance||"",taxi_cost:n.taxi_cost||"",transits:n.transits})}},fail:function(e){t.fail({errCode:"0",errMsg:e.errMsg||""})}})},i.prototype.getRidingRoute=function(t){var e=this,n=e.requestConfig,i={key:e.key,s:n.s,platform:n.platform,appname:e.key,sdkversion:n.sdkversion,logversion:n.logversion};t.origin&&(i["origin"]=t.origin),t.destination&&(i["destination"]=t.destination),wx.request({url:"https://restapi.amap.com/v4/direction/bicycling",data:i,method:"GET",header:{"content-type":"application/json"},success:function(e){e&&e.data&&e.data.data&&t.success({paths:e.data.data.paths})},fail:function(e){t.fail({errCode:"0",errMsg:e.errMsg||""})}})},t.exports.AMapWX=i},a126:function(t,e,n){var i=n("bbdc");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("703d0287",i,!0,{sourceMap:!1,shadowMode:!1})},b252:function(t,e,n){var i=n("6ab5");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("0c30beb4",i,!0,{sourceMap:!1,shadowMode:!1})},b2cf:function(t,e,n){"use strict";n.r(e);var i=n("8907"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},b5c2:function(t,e,n){"use strict";n.r(e);var i=n("62e7"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},bbdc:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},ce7c:function(t,e,n){"use strict";n.r(e);var i=n("0efb"),a=n("ea26");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("eb5f");var r=n("f0c5"),s=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"89d76102",null,!1,i["a"],void 0);e["default"]=s.exports},d97b:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",[this.nodes.length?this._e():this._t("default"),e("v-uni-view",{style:this.showAm+(this.selectable?";user-select:text;-webkit-user-select:text":""),attrs:{id:"_top"}},[e("div",{attrs:{id:"rtf"+this.uid}})])],2)},a=[]},dd77:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={vhNavbar:n("12c6").default,vhSwiper:n("5307").default,vhGap:n("1677").default,uParse:n("2c89").default,uButton:n("4f1b").default,uPopup:n("c4b0").default,vhImage:n("ce7c").default,uNumberBox:n("3bd6").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"content"},[n("vh-navbar",{attrs:{"back-icon-color":"#FFF",background:{background:"#E80404"},title:"商品详情","title-color":"#FFF"}}),t.loading?t._e():n("v-uni-view",{staticClass:"fade-in"},[n("vh-swiper",{attrs:{list:t.goodsInfo.goods_images,"loading-type":2,height:750,mode:"number","indicator-pos":"bottomRight","img-mode":"aspectFit"}}),n("v-uni-view",{staticClass:"ptb-32-plr-24"},[n("v-uni-view",{staticClass:"d-flex j-sb"},[n("v-uni-view",{},[n("v-uni-text",{staticClass:"font-52 font-wei text-e80404 l-h-40"},[n("v-uni-text",{staticClass:"font-28"},[t._v("¥")]),t._v(t._s(t.goodsInfo.collocation[0].price))],1),n("v-uni-text",{staticClass:"ml-04 font-28 text-9 text-dec-l-t"},[t._v("¥"+t._s(t.goodsInfo.collocation[0].market_price))])],1),n("v-uni-view",{},[n("v-uni-text",{staticClass:"font-24 text-6"},[t._v("已售")]),n("v-uni-text",{staticClass:"font-40 font-wei text-e80404 l-h-40"},[t._v(t._s(t.goodsInfo.soldnum))]),n("v-uni-text",{staticClass:"font-24 text-6"},[t._v("份")])],1)],1),n("v-uni-view",{staticClass:"mt-26 font-30 font-wei text-3 l-h-44"},[t._v(t._s(t.goodsInfo.goods_name))]),n("v-uni-view",{staticClass:"mt-12 font-28 text-6 l-h-40"},[t._v(t._s(t.goodsInfo.brief))])],1),n("vh-gap",{attrs:{height:"20","bg-color":"#f5f5f5"}}),n("v-uni-view",{staticClass:"pr-24 pb-124 pl-24"},[n("v-uni-view",{staticClass:"d-flex j-center ptb-32-plr-00 font-32 font-wei text-3"},[t._v("商品详情")]),n("v-uni-view",{staticClass:"w-b-b-w"},[n("u-parse",{attrs:{html:t.goodsInfo.describe}})],1)],1),n("v-uni-view",{staticClass:"p-fixed bottom-160 right-24",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openScan.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"w-92 h-92",attrs:{src:t.osip+"/store_goods_detail/scan_red.png",mode:"aspectFill"}})],1),n("v-uni-view",{staticClass:"p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff b-sh-00021200-022"},[t.nearTheStore?n("v-uni-view",{staticClass:"fade-in w-p100 h-104 d-flex j-center a-center"},[0==t.goodsInfo.is_shelf?n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"686rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#DDDDDD",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.feedback.toast({title:"亲，该商品已下架~"})}}},[t._v("商品已下架")])],1):n("v-uni-view",{staticClass:"w-p100 h-104 d-flex j-sb a-center"},[n("v-uni-view",{staticClass:"d-flex j-sb a-center"},[n("v-uni-view",{staticClass:"d-flex flex-column a-center ml-52",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.jump.loginNavigateTo(t.routeTable.pBStoreShoppingCart)}}},[n("v-uni-image",{staticClass:"w-32 h-32",attrs:{src:t.osip+"/store_goods_detail/car.png",mode:"aspectFill"}}),n("v-uni-text",{staticClass:"font-22 text-9 l-h-32"},[t._v("购物车")])],1),n("v-uni-view",{staticClass:"d-flex flex-column a-center ml-52",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addShoppingCart()}}},[n("v-uni-image",{staticClass:"w-32 h-32",attrs:{src:t.osip+"/store_goods_detail/add_car.png",mode:"aspectFill"}}),n("v-uni-text",{staticClass:"font-22 text-9 l-h-32"},[t._v("加购")])],1)],1),n("v-uni-view",{staticClass:"d-flex a-center mr-24"},[t.goodsInfo.is_cup?n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"460rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.buyNow(3)}}},[t._v("门店场饮")])],1):n("v-uni-view",{},[t.hasSelfMention&&t.hasFieldDrink?n("v-uni-view",{staticClass:"d-flex"},[n("u-button",{attrs:{"hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"230rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#FF9127",border:"none",borderRadius:"32rpx 0 0 32rpx"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.buyNow(2)}}},[t._v("打包外带")]),n("u-button",{attrs:{"hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"230rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none",borderRadius:"0 32rpx 32rpx 0"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.buyNow(3)}}},[t._v("门店场饮")])],1):t._e(),t.hasSelfMention&&!t.hasFieldDrink?n("v-uni-view",[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"460rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#FF9127",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.buyNow(2)}}},[t._v("打包外带")])],1):t._e(),!t.hasSelfMention&&t.hasFieldDrink?n("v-uni-view",{},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"460rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.buyNow(3)}}},[t._v("门店场饮")])],1):t._e()],1)],1)],1)],1):n("v-uni-view",{staticClass:"fade-in w-p100 h-104 d-flex j-center a-center"},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"686rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#DDDDDD",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.feedback.toast({title:"亲，您没在门店范围内~"})}}},[t._v("未在门店范围内")])],1)],1),n("v-uni-view",{},[n("u-popup",{attrs:{mode:"bottom",duration:150,"border-radius":20},model:{value:t.showGoodsPackPop,callback:function(e){t.showGoodsPackPop=e},expression:"showGoodsPackPop"}},[n("v-uni-view",{staticClass:"pt-32 pr-24 pb-48 pl-24"},[n("v-uni-view",{staticClass:"d-flex"},[n("vh-image",{attrs:{"loading-type":2,src:t.goodsInfo.goods_images[0],width:180,height:180,"border-radius":6,mode:"aspectFit"}}),n("v-uni-view",{staticClass:"d-flex flex-1 flex-column j-sb ml-16"},[n("v-uni-view",{staticClass:"font-28 text-3 l-h-40 o-hid text-hidden-2"},[t._v(t._s(t.goodsInfo.goods_name))]),n("v-uni-view",{staticClass:"d-flex a-end"},[n("v-uni-text",{staticClass:"font-44 font-wei text-e80404 l-h-34"},[n("v-uni-text",{staticClass:"font-24 mr-06"},[t._v("¥")]),t._v(t._s(t.packageInfo.price))],1),n("v-uni-text",{staticClass:"ml-10 font-24 text-9 text-dec-l-t l-h-34"},[t._v("¥"+t._s(t.packageInfo.market_price))])],1)],1)],1),n("v-uni-view",{staticClass:"mt-48 font-32 font-wei text-3"},[t._v("规格")]),n("v-uni-view",{staticClass:"d-flex flex-wrap ml-n-24"},t._l(t.packageList,(function(e,i){return n("v-uni-view",{key:i},[n("v-uni-view",{staticClass:"bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3",class:t.packageIndex!=i?"":t.clickSelectPackage?"skew-top bg-fce4e3 b-s-01-e80404 text-e80404":"bg-fce4e3 b-s-01-e80404 text-e80404",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.selectPackage(i)}}},[t._v(t._s(e.c_name))])],1)})),1),n("v-uni-view",{staticClass:"d-flex j-sb a-center mt-52"},[n("v-uni-view",{staticClass:"font-32 font-wei text-3"},[t._v("数量")]),n("v-uni-view",{},[n("u-number-box",{attrs:{min:1,max:t.packageInfo.maxstock,"input-width":64,"input-height":50,size:28},model:{value:t.purchaseNumbers,callback:function(e){t.purchaseNumbers=e},expression:"purchaseNumbers"}})],1)],1),0==t.packageInfo.stock_insufficient&&1==t.packageInfo.is_enable&&t.packageInfo.maxstock?n("v-uni-view",{staticClass:"mt-92 d-flex j-center a-center"},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#E80404",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.confirm.apply(void 0,arguments)}}},[t._v("确定")])],1):n("v-uni-view",{staticClass:"mt-92 d-flex j-center a-center"},[n("u-button",{attrs:{shape:"circle","hair-line":!1,ripple:!0,"ripple-bg-color":"#FFF","custom-style":{width:"646rpx",height:"64rpx",fontSize:"28rpx",fontWeight:"bold",color:"#FFF",backgroundColor:"#DDDDDD",border:"none"}},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.feedback.toast({title:"该套餐库存不足~"})}}},[t._v("库存不足")])],1)],1)],1)],1)],1)],1)},o=[]},e6a9:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* 计数器样式 */[data-v-286f1e63] .u-numberbox{border:1px solid #eee;border-radius:%?12?%}[data-v-286f1e63] .u-icon-minus,[data-v-286f1e63] .u-icon-plus{width:%?50?%!important;background-color:#fff!important}[data-v-286f1e63] .uicon-minus,[data-v-286f1e63] .uicon-plus{font-size:%?24?%!important;color:#666!important}",""]),t.exports=e},ea26:function(t,e,n){"use strict";n.r(e);var i=n("3dc3"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},eb5f:function(t,e,n){"use strict";var i=n("b252"),a=n.n(i);a.a},f074:function(t,e,n){"use strict";n.r(e);var i=n("7f1a"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},f157:function(t,e,n){var i=n("8987");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("e49b1058",i,!0,{sourceMap:!1,shadowMode:!1})},f2f9:function(t,e,n){"use strict";var i=n("a126"),a=n.n(i);a.a},f440:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{style:[this.gapStyle]})},a=[]},faa5:function(t,e,n){var i=n("5820");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("54a38be0",i,!0,{sourceMap:!1,shadowMode:!1})},fb12:function(t,e,n){"use strict";var i=n("faa5"),a=n.n(i);a.a}}]);