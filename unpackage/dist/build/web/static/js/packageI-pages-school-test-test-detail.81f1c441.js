(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageI-pages-school-test-test-detail"],{"663a":function(t,a,e){"use strict";var n=e("9de1"),i=e.n(n);i.a},"6f6a":function(t,a,e){"use strict";e.r(a);var n=e("d3a9"),i=e("aad1");for(var s in i)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(s);e("663a");var o=e("f0c5"),r=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"a8d21fa4",null,!1,n["a"],void 0);a["default"]=r.exports},"9de1":function(t,a,e){var n=e("f58f");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=e("4f06").default;i("29556984",n,!0,{sourceMap:!1,shadowMode:!1})},"9edc":function(t,a,e){"use strict";e("7a82");var n=e("ee27").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0,e("a9e3"),e("99af");var i=n(e("f07e")),s=n(e("c964")),o={data:function(){return{id:"",title:"",status:0,paper_id:"",total_num:0,total_score:0,score:0,appStatusBarHeight:0}},onLoad:function(t){var a=this;uni.getSystemInfo({success:function(t){a.appStatusBarHeight=t.statusBarHeight?t.statusBarHeight:48}}),this.paper_id=t.paper_id,this.title=t.title,this.id=t.id,this.status=t.status,this.total_num=t.total_num,this.total_score=t.total_score,this.score=t.score},methods:{back:function(){this.comes.isFromApp(this.from)?wineYunJsBridge.openAppPage({client_path:{ios_path:"goBack",android_path:"goBack"}}):this.jump.navigateBack()},startQuiz:function(t){var a=this;return(0,s.default)((0,i.default)().mark((function e(){var n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!Number(t)){e.next=8;break}return n={paper_id:a.paper_id},e.next=4,a.$u.api.resetExam(n);case 4:e.sent,a.jump.redirectTo("".concat(a.$routeTable.PIAnswerList,"?id=").concat(a.id)),e.next=9;break;case 8:a.jump.redirectTo("".concat(a.$routeTable.PIAnswerList,"?id=").concat(a.id));case 9:case"end":return e.stop()}}),e)})))()}}};a.default=o},aad1:function(t,a,e){"use strict";e.r(a);var n=e("9edc"),i=e.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){e.d(a,t,(function(){return n[t]}))}(s);a["default"]=i.a},d3a9:function(t,a,e){"use strict";e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return s})),e.d(a,"a",(function(){return n}));var n={uIcon:e("e5e1").default},i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("v-uni-view",{staticClass:"container"},[e("v-uni-image",{staticClass:"background-img",attrs:{src:"http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/test-bg.png"}}),e("v-uni-view",{staticClass:"header"},[e("v-uni-view",{staticClass:"ml-24 d-flex a-center",style:{marginTop:t.$appStatusBarHeight?parseInt(t.$appStatusBarHeight)+"px":"0px"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.jump.navigateBack()}}},[e("u-icon",{staticStyle:{height:"48px"},attrs:{name:"nav-back",color:"#fff",size:44}})],1),e("v-uni-text",{staticClass:"title"},[t._v(t._s(t.title))]),e("v-uni-text",{staticClass:"subtitle"},[t._v("( 共"+t._s(t.total_num)+"道题 )")]),1==t.status?e("v-uni-text",{staticClass:"score"},[e("v-uni-text",{staticClass:"score-title"},[t._v("上次得分")]),e("v-uni-text",{staticClass:"score-number",staticStyle:{"margin-left":"6rpx",color:"#41cc8e"}},[t._v(t._s(t.score))]),e("v-uni-text",{staticClass:"score-number"},[t._v("/"+t._s(t.total_score))])],1):t._e()],1),e("v-uni-view",{staticClass:"content"},[e("v-uni-button",{staticClass:"start-btn",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.startQuiz(t.status)}}},[t._v(t._s(0==t.status?"开始答题":"重新作答"))])],1)],1)},s=[]},f58f:function(t,a,e){var n=e("24fb");a=n(!1),a.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.back[data-v-a8d21fa4]{position:absolute}.container[data-v-a8d21fa4]{display:flex;flex-direction:column;height:100vh;background-color:#fff;position:relative;overflow:hidden\n  /* 确保背景图片超出视口时不会显示滚动条 */}.background-img[data-v-a8d21fa4]{position:absolute;top:0;left:0;width:100%;height:40%;\n  /* 设置为所需背景高度的百分比 */z-index:0\n  /* 确保背景图片在所有其他元素后面 */}.score[data-v-a8d21fa4]{width:%?320?%;padding:%?12?% %?50?%;margin:%?40?% auto;display:inline;background:#fff;border-radius:%?100?%;border:%?6?% solid hsla(0,0%,100%,.35)}.score .score-title[data-v-a8d21fa4]{font-size:%?24?%;color:#666;line-height:%?34?%;text-align:center;font-style:normal}.score .score-number[data-v-a8d21fa4]{font-size:%?28?%;font-weight:700;line-height:%?40?%;text-align:center;font-style:normal}.header[data-v-a8d21fa4]{height:46%;text-align:center;position:relative;display:flex;flex-direction:column}.back-arrow[data-v-a8d21fa4]{position:absolute;left:%?20?%;top:%?20?%;width:%?30?%;height:%?30?%}.title[data-v-a8d21fa4],\n.subtitle[data-v-a8d21fa4]{color:#fff}.title[data-v-a8d21fa4]{font-weight:600;font-size:%?62?%;color:#fff;line-height:%?88?%;text-align:center;font-style:normal}.subtitle[data-v-a8d21fa4]{font-size:%?32?%;color:#fff;line-height:%?44?%;text-align:center;font-style:normal;margin-top:%?10?%}.content[data-v-a8d21fa4]{flex:1;display:flex;justify-content:center;align-items:center}.start-btn[data-v-a8d21fa4]{background-color:#e80404;color:#fff;padding:%?14?% 0;border-radius:%?100?%;width:90%;font-size:%?30?%}',""]),t.exports=a}}]);