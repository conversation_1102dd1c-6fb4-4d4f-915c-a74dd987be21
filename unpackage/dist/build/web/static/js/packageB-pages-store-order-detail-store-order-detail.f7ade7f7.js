(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["packageB-pages-store-order-detail-store-order-detail"],{"0efb":function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"vh-image-con",class:[t.isMf?"mf-card":""],style:[t.wrapStyle,t.backgroundStyle],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onClick.apply(void 0,arguments)}}},[t.isError?t._e():a("v-uni-image",{staticClass:"vh-image",style:{backgroundColor:t.bgColor,borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)},attrs:{src:t.getImage,mode:t.mode,"lazy-load":t.lazyLoad,"show-menu-by-longpress":t.showMenuByLongpress},on:{error:function(e){arguments[0]=e=t.$handleEvent(e),t.onErrorHandler.apply(void 0,arguments)},load:function(e){arguments[0]=e=t.$handleEvent(e),t.onLoadHandler.apply(void 0,arguments)}}}),t.showLoading&&t.loading?a("v-uni-view",{staticClass:"vh-image-loading",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius),backgroundColor:this.bgColor}},[a("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e(),t.showError&&t.isError&&!t.loading?a("v-uni-view",{staticClass:"u-image-error",style:{borderRadius:"circle"==t.shape?"50%":t.$u.addUnit(t.borderRadius)}},[a("v-uni-image",{style:[t.wrapStyle],attrs:{src:t.getLoadingImage,mode:t.mode}})],1):t._e()],1)},n=[]},"12c6":function(t,e,a){"use strict";a.r(e);var i=a("51bd"),n=a("f074");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("f2f9");var r=a("f0c5"),o=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"519170ec",null,!1,i["a"],void 0);e["default"]=o.exports},"353a":function(t,e,a){var i=a("42c4");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("499c108c",i,!0,{sourceMap:!1,shadowMode:!1})},"3dc3":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("d0af")),s=i(a("f3f3"));a("a9e3"),a("d3b7"),a("159b"),a("e25e"),a("c975");var r=a("26cb"),o={name:"u-image",props:{loadingType:{type:[String,Number],default:1},isMf:{type:Boolean,default:!1},src:{default:""},mode:{type:String,default:"aspectFill"},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"auto"},shape:{type:String,default:"square"},borderRadius:{type:[String,Number],default:0},lazyLoad:{type:Boolean,default:!0},showMenuByLongpress:{type:Boolean,default:!1},loadingIcon:{type:String,default:"photo"},errorIcon:{type:String,default:"error-circle"},showLoading:{type:Boolean,default:!0},showError:{type:Boolean,default:!0},fade:{type:Boolean,default:!0},webp:{type:Boolean,default:!1},duration:{type:[String,Number],default:100},bgColor:{type:String,default:"transparent"},isResize:{type:Boolean,default:!1},resizeRatio:{type:Object,default:function(){return{wratio:16,hratio:9}}},resizeUsePx:{type:Boolean,default:!1},opacityProp:{type:Number,default:1},backgroundImage:{type:String,default:""}},data:function(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{}}},watch:{src:{immediate:!0,handler:function(t){t?this.isError=!1:(this.isError=!0,this.loading=!1)}}},computed:(0,s.default)((0,s.default)({},(0,r.mapState)(["ossPrefix"])),{},{wrapStyle:function(){var t={};if(t.width=this.$u.addUnit(this.width),t.height=this.$u.addUnit(this.height),this.backgroundImage&&(t.backgroundImage="url(".concat(this.backgroundImage,")"),t.backgroundSize="100% 100%",t.backgroundRepeat="no-repeat",t.backgroundPosition="center"),this.isResize){var e,a,i=(null===this||void 0===this||null===(e=this.src)||void 0===e||null===(a=e.split("?"))||void 0===a?void 0:a[1])||"",s=i.split("&"),r={};s.forEach((function(t){var e=t.split("="),a=(0,n.default)(e,2),i=a[0],s=a[1];r[i]=s}));var o=+((null===r||void 0===r?void 0:r.w)||""),l=+((null===r||void 0===r?void 0:r.h)||"");if(!isNaN(o)&&!isNaN(l)&&o&&l){var d=parseInt(this.width),u=d/o*l,c=this.resizeRatio,f=c.wratio,v=c.hratio;if("auto"!==f&&"auto"!==v){var p=d*f/v,h=d*v/f;u>p?u=p:u<h&&(u=h)}this.resizeUsePx?t.height="".concat(u,"px"):t.height=this.$u.addUnit(u)}}return t.borderRadius="circle"==this.shape?"50%":this.$u.addUnit(this.borderRadius),t.overflow=this.borderRadius>0||"circle"==this.shape?"hidden":"visible",this.fade&&(t.opacity=this.opacity,t.transition="opacity ".concat(Number(this.durationTime)/1e3,"s ease-in-out")),t},getLoadingImage:function(){return"https://images.vinehoo.com/vinehoomini/v3/occupy_img/new_img".concat(this.loadingType,".png")},getImage:function(){return"string"!==typeof this.src?"":-1==this.src.indexOf("http")?this.ossPrefix+this.src:this.src}}),methods:{onClick:function(){this.$emit("click")},onErrorHandler:function(t){this.loading=!1,this.isError=!0,this.$emit("error",t)},onLoadHandler:function(){var t=this;if(this.loading=!1,this.isError=!1,this.$emit("load"),!this.fade)return this.removeBgColor();this.opacity=0,this.durationTime=0,setTimeout((function(){t.durationTime=t.duration,t.opacity=t.opacityProp,setTimeout((function(){t.removeBgColor()}),t.durationTime)}),50)},removeBgColor:function(){this.backgroundStyle={backgroundColor:"transparent"}}}};e.default=o},"42c4":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,"uni-page-body[data-v-247b367c]{background-color:#f5f5f5}body.?%PAGE?%[data-v-247b367c]{background-color:#f5f5f5}",""]),t.exports=e},"51bd":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={uIcon:a("e5e1").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{},[a("v-uni-view",{staticClass:"vh-navbar",class:[{"vh-navbar-fixed":t.isFixed},t.navbarClass],style:[t.navbarStyle]},[a("v-uni-view",{staticClass:"vh-status-bar",style:{height:(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}),a("v-uni-view",{staticClass:"vh-navbar-inner",class:{"new-year-nav":t.newYearTheme},style:[t.navbarInnerStyle]},[t.isBack?a("v-uni-view",{staticClass:"vh-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[t.vhFrom?a("u-icon",{attrs:{name:"nav-back",color:t.backIconColor,size:t.backIconSize}}):[a("u-icon",{attrs:{name:t.pageLength<=1?"home":t.backIconName,color:t.backIconColor,size:t.backIconSize}}),t.backText?a("v-uni-view",{staticClass:"vh-back-text",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()]],2):t._e(),t.title?a("v-uni-view",{staticClass:"vh-navbar-content-title",style:[t.titleStyle]},[a("v-uni-view",{staticClass:"vh-title",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),a("v-uni-view",{staticClass:"vh-slot-content"},[t._t("default")],2),a("v-uni-view",{staticClass:"vh-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?a("v-uni-view",{staticClass:"vh-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+(t.appStatusBarHeight||t.customStatusBarHeight||t.statusBarHeight)+"px"}}):t._e()],1)},s=[]},"6ab5":function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-image-con[data-v-89d76102]{position:relative;transition:opacity .5s ease-in}.vh-image[data-v-89d76102]{width:100%;height:100%}.vh-image-loading[data-v-89d76102],\n.u-image-error[data-v-89d76102]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#fff;color:#909399;font-size:%?46?%}.mf-card[data-v-89d76102]{background-color:#f7f7f7!important;padding:5px}',""]),t.exports=e},7971:function(t,e,a){"use strict";var i=a("353a"),n=a.n(i);n.a},7987:function(t,e,a){"use strict";a.r(e);var i=a("e350"),n=a("e7fb");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("7971");var r=a("f0c5"),o=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"247b367c",null,!1,i["a"],void 0);e["default"]=o.exports},"7f1a":function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("f3f3"));a("a9e3"),a("caad6"),a("2532");var s=a("26cb"),r=uni.getSystemInfoSync(),o={},l={name:"vh-navbar",props:{newYearTheme:{type:Boolean,default:!1},isFixed:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},height:{type:[String,Number],default:""},background:{type:Object,default:function(){return{background:"#ffffff"}}},isBack:{type:[Boolean,String],default:!0},showBorder:{type:Boolean,default:!1},borderStyle:{type:Object,default:function(){return{borderBottom:"0.5px solid #EEEEEE"}}},backIconName:{type:String,default:"nav-back"},backText:{type:String,default:""},backIconSize:{type:[String,Number],default:"44"},backIconColor:{type:String,default:"#333"},backTextStyle:{type:Object,default:function(){return{color:"#333"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleSize:{type:[String,Number],default:36},titleBold:{type:Boolean,default:!0},titleColor:{type:String,default:"#333"},immersive:{type:Boolean,default:!1},customBack:{type:Function,default:null},navbarClass:{type:String,default:""},customStatusBarHeight:{type:Number,default:0}},data:function(){return{menuButtonInfo:o,statusBarHeight:r.statusBarHeight,appStatusBarHeight:0}},computed:(0,n.default)((0,n.default)({},(0,s.mapState)(["routeTable"])),{},{pageLength:function(){return this.pages.getPageLength()},navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),this.showBorder&&Object.assign(t,this.borderStyle),t},titleStyle:function(){var t={};return t.left=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(r.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44},vhFrom:function(){return this.$vhFrom}}),created:function(){console.log("---------------我是子组件的页面栈长度"),console.log(this.pages.getPageLength()),console.log(this.pageLength);var t=this.pages.getPageFullPath(),e=this.routeTable,a=e.pEAddressAdd,i=e.pEAddressManagement,n=e.pBOrderDepositDetail;(t.includes("/packageH")||t.includes(a)||t.includes(i)||t.includes(n))&&(this.appStatusBarHeight=this.$appStatusBarHeight)},methods:{goBack:function(){if("function"===typeof this.customBack)this.customBack.bind(this.$u.$parent.call(this))();else{if(this.$app&&1===this.pages.getPageLength())return void this.$customBack();this.pageLength<=1?uni.reLaunch({url:"/pages/index/index"}):uni.navigateBack()}}}};e.default=l},a126:function(t,e,a){var i=a("bbdc");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("703d0287",i,!0,{sourceMap:!1,shadowMode:!1})},b252:function(t,e,a){var i=a("6ab5");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("4f06").default;n("0c30beb4",i,!0,{sourceMap:!1,shadowMode:!1})},bbdc:function(t,e,a){var i=a("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\n/**\n * 这里是uni-app内置的常用样式变量\n *\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\n *\n */\n/**\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\n *\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\n */\n/* uni.scss */\n/* 颜色变量 */\n/* 行为相关颜色 */\n/* 文字基本颜色 */\n/* 背景颜色 */\n/* 边框颜色 */\n/* 尺寸变量 */\n/* 文字尺寸 */\n/* 图片尺寸 */\n/* Border Radius */\n/* 水平间距 */\n/* 垂直间距 */\n/* 透明度 */\n/* 文章场景相关 */.vh-navbar[data-v-519170ec]{width:100%}.vh-navbar-fixed[data-v-519170ec]{position:fixed;left:0;right:0;top:0;z-index:991}.vh-status-bar[data-v-519170ec]{width:100%}.vh-navbar-inner[data-v-519170ec]{display:flex;justify-content:space-between;position:relative;align-items:center}.vh-back-wrap[data-v-519170ec]{display:flex;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.vh-back-text[data-v-519170ec]{padding-left:%?4?%;font-size:%?30?%}.vh-navbar-content-title[data-v-519170ec]{display:flex;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;word-break:break-all}.vh-title[data-v-519170ec]{line-height:%?60?%;font-size:%?32?%;flex:1}.vh-navbar-right[data-v-519170ec]{flex:1;display:flex;align-items:center;justify-content:flex-end}.vh-slot-content[data-v-519170ec]{flex:1;display:flex;align-items:center}.new-year-nav[data-v-519170ec]{background-image:url(http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png);background-size:100% 100%;background-repeat:no-repeat;background-position:50%}',""]),t.exports=e},ce7c:function(t,e,a){"use strict";a.r(e);var i=a("0efb"),n=a("ea26");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("eb5f");var r=a("f0c5"),o=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"89d76102",null,!1,i["a"],void 0);e["default"]=o.exports},e350:function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={vhNavbar:a("12c6").default,vhImage:a("ce7c").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"content"},[a("v-uni-view",{staticClass:"fade-in pb-104"},[a("vh-navbar",{attrs:{"back-icon-color":"#FFF",title:"门店订单详情","title-color":"#FFF",background:{background:t.navBackgroundColor}}}),a("v-uni-image",{staticClass:"p-abso top-0 w-p100 h-400",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/order_detail/ban.png",mode:"widthFix"}}),a("v-uni-view",{staticClass:"p-rela z-1 mt-n-20 ml-48 mr-48"},[a("v-uni-view",{staticClass:"d-flex j-sb a-center"},[a("v-uni-view",{},[t.storeOrderDetail.refund_status?a("v-uni-view",{staticClass:"font-36 font-wei text-ffffff"},[t._v(t._s(t.refundStatusList[t.storeOrderDetail.status]))]):a("v-uni-view",{staticClass:"font-36 font-wei text-ffffff"},[t._v(t._s(t.orderStatusList[t.storeOrderDetail.status].name))]),a("v-uni-view",{staticClass:"mt-08 font-28 text-ffffff"},[t._v("下单时间："+t._s(t.storeOrderDetail.create_time))])],1),t.storeOrderDetail.status?a("v-uni-view",{},[a("v-uni-image",{staticClass:"w-128 h-128",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/order_detail/"+t.orderStatusList[t.storeOrderDetail.status].img+".png",mode:"aspectFill"}})],1):t._e()],1)],1),a("v-uni-view",{staticClass:"p-rela z-01 bg-ffffff b-rad-10 mt-24 ml-24 mr-24 ptb-00-plr-24"},[a("v-uni-view",{staticClass:"d-flex ptb-32-plr-00"},[a("v-uni-image",{staticClass:"w-44 h-44 mt-04",attrs:{src:"https://images.vinehoo.com/vinehoomini/v3/order_detail/add_bla.png",mode:"widthFix"}}),1==t.storeOrderDetail.send_type?a("v-uni-view",{staticClass:"w-580 ml-16"},[a("v-uni-view",{},[a("v-uni-text",{staticClass:"mr-36 font-32 font-wei text-3"},[t._v(t._s(t.storeOrderDetail.consignee))]),a("v-uni-text",{staticClass:"font-28 text-3"},[t._v(t._s(t.storeOrderDetail.cellphone))])],1),a("v-uni-view",{staticClass:"mt-12 font-24 text-3 l-h-34"},[t._v(t._s(t.storeOrderDetail.ship_addr))])],1):a("v-uni-view",{staticClass:"w-580 ml-16"},[a("v-uni-view",{staticClass:"font-32 font-wei text-3"},[t._v(t._s(t.storeOrderDetail.store_name))]),a("v-uni-view",{staticClass:"mt-12 font-24 text-3 l-h-34"},[t._v(t._s(t.storeOrderDetail.address))])],1)],1)],1),a("v-uni-view",{staticClass:"p-rela z-01 bg-ffffff b-rad-10 mt-20 ml-24 mr-24 pt-24"},t._l(t.storeOrderDetail.goods_info,(function(e,i){return a("v-uni-view",{key:i,staticClass:" pr-20 pb-24 pl-28"},[a("v-uni-view",{staticClass:"d-flex j-sb"},[a("vh-image",{attrs:{"loading-type":2,src:e.goods_images,width:152,height:152,"border-radius":10,mode:"aspectFit"}}),a("v-uni-view",{staticClass:"flex-1 d-flex flex-column j-sb ml-12"},[a("v-uni-view",{},[a("v-uni-view",{staticClass:"font-24 text-0 l-h-34 text-hidden-2"},[t._v(t._s(e.goods_name))]),a("v-uni-view",{staticClass:"mt-08"},[a("v-uni-text",{staticClass:"bg-f5f5f5 ptb-02-plr-12 b-rad-04 font-20 text-9"},[t._v(t._s(e.c_name))])],1)],1),a("v-uni-view",{staticClass:"font-22 text-6"},[t._v("x"+t._s(e.pay_number))])],1)],1)],1)})),1),a("v-uni-view",{staticClass:"bg-ffffff b-rad-10 mt-20 ml-24 mr-24"},[a("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"},[a("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("订单编号")]),a("v-uni-text",{staticClass:"font-24 text-3"},[t._v(t._s(0==t.storeOrderDetail.status?t.storeOrderDetail.mainorderno:t.storeOrderDetail.orderno))])],1),t.storeOrderDetail.express_no?a("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"},[a("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("物流单号")]),a("v-uni-text",{staticClass:"font-24 text-3"},[t._v(t._s(t.storeOrderDetail.express_no))])],1):t._e(),a("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"},[a("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("支付方式")]),a("v-uni-text",{staticClass:"font-24 text-3"},[t._v(t._s(1==t.storeOrderDetail.pay_type?"微信支付":"线下支付"))])],1),a("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"},[a("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("配送方式")]),a("v-uni-text",{staticClass:"font-24 text-3"},[t._v(t._s(1==t.storeOrderDetail.send_type?"物流配送":2==t.storeOrderDetail.send_type?"门店自提":"门店场饮"))])],1),t.storeOrderDetail.consignee?a("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"},[a("v-uni-text",{staticClass:"font-28 font-wei text-3"},[t._v("姓名")]),a("v-uni-text",{staticClass:"font-24 text-3"},[t._v("storeOrderDetail.consignee")])],1):t._e()],1),a("v-uni-view",{staticClass:"bg-ffffff b-rad-10 mt-20 ml-24 mr-24"},[a("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee"},[a("v-uni-view",{staticClass:"font-28 font-wei text-3"},[t._v("订单金额")]),a("v-uni-view",{staticClass:"font-28 font-wei text-e80404"},[t._v("¥"+t._s(0==t.storeOrderDetail.status?t.storeOrderDetail.pay_money:t.storeOrderDetail.order_money))])],1),3==t.storeOrderDetail.send_type?a("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32"},[a("v-uni-view",{staticClass:"font-28 font-wei text-3"},[t._v("堂饮费")]),a("v-uni-view",{staticClass:"font-28 font-wei text-e80404"},[t._v("¥"+t._s(t.storeOrderDetail.service_charge))])],1):t._e(),1==t.storeOrderDetail.send_type?a("v-uni-view",{staticClass:"d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32"},[a("v-uni-view",{staticClass:"font-28 font-wei text-3"},[t._v("运费")]),a("v-uni-view",{staticClass:"font-28 font-wei text-e80404"},[t._v("¥0.00")])],1):t._e()],1)],1)],1)},s=[]},e7fb:function(t,e,a){"use strict";a.r(e);var i=a("fd4d"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},ea26:function(t,e,a){"use strict";a.r(e);var i=a("3dc3"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},eb5f:function(t,e,a){"use strict";var i=a("b252"),n=a.n(i);n.a},f074:function(t,e,a){"use strict";a.r(e);var i=a("7f1a"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},f2f9:function(t,e,a){"use strict";var i=a("a126"),n=a.n(i);n.a},fd4d:function(t,e,a){"use strict";a("7a82");var i=a("ee27").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("f3f3")),s=a("26cb"),r={name:"store-order-detail",data:function(){return{orderStatusList:[{name:"待支付",img:"ord_be_pad"},{name:"已支付",img:"ord_be_ship"},{name:"已发货",img:"ord_be_rec"},{name:"已完成",img:"ord_comp"}],refundStatusList:["","退款中","退款成功","退款失败"],navBackgroundColor:"rgba(224, 20, 31, 0)"}},computed:(0,n.default)({},(0,s.mapState)(["storeOrderDetail"])),onPageScroll:function(t){t.scrollTop<=100?this.navBackgroundColor="rgba(224, 20, 31, ".concat(t.scrollTop/100,")"):this.navBackgroundColor="rgba(224, 20, 31, 1)"}};e.default=r}}]);