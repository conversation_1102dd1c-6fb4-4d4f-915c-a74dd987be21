"use client"

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, CreditCard, Clock, Sparkles } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function ComingSoonPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 via-red-50/30 to-white">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-white/95 backdrop-blur-md border-b border-gray-100">
        
      </header>

      {/* Main Content */}
      <main className="px-6 py-12">
        <div className="max-w-md mx-auto space-y-12">
          {/* Hero Section */}
          <div className="text-center space-y-8">
            {/* Main Icon with Enhanced Animation */}
            <div className="relative flex justify-center">
              <div className="relative">
                {/* Outer glow ring */}
                <div className="absolute inset-0 w-32 h-32 bg-gradient-to-r from-[#CA101B]/20 to-red-400/20 rounded-full blur-xl"></div>

                {/* Main icon container */}
                <div className="relative w-28 h-28 bg-gradient-to-br from-[#CA101B] via-red-600 to-red-700 rounded-3xl flex items-center justify-center shadow-2xl shadow-red-500/25">
                  <div className="relative">
                    <Gift className="h-12 w-12 text-white drop-shadow-sm" />
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center">
                      <CreditCard className="h-3 w-3 text-[#CA101B]" />
                    </div>
                  </div>
                </div>

                {/* Animated rings */}
                <div className="absolute inset-0 w-28 h-28 rounded-3xl border-2 border-[#CA101B]/30 animate-ping"></div>
                <div
                  className="absolute inset-0 w-28 h-28 rounded-3xl border border-[#CA101B]/20 animate-ping"
                  style={{ animationDelay: "1s", animationDuration: "2s" }}
                ></div>
              </div>
            </div>

            {/* Title Section */}
            <div className="space-y-4">
              <div className="inline-flex items-center px-4 py-2 bg-[#CA101B]/10 rounded-full border border-[#CA101B]/20">
                <Clock className="h-4 w-4 text-[#CA101B] mr-2" />
                <span className="text-sm font-medium text-[#CA101B]">敬请期待</span>
                <Sparkles className="h-4 w-4 text-[#CA101B] ml-2" />
              </div>

              

              <div className="flex justify-center">
                
              </div>
            </div>
          </div>

          {/* Main Message Card */}
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-[#CA101B]/5 to-red-500/5 rounded-3xl blur-sm"></div>
            <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-gray-100">
              <div className="text-center space-y-4">
                <div className="w-12 h-1 bg-gradient-to-r from-[#CA101B] to-red-500 mx-auto rounded-full"></div>
                <h2 className="text-xl font-bold text-gray-800 leading-relaxed">
                  余额/充值卡（礼品卡）
                  <br />
                  功能即将上线
                </h2>
                <p className="text-sm text-gray-600 leading-relaxed">我们正在努力为您带来更好的服务体验</p>
              </div>
            </div>
          </div>

          {/* Features Grid */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 text-center mb-6">即将推出的功能</h3>

            <div className="grid gap-4">
              {[
                { title: "余额充值", desc: "便捷的账户余额管理" },
                { title: "礼品卡兑换", desc: "灵活的礼品卡使用体验" },
                { title: "便捷支付体验", desc: "更快速的支付流程" },
              ].map((feature, index) => (
                <div
                  key={index}
                  className="group bg-white/70 backdrop-blur-sm rounded-2xl p-5 border border-gray-100 hover:border-[#CA101B]/20 transition-all duration-300 hover:shadow-lg hover:shadow-red-500/10"
                >
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-4 h-4 bg-gradient-to-r from-[#CA101B] to-red-500 rounded-full group-hover:scale-110 transition-transform duration-300"></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-800 text-base">{feature.title}</h4>
                      
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* CTA Section */}
          <div className="space-y-6 pt-4">
            

            {/* Footer */}
            <div className="text-center pt-8 border-t border-gray-100">
              <p className="text-xs text-gray-500">感谢您的耐心等待，精彩功能即将与您见面</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
