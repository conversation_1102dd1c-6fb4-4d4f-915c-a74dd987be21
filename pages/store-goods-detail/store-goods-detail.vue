<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar back-icon-color="#FFF" :background="{background: '#E80404'}" title="商品详情" title-color="#FFF" />
		
		<view v-if="!loading" class="fade-in">
			<!-- 轮播图 -->
			<vh-swiper :list="goodsInfo.goods_images" :loading-type="2" :height="750" mode="number" indicator-pos="bottomRight" img-mode="aspectFit"/>
			
			<!-- 商品标题 -->
			<view class="ptb-32-plr-24">
				<view class="d-flex j-sb">
					<view class="">
						<text class="font-52 font-wei text-e80404 l-h-40"><text class="font-28">¥</text>{{goodsInfo.collocation[0].price}}</text>
						<text class="ml-04 font-28 text-9 text-dec-l-t">¥{{goodsInfo.collocation[0].market_price}}</text>
					</view>
					<view class="">
						<text class="font-24 text-6">已售</text>
						<text class="font-40 font-wei text-e80404 l-h-40">{{goodsInfo.soldnum}}</text>
						<text class="font-24 text-6">份</text>
					</view>
				</view>
				<view class="mt-26 font-30 font-wei text-3 l-h-44">{{goodsInfo.goods_name}}</view>
				<view class="mt-12 font-28 text-6 l-h-40">{{goodsInfo.brief}}</view>
			</view>
			
			<!-- 间隔槽 -->
			<vh-gap height="20" bg-color="#f5f5f5" />
			
			<!-- 商品详情 -->
			<view class="pr-24 pb-124 pl-24">
				<view class="d-flex j-center ptb-32-plr-00 font-32 font-wei text-3">商品详情</view>
				<view class="w-b-b-w">
					<u-parse :html="goodsInfo.describe" />
				</view>
			</view>
			
			<!-- 评论 -->
			<!-- <view class="bg-f5f5f5 pt-20 pr-24 pb-120 pl-24">
				<view class="bg-ffffff b-rad-10 pt-40 pl-24 pr-24">
					<view class="">
						<text class="font-36 font-wei text-3">评论</text>
						<text class="ml-08 font-28 text-6">{{commentTotal}}</text>
					</view>
					<view v-if="commentList.length" class="">
						<view class="bb-s-01-eeeeee pt-32 pb-32" v-for="(item, index) in commentList" :key="index">
							<view class="">
								<view class="d-flex a-center">
									<view class="p-rela w-72 h-72">
										<image class="w-72 h-72 b-rad-p50" :src="item.avatar_image" mode="aspectFill" />
										<image v-if="item.certified_info" class="p-abso right-0 bottom-0 w-24 h-26" src="https://images.vinehoo.com/vinehoomini/v3/comm/lv_gold.png" mode="aspectFill" />
									</view>
									
									<view class="d-flex a-center ml-12">
										<text class="font-32 font-wei text-3">{{item.nickname}}</text>
										<VhIconGrade :grade="item.user_level"/>
										<text v-if="item.is_buy" class="bg-li-1 b-rad-20 ml-10 ptb-04-plr-16 font-22 text-ffffff">已购</text>
									</view>
								</view>
								
								<view class="mt-22">
									<text class="ml-06 font-28 text-3">{{item.content}}</text>
								</view>
								
							
								<view v-if="item.emoji_image" class="w-120 b-rad-08 o-hid mt-22">
									<vh-image loading-type="2" :src="`https://images.vinehoo.com/vinehoomini/v3/emoticon/${getEmojiMap.get(item.emoji_image)}.gif`" :height="120" />
								</view>
								
								<view class="d-flex j-sb a-center mt-24">
									<view class="font-24 text-9 l-h-40">{{item.created_time}}</view>
									<view class="d-flex j-end">
										<view class="d-flex a-center" @click="thumbsUp(item)">
											<image class="w-26 h-26" :src="`https://images.vinehoo.com/vinehoomini/v3/comm/${item.is_like ? '' : 'u'}zan.png`" mode="aspectFill" />
											<text class="ml-06 font-28 text-9 l-h-40">{{item.likenums}}</text>
										</view>
										
										<view class="d-flex a-center ml-60" @click="commentAdd(1, item)">
											<image class="w-26 h-26 mt-04" src="https://images.vinehoo.com/vinehoomini/v3/comm/comm.png" mode="aspectFill" />
											<text class="ml-06 font-28 text-9 l-h-40">回复</text>
										</view>
									</view>
								</view>
								
								<view v-if="item.under_comment.length" class="bg-f8f8f8 b-rad-08 mt-20 p-24">
									<view class="mb-16" v-for="(inItem, inIndex) in item.under_comment" :key="inIndex" @click="commentAdd(1, inItem)">
										<view class="">
											<text class="font-28 text-9">{{inItem.first_id != inItem.pid ? `${inItem.nickname}@${inItem.reply_nickname}` : inItem.nickname}}：</text>
											<text class="font-28 text-3">{{inItem.content}}</text>
										</view>
										
										<view v-if="inItem.emoji_image" class="w-120 b-rad-08 o-hid mt-16">
											<vh-image loading-type="2" :src="`https://images.vinehoo.com/vinehoomini/v3/emoticon/${getEmojiMap.get(inItem.emoji_image)}.gif`" :height="120" />
										</view>
									</view>
									
								</view>
							</view>
						</view>
						
					    <view class="ptb-24-plr-00">
					    	<u-loadmore  :status="loadStatus" />
					    </view>
					</view>
					<view v-else class="d-flex j-center a-center pt-24 pb-24  font-28 text-d8d8d8">- 暂无评价 -</view>
				</view>
			</view> -->
			
			<!-- 扫码弹框 -->
			<view class="p-fixed bottom-160 right-24" @click="openScan">
			    <image class="w-92 h-92" :src="`${osip}/store_goods_detail/scan_red.png`" mode="aspectFill" />
			</view>
			
			<!-- 底部操作按钮 -->
			<view class="p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff b-sh-00021200-022">
				<!-- 没在门店附近 -->
				<view v-if="!nearTheStore" class="fade-in w-p100 h-104 d-flex j-center a-center">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'686rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}"
					@click="feedback.toast({title:'亲，您没在门店范围内~'})">未在门店范围内</u-button>
				</view>
			
				<!-- 在门店附近 -->
				<view v-else class="fade-in w-p100 h-104 d-flex j-center a-center">
					<!-- 商品已下架 -->
					<view v-if="goodsInfo.is_shelf == 0" class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'686rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}"
						@click="feedback.toast({title:'亲，该商品已下架~'})">商品已下架</u-button>
					</view>
					
					<!-- 商品正常售卖 -->
					<view v-else class="w-p100 h-104 d-flex j-sb a-center">
						<!-- 评论（评论暂时不做，因为做了也没人用）、购物车、加购 -->
						<view class="d-flex j-sb a-center">
							<view class="d-flex flex-column a-center ml-52" @click="jump.loginNavigateTo(routeTable.pBStoreShoppingCart)">
								<image class="w-32 h-32" :src="`${osip}/store_goods_detail/car.png`" mode="aspectFill" />
								<text class="font-22 text-9 l-h-32">购物车</text>
							</view>
							
							<view class="d-flex flex-column a-center ml-52" @click="addShoppingCart()">
								<image class="w-32 h-32" :src="`${osip}/store_goods_detail/add_car.png`" mode="aspectFill" />
								<text class="font-22 text-9 l-h-32">加购</text>
							</view>
						</view>
						
						<!-- 正常售卖中 -->
						<view class="d-flex a-center mr-24">
							<!-- 配置了杯卖商品（杯卖商品只允许场饮） -->
							<view v-if="goodsInfo.is_cup" class="">
								<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
								:custom-style="{width:'460rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" 
								@click="buyNow(3)">门店场饮</u-button>
							</view>
							
							<!-- 没有配置杯卖商品 -->
							<view v-else class="">
								<!-- 门店配置了自提跟场饮 -->
								<view v-if="hasSelfMention && hasFieldDrink" class="d-flex">
									<u-button :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
									:custom-style="{width:'230rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#FF9127', border:'none', borderRadius:'32rpx 0 0 32rpx'}" 
									@click="buyNow(2)">打包外带</u-button>
									<u-button :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
									:custom-style="{width:'230rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none', borderRadius:'0 32rpx 32rpx 0'}" 
									@click="buyNow(3)">门店场饮</u-button>
								</view>
								
								<!-- 门店只配置了外带 -->
								<view v-if="hasSelfMention && !hasFieldDrink">
									<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
									:custom-style="{width:'460rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#FF9127', border:'none'}" 
									@click="buyNow(2)">打包外带</u-button>
								</view>
								
								<!-- 门店只配置了场饮或者商品中包含杯买商品 -->
								<view v-if="!hasSelfMention && hasFieldDrink" class="">
									<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
									:custom-style="{width:'460rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" 
									@click="buyNow(3)">门店场饮</u-button>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 弹框 -->
			<view class="">
				<!-- 套餐弹框 -->
				<u-popup v-model="showGoodsPackPop" mode="bottom" :duration="150" :border-radius="20">
					<view class="pt-32 pr-24 pb-48 pl-24">
						<view class="d-flex">
							<vh-image :loading-type="2" :src="goodsInfo.goods_images[0]" :width="180" :height="180" :border-radius="6" mode="aspectFit"/>
			
							<view class="d-flex flex-1 flex-column j-sb ml-16">
								<view class="font-28 text-3 l-h-40 o-hid text-hidden-2">{{goodsInfo.goods_name}}</view>
								<view class="d-flex a-end">
									<text class="font-44 font-wei text-e80404 l-h-34"><text class="font-24 mr-06">¥</text>{{packageInfo.price}}</text>
									<text class="ml-10 font-24 text-9 text-dec-l-t l-h-34">¥{{packageInfo.market_price}}</text>
								</view>
							</view>
						</view>
						
						<view class="mt-48 font-32 font-wei text-3">规格</view>
						
						<view class="d-flex flex-wrap ml-n-24">
							<view v-for="(item, index) in packageList" :key="index">
								<view class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3" 
								:class="packageIndex != index ? '' : clickSelectPackage ? 'skew-top bg-fce4e3 b-s-01-e80404 text-e80404' : 'bg-fce4e3 b-s-01-e80404 text-e80404'" 
								@click="selectPackage(index)">{{item.c_name}}</view>
							</view>
						</view>
						
						<view class="d-flex j-sb a-center mt-52">
							<view class="font-32 font-wei text-3">数量</view>
							<view class="">
								<u-number-box v-model="purchaseNumbers" :min="1" :max="packageInfo.maxstock" :input-width="64" :input-height="50" :size="28" />
							</view>
						</view>
						
						<view v-if="packageInfo.stock_insufficient == 0 && packageInfo.is_enable == 1 && packageInfo.maxstock" class="mt-92 d-flex j-center a-center">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'646rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="confirm">确定</u-button>
						</view>
						<view v-else class="mt-92 d-flex j-center a-center">
							<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}" 
							@click="feedback.toast({ title: '该套餐库存不足~' })">库存不足</u-button>
						</view>
					</view>
				</u-popup>
						   
				<!-- 评论弹框 -->
				<!-- <u-popup v-model="showCommPop" :safe-area-inset-bottom="true" mode="bottom" :duration="50" :border-radius="20">
					<vh-comment :placeholder="commPlaceholder" @on-comment-add="onCommentAdd" @on-img-send="onImgSend" />
				</u-popup> -->
			</view>
		</view>
	</view>
</template>

<script>
	import wxMap from '@/common/js/fun/amap-wx.js';
	import { mapState, mapMutations } from 'vuex'
	export default {
		name:'store-goods-detail',
		
		data() {
			return {
				loading: true, //加载状态 true = 加载中、false = 结束加载
				osip:'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				amapPlugin: null, //初始化定位
				key: '6b15b01a3a5ed85f0e66797201febf92', //key
				nearTheStore: true, //是否在门店附近 默认false
				mSid:'', //门店id
				goodsId:'', //商品id
				// storeLatitude:'', //门店纬度
				// storeLongitude:'', //门店经度
				userLatitude:'', //用户纬度
				userLongitude:'', //用户经度
				goodsInfo:{}, //门店商品信息
				clickSelectPackage: 0, //是否点击显示套餐 0 = 点击购买、加入购物车、拼团、1 = 点击套餐选项
				packageIndex:0, //选中的套餐索引
				packageList:[], //套餐列表
				packageInfo:{}, //套餐信息
				purchaseNumbers: 1, //购买数量
				distributionId:0, //配送id
				hasSelfMention: 0, //是否拥有门店自提
				hasFieldDrink: 0, //是否拥有门店场饮
				
				// 评论板块
				commentList:[], //评论列表
				
				// 弹框
				// showCommPop: false, //评论弹框
				isAddShoppingCart: false, //是否加入购物车
				showGoodsPackPop: false, //套餐弹框
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['storeInfo', 'storeOrderInfo', 'routeTable']),
		},
		
		onLoad(options) {
			this.init(options)
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muStoreInfo', 'muStoreOrderInfo']),
			
			// 初始化 options = 页面传递参数
			async init( options ) {
				console.log(options)
				if(options.gid){ // 从小程序内部扫码进入
					console.log('------------小程序内部扫码')
					this.goodsId = parseInt(options.gid)
					this.searchWifi()
				}else if(options.q){ // 从微信扫码进入
					console.log('------------微信扫码')
					let url = decodeURIComponent(options.q)
					console.log( url )
					// this.storeLatitude = parseFloat(this.param.getUrlParam( url, 'lat' ))
					// this.storeLongitude = parseFloat(this.param.getUrlParam( url, 'lng' ))
					this.goodsId = parseInt(this.param.getUrlParam( url, 'gid' ))
					this.searchWifi()
				}else{ // 正常流程进入详情页
					console.log('------------商品列表进入')
					this.goodsId = parseInt(options.id)
					this.getStoreGoodsDetail()
				}
			},
			
			// 门店搜索wifi
			searchWifi() {
				wx.startWifi({
					success: res=> {
						wx.getConnectedWifi({
							success: s => {
								this.getStoreList(0, s.wifi.SSID )
							},
							fail: f => {
								this.feedback.toast({ title:'连接wifi失败, 我需要走定位'})
								this.position()
							}
						})
					},
					fail: err => {
						this.feedback.toast({ title:'我没有wifi, 我需要走定位'})
						this.position()
					}
				})
				
				// this.getStoreList(0, '123')
			},
			
			// 门店定位
			position() {
				uni.getSystemInfo({
					success: res => {
						console.log(res)
						if( res.locationEnabled ){
							uni.authorize({
								scope: 'scope.userLocation',
								success: res => { //1.1 允许授权
									this.feedback.toast({ title:'我允许了定位授权，开始获取经纬度'})
									this.mGetLocation()
								},
								fail: err => {    //1.2 拒绝授权
									uni.showModal({
										content:'检测到您没打开获取位置功能权限，无法获取更多服务，是否去设置打开？',
											confirmText: "确认",
											cancelText:'取消',
											success: ( res ) => {
												if( res.confirm ){
													uni.openSetting({
														success: ( res ) => {
															this.feedback.toast({ title:'我从不允许打开定位到允许了定位授权,获取经纬度'})
															this.mGetLocation()
														}
													})
												}else{
													// this.jump.reLaunch('/pages/index/index')
													this.feedback.toast({ title:'我弹框点击的还是不允许定位授权'})
												}
											}
									})		                            
								}
							})
						}else{
							this.feedback.toast({ title:'请打开定位~'})
						}
					}
				})
			},
			
			// 门店信息初始化 type = 类型（ 0 = wifi、1 = 定位（默认为1） ）、wifiName = wifi名称，默认为 ''
			async getStoreList( type = 1, wifiName = '') {
				let res = await this.$u.api.storeList()
				let { list } = res.data
				if( list.length ) { //线下门店后台配置了门店
					this.mSid = type == 0 ? this.mGetStoreIdByWifi( list, wifiName ) : this.mGetStoreIdByLocation( list ) //通过wifi或者定位获取门店id（ 目前酒云网线下门店不多，只考虑在配置半径范围内的门店，不考虑两个门店同时都在配置的半径范围内的情况 ）
					if( this.mSid ) { //搜索到了最近的门店
						this.mStoreGoodsInfo()
					}else{ //没有搜索到最近的门店
					    this.nearTheStore = false
						this.feedback.toast({ title:'附近没有搜索到门店呀！我得显示无门店占位图'})
					}
				}else{ //线下门店后台没有配置了门店
					this.feedback.toast({ title:'请配置门店！'})
				}
			},
			
			// 门店获取经纬度
			mGetLocation() {
				// 插件定位
				try{
					this.amapPlugin = new wxMap.AMapWX({ key: this.key });
					this.amapPlugin.getRegeo({
						success: ( res ) => {
							let { latitude, longitude } = res[0] //定位信息
							this.userLatitude = latitude //纬度
							this.userLongitude = longitude //经度
							console.log('当前位置的经度（longitude）：' + res[0].longitude);
							console.log('当前位置的纬度（latitude）：' + res[0].latitude);
							this.getStoreList()
						},
						fail: (err) => {
							this.feedback.toast({ title:'amapPlugin.getRegeo失败！'})
						}
					});
				}catch(e){
					this.feedback.toast({ title:'初始化amap失败，请排查错误！'})
				}
				
				// 普通定位
				// uni.getLocation({
				// 	accuracy:'best',
				// 	isHighAccuracy: true,
				// 	success: res => {
				// 		console.log('当前位置的经度：' + res.longitude);
				// 		console.log('当前位置的纬度：' + res.latitude);
						
				// 		// 106.487234,29.597693
						
				// 		console.log(this.gps.lonAndLatDis(29.597693, 106.487234, res.latitude, res.longitude ))
				// 		// 29.59555200 106.48984700
				// 	}
				// });
			},
			
			// 通过wifi获取门店id list = 门店列表、wifiName = wifi名称
			mGetStoreIdByWifi( list, wifiName ) {
				console.log(list)
				console.log(wifiName)
				let storeId = ''
				for(let i = 0; i < list.length; i++){
					for(let j = 0; j < list[i].wifilocation.length; j++){
						if(list[i].wifilocation[j] === wifiName){
							storeId = list[i].id
							break
						}
					}
				}
				return storeId
			},
			
			// 通过定位获取门店id list = 门店列表
			mGetStoreIdByLocation( list ) {
				let storeId = ''
				// 29.59555201 106.48984702
				// 29.59555200 106.48984700
				console.log(list)
				for( let item of list ) {
					if( this.gps.lonAndLatDis(29.59555201, 106.48984702, item.latitude, item.longitude ) <= item.radius ) {
						storeId = item.id
						break
					}
				}
				return storeId
			},
			
			// 获取门店信息
			async mStoreGoodsInfo() {
				let res = await this.$u.api.storeInfo({ sid: this.mSid })
				this.muStoreInfo(res.data)
				this.getStoreGoodsDetail()
				this.storeAddRegister()
			},
			
			// 门店添加用户
			storeAddRegister() {
				uni.getStorage({
					key:'loginInfo',
					success: async res => {
						try{
							let { uid, telephone, nickname, avatar_image, openid } = res.data
							await this.$u.api.storeAddRegister({uid, loginname: telephone, nickname, avatar_image, applet_openid: openid, sid: this.storeInfo.id})
						}catch(e){
							//TODO handle the exception
						}
					},
					fail: err => {
						console.log('-------------我没有用户信息')
					}
				})
			},
			
			// 门店商品详情
			async getStoreGoodsDetail() {
				try{
					let res = await this.$u.api.storeGoodsDetail({ gid: this.goodsId })
					const { collocation } = res.data
					this.goodsInfo = res.data //商品信息
					// this.goodsInfo.is_cup = 1
					this.packageList = collocation.filter( v => { return v.is_enable == 1 }) //套餐列表
					this.packageInfo = this.packageList[this.packageIndex] //套餐信息
					this.judgeDistributionType() //判断配送方式
					this.loading = false
					console.log(res)
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 打开扫码
			openScan() {
				uni.scanCode({
					success: res => {
						if(res.result.indexOf('gid') > -1){
							let url = res.result
							this.goodsId = parseInt(this.param.getUrlParam( url, 'gid' ))
							this.searchWifi()
						}else this.feedback.toast({ title:"请扫描瓶身上的二维码喔~", duration:3000 })
					},
				})
			},
			
			// 判断物流类型
			judgeDistributionType() {
				console.log('----------------------我是门店信息')
				console.log(this.storeInfo)
				this.storeInfo.purchase_mode_list.forEach( v => {
					if( v.id == 2) {
						this.hasSelfMention = 1
					}
					if( v.id == 3) {
						this.hasFieldDrink = 1
					}
				})
			},
			
			// 加入购物车
			addShoppingCart(){
			    if(this.login.isLogin()){
				   this.purchaseNumbers = 1
				   this.isAddShoppingCart = true
				   this.showGoodsPackPop = true
			    }
			},
			
			// 确认加入购物车
			async confirmAddShoppingCart(){
				console.log('-------------------我是确认加入购物车')
				try{
					let res = await this.$u.api.storeGoodsAddShoppingCart({
						goods_id: this.goodsId, 
						pack_id: this.packageInfo.id, 
						nums: this.purchaseNumbers,
					})
					this.feedback.toast({title: '加入成功', icon: 'success'})
				}catch(e){
					//TODO handle the exception
					console.log(e)
				}
			},
			
			// 选择套餐 index = 列表索引
			selectPackage(index){
				this.clickSelectPackage = 1
				this.packageIndex = index
				this.packageInfo = this.packageList[this.packageIndex]
			},
			
			// 立即购买 id 配送方式id 2 = 打包外带、3 = 门店场饮
			buyNow( id ){
				if(this.login.isLogin()){
					 this.purchaseNumbers = 1
					 this.distributionId = id
					 this.packageIndex = 0
					 this.isAddShoppingCart = false
					 this.clickSelectPackage = 0
					 this.showGoodsPackPop = true
				}
			},
			
			// 确认立即购买
			confirmBuyNow() {
				console.log('-----------------------------我是立即购买')
				let storeOrderInfo = { //门店订单信息
				    order_source: 2, //订单来源 1：购物车 2：立即购买
					distribution_id: this.distributionId, //配送id
					storeOrderGoodsList: [], //门店订单商品信息
				}
				let { goods_images, goods_name, is_cup } = this.goodsInfo //商品信息
				let { id, c_name, goods_id } = this.packageInfo //商品信息
				let item = {} //订单商品信息
				item.goods_name = goods_name, //商品名称
				item.goods_image = goods_images[0], //商品图片
				item.goods_id = goods_id, //商品id
				item.pack_id = id //套餐id
				item.c_name = c_name //套餐名称
				item.nums = this.purchaseNumbers //购买数量
				item.is_cup = is_cup, //是否为杯卖订单 0 = 非杯卖商品 1 = 杯卖商品
				storeOrderInfo.storeOrderGoodsList.push(item) //追加商品
				this.muStoreOrderInfo(storeOrderInfo)
				this.jump.navigateTo(`/packageB/pages/store-order-confirm/store-order-confirm`)
			},
			
			// 确认
			confirm(){
				if(this.packageList.length == 0) return this.feedback.toast({title:'请选择套餐~'})
				this.showGoodsPackPop = false
				this.isAddShoppingCart ? this.confirmAddShoppingCart() : this.confirmBuyNow()
			}
		}
	}
</script>

<style scoped>
	/* 计数器样式 */
	::v-deep .u-numberbox{
		border: 1px solid #EEEEEE;
		border-radius: 12rpx;
	}
	
	::v-deep .u-icon-minus, ::v-deep .u-icon-plus{
		width: 50rpx!important;
		background-color: #FFFFFF!important;
	}
	
	::v-deep .uicon-minus, ::v-deep .uicon-plus{
		font-size: 24rpx!important;
		color: #666!important;
	}
</style>
