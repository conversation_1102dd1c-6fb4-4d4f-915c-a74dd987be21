<template>
  <view class="content h-min-vh-100 bg-f5f5f5" :class="loading ? 'h-vh-100 o-hid' : ''">
    <!-- 导航栏 -->
    <view class="o-hid">
      <u-navbar :is-back="false">
        <view class="ml-12">
          <u-tabs
            ref="uTabs"
            :list="tabsList"
            :current="currentTabs"
            :height="92"
            :bg-color="{ background: 'transparent' }"
            :font-size="36"
            inactive-color="#999"
            active-color="#E80404"
            :offset="[15, 40]"
            :bar-width="36"
            :bar-height="8"
            :bar-style="{ background: 'linear-gradient(214deg, #FF8383 0%, #E70000 100%)' }"
            @change="changeTabs"
          ></u-tabs>
        </view>

        <view slot="right" class="d-flex a-center">
          <view class="">
            <image
              class="mr-20 w-40 h-40"
              src="https://images.vinehoo.com/vinehoomini/v3/comm/search-comm.png"
              mode="aspectFill"
              @click="jump.appAndMiniJump(1, `${routeTable.pFGlobalSearch}?type=3`, $vhFrom)"
            ></image>
          </view>
          <view class="p-rela" @click="jump.appAndMiniJump(1, `${routeTable.pEMessageCenter}`, $vhFrom, 0, true)">
            <image
              class="mr-24 w-40 h-40"
              src="https://images.vinehoo.com/vinehoomini/v3/comm/message-comm.png"
              mode="widthFix"
            ></image>
            <view
              v-if="unReadTotalNum"
              class="p-abso right-24 top-0 w-10 h-10 bg-e80404 b-rad-p50 d-flex j-center a-center font-18 text-ffffff"
            />
            <!-- <view
                class="p-abso right-15 top-n-06 w-26 h-26 bg-e80404 b-rad-p50 d-flex j-center a-center font-18 text-ffffff"
                >99</view> -->
          </view>
        </view>
      </u-navbar>
    </view>

    <!-- 数据内容 -->
    <view v-if="!loading" class="fade-in">
      <!-- 内容板块（社区、视频、直播） -->
      <!-- 社区 -->
      <view v-show="currentTabs == 0" class="">
        <!-- 话题 battle -->
        <view class="bg-ffffff b-rad-20 mt-20 ml-24 mr-24 pt-16 pr-24 pb-24 pl-24" v-if="false">
          <!-- 更多battle -->
          <view class="d-flex j-sb a-center">
            <view class="d-flex a-center">
              <image
                class="w-44 h-44"
                src="https://images.vinehoo.com/vinehoomini/v3/community/vs.png"
                mode="aspectFill"
              ></image>
              <text class="ml-06 font-32 font-wei text-3 l-h-52">话题battle</text>
            </view>

            <view class="d-flex a-center">
              <text class="mr-10 font-24 text-9">查看全部</text>
              <u-icon name="arrow-right" :size="20" color="#999"></u-icon>
            </view>
          </view>

          <!-- 单个battle -->
          <view v-if="false" class="bg-li-3 b-rad-20 mt-20 p-24">
            <!-- battle标题 -->
            <view class="font-28 font-wei text-3 l-h-40 text-hidden-2 o-hid"
              >酿葡萄酒，现在农业流行的生物动力法是否还可以清晰地解释其中部分原因，这同样是个难题带我去逗你玩群的完全多群无。</view
            >

            <!-- pk板块 -->
            <!-- 未pk -->
            <view v-if="false" class="p-rela h-180 mt-24 mb-60">
              <view class="p-rela h-p100 d-flex j-center a-center">
                <image
                  class="p-abso w-p100"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/pking_bg.png"
                  mode="aspectFit"
                ></image>
                <text class="z-01 ml-20 font-34 font-wei text-3">科学</text>
                <image
                  class="z-01 w-160 h-112 ml-54 mr-36"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/pking.png"
                  mode="aspectFill"
                ></image>
                <text class="z-01 font-34 font-wei text-3">不科学</text>
              </view>

              <view class="">
                <view class="p-abso z-02 left-52 bottom-n-24">
                  <u-button
                    shape="circle"
                    :hair-line="false"
                    :ripple="true"
                    ripple-bg-color="#FFF"
                    :custom-style="{
                      width: '200rpx',
                      height: '60rpx',
                      fontSize: '28rpx',
                      color: '#FFF',
                      backgroundColor: '#2E7BFF',
                      border: 'none',
                    }"
                    >蓝队</u-button
                  >
                </view>
                <view class="p-abso z-01 left-52 bottom-n-32 w-200 h-60 bg-e2ebfa b-rad-46"></view>

                <view class="p-abso z-02 right-52 bottom-n-24">
                  <u-button
                    shape="circle"
                    :hair-line="false"
                    :ripple="true"
                    ripple-bg-color="#FFF"
                    :custom-style="{
                      width: '200rpx',
                      height: '60rpx',
                      fontSize: '28rpx',
                      color: '#FFF',
                      backgroundColor: '#E80404',
                      border: 'none',
                    }"
                    >红队</u-button
                  >
                </view>
                <view class="p-abso z-01 right-52 bottom-n-32 w-200 h-60 bg-fce0e0 b-rad-46"></view>
              </view>
            </view>

            <!-- 选择蓝方、蓝方获胜（投票） -->
            <view v-if="false" class="p-rela h-180 mt-24 mb-60">
              <view class="p-rela h-p100 d-flex j-center a-center">
                <image
                  class="p-abso w-p100"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/pking_bg.png"
                  mode="aspectFit"
                ></image>
                <view class="z-01 d-flex a-center">
                  <image
                    class="w-26 h-26"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/sel_blu.png"
                    mode="aspectFill"
                  ></image>
                  <text class="ml-10 font-30 font-wei text-2e7bff">科学</text>
                </view>
                <image
                  class="z-01 w-160 h-112 mr-52 mb-16 ml-52"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/pking.png"
                  mode="aspectFill"
                ></image>
                <text class="z-01 font-30 font-wei text-aaaaaa">不科学</text>
              </view>

              <view class="p-abso z-01 bottom-n-24 w-p100 d-flex j-center a-center">
                <view class="w-558 h-50 d-flex b-rad-26 o-hid">
                  <view class="p-rela w-p70 h-p100 bg-2e7bff d-flex a-center t-sk-x-n-10">
                    <view class="p-abso left-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff">{{
                      index == 1 ? '70%' : '胜'
                    }}</view>
                  </view>
                  <view class="p-rela w-p30 h-p100 bg-ffcdcd d-flex j-end a-center t-sk-x-n-10">
                    <view class="p-abso right-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff">{{
                      index == 1 ? '30%' : '败'
                    }}</view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 选择红方、红方获胜（投票） -->
            <view v-if="false" class="p-rela h-180 mt-24 mb-60">
              <view class="p-rela h-p100 d-flex j-center a-center">
                <image
                  class="p-abso w-p100"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/pking_bg.png"
                  mode="aspectFit"
                ></image>
                <text class="z-01 ml-10 font-30 font-wei text-aaaaaa">科学</text>
                <image
                  class="z-01 w-160 h-112 mr-52 mb-16 ml-52"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/pking.png"
                  mode="aspectFill"
                ></image>
                <view class="z-01 d-flex a-center">
                  <image
                    class="w-26 h-26"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/sel_red.png"
                    mode="aspectFill"
                  ></image>
                  <text class="ml-10 font-30 font-wei text-e80404">不科学</text>
                </view>
              </view>

              <view class="p-abso z-01 bottom-n-24 w-p100 d-flex j-center a-center">
                <view class="w-558 h-50 d-flex b-rad-26 o-hid">
                  <view class="p-rela w-p30 h-p100 bg-a9c9ff d-flex a-center t-sk-x-n-10">
                    <view class="p-abso left-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff">{{
                      index == 2 ? '30%' : '败'
                    }}</view>
                  </view>
                  <view class="p-rela w-p70 h-p100 bg-e80404 d-flex j-end a-center t-sk-x-n-10">
                    <view class="p-abso right-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff">{{
                      index == 2 ? '70%' : '胜'
                    }}</view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 选择蓝方、蓝方获胜（未投票） -->
            <view v-if="false" class="p-rela h-180 mt-24 mb-60">
              <view class="p-rela h-p100 d-flex j-center a-center">
                <image
                  class="p-abso w-p100"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/pked_bg.png"
                  mode="aspectFit"
                ></image>
                <text class="z-01 font-34 font-wei text-3">科学</text>
                <image
                  class="z-01 w-160 h-112 ml-64 mb-16 mr-40"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/pked.png"
                  mode="aspectFill"
                ></image>
                <text class="z-01 font-34 font-wei text-3">不科学</text>
              </view>

              <view class="p-abso z-01 bottom-n-24 w-p100 d-flex j-center a-center">
                <view class="w-558 h-50 d-flex b-rad-26 o-hid">
                  <view class="p-rela w-p70 h-p100 bg-aaaaaa d-flex a-center t-sk-x-n-10">
                    <view class="p-abso left-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff">胜</view>
                  </view>
                  <view class="p-rela w-p30 h-p100 bg-e0e0e0 d-flex j-end a-center t-sk-x-n-10">
                    <view class="p-abso right-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff"
                      >败</view
                    >
                  </view>
                </view>
              </view>
            </view>

            <!-- 选择红方、红方获胜（未投票） -->
            <view v-if="true" class="p-rela h-180 mt-24 mb-60">
              <view class="p-rela h-p100 d-flex j-center a-center">
                <image
                  class="p-abso w-p100"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/pked_bg.png"
                  mode="aspectFit"
                ></image>
                <text class="z-01 font-34 font-wei text-3">科学</text>
                <image
                  class="z-01 w-160 h-112 ml-64 mb-16 mr-40"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/pked.png"
                  mode="aspectFill"
                ></image>
                <text class="z-01 font-34 font-wei text-3">不科学</text>
              </view>

              <view class="p-abso z-01 bottom-n-24 w-p100 d-flex j-center a-center">
                <view class="w-558 h-50 d-flex b-rad-26 o-hid">
                  <view class="p-rela w-p30 h-p100 bg-e0e0e0 d-flex a-center t-sk-x-n-10">
                    <view class="p-abso left-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff">败</view>
                  </view>
                  <view class="p-rela w-p70 h-p100 bg-aaaaaa d-flex j-end a-center t-sk-x-n-10">
                    <view class="p-abso right-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff"
                      >胜</view
                    >
                  </view>
                </view>
              </view>
            </view>

            <!-- 结束时间 -->
            <view class="d-flex j-sb a-center mt-32">
              <view class="d-flex a-center">
                <image
                  class="w-26 h-26"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/bat_has_time.png"
                  mode="aspectFill"
                ></image>
                <text class="ml-10 font-24 text-e80404">结束时间：2021.10.05</text>
              </view>

              <view v-if="false" class="d-flex a-center">
                <image
                  class="w-26 h-26"
                  src="https://images.vinehoo.com/vinehoomini/v3/battle/bat_no_time.png"
                  mode="aspectFill"
                ></image>
                <text class="ml-10 font-24 text-6">battle 已结束</text>
              </view>

              <view class="d-flex a-center">
                <view class="d-flex a-center">
                  <image
                    class="w-36 h-36 b-rad-p50 ml-n-10"
                    src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_rec_bg.png"
                    mode="aspectFill"
                  ></image>
                  <image
                    class="w-36 h-36 b-rad-p50 ml-n-10"
                    src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_rec_bg.png"
                    mode="aspectFill"
                  ></image>
                  <image
                    class="w-36 h-36 b-rad-p50 ml-n-10"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/join_more.png"
                    mode="aspectFill"
                  ></image>
                </view>

                <view class="ml-10">
                  <text class="mr-06 font-24 text-757575">1005人已参与</text>
                  <u-icon name="arrow-right" :size="20" color="#757575"></u-icon>
                </view>
              </view>
            </view>
          </view>

          <!-- 多个battle -->
          <view class="d-flex o-scr-x o-hid">
            <view
              class="w-618 flex-shrink b-rad-20 mt-20 mb-20 mr-20 p-24"
              :class="index < 3 ? 'bg-li-3' : 'bg-f5f5f5'"
              v-for="(item, index) in 7"
              :key="index"
            >
              <!-- battle标题 -->
              <view class="font-28 font-wei text-3 l-h-40 text-hidden-2 o-hid" :class="index >= 3 ? 'text-6' : ''"
                >酿葡萄酒，现在农业流行的生物动力法是否还可以清晰地解释其中部分原因，这同样是个难题。</view
              >

              <!-- pk板块 -->
              <!-- 未pk -->
              <view v-if="index == 0" class="p-rela h-180 mt-24 mb-60">
                <view class="p-rela h-p100 d-flex j-center a-center">
                  <image
                    class="p-abso w-p100"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/pking_bg.png"
                    mode="aspectFit"
                  ></image>
                  <text class="z-01 ml-20 font-34 font-wei text-3">科学</text>
                  <image
                    class="z-01 w-160 h-112 mr-48 ml-30"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/pking.png"
                    mode="aspectFill"
                  ></image>
                  <text class="z-01 font-34 font-wei text-3">不科学</text>
                </view>

                <view class="">
                  <view class="p-abso z-02 left-24 bottom-n-24">
                    <u-button
                      shape="circle"
                      :hair-line="false"
                      :ripple="true"
                      ripple-bg-color="#FFF"
                      :custom-style="{
                        width: '200rpx',
                        height: '60rpx',
                        fontSize: '28rpx',
                        color: '#FFF',
                        backgroundColor: '#2E7BFF',
                        border: 'none',
                      }"
                      >蓝队</u-button
                    >
                  </view>
                  <view class="p-abso z-01 left-24 bottom-n-32 w-200 h-60 bg-e2ebfa b-rad-46"></view>

                  <view class="p-abso z-02 right-24 bottom-n-24">
                    <u-button
                      shape="circle"
                      :hair-line="false"
                      :ripple="true"
                      ripple-bg-color="#FFF"
                      :custom-style="{
                        width: '200rpx',
                        height: '60rpx',
                        fontSize: '28rpx',
                        color: '#FFF',
                        backgroundColor: '#E80404',
                        border: 'none',
                      }"
                      >红队</u-button
                    >
                  </view>
                  <view class="p-abso z-01 right-24 bottom-n-32 w-200 h-60 bg-fce0e0 b-rad-46"></view>
                </view>
              </view>

              <!-- 选择蓝方、蓝方获胜（投票） -->
              <view v-if="index == 1 || index == 3" class="p-rela h-180 mt-24 mb-60">
                <view class="p-rela h-p100 d-flex j-center a-center">
                  <image
                    class="p-abso w-p100"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/pking_bg.png"
                    mode="aspectFit"
                  ></image>
                  <view class="z-01 d-flex a-center">
                    <image
                      class="w-26 h-26"
                      src="https://images.vinehoo.com/vinehoomini/v3/battle/sel_blu.png"
                      mode="aspectFill"
                    ></image>
                    <text class="ml-10 font-30 font-wei text-2e7bff">科学</text>
                  </view>
                  <image
                    class="z-01 w-160 h-112 mr-52 mb-16 ml-52"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/pking.png"
                    mode="aspectFill"
                  ></image>
                  <text class="z-01 font-30 font-wei text-aaaaaa">不科学</text>
                </view>

                <view class="p-abso z-01 bottom-n-24 w-p100 d-flex j-center a-center">
                  <view class="w-522 h-50 d-flex b-rad-26 o-hid">
                    <view class="p-rela w-p70 h-p100 bg-2e7bff d-flex a-center t-sk-x-n-10">
                      <view class="p-abso left-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff">{{
                        index == 1 ? '70%' : '胜'
                      }}</view>
                    </view>
                    <view class="p-rela w-p30 h-p100 bg-ffcdcd d-flex j-end a-center t-sk-x-n-10">
                      <view class="p-abso right-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff">{{
                        index == 1 ? '30%' : '败'
                      }}</view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 选择红方、红方获胜（投票） -->
              <view v-if="index == 2 || index == 4" class="p-rela h-180 mt-24 mb-60">
                <view class="p-rela h-p100 d-flex j-center a-center">
                  <image
                    class="p-abso w-p100"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/pking_bg.png"
                    mode="aspectFit"
                  ></image>
                  <text class="z-01 ml-10 font-30 font-wei text-aaaaaa">科学</text>
                  <image
                    class="z-01 w-160 h-112 mr-52 mb-16 ml-52"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/pking.png"
                    mode="aspectFill"
                  ></image>
                  <view class="z-01 d-flex a-center">
                    <image
                      class="w-26 h-26"
                      src="https://images.vinehoo.com/vinehoomini/v3/battle/sel_red.png"
                      mode="aspectFill"
                    ></image>
                    <text class="ml-10 font-30 font-wei text-e80404">不科学</text>
                  </view>
                </view>

                <view class="p-abso z-01 bottom-n-24 w-p100 d-flex j-center a-center">
                  <view class="w-522 h-50 d-flex b-rad-26 o-hid">
                    <view class="p-rela w-p30 h-p100 bg-a9c9ff d-flex a-center t-sk-x-n-10">
                      <view class="p-abso left-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff">{{
                        index == 2 ? '30%' : '败'
                      }}</view>
                    </view>
                    <view class="p-rela w-p70 h-p100 bg-e80404 d-flex j-end a-center t-sk-x-n-10">
                      <view class="p-abso right-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff">{{
                        index == 2 ? '70%' : '胜'
                      }}</view>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 选择蓝方、蓝方获胜（未投票） -->
              <view v-if="index == 5" class="p-rela h-180 mt-24 mb-60">
                <view class="p-rela h-p100 d-flex j-center a-center">
                  <image
                    class="p-abso w-p100"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/pked_bg.png"
                    mode="aspectFit"
                  ></image>
                  <text class="z-01 font-34 font-wei text-3">科学</text>
                  <image
                    class="z-01 w-160 h-112 ml-64 mb-16 mr-40"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/pked.png"
                    mode="aspectFill"
                  ></image>
                  <text class="z-01 font-34 font-wei text-3">不科学</text>
                </view>

                <view class="p-abso z-01 bottom-n-24 w-p100 d-flex j-center a-center">
                  <view class="w-522 h-50 d-flex b-rad-26 o-hid">
                    <view class="p-rela w-p70 h-p100 bg-aaaaaa d-flex a-center t-sk-x-n-10">
                      <view class="p-abso left-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff"
                        >胜</view
                      >
                    </view>
                    <view class="p-rela w-p30 h-p100 bg-e0e0e0 d-flex j-end a-center t-sk-x-n-10">
                      <view class="p-abso right-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff"
                        >败</view
                      >
                    </view>
                  </view>
                </view>
              </view>

              <!-- 选择红方、红方获胜（未投票） -->
              <view v-if="index == 6" class="pked-bg-con p-rela h-180 mt-24 mb-60">
                <view class="h-p100 d-flex j-center a-center">
                  <image
                    class="p-abso w-p100"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/pked_bg.png"
                    mode="aspectFit"
                  ></image>
                  <text class="z-01 font-34 font-wei text-3">科学</text>
                  <image
                    class="z-01 w-160 h-112 ml-64 mb-16 mr-40"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/pked.png"
                    mode="aspectFill"
                  ></image>
                  <text class="z-01 font-34 font-wei text-3">不科学</text>
                </view>

                <view class="p-abso z-01 bottom-n-24 w-p100 d-flex j-center a-center">
                  <view class="w-522 h-50 d-flex b-rad-26 o-hid">
                    <view class="p-rela w-p30 h-p100 bg-e0e0e0 d-flex a-center t-sk-x-n-10">
                      <view class="p-abso left-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff"
                        >败</view
                      >
                    </view>
                    <view class="p-rela w-p70 h-p100 bg-aaaaaa d-flex j-end a-center t-sk-x-n-10">
                      <view class="p-abso right-24 h-p100 d-flex a-center t-sk-x-10 font-28 font-wei text-ffffff"
                        >胜</view
                      >
                    </view>
                  </view>
                </view>
              </view>

              <!-- 结束时间 -->
              <view class="d-flex j-sb a-center mt-32">
                <view v-if="index < 3" class="d-flex a-center">
                  <image
                    class="w-26 h-26"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/bat_has_time.png"
                    mode="aspectFill"
                  ></image>
                  <text class="ml-10 font-24 text-e80404">结束时间：2021.10.05</text>
                </view>

                <view v-else class="d-flex a-center">
                  <image
                    class="w-26 h-26"
                    src="https://images.vinehoo.com/vinehoomini/v3/battle/bat_no_time.png"
                    mode="aspectFill"
                  ></image>
                  <text class="ml-10 font-24 text-6">battle 已结束</text>
                </view>

                <view class="d-flex a-center">
                  <view class="d-flex a-center">
                    <image
                      class="w-36 h-36 b-rad-p50 ml-n-10"
                      src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_rec_bg.png"
                      mode="aspectFill"
                    ></image>
                    <image
                      class="w-36 h-36 b-rad-p50 ml-n-10"
                      src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_rec_bg.png"
                      mode="aspectFill"
                    ></image>
                    <image
                      class="w-36 h-36 b-rad-p50 ml-n-10"
                      src="https://images.vinehoo.com/vinehoomini/v3/battle/join_more.png"
                      mode="aspectFill"
                    ></image>
                  </view>

                  <view class="ml-10">
                    <text class="font-24 text-757575">1005人已参与</text>
                    <u-icon name="arrow-right" :size="20" color="#757575"></u-icon>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 热门话题 -->
        <view class="bg-ffffff b-rad-20 mt-20 ml-24 mr-24 pt-16 pr-24 pb-10 pl-24">
          <view class="d-flex j-sb a-center">
            <view class="d-flex a-center">
              <image
                class="w-44 h-44"
                src="https://images.vinehoo.com/vinehoomini/v3/community/red_top_ico.png"
                mode="aspectFill"
              ></image>
              <text class="ml-10 font-32 font-wei text-3 l-h-52">热门话题</text>
            </view>
            <view class="d-flex a-center" @click="jump.appAndMiniJump(1, `${routeTable.pCTopicMore}`, $vhFrom)">
              <text class="mr-10 font-24 text-9">更多话题</text>
              <u-icon name="arrow-right" :size="20" color="#999"></u-icon>
            </view>
          </view>

          <view class="top-list d-flex flex-wrap a-center mt-18">
            <view
              v-for="(item, index) in topicList"
              :key="index"
              class="w-p50 d-flex a-center mb-20"
              @tap="goToTopicDetail(item.id)"
            >
              <view class="w-max-252 font-28 text-3 o-hid text-hidden-1">{{ item.title }}</view>
              <image
                v-if="item.isrecommend"
                class="ml-20 w-44 h-26"
                src="https://images.vinehoo.com/vinehoomini/v3/comm/new.png"
                mode="aspectFill"
              ></image>
              <image
                v-if="item.ishot"
                class="ml-10 w-46 h-28"
                src="https://images.vinehoo.com/vinehoomini/v3/comm/hot.png"
                mode="aspectFill"
              ></image>
            </view>

            <view class="d-flex a-center mb-20">
              <text class="bg-e2ebfa b-rad-20 ptb-02-plr-22 font-22 text-2e7bff l-h-32" @click="refreshTopics"
                >换一换</text
              >
            </view>
          </view>
        </view>

        <!-- 帖子列表 -->
        <template v-if="communityList.length > 0">
          <postItemList :list="communityList" :isFollowList="false"></postItemList>
        </template>
        <vh-empty
          v-else
          bgColor="transparent"
          :padding-top="100"
          :padding-bottom="100"
          :image-src="ossIcon('/empty/emp_goods.png')"
          text="暂无数据"
          :text-bottom="0"
        />
      </view>

      <!-- 酒评 -->
      <view v-show="currentTabs == 1" class="">
        <template v-if="wineList.length > 0">
          <postItemList :list="wineList" :isFollowList="false"></postItemList>
        </template>
        <vh-empty
          v-else
          bgColor="transparent"
          :padding-top="100"
          :padding-bottom="100"
          :image-src="ossIcon('/empty/emp_goods.png')"
          text="暂无数据"
          :text-bottom="0"
        />
      </view>

      <!-- 关注 -->
      <view v-show="currentTabs == 2" class="">
        <template v-if="followList.length > 0">
          <postItemList :list="followList" :isFollowList="false"></postItemList>
        </template>
        <vh-empty
          v-else
          bgColor="transparent"
          :padding-top="100"
          :padding-bottom="100"
          :image-src="ossIcon('/empty/emp_goods.png')"
          text="暂无数据"
          :text-bottom="0"
        />
      </view>
    </view>

    <!-- 骨架屏 -->
    <view v-else class="fade-in">
      <vh-skeleton :type="6" :has-tab-bar="true" />
    </view>

    <!-- 底部导航栏 tabBar -->
    <!-- <vh-tabbar v-if="$vhFrom == ''" /> -->
  </view>
</template>

<script>
import postItemList from './postList.vue'
import { mapState, mapMutations } from 'vuex'
export default {
  components: {
    postItemList,
  },
  name: 'community',
  computed: {
    ...mapState(['routeTable']),
  },

  data() {
    return {
      loading: true, //加载状态 true = 加载中、false = 结束加载
      tabsList: [
        //tabs列表
        { name: '推荐' },
        { name: '酒评' },
        { name: '关注' },
      ],
      currentTabs: 0, //tabs当前选中页
      topicList: [], // 用于存储话题列表
      communityList: [], // 社区列表数据
      unReadTotalNum: 0,
      page: 1, // 当前页码
      loading: true, // 加载状态
      isLoadMore: false, // 是否正在加载更多
      hasMore: true, // 是否还有更多数据
      followList: [], // 关注列表
      followPage: 1, // 关注列表页码
      followLoading: false, // 关注列表加载状态
      followHasMore: true, // 关注列表是否还有更多
      wineList: [], // 酒评列表
      winePage: 1, // 酒评列表页码
      wineLoading: false, // 酒评列表加载状态
      wineHasMore: true, // 酒评列表是否还有更多
      lastLoginInfo: null, // 用于存储上一次的登录信息
    }
  },

  onLoad(options) {
    this.system.setNavigationBarBlack()
    this.getCommunityList() // 获取社区列表
    this.getTopicList()
    this.getMessageUnreadNum()
    // 保存初始登录信息
    this.lastLoginInfo = uni.getStorageSync('loginInfo')

    // 处理登录后跳回关注 tab
    if (options.tab === '2') {
      this.currentTabs = 2
      this.getFollowList()
    }
  },

  onShow() {
    // 获取当前登录信息
    const currentLoginInfo = uni.getStorageSync('loginInfo')

    // 比较登录信息是否发生变化
    if (JSON.stringify(this.lastLoginInfo) !== JSON.stringify(currentLoginInfo)) {
      console.log('登录状态发生变化，刷新数据')
      // 更新存储的登录信息
      this.lastLoginInfo = currentLoginInfo

      // 重置页面数据
      this.page = 1
      this.winePage = 1
      this.followPage = 1
      this.communityList = []
      this.wineList = []
      this.followList = []

      // 刷新当前标签页数据
      switch (this.currentTabs) {
        case 0:
          Promise.all([this.getTopicList(), this.getCommunityList(false, true), this.getMessageUnreadNum()])
          break
        case 1:
          this.getWineList(false, true)
          break
        case 2:
          this.getFollowList(false, true)
          break
      }
    }
  },

  methods: {
    // tabs切换
    changeTabs(index) {
      // 如果是关注 tab 且未登录,则跳转到登录页
      if (index === 2 && !this.login.isLogin(this.$vhFrom, 0)) {
        if (this.$vhFrom == '1' || this.$vhFrom == '2' || this.$vhFrom == 'next') {
          wineYunJsBridge.openAppPage({
            client_path: { ios_path: 'login', android_path: 'login' },
          })
        } else {
          uni.setStorageSync('login_back_url', '/pages/community/community?tab=2')
          uni.navigateTo({
            url: '/pages/login/login',
          })
        }
        return
      }

      this.currentTabs = index
      if (index === 2) {
        // 关注列表逻辑
        this.followPage = 1
        this.followList = []
        this.followHasMore = true
        this.getFollowList()
      } else if (index === 1) {
        // 酒评列表逻辑
        this.winePage = 1
        this.wineList = []
        this.wineHasMore = true
        this.getWineList()
      } else if (index === 0) {
        // 推荐列表逻辑
        this.page = 1
        this.communityList = []
        this.hasMore = true
        this.getCommunityList()
      }
    },
    // 获取话题列表
    async getTopicList() {
      try {
        const res = await this.$u.api.getTopicList({
          type: 2, // 话题首页换一换
          limit: 5, // 获取5个话题
        })
        if (res.error_code === 0) {
          this.topicList = res.data.list
        } else {
          this.$u.toast(res.error_msg)
        }
      } catch (error) {
        console.error('获取话题列表失败:', error)
      }
    },

    // 换一换功能
    refreshTopics() {
      this.getTopicList()
    },
    async getMessageUnreadNum() {
      if (this.login.isLogin(this.$vhFrom, 0)) {
        let res = await this.$u.api.messageUnreadNum()
        this.unReadTotalNum = res.data.total_num
      }
    },
    // 获取社区列表
    async getCommunityList(isLoadMore = false, isRefresh = false) {
      if (this.isLoadMore || (!this.hasMore && !isRefresh)) {
        return Promise.resolve()
      }

      try {
        this.isLoadMore = true
        console.log('开始加载数据，页码：', this.page)

        const res = await this.$u.api.getCommunityList({
          page: isRefresh ? 1 : this.page,
          limit: 5,
        })

        if (res.error_code === 0) {
          const list = res.data.list || []
          console.log('获取到数据条数：', list.length)
          console.log('返回的数据：', res.data)

          if (isRefresh) {
            this.page = 1
            this.communityList = list
            this.hasMore = true
          } else if (isLoadMore) {
            this.communityList = [...this.communityList, ...list]
          } else {
            this.communityList = list
          }

          if (res.data.total) {
            this.hasMore = this.communityList.length < res.data.total
          } else {
            this.hasMore = list.length >= 5
          }

          console.log('是否还有更多：', this.hasMore, '当前列表总长度：', this.communityList.length)
        } else {
          this.$u.toast(res.error_msg)
        }
      } catch (error) {
        console.error('获取社区列表失败:', error)
      } finally {
        this.isLoadMore = false
        this.loading = false
      }
    },

    // 滚动到底部触发
    onReachBottom() {
      console.log('触发底部加载', {
        currentTabs: this.currentTabs,
        hasMore: this.hasMore,
        isLoadMore: this.isLoadMore,
      })

      if (this.currentTabs === 2) {
        // 关注列表的加载更多
        if (this.followHasMore && !this.followLoading) {
          this.followPage++
          this.getFollowList(true)
        }
      } else if (this.currentTabs === 1) {
        // 酒评列表的加载更多
        if (this.wineHasMore && !this.wineLoading) {
          this.winePage++
          this.getWineList(true)
        }
      } else {
        // 社区列表的加载更多
        if (this.hasMore && !this.isLoadMore) {
          this.page++
          this.getCommunityList(true)
        }
      }
    },

    // 获取关注列表
    async getFollowList(isLoadMore = false, isRefresh = false) {
      if (this.followLoading || (!this.followHasMore && !isRefresh)) return Promise.resolve()

      try {
        this.followLoading = true
        const res = await this.$u.api.getCommunityList({
          page: isRefresh ? 1 : this.followPage,
          limit: 5,
          list_type: 1, // 关注列表
        })

        if (res.error_code === 0) {
          const list = res.data.list || []

          if (isRefresh) {
            // 刷新时,先获取数据再重置
            this.followPage = 1
            this.followList = list
            this.followHasMore = true
          } else if (isLoadMore) {
            // 加载更多,追加数据
            this.followList = [...this.followList, ...list]
          } else {
            // 首次加载,直接赋值
            this.followList = list
          }

          // 判断是否还有更多数据
          this.followHasMore = list.length === 5
        } else {
          this.$u.toast(res.error_msg || '获取关注列表失败')
        }
      } catch (error) {
        console.error('获取关注列表失败:', error)
      } finally {
        this.followLoading = false
      }
    },

    // 获取酒评列表
    async getWineList(isLoadMore = false, isRefresh = false) {
      if (this.wineLoading || (!this.wineHasMore && !isRefresh)) return Promise.resolve()

      try {
        this.wineLoading = true
        const res = await this.$u.api.wineEvaluationList({
          page: isRefresh ? 1 : this.winePage,
          limit: 10,
          type: 2, // 所有酒评列表
        })

        if (res.error_code === 0) {
          let list = res.data.list || []

          // 酒评列表数据适配
          list = list.map((item) => ({
            ...item,
            type_data: item.type_data || '',
            content: item.wine_evaluation || item.content,
            diggnums: item.diggnums || item.likenums,
            viewnums: item.viewnums || 0,
            is_digg: item.is_digg || 0,
            video_url: '',
            is_attention: item.userinfo.is_follow ? 1 : 0,
            cover_img: '',
          }))

          if (isRefresh) {
            // 刷新时,先获取数据再重置
            this.winePage = 1
            this.wineList = list
            this.wineHasMore = true
          } else if (isLoadMore) {
            // 加载更多,追加数据
            this.wineList = [...this.wineList, ...list]
          } else {
            // 首次加载,直接赋值
            this.wineList = list
          }

          // 判断是否还有更多数据
          this.wineHasMore = list.length === 10
        } else {
          this.$u.toast(res.error_msg || '获取酒评列表失败')
        }
      } catch (error) {
        console.error('获取酒评列表失败:', error)
        this.$u.toast('获取酒评列表失败')
      } finally {
        this.wineLoading = false
      }
    },

    goToTopicDetail(topicId) {
      if (!topicId) {
        return
      }
      this.jump.appAndMiniJump(1, `/packageC/pages/topic-detail/topic-detail?id=${topicId}`, this.$vhFrom)
    },

    // 下拉刷新
    onPullDownRefresh() {
      switch (this.currentTabs) {
        case 0:
          // 推荐列表刷新
          Promise.all([
            this.getTopicList(), // 刷新话题列表
            this.getCommunityList(false, true), // 传入 isRefresh 参数
          ]).then(() => {
            uni.stopPullDownRefresh()
          })
          break
        case 1:
          // 酒评列表刷新
          this.getWineList(false, true).then(() => {
            uni.stopPullDownRefresh()
          })
          break
        case 2:
          // 关注列表刷新
          this.getFollowList(false, true).then(() => {
            uni.stopPullDownRefresh()
          })
          break
      }
    },
  },
  created() {
    // 添加页面配置
  },
  onPullDownRefresh() {
    return true
  },
  // 页面配置选项
  navigationBarTitleText: '社区',
  enablePullDownRefresh: true,
  backgroundTextStyle: 'dark',
}
</script>

<style scoped></style>
