<template>
  <view>
    <view
      class="bg-ffffff b-rad-20 mt-20 ml-24 mr-24 pt-28 pr-22 pb-28 pl-22 p-rela"
      v-for="(item, index) in localList"
      :key="index"
      @click="goToDetail(item, index)"
    >
      <view class="d-flex">
        <!-- 用户头像 -->
        <view class="p-rela w-88 h-88" @tap.stop="goToUserPost(item)">
          <image class="w-88 h-88 b-rad-p50" :src="getAvatarSrc(item)" mode="aspectFill"></image>
          <image
            v-if="item.userinfo && item.userinfo.certified_info"
            :src="ossIcon('/comm/certified_24_26.png')"
            class="p-abso bottom-n-02 right-0 w-24 h-26"
          />
        </view>

        <!-- 用户信息 -->
        <view class="ml-10 flex-sb-c w-p100">
          <view class="d-flex a-center">
            <text class="font-28 font-wei text-2d2d2d l-h-40">{{ getUserNickname(item) }}</text>
            <view
              v-if="getUserType(item) != 2"
              class="font-22 d-flex j-center a-center b-rad-26 text-ffffff ml-10 w-80 h-34 bg-ff9300"
              >LV.{{ getUserLevel(item) }}</view
            >
            <view v-else class="font-22 d-flex j-center a-center b-rad-10 text-ffffff ml-10 w-80 h-34 bg-e80404"
              >官方</view
            >
          </view>

          <!-- 右侧区域：关注按钮或更多操作按钮 -->
          <view class="d-flex a-center">
            <view class="reject-icon-post" v-if="myPostSelf && item.status === 2">未通过</view>
            <!-- 更多操作按钮 - 仅在myPostSelf为true时显示 -->
            <view v-if="myPostSelf" class="ml-20" @tap.stop="openPostOptions(item)">
              <u-icon name="more-dot-fill" color="#666" size="34"></u-icon>
            </view>
            <!-- 关注按钮 -->
            <view
              v-if="!isFollowList && item.userinfo && item.userinfo.uid != currentUid"
              :class="[item.is_attention ? 'text-3 b-s-02-d8d8d8' : 'text-2e7bff bg-e2ebfa']"
              class="w-100 mt-06 ptb-04-plr-00 font-24 text-center text-9 l-h-34 b-rad-26"
              @tap.stop="handleFollow(item, index)"
              >{{ item.is_attention ? '已关注' : '+关注' }}
            </view>
          </view>
        </view>
      </view>

      <!-- 话题标签 -->
      <view class="mt-24" v-if="item.topics && item.topics.length">
        <view class="topic-container">
          <text
            v-for="(topic, tIndex) in item.topics"
            :key="tIndex"
            class="bg-e2ebfa ptb-02-plr-22 b-rad-26 font-24 text-2e7bff l-h-34 mr-10 mb-10 topic-item"
            @tap.stop="goToTopicDetail(topic.id)"
            >#{{ topic.title }}#</text
          >
        </view>
      </view>

      <!-- 内容 -->
      <view class="font-24 text-3 l-h-40 o-hid text-hidden-4 d-flex a-center">
        <image
          v-if="item.is_best"
          class="w-44 h-30 mr-06"
          style="vertical-align: middle"
          src="https://images.vinehoo.com/vinehoomini/v3/comm/best-tag.png"
        ></image>
        <image
          class="w-44 h-30 mr-06"
          style="vertical-align: middle"
          v-if="item.is_top"
          src="https://images.vinehoo.com/vinehoomini/v3/comm/top-tag.png"
        ></image>
        <text>{{ item.content }}</text>
      </view>

      <!-- 图片内容 -->

      <!-- 视频内容 -->
      <view v-if="item.video_url" class="mt-20">
        <video :src="item.video_url" :poster="item.cover_img" class="w-p100"></video>
      </view>

      <view v-if="item.source === 6">
        <!-- 酒评 -->
        <view class="mt-10 mb-20 d-flex a-center" @tap.stop="goToWineDetail(index)">
          <image
            class="w-20 h-20 flex-shrink-0"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/wine_link.png"
          ></image>
          <view class="ml-10 wine-data-title">{{ item.wine_data.period_title }}</view>
        </view>
        <view class="bg-f7f7f7 w-p100 p-20 b-rad-06" v-if="item.wine_data.comment_type === 1">
          <!-- 只在有评分时显示评分部分 -->
          <view v-if="item.wine_data && item.wine_data.grade" class="d-flex a-center j-sb">
            <view class="d-flex a-center">
              <image
                class="w-34 h-34 mr-10"
                src="https://images.vinehoo.com/vinehoomini/v3/comm/wine-point.png"
              ></image>
              <view class="wine-data-bottom-title">评分</view>
            </view>
            <view class="d-flex a-center">
              <image
                v-for="star in getStarArray(item)"
                :key="star.index"
                class="w-26 h-26 mr-6"
                :src="
                  star.active
                    ? 'https://images.vinehoo.com/vinehoomini/v3/comm/point-active.png'
                    : 'https://images.vinehoo.com/vinehoomini/v3/comm/point.png'
                "
              ></image>
              <view class="ml-10 wine-value">{{ item.wine_data.grade }}分</view>
            </view>
          </view>
          <!-- 只在有品味数据时显示品味部分 -->
          <view
            v-if="item.wine_data && item.wine_data.taste && item.wine_data.taste.length"
            class="mt-10 d-flex a-center j-sb"
          >
            <view class="d-flex a-center">
              <image class="w-34 h-34 mr-10" src="https://images.vinehoo.com/vinehoomini/v3/comm/wine-type.png"></image>
              <view class="wine-data-bottom-title">品味</view>
            </view>
            <view class="wine-value text-ellipsis">{{ getFormatTaste(item) }}</view>
          </view>
        </view>
      </view>
      <view v-if="item.type_data" class="d-flex flex-wrap mt-20">
        <view
          v-for="(img, index) in getImageArray(item.type_data).slice(0, 3)"
          :key="index"
          class="image-wrapper p-rela"
          :class="[getImageArray(item.type_data).length === 1 ? 'w-330' : 'w-210 h-210']"
          :style="{ marginRight: index === 2 ? '0' : '14rpx' }"
          @tap.stop="previewImage(getImageArray(item.type_data), index)"
        >
          <image
            class="b-rad-10 w-p100 h-p100"
            :src="img"
            :mode="getImageArray(item.type_data).length === 1 ? 'widthFix' : 'aspectFill'"
          />

          <!-- 添加蒙层和剩余数量 -->
          <view
            v-if="index === 2 && getImageArray(item.type_data).length > 3"
            class="image-overlay d-flex j-center a-center"
          >
            <text class="remaining-count">+{{ getImageArray(item.type_data).length - 3 }}</text>
          </view>
        </view>
      </view>

      <!-- 底部数据显示 -->
      <view class="d-flex j-sa a-center mt-24">
        <view class="d-flex a-center">
          <image
            class="w-26 h-26"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/view.png"
            mode="aspectFill"
          ></image>
          <text class="ml-06 font-24 text-6">{{ formatViewNum(item.viewnums || 0) }}</text>
        </view>

        <view class="d-flex a-center">
          <image
            class="w-26 h-26"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/comm.png"
            mode="aspectFill"
          ></image>
          <text class="ml-06 font-24 text-6">{{ item.commentnums || 0 }}</text>
        </view>

        <view class="d-flex a-center" @tap.stop="handleLike(item)">
          <image
            class="w-26 h-26"
            :src="
              item.is_digg
                ? 'https://images.vinehoo.com/vinehoomini/v3/comm/zan_active.png'
                : 'https://images.vinehoo.com/vinehoomini/v3/comm/zan_none.png'
            "
            mode="aspectFill"
          ></image>
          <text class="ml-06 font-24" :class="item.is_digg ? 'text-e80404' : 'text-6'">{{ item.diggnums || 0 }}</text>
        </view>
      </view>

      <!-- 原有代码... -->
    </view>

    <u-popup
      v-model="showPostOpt"
      mode="bottom"
      :border-radius="20"
      :mask-custom-style="{ background: 'rgba(0, 0, 0, 0.3)' }"
    >
      <view class="pl-32 pr-32">
        <view
          class="bb-s-01-dedede pt-36 pb-36 text-center font-32 text-3"
          v-if="currentPost && currentPost.status === 2"
          @tap="viewReason"
          >查看原因</view
        >
        <view
          class="bb-s-01-dedede pt-36 pb-36 text-center font-32 text-3"
          v-if="currentPost && (currentPost.source === 6 || currentPost.status === 2)"
          @tap="handleEditPost"
          >重新编辑</view
        >
        <view
          class="bb-s-01-dedede pt-36 pb-36 text-center font-32 text-3"
          v-if="currentPost && !currentPost.source"
          @tap="handleDeletePost"
          >删除帖子</view
        >
        <view class="pt-36 pb-36 text-center font-32 text-9" @tap="showPostOpt = false">取消</view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    myPostSelf: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
    // 添加新的prop来标识是否为关注列表
    isFollowList: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      localList: [], // 添加本地数据
      currentUid: '', // 添加当前用户uid字段
      showPostOpt: false, // 控制底部弹窗显示
      currentPost: null, // 当前选中的帖子
    }
  },
  created() {
    // 在组件创建时获取storage中的uid
    const loginInfo = uni.getStorageSync('loginInfo')
    this.currentUid = loginInfo ? loginInfo.uid : ''
  },
  computed: {
    ...mapState(['routeTable', 'ossPrefix']), // 移除 userInfo
  },
  methods: {
    getImageArray(imageStr) {
      if (!imageStr) return []
      return imageStr.split(',').map((img) => (img.startsWith('http') ? img : this.ossPrefix + img))
    },
    async handleLike(item) {
      this.login.isLoginV3(this.$vhFrom, 1).then(async (isLogin) => {
        if (isLogin) {
          // 如果已经点赞,直接返回
          if (item.is_digg) {
            return
          }

          const params = {
            id: item.id,
            type: 0,
            action: 0, // 固定为点赞操作
          }
          try {
            let res
            if (item.source === 6) {
              res = await this.$u.api.wineEvaluationLike(params)
            } else {
              res = await this.$u.api.postsLike(params)
            }

            if (res.error_code === 0) {
              // 更新点赞状态和数量
              const index = this.localList.findIndex((post) => post.id === item.id)
              if (index !== -1) {
                const updatedList = [...this.localList]
                updatedList[index] = {
                  ...updatedList[index],
                  diggnums: item.diggnums + 1,
                  is_digg: true,
                }
                this.localList = updatedList
              }
            }
          } catch (e) {
            this.$toast('点赞失败')
          }
        }
      })
    },
    async handleFollow(item, index) {
      // 如果是取消注，先出确认框
      if (item.is_attention) {
        uni.showModal({
          title: '提示',
          content: '确定取消关注该用户吗？',
          success: async (res) => {
            if (res.confirm) {
              await this.doFollow(item, index)
            }
          },
        })
      } else {
        // 如果是关注操作，直接执行
        await this.doFollow(item, index)
      }
    },

    // 执行关注/取消关注的具体操作
    async doFollow(item, index) {
      this.login.isLoginV3(this.$vhFrom, 1).then(async (isLogin) => {
        if (isLogin) {
          try {
            const params = {
              operate_uid: item.userinfo.uid,
              status: item.is_attention ? 2 : 1,
            }

            const res = await this.$u.api.focusUser(params)

            if (res.error_code == 0) {
              this.$u.toast(item.is_attention ? '取消关注成功' : '关注成功')

              // 1. 获取新的关注状态
              const newAttentionStatus = !item.is_attention ? 1 : 0

              // 2. 更新当前列表中该用户的所有帖子的关注状态
              this.localList = this.localList.map((post) => {
                if (post.userinfo?.uid === item.userinfo.uid) {
                  return {
                    ...post,
                    is_attention: newAttentionStatus,
                  }
                }
                return post
              })

              // 3. 发出全局事件，通知其他列表更新
              uni.$emit('updateFollowStatus', {
                uid: item.userinfo.uid,
                status: newAttentionStatus,
              })

              // 4. 如果是在关注列表中取消关注，刷新关注列表
              if (this.isFollowList && !newAttentionStatus) {
                this.$parent.followPage = 1
                this.$parent.followList = []
                this.$parent.followHasMore = true
                this.$parent.getFollowList()
              }
            }
          } catch (error) {
            console.error('关注操作错误:', error)
            // this.$u.toast('操作失败')
          }
        }
      })
    },

    // 添加图片预览方法
    previewImage(urls, current) {
      // #ifdef APP-PLUS
      plus.nativeUI.previewImage(urls, {
        current: current,
        indicator: 'number',
        loop: true,
        onLongPress: function (e) {
          plus.nativeUI.actionSheet(
            {
              title: '操作',
              cancel: '取消',
              buttons: [
                {
                  title: '保存图片',
                },
                {
                  title: '发送给朋友',
                },
              ],
            },
            function (e) {
              switch (e.index) {
                case 1:
                  // 保存图片
                  break
                case 2:
                  // 发送给朋友
                  break
              }
            }
          )
        },
      })
      // #endif

      // #ifndef APP-PLUS
      uni.previewImage({
        urls: urls,
        current: current,
        loop: true,
        indicator: 'number',
        showCloseBtn: false,
        longPressActions: {
          itemList: ['发送给朋友', '保存图片', '收藏'],
          success: function (data) {
            console.log('选中了第' + (data.tapIndex + 1) + '个按钮')
          },
          fail: function (err) {
            console.log(err.errMsg)
          },
        },
      })
      // #endif
    },

    // 添加跳转方法
    goToWineDetail(index) {
      const period = this.localList[index].wine_data.period
      this.jump.appAndMiniJump(1, `/pages/goods-detail/goods-detail?id=${period}`, this.$vhFrom)
    },

    // 将星星数组的生成移到methods中
    getStarArray(item) {
      const grade = item?.wine_data?.grade || 0
      return Array.from({ length: 5 }, (_, index) => ({
        index,
        active: index < grade,
      }))
    },

    // 将品味数据的处理也移到methods中
    getFormatTaste(item) {
      const taste = item?.wine_data?.taste
      if (!taste || !Array.isArray(taste)) return ''

      return taste
        .map((item) => {
          const index = Math.floor(item.value / 25)
          return `${item.name}${item.rang[index]}`
        })
        .join('/')
    },

    // 在methods中添加
    goToTopicDetail(topicId) {
      if (!topicId) return
      this.jump.appAndMiniJump(1, `/packageC/pages/topic-detail/topic-detail?id=${topicId}`, this.$vhFrom)
    },

    // 获取头像src
    getAvatarSrc(item) {
      if (!item.userinfo || !item.userinfo.avatar_image) return ''
      return item.userinfo.avatar_image.startsWith('http')
        ? item.userinfo.avatar_image
        : this.ossPrefix + item.userinfo.avatar_image
    },

    // 获取用户昵称
    getUserNickname(item) {
      return item.userinfo ? item.userinfo.nickname || '未知用户' : '未知用户'
    },

    // 获取用户类型
    getUserType(item) {
      return item.userinfo ? item.userinfo.type : ''
    },

    // 获取用户等级
    getUserLevel(item) {
      return item.userinfo ? item.userinfo.user_level || 0 : 0
    },

    // 获取关注状态
    getIsAttention(item) {
      return item.is_attention
    },

    // 添加跳转到详情页的方法
    goToDetail(item, index) {
      // 跳转到详情页，传递id和source参数
      //   console.log(item.status)
      //   if (item.status === 2) {
      //     return
      //   }
      const id = this.localList[index].id
      const source = this.localList[index].source
      this.jump.appAndMiniJump(
        1,
        `/packageC/pages/wine-comment-detail/wine-comment-detail?id=${id}&source=${source}`,
        this.$vhFrom
      )
    },

    // 获取评论数量和点赞状态
    async getPostsOtherData(list) {
      if (!list || list.length === 0) return

      try {
        const params = {
          data: list.map((item) => ({
            id: item.id,
            source: item.source || (item.wine_data ? 6 : 2),
          })),
          start_index: 0,
          end_index: 0,
        }
        if (!params.data[0].id) {
          return
        }
        const res = await this.$u.api.getOtherData(params)
        if (res.error_code === 0 && res.data.list) {
          const updatedList = [...this.localList]

          res.data.list.forEach((newData) => {
            const index = updatedList.findIndex(
              (item) => item.id === newData.id && (item.source || (item.wine_data ? 6 : 2)) === newData.source
            )

            if (index !== -1) {
              updatedList[index] = {
                ...updatedList[index],
                commentnums: newData.commentnums,
                is_digg: newData.is_digg,
              }
            }
          })

          this.localList = updatedList
        }
      } catch (error) {
        console.error('获取评论数量和点赞状态失败:', error)
      }
    },

    // 添加跳转到用户主页的方法
    goToUserPost(item) {
      if (!item.userinfo || !item.userinfo.uid) {
        return
      } else {
        this.jump.appAndMiniJump(1, `/packageC/pages/my-post/my-post?uid=${item.userinfo.uid}`, this.$vhFrom, 0, true)
      }
    },
    formatViewNum(num) {
      if (num > 1000) {
        // 将数字除以1000并截取一位小数（不四舍五入）
        const k = Math.floor((num / 1000) * 10) / 10
        return k + 'k+'
      }
      return num
    },
    convertModel(model) {
      // 处理图片数据
      const imageArr = model.type_data ? model.type_data.split(',') : []
      const images = imageArr.map((url) => ({
        status: 'success',
        progress: 100,
        error: false,
        url: url,
        path: url,
        file: { path: url },
      }))

      // 处理话题ID
      const topics = model.topic_id ? model.topic_id.split(',') : []
      const topicArr = topics.map((topicid) => parseInt(topicid, 10))

      // 处理口感数据
      console.log(model.wine_data)
      const taste =
        model.wine_data?.taste?.map((item) => ({
          // 根据实际字段转换，示例保留关键字段
          value: item.value,
          name: item.name,
          // 添加其他需要转换的字段...
        })) || []

      // 组合最终对象
      return {
        id: model.id,
        orderNo: model.main_order_no,
        grade: model.wine_data?.grade || 0,
        wineColor: model.wine_data?.wine_color || '',
        wineColorImage: model.wine_data?.wine_color_image || '',
        fragrance: model.smell_aroma || '',
        content: model.content || '',
        images: images,
        topics: topicArr,
        taste: taste,
      }
    },

    // 使用示例
    // 打开帖子操作选项
    openPostOptions(post) {
      this.currentPost = post
      this.showPostOpt = true
    },

    // 处理编辑帖子
    handleEditPost() {
      if (!this.currentPost) return
      // 关闭底部弹窗
      this.showPostOpt = false
      if (this.currentPost.source === 6) {
        // 酒评
        this.jump.appAndMiniJump(
          1,
          `${this.routeTable.pCWineCommentSend}?editData=${encodeURIComponent(
            JSON.stringify(this.convertModel(this.currentPost))
          )}&fromDetail=true`,
          this.$vhFrom,
          0,
          true
        )
      } else {
        //帖子
        const postData = encodeURIComponent(JSON.stringify(this.currentPost))
        this.jump.appAndMiniJump(1, `${this.routeTable.pCSendPost}?postData=${postData}`, this.$vhFrom, 0, true)
      }
    },

    // 删除帖子
    async handleDeletePost() {
      if (!this.currentPost) return
      this.showPostOpt = false
      uni.showModal({
        title: '提示',
        content: '确定要删除这条帖子吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              const params = {
                id: this.currentPost.id,
                type: 3,
              }
              const res = await this.$u.api.deletePost(params)
              if (res.error_code === 0) {
                // 从列表中移除该帖子
                this.localList = this.localList.filter((item) => item.id !== this.currentPost.id)
                // 通知父组件更新
                this.$emit('refresh')
              }
            } catch (error) {}
          }
          this.showPostOpt = false
        },
      })
    },

    viewReason() {
      this.showPostOpt = false
      uni.showModal({
        title: '驳回原因',
        content: this.currentPost.reject_reason || '暂无驳回原因',
        showCancel: false,
        confirmText: '我知道了',
      })
    },
  },

  // 添加watch来监听list的变化
  watch: {
    list: {
      handler(newList) {
        if (newList && newList.length > 0) {
          this.localList = JSON.parse(JSON.stringify(newList))
          this.$nextTick(() => {
            this.getPostsOtherData(this.localList)
          })
        }
      },
      immediate: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.wine-data-title {
  font-weight: 400;
  font-size: 20rpx;
  color: #333333;
  line-height: 40rpx;
  /* 添加以下样式实现单行省略 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* 让元素占用剩余空间 */
  flex: 1;
}
.wine-data-bottom-title {
  font-weight: 400;
  font-size: 24rpx;
  color: #666666;
  line-height: 34rpx;
}
.wine-value {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  line-height: 34rpx;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.topic-tags-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.topic-tag {
  display: inline-block;
}

.topic-container {
  display: block;
  width: 100%;
  word-break: break-all;
  word-wrap: break-word;
}

.topic-item {
  display: inline-block;
  margin-bottom: 16rpx;
}

.image-wrapper {
  position: relative;
  overflow: hidden;
}
.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10rpx;
}

.remaining-count {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: bold;
}

/* 添加以下样式来隐藏关闭按钮 */
::v-deep .uni-popup .uni-popup__mask {
  background-color: rgba(0, 0, 0, 0.7);
}

::v-deep .uni-popup .uni-popup__wrapper {
  background-color: transparent;
}

::v-deep .uni-image-viewer__close {
  display: none !important;
}

::v-deep .uni-image-viewer__btn-close,
::v-deep .close-btn,
::v-deep .wx-image-viewer__close {
  display: none !important;
}

::v-deep .uni-preview-close {
  display: none !important;
}

::v-deep .uni-preview__close {
  display: none !important;
}
.reject-icon-post {
  position: absolute;
  font-size: 24rpx;
  right: 0rpx;
  background-color: #a7a4a4;
  padding: 6rpx 14rpx;
  color: #fff;
  border-radius: 0 10px 0 10px;
  top: 0;
}
</style>
