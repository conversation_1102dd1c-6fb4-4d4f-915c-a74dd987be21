<template>
  <view class="psj">
    <template v-if="isCancel">
      <span class="psj__text">支付取消</span>
      <button class="psj__btn" @click="cancel">{{ cancelPayBtnText }}</button>
    </template>
    <template v-else>
      <span class="psj__text">支付成功</span>
      <button class="psj__btn" @click="jumpOut">返回商家</button>
    </template>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { MAuctionEarnestType } from '@/common/js/utils/mapperModel'

export default {
  name: 'PaySuccessJump',
  data: () => ({
    vhType: -1,
    vhPartyId: '',
		vhPackageId: '',
		vhGid: '',
    status: '',
    pageFullPath: '',
    orderNo: '',
  }),
  computed: {
    ...mapState(['routeTable']),
    isCancel ({ status }) {
      return status === 'WAIT_BUYER_PAY'
    },
    cancelPayBtnText ({ vhType }) {
      switch (vhType) {
        case 0:
          return '返回订单列表'
        case 1:
          return '返回酒会'
        case 6:
          return '返回订金列表'
        default:
          return '返回酒云网'
      }
    }
  },
  methods: {
    addScriptTag (src) {
      const script = document.createElement('script')
      script.setAttribute('type', 'text/javascript')
      script.src = src
      script.onload = () => {
        const mchData = { action: 'onIframeReady', displayStyle: 'SHOW_CUSTOM_PAGE' }
        const postData = JSON.stringify(mchData)
        parent.postMessage(postData, 'https://payapp.weixin.qq.com')
      }
      document.body.appendChild(script)
    },
    jumpOut () {
      console.log('jumpOut')
      const { origin } = location
      let jumpOutUrl = origin
      switch (this.vhType) {
        case 0:
          jumpOutUrl = `${origin}${this.routeTable.pEMyOrder}`
          break
        case 1:
          jumpOutUrl = `${origin}${this.routeTable.pDWinePartyOrderList}`
          break
        case 3:
          if (this.pageFullPath) {
            jumpOutUrl = `${origin}${this.pageFullPath}${this.pageFullPath.includes('?') ? '&' : '?'}aePaySuccess=1`
          } else {
            jumpOutUrl = origin
          }
          break
        case 4:
          jumpOutUrl = `${origin}${this.pageFullPath}`
          break
        case 6:
          jumpOutUrl = `${origin}${this.routeTable.pBOrderDeposit}`
          break
      }
      const mchData = { action: 'jumpOut', jumpOutUrl }
      console.log('mchData', mchData)
      const postData = JSON.stringify(mchData)
      parent.postMessage(postData, 'https://payapp.weixin.qq.com')
    },
    cancel () {
      console.log('cancel')
      const { origin } = location
      let cancelUrl = origin
      switch (this.vhType) {
        case 0:
          cancelUrl = `${origin}${this.routeTable.pEMyOrder}?status=1`
          break
        case 1:
          cancelUrl = `${origin}${this.routeTable.pDWinePartyDetail}?id=${this.vhPartyId}&package_id=${this.vhPartyId}&gid=${this.vhGid}`
          break
        case 3:
          cancelUrl = `${origin}${this.pageFullPath}${this.pageFullPath.includes('?') ? '&' : '?'}aePayCancel=1`
          break
        case 4:
          cancelUrl = `${origin}${this.pageFullPath}`
          break
        case 6:
          cancelUrl = `${origin}${this.routeTable.pBOrderDeposit}`
      }
      location.href = cancelUrl
    },
    async loadAuctionEarnestOrderDetail () {
      const res = await this.$u.api.getAuctionEarnestSimpleOrderDetail({ main_order_no: this.orderNo })
      const { goods_id, type } = res?.data || {}
      switch (type) {
        case MAuctionEarnestType.Bidding:
          this.pageFullPath = goods_id ? `${this.routeTable.pHAuctionGoodsDetail}?id=${goods_id}` : ''
          break
        case MAuctionEarnestType.Entrust:
          this.pageFullPath = this.routeTable.pHAuctionEntrustEarnestPaySuccess
          break
        default:
          this.pageFullPath = ''
          break
      }
    },
  },
  onLoad (options) {
    this.vhType = +options.vhType
    this.vhPartyId = options.vhPartyId
    this.vhPackageId = options.vhPackageId
    this.vhGid = options.vhGid
    this.status = options.status
    this.pageFullPath = options.pageFullPath
    const out_trade_no = options.out_trade_no
    this.orderNo = out_trade_no
    if (this.orderNo) {
      if (this.orderNo.includes('VHA')) {
        this.vhType = 4
        this.pageFullPath = `${this.routeTable.pHAuctionBuyerOrderDetail}?orderNo=${this.orderNo}`
      } else if (this.orderNo.includes('VHE')) {
        this.vhType = 3
        this.loadAuctionEarnestOrderDetail()
      } else if (this.orderNo.includes('VHM')) {
        this.vhType = 0
      }
    }
  },
  onShow () {
    this.addScriptTag('https://wx.gtimg.com/pay_h5/goldplan/js/jgoldplan-1.0.0.js')
  }
}
</script>

<style lang="scss" scoped>
  .psj {
    @include flex-col;
    height: 100vh;

    &__text {
      font-size: 48rpx;
    }

    &__btn {
      margin-top: 10rpx;
      font-size: 28rpx;
      color: #409eff;
      background: none;
    }
  }
</style>
