<template>
  <view>
    <vh-navbar height="46" :is-back="false">
      <view class="p-rela pl-24 pr-24 w-p100" :class="isShowStoreTabs ? 'flex-e-c' : 'flex-sb-c'">
        <view
          v-if="isShowStoreTabs"
          class="p-abso left-p-50 t-trans-x-m50 flex-c-c p-02 h-46 b-rad-28"
          style="background: rgba(0, 0, 0, 0.6)"
        >
          <view class="flex-c-c w-122 h-p100 font-wei-500 font-24 text-3 bg-ffffff b-rad-28">秒发</view>
          <view class="flex-c-c w-114 h-p100 font-wei-500 font-24 text-ffffff bg-transp" @click="onJumpStore"
            >去门店</view
          >
        </view>
        <view v-else class="flex-1">
          <view
            class="flex-sb-c h-60 bg-ffffff b-s-02-e80404 b-rad-30"
            @click="jump.navigateTo(`${routeTable.pFGlobalSearch}?type=2`)"
          >
            <text class="ml-24 font-24 text-9">{{ channelKeyword }}</text>
            <button class="vh-btn flex-c-c w-96 h-60 font-wei-500 font-24 text-ffffff bg-e80404 b-rad-30">搜索</button>
          </view>
        </view>
        <view class="flex-c-c ml-32">
          <view v-if="isShowStoreTabs" class="flex-c-c mr-26 wh-36">
            <view class="d-flex">
              <image
                class="wh-36 p-10"
                :src="ossIcon(`/second_hair/s_sea_36.png`)"
                @click="jump.navigateTo(`${routeTable.pFGlobalSearch}?type=2`)"
              />
            </view>
          </view>
          <view class="flex-c-c mr-26 wh-36">
            <view class="p-rela d-flex" @click="jump.loginNavigateTo(`${routeTable.pBShoppingCart}`)">
              <image class="wh-36 p-10" :src="ossIcon(`/second_hair/s_car_36.png`)" />
              <view
                v-if="shoppingCartNum"
                class="p-abso top-n-02 right-n-02 w-26 h-26 bg-e80404 b-rad-p50 flex-c-c font-16 text-ffffff"
                >{{ shoppingCartNum }}</view
              >
            </view>
          </view>
          <view class="flex-c-c wh-36" @click="jump.loginNavigateTo(`${routeTable.pEMessageCenter}`)">
            <view class="p-rela d-flex">
              <image class="w-36 h-38 p-10" :src="ossIcon(`/second_hair/s_not_36_38.png`)" />
              <view v-if="unReadTotalNum" class="p-abso top-10 right-10 wh-10 bg-e80404 b-rad-p50" />
            </view>
          </view>
        </view>
      </view>
    </vh-navbar>
    <view v-if="goldAreaList.length" class="flex-sb-c ptb-20-plr-24 bg-ffffff">
      <view
        class="flex-c-c-c w-min-96"
        v-for="(item, index) in goldAreaList"
        :key="index"
        @click="jumpSecondHairSecond(item)"
      >
        <view class="p-rela w-72 h-72">
          <vh-image :loading-type="2" :src="item.image" :width="72" :height="72" :border-radius="20" />
          <image
            :src="ossIcon('/second_hair/shadow_84_30.png')"
            class="p-abso bottom-n-16 left-p-50 t-trans-x-m50 w-84 h-30"
          ></image>
        </view>
        <view class="mt-10 font-22 text-6 l-h-32">{{ item.second_name }}</view>
      </view>
    </view>
    <!-- <NewPeopleGift 
				v-if="newPeopleAreaVisible" 
				:list="newPeopleGoodsList" 
				:info="newPeopleCouponPackage" 
				@jumpActivity="onJumpActivityPage"
				@countDownEnd="newPeopleCouponPackage.remainder = 0"
			/> -->
    <view v-if="filterList.length" style="position: sticky; top: 46px; z-index: 9" class="ptb-20-plr-24 bg-f9f9f9">
      <scroll-view scroll-x :scroll-left="scrollLeft" @scroll="onScroll">
        <view class="d-flex">
          <view
            v-for="(item, index) in filterList"
            :key="index"
            class="flex-c-c flex-shrink mr-20 ptb-00-plr-20 w-min-88 h-46 font-24 b-rad-23"
            :class="[query.identifier === item.identifier ? 'text-ffffff bg-e80404' : 'text-3 bg-ececec']"
            @click="onFilter(item, index)"
            >{{ item.title }}</view
          >
        </view>
      </scroll-view>
    </view>
    <vh-empty
      v-if="isEmpty"
      bgColor="transparent"
      :padding-top="100"
      :padding-bottom="100"
      :image-src="ossIcon('/empty/emp_goods.png')"
      text="暂无数据"
      :text-bottom="0"
    />
    <view v-else class="ptb-00-plr-24">
      <VhWaterfall
        ref="vhWaterfallRef"
        v-model="list"
        :idKey="$wfitemIdKey"
        :itemStyle="wfitemStyle"
        @changePendInsertId="onChangePendInsertId"
        @renderStart="renderLoading = true"
        @renderEnd="renderLoading = false"
      />
      <u-loadmore
        :status="initLoading || renderLoading ? 'loading' : reachBottomLoadStatus"
        @loadmore="reachBottomLoad"
      />
    </view>
    <vh-tabbar :loading="initLoading" @topRefresh="topRefresh" @recordTabbarPages="recordPages" />
    <SecStoreRangeMask
      v-if="!newPeopleIndexMaskVisible && storeInfo"
      v-model="secStoreRangeMaskVisible"
      :info="storeInfo"
      @cancel="secStoreRangeMaskVisible = false"
    />
    <NewPeopleFloatingFrame
      v-if="newPeopleFloatingFrameVisible"
      :isLogin="userIsLogin"
      :received="newPeopleCouponPackage.collect_status"
      @jumpActivity="onJumpActivityPage"
    />
    <!-- <NewPeopleIndexMask
      v-if="newPeopleIndexMaskVisible"
      :show="showNewPeopleIndexMask"
      :received="newPeopleCouponPackage.collect_status"
      @close="closeNewPeopleIndexMask()"
      @receive="getNewPeopleReceiveBenefits"
      @jumpActivity="onJumpActivityPage"
    /> -->

    <StartupPageOptionsPopup v-if="miaofaStartupPageCount === 2 && userIsLogin" :type="0" />
  </view>
</template>

<script>
import navMsgMixin from '@/common/js/mixins/navMsgMixin.js'
import listMixin from '@/common/js/mixins/listMixin'
import newPeopleMixin from '@/common/js/mixins/newPeopleMixin'
import userMixin from '@/common/js/mixins/userMixin'
import topRefreshMixin from '@/common/js/mixins/topRefreshMixin'
import startupPageOptionsMixin from '@/common/js/mixins/startupPageOptionsMixin'
import { mapState, mapMutations, mapActions } from 'vuex'
import { MSecondsWfitemType, MSWfitemTypeToIncParamsKey, MSWfitemIncRes } from '@/common/js/utils/mapperModel'
import { STORE_URL } from '@/common/js/fun/constant'

const screenWidth = uni.getSystemInfoSync()?.screenWidth || 0
const $isBigScreen = screenWidth >= 390
console.log('$isBigScreen', screenWidth, $isBigScreen)
const WfitemWidth = 344
const WfitemMarginRight = `${screenWidth - uni.upx2px(WfitemWidth) * 2 - uni.upx2px(24) * 2}px`
console.log('WfitemMarginRight', WfitemMarginRight)
const WfitemCustom = {
  $wfitemIdKey: '$wfitemId',
  $isRecommended: false,
  $wfitemWidth: WfitemWidth,
  $imgIsResize: true,
  $isBigScreen,
  $reportDisabled: true,
}

export default {
  mixins: [navMsgMixin, listMixin, newPeopleMixin, userMixin, topRefreshMixin, startupPageOptionsMixin],
  data: () => ({
    query: {
      uuid: uni.getStorageSync('uniqueId') || '',
      identifier: '',
    },
    isListPattern: true,
    initLoading: true,
    renderLoading: true,
    wfitemStyle: { width: WfitemWidth, marginRight: WfitemMarginRight, marginBottom: 16 },
    isShowStoreTabs: false,
    storeInfo: null,
    goldAreaList: [],
    filterList: [],
    secStoreRangeMaskVisible: false,
    pendInsertId: '',
    isEmpty: false,
    scrollLeft: 0,
    oldScrollLeft: 0,
  }),
  computed: {
    ...mapState(['routeTable']),
    ...mapState('startupPageOptions', ['miaofaStartupPageCount']),
    $wfitemIdKey() {
      return WfitemCustom.$wfitemIdKey
    },
  },
  methods: {
    ...mapActions('newcomerCoupon', ['initNewcomerCouponInfo']),
    async getFilterList() {
      try {
        this.feedback.loading()
        let res = await this.$u.api.flashFilterList()
        const list = res.data.list
        const typeToList = list.reduce(
          (prev, curr) => ({
            ...prev,
            [curr.type]: [...(prev[curr.type] || []), curr],
          }),
          {}
        )
        const { 1: countryList = [], 2: wineTypeList = [], 3: keywordList = [], 5: regionsList = [] } = typeToList
        this.countryList = countryList
        this.wineTypeList = wineTypeList
        this.keywordList = keywordList
        this.regionsList = regionsList
        this.showScreenPop = true
      } catch (err) {
        this.feedback.hideLoading()
      }
    },
    init(query = this.query) {
      this.initLoading = true
      Promise.all([this.loadCombine(), this.load(query)])
        .then((res) => {
          const { goldAreaList, filterList, interestedWfitem, adSwiperWfitem } = res[0]
          const { list = [] } = res[1]?.data || {}
          const targetList = list.filter((item) => !item.$isHandInsert)
          this.goldAreaList = goldAreaList
          this.filterList = filterList
          if (!query.identifier && interestedWfitem && targetList.length >= 10) {
            targetList.splice(Math.floor(Math.random() * targetList.length), 0, interestedWfitem)
          }
          if (!query.identifier && adSwiperWfitem) {
            targetList.unshift(adSwiperWfitem)
          }
          this.list = []
          this.$nextTick(() => {
            this.list = targetList
          })
        })
        .finally(() => {
          this.initLoading = false
          // this.scrollLeft = this.oldScrollLeft
          // this.$nextTick(() => {
          // 	this.scrollLeft = 0
          // })
        })
    },
    async loadCombine() {
      let goldAreaList = this.goldAreaList
      let filterList = this.filterList
      let interestedWfitem = this.list.find((item) => item.type === MSecondsWfitemType.Interested && item.$isHandInsert)
      let adSwiperWfitem = this.list.find((item) => item.type === MSecondsWfitemType.AdSwiper && item.$isHandInsert)
      try {
        const res = await this.$u.api.getSecondCombine({ client: this.$client })
        const { home_screen = [], second_filters_list = [], collect_tags = {}, banner = [] } = res?.data || {}
        goldAreaList = home_screen.slice(0, 5)
        filterList = [{ identifier: '', title: '推荐' }, ...second_filters_list]
        if (collect_tags?.labels?.length >= 3) {
          interestedWfitem = {
            ...collect_tags,
            [this.$wfitemIdKey]: Date.now() + 1,
            type: MSecondsWfitemType.Interested,
            ...WfitemCustom,
            $isHandInsert: true,
          }
        } else {
          interestedWfitem = null
        }
        if (banner?.length) {
          banner.forEach((item) => {
            item.image = item.vertical_image
          })
          adSwiperWfitem = {
            [this.$wfitemIdKey]: Date.now() + 2,
            type: MSecondsWfitemType.AdSwiper,
            list: banner,
            ...WfitemCustom,
            $isHandInsert: true,
          }
        } else {
          adSwiperWfitem = null
        }
        return { goldAreaList, filterList, interestedWfitem, adSwiperWfitem }
      } catch (e) {
        return { goldAreaList, filterList, interestedWfitem, adSwiperWfitem }
      }
    },
    async load(query) {
      this.isEmpty = false
      query.limit = 10
      const { page, limit, uuid, lonlat = '', identifier } = query
      const isFirstPage = page === 1
      const listCount = isFirstPage ? 0 : this.list.length
      let lastInvestigatesCount = 0
      if (isFirstPage) {
        lastInvestigatesCount = 0
      } else {
        const findLastInvestigatesIndex = this.list.findLastIndex(
          (item) => item.type === MSecondsWfitemType.Investigates
        )
        if (findLastInvestigatesIndex !== -1) {
          lastInvestigatesCount = findLastInvestigatesIndex + 1
        }
      }
      let res
      if (identifier) {
        res = await this.$u.api.filterSecondDataList({
          page,
          limit,
          tag: identifier,
          uuid,
        })
      } else {
        res = await this.$u.api.getSecondRecommendList({
          page,
          uuid,
          count: listCount,
          last_questionnaire_count: lastInvestigatesCount,
        })
      }
      res.data.list = res?.data?.list?.map((item) => ({
        [this.$wfitemIdKey]: `${item.genre}-${item[item.genre].id}-${page}`,
        type: item.genre,
        ...item[item.genre],
        ...WfitemCustom,
      }))
      const { list = [] } = res?.data || {}
      await this.loadIncrementData(list, query)
      if (query.page !== 1) {
        this.list = this.list.concat(list)
      }
      this.isEmpty = query.page === 1 && !list.length
      // res.$pendUpdateObj = { isListPattern: !identifier }
      return res
    },
    async loadIncrementData(list = [], query) {
      if (!list.length) return
      const group = list.reduce((prev, curr) => {
        if (prev[curr.type]) {
          prev[curr.type].push(curr)
          return prev
        } else {
          prev[curr.type] = [curr]
          return prev
        }
      }, {})
      const params = {}
      Object.keys(group).forEach((groupKey) => {
        const key = MSWfitemTypeToIncParamsKey[groupKey]
        if (key) params[key] = group[groupKey].map(({ id }) => id).join()
      })
      params.periods_params =
        group[MSecondsWfitemType.Goods]?.map(({ id, periods_type, price }) => ({
          period: id,
          period_type: periods_type,
          price: +price,
        })) || []
      const incrementRes = await this.$u.api.getSecondRecommendIncrement(params)
      const incrementData = incrementRes.data
      Object.keys(incrementData).forEach((key) => {
        const incrementDataList = incrementData[key]
        const obj = MSWfitemIncRes[key]
        if (incrementDataList && obj) {
          incrementDataList.forEach((data) => {
            const findItem = list.find(
              (item) => item[this.$wfitemIdKey] === `${obj.type}-${data[obj.id]}-${query.page}`
            )
            if (findItem) {
              const target = {}
              Object.entries(obj)
                .filter((arr) => !['type', 'id'].includes(arr[0]))
                .forEach((arr) => {
                  target[arr[1]] = data[arr[0]]
                })
              Object.assign(findItem, target)
            }
          })
        }
      })
      const goodsDataList = incrementData.goods_data
      if (goodsDataList.length) {
        group[MSecondsWfitemType.Goods]?.forEach((goods) => {
          const findItem = goodsDataList.find((item) => item.period === goods.id)
          if (findItem) {
            const {
              label: { left_top_label = [], product_label = [], top_label = [] },
              price,
            } = findItem
            Object.assign(goods, { left_top_label, product_label, top_label, price })
          }
        })
      }
    },
    pullDownRefresh() {
      //   this.getNewPeopleCouponActivityInfo()
      this.init({ ...this.query, page: 1 })
    },
    getStoreInfo() {
      // this.$u.api.storeDeliveryRange({ lng: '106.55843', lat: ' 29.56900 ' }).then(res => {
      //   const { nearest_store, is_store_range } = res?.data
      //   this.storeInfo = nearest_store
      //   this.secStoreRangeMaskVisible = is_store_range
      //   uni.getStorage({
      //  	key: 'storeMaskVisibleCountDown',
      //  	success: res => {
      // 		// (24 * 60 * 60 * 1000 )
      //  		const { data: storageTimeStamp } = res
      // 		const sevenDayTimeStamp = storageTimeStamp + 30 * 1000
      //  		const currentTimeStamp = Date.now()
      //  		if( sevenDayTimeStamp > currentTimeStamp ) this.secStoreRangeMaskVisible = false
      //  		console.log(sevenDayTimeStamp)
      //  		console.log(currentTimeStamp)
      //  	},
      // 	fail: () => {
      // 		console.log('------------------------没有storeMaskVisibleCountDown' )
      // 	}
      //  })
      // })
      // return
      uni.getLocation({
        type: 'gcj02',
        success: async (data) => {
          const { latitude, longitude } = data
          if (latitude && longitude) {
            console.log('location latitude longitude', latitude, longitude)
            const res = await this.$u.api.storeDeliveryRange({ lng: longitude, lat: latitude })
            const { nearest_store, is_store_range } = res?.data || {}
            this.storeInfo = nearest_store
            this.secStoreRangeMaskVisible = is_store_range
            this.isShowStoreTabs = is_store_range
            uni.getStorage({
              key: 'storeMaskVisibleCountDown',
              success: (res) => {
                // (24 * 60 * 60 * 1000 )
                const { data: storageTimeStamp } = res
                const sevenDayTimeStamp = storageTimeStamp + 7 * 24 * 60 * 60 * 1000
                const currentTimeStamp = Date.now()
                if (sevenDayTimeStamp > currentTimeStamp) this.secStoreRangeMaskVisible = false
                console.log(sevenDayTimeStamp)
                console.log(currentTimeStamp)
              },
              fail: () => {
                uni.getStorage({
                  key: 'storeMaskTomorrowDate',
                  success: (res) => {
                    const { data: storageDate } = res
                    // yyyy-mm-dd hh:MM:ss
                    const currentDate = this.$u.timeFormat(Date.now(), 'yyyy-mm-dd')
                    if (storageDate == currentDate) this.secStoreRangeMaskVisible = false
                    console.log(storageDate)
                    console.log(currentDate)
                  },
                })
              },
            })
          }

          // const { list = [] } = res?.data
          // if (list.length) {
          // 	list.forEach(item => {
          // 		item.storeDistance = this.gps.lonAndLatDis(latitude, longitude, item.latitude, item.longitude)
          // 	})
          // 	list.sort((itemA, itemB) => itemA.storeDistance - itemB.storeDistance)
          // 	console.log('store list', list)
          // 	const [storeInfo] = list
          // 	const { storeDistance, radius } = storeInfo
          // 	if (storeDistance <= radius + 100) {
          // 		this.storeInfo = storeInfo
          // 		this.secStoreRangeMaskVisible = true
          // 		uni.getStorage({
          // 			key: 'storeMaskVisibleCountDown',
          // 			success: res => {
          // 				// (24 * 60 * 60 * 1000 )
          // 				const { data: storageTimeStamp } = res
          // 				const sevenDayTimeStamp = storageTimeStamp + 30 * 1000
          // 				const currentTimeStamp = Date.now()
          // 				if( sevenDayTimeStamp > currentTimeStamp ) this.secStoreRangeMaskVisible = false
          // 				console.log(sevenDayTimeStamp)
          // 				console.log(currentTimeStamp)
          // 			}
          // 		})
          // 	}
          // }
        },
      })
    },
    jumpSecondHairSecond(item) {
      // this.$u.api.reportBuryDot({
      // 	data: [{
      // 		channel: 4,
      // 		genre: 3,
      // 		region_id: 302000,
      // 		button_id: item.id
      // 	}]
      // })
      this.jump.navigateTo(`${this.routeTable.pBSecondHairSecond}?id=${item.id}`)
    },
    onChangePendInsertId(id) {
      this.pendInsertId = id
    },
    async insertWfitem() {
      if (!this.pendInsertId) return
      const size = 4
      const isLogin = await this.login.isLoginV3(this.$vhFrom, 0)
      let isNewUser = 1
      //   if (isLogin) {
      //     const {
      //       data: { is_new_user = 0 },
      //     } = await this.$u.api.userSpecifiedData({ field: 'is_new_user' })
      //     isNewUser = +is_new_user
      //   }
      const res = await this.$u.api.getSecondGoodsRecommendList({
        period_id: this.pendInsertId.id,
        is_newcomer: isNewUser,
      })
      const { list = [] } = res?.data || {}
      if (list?.length >= size) {
        const insertItem = {
          [this.$wfitemIdKey]: Date.now(),
          type: MSecondsWfitemType.GuessLike,
          list: list.slice(0, size),
          ...WfitemCustom,
        }
        this?.$refs?.vhWaterfallRef?.insert(this.pendInsertId[this.$wfitemIdKey], insertItem)
      }
      this.pendInsertId = ''
    },
    onJumpStore() {
      window.open(STORE_URL)
    },
    onFilter(item) {
      if (!item.identifier) {
        this.init({ ...this.query, page: 1, identifier: '' })
      } else {
        this.reachBottomLoadStatus = 'loading'
        this.load({ ...this.query, page: 1, identifier: item.identifier }).then((res) => {
          const { list = [] } = res?.data || {}
          this.list = []
          this.$nextTick(() => {
            this.list = list
          })
        })
      }
    },
    onScroll(e) {
      this.oldScrollLeft = e.detail.scrollLeft
    },
  },
  onLoad() {
    this.init()
    // this.getStoreInfo()
  },
  onShow() {
    const flag = this?.$refs?.vhWaterfallRef?.startRender()
    if (flag) this.insertWfitem()
    this.login.isLoginV3(this.$vhFrom, 0).then((isLogin) => {
      this.userIsLogin = isLogin
      this.initNewcomerCouponInfo()
    })
  },
  onHide() {
    this?.$refs?.vhWaterfallRef?.stopRender()
  },
  onUnload() {
    this?.$refs?.vhWaterfallRef?.stopRender()
  },
  onPullDownRefresh() {
    if (this.initLoading) return
    this.pullDownRefresh()
  },
  onReachBottom() {
    this.reachBottomLoad()
  },
}
</script>

<style>
page {
  font-family: 'PingFang SC', OpenSans, apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial,
    Roboto, 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  background: #f9f9f9;
}
</style>
