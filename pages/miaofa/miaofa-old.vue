<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 数据加载完成 -->
		<view v-if="!loading" id="outer-content">
			<!-- 开启定位 -->
			<view v-if="openPosition" class="">
				<!-- 秒发板块 -->
				<view v-if="!isStore" class="fade-in miaofa">
					<!-- 导航栏 -->
					<vh-navbar v-if="!isFromAuction" height="46" :is-back="false" :show-border="true">
						<view class="d-flex j-sb a-center w-p100">
							<view class="d-flex a-center flex-1">
								<view v-if="mSid" class="d-flex a-center ml-10" @click="openStore()">
									<u-icon name="nav-back" color="#333" :size="44" />
									<view class="ml-24 font-32 font-wei text-3 w-s-now">门店</view>
								</view>
								<view class="h-68 bg-f7f7f7 b-rad-40 d-flex a-center ml-24" :class="mSid ? 'w-240' : 'w-p100'" @click="jump.navigateTo(`${routeTable.pFGlobalSearch}?type=2`)">
									<image class="w-36 h-36 ml-34" src="https://images.vinehoo.com/vinehoomini/v3/comm/ser_gray.png" mode="aspectFill" />
									<text class="ml-08 font-28 text-9">{{channelKeyword}}</text>
								</view>
							</view>
							<!-- <view class="d-flex a-center">
								<view class="" @click="jump.loginNavigateTo(`/packageB/pages/shopping-cart/shopping-cart`)">
									<image class="mr-24 w-44 h-44" :src="`${osip}/second_hair/black_cart.png`" mode="widthFix" />
								</view>
								<view class="p-rela" @click="jump.loginNavigateTo(`/packageE/pages/message-center/message-center`)">
									<image class="mr-24 w-44 h-44" :src="`${osip}/second_hair/black_not.png`" mode="widthFix" />
									<view  v-if="unReadTotalNum" class="p-abso right-15 top-n-06 w-26 h-26 bg-e80404 b-rad-p50 d-flex j-center a-center font-18 text-ffffff">{{unReadTotalNum > 99 ? '99' : unReadTotalNum}}</view>
								</view>
							</view> -->
							
							<view class="d-flex a-center mtb-00-mlr-04">
								<view class="p-rela d-flex a-center" @click="jump.loginNavigateTo(`${routeTable.pBShoppingCart}`)">
									<image class="w-44 h-44 p-20" :src="`${osip}/flash_purchase/black_cart.png`" />
								    <view v-if="shoppingCartNum" class="p-abso left-46 top-16 w-26 h-26 bg-e80404 b-rad-p50 d-flex j-center a-center font-18 text-ffffff">{{shoppingCartNum > 99 ? '99' : shoppingCartNum}}</view>
								</view>
								<view class="p-rela d-flex" @click="jump.loginNavigateTo(`${routeTable.pEMessageCenter}`)">
									<image class="w-44 h-44 p-20" :src="`${osip}/flash_purchase/black_not.png`" />
									<view  v-if="unReadTotalNum" class="p-abso left-46 top-16 w-26 h-26 bg-e80404 b-rad-p50 d-flex j-center a-center font-18 text-ffffff">{{unReadTotalNum > 99 ? '99' : unReadTotalNum}}</view>
								</view>
							</view>
						</view>
					</vh-navbar>
				
					<!-- 轮播图 + 金刚区 -->
					<view class="p-rela bg-ffffff pl-24 pr-24">
						<!-- 轮播图 -->
						<view v-if="swiperList.length" class="pt-20">
							<vh-swiper :loading-type="2" :list="swiperList" :height="344" @click="jump.pubConfJumpBD($event, 4, 3, 301000, $event.id, $vhFrom)"/>
						</view>
						
						<!-- 金刚区 -->
						<view v-if="goldAreaList.length" class="d-flex j-sb a-center ptb-32-plr-00">
							<view class="d-flex flex-column j-center a-center" v-for="(item, index) in goldAreaList" :key="index" @click="jumpSecondHairSecond(item)">
								<vh-image :loading-type="2" :src="item.image" :width="86" :height="86" :border-radius="20"/>
								<view class="mt-10 font-24 text-0">{{item.second_name}}</view>
							</view>
						</view>
					</view>
					
					<!-- 卡片（爆款好物更优惠）+ banner -->
					<MiaofaCard :list="cardList" @openPackPop="openPackPop" />
					
					<!-- 筛选 -->
					<view id="filter-tabs" class="filter z-978 d-flex j-sb a-center ptb-00-plr-24 h-104" :class="[!isFixedFilter ? 'bg-f7f7f7' : 'bg-ffffff', { 'p-stic': true }]" :style="{ top: filterTop + 'px'}">
						<view class="font-28 text-6" :class="filterIndex == 0 ? 'text-e80404 font-wei' : ''" @click="changeSort(0)">默认</view>
						<view class="d-flex a-center" v-for="(item, index) in sortList" :key="index" @click="changeSort(item.index)">
							<text class="font-28 text-6" :class="filterIndex == item.index ? 'text-e80404 font-wei' : ''">{{item.name}}</text>
							<image class="ml-04 w-14 h-14" v-if="sortType == item.type" :src="`${osip}/second_hair/sort_${sort}.png`"  mode="aspectFill" />
							<image class="ml-04 w-14 h-14" v-else :src="`${osip}/second_hair/sort.png`" mode="aspectFill" />
						</view>
						<view class="d-flex a-center w-28 h-28">
							<image class="flex-shrink p-20 w-28 h-28" :src="`${osip}/second_hair/change_${showWaterfallFlow ? 'layout' : 'list'}.png`" mode="aspectFill" @click="onListLayoutChange" />
						</view>
						<view class="d-flex a-center pl-30 bl-s-01-cccccc font-28 text-6" @click="getFilterList">
							<text :class="canFilter && doFilter ? 'text-e80404 font-wei' : ''">筛选</text>
							<image class="w-28 h-28 ml-10" :src="`${osip}/comm/funnel${canFilter && doFilter ? '_sel' : ''}.png`" mode="aspectFill" />
						</view>
					</view>
					
					<!-- 商品列表（有数据） -->
					<view id="goods-list-con" class="pb-20 pl-24 pr-24" :style="{ minHeight: `${goodsListMinHeight}px` }">
						<!-- 商品列表（瀑布流） -->
						<view v-if="showWaterfallFlow" class="">
							<u-waterfall v-model="goodsList" ref="uWaterfall" :add-time="4">
								<template v-slot:left="{ leftList }">
									<view class="p-rela bg-ffffff w-346 b-rad-10 o-hid t-trans-3d-1 mb-10" v-for="(item, index) in leftList" :key="index">
										<!-- 广告位（酒会） -->
										<vh-waterfall-wine-party-ad :from="$vhFrom" v-if="item.modul == 1" :item="item" :buryDotParams="buryDotParams" />
										<!-- 广告位（直播） -->
										<vh-waterfall-live-ad v-else-if="item.modul == 2" :from="$vhFrom" :item="item" :buryDotParams="buryDotParams" />
										<!-- 广告位（专题活动） -->
										<vh-waterfall-thematic-activities-ad v-else-if="item.modul == 3" :from="$vhFrom" :item="item" :buryDotParams="buryDotParams" />
										<!-- 广告位（专题产品） -->
										<vh-waterfall-special-products-ad v-else-if="item.modul == 4" :from="$vhFrom" :item="item" :buryDotParams="buryDotParams" />
										<!-- 常规商品 -->
										<vh-waterfall-goods-list v-else :from="$vhFrom" :item="item"/>
									</view>
								</template>
								<template v-slot:right="{ rightList }">
									<view class="p-rela bg-ffffff w-346 b-rad-10 o-hid t-trans-3d-1 mb-10 ml-10" v-for="(item, index) in rightList" :key="index">
										<!-- 广告位（酒会） -->
										<vh-waterfall-wine-party-ad v-if="item.modul == 1" :from="$vhFrom" :item="item" :buryDotParams="buryDotParams" />
										<!-- 广告位（直播） -->
										<vh-waterfall-live-ad v-else-if="item.modul == 2" :from="$vhFrom" :item="item" :buryDotParams="buryDotParams" />					
										<!-- 广告位（专题活动） -->
										<vh-waterfall-thematic-activities-ad v-else-if="item.modul == 3" :from="$vhFrom" :item="item" :buryDotParams="buryDotParams" />
										<!-- 广告位（专题产品） -->
										<vh-waterfall-special-products-ad v-else-if="item.modul == 4" :from="$vhFrom" :item="item" :buryDotParams="buryDotParams" />
										<!-- 常规商品 -->
										<vh-waterfall-goods-list v-else :from="$vhFrom" :item="item"/>
									</view>
								</template>
							</u-waterfall>
							<u-loadmore :status="loadStatus" />
						</view>
						
						<!-- 商品列表（常规） -->
						<view v-if="!showWaterfallFlow" class="">
							<view class="bg-ffffff b-rad-10 mb-20 o-hid t-trans-3d-1" v-for="(item,index) in goodsList" :key="index">
								<!-- 广告位（酒会） -->
								<vh-normal-wine-party-ad v-if="item.modul == 1" :from="$vhFrom" :item="item" :buryDotParams="buryDotParams" />
								<!-- 广告位（直播） -->
								<vh-normal-live-ad v-else-if="item.modul == 2" :from="$vhFrom" :item="item" :buryDotParams="buryDotParams" />
								<!-- 广告位（专题活动） -->
								<vh-normal-thematic-activities-ad v-else-if="item.modul == 3" :from="$vhFrom" :item="item" :buryDotParams="buryDotParams" />
								<!-- 广告位（专题产品） -->
								<vh-normal-special-products-ad v-else-if="item.modul == 4" :from="$vhFrom" :item="item" :buryDotParams="buryDotParams" />
								<!-- 常规商品 -->
								<vh-normal-goods-list v-else :from="$vhFrom" :item="item" />
							</view>
							<u-loadmore :status="loadStatus" />
						</view>
					</view>
					
					<!-- 弹框 -->
					<view class="">
						<!-- 筛选弹框 -->
						<u-popup v-model="showScreenPop" mode="bottom" height="800" :border-radius="20">
							<view class="p-rela h-800">
								<!-- 标题 -->
								<view class="p-fixed top-0 z-100 w-p100 bg-ffffff d-flex j-sb a-center ptb-32-plr-30">
									<view class=""></view>
									<view class="text-center font-32 font-wei text-0">全部筛选</view>
									<image class="w-44 h-44" :src="`${osip}/second_hair/clo_gray.png`" mode="widthFix" @click.stop="showScreenPop = false" />
								</view>
								
								<!-- 拥有筛选数据 -->
								<view v-if="hasGotFilterList" class="fade-in">
									<!-- 筛选内容（关键词、类型、国家、价格区间） -->
									<view class="pt-100 pr-28 pb-40 pl-28">
										<!-- 关键词 -->
										<view class="mt-02">
											<view class="font-32 font-wei text-3">关键词</view>
											<view class="d-flex a-center flex-wrap ml-n-28">
												<view class="ml-24 bg-f6f6f6 w-216 h-70 b-rad-36 d-flex j-center a-center mt-20 font-26 text-3" 
												:class="keywordId == item.id ? 'fade-in bg-fce0e0 b-s-01-e80404 text-e04040' : 'fade-in'" 
												v-for="(item, index) in keywordList" :key="index" @click="selectFilterItem('product_keyword', 'keywordId', item)">{{item.name}}</view>
											</view>
										</view>	
										
										<!-- 类型 -->
										<view class="mt-60">
											<view class="font-32 font-wei text-3">类型</view>
											<view class="d-flex a-center flex-wrap ml-n-28">
												<view class="ml-24 bg-f6f6f6 w-216 h-70 b-rad-36 d-flex j-center a-center mt-20 font-26 text-3" 
												:class="categoryId == item.id ? 'fade-in bg-fce0e0 b-s-01-e80404 text-e04040' : 'fade-in'" 
												v-for="(item, index) in wineTypeList" :key="index" @click="selectFilterItem('product_category', 'categoryId', item)">{{item.name}}</view>
												<!-- <view class="w-216 h-70 b-rad-36 d-flex j-center a-center mt-20">
													<text class="font-28 text-3">查看更多</text>
													<image class="w-20 h-12 ml-04" src="https://images.vinehoo.com/vinehoomini/v3/flash_purchase/more_arr.png" mode="widthFix"></image>
												</view> -->
											</view>
										</view>
										
										<!-- 国家 -->
										<view class="mt-60">
											<view class="font-32 font-wei text-3">国家</view>
											<view class="d-flex a-center flex-wrap ml-n-28">
												<view class="ml-24 bg-f6f6f6 w-216 h-70 b-rad-36 d-flex j-center a-center mt-20 font-26 text-3" 
												:class="countryId == item.id ? 'fade-in bg-fce0e0 b-s-01-e80404 text-e04040' : 'fade-in'" 
												v-for="(item, index) in countryList" :key="index" @click="selectFilterItem('country', 'countryId', item)">{{item.name}}</view>
											</view>
										</view>
										
										<!-- 价格区间 -->
										<view class="mt-60">
											<view class="font-32 font-wei text-3">价格区间（元）</view>
											<view class="d-flex j-sb a-center mt-20">
												<input v-model="minPrice" class="bg-f6f6f6 w-308 h-70 b-rad-36 text-center font-26 text-3" type="number" placeholder="最低价" placeholder-style="color:#999;font-size:26rpx;" />
												<view class="bg-999999 w-32 h-01 t-sc-y-h-1"></view>
												<input v-model="maxPrice" class="bg-f6f6f6 w-308 h-70 b-rad-36 text-center font-26 text-3" type="number" placeholder="最高价" placeholder-style="color:#999;font-size:26rpx;" />
											</view>
										</view>				
									</view>
									
									<!-- 底部按钮 -->
									<view class="p-stic bottom-0 z-999 bg-feffff w-p100 h-104 d-flex j-sa a-center b-sh-00021200-022 pl-32 pr-32">
										<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
										:custom-style="{width:'308rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#999', backgroundColor: '#EEEEEE', border:'none'}"
										@click="resetFilter()">重置</u-button>
										
										<u-button :disabled="!canFilter" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
										:custom-style="{width:'308rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: !canFilter ? '#FCE4E3' : '#E80404', border:'none'}"
										@click="confirmFilter()">确定</u-button>
									</view>
								</view>
							
								<!-- 没有筛选数据 -->
								<view v-else class="fade-in h-550 d-flex j-center a-center">
									<vh-loading mode="circle"></vh-loading>
								</view>
							</view>
						</u-popup>
						
						<!-- 套餐弹框 -->
						<u-popup v-model="showGoodsPackPop" mode="bottom" :duration="150" :border-radius="20">
							<view class="pt-32 pr-24 pb-48 pl-24">
								<view class="d-flex">
									<vh-image :loading-type="2" :src="goodsInfo.product_img" :width="152" :height="152" bg-color="#F9F9F9" :border-radius="6"/>
									
									<view class="d-flex flex-1 flex-column j-sb ml-16">
										<view class="font-28 text-3 l-h-40 o-hid text-hidden-2">{{goodsInfo.title}}</view>
										<view class="d-flex a-center mt-12">
											<text v-if="goodsInfo.is_hidden_price || [3, 4].includes(goodsInfo.onsale_status)" class="font-44 font-wei text-e80404">价格保密</text>
											<text v-else class="font-44 font-wei text-e80404"><text class="font-24 mr-06">¥</text>{{goodsInfo.price}}</text>
											<!-- <text v-if="packageType == 1" class="font-44 font-wei text-e80404"><text class="font-24 mr-06">¥</text>{{packageInfo.group_price}}</text>
											<text v-if="packageType == 2" class="font-44 font-wei text-e80404"><text class="font-24 mr-06">¥</text>{{packageInfo.newcomer_price}}</text> -->
											<text class="ml-10 font-24 text-9 text-dec-l-t">¥{{goodsInfo.market_price}}</text>
										</view>
									</view>
								</view>
								
								<view class="mt-48 font-32 font-wei text-3">规格</view>
								
								<view class="d-flex flex-wrap ml-n-24">
									<!-- 普通商品套餐选项 -->
									<view v-for="(item, index) in packageList" :key="index">
										<view class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3"
										:class="packageIndex != index ? '' : clickSelectPackage ? 'skew-top bg-fce4e3 b-s-01-e80404 text-e80404' : 'bg-fce4e3 b-s-01-e80404 text-e80404'" 
										@click="selectPackage(index)">{{item.package_name}}</view>
									</view>
									
									<!-- 拼团商品套餐选项 -->
									<!-- <view v-if="packageType == 1" v-for="(item, index) in groupPackageList" :key="index">
										<view class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3" 
										:class="packageIndex != index ? '' : clickSelectPackage ? 'skew-top bg-fce4e3 b-s-01-e80404 text-e80404' : 'bg-fce4e3 b-s-01-e80404 text-e80404'" 
										@click="selectPackage(index, 1)">{{item.package_name}}</view>
									</view> -->
									
									<!-- 新人套餐选项 -->
									<!-- <view v-if="packageType == 2" v-for="(item, index) in newPeoplePackList" :key="index">
										<view class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3" 
										:class="packageIndex != index ? '' : clickSelectPackage ? 'skew-top bg-fce4e3 b-s-01-e80404 text-e80404' : 'bg-fce4e3 b-s-01-e80404 text-e80404'" 
										@click="selectPackage(index, 2)">{{item.package_name}}</view>
									</view> -->
								</view>
								
								<view class="d-flex j-sb a-center mt-52">
									<view class="font-32 font-wei text-3">数量</view>
									<view class="">
										<u-number-box v-model="purchaseNumbers" :min="1" :input-width="64" :input-height="50" :size="28" />
									</view>
								</view>
								
								<view class="mt-92 d-flex j-center a-center">
									<view v-if="packageInfo.inventory < 1" class="fade-in">
										<u-button :disabled="true" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
										:custom-style="{width:'646rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}">库存不足</u-button>
									</view>
									<view v-else class="">
										<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
										:custom-style="{width:'646rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" 
										@click="addCar">确定</u-button>
									</view>
								</view>
							</view>
						</u-popup>
					</view>
				</view>
				
				<!-- 门店板块 -->
				<view v-if="isStore" class="bg-ffffff fade-in">
					<!-- 导航栏 -->
					<vh-navbar :is-back="false" :background="{background: '#E80404'}" title="线下门店" title-color="#FFF">
						<view class="h-p100 d-flex a-center ml-24" @click="mOpenSecondHair()">
							<u-icon name="nav-back" color="#FFF" :size="44" />
							<view class="ml-10 font-32 font-wei text-ffffff w-s-now">秒发</view>
						</view>
					</vh-navbar>
					
					<!-- 工具栏 -->
					<view class="h-100 d-flex j-sb a-center ptb-00-plr-28">
						<view class="w-430 h-68 bg-f7f7f7 b-rad-40 d-flex a-center" @click="jump.navigateTo(`${routeTable.pBStoreGoodsSearch}?sid=${mSid}`)">
							<image class="w-36 h-36 ml-34" src="https://images.vinehoo.com/vinehoomini/v3/comm/ser_gray.png" mode="aspectFill" />
							<text class="ml-08 font-28 text-9">搜索酒款</text>
						</view>
						
						<view class="d-flex a-center">
							<image class="w-44 h-44" :src="`${osip}/second_hair/m_scan.png`" mode="widthFix" @click="mStoreScan"/>
							<image class="w-44 h-44 ml-30" :src="`${osip}/second_hair/m_cart.png`" mode="widthFix" @click="jump.loginNavigateTo( routeTable.pBStoreShoppingCart )"/>
							<image class="w-44 h-44 ml-30" :src="`${osip}/second_hair/m_history.png`" mode="widthFix" @click="jump.loginNavigateTo( routeTable.pBStoreOrder )"/>
						</view>
					</view>
					
					<!-- 门店信息 -->
					<view class="d-flex a-center bt-s-01-eeeeee bb-s-01-eeeeee ptb-30-plr-32" @click="jump.navigateTo(`/packageB/pages/store-detail/store-detail`)">
						<image class="w-28 h-28" :src="`${osip}/second_hair/m_add_red.png`" mode="widthFix" />
						<view class="w-max-620 mtb-00-mlr-12 font-28 text-3 text-hidden-1">{{storeInfo.store_name}}</view>
						<u-icon name="arrow-right" :size="20" color="#999" />
					</view>
					
					<!-- 分类筛选栏 -->
					<view class="d-flex" :style="{height: '65vh'}">
						<!-- 门店分类列表 -->
						<scroll-view class="w-160 h-p100 bg-f5f5f5" :scroll-y="true">
							<view class="p-rela w-160 h-94 d-flex j-center a-center font-28" :class="item.id == mGoodsCustomTypeId ? 'bg-ffffff font-30 font-wei text-ca101a' : 'bg-f5f5f5 text-6'" 
							v-for="(item, index) in mGoodsFilterList" :key="index" @click="mSelectGoodsType(item)">
								<view v-show="item.id == mGoodsCustomTypeId" class="p-abso left-0 top-0 w-08 h-94 bg-ca101a" />
								{{item.classify_name}}
							</view>
						</scroll-view>
						
						<!-- 门店商品列表 -->
						<scroll-view class="p-rela h-p100" style="width: calc(100% - 160rpx);" :scroll-y="true" @scrolltolower="mScrollEnd()">
							<!-- 筛选栏 -->
							<view class="p-stic top-0 z-02 h-94 bg-ffffff d-flex j-sb a-center ptb-00-plr-20">
								<view class="d-flex a-center ml-60" @click="mChangeSort(1)">
									<text class="font-28" :class="mSortType == 1 ? 'font-wei text-3' : 'text-9'">价格</text>
									<image class="w-12 h-12 ml-06" :src="`${osip}/second_hair/sort${mPriceSort == 1 ? '_asc' : mPriceSort == 2 ? '_desc' : ''}.png`" mode="widthFix" />
								</view>
								
								<view class="d-flex a-center" @click="mChangeSort(2)">
									<text class="font-28" :class="mSortType == 2 ? 'font-wei text-3' : 'text-9'">销量</text>
									<image class="w-12 h-12 ml-06" :src="`${osip}/second_hair/sort${mSoldSort == 1 ? '_asc' : mSoldSort == 2 ? '_desc' : ''}.png`" mode="widthFix" />
								</view>
								
								<view class="d-flex a-center" @click="mShowScreenPop = true">
									<view class="w-01 h-40 bg-cccccc"></view>
									
									<view class="ml-34 d-flex a-center">
										<text class="font-28 text-9">筛选</text>
										<image class="w-34 h-34 ml-10" :src="`${osip}/comm/funnel.png`" mode="aspectFill" />
									</view>
								</view>
							</view>
							
							<!-- 门店商品列表 -->
							<view class="">
								<!-- 有门店商品列表 -->
								<view v-if="mGoodsList.length" class="">
									<view class="d-flex pr-20 pb-20 pl-20" v-for="(item, index) in mGoodsList" :key="index" @click="jump.navigateTo(`/pages/store-goods-detail/store-goods-detail?id=${item.id}`)">
										<vh-image :loading-type="2" :src="item.goods_images" :width="200" :height="200" :border-radius="10" mode="aspectFit"/>
										
										<view class="flex-1 d-flex flex-column j-sb ml-20">
											<view class="">
												<view class="font-28 text-0 text-hidden-2 l-h-40">{{item.goods_name}}</view>
												<view class="mt-10 font-24 text-6 text-hidden-1 l-h-36">{{item.brief}}</view>
											</view>
											
											<view class="d-flex j-sb a-center">
												<view class="">
													<text class="font-32 font-wei text-ff0013"><text class="font-24">¥</text>{{item.price}}</text>
													<text class="ml-04 font-20 text-9">已售<text class="text-ff0013">{{item.soldnum}}</text>份</text>
												</view>
												
												<image class="w-40 h-40 p-04" :src="`${osip}/second_hair/m_add_car.png`" mode="aspectFill" @click.stop="mOpenPackagePop(item)"></image>
											</view>
										</view>
									</view>
									<view class="ptb-20-plr-00">
										<u-loadmore :status="loadStatus" />
									</view>
								</view>
								
								<!-- 没有门店商品列表 -->
								<vh-empty v-else :padding-top="200" :image-src="`${osip}/empty/emp_goods.png`" text="暂无门店商品~" />
							</view>
						</scroll-view>
					</view>
				    
					<!-- 弹框 -->
					<view class="">
						<!-- 门店筛选弹框 -->
						<u-popup v-model="mShowScreenPop" mode="right" :width="548" :border-radius="20">
							<view class="p-rela fade-in">
								<!-- 筛选内容（ 国家、关键词、价格区间 ） -->
								<view class="pr-32 pb-124 pl-32">
									<!-- 国家 -->
									<view class="mt-120">
										<view class="font-28 font-wei text-3">国家</view>
										<view class="d-flex j-sb a-center flex-wrap">
											<view class="w-148 h-48 bg-f6f6f6 b-rad-24 d-flex j-center a-center mt-28 font-24 text-6" :class="mCountryId == item.id ? 'bg-fce0e0 text-e80404' : ''"
											v-for="(item, index) in mCountrylist" :key="index"
											@click="mScreen( 1, item )">{{item.attribute_value}}</view>
										</view>
									</view>
									
									<!-- 关键字 -->
									<view class="mt-60">
										<view class="font-28 font-wei text-3">关键词</view>
										<view class="d-flex j-sb a-center flex-wrap">
											<view class="w-148 h-48 bg-f6f6f6 b-rad-24 d-flex j-center a-center mt-28 font-24 text-6" :class="mKeywordId == item.id ? 'bg-fce0e0 text-e80404' : ''"
											v-for="(item, index) in mKeywordlist" :key="index"
											@click="mScreen( 2, item )">{{item.attribute_value}}</view>
										</view>
									</view>
									
									<!-- 价格 -->
									<view class="mt-60">
										<view class="font-28 font-wei text-3">价格区间</view>
										
										<view class="d-flex j-sb a-center mt-20">
											<input v-model="mMinPrice" class="bg-f6f6f6 w-218 h-48 b-rad-24 text-center font-24 text-3" type="number" placeholder="最低价" placeholder-style="color:#999;font-size:24rpx;" />
											<view class="bg-cccccc w-26 h-01"></view>
											<input v-model="mMaxPrice" class="bg-f6f6f6 w-218 h-48 b-rad-24 text-center font-24 text-3" type="number" placeholder="最高价" placeholder-style="color:#999;font-size:24rpx;" />
										</view>
									</view>
								</view>
								
								<!-- 底部按钮 -->
								<view class="p-fixed bottom-0 z-999 bg-feffff w-p100 h-104 d-flex j-sa a-center b-sh-00021200-022 pl-32 pr-32">
									<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
									:custom-style="{width:'228rpx', height:'72rpx', fontSize:'28rpx', color:'#666', backgroundColor: '#EEEEEE', border:'none'}"
									@click="mResetFilter()">重置</u-button>
									
									<u-button :disabled="!mCanFilter" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
									:custom-style="{width:'228rpx', height:'72rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: !mCanFilter ? '#FCE4E3' : '#E80404', border:'none'}"
									@click="mConfirmFilter()">确定</u-button>
								</view>
								
							</view>
						</u-popup>
					    
						<!-- 门店套餐弹框 -->
						<u-popup v-model="mShowGoodsPackPop" mode="bottom" :duration="150" :border-radius="20">
							<view class="pt-32 pr-24 pb-48 pl-24">
								<view class="d-flex">
									<vh-image :loading-type="2" :src="mGoodsInfo.goods_images" :width="152" :height="152" bg-color="#F9F9F9" :border-radius="6" mode="aspectFit"/>
									<view class="d-flex flex-1 flex-column j-sb ml-16">
										<view class="font-28 text-3 l-h-40 o-hid text-hidden-2">{{mGoodsInfo.goods_name}}</view>
										<view class="d-flex a-center mt-12">
											<text class="font-44 font-wei text-e80404"><text class="font-24 mr-06">¥</text>{{mGoodsInfo.price}}</text>
											<text class="ml-10 font-24 text-9 text-dec-l-t">¥{{mGoodsInfo.market_price}}</text>
										</view>
									</view>
								</view>
								
								<view class="mt-48 font-32 font-wei text-3">规格</view>
								
								<view class="d-flex flex-wrap ml-n-24">
									<!-- 普通商品套餐选项 -->
									<view v-for="(item, index) in mPackageList" :key="index">
										<view class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3" :class="mPackageIndex == index ? 'bg-fce4e3 b-s-01-e80404 text-e80404' : ''" 
										@click="mSelectPackage(index)">{{item.c_name}}</view>
									</view>
								</view>
								
								<view class="d-flex j-sb a-center mt-52">
									<view class="font-32 font-wei text-3">数量</view>
									<u-number-box v-model="mPurchaseNumbers" :min="1" :max="mPackageInfo.maxstock" :input-width="64" :input-height="50" :size="28" />
								</view>
								
								<view class="mt-92 d-flex j-center a-center">
									<view v-if="mPackageInfo.stock_insufficient == 0 && mPackageInfo.is_enable == 1 && mPackageInfo.maxstock" class="">
										<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
										:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" 
										@click="mConfirmAddShoppingCart">确定</u-button>
									</view>
									<view v-else class="">
										<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
										:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}" 
										@click="feedback.toast({ title: '该套餐库存不足~' })">库存不足</u-button>
									</view>
								</view>
							</view>
						</u-popup>
					</view>
				</view>
			</view>
			
			<!-- 未开启定位 -->
			<view v-else class="bg-ffffff h-1440 d-flex flex-column a-center">
				<image class="w-440 h-360 mt-p40" :src="`${osip}/empty/emp_addr.png`" mode="aspectFill"></image>
				<view class="font-30 text-9 mt-20">获取位置失败，无法帮您找到秒发信息</view>
				<view class="font-28 text-9 mt-10">请确保手机定位打开</view>
				<view class="d-flex j-center mt-130">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'646rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}" @click="mSearchWifi()">重新定位</u-button>
				</view>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<vh-skeleton v-else :has-status-bar="false" :has-navigation-bar="false" :has-tab-bar="true" :show-loading="false" />
		
		<!-- 提示内容 -->
		<u-toast ref="uToast" />
		
		<!-- 底部导航栏 tabBar -->
		<vh-tabbar :show="!isFromAuction" />
	</view>
</template>

<script>
	import wxMap from '@/common/js/fun/amap-wx.js'
	import { mapState, mapMutations } from 'vuex'
	import judgeAppNewVersion from '@/common/js/utils/judgeAppNewVersion'
	
	export default {
		name: 'miaofa',

		props: {
			isFromAuction: {
				type: Boolean,
				default: false
			}
		},
		
		data() {
			return {
				// 公共板块
				osip:'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
				loading: true, //加载状态 true = 加载中、false = 结束加载
				amapPlugin: null, //定位插件
				key: '6b15b01a3a5ed85f0e66797201febf92', //定位key
				openPosition: true, //是否开启定位
				isStore: 0,// 是否显示门店 0 = 秒发板块，1 = 门店板块
				page: 1, //第几页
				limit: 10, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //底部加载状态
				
				// 秒发板块
				hasGotSecondData:0, //是否已经获取过秒发数据
				channelKeyword:'大家都在搜', //搜索关键字
				shoppingCartNum: 0, //购物车数量
				unReadTotalNum:0, //未读消息总数量
				allObj:{}, //所有数据
				swiperList: [], //轮播列表 
				goldAreaList: [], //金刚区列表
				cardList: [], //卡片列表
				sortList:[ {index: 1, name: '上新', type: 'onsale_time'}, {index: 2, name: '价格', type: 'price'}, {index: 3, name: '销量', type: 'purchased'} ], //排序列表
				sortType:'sort', //需要排序的字段 上新 = onsale_time、价格 = price、销量 = purchased
				sort:'desc', //排序方式 asc = 升序、desc = 降序
				showWaterfallFlow: true, //切换排版方式 false = 正常列表 true = 瀑布流
				showScreenPop: false, //是否展示筛选弹窗
				hasGotFilterList: 0, //是否已经请求过筛选列表 0 = 未请求过、1 = 请求过
				wineTypeList: [], //酒类型列表
				categoryId:'', //酒类型id
				product_category: [0], //酒类型名称 形如 ['干白']
				countryList:[], //国家列表
				countryId:'', //国家id
				country: [0], //国家名称 形如 ['英国']
				keywordList: [], //关键字列表
				keywordId:'', //关键字id
				product_keyword: [0], //关键字名称 形如 ['气泡']
				minPrice: '', //最低金额
				maxPrice: '', //最高金额
				isFilter: 0, //是否筛选 0 = 不是筛选 1 = 是筛选
				doFilter: 0, //是否做过筛选 0 = 未做筛选、1 = 做了筛选
				filterIndex:0, //筛选索引
				filterInfo: {}, //过滤信息
				goodsList: [], //商品列表
				goodsInfo:{}, //商品信息
				hasGotGoodsList:0, //是否获取过商品列表数据 0 = 没获取过、1 = 获取过
				adList: [], //广告列表
				filterScrollTop: 1000, //filter容器离页面顶部的高度
				
				// 套餐
				packageIndex:0, //选中的套餐索引
				clickSelectPackage: 0, //是否点击显示套餐 0 = 未点击、1 = 点击
				packageInfo:{}, //套餐信息
				showGoodsPackPop: false, //是否展示套餐弹框
				packageList:[], //普通套餐列表
				packageInfo:{}, //套餐信息
				limitInfo:{}, //限购信息
				purchaseNumbers:1, //购买数量
				
				// 门店板块
				mClassifyContainerRestHeight: 0, //分类栏剩余高度
				mLatitude:'', //纬度
				mLongitude:'', //经度
				mSid: '', //门店id
				mGoodsFilterList: [], //门店商品筛选列表
				mGoodsTypeId:'', //门店商品类型ID
				mGoodsCustomTypeId:'', //门店商品自定义类型id
				mSortType:'', //排序类型 1 = 按价格排序、2 = 按销量排序
				mPriceSort:'', //按价格排序
				mSoldSort:'', //按销量排序
				mShowScreenPop: false, //是否展示门店筛选弹框
				mCountrylist: [], //国家列表
				mHasGotGoodsList:0, //是否获取过门店商品列表数据 0 = 没获取过、1 = 获取过
				mCountryId: '', //国家id
				mKeywordId:'', //关键字id
				mMinPrice:'', //最低价
				mMaxPrice:'', //最高价
				mKeywordlist: [], //关键字列表
				mGoodsList: [], //商品列表
				mGoodsInfo:{}, //门店商品信息
				mPackageList:[], //门店套餐列表
				mPackageInfo: {}, //门店商品套餐信息
				mPackageIndex: 0, //套餐索引
				mPurchaseNumbers: 1, //购买数量
				mShowGoodsPackPop: false, //显示门店套餐弹框
				buryDotParams: {
					channel: 4,
					region_id: 305000
				},
				isFixedFilter: false,
				goodsListMinHeight: 0,
			}
		},
		
		onLoad( options ) {
			if (options.q) { //微信扫码
				let url = decodeURIComponent(options.q)
				let tid = parseInt(this.param.getUrlParam( url, 'tid' ))
				let sid = parseInt(this.param.getUrlParam( url, 'sid' ))
				let tablename = this.param.getUrlParam(url, 'tablename')
				let tableInfo = {tid, sid, tablename }
				console.log( url )
				this.muStoreTableInfo(tableInfo)
			}
			
			this.system.setNavigationBarBlack()
			// this.mSearchWifi() //门店请求首页数据流程
			this.initOnload() //初始化秒发数据
		},
	
		onShow() {
			this.initOnShow()
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['storeTableInfo', 'storeInfo', 'routeTable', 'requestPrefix']),
			
			// 获取导航栏高度
			getNavigationBarHeight(){
				return this.system.navigationBarHeight()
			},
			
			// 秒发是否能够筛选
			canFilter() {
				// console.log(Object.keys(this.filterInfo).length)
				if( ( this.product_category[0] !== 0 || this.country[0] !== 0 || this.product_keyword[0] !== 0 ) && this.minPrice === '' && this.maxPrice === ''){
					return true
				}else{
					if( this.minPrice !== '' && this.maxPrice !== '' && parseInt( this.minPrice ) < parseInt( this.maxPrice )) {
						return true
					}
				}
				return false
			},
			
			//门店是否能够筛选
			mCanFilter() {
				if( ( this.mCountryId !== '' || this.mKeywordId !== '' ) && this.mMinPrice === '' && this.mMaxPrice === ''){
					return true
				}else{
					if( this.mMinPrice !== '' && this.mMaxPrice !== '' && parseInt( this.mMinPrice ) < parseInt( this.mMaxPrice )) {
						return true
					}
				}
				return false
			},

			client ({ $android, $ios }) {
				let client = 3
				if($android) client = 1
				else if($ios) client = 0
				return client
			},

			filterTop ({ isFromAuction, $ios, $appStatusBarHeight }) {
				if (isFromAuction) {
					if ($ios) return 46 + $appStatusBarHeight
					return 66
				}
				return 46.5
			}
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muStoreTableInfo','muStoreInfo']),
			
			// 共用板块（ 先搜索wifi，再定位 ）
			// 门店搜索wifi
			mSearchWifi() {
				wx.startWifi({
					success: res=> {
						wx.getConnectedWifi({
							success: s => {
								this.mInit( 0, s.wifi.SSID )
							},
							fail: f => {
								// this.feedback.toast({ title:'连接wifi失败, 我需要走定位'})
								this.mPosition()
							}
						})
					},
					fail: err => {
						// this.feedback.toast({ title:'我没有wifi, 我需要走定位'})
						this.mPosition()
					}
				})
				
				// this.mInit(0, '123')
			},
			
			// 门店定位
			mPosition() {
				uni.getSystemInfo({
					success: res => {
						console.log(res)
						if( res.locationEnabled ){
							uni.authorize({
								scope: 'scope.userLocation',
								success: res => { //1.1 允许授权
									// this.feedback.toast({ title:'我允许了定位授权，开始获取经纬度'})
									this.mGetLocation()
								},
								fail: err => {    //1.2 拒绝授权
									uni.showModal({
										content:'检测到您没打开获取位置功能权限，无法获取更多服务，是否去设置打开？',
											confirmText: "确认",
											cancelText:'取消',
											success: ( res ) => {
												if( res.confirm ){
													uni.openSetting({
														success: ( res ) => {
															// this.feedback.toast({ title:'我从不允许打开定位到允许了定位授权,获取经纬度'})
															this.mGetLocation()
														}
													})
												}else{
													this.jump.reLaunch( this.routeTable.pgIndex )
													// this.feedback.toast({ title:'我弹框点击的还是不允许定位授权'})
												}
											}
									})		                            
								}
							})
						}else{
							this.loading = false
							this.openPosition = false
							this.feedback.toast({ title:'请打开定位~'})
						}
					}
				})
			},
			
			// 门店获取经纬度
			mGetLocation() {
				// 插件定位
				try{
					this.amapPlugin = new wxMap.AMapWX({ key: this.key });
					this.amapPlugin.getRegeo({
						success: ( res ) => {
							this.openPosition = true
							let { latitude, longitude } = res[0] //定位信息
							this.mLatitude = latitude //纬度
							this.mLongitude = longitude //经度
							console.log('当前位置的经度（longitude）：' + res[0].longitude);
							console.log('当前位置的纬度（latitude）：' + res[0].latitude);
							this.mInit()
						},
						fail: (err) => {
							this.feedback.toast({ title:'amapPlugin.getRegeo失败！'})
						}
					});
				}catch(e){
					this.feedback.toast({ title:'初始化amap失败，请排查错误！'})
				}
				
				// 普通定位
				// uni.getLocation({
				// 	accuracy:'best',
				// 	isHighAccuracy: true,
				// 	success: res => {
				// 		console.log('当前位置的经度：' + res.longitude);
				// 		console.log('当前位置的纬度：' + res.latitude);
						
				// 		// 106.487234,29.597693
						
				// 		console.log(this.gps.lonAndLatDis(29.597693, 106.487234, res.latitude, res.longitude ))
				// 		// 29.59555200 106.48984700
				// 	}
				// });
			},
			
			// 门店板块
			// 门店信息初始化 type = 类型（ 0 = wifi、1 = 定位 ）、wifiName = wifi名称，默认为 ''
			async mInit( type = 1, wifiName = '') {
				let res = await this.$u.api.storeList()
				let { list } = res.data
				if( list.length ) { //线下门店后台配置了门店
					this.mSid = type == 0 ? this.mGetStoreIdByWifi( list, wifiName ) : this.mGetStoreIdByLocation( list ) //通过wifi或者定位获取门店id（ 目前酒云网线下门店不多，只考虑在配置半径范围内的门店，不考虑两个门店同时都在配置的半径范围内的情况 ）
					if( this.mSid ) { //搜索到了最近的门店
						this.mStoreGoodsInfo()
					}else{ //没有搜索到最近的门店
						// this.feedback.toast({ title:'附近没有搜索到门店呀！我得显示秒发'})
						this.initOnload()
					}
				}else{ //线下门店后台没有配置了门店
					// this.feedback.toast({ title:'请配置门店！'})
					this.initOnload()
				}
			},
			
			// 通过wifi获取门店id list = 门店列表、wifiName = wifi名称
			mGetStoreIdByWifi( list, wifiName ) {
				console.log(list)
				console.log(wifiName)
				let storeId = ''
				for(let i = 0; i < list.length; i++){
					for(let j = 0; j < list[i].wifilocation.length; j++){
						if(list[i].wifilocation[j] === wifiName){
							storeId = list[i].id
							break
						}
					}
				}
				return storeId
			},
			
			// 通过定位获取门店id list = 门店列表
			mGetStoreIdByLocation( list ) {
				let storeId = ''
				// 29.59555201 106.48984702
				// 29.59555200 106.48984700
				console.log(list)
				for( let item of list ) {
					if( this.gps.lonAndLatDis( this.mLatitude, this.mLongitude , item.latitude, item.longitude ) <= item.radius ) {
						storeId = item.id
						break
					}
				}
				return storeId
			},
			
			// 获取门店信息
			async mStoreGoodsInfo() {
				let res = await this.$u.api.storeInfo({ sid: this.mSid })
				this.muStoreInfo(res.data)
				this.mGetStoreFilterList()
				this.mStoreAddRegister()
				if( this.storeTableInfo.tid ) this.$refs.uToast.show({ title: `下单后您的酒款将配送到${this.storeTableInfo.tablename}~`, duration:3000 })
				// this.feedback.toast({ title: `下单后您的酒款将配送到${this.storeTableInfo.tablename}~`, duration: 3000 })
				// this.mTabCont = `下单后您的酒款将配送到${tableInfo.tablename}`
			},
			
			// 门店添加用户
			mStoreAddRegister() {
				uni.getStorage({
					key:'loginInfo',
					success: async res => {
						try{
							let { uid, telephone, nickname, avatar_image, openid } = res.data
							console.log('---------------------------------我是门店注册用户接口需要上传的参数')
							await this.$u.api.storeAddRegister({uid, loginname: telephone, nickname, avatar_image, applet_openid: openid, sid: this.storeInfo.id})
						}catch(e){
							//TODO handle the exception
						}
					},
					fail: err => {
						console.log('-------------我没有用户信息')
					}
				})
			},
			
			// 门店筛选列表
			async mGetStoreFilterList() {
				let res = await this.$u.api.storeFilterList({ sid: this.mSid })
				let { classifylist, countrylist, keywordlist } = res.data
				if( classifylist.length ) { //分类列表
					this.mGoodsFilterList = classifylist
					// this.mGoodsTypeId = this.mGoodsFilterList[0].id //（分类列表（目前弃用））
					this.mGoodsCustomTypeId = this.mGoodsFilterList[0].id
				}
				this.mCountrylist = countrylist //国家列表
				this.mKeywordlist = keywordlist //关键词列表
				this.mGetStoreGoodsList()
			},
			
			// 选择商品类型 item = 列表某一项
			mSelectGoodsType( item ) {
				// this.mGoodsTypeId = '', //门店商品类型ID（目前弃用）
				this.mGoodsCustomTypeId = item.id //门店商品自定义类型id
				this.page = 1
				this.mGetStoreGoodsList()
			},
			
			// 门店排序筛选 type = 排序类型、1 = 按价格排序、2 = 按销量排序
			mChangeSort( type ) {
				console.log( type )
				this.mSortType = type
				switch( type ) {
					case 1:
					this.mPriceSort == 1 ? this.mPriceSort = 2 : this.mPriceSort = 1
					this.mSoldSort = ''
					break
					case 2:
					this.mSoldSort == 1 ? this.mSoldSort = 2 : this.mSoldSort = 1
					this.mPriceSort = ''
					break
				}
				this.page = 1
				this.mGetStoreGoodsList()
			},
			
			// 门店过滤国家、关键字 type = 筛选类型 1 = 国家、2 = 关键字、item = 列表某一项
			mScreen( type, item ) {
				console.log(type)
				console.log(item)
				switch( type ) {
					case 1:
					console.log('----------我是国家')
					this.mCountryId == item.id ? this.mCountryId = '' : this.mCountryId = item.id
					break
					case 2:
					this.mKeywordId == item.id ? this.mKeywordId = '' : this.mKeywordId = item.id
					console.log('----------我是关键字')
					break
				}
			},
			
			// 门店确认筛选
			mConfirmFilter() {
				this.mShowScreenPop = false
				this.mGetStoreGoodsList()
			},
			
			// 门店重置筛选
			mResetFilter() {
				this.mCountryId = '' //国家id
				this.mKeywordId = '' //关键字id
				this.mMinPrice = '' //最低价
				this.mMaxPrice = '' //最高价
				this.mShowScreenPop = false
				this.mGetStoreGoodsList()
			},
			
			// 获取门店商品接口
			async mGetStoreGoodsList() {
				if( this.mHasGotGoodsList ) this.feedback.loading() //是否已经获取过商品列表数据
				let data = {}
				data.sid = this.mSid //门店id
				// data.goods_type = this.mGoodsTypeId //类型ID
				data.classify = this.mGoodsCustomTypeId //自定义类型ID
				data.sort_price = this.mPriceSort //价格排序
				data.sort_soldnum = this.mSoldSort //销量排序
				data.country_id = this.mCountryId //国家id
				data.keyword = this.mKeywordId //关键字id
				data.price_low = parseInt(this.mMinPrice) || '' //最低价
				data.price_high = parseInt(this.mMaxPrice) || '' //最高价
				data.page = this.page //第几页
				data.limit = this.limit //每页多少条
				let res = await this.$u.api.storeGoodsList(data) //门店商品列表
				let { list, total } = res.data
				this.page == 1 ? this.mGoodsList = list : this.mGoodsList = [...this.mGoodsList, ...list]
				this.totalPage = Math.ceil( total / this.limit )
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				this.feedback.hideLoading() //是否已经获取过商品列表数据
				uni.stopPullDownRefresh()
				this.isStore = 1
				this.loading = false
				this.openPosition = true
			},
			
			// 打开门店套餐弹框 item = 商品列表每一项
			mOpenPackagePop( item ) {
				console.log(item)
				if(this.login.isLogin()) {
					if( item.collocation && item.collocation.length > 0 ) { //有套餐的情况
						this.mPurchaseNumbers = 1 //重置
						this.mGoodsInfo = item //门店商品信息
						this.mPackageList = item.collocation //门店商品套餐信息
						this.mPackageInfo = this.mPackageList[this.mPackageIndex] //默认选择第一个套餐
						this.mShowGoodsPackPop = true
					}else{
						this.feedback.toast({ title: '该商品没有套餐~' })
					}
				}
			},
			
			// 选择套餐 index = 套餐列表索引
			mSelectPackage( index ) {
				this.mPurchaseNumbers = 1
				this.mPackageIndex = index
				this.mPackageInfo = this.mPackageList[this.mPackageIndex]
			},
			
			// 确认加入购物车
			async mConfirmAddShoppingCart(){
				console.log('-------------------我是确认加入购物车')
				try{
					let res = await this.$u.api.storeGoodsAddShoppingCart({
						goods_id: this.mGoodsInfo.id, 
						pack_id: this.mPackageInfo.id, 
						nums: this.mPurchaseNumbers,
					})
					this.feedback.toast({title: '加入成功', icon: 'success'})
					this.mShowGoodsPackPop = false
				}catch(e){
					//TODO handle the exception
					console.log(e)
				}
			},
			
			// 门店扫码
			mStoreScan() {
				uni.scanCode({
					success: res => {
						console.log(res.result)
						if (res.result.indexOf('tid') > -1) { //扫桌子码
							console.log('-------------进入了桌面扫码分支')
							let url = res.result
							let tid = parseInt(this.param.getUrlParam( url, 'tid' ))
							let sid = parseInt(this.param.getUrlParam( url, 'sid' ))
							let tablename = this.param.getUrlParam(url, 'tablename')
							let tableInfo = {tid, sid, tablename }
							this.muStoreTableInfo(tableInfo)
							this.mSearchWifi()
						} else if ( res.result.indexOf('gid') > -1 ) { //扫酒瓶码
							console.log('-------------进入了酒瓶扫码的分支')
							let url = '';
							let httpsPath = ''
							if( this.requestPrefix == 'https://test-wxapp.wineyun.com') { //开发环境（测试域名）
								httpsPath = 'https://test-osms.wineyun.com/details'
							}else{ //生产环境（正式域名）
								httpsPath = 'https://osms.vinehoo.com/details'
							}
							url = res.result.replace(httpsPath, this.routeTable.pgStoreGoodsDetail)
							console.log('------------------------这是url')
							console.log(url)
							console.log('------------------------这是httpsPath')
							console.log(httpsPath)
							this.jump.navigateTo(url)
						} else {
							this.feedback.toast({ title: '请扫桌面二维码或者酒瓶二维码喔~'})
						}
					},
				})
			},
			
			// 门店商品列表滚动到底
			mScrollEnd() {
				console.log('====到底了')
				if (this.page == this.totalPage || this.totalPage == 0 ) return;
				this.loadStatus = 'loading'
				this.page ++
				this.mGetStoreGoodsList()
			},
			
			// 打开秒发
			mOpenSecondHair() {
				console.log('-------------------我是打开秒发')
				this.page = 1
				this.isStore = 0
				if( !this.hasGotSecondData ) this.initOnload()
			},

			
			// 秒发板块
			// 初始化（轮播...）
			async initOnload() {
				await Promise.all([ this.getSecondCombine(), this.getGoodsList(), this.getChannelKeyword() ])
				uni.stopPullDownRefresh()
				this.hasGotSecondData = 1
				this.loading = false
			},
			
			// 初始化onShow（接口聚合:消息通知、购物车数量...）
			async initOnShow() {
				try{
					await Promise.all([this.getShoppingCartNum(), this.getMessageUnreadNum()])
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 获取搜索关键字
			async getChannelKeyword() {
				let res = await this.$u.api.channelKeyword({ type: 2 })
				this.channelKeyword = res.data.keyword
			},
			
			// 获取购物车数量
			async getShoppingCartNum() {
				if(this.login.isLogin(this.from, 0)){
					let res = await this.$u.api.shoppingCartNum()
					console.log('-----------------------------我是购物车数量接口')
					console.log(res)
					this.shoppingCartNum = res.data
				}
			},
			
			// 获取未读消息数据
			async getMessageUnreadNum() {
				if(this.login.isLogin(this.from, 0)){
					let res = await this.$u.api.messageUnreadNum()
					this.unReadTotalNum = res.data.total_num
				}
			},
			
			// 秒发首页聚合
			async getSecondCombine() {
				let res = await this.$u.api.secondCombine({ client: this.client }) 
				let {banner, catalog, card } = res.data
				this.allObj = res.data
				this.swiperList = banner
				this.goldAreaList = catalog.filter((item, index) => { return index < 5 })
				this.cardList = card
				console.log('--------------------我是秒发聚合接口')
				console.log(res)
			},
			
			// 获取商品列表
			async getGoodsList() {
				if( this.hasGotGoodsList ) this.feedback.loading() //是否已经获取过商品列表数据
				let data = {} // 接口需要上传的数据
				data.page = this.page //第几页
				data.limit = this.limit // 每页限制多少条
				data.periods_type = [1]
				data.sort_type = this.sortType //商品需要排序的字段
				data.order = this.sort //排序方式 asc = 升序、desc = 降序
				data.filters = this.filterInfo //过滤信息
				if(this.minPrice !== '' && this.maxPrice !== '' ) { //价格区间（用户选择了价格后才传参）
					data.price_gte = parseInt(this.minPrice) //最低价（取整）
					data.price_lte = parseInt(this.maxPrice) //最高价（取整）
				}
				let res = await this.$u.api.flashGoodsList(data) // 获取闪购商品列表（筛选）
				this.hasGotGoodsList = 1
				let { total, list } = res.data
				if(this.page == 1 && this.isFilter == 0) await this.getAdList() //获取广告位数据
				this.page == 1 ? this.goodsList = list : this.goodsList = [...this.goodsList, ...list] 
				this.totalPage = Math.ceil(total / this.limit)
				this.insertAd() //插入广告位
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				this.feedback.hideLoading()
				this.$emit('totalPageChange', this.totalPage)
			},
			
			// 获取广告位数据
			async getAdList() {
				let res = await this.$u.api.advertisingList({ type: 5, channel: 2, client: this.client }) //获取广告数据
				this.adList = res.data.list.filter(v => { return v.modul_data != null }) //过滤不合法数据
				this.adList.forEach( item => {
					if( item.modul === 4 ) {
						item.modul_data.quota_rule = JSON.parse(item.modul_data.quota_rule) || {}
					}
				})
				this.adList = this.adList.sort((a, b) => { return a.sort - b.sort; }) //数组排序
			},
			
			// 打开门店
			openStore() {
				this.page = 1
				this.isStore = 1
			},
			
			// 清除瀑布流数据
			clearWaterfallList() {
				if(this.showWaterfallFlow) {
					console.log('-------------------------this.$refs.uWaterfall')
					console.log(this.$refs.uWaterfall)
					if(this.$refs.uWaterfall) {
						this.$refs.uWaterfall.clear()
					}
				}
			},
			
			// 插入广告位 res = 广告位数据
			insertAd() {
				if(this.isFilter == 0 && this.adList.length) {
					this.adList.forEach((item, index) => {
						if((this.page - 1) * this.limit <= item.sort && item.sort < this.page * this.limit && item.sort < this.goodsList.length) { // sort在列表数据之间
						    console.log('-----------------------sort >= 0 && sort < 当前页 * 每页限制数 && sort < goodsList.length')
							this.goodsList.splice(item.sort, 0, item)
						}else if(this.page == this.totalPage && item.sort >= this.goodsList.length){ // sort > 总条数
						    console.log('-----------------------sort > 总条数')
							this.goodsList.splice(this.goodsList.length, 0, item)
						}else{
							console.log('有数据没插入')
						}
					})
				}
			},
			
			// 排序筛选
			async changeSort(index) {
				await this.initGoodsListMinHeight()
				this.filterIndex = index
				this.isFilter = 1
				this.clearWaterfallList()
				switch(index) {
					case 0:
					this.sortType = 'sort'
					this.sort = 'desc'
					this.isFilter = 0
					break
					case 1:
					case 2:
					case 3:
					this.sortType = this.sortList[index - 1].type
					this.sort == 'desc' ? this.sort = 'asc' : this.sort = 'desc'
					break
				}
				this.resetPage()
				this.getGoodsList().then(() => {
					this.changeScroll()
				})
			},
			
			// 获取筛选列表（类型、国家、关键字）
			async getFilterList() {
				this.showScreenPop = true
				if(this.hasGotFilterList == 0) {
					let res = await this.$u.api.secondHairFilterList()
					this.countryList = res.data.list.filter(v => { return v.type == 1 }) //过滤国家
					this.wineTypeList = res.data.list.filter(v => { return v.type == 2 }) //过滤类型
					this.keywordList = res.data.list.filter(v => { return v.type == 3 }) //过滤关键字
					this.hasGotFilterList = 1
				}
			},
			
			// 选中筛选内容 typeName = 需要过滤的类型名称 类型 = product_category、国家 = country、关键字 = product_keyword、item = 筛选类型列表某一项
			selectFilterItem(typeName, typeId, item) {
				if(this[typeId] == item.id) {
					this[typeName] = [0]
					this[typeId] = ''
					delete this.filterInfo[typeName]
				}else {
					this[typeName] = [item.name]
					this[typeId] = item.id
					this.filterInfo[typeName] = [item.name]
				}
			},
			
			// 确认筛选
			async confirmFilter() {
				await this.initGoodsListMinHeight()
				this.doFilter = 1
				this.isFilter = 1
				this.resetPage()
				this.clearWaterfallList()
				await this.getGoodsList()
				this.showScreenPop = false
				this.changeScroll()
			},
			
			// 重置筛选
			async resetFilter() {
				await this.initGoodsListMinHeight()
				this.doFilter = 0
				this.categoryId = ''
				this.product_category = [0]
				this.countryId =''
				this.country = [0]
				this.keywordId = ''
				this.product_keyword = [0]
				this.filterInfo = {}
				this.isFilter = 0
				this.minPrice = ''
				this.maxPrice = ''
				this.resetPage()
				this.clearWaterfallList()
				await this.getGoodsList()
				this.showScreenPop = false
				this.changeScroll()
			},
			
			// 筛选滚动
			filterScroll() {
				uni.createSelectorQuery().in(this).select('#goods-list-con').boundingClientRect(data=>{
					uni.createSelectorQuery().in(this).select('#outer-content').boundingClientRect(res=>{
						setTimeout(() => { this.system.pageScrollTo(data.top - res.top - 120 , 300) }, 500 )
					}).exec()
				}).exec()
			},
			
			// 打开套餐弹框 item = 秒发列表某一项
			async openPackPop(item) {
				if(this.login.isLogin(this.$vhFrom)) {
					this.feedback.loading()
					this.goodsInfo = item
					console.log('goodsInfo', this.goodsInfo)
					this.showGoodsPackPop = true
					let data = {}
					data.period = item.id //商品期数
					data.periods_type = item.periods_type //商品频道
					let res = await this.$u.api.packageDetail(data)
					let { purchased, limit_number, packageList } = res.data
					this.packageList = packageList.filter(v => {return v.is_hidden == 0}) //普通套餐列表
					this.limitInfo = { aleradyBuy: purchased, limitNumber: limit_number }
					this.packageIndex = this.$options.data().packageIndex
					this.packageInfo = packageList[this.packageIndex]
					this.purchaseNumbers = this.$options.data().purchaseNumbers
					this.feedback.hideLoading()
				}
			},
			
			// 选择套餐 index = 列表索引
			selectPackage(index){
				this.clickSelectPackage = 1
				this.packageIndex = index
				this.packageInfo = this.packageList[this.packageIndex]
			},
			
			// 加入购物车
			async addCar() {
				if(this.packageList.length == 0) return this.feedback.toast({title:'请选择套餐~'})
				try {
					const { id, periods_type } = this.goodsInfo
					let res = await this.$u.api.addShoppingCart({
						period: id, 
						package_id: this.packageInfo.id, 
						periods_type: periods_type, 
						nums: this.purchaseNumbers,
					})
					this.feedback.toast({title: '加入成功', icon: 'success'})
				}catch(e){
					console.log(e)
				}
			},
			
			// 监听页面滚动
			monitorScroll(){
				if( !this.isStore ) {
					uni.createSelectorQuery().in(this).select('.filter').boundingClientRect( res => {
						// this.filterScrollTop = res.top
						this.isFixedFilter = this.filterTop >= res.top
					}).exec()
				}
			},

			async onListLayoutChange () {
				await this.initGoodsListMinHeight()
				this.changeScroll()
				this.showWaterfallFlow = !this.showWaterfallFlow
				this.$u.api.reportBuryDot({
					data: [{
						channel: 4,
						genre: 3,
						region_id: 306000,
						button_id: 1
					}]
				})
			},

			jumpSecondHairSecond (item) {
				if (this.$ios && !judgeAppNewVersion('9.16')) {
					this.feedback.toast({ title: '请升级到最新版本' })
					return
				}
				this.$u.api.reportBuryDot({
					data: [{
						channel: 4,
						genre: 3,
						region_id: 302000,
						button_id: item.id
					}]
				})
				if (this.$app) {
					const data = JSON.stringify({ topId: item.id })
					wineYunJsBridge.openAppPage({
						client_path: {
							"ios_path": "MiaofaMoreViewController",
							"android_path": "com.stg.rouge.activity.MiaoFaSiftActivity"
						},
						ad_path_param: [
							{ 
								"ios_key": "topId", "ios_val": item.id,  
								"android_key":"data", "android_val": data
							}
						]
					})
					return
				}
				this.jump.navigateTo(`${this.routeTable.pBSecondHairSecond}?id=${item.id}`)
			},
			changeScroll () {
				if (this.isFixedFilter) {
					uni.createSelectorQuery().in(this).select('#goods-list-con').boundingClientRect(data=>{
						uni.createSelectorQuery().in(this).select('#outer-content').boundingClientRect(res=>{
							let scrollTop = data.top - res.top - uni.upx2px(104)
							if (this.isFromAuction) {
								scrollTop = scrollTop + uni.upx2px(92 + 20)
							} else {
								scrollTop = scrollTop - this.filterTop
							}
							this.system.pageScrollTo(scrollTop, 0)
						}).exec()
					}).exec()
				}
			},
			async initGoodsListMinHeight () {
				if (!this.goodsListMinHeight && this.isFixedFilter) {
					const getRect = (selector) => {
						return new Promise(resolve => {
							uni.createSelectorQuery().in(this).select(selector).boundingClientRect(rect => resolve(rect)).exec()
						})
					}
					const { height = 0 } = await getRect('#filter-tabs')
					const { screenHeight } = this.system.getSysInfo()
					this.goodsListMinHeight = screenHeight - height
				}
			},
			resetPage () {
				this.page = 1
				this.totalPage = 1
				this.$emit('pageChange', this.page)
				this.$emit('totalPageChange', this.totalPage)
			}
		},
		
		onPullDownRefresh() {
			this.page = 1
			if( this.isStore ) {
				this.mResetFilter()
				this.mGetStoreGoodsList()
			}else{
				this.initOnload()
			}
		},
		
		onPageScroll(res) {
			this.monitorScroll()
		},
		
		onReachBottom() {
			console.log('onReachBottom')
			if (this.page == this.totalPage || this.totalPage == 0 ) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getGoodsList()
		}
	}
</script>

<style>
	@import "../../common/css/page.css";
</style>

<style lang="scss" scoped>
</style>
