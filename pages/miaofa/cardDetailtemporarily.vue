<template>
  <view class="content">
    <!-- 导航栏 -->
    <vh-navbar>
      <view class="flex-sb-c pr-14 w-p100">
        <view class="flex-1 mr-22">
          <view
            class="flex-sb-c h-60 bg-f5f5f5 b-rad-30"
            @click="jump.navigateTo(`${routeTable.pFGlobalSearch}?type=4`)"
          >
            <text class="ml-24 font-24 text-9">大家都在搜</text>
            <view class="flex-c-c">
              <view class="w-02 h-36 bg-e4e4e4"></view>
              <button class="vh-btn flex-c-c w-100 h-60 font-wei-500 font-24 text-3 bg-transp">搜索</button>
            </view>
          </view>
        </view>
        <view class="p-rela d-flex" @click="openCar">
          <image class="wh-36 p-10" :src="ossIcon(`/second_hair/s_car.png`)" />
          <view
            v-if="shoppingCartNum"
            class="p-abso top-n-02 right-n-02 w-26 h-26 bg-e80404 b-rad-p50 flex-c-c font-16 text-ffffff"
            >{{ shoppingCartNum }}</view
          >
        </view>
      </view>
    </vh-navbar>

    <!-- 数据加载完成 -->
    <view style="overflow-x: hidden" class="">
      <view
        class="classify-con p-rela z-01 bg-ffffff d-flex o-hid"
        :style="{ height: classifyContainerRestHeight + 'px' }"
      >
        <!-- 分类 -->
        <scroll-view class="w-192 h-p100 bg-f5f5f5" :scroll-y="true">
          <view
            class="p-rela w-192 bg-ffffff"
            v-for="(item, index) in secondClassifyList"
            :key="index"
            @click="handleCategoryClick(index)"
          >
            <view
              class="tran-2 ptb-24-plr-20 w-192 d-flex j-center a-center"
              :class="
                secondClassifyIndex == index
                  ? 'bg-ffffff'
                  : secondClassifyIndex - 1 == index
                  ? 'bg-f5f5f5 b-br-rad-28'
                  : secondClassifyIndex + 1 == index
                  ? 'bg-f5f5f5 b-tr-rad-28'
                  : 'bg-f5f5f5'
              "
            >
              <view v-show="secondClassifyIndex == index" class="p-abso left-0 w-06 h-48 b-rad-04 bg-e80404" />
              <text
                class="font-28 text-center"
                style="min-height: 80rpx; line-height: 80rpx"
                :class="secondClassifyIndex == index ? 'font-wei text-e80404' : 'text-3'"
                >{{ item.name }}</text
              >
            </view>
          </view>
          <view class="w-192 h-128 bg-ffffff">
            <view
              class="wh-p100 bg-f5f5f5"
              style="height: 130px"
              :class="secondClassifyIndex + 1 === secondClassifyList.length ? 'b-tr-rad-28' : ''"
            ></view>
          </view>
        </scroll-view>
        <view class="flex-1 h-p100 d-flex flex-column">
          <view
            v-if="secondClassifyInfo.list && secondClassifyInfo.list.length"
            class="w-p100 bg-ffffff pt-12 pb-26 pl-24 pr-24"
          >
            <view class="d-flex flex-wrap ml-n-20">
              <view
                class="flex-c-c w-88 h-38 bg-f6f6f6 mt-20 ml-20 ptb-02-plr-20 b-rad-08 font-24 text-6"
                :class="secondClassifySubIndex == -1 ? 'bg-fce4e3 text-ed2317' : ''"
                @click="selectSecondClassifySubIndex(-1)"
                >全部</view
              >
              <view
                v-for="(item, index) in secondClassifyInfo.list.slice(0, max)"
                :key="index"
                @click="selectSecondClassifySubIndex(index)"
              >
                <view
                  class="flex-c-c h-38 bg-f6f6f6 mt-20 ml-20 ptb-00-plr-20 b-rad-08 font-24 text-6"
                  :class="index == secondClassifySubIndex ? 'bg-fce4e3 text-ed2317' : ''"
                  >{{ item }}</view
                >
              </view>

              <view v-if="secondClassifyInfo.list.length > count" class="flex-c-c w-112 h-38 mt-20" @click="onExpand">
                <text class="font-24 text-9">{{ isExpand ? '收起' : '展开' }}</text>
                <image
                  :src="ossIcon('/invoices/arrow_d_20_12.png')"
                  class="ml-04 w-20 h-12"
                  :class="isExpand ? 't-ro-n-180 tran-2' : 'tran-2'"
                />
              </view>
            </view>
          </view>
          <!-- 秒发商品列表 -->
          <view class="w-p100 flex-1 o-hid">
            <scroll-view
              class="h-p100"
              :scroll-y="true"
              @scrolltolower="onScrollToLower"
              @scroll="handleScroll"
              :scroll-top="scrollTop"
              :scroll-with-animation="true"
            >
              <view class="goods-list" style="padding-bottom: 120px">
                <!-- 商品列表 -->
                <view
                  v-for="(item, index) in goodsList"
                  :key="index"
                  class="bb-s-01-f0f0f0 ml-24 mr-24 pt-24 pb-24"
                  @click="jump.navigateTo(`/pages/goods-detail/goods-detail?id=${item.id}`)"
                >
                  <view class="d-flex">
                    <vh-image :src="item.product_img" :width="176" :height="176" :border-radius="6" />
                    <view class="flex-1 d-flex flex-column j-sb ml-12">
                      <view class="">
                        <view class="font-28 font-wei-700 text-3 text-hidden-3">{{ item.title }}</view>
                        <view class="font-24 mt-10">
                          <text class="text-6">已售</text>
                          <text class="text-e80404">{{ item.vest_purchased + item.purchased }}</text>
                        </view>
                      </view>
                      <view class="d-flex j-sb a-center">
                        <view
                          v-if="item.is_hidden_price == 1 || [3, 4].includes(item.onsale_status)"
                          class="font-32 font-wei text-e80404"
                          >价格保密</view
                        >
                        <view v-else class="">
                          <text class="font-36 font-wei text-e80404">
                            <text class="font-18">¥</text>{{ item.price }}
                          </text>
                        </view>
                        <image
                          class="w-52 h-52"
                          :src="ossIcon(`/second_hair_second/car_gray.png`)"
                          mode="aspectFill"
                          @click.stop="openPackPop(item)"
                        />
                      </view>
                    </view>
                  </view>
                </view>

                <!-- 加载状态 -->
                <view class="loading-status pt-20 pb-20 text-center font-24 text-9">
                  <text v-if="isLoading">加载中...</text>
                  <text v-else-if="!hasMore && goodsList.length > 0">没有更多了</text>
                  <text v-else-if="!hasMore && goodsList.length === 0">暂无数据</text>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>

        <view v-if="shoppingCartNum && shoppingCartMoney" class="p-fixed bottom-120 ptb-00-plr-32 w-p100">
          <view class="d-flex h-100 b-rad-100 o-hid" @click="jump.loginNavigateTo(routeTable.pBShoppingCart)">
            <view class="flex-1 d-flex a-center ptb-00-plr-48 bg-333333">
              <view class="p-rela d-flex">
                <image class="wh-36" :src="ossIcon(`/second_hair/s_car_white_36.png`)" />
                <view
                  v-if="shoppingCartNum"
                  class="p-abso top-n-16 right-n-14 w-26 h-26 bg-e80404 b-rad-p50 flex-c-c font-16 text-ffffff"
                  >{{ shoppingCartNum }}</view
                >
              </view>
              <view class="ml-36 mr-30 w-02 h-44 bg-666666"></view>
              <view class="text-ffffff font-36"><text class="font-26">¥</text>{{ shoppingCartMoney }}</view>
            </view>
            <view class="flex-c-c w-160 font-28 text-ffffff bg-e80404">去结算</view>
          </view>
        </view>
      </view>

      <!-- 弹框 -->
      <view class="">
        <!-- 筛选弹框 -->
        <u-popup
          v-model="showClassifyPop"
          mode="top"
          height="710"
          :border-radius="20"
          :z-index="979"
          :custom-style="{ top: system.navigationBarHeight() + 'px' }"
        >
          <view class="p-rela">
            <!-- 标题 -->
            <view class="p-stic top-0 bg-ffffff pt-32 pb-32 pl-40 font-32 text-3">全部分类</view>

            <!-- 分类 -->
            <view class="d-flex flex-wrap ml-10 mr-10 pb-104">
              <view
                class="d-flex flex-column j-center a-center mb-40 ml-30 mr-28"
                v-for="(item, index) in firstClassifyList"
                :key="index"
                @click="selectFirstClassifyId(item.id)"
              >
                <vh-image :loading-type="2" :src="item.image" :width="88" :height="88" :border-radius="20" />
                <view
                  class="tran-2 mt-10 font-22 l-h-32"
                  :class="firstClassifyId == item.id ? 'text-ff9127' : 'text-3'"
                  >{{ item.second_name }}</view
                >
              </view>
            </view>

            <!-- 底部按钮 -->
            <view
              class="p-fixed bottom-0 z-100 bg-ffffff w-p100 h-104 d-flex j-center a-center b-sh-00021200-022"
              @click="showClassifyPop = false"
            >
              <text class="mr-12 font-28 text-6">点击收起</text>
              <u-icon name="arrow-up-fill" :size="10" color="#666" />
            </view>
          </view>
        </u-popup>

        <!-- 套餐弹框 -->
        <u-popup v-model="showGoodsPackPop" mode="bottom" :duration="150" :border-radius="20">
          <view class="pt-32 pr-24 pb-48 pl-24">
            <view class="d-flex">
              <vh-image
                :loading-type="2"
                :src="secondHairGoodsInfo.product_img"
                :width="152"
                :height="152"
                bg-color="#F9F9F9"
                :border-radius="6"
              />

              <view class="d-flex flex-1 flex-column j-sb ml-16">
                <view class="font-28 text-3 l-h-40 o-hid text-hidden-2">{{ secondHairGoodsInfo.title }}</view>
                <view class="d-flex a-center mt-12">
                  <text
                    v-if="secondHairGoodsInfo.is_hidden_price || [3, 4].includes(secondHairGoodsInfo.onsale_status)"
                    class="font-44 font-wei text-e80404"
                    >价格保密</text
                  >
                  <view v-else class="d-flex a-start">
                    <view class="h-60 font-44 font-wei text-e80404 l-h-60"
                      ><text class="font-24">¥{{ ' ' }}</text
                      >{{ packageInfo.price }}</view
                    >
                  </view>
                </view>
              </view>
            </view>

            <view class="mt-48 font-32 font-wei-500 text-3 l-h-44">规格</view>

            <view class="d-flex flex-wrap ml-n-24">
              <!-- 普通商品套餐选项 -->
              <view v-for="(item, index) in packageList" :key="index">
                <view
                  class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3"
                  :class="
                    packageIndex != index
                      ? ''
                      : clickSelectPackage
                      ? 'skew-top bg-fce4e3 b-s-01-e80404 text-e80404'
                      : 'bg-fce4e3 b-s-01-e80404 text-e80404'
                  "
                  @click="selectPackage(index)"
                  >{{ item.package_name }}</view
                >
              </view>

              <!-- 拼团商品套餐选项 -->
              <!-- <view v-if="packageType == 1" v-for="(item, index) in groupPackageList" :key="index">
                                    <view class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3"
                                    :class="packageIndex != index ? '' : clickSelectPackage ? 'skew-top bg-fce4e3 b-s-01-e80404 text-e80404' : 'bg-fce4e3 b-s-01-e80404 text-e80404'"
                                    @click="selectPackage(index, 1)">{{item.package_name}}</view>
                                </view> -->

              <!-- 新人套餐选项 -->
              <!-- <view v-if="packageType == 2" v-for="(item, index) in newPeoplePackList" :key="index">
                                    <view class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3"
                                    :class="packageIndex != index ? '' : clickSelectPackage ? 'skew-top bg-fce4e3 b-s-01-e80404 text-e80404' : 'bg-fce4e3 b-s-01-e80404 text-e80404'"
                                    @click="selectPackage(index, 2)">{{item.package_name}}</view>
                                </view> -->
            </view>

            <view class="d-flex j-sb a-center mt-52">
              <view class="font-32 font-wei-500 text-3 l-h-44">数量</view>
              <view class="unumberbox">
                <u-number-box v-model="purchaseNumbers" :min="1" :input-width="64" :input-height="50" :size="28" />
              </view>
            </view>

            <view class="mt-92 d-flex j-center a-center">
              <u-button
                shape="circle"
                :hair-line="false"
                :ripple="true"
                ripple-bg-color="#FFF"
                :custom-style="{
                  width: '646rpx',
                  height: '64rpx',
                  fontSize: '28rpx',
                  fontWeight: 'bold',
                  color: '#FFF',
                  backgroundColor: '#E80404',
                  border: 'none',
                }"
                @click="addCar"
                >确定</u-button
              >
            </view>
          </view>
        </u-popup>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import navMsgMixin from '@/common/js/mixins/navMsgMixin.js'

export default {
  name: 'second-hair-second',
  mixins: [navMsgMixin],
  data() {
    return {
      isGetChannelKeyword: false,
      isGetMessageUnreadNum: false,
      // loading: false, //加载状态 true = 加载中、false = 结束加载
      osip: 'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
      classifyContainerRestHeight: 0, //分类容器剩余高度
      firstClassifyId: 0, //一级分类id
      firstClassifyList: [], //一级分类列表
      hairTitle: '',
      secondClassifyIndex: 0, //二级分类索引
      secondClassifyList: [], //二级分类列表
      secondClassifyInfo: {}, //二级分类信息
      secondClassifySubIndex: -1, //二级分类辅助索引
      secondHairList: [], //秒发二级页面列表
      shoppingCartMoney: 0,
      secondHairGoodsInfo: {}, //秒发商品信息
      packageIndex: 0, //选中的套餐索引
      clickSelectPackage: 0, //是否点击显示套餐 0 = 未点击、1 = 点击
      packageInfo: {}, //套餐信息
      showClassifyPop: false, //是否展示分类弹框
      showGoodsPackPop: false, //是否展示套餐弹框
      packageList: [], //普通套餐列表
      packageInfo: {}, //套餐信息
      limitInfo: {}, //限购信息
      purchaseNumbers: 1, //购买数量

      page: 1,
      limit: 10,
      goodsList: [], // 商品列表
      isLoading: false, // 是否正在加载
      hasMore: true, // 是否还有更多数据

      //xxx
      cid: '',
      type: 'card',
      scrollIntoView: '',
      scrollDirection: 'down', // 新增:用于记录滚动方向
      lastScrollTop: 0, // 新增:用于记录上次滚动位置
      isUserScrolling: false,
      lastClickTime: 0,
      scrollTop: 0,
      scrollOffset: 100,
      sectionPositions: [],
      scrollThreshold: 50, // 新增：滚动阈值
    }
  },

  onLoad(options) {
    if (options.cid) this.cid = parseInt(options.cid)
    this.type = options.type
    this.init()
  },
  onShow() {
    this.getShoppingCartMoney()
  },

  onReady() {
    this.getRestHeight()
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable']),
  },

  mounted() {
    this.$nextTick(() => {
      this.calculateSectionPositions()
    })
  },

  watch: {
    secondHairList: {
      handler() {
        this.$nextTick(() => {
          this.calculateSectionPositions()
        })
      },
      deep: true,
    },
  },

  methods: {
    // 获取系统剩余高度
    scrollToSection(index) {
      console.log('Scrolling to section:', index)
      console.log('Section positions:', this.sectionPositions)
      if (this.sectionPositions && this.sectionPositions.length > index && this.sectionPositions[index] !== undefined) {
        this.scrollTop = Math.max(0, this.sectionPositions[index] - this.scrollOffset)
      } else {
        console.warn('Invalid section index or section positions not calculated yet')
      }
    },
    handleScroll(e) {
      //   const scrollTop = e.detail.scrollTop
      //   const currentTime = Date.now()
      //   // 如果是用户最近点击触发的滚动，不处理
      //   if (currentTime - this.lastClickTime < 500) {
      //     return
      //   }
      //   this.isUserScrolling = true
      //   // 找出当前滚动位置对应的分类
      //   let activeIndex = this.findActiveIndex(scrollTop)
      //   // 更新左侧菜单选中状态
      //   if (this.isUserScrolling && activeIndex !== this.secondClassifyIndex) {
      //     console.log('Updating active index to:', activeIndex)
      //     this.secondClassifyIndex = activeIndex
      //   }
    },

    findActiveIndex(scrollTop) {
      const adjustedScrollTop = scrollTop + this.scrollOffset
      let activeIndex = 0

      for (let i = 0; i < this.sectionPositions.length; i++) {
        if (adjustedScrollTop >= this.sectionPositions[i] - this.scrollThreshold) {
          activeIndex = i
        } else {
          break
        }
      }

      // 处理滚动到底部的情况
      if (adjustedScrollTop + this.scrollThreshold >= this.sectionPositions[this.sectionPositions.length - 1]) {
        activeIndex = this.sectionPositions.length - 1
      }

      return activeIndex
    },

    getRestHeight() {
      uni.getSystemInfo({
        success: (res1) => {
          uni
            .createSelectorQuery()
            .in(this)
            .select('.classify-con')
            .boundingClientRect((res2) => {
              this.classifyContainerRestHeight = res1.windowHeight - res2.top
            })
            .exec()
        },
      })
    },

    // 初始化
    async init() {
      //   await this.getFirstClassifyList()
      await this.getSecondClassifyList()
      //   await this.getSecondHairList()
      // this.loading = false
    },

    // 获取一级分类列表
    async getFirstClassifyList() {
      let res = await this.$u.api.secondGoldAreaList()
      this.firstClassifyList = res.data.list
      if (this.firstClassifyList.length) {
        if (this.firstClassifyId == 0) this.firstClassifyId = this.firstClassifyList[0]?.id
      }
    },

    // 择一级分类id id = 一级分类列表id
    selectFirstClassifyId(id) {
      if (this.firstClassifyId == id) return
      this.firstClassifyId = id
      this.getSecondClassifyList()
      this.showClassifyPop = false
    },
    openCar() {
      if (this.login.isLogin(this.$vhFrom)) {
        this.jump.loginNavigateTo(`${this.routeTable.pBShoppingCart}`)
      }
    },
    // 获取二级分类列表
    async getSecondClassifyList() {
      const res = await this.$u.api.cardGetfilter({ cid: this.cid })
      console.log(res.data)
      this.secondClassifyList = res.data || []
      if (this.secondClassifyList.length > 0) {
        // 默认选中第一条
        this.secondClassifyIndex = 0
        // 重置分页并获取商品列表
        this.resetPagination()
        await this.getGoodsList(this.secondClassifyList[0])
      }
    },

    // 选择二级分类信息 index = 二级分类列表索引
    async selectSecondClassifyInfo(index) {
      this.secondClassifyIndex = index
      const secondClassifyInfo = this.secondClassifyList[index]
      if (secondClassifyInfo) {
        this.secondClassifyInfo = secondClassifyInfo
        // 重置分页并获取商品列表
        this.resetPagination()
        await this.getGoodsList(secondClassifyInfo)
      }
    },

    // 重置分页相关数据
    resetPagination() {
      this.page = 1
      this.goodsList = []
      this.hasMore = true
      this.isLoading = false
    },

    // 获取商品列表
    async getGoodsList(classifyInfo) {
      if (this.isLoading || !this.hasMore) return

      this.isLoading = true
      this.feedback.loading()

      try {
        const res = await this.$u.api.getfiltergoodslist({
          cid: this.cid,
          filter_id: classifyInfo.id,
          page: this.page,
          limit: this.limit,
        })

        const list = res.data.list || []
        const total = res.data.total || 0

        // 如果是第一页，直接替换列表，否则追加到列表
        if (this.page === 1) {
          this.goodsList = list
        } else {
          this.goodsList = [...this.goodsList, ...list]
        }

        // 判断是否还有更多数据
        this.hasMore = this.goodsList.length < total

        // 页码加1
        if (this.hasMore) {
          this.page++
        }
      } catch (error) {
        console.error('获取商品列表失败:', error)
      } finally {
        this.isLoading = false
        this.feedback.hideLoading()
      }
    },

    // 选择二级分类辅助分类索引 index = 二级辅助分类列表 -1 = 全部、其他 = 二级辅助分类列表索引
    selectSecondClassifySubIndex(index) {
      this.secondClassifySubIndex = index
      this.page = 1
      this.totalPage = 1
      this.getSecondHairList()
    },

    // 获取秒发列表
    async getSecondHairList() {
      this.feedback.loading()
      let data = {}
      data.second_id = this.firstClassifyId
      data.page = this.page
      data.limit = this.limit
      if (this.secondClassifySubIndex > -1) {
        let { key, list } = this.secondClassifyInfo
        data.key = key
        data.value = list[this.secondClassifySubIndex]
      }
      let res = await this.$u.api.secondHairSonPageList(data)
      let { total, list } = res.data
      this.page == 1 ? (this.secondHairList = list) : (this.secondHairList = [...this.secondHairList, ...list])
      this.totalPage = Math.ceil(total / this.limit)
      this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
      this.feedback.hideLoading()
    },

    // 打开套餐弹框 item = 秒发列表某一项
    async openPackPop(item) {
      if (this.login.isLogin(this.$vhFrom)) {
        this.feedback.loading()
        this.packageIndex = 0
        this.secondHairGoodsInfo = item
        let data = {}
        data.period = item.id //商品期数
        data.periods_type = item.periods_type //商品频道
        let res = await this.$u.api.packageDetail(data)
        let { purchased, limit_number, packageList } = res.data
        this.packageList = packageList.filter((v) => {
          return v.is_hidden == 0
        }) //普通套餐列表
        this.limitInfo = { aleradyBuy: purchased, limitNumber: limit_number }
        this.packageInfo = packageList[this.packageIndex]
        this.showGoodsPackPop = true
        this.purchaseNumbers = 1
      }
    },

    // 选择套餐 index = 列表索引
    selectPackage(index) {
      this.clickSelectPackage = 1
      this.packageIndex = index
      this.packageInfo = this.packageList[this.packageIndex]
    },

    // 加入购物车
    async addCar() {
      if (this.packageList.length == 0) return this.feedback.toast({ title: '请选择套餐~' })
      try {
        const { id, periods_type } = this.secondHairGoodsInfo
        let res = await this.$u.api.addShoppingCart({
          period: id,
          package_id: this.packageInfo.id,
          periods_type: periods_type,
          nums: this.purchaseNumbers,
        })
        this.feedback.toast({ title: '加入成功', icon: 'success' })
        this.showGoodsPackPop = false

        // 更新购物车数量和金额
        if (!this.shoppingCartNum) {
          this.shoppingCartNum = 0
        }
        this.shoppingCartNum += this.purchaseNumbers

        // 重新计算购物车金额
        await this.getShoppingCartMoney()
      } catch (e) {
        console.log(e)
      }
    },

    // scroll-到底刷新
    scrollEnd() {
      console.log('---------到底啦')
      //   if (this.loadStatus !== 'loadmore') return
      //   this.loadStatus = 'loading'
      //   if (this.secondClassifyList.length - 1 !== this.secondClassifyIndex) {
      //     this.secondClassifyIndex++
      //     this.secondHairList = this.secondClassifyList[this.secondClassifyIndex].goods
      //     this.hairTitle = this.secondClassifyList[this.secondClassifyIndex].name
      //   }

      //   this.page++
      //   this.getSecondHairList()
    },
    getShoppingCartMoney() {
      // Check if user is logged in before proceeding
      if (!this.login.isLogin(this.$vhFrom, 0)) return
      this.$u.api.shoppingCartMoneyCalclute().then((res) => {
        const { total_money = 0 } = res?.data || {}
        this.shoppingCartMoney = total_money
      })
    },
    onExpand() {
      this.isExpand = !this.isExpand
      this.max = this.isExpand ? this.secondClassifyInfo.list.length : this.$options.data().max
    },
    async handleCategoryClick(index) {
      if (this.secondClassifyIndex === index) return

      console.log('Category clicked:', index)
      this.secondClassifyIndex = index
      const secondClassifyInfo = this.secondClassifyList[index]
      if (secondClassifyInfo) {
        // 重置分页并获取商品列表
        this.resetPagination()
        await this.getGoodsList(secondClassifyInfo)
      }
    },
    calculateSectionPositions() {
      console.log('Calculating section positions')
      const query = uni.createSelectorQuery().in(this)
      this.secondHairList.forEach((_, index) => {
        query.select(`#section-${index}`).boundingClientRect()
      })
      query.exec((rects) => {
        this.sectionPositions = rects.filter((rect) => rect).map((rect) => rect.top)
        console.log('Section positions:', this.sectionPositions)
      })
    },
    // 滚动到底部触发
    async onScrollToLower() {
      console.log('---------到底啦')
      if (this.secondClassifyList.length > 0) {
        const currentClassify = this.secondClassifyList[this.secondClassifyIndex]
        await this.getGoodsList(currentClassify)
      }
    },
  },
}
</script>

<style scoped>
/* 一级分类 */
.fir-cla-bg {
  background-image: url(https://images.vinehoo.com/vinehoomini/v3/second_hair_second/cla_bg.png);
  background-size: cover;
}
/* .fir-cla-list view:first-child{
            margin-left: 0;
        }
        .fir-cla-list view:last-child{
            margin-right: 0;
        } */
</style>

<style lang="scss" scoped>
.unumberbox {
  ::v-deep {
    .u-numberbox {
      border: 1px solid #eeeeee;
      border-radius: 12rpx;
    }

    .u-number-input {
      margin: 0;
      padding: 0 3px;
    }

    .u-icon-minus,
    .u-icon-plus {
      width: 50rpx !important;
      background-color: #fff !important;

      &.u-icon-disabled {
        .uicon-minus,
        .uicon-plus {
          color: #ddd !important;
        }
      }
    }

    .uicon-minus,
    .uicon-plus {
      font-size: 24rpx !important;
      color: #666 !important;
    }
  }
}
.hair-title {
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  margin-left: 24rpx;
  padding-top: 40rpx;
  line-height: 44rpx;
  text-align: left;
  font-style: normal;
}
</style>
