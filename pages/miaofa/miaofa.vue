<template>
  <view :class="newYearTheme ? 'bg-B21605' : ''">
    <scroll-view
      scroll-y
      @scroll="handleGoodsScroll"
      style="height: 100vh"
      :scroll-top="scrollTop"
      @scrolltolower="handleScrollToLower"
    >
      <vh-navbar height="46" :is-back="false" :newYearTheme="newYearTheme">
        <view class="p-rela pl-24 pr-24 w-p100" :class="isShowStoreTabs ? 'flex-e-c' : 'flex-sb-c'">
          <view class="flex-1">
            <view
              class="flex-sb-c h-60 bg-f5f5f5 b-rad-30"
              @click="jump.navigateTo(`${routeTable.pFGlobalSearch}?type=2`)"
            >
              <text class="ml-24 font-24 text-9">{{ channelKeyword }}</text>
              <view class="d-flex a-center"
                ><view style="width: 2rpx; height: 36rpx; background: #e4e4e4"></view>
                <button
                  class="vh-btn bg-f5f5f5 flex-c-c w-96 h-60 font-24 font-wei-450 text-333 search-miaofa b-rad-30"
                  :class="{ 'text-e80404': newYearTheme }"
                >
                  搜索
                </button></view
              >
            </view>
          </view>

          <view class="flex-c-c ml-32">
            <view class="flex-c-c mr-26 wh-36">
              <view class="p-rela d-flex" @click="jump.loginNavigateTo(`${routeTable.pBShoppingCart}`)">
                <image class="wh-36 p-10" v-if="newYearTheme" :src="`${osip}/theme/newyear/nav-car.png`" />
                <image class="wh-36 p-10" v-else :src="ossIcon(`/second_hair/s_car_36.png`)" />
                <view
                  v-if="shoppingCartNum"
                  class="p-abso top-n-02 right-n-02 w-26 h-26 bg-e80404 b-rad-p50 flex-c-c font-16 text-ffffff"
                  >{{ shoppingCartNum }}</view
                >
              </view>
            </view>
            <view class="flex-c-c wh-36" @click="jump.loginNavigateTo(`${routeTable.pEMessageCenter}`)">
              <view class="p-rela d-flex">
                <image class="w-36 h-38 p-10" v-if="newYearTheme" :src="`${osip}/theme/newyear/nav-message.png`" />
                <image class="w-36 h-38 p-10" v-else :src="ossIcon(`/second_hair/s_not_36_38.png`)" />
                <view v-if="unReadTotalNum" class="p-abso top-10 right-10 wh-10 bg-e80404 b-rad-p50" />
              </view>
            </view>
          </view>
        </view>
      </vh-navbar>
      <view
        :class="
          newYearTheme && goldAreaList.length && goldAreaCols.length ? 'new-year-glodarea-bg  ptb-18-plr-24 ' : ''
        "
      >
        <view
          v-if="goldAreaList.length && goldAreaCols.length"
          style="overflow: hidden; white-space: nowrap"
          class="d-flex j-sb pt-20 pb-20 bg-ffffff"
          :class="[
            goldAreaCols.length ? 'ptb-00-plr-30' : 'ptb-00-plr-40',
            newYearTheme ? 'new-year-glodarea b-rad-24' : '',
          ]"
        >
          <view v-for="(list, colIndex) in goldAreaCols" :key="colIndex">
            <view
              v-for="(item, index) in list"
              :key="index"
              class="flex-c-c flex-column"
              :class="[index ? 'mt-20' : '']"
              @click="JumpColumn(item)"
            >
              <vh-image :loading-type="2" :src="item.icon" :width="92" :height="92" border-radius="12rpx" />
              <view class="mt-14 font-22 l-h-32 font-wei-450 text-6" :class="newYearTheme ? 'text-e80404' : ''">{{
                item.name
              }}</view>
            </view>
          </view>
        </view>
      </view>
      <view v-for="(item, index) in cardList" :key="index" @click="jumpCardDetail(item)">
        <view
          class="bg-ffffff miaofa-card b-rad-12"
          :class="newYearTheme ? 'new-year-card-bg b-rad-24' : ''"
          v-if="item.card_extend_detail.length"
        >
          <view :class="newYearTheme ? 'card-title-newYear w-p80' : ''" class="card-title">{{ item.card_name }} </view>
          <view class="card-price" :class="newYearTheme ? 'card-title-newYear' : ''"
            ><text :class="newYearTheme ? 'card-title-newYear' : ''" class="price-unit">¥</text>{{ item.min_price }}-{{
              item.max_price
            }}
          </view>
          <view class="d-flex j-sb">
            <vh-image
              :isMf="true"
              :borderRadius="10"
              v-for="(child, imageIndex) in item.card_extend_detail"
              :width="210"
              :height="210"
              :key="imageIndex"
              :src="child.product_img"
              :background-image="
                newYearTheme
                  ? 'http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/goods-bg.png'
                  : ''
              "
            >
            </vh-image>
          </view>
        </view>
      </view>
      <view class="p-stic z-978" style="top: 46px">
        <view class="d-flex j-sb a-center ptb-20-plr-44" :class="newYearTheme ? 'bg-B21605' : 'bg-f7f7f7'">
          <view
            class="font-28"
            :class="
              newYearTheme
                ? filterIndex == 0
                  ? 'text-ff9127 font-wei'
                  : 'text-ffffff'
                : filterIndex == 0
                ? 'text-e80404 font-wei'
                : 'text-6'
            "
            @click="changeSort(0)"
            >默认</view
          >
          <view class="d-flex a-center" v-for="(item, index) in sortList" :key="index" @click="changeSort(item.index)">
            <text
              class="font-28"
              :class="
                newYearTheme
                  ? filterIndex == item.index
                    ? 'text-ff9127 font-wei'
                    : 'text-ffffff'
                  : filterIndex == item.index
                  ? 'text-e80404 font-wei'
                  : 'text-6'
              "
              >{{ item.name }}</text
            >
            <view v-if="item.index === 1 && !newYearTheme">
              <image
                class="ml-04 w-14 h-14"
                v-if="sortType == item.type"
                :src="`${osip}/flash_purchase/sort_${sort}.png`"
                mode="aspectFill"
              />

              <image class="ml-04 w-14 h-14" v-else :src="`${osip}/flash_purchase/sort.png`" mode="aspectFill" />
            </view>
            <view v-if="item.index === 1 && newYearTheme">
              <image
                class="ml-04 w-14 h-14"
                v-if="sortType == item.type"
                :src="`${osip}/theme/newyear/sort_${sort}.png`"
                mode="aspectFill"
              />

              <image class="ml-04 w-14 h-14" v-else :src="`${osip}/theme/newyear/sort.png`" mode="aspectFill" />
            </view>
          </view>
          <view class="flex-shrink flex-c-c" @click="getFilterList">
            <view class="w-02 h-28" :class="newYearTheme ? 'bg-ffffff' : 'bg-cccccc'"></view>
            <view
              class="ptb-00-plr-16 font-28 pl-40 l-h-40"
              v-if="newYearTheme"
              :class="doFilter ? 'text-ff9127' : 'text-ffffff'"
              >筛选</view
            >
            <view class="ptb-00-plr-16 font-28 pl-40 l-h-40" v-else :class="doFilter ? 'text-e80404' : 'text-6'"
              >筛选</view
            >
            <image
              v-if="newYearTheme"
              :src="`${osip}/theme/newyear/funnel${doFilter ? '_sel' : ''}.png`"
              class="w-28 h-28"
            />
            <image v-else :src="ossIcon(`/comm/funnel${doFilter ? '_sel' : ''}.png`)" class="w-28 h-28" />
          </view>
        </view>
      </view>
      <view v-if="goodsList.length === 0" style="padding: 31vh 0">
        <vh-empty
          bgColor="transparent"
          :padding-top="0"
          :padding-bottom="0"
          :image-src="ossIcon('/empty/emp_goods.png')"
          text="暂无数据"
          :text-bottom="0"
        />
      </view>
      <view v-else class="ptb-00-plr-24">
        <view
          v-for="(item, index) in goodsList"
          :key="index"
          :id="'goods_' + index"
          :class="newYearTheme ? 'new-goods-list-bg ' : ''"
          class="bg-ffffff p-20 d-flex mb-20 b-rad-12"
          @click="handleJump(`${routeTable.pgGoodsDetail}?id=${item.id}`, '')"
        >
          <view class="miaofa-product-image">
            <vh-image
              :background-image="
                newYearTheme
                  ? 'http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/goods-bg.png'
                  : ''
              "
              :width="210"
              :height="210"
              :borderRadius="10"
              :src="item.product_img"
            ></vh-image>
          </view>
          <view class="d-flex flex-column j-sb ml-20 w-p100">
            <view>
              <view class="product-title">{{ item.title }} </view>
              <view class="product-brief">{{ item.brief }}</view>
            </view>
            <view class="miaofa-product-bottom d-flex a-center j-sb">
              <view class="font-wei-500 text-e80404"
                ><text class="font-24">¥</text><text class="font-36">{{ item.price }}</text></view
              >
              <view class="font-24" style="color: #999"
                >已售<text style="color: #e80404">{{ item.vest_purchased + item.purchased }}</text></view
              >
            </view>
          </view>
        </view>
      </view>
      <u-loadmore :status="loadStatus" v-if="goodsList.length !== 0" />
      <view class="">
        <!-- 筛选弹框 -->
        <u-popup v-model="showScreenPop" :safe-area-inset-bottom="true" mode="bottom" :border-radius="20">
          <view :style="[{ maxHeight: screenPopMaxHeight }]" class="p-rela">
            <!-- 标题 -->
            <view class="p-fixed top-0 z-100 w-p100 bg-ffffff d-flex j-sb a-center ptb-32-plr-30">
              <view class=""></view>
              <view class="text-center font-32 font-wei text-0">全部筛选</view>
              <image
                class="w-44 h-44"
                :src="`${osip}/flash_purchase/clo_gray.png`"
                mode="widthFix"
                @click.stop="showScreenPop = false"
              ></image>
            </view>

            <!-- 拥有筛选数据 -->
            <view class="fade-in">
              <view class="pt-100 mt-10 pr-28 pl-28">
                <view class="font-32 font-wei text-3">价格区间（元）</view>
                <view class="d-flex j-sb a-center mt-20">
                  <input
                    v-model="minPrice"
                    class="bg-f6f6f6 w-308 h-70 text-center text-3"
                    :class="[false ? 'font-28 b-rad-08' : 'font-26 b-rad-36']"
                    type="number"
                    placeholder="最低价"
                    :placeholder-style="false ? 'color: #999; font-size: 28rpx;' : 'color: #999; font-size: 26rpx;'"
                  />
                  <view class="bg-999999 w-32 h-02"></view>
                  <input
                    v-model="maxPrice"
                    class="bg-f6f6f6 w-308 h-70 text-center text-3"
                    :class="[false ? 'font-28 b-rad-08' : 'font-26 b-rad-36']"
                    type="number"
                    placeholder="最高价"
                    :placeholder-style="false ? 'color: #999; font-size: 28rpx;' : 'color: #999; font-size:26rpx;'"
                  />
                </view>
              </view>
              <!-- 筛选内容（关键词、类型、国家、价格区间） -->
              <view class="pt-60 pr-28 pb-40 pl-28">
                <view
                  v-for="(row, index) in [
                    { title: '关键词', list: keywordList, typeName: 'product_keyword', typeId: 'keywordId' },
                    { title: '类型', list: wineTypeList, typeName: 'product_category', typeId: 'categoryId' },
                    { title: '国家', list: countryList, typeName: 'country', typeId: 'countryId' },
                  ]
                    .concat(
                      false ? [{ title: '产区', list: regionsList, typeName: 'regions', typeId: 'regionsId' }] : []
                    )
                    .filter((item) => item.list.length)"
                  :key="row.title"
                  :class="[index ? 'mt-60' : 'mt-02']"
                >
                  <view class="font-32 font-wei text-3">{{ row.title }}</view>
                  <view class="d-flex j-sb flex-wrap">
                    <view
                      v-for="item in row.list"
                      :key="item.id"
                      class="bg-f6f6f6 w-216 h-70 d-flex j-center a-center mt-20 font-26 text-3"
                      :class="[
                        false ? 'b-rad-08' : 'b-rad-36',
                        _data[row.typeId] == item.id
                          ? false
                            ? 'bg-liquor-filter-item'
                            : 'fade-in bg-fce0e0 b-s-01-e80404 text-e04040'
                          : false
                          ? ''
                          : 'fade-in',
                      ]"
                      @click="selectFilterItem(row.typeName, row.typeId, item)"
                      >{{ item.name }}
                    </view>
                    <view class="w-216"></view>
                    <view class="w-216"></view>
                  </view>
                </view>

                <!-- 价格区间 -->
              </view>

              <!-- 底部按钮 -->
              <view
                class="p-stic bottom-0 z-999 bg-feffff w-p100 h-104 d-flex j-sa a-center b-sh-00021200-022 ptb-00-plr-28"
              >
                <button class="vh-btn flex-c-c w-308 h-64 font-28 text-9 bg-eeeeee b-rad-32" @click="resetFilter">
                  重置
                </button>
                <button
                  class="vh-btn flex-c-c w-308 h-64 font-28 b-rad-32"
                  :class="[false ? 'text-ff9500 bg-0c0001' : 'text-ffffff bg-e80404']"
                  @click="confirmFilter"
                >
                  确定
                </button>
              </view>
            </view>
          </view>
        </u-popup>
      </view>
      <vh-tabbar :loading="initLoading" @topRefresh="topRefresh" @recordTabbarPages="recordPages" />
    </scroll-view>
  </view>
</template>

<script>
import navMsgMixin from '@/common/js/mixins/navMsgMixin.js'
import listMixin from '@/common/js/mixins/listMixin'
import newPeopleMixin from '@/common/js/mixins/newPeopleMixin'
import userMixin from '@/common/js/mixins/userMixin'
import topRefreshMixin from '@/common/js/mixins/topRefreshMixin'
import startupPageOptionsMixin from '@/common/js/mixins/startupPageOptionsMixin'
import { mapState, mapMutations, mapActions } from 'vuex'
import { MSecondsWfitemType, MSWfitemTypeToIncParamsKey, MSWfitemIncRes } from '@/common/js/utils/mapperModel'
import { STORE_URL } from '@/common/js/fun/constant'
import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'
const screenWidth = uni.getSystemInfoSync()?.screenWidth || 0
const $isBigScreen = screenWidth >= 390
console.log('$isBigScreen', screenWidth, $isBigScreen)
const WfitemWidth = 344
const WfitemMarginRight = `${screenWidth - uni.upx2px(WfitemWidth) * 2 - uni.upx2px(24) * 2}px`
console.log('WfitemMarginRight', WfitemMarginRight)
const WfitemCustom = {
  $wfitemIdKey: '$wfitemId',
  $isRecommended: false,
  $wfitemWidth: WfitemWidth,
  $imgIsResize: true,
  $isBigScreen,
  $reportDisabled: true,
}

export default {
  mixins: [
    navMsgMixin,
    listMixin,
    newPeopleMixin,
    userMixin,
    topRefreshMixin,
    startupPageOptionsMixin,
    longpressCopyMixin,
  ],
  data: () => ({
    query: {
      uuid: uni.getStorageSync('uniqueId') || '',
      identifier: '',
    },
    initLoading: true,
    osip: 'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
    renderLoading: true,
    wfitemStyle: { width: WfitemWidth, marginRight: WfitemMarginRight, marginBottom: 16 },
    isShowStoreTabs: false,
    storeInfo: null,
    filterList: [],
    newYearTheme: false,
    secStoreRangeMaskVisible: false,
    pendInsertId: '',
    wineTypeList: [], //酒类型列表
    categoryId: '', //酒类型id
    product_category: [0], //酒类型名称 形如 ['干白']
    countryList: [], //国家列表
    countryId: '', //国家id
    country: [0], //国家名称 形如 ['英国']
    regionsList: [],
    regionsId: '',
    regions: [0],
    keywordList: [], //关键字列表
    keywordId: '', //关键字id
    product_keyword: [0], //关键字名称 形如 ['气泡']
    minPrice: '', //最低金额
    maxPrice: '', //最高金额
    doFilter: 0, //是否做过筛选 0 = 未做筛选、1 = 做了筛选
    filterIndex: 0, //筛选索引
    visibleGoods: [],
    filterInfo: {}, //过滤信息
    isEmpty: false,
    page: 1,
    limit: 8,
    totalPage: 0,
    scrollLeft: 0,
    oldScrollLeft: 0,
    goldAreaList: [],
    cardList: [],
    filterIndex: 0,
    scrollTop: 0,
    sortType: 'sort', //需要排序的字段 上新 = onsale_time、价格 = price、销量 = purchased
    sort: 'desc', //排序方式 asc = 升序、desc = 降序
    doFilter: 0,
    screenPopMaxHeight: 0,
    showScreenPop: false,
    countryList: [],
    wineTypeList: [],
    goodsList: [],
    keywordList: [],
    regionsList: [],
    loadStatus: 'loadmore', //底部加载状态
  }),
  mounted() {
    setInterval(() => {
      const existingData = uni.getStorageSync('visibleGoods') || []
      let newData = [...existingData, ...this.visibleGoods]
      uni.setStorageSync('visibleGoods', newData)
      this.visibleGoods = []
    }, 3000)
  },
  computed: {
    ...mapState(['routeTable', 'dev']),
    ...mapState('startupPageOptions', ['miaofaStartupPageCount']),
    sortList() {
      return [
        { index: 1, name: '价格', type: 'price' },
        { index: 2, name: '上新', type: 'onsale_time' },
        { index: 3, name: '销量', type: 'purchased' },
      ]
      //   .concat(isKj ? [{ index: 4, name: '发货时间', type: 'predict_shipment_time' }] : [])
    },
    goldAreaCols({ goldAreaList }) {
      if (![4, 5, 8, 10].includes(goldAreaList.length)) return []
      switch (goldAreaList.length) {
        case 8:
          return goldAreaList.slice(0, 4).map((item, index) => [item, goldAreaList[index + 4]])
        case 10:
          return goldAreaList.slice(0, 5).map((item, index) => [item, goldAreaList[index + 5]])
        default:
          return goldAreaList.map((item) => [item])
      }
    },
    $wfitemIdKey() {
      return WfitemCustom.$wfitemIdKey
    },
  },
  methods: {
    ...mapActions('newcomerCoupon', ['initNewcomerCouponInfo']),
    async secondConfig() {
      const res = await this.$u.api.secondConfig()
      this.newYearTheme = res.data.isopen
    },
    handleScrollToLower() {
      console.log('触发了 scrolltolower 事件', this.page, this.totalPage)
      if (this.page < this.totalPage && this.loadStatus !== 'loading') {
        console.log('开始加载更多数据')
        this.loadStatus = 'loading'
        this.page++
        this.getGoodsList()
      } else {
        console.log('没有更多数据了 或 正在加载中')
      }
    },
    handleGoodsScroll(e) {
      // 处理导航栏背景色
      const scrollTop = e.detail.scrollTop
      if (scrollTop <= 100) {
        this.navBackgroundColor = `rgba(224, 20, 31, ${scrollTop / 100})`
      } else {
        this.navBackgroundColor = `rgba(224, 20, 31, 1)`
        // this.monitorScroll()
      }

      // 原有的曝光检测逻辑
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this)
        this.goodsList.forEach((item, index) => {
          query
            .select(`#goods_${index}`)
            .boundingClientRect((data) => {
              if (data && data.top <= window.innerHeight && data.bottom >= 0) {
                let uid = ''
                let device = ''
                const userinfo = uni.getStorageSync('loginInfo') || '{}'
                if (userinfo && userinfo.uid) {
                  uid = userinfo.uid
                } else {
                  uid = uni.getStorageSync('uniqueId')
                }
                if (this.$vhFrom == 'next') {
                  device = 'hm'
                } else if (this.$vhFrom == '1') {
                  device = 'android'
                } else if (this.$vhFrom == '2') {
                  device = 'ios'
                } else {
                  device = 'h5'
                }
                const goodsItem = {
                  uid: String(uid),
                  created_time: new Date().getTime(),
                  metric_name: 'period_exposure',
                  device,
                  period: Number(item.id),
                  period_type: Number(item.periods_type),
                }

                // 基于ID去重
                const exists = this.visibleGoods.some((v) => v.period === item.id)
                if (!exists) {
                  this.visibleGoods.push(goodsItem)
                }
              }
            })
            .exec()
        })
      })
    },
    async getFilterList() {
      try {
        this.feedback.loading()
        let res = null
        res = await this.$u.api.secondHairFilterList()

        const list = res.data.list
        const typeToList = list.reduce(
          (prev, curr) => ({
            ...prev,
            [curr.type]: [...(prev[curr.type] || []), curr],
          }),
          {}
        )
        const { 1: countryList = [], 2: wineTypeList = [], 3: keywordList = [], 5: regionsList = [] } = typeToList
        this.countryList = countryList
        this.wineTypeList = wineTypeList
        this.keywordList = keywordList
        this.regionsList = regionsList
        this.showScreenPop = true
      } catch (err) {
        this.feedback.hideLoading()
      }
    },
    async changeSort(index) {
      this.page = 1
      this.totalPage = 0
      switch (index) {
        case 0:
          this.filterIndex = index
          this.sortType = 'sort'
          this.sort = 'desc'
          this.getGoodsList()
          break
        case 1:
        case 2:
        case 3:
        case 4:
          this.sortType = this.sortList[index - 1].type
          if (index !== 1) {
            this.sort = 'desc'
          } else {
            if (this.filterIndex === index) {
              this.sort == 'desc' ? (this.sort = 'asc') : (this.sort = 'desc')
            } else {
              this.sort = 'asc'
            }
          }

          this.filterIndex = index
          await this.getGoodsList()
          break
      }
      //   this.scrollToTop()
    },
    scrollToTop() {
      this.$nextTick(() => {
        this.system.pageScrollTo(0, 0)
      })
    },
    jumpCardDetail(item) {
      console.log(item)
      let data = {
        channel: 4,
        genre: 3,
        region_id: 307000,
        button_id: item.id,
      }
      this.$u.api.reportBuryDot({
        data: [data],
      })
      this.handleJump(`${this.routeTable.pgMiaoFaCard}?cid=${item.id}&type=card`, '')
    },
    async getGoodsList() {
      console.log(this.page)
      this.feedback.loading()
      let data = {} // 接口需要上传的数据
      data.page = this.page //第几页
      data.limit = this.limit // 每页限制多少条
      data.periods_type = [1]
      data.sort_type = this.sortType //商品需要排序的字段
      data.order = this.sort //排序方式 asc = 升序、desc = 降序
      data.filters = this.filterInfo //过滤信息
      if (this.minPrice) data.price_gte = parseFloat(this.minPrice)
      if (this.maxPrice) data.price_lte = parseFloat(this.maxPrice)
      if (this.minPrice && this.maxPrice && data.price_gte > data.price_lte) {
        ;[data.price_gte, data.price_lte] = [data.price_lte, data.price_gte]
      }

      let res = await this.$u.api.flashGoodsList(data) // 获取mf商品列表（筛选）
      let { total, list } = res.data
      if (this.page == 1) {
        this.goodsList = list
      } else {
        this.goodsList = this.mergeUnique(this.goodsList, list)
      }
      uni.stopPullDownRefresh()
      this.totalPage = Math.ceil(total / this.limit)
      console.log(this.totalPage, total, this.limit)
      this.loadStatus = this.page == this.totalPage || !this.totalPage ? 'nomore' : 'loadmore'
      this.feedback.hideLoading()
    },
    mergeUnique(goodsList, list) {
      const map = new Map()
      // 添加现有商品到 Map
      goodsList.forEach((item) => {
        map.set(item.id, item)
      })
      // 添加新列表中的商品到 Map，如果已有相同 id 的商品，则会被跳过
      list.forEach((item) => {
        if (!map.has(item.id)) {
          map.set(item.id, item)
        }
      })
      // 将 Map 转换回数组
      return Array.from(map.values())
    },
    async confirmFilter() {
      if (this.categoryId || this.countryId || this.keywordId || this.regionsId || this.minPrice || this.maxPrice) {
        this.doFilter = 1
      } else {
        this.doFilter = 0
      }
      this.page = 1
      this.totalPage = 1
      await this.getGoodsList()
      this.showScreenPop = false
      //   this.scrollToTop()
    },

    getAggregateData() {
      this.goldAreaList = []
      this.$u.api.secondHomeCard({ client: 3 }).then((res) => {
        console.warn(res.data)
        const { column = [], card = [] } = res.data
        this.goldAreaList = column
        this.cardList = card
      })
    },
    init() {
      this.initLoading = true
      this.getAggregateData()
      this.getGoodsList()
      setTimeout(() => {
        this.initLoading = false
      }, 1000)
    },
    async resetFilter() {
      this.categoryId = ''
      this.product_category = [0]
      this.countryId = ''
      this.country = [0]
      this.keywordId = ''
      this.product_keyword = [0]
      this.regionsId = ''
      this.regions = [0]
      this.filterInfo = {}
      this.minPrice = ''
      this.maxPrice = ''
    },

    selectFilterItem(typeName, typeId, item) {
      console.log(typeName, typeId, item)
      if (this[typeId] == item.id) {
        this[typeName] = [0]
        this[typeId] = ''
        delete this.filterInfo[typeName]
      } else {
        this[typeName] = [item.name]
        this[typeId] = item.id
        this.filterInfo[typeName] = [item.name]
      }
    },
    JumpColumn(item) {
      let data = {
        channel: 4,
        genre: 3,
        region_id: 306000,
        button_id: item.id,
      }
      this.$u.api.reportBuryDot({
        data: [data],
      })
      console.log(item)
      if (!item.page_mode) {
        let path = ''
        if (!this.dev) {
          path = 'https://activity-cdn.vinehoo.com'
        } else {
          path = 'https://test-activity.wineyun.com'
        }
        this.jump.h5Jump(path + item.path, this.$vhFrom, 4)
      } else {
        this.handleJump(`${this.routeTable.pgMiaoFaCard}?cid=${item.id}&type=colum`, '')
      }
    },
    // 新版

    pullDownRefresh() {
      //   this.getNewPeopleCouponActivityInfo()
      this.init()
    },
  },
  onLoad() {
    this.init()
    const { windowHeight, screenHeight } = this.system.getSysInfo()
    this.screenPopMaxHeight = `${(windowHeight || screenHeight) * 0.8}px`
    // this.getStoreInfo()
    this.secondConfig()
  },
  onShow() {},
  onHide() {
    this?.$refs?.vhWaterfallRef?.stopRender()
  },
  onUnload() {
    this?.$refs?.vhWaterfallRef?.stopRender()
  },
  onPullDownRefresh() {
    if (this.initLoading) return
    this.pullDownRefresh()
  },
  onReachBottom() {
    if (this.page == this.totalPage || this.totalPage == 0) return
    this.loadStatus = 'loading'
    this.page++
    this.getGoodsList()
  },
}
</script>

<style scoped lang="scss">
page {
  font-family: 'PingFang SC', OpenSans, apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial,
    Roboto, 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  background: #f9f9f9;
}
.miaofa-card {
  width: 94%;
  margin: 0 auto;
  padding: 20rpx 22rpx;
  margin-top: 20rpx;
  .card-title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 700;
    font-size: 32rpx;
    color: #333333;
    text-align: left;
    font-style: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 显示两行 */
    -webkit-box-orient: vertical;
    line-height: 42rpx;
    max-height: 84rpx; /* 行高 * 行数 */
  }
  .card-price {
    margin: 20rpx 0 26rpx 0;
    height: 44rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 450;
    font-size: 42rpx;
    color: #e80404;
    line-height: 44rpx;
    text-align: left;
    font-style: normal;
    .price-unit {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 26rpx;
      color: #e80404;
      line-height: 44rpx;
      text-align: left;
      font-style: normal;
    }
  }
}
.product-title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  line-height: 34rpx;
  text-align: left;
  font-style: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 显示两行 */
  -webkit-box-orient: vertical;
  line-height: 34rpx; /* 根据你的字体和设计调整行高 */
  max-height: 68rpx;
}
.product-brief {
  font-weight: 400;
  font-size: 22rpx;
  color: #999999;
  margin-top: 4rpx;
  line-height: 32rpx;
  text-align: left;
  font-style: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1; /* 显示两行 */
  -webkit-box-orient: vertical;
  line-height: 32rpx; /* 根据你的字体和设计调整行高 */
  max-height: 32rpx;
}
.new-year-glodarea {
  background-image: url('http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-goldarea.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}
.new-year-glodarea-bg {
  background-image: url('http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-goldarea-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}
.new-year-card-bg {
  background-image: url('http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-card-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}
.new-goods-list-bg {
  background-image: url('http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/goods-list-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}
.bg-B21605 {
  background-color: #b21605;
}
.card-title-newYear {
  color: #fdf7e5 !important;
}
</style>
