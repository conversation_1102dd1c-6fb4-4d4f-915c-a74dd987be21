<template>
	<view class="content p-rela h-vh-100 o-hid">
		<!-- 导航栏 -->
		<vh-navbar title="" :background="{ background: '#CA101B' }" back-icon-color="#FFF" title-color="#FFF" />
		
		<!-- 页面内容 -->
		<view class="d-flex flex-column a-center pt-120">
			<!-- 标题 -->
			<view class="font-48 text-ffffff font-wei-500 text-center mb-40">输入验证码</view>
			
			<!-- 提示文字 -->
			<view class="font-28 text-ffffff text-center mb-80">
				已发送 4 位验证码至 {{ telephone }}
			</view>
			
			<!-- 验证码输入框 -->
			<view class="d-flex j-center mb-120">
				<view 
					v-for="(item, index) in codeArray" 
					:key="index"
					class="code-input-box mr-20"
					:class="{ 'mr-0': index === 3 }"
				>
					<input
						:ref="`codeInput${index}`"
						v-model="codeArray[index]"
						type="number"
						:maxlength="1"
						class="code-input"
						@input="onCodeInput(index, $event)"
						@focus="onCodeFocus(index)"
						@keydown="onKeyDown(index, $event)"
					/>
				</view>
			</view>
			
			<!-- 确定按钮 -->
			<view class="w-p100 ptb-00-plr-54 mb-60">
				<button 
					@click="confirmCode" 
					class="w-p100 h-100 m-0 p-0 bg-ffffff b-rad-50 font-32 font-wei-500"
					:class="{ 'bg-rgba-255-255-255-06': !isCodeComplete }"
					:style="{ color: isCodeComplete ? '#CA101B' : 'rgba(202, 16, 27, 0.6)' }"
				>
					确定
				</button>
			</view>
			
			<!-- 重新发送 -->
			<view class="d-flex j-center a-center">
				<view 
					v-if="countDown > 0" 
					class="font-28 text-ffffff"
				>
					{{ countDown }}s后重发
				</view>
				<view 
					v-else 
					@click="resendCode" 
					class="font-28 text-ffffff text-decoration-underline"
				>
					重新发送
				</view>
			</view>
		</view>

		<!-- 滑块验证 -->
		<view v-if="sliderStatus" class="p-fixed w-p100 h-p100 top-0 z-9999">
			<iframe style="width: 100%; height: 100%;" :src="sliderUrl" />
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	
	export default {
		name: "verify-code",
		data: () => ({
			telephone: '',
			codeArray: ['', '', '', ''],
			countDown: 59,
			countDownInterval: null,
			sliderUrl: '/html-statics/view/pcLogin.html',
			sliderStatus: false,
			sliderRes: {
				randstr: '',
				ticket: ''
			}
		}),
		computed: {
			...mapState(['routeTable']),
			isCodeComplete() {
				return this.codeArray.every(code => code !== '')
			},
			verifyCode() {
				return this.codeArray.join('')
			}
		},
		methods: {
			// 验证码输入处理
			onCodeInput(index, event) {
				const value = event.detail.value

				// 只允许输入数字
				if (value && !/^\d$/.test(value)) {
					this.codeArray[index] = ''
					return
				}

				// 如果输入了数字且不是最后一个输入框，自动跳转到下一个
				if (value && index < 3) {
					this.$nextTick(() => {
						const nextInput = this.$refs[`codeInput${index + 1}`]
						if (nextInput && nextInput[0]) {
							nextInput[0].focus()
						}
					})
				}

				// 如果删除了内容且不是第一个输入框，跳转到前一个
				if (!value && index > 0) {
					this.$nextTick(() => {
						const prevInput = this.$refs[`codeInput${index - 1}`]
						if (prevInput && prevInput[0]) {
							prevInput[0].focus()
						}
					})
				}
			},
			
			// 输入框获得焦点
			onCodeFocus(index) {
				// 如果当前输入框为空，且前面有空的输入框，则跳转到第一个空的输入框
				for (let i = 0; i < index; i++) {
					if (this.codeArray[i] === '') {
						this.$nextTick(() => {
							const targetInput = this.$refs[`codeInput${i}`]
							if (targetInput && targetInput[0]) {
								targetInput[0].focus()
							}
						})
						break
					}
				}
			},

			// 键盘事件处理
			onKeyDown(index, event) {
				// 处理退格键
				if (event.keyCode === 8 || event.key === 'Backspace') {
					if (this.codeArray[index] === '' && index > 0) {
						// 当前输入框为空且不是第一个，跳转到前一个输入框
						this.$nextTick(() => {
							const prevInput = this.$refs[`codeInput${index - 1}`]
							if (prevInput && prevInput[0]) {
								prevInput[0].focus()
							}
						})
					}
				}
			},
			
			// 确认验证码
			confirmCode() {
				if (!this.isCodeComplete) {
					this.feedback.toast({ title: '请输入完整的验证码' })
					return
				}
				
				const loginParams = {
					telephone: this.telephone,
					code: this.verifyCode,
					reg_from: 4
				}
				
				// 调用登录接口
				this.$u.api.loginByCode(loginParams).then(res => {
					uni.setStorageSync('loginInfo', res.data)
					uni.removeStorageSync('newPeopleIndexMaskCountDown')
					
					if(res?.data?.new_user) {
						this.registerUidByTencentMoments()
					}
					
					// #ifdef H5
						const { token, uid } = res.data
						const maxAge = 24 * 60 * 60 * 30
						document.cookie = `h5token=${token}; path=/; domain=vinehoo.com; max-age=${maxAge}`
						document.cookie = `h5uid=${uid}; path=/; domain=vinehoo.com; max-age=${maxAge}`
					// #endif
					
					// 登录成功后的跳转逻辑
					const pageLength = this.pages.getPageLength()
					if (pageLength <= 2) {
						uni.reLaunch({ url: '/pages/index/index' })
					} else {
						// 返回到登录前的页面（跳过登录页和验证码页）
						uni.navigateBack({ delta: 2 })
					}
				}).catch(err => {
					console.error('登录失败:', err)
					this.feedback.toast({ title: '验证码错误，请重新输入' })
					// 清空验证码
					this.codeArray = ['', '', '', '']
					this.$nextTick(() => {
						const firstInput = this.$refs.codeInput0
						if (firstInput && firstInput[0]) {
							firstInput[0].focus()
						}
					})
				})
			},
			
			// 重新发送验证码
			resendCode() {
				if (this.countDown > 0) return

				const params = {
					telephone: this.telephone,
					version: 'v2'
				}

				this.$u.api.sendSmsCode(params).then(res => {
					if (res.data && res.data.is_ticket) {
						// 需要滑块验证
						this.sliderStatus = true
					} else {
						// 验证码发送成功
						this.feedback.toast({ title: '验证码已重新发送' })
						this.startCountDown()
					}
				}).catch(err => {
					console.error('重新发送验证码失败:', err)
				})
			},
			
			// 开始倒计时
			startCountDown() {
				this.countDown = 59
				this.countDownInterval = setInterval(() => {
					this.countDown--
					if (this.countDown <= 0) {
						clearInterval(this.countDownInterval)
						this.countDownInterval = null
					}
				}, 1000)
			},
			
			// 微信朋友圈注册
			async registerUidByTencentMoments() {
				if (this.param.isMomentsParam()) {
					await this.$u.api.registerUidByTencentMoments()
				}
			},

			// 处理滑块验证消息
			handleMessage(e) {
				if (e?.data?.act === 'response') {
					const { status = 'close', randstr = '', ticket = '' } = e.data.msg?.answer || {}
					if (status === 'success') {
						this.sliderRes = { randstr, ticket }
						this.sendSmsCodeWithSlider()
					}
					this.sliderStatus = false
				}
			},

			// 通过滑块验证发送验证码
			sendSmsCodeWithSlider() {
				const { telephone, sliderRes: { randstr, ticket } } = this
				if (!telephone) {
					this.feedback.toast({ title: '请输入手机号' })
					return
				}
				if (!randstr || !ticket) {
					this.feedback.toast({ title: '请完成滑块验证' })
					return
				}

				const params = { telephone, randstr, ticket, version: 'v2' }
				this.$u.api.sendSmsCode(params).then(() => {
					this.feedback.toast({ title: '验证码已重新发送' })
					this.startCountDown()
				}).catch(err => {
					console.error('重新发送验证码失败:', err)
				})
			}
		},
		onLoad(options) {
			this.telephone = options.telephone || ''
			this.startCountDown()

			// 添加滑块验证消息监听
			window.addEventListener('message', this.handleMessage)

			// 自动聚焦第一个输入框
			this.$nextTick(() => {
				const firstInput = this.$refs.codeInput0
				if (firstInput && firstInput[0]) {
					firstInput[0].focus()
				}
			})
		},
		onUnload() {
			// 移除事件监听器
			window.removeEventListener('message', this.handleMessage)
			if (this.countDownInterval) {
				clearInterval(this.countDownInterval)
			}
		}
	}
</script>

<style scoped>
	.content {
		background: linear-gradient(180deg, #CA101B 0%, #8B0A12 100%);
	}
	
	.code-input-box {
		width: 120rpx;
		height: 120rpx;
		background: rgba(139, 10, 18, 0.8);
		border-radius: 16rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.code-input {
		width: 100%;
		height: 100%;
		text-align: center;
		font-size: 48rpx;
		font-weight: 500;
		color: #FFFFFF;
		background: transparent;
		border: none;
	}
	
	.bg-rgba-255-255-255-06 {
		background: rgba(255, 255, 255, 0.6);
	}
	
	.text-decoration-underline {
		text-decoration: underline;
	}
</style>
