<template>
	<view class="content p-rela h-vh-100 o-hid">
		<!-- 导航栏 -->
		<vh-navbar title="登录" :background="{ background: '#C8111B' }" back-icon-color="#FFF" title-color="#FFF" >
			<view v-if="pages.getPageLength() > 1" class="d-flex a-center font-32 font-wei text-ffffff w-s-now" @click="jump.reLaunch( routeTable.pgIndex )">
				<u-icon name="home" color="#FFF" size="44" />
			</view>
		</vh-navbar>
		
		<!-- 登录logo -->
		<view class="d-flex j-center a-center mt-40">
			<image class="w-342 h-272" src="https://img.vinehoo.com/vinehoomini/v2/login/login_logo.png" mode="aspectFill"></image>
		</view>
		
		<view class="mt-132 ptb-00-plr-54">
			<view class="ptb-00-plr-14">
				<input v-model="loginParams.telephone" type="text" :maxlength="11" :placeholder="placeholder.telephone" class="bb-s-02-eeeeee h-116 font-44 text-ffffff font-wei-600" :placeholder-style="placeholderStyle">
				<view class="d-flex a-center j-sb mt-28">
					<input v-model="loginParams.code" type="text" :maxlength="6" :placeholder="placeholder.code" class="w-418 h-116 bb-s-02-eeeeee font-44 text-ffffff font-wei-600"  :placeholder-style="placeholderStyle">
					<button @click="getCode" class="w-190 h-62 m-0 p-0 bg-e58e93 b-rad-08 font-30 text-ffffff font-wei-500 l-h-62">{{ btnText }}</button>
				</view>
			</view>
			<button @click="handleLogin" class="h-100 mt-72 bg-e58e93 b-rad-52 font-36 text-ffffff font-wei-500">登录</button>
		</view>
		
		<view v-if="sliderStatus" class="p-fixed w-p100 h-p100 top-0 z-9999">
			<iframe style="width: 100%; height: 100%;" :src="sliderUrl" />
		</view>
		
		<!-- 公司名称 -->
		<!-- <view class="p-abso bottom-86 w-p100 d-flex j-center a-center">
			<image class="w-120 h-68" src="https://img.vinehoo.com/vinehoomini/v2/login/vinehoo.png" mode="aspectFill"></image>
		</view> -->
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	import { AUCTION_PATH_PREFIX, AUCTION_SOURCE } from '@/common/js/fun/constant'

	export default{
		name:"login",
		data: () => ({
			placeholderStyle: 'font-size: 34rpx; color: white; font-weight: 400;',
			placeholder: {
				telephone: '请输入手机号',
				code: '请输入验证码',
				slider: '请完成滑块验证'
			},
			loginParams: {
				telephone: '',
				code: '',
				reg_from: 4
			},
			btnText: '获取验证码',
			countDown: 60,
			sliderUrl: '/html-statics/view/pcLogin.html',
			sliderStatus: false,
			sliderRes: {
				randstr: '',
				ticket: ''
			},
			refererUrl: ''
		}),
		computed: {
			...mapState(['routeTable'])
		},
		methods:{
			getCode() {
				if (this.countDown !== this.$options.data().countDown) return
				if (!this.loginParams.telephone) {
					this.feedback.toast({ title: this.placeholder.telephone })
					return
				}
				this.sliderStatus = true
			},
			handleMessage(e) {
				if (e?.data?.act === 'response') {
					const { status = 'close', randstr = '', ticket = '' } = e.data.msg?.answer || {}
					if (status === 'success') {
						this.sliderRes = { randstr, ticket }
						this.sendSmsCode()
					}
					this.sliderStatus = false
				}
			},
			sendSmsCode() {
				const { loginParams: { telephone }, sliderRes: { randstr, ticket } } = this
				if (!telephone) {
					this.feedback.toast({ title: this.placeholder.telephone })
					return
				}
				if (!randstr || !ticket) {
					this.feedback.toast({ title: this.placeholder.slider })
					return
				}
				this.startCountDown()
				const params = { telephone, randstr, ticket }
				this.$u.api.sendSmsCode(params)
			},
			startCountDown() {
				this.countDownStatus = true
				this.btnText = `${this.countDown}s`
				this.countDownInterval = setInterval(() => {
					this.countDown--
					if (this.countDown > 0) {
						this.btnText = `${this.countDown}s`
					} else {
						clearInterval(this.countDownInterval)
						this.btnText = '重新获取'
						this.countDown = this.$options.data().countDown
					}
				}, 1000)
			},
			
			// 微信朋友圈
			async registerUidByTencentMoments() {
				if( this.param.isMomentsParam() ) {
					await this.$u.api.registerUidByTencentMoments()
				}
			},
			
			handleLogin() {
				const { telephone, code } = this.loginParams
				if (!telephone) {
					this.feedback.toast({ title: this.placeholder.telephone })
					return
				}
				if (!code) {
					this.feedback.toast({ title: this.placeholder.code })
					return
				}
				const pageLength = this.pages.getPageLength()
				let isFromAuctionPage = false
				if (pageLength > 1) {
					isFromAuctionPage = this.pages.getPrvePageFullPath().includes(AUCTION_PATH_PREFIX)
				}
				console.log('isFromAuctionPage', isFromAuctionPage)
				let params = this.loginParams
				if (isFromAuctionPage) {
					const { platform, event } = AUCTION_SOURCE
					Object.assign(params, { vhPlatform: platform, vhEvent: event })
				}
				this.$u.api.loginByCode(this.loginParams).then(res => {
					uni.setStorageSync('loginInfo', res.data)
					uni.removeStorageSync('newPeopleIndexMaskCountDown')
					if(res?.data?.new_user) {
						this.registerUidByTencentMoments()
					}
					// #ifdef H5
						const { token, uid } = res.data
						const maxAge = 24 * 60 * 60 * 30
						document.cookie = `h5token=${token}; path=/; domain=vinehoo.com; max-age=${maxAge}`
						document.cookie = `h5uid=${uid}; path=/; domain=vinehoo.com; max-age=${maxAge}`
					// #endif
					if (this.refererUrl) {
						location.href = this.refererUrl
					} else {
						const pageLength = this.pages.getPageLength()
						if ( pageLength <= 1) {
							uni.reLaunch({ url:'/pages/index/index' })
						} else {
							if (this.pages.getPrvePageFullPath() === '/pages/mine/mine') {
								uni.reLaunch({ url: '/pages/index/index' })
								return
							}
							uni.navigateBack()
						}
					}
				})
			}
		},
		onLoad(options) {
			this.refererUrl = options.refererUrl
			window.addEventListener('message', this.handleMessage)
		},
		onUnload() {
			window.removeEventListener('message', this.handleMessage)
			this.countDownInterval && clearInterval(this.countDownInterval)
		}
	}
</script>

<style scoped>
	.content {
		background-image: url(https://img.vinehoo.com/vinehoomini/v2/login/login_bg.png);
		background-size: cover;
	}
	
	/* uni-page-body {
		height: 100%;
	} */
</style>
