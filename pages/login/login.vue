<template>
	<view class="content p-rela h-vh-100 o-hid">
		<!-- 导航栏 -->
		<vh-navbar title="登录" :background="{ background: '#C8111B' }" back-icon-color="#FFF" title-color="#FFF" >
			<view v-if="pages.getPageLength() > 1" class="d-flex a-center font-32 font-wei text-ffffff w-s-now" @click="jump.reLaunch( routeTable.pgIndex )">
				<u-icon name="home" color="#FFF" size="44" />
			</view>
		</vh-navbar>
		
		<!-- 登录logo -->
		<view class="d-flex j-center a-center mt-40">
			<image class="w-342 h-272" src="https://img.vinehoo.com/vinehoomini/v2/login/login_logo.png" mode="aspectFill"></image>
		</view>
		
		<!-- 输入框区域 -->
		<view class="mt-120 ptb-00-plr-54">
			<view class="ptb-00-plr-14">
				<!-- 手机号输入框 -->
				<view class="p-rela h-92 b-rad-46 bg-000-000-000-030">
					<input
						v-model="loginParams.telephone"
						type="text"
						:maxlength="11"
						:placeholder="placeholder.telephone"
						class=" h-92 ptb-00-plr-38    font-32 text-ffffff "
						:placeholder-style="placeholderStyle"
					>
				</view>

				<!-- 获取验证码按钮 -->
				<view class="mt-70">
					<u-button :disabled="!canSubmit"  shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
			:custom-style="{ height:'80rpx', fontSize:'32rpx', fontWeight:'bold', color:'#E80404', backgroundColor: !canSubmit ? '#FCE4E3' : '#ffffff', border:'none' }" @click="getCode">获取验证码</u-button>
				</view>
			</view>
		</view>

		<!-- 协议确认 -->
		<view class="p-abso bottom-80 w-p100 d-flex j-center a-center">
			<view class="d-flex a-center">
				
				<vh-check :checked="isAgreed" @click="isAgreed = !isAgreed" />
				<view class="font-24 text-ffffff">
					<text>登录即代表同意</text>
					<text @click="jumpToUserAgreement" class="text-decoration-underline">《用户协议》</text>
					<text>和</text>
					<text @click="jumpToPrivacyPolicy" class="text-decoration-underline">《隐私政策》</text>
				</view>
			</view>
		</view>
		
		<!-- 滑块验证 -->
		<view v-if="sliderStatus" class="p-fixed w-p100 h-p100 top-0 z-9999">
			<iframe style="width: 100%; height: 100%;" :src="sliderUrl" />
		</view>
		
		<!-- 协议确认弹窗 -->
		<u-popup v-model="showAgreementPopup" mode="center" width="600" border-radius="20">
			<view class="p-40">
				<view class="font-32 font-wei-500 text-3 text-center mb-40">温馨提示</view>
				<view class="font-28 text-6 l-h-40 text-center mb-60">
					<text>登录即代表同意</text>
					<text @click="jumpToUserAgreement" class="text-ca101b">《用户协议》</text>
					<text>和</text>
					<text @click="jumpToPrivacyPolicy" class="text-ca101b">《隐私政策》</text>
				</view>
				<view class="d-flex j-sb">
					<button @click="showAgreementPopup = false" class="w-240 h-80 bg-f5f5f5 b-rad-40 font-28 text-6">取消</button>
					<button @click="agreeAndSendCode" class="w-240 h-80 bg-ca101b b-rad-40 font-28 text-ffffff">同意并登录</button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	import { AUCTION_PATH_PREFIX, AUCTION_SOURCE } from '@/common/js/fun/constant'

	export default{
		name:"login",
		data: () => ({
			placeholderStyle: 'font-size: 28rpx; color: rgba(255,255,255,0.6); font-weight: 400;',
			placeholder: {
				telephone: '请输入手机号',
				code: '请输入验证码',
				slider: '请完成滑块验证'
			},
			loginParams: {
				telephone: '',
				code: '',
				reg_from: 4
			},
			btnText: '获取验证码',
			countDown: 60,
			sliderUrl: '/html-statics/view/pcLogin.html',
			sliderStatus: false,
			sliderRes: {
				randstr: '',
				ticket: ''
			},
			refererUrl: '',
			isAgreed: false,
			showAgreementPopup: false
		}),
		computed: {
			...mapState(['routeTable', 'agreementPrefix']),
			canSubmit() {
				if(this.loginParams.telephone.length == 11){
					return true;
				}
			},
		},
		methods:{
			getCode() {
				if (this.countDown !== this.$options.data().countDown) return
				if (!this.loginParams.telephone) {
					this.feedback.toast({ title: this.placeholder.telephone })
					return
				}

				// 检查是否同意协议
				if (!this.isAgreed) {
					this.showAgreementPopup = true
					return
				}

				this.sendSmsCodeWithVersion()
			},

			// 切换协议同意状态
			toggleAgreement() {
				this.isAgreed = !this.isAgreed
			},

			// 跳转到用户协议
			jumpToUserAgreement() {
				
				this.jump.jumpH5Agreement(`${this.agreementPrefix}/vinehooUser`, this.$vhFrom)
			},

			// 跳转到隐私政策
			jumpToPrivacyPolicy() {
				this.jump.jumpH5Agreement(`${this.agreementPrefix}/PrivacyPolicy`, this.$vhFrom)
				
			},

			// 同意协议并发送验证码
			agreeAndSendCode() {
				this.isAgreed = true
				this.showAgreementPopup = false
				this.sendSmsCodeWithVersion()
			},

			// 发送验证码（新版本）
			sendSmsCodeWithVersion() {
				const { loginParams: { telephone } } = this
				if (!telephone) {
					this.feedback.toast({ title: this.placeholder.telephone })
					return
				}

				const params = {
					telephone,
					version: 'v2'
				}

				this.$u.api.sendSmsCode(params).then(res => {
					if (res.data && res.data.is_ticket) {
						// 需要滑块验证
				this.sliderStatus = true
					} else {
						// 验证码发送成功，跳转到验证码输入页面
						this.startCountDown()
						this.jump.navigateTo(`/pages/verify-code/verify-code?telephone=${telephone}`)
					}
				}).catch(err => {
					console.error('发送验证码失败:', err)
				})
			},
			handleMessage(e) {
				if (e?.data?.act === 'response') {
					const { status = 'close', randstr = '', ticket = '' } = e.data.msg?.answer || {}
					if (status === 'success') {
						this.sliderRes = { randstr, ticket }
						this.sendSmsCode()
					}
					this.sliderStatus = false
				}
			},
			sendSmsCode() {
				const { loginParams: { telephone }, sliderRes: { randstr, ticket } } = this
				if (!telephone) {
					this.feedback.toast({ title: this.placeholder.telephone })
					return
				}
				if (!randstr || !ticket) {
					this.feedback.toast({ title: this.placeholder.slider })
					return
				}
				this.startCountDown()
				const params = { telephone, randstr, ticket }
				this.$u.api.sendSmsCode(params)
			},
			startCountDown() {
				this.countDownStatus = true
				this.btnText = `${this.countDown}s`
				this.countDownInterval = setInterval(() => {
					this.countDown--
					if (this.countDown > 0) {
						this.btnText = `${this.countDown}s`
					} else {
						clearInterval(this.countDownInterval)
						this.btnText = '重新获取'
						this.countDown = this.$options.data().countDown
					}
				}, 1000)
			},
			
			// 微信朋友圈
			async registerUidByTencentMoments() {
				if( this.param.isMomentsParam() ) {
					await this.$u.api.registerUidByTencentMoments()
				}
			},
			
			handleLogin() {
				const { telephone, code } = this.loginParams
				if (!telephone) {
					this.feedback.toast({ title: this.placeholder.telephone })
					return
				}
				if (!code) {
					this.feedback.toast({ title: this.placeholder.code })
					return
				}
				const pageLength = this.pages.getPageLength()
				let isFromAuctionPage = false
				if (pageLength > 1) {
					isFromAuctionPage = this.pages.getPrvePageFullPath().includes(AUCTION_PATH_PREFIX)
				}
				console.log('isFromAuctionPage', isFromAuctionPage)
				let params = this.loginParams
				if (isFromAuctionPage) {
					const { platform, event } = AUCTION_SOURCE
					Object.assign(params, { vhPlatform: platform, vhEvent: event })
				}
				this.$u.api.loginByCode(this.loginParams).then(res => {
					uni.setStorageSync('loginInfo', res.data)
					uni.removeStorageSync('newPeopleIndexMaskCountDown')
					if(res?.data?.new_user) {
						this.registerUidByTencentMoments()
					}
					// #ifdef H5
						const { token, uid } = res.data
						const maxAge = 24 * 60 * 60 * 30
						document.cookie = `h5token=${token}; path=/; domain=vinehoo.com; max-age=${maxAge}`
						document.cookie = `h5uid=${uid}; path=/; domain=vinehoo.com; max-age=${maxAge}`
					// #endif
					if (this.refererUrl) {
						location.href = this.refererUrl
					} else {
						const pageLength = this.pages.getPageLength()
						if ( pageLength <= 1) {
							uni.reLaunch({ url:'/pages/index/index' })
						} else {
							if (this.pages.getPrvePageFullPath() === '/pages/mine/mine') {
								uni.reLaunch({ url: '/pages/index/index' })
								return
							}
							uni.navigateBack()
						}
					}
				})
			}
		},
		onLoad(options) {
			this.refererUrl = options.refererUrl
			window.addEventListener('message', this.handleMessage)
		},
		onUnload() {
			window.removeEventListener('message', this.handleMessage)
			this.countDownInterval && clearInterval(this.countDownInterval)
		}
	}
</script>

<style scoped>
	.content {
		background-image: url(https://img.vinehoo.com/vinehoomini/v2/login/login_bg.png);
		background-size: cover;
	}
	
	.bg-ca101b {
		background-color: #CA101B;
	}

	.bg-f5f5f5 {
		background-color: #F5F5F5;
	}

	.text-decoration-underline {
		text-decoration: underline;
	}
</style>
