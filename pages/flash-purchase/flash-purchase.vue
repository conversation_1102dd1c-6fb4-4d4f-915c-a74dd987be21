<template>
  <view :class="['h-min-p100', isLiquorActivity ? 'bg-0c0001' : 'bg-f5f5f5']">
    <scroll-view
      scroll-y
      :style="{ height: scrollViewHeight }"
      @scroll="handleGoodsScroll"
      @scrolltolower="onScrollToLower"
    >
      <!-- 导航栏 -->
      <vh-navbar
        height="46"
        :is-back="false"
        :show-border="true"
        :background="{ background: navbarBackground }"
        :borderStyle="{ borderBottom: navbarBorder }"
        style="height: 47px"
      >
        <view class="d-flex j-sb a-center w-p100">
          <image class="w-132 h-48 ml-24" :src="logoIconSrc" @click="jump.reLaunch(routeTable.pgIndex)" />
          <view class="flex-c-c mr-24">
            <view class="flex-c-c mr-26 wh-36">
              <view class="d-flex">
                <image
                  class="wh-36 p-10"
                  :src="searchIconSrc"
                  @click="jump.navigateTo(`${routeTable.pFGlobalSearch}?type=1`)"
                />
              </view>
            </view>
            <view class="flex-c-c mr-26 wh-36">
              <view class="p-rela d-flex" @click="jump.loginNavigateTo(`${routeTable.pBShoppingCart}`)">
                <image class="wh-36 p-10" :src="shoppingCartIconSrc" />
                <view
                  v-if="shoppingCartNum"
                  class="p-abso top-n-02 right-n-02 w-26 h-26 bg-e80404 b-rad-p50 flex-c-c font-16 text-ffffff"
                  >{{ shoppingCartNum }}</view
                >
              </view>
            </view>
            <view class="flex-c-c wh-36" @click="jump.loginNavigateTo(`${routeTable.pEMessageCenter}`)">
              <view class="p-rela d-flex">
                <image class="w-36 h-38 p-10" :src="msgIconSrc" />
                <view v-if="unReadTotalNum" class="p-abso top-10 right-10 wh-10 bg-e80404 b-rad-p50" />
              </view>
            </view>
          </view>
        </view>
      </vh-navbar>

      <!-- 数据内容 -->
      <view v-if="!loading" class="fade-in">
        <!-- tabs列表 -->
        <view
          class="w-p100 h-px-45 d-flex j-sb a-center ptb-00-plr-60"
          :class="[
            channelListSticky ? 'p-stic z-979' : 'p-stic z-979',
            { 'choose-wine-activity': isChooseWineActivity },
            isLiquorActivity ? 'bg-1c1c1c' : 'bg-ffffff',
          ]"
          :style="{ top: getNavigationBarHeight + 'px' }"
        >
          <view
            v-for="(item, index) in channelList"
            :key="index"
            class="p-rela h-p100 flex-c-c"
            @click="changeChannel(item)"
          >
            <vh-image
              v-if="item.icon"
              :loading-type="2"
              :src="item.icon"
              :width="item.iconWidth"
              :height="item.iconHeight"
            ></vh-image>
            <view
              v-else
              class="font-32 font-wei-600 l-h-44"
              :class="[
                {
                  'text-3': channelId !== item.id && !isLiquorActivity,
                  'text-a7a7a7': channelId !== item.id && isLiquorActivity,
                  'text-e80404': channelId === item.id && !isKj,
                  'text-8247e4': channelId === item.id && isKj,
                },
              ]"
              >{{ item.name }}</view
            >
            <view class="p-abso bottom-0 flex-c-c w-p100">
              <view
                v-show="channelId === item.id"
                class="w-36 h-08 b-rad-04"
                :class="[
                  {
                    'bg-li-12': !isLiquorActivity && !isKj,
                    'bg-ff9500': isLiquorActivity,
                    'bg-8247e4': !isLiquorActivity && isKj,
                  },
                ]"
              ></view>
            </view>
          </view>
        </view>

        <view
          v-if="goldAreaList.length"
          style="overflow: hidden; white-space: nowrap"
          class="d-flex j-sb pt-20"
          :class="[
            goldAreaCols.length ? 'ptb-00-plr-30' : 'ptb-00-plr-40',
            isLiquorActivity ? 'bg-1c1c1c' : 'bg-ffffff',
          ]"
        >
          <view v-for="(list, colIndex) in goldAreaCols" :key="colIndex">
            <view
              v-for="(item, index) in list"
              :key="index"
              class="flex-c-c flex-column"
              :class="[index ? 'mt-20' : '']"
              @click="JumpColumn(item)"
            >
              <vh-image :loading-type="2" :src="item.icon" :width="92" :height="92" border-radius="12rpx" />
              <view class="mt-14 font-22 l-h-32" :class="isLiquorActivity ? 'text-ffffff' : 'text-6'">{{
                item.name
              }}</view>
            </view>
          </view>
        </view>
        <view
          v-if="swiperList.length"
          class="ptb-00-plr-24 pt-20"
          :class="isLiquorActivity ? 'bg-1c1c1c' : 'bg-ffffff'"
        >
          <vh-swiper
            :loading-type="2"
            :list="swiperList"
            :height="168"
            @click="jump.pubConfJumpBD($event, 3, 3, 202000, $event.id)"
          />
        </view>

        <!-- 筛选 -->
        <view
          v-if="false"
          class="p-stic z-978 d-flex ptb-32-plr-24 bg-1c1c1c"
          :style="{ top: getNavigationBarHeight + 45 + 'px' }"
        >
          <view class="flex-1 o-hid">
            <scroll-view scroll-x>
              <view class="d-flex">
                <view
                  v-for="(item, index) in currentActivityLabelList"
                  :key="item.id"
                  class="flex-shrink flex-c-c ptb-00-plr-14 w-min-100 h-46 font-24 b-rad-04"
                  :class="[
                    index ? 'ml-20' : '',
                    item.id === channelActivityLabelId ? 'bg-ff9500 text-ffffff' : 'bg-403021 text-d8d8d8',
                  ]"
                  @click="onActivityLabelItemClick(item)"
                  >{{ item.label_name }}</view
                >
              </view>
            </scroll-view>
          </view>
          <view class="flex-shrink flex-c-c pl-16" @click="getFilterList">
            <view class="w-02 h-28 bg-ffffff"></view>
            <view class="ptb-00-plr-16 font-28 l-h-40" :class="doFilter ? 'text-e80404' : 'text-ffffff'">筛选</view>
            <image :src="ossIcon(`/comm/funnel${doFilter ? '_sel' : ''}.png`)" class="w-28 h-28" />
          </view>
        </view>
        <!-- <view
        v-else
        class="p-stic z-978 d-flex ptb-16-plr-24 bg-ffffff"
        :style="{ top: getNavigationBarHeight + 45 + 'px' }"
      >
        <view class="flex-1 o-hid">
          <scroll-view scroll-x>
            <view class="d-flex">
              <view
                v-for="(item, index) in labelList"
                :key="index"
                class="flex-c-c flex-shrink mr-16 ptb-00-plr-20 w-min-128 h-46 font-24 b-rad-04"
                :class="[item.label_id === labelId ? 'font-wei-500 text-ffffff bg-e80404' : 'text-3 bg-ececec']"
                @click="onLabelItemClick(item)"
                >{{ item.label_name }}</view
              >
            </view>
          </scroll-view>
        </view>
        <view class="flex-shrink flex-c-c pl-16" @click="getFilterList">
          <view class="w-02 h-28 bg-cccccc"></view>
          <view class="ptb-00-plr-16 font-28 l-h-40" :class="doFilter ? 'text-e80404' : 'text-6'">筛选</view>
          <image :src="ossIcon(`/comm/funnel${doFilter ? '_sel' : ''}.png`)" class="w-28 h-28" />
        </view>
      </view> -->
        <view class="p-stic z-978" :style="{ top: getNavigationBarHeight + 45 + 'px' }">
          <view :class="isLiquorActivity ? 'bg-1c1c1c' : 'bg-ffffff'" class="d-flex j-sb a-center ptb-20-plr-44">
            <view
              v-if="isLiquorActivity"
              class="font-28 text-6"
              :class="filterIndex == 0 ? 'text-ff9500 font-wei' : 'text-ffffff'"
              @click="changeSort(0)"
              >默认</view
            >
            <view
              v-else
              class="font-28 text-6"
              :class="filterIndex == 0 ? 'text-e80404 font-wei' : ''"
              @click="changeSort(0)"
              >默认</view
            >
            <view
              class="d-flex a-center"
              v-for="(item, index) in sortList"
              :key="index"
              @click="changeSort(item.index)"
            >
              <text
                class="font-28 text-6"
                v-if="isLiquorActivity"
                :class="filterIndex == item.index ? 'text-ff9500 font-wei' : 'text-ffffff'"
                >{{ item.name }}</text
              >
              <text class="font-28 text-6" v-else :class="filterIndex == item.index ? 'text-e80404 font-wei' : ''">{{
                item.name
              }}</text>
              <view v-if="item.index === 1 && !isLiquorActivity">
                <image
                  class="ml-04 w-14 h-14"
                  v-if="sortType == item.type"
                  :src="`${osip}/flash_purchase/sort_${sort}.png`"
                  mode="aspectFill"
                />
                <image class="ml-04 w-14 h-14" v-else :src="`${osip}/flash_purchase/sort.png`" mode="aspectFill" />
              </view>
              <view v-if="item.index === 1 && isLiquorActivity">
                <image
                  class="ml-04 w-14 h-14"
                  v-if="sortType == item.type"
                  :src="`${osip}/flash_purchase/liquor_sort_${sort}.png`"
                  mode="aspectFill"
                />
                <image class="ml-04 w-14 h-14" v-else :src="`${osip}/flash_purchase/sort.png`" mode="aspectFill" />
              </view>
            </view>
            <view class="flex-shrink flex-c-c" @click="getFilterList">
              <view class="w-02 h-28 bg-cccccc"></view>
              <view
                v-if="isLiquorActivity"
                class="ptb-00-plr-16 font-28 pl-40 l-h-40"
                :class="doFilter ? 'text-ff9500' : 'text-ffffff'"
                >筛选</view
              >
              <view v-else class="ptb-00-plr-16 font-28 pl-40 l-h-40" :class="doFilter ? 'text-e80404' : 'text-6'"
                >筛选</view
              >

              <image
                v-if="isLiquorActivity"
                :src="ossIcon(`/comm/funnel${doFilter ? '_sel_liquor' : ''}.png`)"
                class="w-28 h-28"
              />
              <image v-else :src="ossIcon(`/comm/funnel${doFilter ? '_sel' : ''}.png`)" class="w-28 h-28" />
            </view>
            <!-- <view v-if="!isKj" class="d-flex a-center w-28 h-28">
            <image
              class="flex-shrink p-20 w-28 h-28"
              :src="`${osip}/flash_purchase/change_${showWaterfallFlow ? 'layout' : 'list'}.png`"
              mode="aspectFill"
              @click="onListLayoutChange"
            />
          </view>
          <view class="d-flex a-center pl-30 bl-s-01-cccccc font-28 text-6" @click="getFilterList">
            <text :class="doFilter ? 'text-e80404 font-wei' : ''">筛选</text>
            <image
              class="w-28 h-28 ml-10"
              :src="`${osip}/comm/funnel${doFilter ? '_sel' : ''}.png`"
              mode="aspectFill"
            />
          </view> -->
          </view>
        </view>

        <!-- 商品列表（有数据） -->
        <view>
          <!-- 商品列表（瀑布流） -->
          <view v-if="showWaterfallFlow && !isLiquorActivity && !isKj" class="p-24">
            <u-waterfall v-model="goodsList" ref="uWaterfall" :add-time="4">
              <template v-slot:left="{ leftList }">
                <view
                  class="p-rela bg-ffffff w-346 b-rad-10 o-hid t-trans-3d-1 mb-10"
                  v-for="(item, index) in leftList"
                  :key="index"
                >
                  <ChooseWineWfItem v-if="isChooseWineActivity" :item="item"></ChooseWineWfItem>
                  <!-- 广告位（酒会） -->
                  <vh-waterfall-wine-party-ad v-else-if="item.modul == 1" :item="item" :buryDotParams="buryDotParams" />
                  <!-- 广告位（直播） -->
                  <vh-waterfall-live-ad v-else-if="item.modul == 2" :item="item" :buryDotParams="buryDotParams" />
                  <!-- 广告位（专题活动） -->
                  <vh-waterfall-thematic-activities-ad
                    v-else-if="item.modul == 3"
                    :item="item"
                    :buryDotParams="buryDotParams"
                  />
                  <!-- 广告位（专题产品） -->
                  <vh-waterfall-special-products-ad
                    v-else-if="item.modul == 4"
                    :item="item"
                    :buryDotParams="buryDotParams"
                  />
                  <!-- 常规商品 -->
                  <vh-waterfall-goods-list v-else :item="item" />
                </view>
              </template>
              <template v-slot:right="{ rightList }">
                <view
                  class="p-rela bg-ffffff w-346 b-rad-10 o-hid t-trans-3d-1 mb-10 ml-10"
                  v-for="(item, index) in rightList"
                  :key="index"
                >
                  <ChooseWineWfItem v-if="isChooseWineActivity" :item="item"></ChooseWineWfItem>
                  <!-- 广告位（酒会） -->
                  <vh-waterfall-wine-party-ad v-else-if="item.modul == 1" :item="item" :buryDotParams="buryDotParams" />
                  <!-- 广告位（直播） -->
                  <vh-waterfall-live-ad v-else-if="item.modul == 2" :item="item" :buryDotParams="buryDotParams" />
                  <!-- 广告位（专题活动） -->
                  <vh-waterfall-thematic-activities-ad
                    v-else-if="item.modul == 3"
                    :item="item"
                    :buryDotParams="buryDotParams"
                  />
                  <!-- 广告位（专题产品） -->
                  <vh-waterfall-special-products-ad
                    v-else-if="item.modul == 4"
                    :item="item"
                    :buryDotParams="buryDotParams"
                  />
                  <!-- 常规商品 -->
                  <vh-waterfall-goods-list v-else :item="item" />
                </view>
              </template>
            </u-waterfall>
            <u-loadmore :status="loadStatus" />
          </view>

          <!-- 商品列表（常规） -->

          <view v-if="!showWaterfallFlow || isLiquorActivity || isKj" class="p-24">
            <ChooseWineGoodsList v-if="isChooseWineActivity" :list="goodsList" />
            <LiquorGoodsList
              v-else-if="isLiquorActivity"
              :list="liquorActivityGoodsList"
              :page="page"
              :totalPage="totalPage"
              @loadMore="onScrollToLower"
            />
            <template v-else>
              <view class="bg-ffffff b-rad-10 mb-20 o-hid t-trans-3d-1" v-for="(item, index) in goodsList" :key="index">
                <!-- 广告位（酒会） -->
                <vh-normal-wine-party-ad v-if="item.modul == 1" :item="item" :buryDotParams="buryDotParams" />
                <!-- 广告位（直播） -->
                <vh-normal-live-ad v-else-if="item.modul == 2" :item="item" :buryDotParams="buryDotParams" />
                <!-- 广告位（专题活动） -->
                <vh-normal-thematic-activities-ad
                  v-else-if="item.modul == 3"
                  :item="item"
                  :buryDotParams="buryDotParams"
                />
                <!-- 广告位（专题产品） -->
                <vh-normal-special-products-ad
                  v-else-if="item.modul == 4"
                  :item="item"
                  :buryDotParams="buryDotParams"
                />
                <!-- 常规商品 -->
                <vh-normal-goods-list :id="'goods_' + index" :newYearTheme="newYearTheme" v-else :item="item" />
              </view>
            </template>

            <u-loadmore v-if="!isLiquorActivity" :status="loadStatus" />
          </view>
        </view>

        <!-- 商品列表（无数据） -->
        <!-- <vh-empty v-else :padding-top="52" :padding-bottom="350" :image-src="`${osip}/empty/emp_goods.png`" text="亲，暂无商品哦~" :text-bottom="0" /> -->

        <!-- 弹框 -->
        <view class="">
          <!-- 筛选弹框 -->
          <u-popup v-model="showScreenPop" :safe-area-inset-bottom="true" mode="bottom" :border-radius="20">
            <view :style="[{ maxHeight: screenPopMaxHeight }]" class="p-rela">
              <!-- 标题 -->
              <view class="p-fixed top-0 z-100 w-p100 bg-ffffff d-flex j-sb a-center ptb-32-plr-30">
                <view class=""></view>
                <view class="text-center font-32 font-wei text-0">全部筛选</view>
                <image
                  class="w-44 h-44"
                  :src="`${osip}/flash_purchase/clo_gray.png`"
                  mode="widthFix"
                  @click.stop="showScreenPop = false"
                ></image>
              </view>

              <!-- 拥有筛选数据 -->
              <view class="fade-in">
                <!-- 筛选内容（关键词、类型、国家、价格区间） -->
                <view class="pt-100 pr-28 pb-40 pl-28 mt-10">
                  <view style="margin-bottom: 60rpx">
                    <view class="font-32 font-wei text-3">价格区间（元）</view>
                    <view class="d-flex j-sb a-center mt-20">
                      <input
                        v-model="minPrice"
                        class="bg-f6f6f6 w-308 h-70 text-center text-3"
                        :class="[isLiquorActivity ? 'font-28 b-rad-08' : 'font-26 b-rad-36']"
                        type="number"
                        placeholder="最低价"
                        :placeholder-style="
                          isLiquorActivity ? 'color: #999; font-size: 28rpx;' : 'color: #999; font-size: 26rpx;'
                        "
                      />
                      <view class="bg-999999 w-32 h-02"></view>
                      <input
                        v-model="maxPrice"
                        class="bg-f6f6f6 w-308 h-70 text-center text-3"
                        :class="[isLiquorActivity ? 'font-28 b-rad-08' : 'font-26 b-rad-36']"
                        type="number"
                        placeholder="最高价"
                        :placeholder-style="
                          isLiquorActivity ? 'color: #999; font-size: 28rpx;' : 'color: #999; font-size:26rpx;'
                        "
                      />
                    </view>
                  </view>
                  <view
                    v-for="(row, index) in [
                      isKj
                        ? { list: [] }
                        : { title: '关键词', list: keywordList, typeName: 'product_keyword', typeId: 'keywordId' },
                      { title: '类型', list: wineTypeList, typeName: 'product_category', typeId: 'categoryId' },
                      { title: '国家', list: countryList, typeName: 'country', typeId: 'countryId' },
                    ]
                      .concat(
                        isLiquorActivity
                          ? [{ title: '产区', list: regionsList, typeName: 'regions', typeId: 'regionsId' }]
                          : []
                      )
                      .filter((item) => item.list.length)"
                    :key="row.title"
                    :class="[index ? 'mt-60' : 'mt-02']"
                  >
                    <view class="font-32 font-wei text-3">{{ row.title }}</view>
                    <view class="d-flex j-sb flex-wrap">
                      <view
                        v-for="item in row.list"
                        :key="item.id"
                        class="bg-f6f6f6 w-216 h-70 d-flex j-center a-center mt-20 font-26 text-3"
                        :class="[
                          isLiquorActivity ? 'b-rad-08' : 'b-rad-36',
                          _data[row.typeId] == item.id
                            ? isLiquorActivity
                              ? 'bg-liquor-filter-item'
                              : 'fade-in bg-fce0e0 b-s-01-e80404 text-e04040'
                            : isLiquorActivity
                            ? ''
                            : 'fade-in',
                        ]"
                        @click="selectFilterItem(row.typeName, row.typeId, item)"
                        >{{ item.name }}</view
                      >
                      <view class="w-216"></view>
                      <view class="w-216"></view>
                    </view>
                  </view>

                  <!-- 价格区间 -->
                  <!-- <view v-if="!isKj" class="mt-60">
                  <view class="font-32 font-wei text-3">价格区间（元）</view>
                  <view class="d-flex j-sb a-center mt-20">
                    <input
                      v-model="minPrice"
                      class="bg-f6f6f6 w-308 h-70 text-center text-3"
                      :class="[isLiquorActivity ? 'font-28 b-rad-08' : 'font-26 b-rad-36']"
                      type="number"
                      placeholder="最低价"
                      :placeholder-style="
                        isLiquorActivity ? 'color: #999; font-size: 28rpx;' : 'color: #999; font-size: 26rpx;'
                      "
                    />
                    <view class="bg-999999 w-32 h-02"></view>
                    <input
                      v-model="maxPrice"
                      class="bg-f6f6f6 w-308 h-70 text-center text-3"
                      :class="[isLiquorActivity ? 'font-28 b-rad-08' : 'font-26 b-rad-36']"
                      type="number"
                      placeholder="最高价"
                      :placeholder-style="
                        isLiquorActivity ? 'color: #999; font-size: 28rpx;' : 'color: #999; font-size:26rpx;'
                      "
                    />
                  </view>
                </view> -->
                </view>

                <!-- 底部按钮 -->
                <view
                  class="p-stic bottom-0 z-999 bg-feffff w-p100 h-104 d-flex j-sa a-center b-sh-00021200-022 ptb-00-plr-28"
                >
                  <button class="vh-btn flex-c-c w-308 h-64 font-28 text-9 bg-eeeeee b-rad-32" @click="resetFilter">
                    重置
                  </button>
                  <button
                    class="vh-btn flex-c-c w-308 h-64 font-28 b-rad-32"
                    :class="[isLiquorActivity ? 'text-ff9500 bg-0c0001' : 'text-ffffff bg-e80404']"
                    @click="confirmFilter"
                  >
                    确定
                  </button>
                </view>
              </view>
            </view>
          </u-popup>

          <NewPeopleFloatingFrame
            v-if="newPeopleFloatingFrameVisible"
            :isLogin="userIsLogin"
            :received="newPeopleCouponPackage.collect_status"
            :isLiquorActivity="isLiquorActivity"
            @jumpActivity="onJumpActivityPage"
          />
        </view>
      </view>

      <!-- 骨架屏 -->
      <vh-skeleton v-else :type="4" bg-color="#FFF" :has-tab-bar="true" />

      <!-- 底部导航栏 tabBar -->
      <vh-tabbar
        :loading="loading"
        :activeColor="isLiquorActivity ? '#FF9500' : '#E80404'"
        :inactiveColor="isLiquorActivity ? '#A3A7B0' : '#333'"
        :list="tabbarList"
        :tabbarClass="isLiquorActivity ? 'bg-1e1a16' : ''"
        @topRefresh="topRefresh"
      />
    </scroll-view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { CHOOSE_WINE_ACTIVITY_ID, LIQUOR_ACTIVITY_ID } from '@/common/js/fun/constant'
import newPeopleMixin from '@/common/js/mixins/newPeopleMixin'
import userMixin from '@/common/js/mixins/userMixin'
import topRefreshMixin from '@/common/js/mixins/topRefreshMixin'
import startupPageOptionsMixin from '@/common/js/mixins/startupPageOptionsMixin'
import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'
export default {
  name: 'flash-purchase',
  mixins: [newPeopleMixin, userMixin, topRefreshMixin, startupPageOptionsMixin, longpressCopyMixin],
  data() {
    return {
      visibleGoods: [], // 存储可见商品信息
      scrollViewHeight: '100vh', // 根据实际布局调整高度
      osip: 'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
      loading: true, //加载状态 true = 加载中、false = 结束加载
      initChannel: 0, //初始化频道  闪购：0、秒发：1、跨境：2、尾货：3
      shoppingCartNum: 0, //购物车数量
      unReadTotalNum: 0, //未读消息总数量
      scrollTop: 0, //距离顶部距离
      channelListSticky: false, //是否让频道列表吸顶
      channelId: 0, //频道id
      channelList: [], //频道列表
      channelActivityId: 0, //频道活动id
      channelActivityLabelId: 0,
      currentChannel: 0, //当前选中的频道tabs 闪购：0、秒发：1、跨境：2、尾货：3
      currentChannelType: 0, //0 = 商品、1 = 活动
      currentActivityLabelList: [],
      goldAreaList: [],
      labelList: [],
      labelId: '',
      swiperList: [], //轮播列表
      sortType: 'sort', //需要排序的字段 上新 = onsale_time、价格 = price、销量 = purchased
      sort: 'desc', //排序方式 asc = 升序、desc = 降序
      showWaterfallFlow: false, //切换排版方式 false = 正常列表 true = 瀑布流
      showScreenPop: false, //是否展示筛选弹窗
      wineTypeList: [], //酒类型列表
      categoryId: '', //酒类型id
      product_category: [0], //酒类型名称 形如 ['干白']
      countryList: [], //国家列表
      countryId: '', //国家id
      country: [0], //国家名称 形如 ['英国']
      regionsList: [],
      regionsId: '',
      regions: [0],
      keywordList: [], //关键字列表
      keywordId: '', //关键字id
      product_keyword: [0], //关键字名称 形如 ['气泡']
      minPrice: '', //最低金额
      newYearTheme: false,
      maxPrice: '', //最高金额
      doFilter: 0, //是否做过筛选 0 = 未做筛选、1 = 做了筛选
      filterIndex: 0, //筛选索引
      filterInfo: {}, //过滤信息
      goodsList: [], //商品列表
      hasGotGoodsList: 0, //是否获取过商品列表数据 0 = 没获取过、1 = 获取过
      adList: [
        // {"id":104,"title":"广告列表-直播","path":0,"sort":0,"status":1,"type":5,"pattern":null,"operator_id":43,"operator_name":"黄雨欣","created_at":"2022-03-17 11:17:57","param":null,"modul":2,"modul_id":14,"ad_path_param":[],"pattern_name":"","client_path":null,"modul_data":{"id":14,"anchor_id":0,"domain_push":"livepush.wineyun.com","domain_name":"live.wineyun.com","app_name":"test3","stream_name":"stream2022022412000023723_lud","start_time":"2022-02-24 12:00:00","end_time":1681671600,"title":"也是有","cover_url":"https://images.wineyun.com/vinehoo/vos/live/微信图片_20211206170413.jpg","source_url":"http://wineyun.com/","num":0,"comment":0,"goods_ids":"","status":1,"robert_num":0,"robert_comment":null,"live_type":1,"video_uri":"","duration":0,"video_start_time":0,"video_end_time":0,"created_time":1644826243,"auth_key":"1648736560-9527-0-702ff40449f18cf11e0e590452a0e29e","share_link":"http://52.83.60.235:10381/web-static/details/liveDetail.html?id=14"}},
        // {"id":70,"title":"广告列表-商品","path":1,"sort":0,"status":1,"type":5,"pattern":1,"operator_id":0,"operator_name":null,"created_at":"2022-03-14 14:28:59","param":null,"modul":4,"modul_id":567,"ad_path_param":[],"pattern_name":"新用户","client_path":null,"modul_data":{"country":"法国 France","onsale_status":2,"vest_purchased":0,"note_number":0,"buyer_name":"夏静","operation_review_name":"熊俊杰","is_hidden_price":0,"buyer_review_status":3,"onsale_verify_status":1,"product_comment_count":0,"is_presell":0,"price":0.01,"is_channel":0,"is_hot":0,"product_id":"12491,","id":567,"import_type":1,"sold_out_time":"2022-03-18 16:26:06","onsale_review_time":"2022-03-11 16:26:18","horizontal_img":"","created_time":"2022-03-11 16:18:22","periods_set_count":2,"is_supplier_delivery":0,"virtual_comment_count":0,"sort":0,"periods_comment_count":0,"limit_number":6,"predict_shipment_time":"2022-03-31 00:00:00","purchased":2,"saled_count":2,"banner_img":"https://images.wineyun.com/vinehoo/goods-images/1646986658594bHJFm6B8G_b.png","market_price":199,"praise_count":0,"supplier_id":3,"is_support_coupon":1,"is_support_reduction":1,"title":"1617闪购商品","inventory":1,"capacity":"750ml","brief":"1617闪购商品","order_count":2,"unlimited":0,"sell_time":"2022-03-11 16:26:07","is_delete":0,"operation_name":"石家诚","is_cold_chain":0,"onsale_review_status":3,"product_keyword":"","onsale_time":"2022-03-11 16:26:06","is_support_ts":1,"creator_name":"熊俊杰","copywriting_review_status":2,"product_category":"干红葡萄酒","quota_rule":"{\"check_addr\":false,\"check_level\":false,\"check_time\":false,\"quota_type\":\"1\",\"quota_number\":9999,\"register_time\":\"\",\"min_level\":\"\",\"max_level\":\"\",\"addrs\":[],\"district\":\"\",\"rank\":\"\"}","periods_type":0}},
        // {"id":64,"title":"广告列表-酒会","path":1,"sort":30,"status":1,"type":5,"pattern":1,"operator_id":0,"operator_name":null,"created_at":"2022-03-07 12:06:02","param":null,"modul":1,"modul_id":722,"ad_path_param":[],"pattern_name":"新用户","client_path":null,"modul_data":{"company_phone":"1234323454","fee":12,"description":"<p style=\"text-align: left;\">1 &nbsp;1 &nbsp;我的是参加</p>\n<p style=\"text-align: left;\"><span style=\"text-decoration: line-through;\"><span style=\"text-decoration: underline;\"><em><strong>多久能就</strong></em></span></span></p>","stime":"2021-12-14 18:10:04","avatar_image":"/vinehoo/user/avatar/oz2t_1646107866848.jpg?w=1023&h=1035","source":4,"title":"999","uid":1,"district_name":"涪陵区","city_name":"重庆市","update_time":"2022-02-07 11:39:16","is_pure_pt":0,"nickname":"团团子","from":6,"id":722,"nums":0,"is_vh_show":1,"created_time":"2021-12-14 18:10:33","address":"重庆","thumb_image":"https://images.wineyun.com/vinehoo/vos/wineparty/windows-xp-3840x2160-bliss-microsoft-4k-23534.jpeg","share_nums":0,"endtime":"2021-12-14 18:10:02","sort":0,"is_top":1,"province_name":"重庆","company_user":"1","longitude_latitude":"29.703113,107.389298","province_id":23,"diggnum":0,"company_name":"重庆","etime":"2021-12-23 18:10:06","c_type":0,"district_id":2493,"is_cash":0,"desc_img":"","status":1,"city_id":271,"activity_time":"12.14(周二)18:10至12.23(周四)18:10","last_num":1,"money":0,"share_link":"https://h5.wine-talk.cn/web-static/details/winePartyDetail.html?id=722","packages":[{"id":999,"party_id":722,"package_name":"1","type":1,"money":0,"limitnum":1,"apply_num":0,"group_num":0,"group_countdown":0,"group_require":0,"update_time":"2021-12-28 14:25:37","created_time":"2021-12-28 14:25:37"}]}},
        // {"id":65,"title":"广告列表-直播","path":1,"sort":1,"status":1,"type":5,"pattern":1,"operator_id":0,"operator_name":null,"created_at":"2022-03-07 12:06:02","param":null,"modul":2,"modul_id":14,"ad_path_param":[],"pattern_name":"新用户","client_path":null,"modul_data":{"id":14,"anchor_id":0,"domain_push":"livepush.wineyun.com","domain_name":"live.wineyun.com","app_name":"test3","stream_name":"stream2022022412000023723_lud","start_time":"2022-02-24 12:00:00","end_time":1681671600,"title":"也是有","cover_url":"https://images.wineyun.com/vinehoo/vos/live/微信图片_20211206170413.jpg","source_url":"http://wineyun.com/","num":0,"comment":0,"goods_ids":"","status":1,"robert_num":0,"robert_comment":null,"live_type":1,"video_uri":"","duration":0,"video_start_time":0,"video_end_time":0,"created_time":1644826243,"auth_key":"1648736561-9527-0-cc84d9ea19a082f83718a893df73cef9","share_link":"http://52.83.60.235:10381/web-static/details/liveDetail.html?id=14"}}
      ], //广告列表
      page: 1, //第几页
      limit: 10, //每页显示多少条
      totalPage: 1, //总页数
      loadStatus: 'loadmore', //底部加载状态
      buryDotParams: {
        channel: 3,
        region_id: 203000,
      },
      screenPopMaxHeight: 0,
    }
  },

  onLoad(options) {
    this.secondConfig()
    console.log('this', this)
    this.system.setNavigationBarBlack()
    this.acceptParams(options)
    this.initOnLoad()
    const { windowHeight, screenHeight } = this.system.getSysInfo()
    this.screenPopMaxHeight = `${(windowHeight || screenHeight) * 0.8}px`

    // this.$u.api.indexCombine({ client: 3 }).then(res => {
    // 	this.goldAreaList = res.data.goldArea.list.slice(0, 8)
    // })
  },
  mounted() {
    setInterval(() => {
      const existingData = uni.getStorageSync('visibleGoods') || []
      let newData = [...existingData, ...this.visibleGoods]
      uni.setStorageSync('visibleGoods', newData)
      this.visibleGoods = []
    }, 3000)
  },
  onShow() {
    this.login.isLoginV3(this.$vhFrom, 0).then((isLogin) => {
      this.userIsLogin = isLogin
    })
    this.initOnShow()
  },
  beforeDestroy() {
    if (this.logInterval) {
      clearInterval(this.logInterval)
      this.logInterval = null
    }
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable', 'dev']),

    // 获取导航栏高度
    getNavigationBarHeight() {
      return 47
      // return this.system.navigationBarHeight()
    },

    isLiquorActivity({ channelActivityId }) {
      // return channelActivityId === LIQUOR_ACTIVITY_ID()
      if (channelActivityId == 0 || channelActivityId == '0') {
        return false
      } else {
        return true
      }
      //   return !!channelActivityId
    },
    isChooseWineActivity({ channelActivityId }) {
      // return channelActivityId === CHOOSE_WINE_ACTIVITY_ID()
      return false
    },
    navbarBackground({ isLiquorActivity, isChooseWineActivity }) {
      if (isLiquorActivity) return '#1C1C1C'
      return isChooseWineActivity ? '#C9C7F9' : '#fff'
    },
    navbarBorder({ isLiquorActivity, isChooseWineActivity }) {
      if (isLiquorActivity) return '1px solid #1C1C1C'
      return isChooseWineActivity ? '1px solid #C9C7F9' : '1px solid #EEEEEE'
    },
    logoIconSrc({ isLiquorActivity, isChooseWineActivity }) {
      if (isLiquorActivity || isChooseWineActivity) return this.ossIcon('/logo1.png')
      return this.ossIcon('/logo2.png')
    },
    searchIconSrc({ isLiquorActivity }) {
      if (isLiquorActivity) return this.ossIcon(`/second_hair/s_sea_white_36.png`)
      return this.ossIcon(`/second_hair/s_sea_36.png`)
    },
    shoppingCartIconSrc({ isLiquorActivity }) {
      if (isLiquorActivity) return this.ossIcon(`/second_hair/s_car_white_36.png`)
      return this.ossIcon(`/second_hair/s_car_36.png`)
    },
    msgIconSrc({ isLiquorActivity }) {
      if (isLiquorActivity) return this.ossIcon(`/second_hair/s_not_white_36_38.png`)
      return this.ossIcon(`/second_hair/s_not_36_38.png`)
    },
    tabbarList({ isLiquorActivity }) {
      console.log(this.$vhFrom)
      let list = [
        {
          iconPath: `https://images.vinehoo.com/vinehoomini/v3/tab_bar/${
            isLiquorActivity ? 'index_gray1' : 'index_black'
          }.png`,
          selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/index_red.png',
          text: '首页',
          customIcon: false,
          pagePath: '/pages/index/index',
        },
        {
          iconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/fla_pur_black.png',
          selectedIconPath: `https://images.vinehoo.com/vinehoomini/v3/tab_bar/${
            isLiquorActivity ? 'fla_pur_orange' : 'fla_pur_red'
          }.png`,
          text: '闪购',
          customIcon: false,
          pagePath: '/pages/flash-purchase/flash-purchase',
        },
        // {
        //   iconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/community-unclick.png',
        //   selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/community-click.png',
        //   midButton: true,
        //   customIcon: false,
        //   pagePath: '/pages/community/community',
        // },
        {
          iconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/community-unclick.png',
          selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/community-click.png',
          midButton: true,
          customIcon: false,
          pagePath: '/pages/community/community',
        },
        {
          iconPath: this.newYearTheme
            ? 'http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/tabbar-NewYear-sel.gif'
            : `https://images.vinehoo.com/vinehoomini/v3/tab_bar/${
                isLiquorActivity ? 'new_sec_hair_gray1' : 'new_sec_hair_black'
              }.png`,
          selectedIconPath: this.newYearTheme
            ? 'http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/tabbar-NewYear-sel.gif'
            : 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/new_sec_hair_red.png',
          text: this.newYearTheme ? '年货节' : '现货速发',
          customIcon: false,
          pagePath: '/pages/miaofa/miaofa',
        },
        {
          iconPath: `https://images.vinehoo.com/vinehoomini/v3/tab_bar/${
            isLiquorActivity ? 'mine_gray1' : 'mine_black'
          }.png`,
          selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/mine_red.png',
          text: '我的',
          customIcon: false,
          pagePath: '/pages/mine/mine',
        },
      ]
      if (this.$vhFrom !== 'next') {
        list = [
          {
            iconPath: `https://images.vinehoo.com/vinehoomini/v3/tab_bar/${
              isLiquorActivity ? 'index_gray1' : 'index_black'
            }.png`,
            selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/index_red.png',
            text: '首页',
            customIcon: false,
            pagePath: '/pages/index/index',
          },
          {
            iconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/fla_pur_black.png',
            selectedIconPath: `https://images.vinehoo.com/vinehoomini/v3/tab_bar/${
              isLiquorActivity ? 'fla_pur_orange' : 'fla_pur_red'
            }.png`,
            text: '闪购',
            customIcon: false,
            pagePath: '/pages/flash-purchase/flash-purchase',
          },
          // {
          //   iconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/community-unclick.png',
          //   selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/community-click.png',
          //   midButton: true,
          //   customIcon: false,
          //   pagePath: '/pages/community/community',
          // },

          {
            iconPath: this.newYearTheme
              ? 'http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/tabbar-NewYear-sel.gif'
              : `https://images.vinehoo.com/vinehoomini/v3/tab_bar/${
                  isLiquorActivity ? 'new_sec_hair_gray1' : 'new_sec_hair_black'
                }.png`,
            selectedIconPath: this.newYearTheme
              ? 'http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/tabbar-NewYear-sel.gif'
              : 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/new_sec_hair_red.png',
            text: this.newYearTheme ? '年货节' : '现货速发',
            customIcon: false,
            pagePath: '/pages/miaofa/miaofa',
          },
          {
            iconPath: `https://images.vinehoo.com/vinehoomini/v3/tab_bar/${
              isLiquorActivity ? 'mine_gray1' : 'mine_black'
            }.png`,
            selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/mine_red.png',
            text: '我的',
            customIcon: false,
            pagePath: '/pages/mine/mine',
          },
        ]
      }
      return list
    },
    liquorActivityGoodsList({ goodsList, currentActivityLabelList }) {
      return goodsList.map((item) => {
        const { name = '', label_color = '' } =
          this.goldAreaList.find(({ id }) => id === (item.column_id ? item.column_id[0] : '')) || {}
        return {
          ...item,
          $activityLabelText: name,
          $activityLableTextBg: label_color
            ? `linear-gradient(180deg, ${label_color[0]} 0%, ${label_color[1] || label_color[0]} 100%)`
            : '',
        }
      })
    },
    isKj({ currentChannel }) {
      return currentChannel === 2
    },
    goldAreaCols({ goldAreaList }) {
      if (![4, 5, 8, 10].includes(goldAreaList.length)) return []
      switch (goldAreaList.length) {
        case 8:
          return goldAreaList.slice(0, 4).map((item, index) => [item, goldAreaList[index + 4]])
        case 10:
          return goldAreaList.slice(0, 5).map((item, index) => [item, goldAreaList[index + 5]])
        default:
          return goldAreaList.map((item) => [item])
      }
    },
    sortList({ isKj }) {
      return [
        { index: 1, name: '价格', type: 'price' },
        { index: 2, name: '上新', type: 'onsale_time' },
        { index: 3, name: '销量', type: 'purchased' },
      ]
      //   .concat(isKj ? [{ index: 4, name: '发货时间', type: 'predict_shipment_time' }] : [])
    },
  },
  watch: {},

  methods: {
    // 商品滚动监听
    handleGoodsScroll(e) {
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this)
        this.goodsList.forEach((item, index) => {
          query
            .select(`#goods_${index}`)
            .boundingClientRect((data) => {
              if (data && data.top <= window.innerHeight && data.bottom >= 0) {
                let uid = ''
                let device = ''
                const userinfo = uni.getStorageSync('loginInfo') || '{}'
                if (userinfo && userinfo.uid) {
                  uid = userinfo.uid
                } else {
                  uid = uni.getStorageSync('uniqueId')
                }
                if (this.$vhFrom == 'next') {
                  device = 'hm'
                } else if (this.$vhFrom == '1') {
                  device = 'android'
                } else if (this.$vhFrom == '2') {
                  device = 'ios'
                } else {
                  device = 'h5'
                }
                const goodsItem = {
                  uid: String(uid),
                  created_time: new Date().getTime(),
                  metric_name: 'period_exposure',
                  device,
                  period: Number(item.id),
                  period_type: Number(item.periods_type),
                }

                // 基于ID去重
                const exists = this.visibleGoods.some((v) => v.period === item.id)
                if (!exists) {
                  this.visibleGoods.push(goodsItem)
                }
              }
            })
            .exec()
        })
      })
    },

    async secondConfig() {
      // 优先从本地存储读取配置
      const localConfig = uni.getStorageSync('tabbarThemeConfig')
      if (localConfig) {
        this.newYearTheme = localConfig.isopen
        return
      }

      //   // 如果本地没有配置，则从服务器获取
      //   const res = await this.$u.api.secondConfig()
      //   this.newYearTheme = res.data.isopen
    },

    JumpColumn(item) {
      let data = {
        channel: '',
        genre: 3,
        region_id: '',
        button_id: item.id,
      }
      if (this.currentChannel === 0 || this.currentChannel === 2 || this.currentChannel === 3) {
        data.channel = 3
      } else if (this.currentChannel === 1) {
        data.channel = 4
      }
      switch (this.currentChannel) {
        case 0:
          if (this.isLiquorActivity) {
            data.region_id = 205000
          } else {
            data.region_id = 204000
          }
          break
        case 1:
          data.region_id = 306000
          break
        case 2:
          data.region_id = 206000
          break
        case 3:
          data.region_id = 207000
          break
        default:
          break
      }
      this.$u.api.reportBuryDot({
        data: [data],
      })
      if (!item.page_mode) {
        let path = ''
        if (!this.dev) {
          path = 'https://activity-cdn.vinehoo.com'
        } else {
          path = 'https://test-activity.wineyun.com'
        }
        this.jump.h5Jump(path + item.path, this.$vhFrom, 4)
      } else {
        this.handleJump(`${this.routeTable.pgMiaoFaCard}?cid=${item.id}&type=colum`, '')
      }
    },
    acceptParams(options) {
      if (options.initChannel) this.initChannel = parseInt(options.initChannel)
    },

    // 初始化onload（接口聚合:聚合闪购频道列表、闪购列表...）loadingType = 加载类型 0 = 默认加载 1 = 下拉加载
    async initOnLoad(type = 0) {
      try {
        if (type == 0) await Promise.all([this.getChannelList(), this.getSwiperList()]) //仅在初始化时调用

        //判断所属频道（0 = 闪购、1 = 秒发、2 = 跨境、3 = 尾货（如果是闪购频道 && 商品列表 && 不能够筛选 && 过滤索引为0 则请求默认的闪购列表，其他则请求筛选列表））
        if (
          this.currentChannel == 0 &&
          this.currentChannelType == 0 &&
          this.filterIndex == 0 &&
          !this.doFilter &&
          !this.labelId
        ) {
          await this.getFlashIndexGoodsList()
        } else {
          await this.getFlashGoodsList()
        }
        await this.getAggregateData()
        uni.stopPullDownRefresh() //停止下拉刷新
        this.loading = false
      } catch (e) {
        //TODO handle the exception
        console.log(e)
      }
    },

    // 初始化onShow（接口聚合:消息通知、购物车数量...）
    async initOnShow() {
      try {
        await Promise.all([this.getShoppingCartNum(), this.getMessageUnreadNum()])
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 获取购物车数量
    async getShoppingCartNum() {
      if (this.login.isLogin(this.from, 0)) {
        let res = await this.$u.api.shoppingCartNum()
        console.log('-----------------------------我是购物车数量接口')
        console.log(res)
        this.shoppingCartNum = res.data
      }
    },

    // 获取未读消息数据
    async getMessageUnreadNum() {
      if (this.login.isLogin(this.from, 0)) {
        let res = await this.$u.api.messageUnreadNum()
        this.unReadTotalNum = res.data.total_num
      }
    },

    // 获取频道列表
    async getChannelList() {
      let res = await this.$u.api.flashChannelList()
      let { list } = res.data
      // 从签到页跳转过来传递的参数
      if (list.length) {
        this.channelList = list.map((item) => {
          if (item.icon) {
            item.iconWidth = 104
            item.iconHeight = 36
            // if (item.activity_id === CHOOSE_WINE_ACTIVITY_ID()) {
            // 	item.iconWidth = 170
            // 	item.iconHeight = 64
            // } else if (item.activity_id === LIQUOR_ACTIVITY_ID()) {
            // 	item.iconWidth = 76
            // 	item.iconHeight = 34
            // }
            const params = item.icon.split('?')?.[1] || ''
            const vars = params.split('&')
            const searchParams = {}
            vars.forEach((varStr) => {
              const [key, value] = varStr.split('=')
              searchParams[key] = value
            })
            const w = +(searchParams?.w || '')
            const h = +(searchParams?.h || '')
            if (!isNaN(w) && !isNaN(h) && w && h) {
              item.iconWidth = w
              item.iconHeight = h
            }
          }
          return item
        }) //频道列表
        // if( this.initChannel == 2 ) { //需要初始化显示跨境频道的数据
        // 	channelObj = list.find( v => v.channel == this.initChannel )
        // } else { //默认显示
        // 	channelObj = list[0]
        // }
        let channelObj = list.find((v) => v.channel == this.initChannel && v.type == 0) //找出对应频道、目前不支持初始化活动选中
        let { channel, id, type, activity_id, activity_label = [] } = channelObj || list[0]
        console.log('-----------------------------------我是频道对象')
        console.warn(channelObj)
        this.currentChannel = channel //频道
        this.channelId = id //频道id
        this.currentChannelType = type //频道类型（用于显示活动商品、还是普通商品）
        this.channelActivityId = activity_id //活动id（频道类型为活动所需要的id）
        this.currentActivityLabelList = activity_label
        this.getAggregateData()
      }
    },

    // 获取轮播列表
    async getSwiperList() {
      // let res = await this.$u.api.advertisingList({ type:1, channel:1 })
      // this.swiperList = res.data.list
      // console.log(res)
    },

    // 获取闪购商品初始化列表（默认）
    async getFlashIndexGoodsList() {
      if (this.hasGotGoodsList) this.feedback.loading() //是否已经获取过商品列表数据
      let data = {} // 接口需要上传的数据
      data.page = this.page //第几页
      data.limit = this.limit // 每页限制多少条
      // data.periods_type = [this.currentChannel] //频道
      // data.sort_type = this.sortType //商品需要排序的字段
      // data.order = this.sort //排序方式 asc = 升序、desc = 降序
      // data.filters = this.filterInfo //过滤信息
      // if(this.minPrice !== '' && this.maxPrice !== '' ) { //价格区间（用户选择了价格后才传参）
      // 	data.price_gte = parseInt(this.minPrice) //最低价（取整）
      // 	data.price_lte = parseInt(this.maxPrice) //最高价（取整）
      // }
      let res = await this.$u.api.flashIndexGoodsList(data) //获取闪购商品初始化列表（默认）
      this.hasGotGoodsList = 1
      let { total, list } = res.data
      if (this.page == 1) await this.getAdList() //获取广告位数据
      if (this.page == 1) {
        this.goodsList = list
      } else {
        this.goodsList = this.mergeUnique(this.goodsList, list)
      }
      this.totalPage = Math.ceil(total / this.limit)
      this.insertAd() //插入广告位
      this.loadStatus = this.page == this.totalPage || !this.totalPage ? 'nomore' : 'loadmore'
      this.feedback.hideLoading()
    },

    //  获取闪购商品列表（筛选）
    async getFlashGoodsList(
      tabItem = {
        type: this.currentChannelType,
        channel: this.currentChannel,
        activity_id: this.channelActivityId,
        activity_label_id: this.channelActivityLabelId,
        labelId: this.labelId,
      }
    ) {
      this.feedback.loading()
      let data = {} // 接口需要上传的数据
      data.page = this.page //第几页
      data.limit = this.limit // 每页限制多少条
      if (tabItem.type == 0) {
        data.periods_type = [tabItem.channel] //频道
      } else {
        // data.activity_list = [tabItem.activity_id] //活动id
        data.activity_list = [-1] //活动id
        if (tabItem.activity_label_id) data.activity_label_list = [tabItem.activity_label_id]
      }
      data.sort_type = this.sortType //商品需要排序的字段
      data.order = this.sort //排序方式 asc = 升序、desc = 降序
      data.filters = this.filterInfo //过滤信息
      if (this.minPrice) data.price_gte = parseFloat(this.minPrice)
      if (this.maxPrice) data.price_lte = parseFloat(this.maxPrice)
      if (this.minPrice && this.maxPrice && data.price_gte > data.price_lte) {
        ;[data.price_gte, data.price_lte] = [data.price_lte, data.price_gte]
      }
      if (tabItem.labelId) data.label_list = [tabItem.labelId]
      let res = await this.$u.api.flashGoodsList(data) // 获取闪购商品列表（筛选）
      let { total, list } = res.data
      if (this.page == 1) {
        this.goodsList = list
      } else {
        this.goodsList = this.mergeUnique(this.goodsList, list)
      }
      this.totalPage = Math.ceil(total / this.limit)
      this.loadStatus = this.page == this.totalPage || !this.totalPage ? 'nomore' : 'loadmore'
      this.feedback.hideLoading()
    },
    mergeUnique(goodsList, list) {
      const map = new Map()
      // 添加现有商品到 Map
      goodsList.forEach((item) => {
        map.set(item.id, item)
      })
      // 添加新列表中的商品到 Map，如果已有相同 id 的商品，则会被跳过
      list.forEach((item) => {
        if (!map.has(item.id)) {
          map.set(item.id, item)
        }
      })
      // 将 Map 转换回数组
      return Array.from(map.values())
    },
    // 获取广告位数据
    async getAdList() {
      let res = await this.$u.api.advertisingList({ type: 5, channel: 1, client: 2 }) //获取广告数据
      this.adList = res.data.list.filter((v) => {
        return v.modul_data != null
      }) //过滤不合法数据
      this.adList.forEach((item) => {
        if (item.modul === 4) {
          item.modul_data.quota_rule = JSON.parse(item.modul_data.quota_rule) || {}
        }
      })
      this.adList = this.adList.sort((a, b) => {
        return a.sort - b.sort
      }) //数组排序
    },

    // 清除瀑布流数据
    clearWaterfallList() {
      if (this.showWaterfallFlow) {
        console.log('-------------------------this.$refs.uWaterfall')
        console.log(this.$refs.uWaterfall)
        if (this.$refs.uWaterfall) {
          this.$refs.uWaterfall.clear()
        }
      }
    },

    // 切换tabs item = 频道每一项
    async changeChannel(item) {
      // if(type == 1) {
      // 	// this.jump.navigateTo(`${this.routeTable.pFWebView}?url=${encodeURIComponent(item.path)}`)
      // 	this.feedback.toast({ title:'我需要调达哥的活动列表~' })
      // }else{
      // 	this.clearWaterfallList()
      // 	this.page = 1
      // 	channel == 0 ? this.getFlashIndexGoodsList() : this.getFlashGoodsList()
      // }
      let { id, channel, type, activity_id, activity_label = [] } = item
      this.clearWaterfallList()
      this.page = 1
      this.totalPage = 0
      const { filterIndex, sortType, sort } = this.$options.data()
      this.filterIndex = filterIndex
      this.sortType = sortType
      this.sort = sort
      this.categoryId = ''
      this.product_category = [0]
      this.countryId = ''
      this.country = [0]
      this.keywordId = ''
      this.product_keyword = [0]
      this.regionsId = ''
      this.regions = [0]
      this.filterInfo = {}
      this.minPrice = ''
      this.maxPrice = ''
      this.doFilter = 0
      channel == 0 && type == 0 && !this.filterIndex && !this.doFilter
        ? await this.getFlashIndexGoodsList()
        : await this.getFlashGoodsList(item)
      this.scrollToTop()
      this.channelId = id
      this.channelActivityId = activity_id
      this.channelActivityLabelId = this.$options.data().channelActivityLabelId
      this.currentChannel = channel
      this.currentChannelType = type
      this.currentActivityLabelList = activity_label
      this.labelId = this.$options.data().labelId
      this.$u.api.reportBuryDot({
        data: [
          {
            channel: 3,
            genre: 3,
            region_id: 201000,
            button_id: id,
          },
        ],
      })
      this.getAggregateData()
    },

    // 排序筛选!
    async changeSort(index) {
      this.clearWaterfallList()
      this.page = 1
      this.totalPage = 0
      switch (index) {
        case 0:
          this.filterIndex = index
          this.sortType = 'sort'
          this.sort = 'desc'
          // this.getFlashIndexGoodsList()
          this.currentChannel == 0 && this.currentChannelType == 0 && !this.doFilter && !this.labelId
            ? await this.getFlashIndexGoodsList()
            : await this.getFlashGoodsList()
          break
        case 1:
        case 2:
        case 3:
        case 4:
          this.sortType = this.sortList[index - 1].type
          if (index !== 1) {
            this.sort = 'desc'
          } else {
            if (this.filterIndex === index) {
              this.sort == 'desc' ? (this.sort = 'asc') : (this.sort = 'desc')
            } else {
              this.sort = 'asc'
            }
          }

          this.filterIndex = index
          await this.getFlashGoodsList()
          break
      }
      //   this.scrollToTop()
    },

    // 获取筛选列表（类型、国家、关键字）
    async getFilterList() {
      try {
        this.feedback.loading()
        let res = null
        if (this.isLiquorActivity) {
          res = await this.$u.api.getFlashLiquorFilterList()
        } else if (this.isKj) {
          res = await this.$u.api.getFlashKjFilterList()
        } else {
          res = await this.$u.api.flashFilterList()
        }
        const list = res.data.list
        const typeToList = list.reduce(
          (prev, curr) => ({
            ...prev,
            [curr.type]: [...(prev[curr.type] || []), curr],
          }),
          {}
        )
        const { 1: countryList = [], 2: wineTypeList = [], 3: keywordList = [], 5: regionsList = [] } = typeToList
        this.countryList = countryList
        this.wineTypeList = wineTypeList
        this.keywordList = keywordList
        this.regionsList = regionsList
        this.showScreenPop = true
      } catch (err) {
        this.feedback.hideLoading()
      }
    },

    // 选中筛选内容 typeName = 需要过滤的类型名称 类型 = product_category、国家 = country、关键字 = product_keyword、item = 筛选类型列表某一项
    selectFilterItem(typeName, typeId, item) {
      if (this[typeId] == item.id) {
        this[typeName] = [0]
        this[typeId] = ''
        delete this.filterInfo[typeName]
      } else {
        this[typeName] = [item.name]
        this[typeId] = item.id
        this.filterInfo[typeName] = [item.name]
      }
    },

    // 确认筛选
    async confirmFilter() {
      if (this.isLiquorActivity) {
        if (this.categoryId || this.countryId || this.keywordId || this.regionsId || this.minPrice || this.maxPrice) {
          this.doFilter = 1
        } else {
          this.doFilter = 0
        }
      } else {
        if (this.categoryId || this.countryId || this.keywordId || this.regionsId || this.minPrice || this.maxPrice) {
          this.doFilter = 1
        } else {
          this.doFilter = 0
        }
      }
      this.page = 1
      this.totalPage = 1
      this.clearWaterfallList()
      this.currentChannel == 0 && this.currentChannelType == 0 && this.filterIndex == 0 && !this.doFilter
        ? await this.getFlashIndexGoodsList()
        : await this.getFlashGoodsList()
      this.showScreenPop = false
      this.scrollToTop()
    },

    // 重置筛选
    async resetFilter() {
      this.categoryId = ''
      this.product_category = [0]
      this.countryId = ''
      this.country = [0]
      this.keywordId = ''
      this.product_keyword = [0]
      this.regionsId = ''
      this.regions = [0]
      this.filterInfo = {}
      this.minPrice = ''
      this.maxPrice = ''
    },

    // 插入广告位 res = 广告位数据
    insertAd() {
      if (this.adList.length) {
        const minSort = (this.page - 1) * this.limit
        const maxSort = this.page * this.limit
        const adList = this.adList.filter((item) => {
          if (this.page === this.totalPage) {
            return item.sort > minSort
          }
          return item.sort > minSort && item.sort <= maxSort
        })
        console.log('adList', adList)
        const currInsertAdNum = this.goodsList.filter((item) => item.modul).length
        const goodsList = [...this.goodsList.filter((item) => !item.modul)]
        adList.forEach((adItem, adIndex) => {
          const findIndex = goodsList.findIndex((item, index) => !item.modul && adItem.sort < index + 1)
          if (findIndex === -1) {
            this.goodsList.push(adItem)
          } else {
            this.goodsList.splice(findIndex + currInsertAdNum + adIndex, 0, adItem)
          }
        })
      }
    },

    onListLayoutChange() {
      this.showWaterfallFlow = !this.showWaterfallFlow
      this.$u.api.reportBuryDot({
        data: [
          {
            channel: 3,
            genre: 3,
            region_id: 204000,
            button_id: 1,
          },
        ],
      })
      this.scrollToTop()
    },

    scrollToTop() {
      this.$nextTick(() => {
        this.system.pageScrollTo(0, 0)
      })
    },

    // 刷新页面
    refreshPage() {
      this.page = 1
      this.totalPage = 1
      this.clearWaterfallList()
      this.initOnLoad(1)
    },
    onActivityLabelItemClick(item) {
      this.page = 1
      if (this.channelActivityLabelId === item.id) {
        this.channelActivityLabelId = this.$options.data().channelActivityLabelId
      } else {
        this.channelActivityLabelId = item.id
      }
      this.getFlashGoodsList()
    },
    onLabelItemClick(item) {
      if (this.labelId === item.label_id) {
        this.labelId = this.$options.data().labelId
      } else {
        this.labelId = item.label_id
      }
      this.page = 1
      if (this.currentChannel === 0 && this.labelId === '' && !this.filterIndex) {
        this.getFlashIndexGoodsList()
      } else {
        this.getFlashGoodsList()
      }
    },
    getAggregateData() {
      this.swiperList = []
      this.goldAreaList = []
      this.labelList = []
      let channel = 1
      console.warn(this.channelActivityId)
      if (Number(this.channelActivityId) !== 0) {
        channel = 7
      } else {
        switch (this.currentChannel) {
          case 0:
            channel = 1
            break
          case 2:
            channel = 8
            break
          case 3:
            channel = 9
            break
        }
      }
      console.log(channel)
      this.$u.api.getFlashAggregateData({ channel, client: 3 }).then((res) => {
        console.log('getFlashAggregateData', res.data)
        const { banner = [], column = [], tag = [] } = res.data
        this.swiperList = banner
        this.goldAreaList = column
        this.labelList = tag
      })
    },

    // 添加新方法处理滚动到底部
    onScrollToLower() {
      if (this.page == this.totalPage || this.totalPage == 0) return
      this.loadStatus = 'loading'
      this.page++
      this.currentChannel == 0 &&
      this.currentChannelType == 0 &&
      !this.canFilter &&
      this.filterIndex == 0 &&
      !this.labelId &&
      !this.doFilter
        ? this.getFlashIndexGoodsList()
        : this.getFlashGoodsList()
    },
  },

  onPullDownRefresh() {
    this.refreshPage()
  },

  onShareAppMessage(res) {
    return {
      title: '酒云网 与百万发烧友一起淘酒',
      path: this.routeTable.pgFlashPurchase,
      imageUrl: this.swiperList[0].image,
    }
  },

  onPageScroll(res) {
    this.scrollTop > res.scrollTop ? (this.channelListSticky = true) : (this.channelListSticky = false)
    this.scrollTop = res.scrollTop
  },

  onReachBottom() {
    if (this.page == this.totalPage || this.totalPage == 0) return
    this.loadStatus = 'loading'
    this.page++
    this.currentChannel == 0 &&
    this.currentChannelType == 0 &&
    !this.canFilter &&
    this.filterIndex == 0 &&
    !this.labelId &&
    !this.doFilter
      ? this.getFlashIndexGoodsList()
      : this.getFlashGoodsList()
  },
}
</script>

<style>
page {
  height: 100%;
}
</style>

<style lang="scss" scoped>
// .flash-purchase {
// 	::v-deep .vh-navbar {
// 		&-inner,
// 		&-placeholder {
// 			height: 92rpx !important;
// 		}
// 	}
// }

.choose-wine-activity {
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    @include size(100%);
    background: linear-gradient(180deg, #c9c7f9 0%, rgba(205, 211, 243, 0.73) 100%);
  }
}

.bg-liquor-filter-item {
  @include iconBgImg('/flash_purchase/liquor/filter_item_bg_h1.png');
  background-size: 216rpx 70rpx;
}
</style>
