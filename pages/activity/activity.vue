<template>
  <view class="activity">
    <!-- <view class="activity__navbar">
      <u-navbar height="52" :is-back="true" title="酒云网" title-color="#333" :title-size="32" :title-bold="true" />
    </view> -->
    <view v-if="loading && activityDetail" class="activity__content" :style="contentStyle">
      <view class="activity__inner">
        <image v-if="activityDetail.wine_party_img" :src="activityDetail.wine_party_img" mode="widthFix" class="activity__img" />
        <view class="activity__tt" :style="ttStyle">
          <view v-if="activityDetail.active_name" class="activity__title">{{ activityDetail.active_name }}</view>
          <view v-if="activityDetail.effected_time && activityDetail.invalidate_time" class="activity__time">{{ activityDetail.effected_time | toTime }} - {{ activityDetail.invalidate_time | toTime }}</view>
        </view>
        <activity-goods-list :list="list" class="activity__goods-list" />
      </view>
    </view>
    <view class="activity__home" @click.stop="jump.reLaunch(routeTable.pgIndex)">
      <image src="https://images.vinehoo.com/vinehoomini/v3/activity/activity_home.png" class="activity__home-img" />
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

const PAGE_SIZE = 10

export default {
  name: 'activity',
  data: () => ({
    loading: false,
    query: {
      wineparty_adid: 0,
      page: 1,
      limit: PAGE_SIZE,
      onsale_status_ids: 2
    },
    activityDetail: {},
    toatalPage: 0,
    list: []
  }),
  computed: {
    ...mapState(['routeTable']),
    contentStyle ({ activityDetail }) {
      return { background: activityDetail.theme_color }
    },
    ttStyle ({ activityDetail: { wine_party_img } }) {
      const styleObj = {}
      if (wine_party_img) {
        Object.assign(styleObj, {
          position: 'absolute',
          top: '0',
          left: '0'
        })
      }
      return styleObj
    }
  },
  filters: {
    toTime (input) {
      const index = input.indexOf('-')
      const lastIndex = input.lastIndexOf(':')
      return input.slice(index + 1, lastIndex)
    }
  },
  methods: {
    queryActivityDetail () {
      const params = {
        id: this.query.wineparty_adid
      }
      return this.$u.api.winepartyActivityDetail(params).then(res => {
        this.activityDetail = res?.data || {}
        return res
      })
    },
    queryActivityGoodsList () {
      const { wineparty_adid, page, limit, onsale_status_ids } = this.query
      const params = {
        invite_activity_id: wineparty_adid,
        page,
        limit,
        onsale_status_ids
      }
      return this.$u.api.winepartyActivityGoodsList(params).then(res => {
        const { list = [], total = 0 } = res?.data || {}
        list.forEach(item => {
          item.quota_rule = JSON.parse(item.quota_rule || '{}')
        })
        this.list = this.list.concat(list)
        this.totalPage = Math.ceil(total / PAGE_SIZE)
        return res
      })
    },
    load () {
      this.loading = false
      Promise.all([
        this.queryActivityDetail(),
        this.queryActivityGoodsList()
      ]).then(() => {
        this.loading = true
      })
    }
  },
  onLoad(options) {
    const { id = 0 } = options
    this.query.wineparty_adid = id
    if (!id) return
    this.load()
  },
  onReachBottom() {
    if (this.query.page === this.totalPage || !this.totalPage) return
    this.query.page++
    this.queryActivityGoodsList()
  }
}
</script>

<style lang="scss" scoped>
  .activity {
    @include flex-col(flex-start, stretch);
    height: 100vh;

    &__navbar {
      flex-shrink: 0;
      height: 104rpx;
    }

    &__content {
      flex: 1;
      background: #DE587F;
    }

    &__inner {
      position: relative;
      padding: 0 0 80rpx;
    }

    &__img {
      width: 100%;
    }

    &__tt {
      padding: 0 24rpx;
      margin: 80rpx 0 0;
      width: 100%;
    }

    &__title {
      @include font(500, 72rpx, #fff);
      line-height: 100rpx;
      text-shadow: 0px 4rpx 10rpx rgba(0,0,0,0.0900);
      text-align: center;
    }

    &__time {
      margin-top: 12rpx;
      @include font(500, 32rpx, #fff);
      line-height: 44rpx;
      text-shadow: 0px 4rpx 10rpx rgba(0,0,0,0.0900);
      text-align: center;
    }

    &__goods-list {
      margin: 60rpx 24rpx 0;
    }

    &__home {
      position: fixed;
      bottom: 124rpx;
      right: 14rpx;

      &-img {
        @include size(94rpx);
      }
    }
  }
</style>
