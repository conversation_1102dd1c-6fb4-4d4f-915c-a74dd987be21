<template>
  <view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
    <view v-if="!loading" id="outer-content" :style="{ marginTop: scrollTop === 0 ? statusBarHeight + 'px' : '20px' }">
      <view
        v-if="!from"
        class="h-112 bg-ffffff p-stic z-980 top-0 d-flex a-center j-sb ptb-00-plr-24"
        @click.stop="handleOpenApp"
      >
        <view class="d-flex a-center">
          <image class="w-64 h-64 mr-16" src="/static/logo_64.png" />
          <view>
            <view class="font-26 font-wei-500 text-3 l-h-36">酒云网</view>
            <view class="font-20 text-9 l-h-28">与百万发烧友一起淘酒</view>
          </view>
        </view>
        <u-button
          :hair-line="false"
          :ripple="true"
          ripple-bg-color="#FFF"
          :custom-style="{
            width: '128rpx',
            height: '48rpx',
            background: '#E80404',
            boxShadow: '0rpx 0rpx 8rpx 0rpx rgba(0,0,0,0.2)',
            borderRadius: '30rpx',
            fontSize: '24rpx',
            fontWeight: '500',
            color: '#FFF',
            border: 'none',
            margin: '0',
          }"
          @click.stop="handleOpenApp"
          >打开App</u-button
        >
      </view>

      <!-- 导航栏 -->
      <view
        v-show="scrollTop > 10 && from != '3'"
        class="fade-in p-stic z-980 w-p100 bg-ffffff"
        :class="from ? 'top-0' : 'top-112 bt-s-02-eeeeee'"
      >
        <view :style="{ height: statusBarHeight + 'px' }" />
        <view class="h-92 d-flex a-center j-sb">
          <view class="d-flex a-center">
            <image
              class="w-56 h-56 ptb-18-plr-24"
              :src="
                pageLength <= 1 && !from
                  ? 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_home_bla.png'
                  : 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_bac_bla.png'
              "
              @click="jumpBack()"
            />
            <view>
              <u-tabs
                class="tab-list"
                :list="tabList"
                :is-scroll="false"
                :height="92"
                font-size="32"
                bg-color="transparent"
                :show-bar="false"
                active-color="#333"
                inactive-color="#666"
                :current="tabsCurrent"
                :active-item-style="{ fontSize: '36rpx', fontWeight: 500 }"
                item-width="98"
                @change="changeTabs"
              />
            </view>
          </view>

          <view v-if="!basicFun" class="d-flex a-center mr-10">
            <image
              class="w-56 h-56 p-14"
              src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_ser_bla.png"
              @click="openService()"
            />
            <view class="p-rela d-flex a-center" v-if="!goodsInfo.is_seckill">
              <image
                class="w-56 h-56 p-14"
                src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_car_bla.png"
                @click="openShoppingCar()"
              />
              <view
                v-if="shoppingCartNum"
                class="p-abso top-04 right-04 w-32 h-32 bg-e80404 b-rad-p50 d-flex j-center a-center font-18 text-ffffff"
                >{{ shoppingCartNum > 99 ? '99' : shoppingCartNum }}</view
              >
            </view>
            <!-- app分享 -->
            <image
              v-show="from == '1' || from == '2'"
              class="w-56 h-56 p-14"
              src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_sha_bla.png"
              @click="onAppShare"
            />
            <!-- <button v-show="from == ''" class="sha-fri-btn-bla w-56 h-56 ml-10" open-type="share" /> -->
          </view>
        </view>
      </view>

      <!-- 轮播图 -->
      <view class="p-rela" @click="handlePreviewSwiper">
        <view
          v-show="scrollTop <= 10 && from != '3'"
          class="fade-in p-abso z-980 top-0 w-p100 h-112 d-flex a-center j-sb"
          :class="from ? 'j-sb' : ''"
        >
          <view class="d-flex a-center">
            <image
              class="w-56 h-56 p-24"
              :src="
                pageLength <= 1 && !from
                  ? 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_home_whi.png'
                  : 'https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_bac_whi.png'
              "
              @click.stop="jumpBack()"
            />
            <!-- <view class="w-252 ml-10"></view> -->
          </view>

          <view v-if="!basicFun" class="d-flex a-center mr-12">
            <image
              class="w-56 h-56 p-14"
              src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_ser_whi.png"
              @click.stop="openService()"
            />
            <view class="p-rela d-flex a-center" v-if="!goodsInfo.is_seckill">
              <image
                class="w-56 h-56 p-14"
                src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_car_whi.png"
                @click.stop="openShoppingCar()"
              />
              <view
                v-if="shoppingCartNum"
                class="p-abso top-04 right-04 w-32 h-32 bg-e80404 b-rad-p50 d-flex j-center a-center font-18 text-ffffff"
                >{{ shoppingCartNum > 99 ? '99' : shoppingCartNum }}</view
              >
            </view>
            <!-- app分享 -->
            <image
              v-show="from == '1' || from == '2'"
              class="w-56 h-56 p-14"
              src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_sha_whi.png"
              @click.stop="onAppShare"
            />
            <!-- <button v-show="from == ''" class="sha-fri-btn-whi w-56 h-56 ml-10" open-type="share" /> -->
          </view>
        </view>

        <!-- 新人标签 -->
        <!--  <view
                  v-if="!isMiaoFa && isNewPeopleGoods && newPeoplePackList.length"
                  class="new-peo-bg p-abso left-0 z-100 bottom-0 w-262 h-56 d-flex a-center"
                >
                  <view class="ml-16 font-22 text-ffffff">新人专享</view>
                  <view class="ml-06">
                    <text class="font-18 font-wei text-ffffff">¥</text>
                    <text class="font-30 font-wei text-ffffff">{{ newPeoplePackList[0].newcomer_price }}</text>
                  </view>
                </view> -->

        <!-- 有边框图 -->
        <view v-if="activityBorderImage">
          <vh-swiper
            :loading-type="2"
            mode="number"
            indicator-pos="bottomRight"
            :list="goodsInfo.swiperList"
            :border-image="activityBorderImage"
            height="466"
            border-radius="0"
            img-mode="aspectFit"
            @change="handlePreviewIndex"
            @click="handlePreviewSwiper"
          />
        </view>

        <!-- 没有边框图 -->
        <view v-else>
          <vh-swiper
            :loading-type="2"
            mode="number"
            indicator-pos="bottomRight"
            :list="goodsInfo.swiperList"
            height="466"
            border-radius="0"
            img-mode="aspectFit"
            @change="handlePreviewIndex"
            @click="handlePreviewSwiper"
          />
        </view>
      </view>
      <!-- 商品信息 -->
      <view id="current0" class="">
        <!--频道底图（商品价格、市场价、限购、倒计时） 0 = 闪购、1 = 秒发、2 = 跨境、3 = 尾货 （拼团、秒杀待定） -->
        <!-- 2022-07-19 开始售卖时间和结束售卖时间取套餐接口里面的 需求方：vber-->
        <view
          v-if="
            packageList.length &&
            packageList[0].price &&
            Number(packageList[0].price) > 0 &&
            (packageAllInfo.sell_time || packageAllInfo.sold_out_time)
          "
          class=""
        >
          <view v-if="openSaleSeconds > 86400 && endSaleSeconds >= 0">
            <vh-goods-detail-channel-bg
              :sale-status="0"
              :quota_number="goodsInfo.quota_number"
              :on-sale-status="onSaleStatus"
              :is_seckill="goodsInfo.is_seckill"
              :sale-date="packageAllInfo.sell_time"
              :channel="getChannel"
              :timestamp="date.getSeconds(packageAllInfo.sell_time)"
              :src="goodsInfo.channel_bg"
              :price-secrecy="isHiddenPrice"
              :price="currPrice"
              :secNewPrice="packageAllInfo.price"
              :priceDiscount="packageAllInfo.price_discount"
              :isSecondNewPeopleGoodsConfNewActivity="isSecondNewPeopleGoodsConfNewActivity"
              :userIsLogin="userIsLogin"
              :market-price="currMarketPrice"
              :sold-num="limitInfo.aleradyBuy"
              :isDepositGoods="isDepositGoods"
              :depositPackageList="depositPackageList"
            />
          </view>
          <view v-else-if="openSaleSeconds > 0 && openSaleSeconds <= 86400 && endSaleSeconds >= 0">
            <vh-goods-detail-channel-bg
              :sale-status="1"
              :quota_number="goodsInfo.quota_number"
              :is_seckill="goodsInfo.is_seckill"
              :on-sale-status="onSaleStatus"
              :sale-date="packageAllInfo.sell_time"
              :channel="getChannel"
              :timestamp="date.getSeconds(packageAllInfo.sell_time)"
              :src="goodsInfo.channel_bg"
              :price-secrecy="isHiddenPrice"
              :price="currPrice"
              :secNewPrice="packageAllInfo.price"
              :priceDiscount="packageAllInfo.price_discount"
              :isSecondNewPeopleGoodsConfNewActivity="isSecondNewPeopleGoodsConfNewActivity"
              :userIsLogin="userIsLogin"
              :market-price="currMarketPrice"
              :sold-num="limitInfo.aleradyBuy"
              :isDepositGoods="isDepositGoods"
              :depositPackageList="depositPackageList"
            />
          </view>
          <view v-else>
            <vh-goods-detail-channel-bg
              :sale-status="2"
              :quota_number="goodsInfo.quota_number"
              :on-sale-status="onSaleStatus"
              :channel="getChannel"
              :is_seckill="goodsInfo.is_seckill"
              :timestamp="date.getSeconds(packageAllInfo.sold_out_time)"
              :src="goodsInfo.channel_bg"
              :price-secrecy="isHiddenPrice"
              :price="currPrice"
              :secNewPrice="packageAllInfo.price"
              :priceDiscount="packageAllInfo.price_discount"
              :isSecondNewPeopleGoodsConfNewActivity="isSecondNewPeopleGoodsConfNewActivity"
              :userIsLogin="userIsLogin"
              :market-price="currMarketPrice"
              :sold-num="limitInfo.aleradyBuy"
              :isDepositGoods="isDepositGoods"
              :depositPackageList="depositPackageList"
            />
          </view>
        </view>

        <!-- 秒发优惠券 -->
        <view
          v-if="
            !needHiddenPrice &&
            packageAllInfo.is_support_coupon &&
            Object.keys(couponInfo).length &&
            !couponInfoLoading &&
            !isDepositGoods
          "
          class="fade-in ptb-00-plr-24"
        >
          <view v-if="isSecondNewPeopleGoods" class="mt-20">
            <SecGoodsDetailCouponNewReceive
              :couponInfo="couponInfo.new_user_coupon_package"
              @receiveCoupon="couponPopupNewVisible = true"
            />
          </view>

          <view v-else class="mt-10">
            <SecGoodsDetailCouponReceive
              :list="couponInfo.coupon_list"
              :rabbitCoupon="couponInfo.rabbit_coupon"
              @receiveCoupon="jumpRabbitCou"
            />
          </view>
        </view>

        <view v-if="goodsFullReductionLabel" class="flex-c-c ptb-00-plr-24">
          <image
            :src="goodsFullReductionLabel"
            mode="widthFix"
            style="height: auto"
            class="w-p100"
            @click="onGoodsFullReductionJump"
          ></image>
        </view>

        <!-- 商品名称、限购数量-->
        <view class="ptb-20-plr-24 pb-28">
          <view class="">
            <vh-channel-title-icon
              v-if="!isMiaoFaOrBusinessMiaofa"
              :channel="getChannel"
              :is_seckill="goodsInfo.is_seckill"
              :marketing-attribute="goodsInfo.marketing_attribute"
              :warehouse-type="goodsInfo.warehouse_type"
              class="p-rela top-n-02"
            />
            <text
              class="font-30 font-wei-500 text-3"
              :class="isMiaoFaOrBusinessMiaofa ? 'l-h-44' : 'ml-06 l-h-42'"
              @click.stop="copy.copyText(goodsInfo.title)"
              >{{ goodsInfo.title }}</text
            >
          </view>

          <view class="mt-12 font-28 text-6 l-h-40" style="user-select: auto">{{ goodsInfo.brief }}</view>

          <!--已购/限量百分比 0 = 闪购、2 = 跨境、3 = 尾货、101 = 拼团  (秒杀待定)-->
          <vh-goods-detail-limit-progress
            v-if="!isDepositGoods"
            :channel="getChannel"
            :alerady-buy="limitInfo.aleradyBuy"
            :limit-number="limitInfo.limitNumber ? limitInfo.limitNumber : 0"
            :limit-purchase="goodsInfo.quota_number"
          />

          <view v-if="goodsInfo.warehouse_type == 1" class="flex-s-c mt-20 pt-28 bt-s-02-eeeeee">
            <text class="font-26 text-3">预计送达时间</text>
            <text class="ml-36 font-24 text-6">{{ estimatedDeliveryTime }}</text>
          </view>
          <view v-else class="flex-s-c mt-20 pt-28 bt-s-02-eeeeee">
            <text class="font-26 text-3 l-h-36">预计发货时间</text>
            <text class="ml-36 font-26 text-3 l-h-36">{{ estimatedDeliveryTime }}</text>
            <text class="font-24 text-9">（不支持7天无理由退货）</text>
          </view>
          <view
            v-if="
              [0, 1, 3, 9].includes(goodsInfo.periods_type) &&
              Object.keys(packageAllInfo).length &&
              !packageAllInfo.is_support_coupon
            "
            class="flex-c-c"
          >
            <image :src="ossIcon('/goods_detail/coupon_tips_702_58.png')" class="mt-28 w-p100 h-58"></image>
          </view>
          <!-- 秒杀 -->
          <view v-if="false" class="mt-30 d-flex j-sb a-center">
            <view class="font-28 font-wei text-e80404">温馨提示：预约需支付50兔头</view>
            <view class="">
              <text class="font-40 font-wei text-e80404">64</text>
              <text class="ml-06 font-24 text-6">人已预约</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 商品详细信息 -->
      <view class="bg-f5f5f5 pt-20 pl-24 pb-130 pr-24">
        <!-- 拼团信息 -->
        <view v-if="isGroup && groupId" class="">
          <!-- 拼团人数未满 -->
          <view
            v-if="groupInfo.group_last_num > 0"
            class="bg-ffffff d-flex flex-column j-center a-center b-rad-10 mb-20 ptb-32-plr-00"
          >
            <!-- 不可以拼团标题 -->
            <view
              v-if="groupInfo.remaining_time <= 0 || groupInfo.group_status === 3"
              class="mt-32 font-28 font-wei text-3"
            >
              {{ groupInfo.group_status === 3 ? '团长已取消拼团，您可发起新的拼团！' : '哎呀，拼单时间已过，人数不够' }}
            </view>

            <!-- 可以拼团标题 -->
            <view v-else class="d-flex mt-32">
              <view class="font-28 text-9">还差</view>
              <view class="font-28 font-wei text-e80404">{{ groupInfo.group_last_num }}人</view>
              <view class="font-28 text-9">即可拼成，</view>
              <vh-count-down
                :timestamp="groupInfo.remaining_time"
                :all-font-bold="true"
                :show-days="false"
                bg-color="transparent"
                :has-separator-distance="false"
                :separator-size="28"
                separator-color="#333"
                :font-size="28"
                color="#333"
                @end="groupInfo.remaining_time = 0"
              />
              <view class="font-28 text-9">后结束</view>
            </view>

            <!-- 拼团头像 -->
            <view
              class="w-648 d-flex a-center mt-40 o-scr-x"
              :class="groupInfo.user_head_img.length > 5 ? '' : 'j-center'"
            >
              <view
                class="p-rela w-112 w-112 bg-ffffff d-flex j-center a-center b-rad-p50 mr-n-20"
                :style="{ zIndex: groupInfo.user_head_img.length - index }"
                v-for="(item, index) in groupInfo.user_head_img"
                :key="index"
              >
                <image class="w-108 h-108 b-rad-p50" :src="item" mode="aspectFill" />
                <view v-if="index == 0" class="p-abso z-03 bottom-0 w-110 d-flex j-center">
                  <text class="bg-ff9127 b-rad-16 b-s-02-ffffff ptb-02-plr-16 font-20 text-ffffff">团长</text>
                </view>
              </view>

              <view class="p-rela w-112 w-112 bg-ffffff d-flex j-center a-center b-rad-p50 ml-54">
                <image
                  class="w-108 h-108 b-rad-p50"
                  src="https://images.vinehoo.com/vinehoomini/v3/order_detail/que_mark.png"
                  mode="aspectFill"
                />
              </view>
            </view>
          </view>

          <!-- 拼团人数已满 -->
          <view v-else class="bg-ffffff d-flex flex-column j-center a-center b-rad-10 mb-20 ptb-32-plr-00">
            <!-- 拼团已满标题 -->
            <view class="mt-32 font-28 font-wei text-3">抱歉，此团人已满！</view>

            <!-- 拼团头像 -->
            <view
              class="w-648 d-flex a-center mt-40 o-scr-x"
              :class="groupInfo.user_head_img.length > 5 ? '' : 'j-center'"
            >
              <view
                class="p-rela w-112 w-112 bg-ffffff d-flex j-center a-center b-rad-p50 ml-10 mr-10"
                v-for="(item, index) in groupInfo.user_head_img"
                :key="index"
              >
                <image class="w-108 h-108 b-rad-p50" :src="item" mode="aspectFill" />
                <view v-if="index == 0" class="p-abso z-03 bottom-0 w-110 d-flex j-center">
                  <text class="bg-ff9127 b-rad-16 b-s-02-ffffff ptb-02-plr-16 font-20 text-ffffff">团长</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <GoodsDetailDepositInfo
          ref="goodsDetailDepositInfoRef"
          v-if="isDepositGoods"
          :estimatedDeliveryTime="estimatedDeliveryTime"
          :timeInfo="packageAllInfo"
        />
        <view class="d-flex j-center mb-20" @click="jump.h5Jump(`${agreementPrefix}/aboutVineHoo`, from)">
          <image class="w-p100 h-114" :src="goodsInfo.gold_area_img" mode="aspectFill" />
        </view>
        <!-- 广告位 -->
        <!-- <view class="h-96 bg-ffb874 d-flex j-center a-center b-rad-12 mt-20 font-28 text-c27807">
                            广告位（可配置）只限宽度
                        </view> -->
        <template>
          <!-- 跨境告知书 -->
          <view v-if="goodsInfo.periods_type == 2" class="d-flex j-sb mb-20">
            <view
              style="width: 49%"
              class="b-rad-08 h-84 p-rela"
              @click="jump.h5Jump(`${agreementPrefix}/crossBorderGoods`, from)"
            >
              <image
                class="w-p100 h-p100 b-rad-08"
                src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/OIP2.jpeg"
                mode="aspectFill"
              />
              <view class="p-abso w-p100 h-p100 top-0 left-0 d-flex j-center a-center">
                <text class="text-ffffff font-28">跨境商品消费者告知书</text>
              </view>
            </view>
            <view
              style="width: 49%"
              class="b-rad-08 h-84 p-rela"
              @click="jump.h5Jump(`${agreementPrefix}/crossEducation`, from)"
            >
              <image
                class="w-p100 h-p100 b-rad-08"
                src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/OIP1.jpeg"
                mode="aspectFill"
              />
              <view class="p-abso w-p100 h-p100 top-0 left-0 d-flex j-center a-center">
                <text class="text-ffffff font-28">跨境消费合规教育专栏</text>
              </view>
            </view>
          </view>

          <view v-if="activityCapsuleImage" class="mb-20" @click="jump.h5Jump(activityData.activity_url, from, 4)">
            <image :src="activityCapsuleImage" class="w-p100 h-206" />
          </view>

          <view v-if="isChooseWineActivity" class="p-rela flex-c-c mb-20 h-730">
            <image class="p-abso w-750 h-p100" :src="ossIcon('/goods_detail/choose_wine_intro_750_730.png')" />
          </view>
          <!-- 采购说 -->
          <view
            v-if="
              packageAllInfo.purchasing_said_image &&
              packageAllInfo.purchasing_said_desc &&
              packageAllInfo.purchasing_said_name &&
              goodsInfo.purchasing_said
            "
            @click="goJumpPurchasingSaid"
            id="current1"
            class="bg-ffffff b-rad-10 mb-20"
            style="padding-top: 0"
          >
            <view
              class="p-rela b-rad-10"
              :style="{
                backgroundColor: '#FDF8F5',
                backgroundSize: '100% 100%',
                padding: '24rpx 0 24rpx 24rpx',
                backgroundRepeat: 'no-repeat',
                minHeight: '110px',
                height: 'auto',
                backgroundPosition: 'center',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'flex-start',
              }"
            >
              <!-- 头像和名字容器 -->
              <view style="position: relative; margin-top: 10rpx; margin-left: 56rpx; width: 116rpx">
                <!-- 头像固定为正方形，等比缩放 -->
                <view style="width: 116rpx; height: 116rpx; overflow: hidden; border-radius: 8rpx">
                  <img
                    style="width: 100%; height: 100%"
                    :src="packageAllInfo.purchasing_said_image"
                    mode="aspectFill"
                    alt=""
                  />
                </view>

                <!-- 名字背景图作为父级view的背景图，与头像居中对齐 -->
                <view
                  style="
                    position: absolute;
                    color: #5c3d2b;
                    bottom: -40rpx;
                    left: 50%;
                    transform: translateX(-50%);
                    white-space: nowrap;
                    text-align: center;
                    z-index: 11;
                    min-width: 100%;
                  "
                  class="l-h-34 font-wei-500 font-24 ptb-02-plr-20"
                  :style="{
                    backgroundImage: `url(${ossIcon('/goods_detail/buyer-name-bg.png')})`,
                    backgroundSize: '100% 100%',
                    backgroundRepeat: 'no-repeat',
                  }"
                  >{{ packageAllInfo.purchasing_said_name }}</view
                >
              </view>

              <view
                class="font-24 text-3 p-rela"
                style="
                  width: 65%;
                  color: #785a50;
                  white-space: break-spaces;
                  right: 20px;
                  font-size: 24rpx;
                  line-height: 34rpx;
                  background-color: #f5f5f3;
                  border-radius: 26px 10px 10px 26px;
                  padding: 10px 18px;
                  min-height: 70px;
                  height: auto;
                  display: flex;
                  margin-top: 6px;
                  margin-left: 36px;
                  justify-content: flex-start;
                  overflow: visible;
                "
              >
                <!-- 左上角图片 -->
                <image
                  class="p-abso z-990 w-34 h-28"
                  style="top: -4px; left: -4px"
                  src="https://images.vinehoo.com/vinehoomini/v3/coupon/dou2.png"
                  mode="aspectFit"
                />
                <!-- 右下角图片 -->
                <image
                  style="bottom: -4px; right: -4px"
                  class="p-abso z-990 w-34 h-28"
                  src="https://images.vinehoo.com/vinehoomini/v3/coupon/dou1.png"
                  mode="aspectFit"
                />
                {{ packageAllInfo.purchasing_said_desc }}
              </view>
            </view>
            <view
              class="pt-20 pl-24 pb-40 pr-24 w-b-b-w font-28 text-5 o-hid"
              style="line-height: 1.4; white-space: break-spaces"
            >
              {{ goodsInfo.purchasing_said }}
            </view>
          </view>
          <!-- 商品详情 -->
          <view id="current1" class="bg-ffffff b-rad-10 mb-20 pt-40 pl-24 pb-40 pr-24">
            <view class="font-36 font-wei text-3">商品详情</view>
            <view class="p-rela w-654 h-404 b-rad-10 o-hid mt-32" v-if="goodsInfo.video_cover && goodsInfo.video">
              <vh-image :loading-type="2" :src="goodsInfo.video_cover" :height="404" />
              <view class="p-abso z-03 top-0 left-0 w-654 h-404 d-flex j-center a-center">
                <image
                  class="w-116 h-116"
                  src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/video_play.png"
                  mode="aspectFill"
                  @click="playVideo"
                />
              </view>
            </view>

            <!-- <video class="w-654 h-404 b-rad-10 mt-32" objectFit="cover"
                                :poster="`${goodsInfo.video}?x-oss-process=video/snapshot,t_40001,m_fast`" :src="goodsInfo.video"></video> -->

            <view class="mt-32 w-b-b-w font-32 o-hid" style="line-height: 1.4">
              <u-parse :html="goodsInfo.detail" :show-with-animation="true" style="user-select: auto" />
            </view>
          </view>

          <!-- 酒款信息 -->
          <view v-if="!goodsInfo.title.includes('大侦探')" class="" style="overflow: scroll">
            <u-tabs
              :list="wineStyleTabsList"
              bg-color="#F5F5F5"
              :current="currentWineStyleTabs"
              :height="94"
              :font-size="32"
              inactive-color="#999"
              active-color="#333"
              :bar-width="36"
              :bar-height="8"
              :bar-style="{ background: 'linear-gradient(214deg, #FF8383 0%, #E70000 100%)' }"
              @change="changeWineStyleTabs"
            />
          </view>

          <!-- 商品参数、商品介绍 -->
          <view
            v-if="goodsInfo.product_info && goodsInfo.product_info.length && !goodsInfo.title.includes('大侦探')"
            class=""
            style="user-select: auto"
          >
            <!-- 商品参数 -->
            <view class="bg-ffffff b-rad-10 mb-20 pt-32 pl-24 pb-16 pr-24">
              <view class="mb-24 font-36 font-wei text-3">商品参数</view>
              <vh-param title="品名" :introduce="wineStyleInfo.en_product_name" />
              <vh-param title="类型" :introduce="wineStyleInfo.product_type_name" />
              <vh-param title="酒庄" :introduce="wineStyleInfo.chateau_name" />
              <vh-param title="产区" :introduce="wineStyleInfo.producing_area_name" />
              <vh-param title="葡萄品种" :introduce="wineStyleInfo.grape" />
              <vh-param title="酒精度" :introduce="wineStyleInfo.alcohol" />
              <vh-param title="容量" :introduce="wineStyleInfo.capacity" />
              <vh-param title="残糖" :introduce="wineStyleInfo.residual_sugar" />
              <vh-param title="酿造工艺" :introduce="wineStyleInfo.brewing" />
              <vh-param title="评分" :introduce="wineStyleInfo.score" />
              <vh-param title="获奖" :introduce="wineStyleInfo.prize" />
              <vh-param title="品鉴笔记" :layout-mode="'vertical'" :introduce="wineStyleInfo.tasting_notes" />
              <vh-param title="饮用建议" :layout-mode="'vertical'" :introduce="wineStyleInfo.drinking_suggestion" />
            </view>

            <!-- 商品介绍 -->
            <view class="mb-20">
              <vh-goods-detail-introduce title="酒庄介绍" :info="wineStyleInfo.chateau" />
              <vh-goods-detail-introduce title="葡萄介绍" :info="wineStyleInfo.grape_info" />
              <vh-goods-detail-introduce title="产区介绍" :info="wineStyleInfo.regions" />
            </view>
          </view>

          <view v-if="isShowSparkingAttention" class="flex-c-c mb-20">
            <image :src="`${ossPrefix}/mall/detail/sparking_attention.jpeg`" class="w-p100"></image>
          </view>
          <!-- 酒云网售后服务政策 -->
          <view class="d-flex j-center mb-20">
            <image
              class="w-p100 h-130 b-rad-10"
              :src="goodsInfo.service_policy_img"
              mode="aspectFill"
              @click="jump.h5Jump(`${agreementPrefix}/vinehooAfterSale`, from)"
            />
          </view>

          <!-- 热门推荐 -->
          <view v-if="from != '3'" id="goods-recommends" class="bg-ffffff b-rad-10 mb-20">
            <view class="mb-08 pt-40 pr-24 pl-24 font-36 font-wei text-3">热门推荐</view>
            <vh-goods-recommend-list
              ref="vhGoodsRecommendListRef"
              :from="from"
              :outer-padding-bottom="0"
              :inn-margin-left="0"
              :inn-margin-right="0"
              :isInit="false"
            />
          </view>
        </template>

        <!-- 评论 -->
        <view id="current2" class="bg-ffffff b-rad-10 mt-20 pt-40 pb-32 pl-24 pr-24">
          <view class="font-wei-600 font-36 text-3 l-h-50">评论</view>
          <view v-if="commentList.length" class="mt-32">
            <VhCommentList
              :list="commentList"
              @enjoy="thumbsUp"
              @reply="onReply"
              @toggleReply="onToggleReply"
            ></VhCommentList>
          </view>
          <view v-else class="flex-c-c pt-24 pb-24 font-28 text-d8d8d8">- 暂无评论 -</view>
        </view>
        <view v-if="commentList.length" class="mt-28">
          <u-loadmore :status="loadStatus" color="#999" font-size="24" />
        </view>
      </view>

      <!-- 弹框 -->
      <view class="">
        <!-- 商品为线上商品提示弹框提示 -->
        <u-mask :show="showOnLineGoodsMask" :z-index="10080" :zoom="false" @click="showOnLineGoodsMask = false">
          <view class="h-p100 d-flex j-center a-center">
            <view class="p-rela z-01 w-582 bg-ffffff d-flex flex-column j-center a-center b-rad-10 ptb-66-plr-00">
              <view class="font-28 font-wei text-3">该商品为线上商品，无法在线下门店提取。</view>
              <view class="w-430 mt-24 text-center font-28 font-wei text-3 w-b-b-w"
                >This product is selling ONLINE and CAN NOT pick-up in offline stores.</view
              >
            </view>
          </view>
        </u-mask>

        <!-- 套餐弹框 -->
        <u-popup v-model="showGoodsPackPop" mode="bottom" :duration="150" :border-radius="20">
          <view v-if="packageList.length" class="pt-32 pr-24 pb-48 pl-24">
            <view class="d-flex">
              <view class="w-246 h-152 b-rad-06 o-hid">
                <vh-image
                  :loading-type="2"
                  :src="packageInfo.package_img || goodsInfo.banner_img[0]"
                  :mode="packageInfo.package_img ? 'aspectFit' : 'aspectFill'"
                  :height="152"
                />
              </view>

              <view class="d-flex flex-1 flex-column j-sb ml-16">
                <view class="font-28 text-3 l-h-40 o-hid text-hidden-2">{{ goodsInfo.title }}</view>
                <view class="d-flex a-center mt-12">
                  <text v-if="isHiddenPrice" class="font-44 font-wei text-e80404">价格保密</text>
                  <view v-else class="d-flex a-start">
                    <view class="h-60 font-44 font-wei text-e80404 l-h-60"
                      ><text class="font-24">¥{{ ' ' }}</text
                      >{{ currentPackagePrice }}</view
                    >
                    <!-- <text v-if="packageType == 0" class="font-44 font-wei text-e80404"><text class="font-24 mr-06">¥</text>{{packageInfo.price}}</text>
                                                <text v-if="packageType == 1" class="font-44 font-wei text-e80404"><text class="font-24 mr-06">¥</text>{{packageInfo.group_price}}</text>
                                                <text v-if="packageType == 2" class="font-44 font-wei text-e80404"><text class="font-24 mr-06">¥</text>{{packageInfo.newcomer_price}}</text>
                                                <text v-if="packageType == 4" class="font-44 font-wei text-e80404"><text class="font-24 mr-06">¥</text>{{packageInfo.deposit_price}}</text> -->
                    <!-- <view class="p-rela top-20 ml-10 font-24 text-9 text-dec-l-t">¥{{ packageInfo.market_price }}</view> -->
                  </view>
                </view>
              </view>
            </view>

            <view class="mt-48 font-32 font-wei-500 text-3 l-h-44">规格</view>

            <view class="d-flex flex-wrap ml-n-24">
              <!-- 普通商品套餐选项 -->
              <view v-if="packageType == 0" v-for="(item, index) in packageList" :key="index">
                <view
                  class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3"
                  :class="
                    packageIndex != index
                      ? ''
                      : clickSelectPackage
                      ? 'skew-top bg-fce4e3 b-s-01-e80404 text-e80404'
                      : 'bg-fce4e3 b-s-01-e80404 text-e80404'
                  "
                  @click="selectPackage(index, 0)"
                  >{{ item.package_name }}</view
                >
              </view>

              <!-- 拼团商品套餐选项 -->
              <view v-if="packageType == 1" v-for="(item, index) in groupPackageList" :key="index">
                <view
                  class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3"
                  :class="
                    packageIndex != index
                      ? ''
                      : clickSelectPackage
                      ? 'skew-top bg-fce4e3 b-s-01-e80404 text-e80404'
                      : 'bg-fce4e3 b-s-01-e80404 text-e80404'
                  "
                  @click="selectPackage(index, 1)"
                  >{{ item.package_name }}</view
                >
              </view>

              <!-- 新人套餐选项 -->
              <view v-if="packageType == 2" v-for="(item, index) in newPeoplePackList" :key="index">
                <view
                  class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3"
                  :class="
                    packageIndex != index
                      ? ''
                      : clickSelectPackage
                      ? 'skew-top bg-fce4e3 b-s-01-e80404 text-e80404'
                      : 'bg-fce4e3 b-s-01-e80404 text-e80404'
                  "
                  @click="selectPackage(index, 2)"
                  >{{ item.package_name }}</view
                >
              </view>

              <!-- 订金套餐 -->
              <view v-if="packageType == 4" v-for="(item, index) in depositPackageList" :key="index">
                <view
                  class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3"
                  :class="
                    packageIndex != index
                      ? ''
                      : clickSelectPackage
                      ? 'skew-top bg-fce4e3 b-s-01-e80404 text-e80404'
                      : 'bg-fce4e3 b-s-01-e80404 text-e80404'
                  "
                  @click="selectPackage(index, 4)"
                  >{{ item.package_name }}</view
                >
              </view>
            </view>

            <view v-if="!isKuaJing" class="d-flex j-sb a-center mt-52">
              <view class="font-32 font-wei-500 text-3 l-h-44">数量</view>
              <view class="unumberbox">
                <u-number-box
                  :disabled="!packageInfo.inventory"
                  v-model="purchaseNumbers"
                  :min="1"
                  :max="goodsInfo.quota_number == '9999' ? packageInfo.inventory : Number(goodsInfo.quota_number)"
                  :input-width="64"
                  :input-height="50"
                  :size="28"
                />
              </view>
            </view>

            <!-- packageInfo.is_original_package === 0 -->
            <!-- 2022-09-14 商品详情增加一个"原箱提醒"-前端 需求方：龙飞 -->
            <view v-if="packageInfo.is_original_package === 1" class="d-flex j-center a-center mt-52">
              <view class="d-flex a-center p-10" @click="showOriginalBoxMask = true">
                <vh-check :checked="isOriginalBox === 1" @click="showOriginalBoxMask = true" />
                <text class="ml-10 font-24 text-6">原箱发货</text>
              </view>
            </view>

            <!-- 在套餐选择区域下方添加自选产品区域 -->
            <view v-if="packageInfo.is_custom_package === 1" class="mt-32">
              <view class="font-32 font-wei-500 text-3 l-h-44 mb-24">选择产品</view>
              <view class="d-flex flex-wrap ml-n-24">
                <view v-for="(item, index) in packageInfo.product_list" :key="index">
                  <view
                    class="bg-f6f6f6 mt-32 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3"
                    :class="selectedProducts.includes(item.product_id) ? 'bg-fce4e3 b-s-01-e80404 text-e80404' : ''"
                    @click="selectProduct(item.product_id)"
                  >
                    {{ item.product_name || item.custom_product_name }}
                  </view>
                </view>
              </view>
            </view>

            <view class="mt-92 d-flex j-center a-center">
              <view v-if="packageInfo.inventory < 1" class="fade-in">
                <u-button
                  :disabled="true"
                  shape="circle"
                  :hair-line="false"
                  :ripple="true"
                  ripple-bg-color="#FFF"
                  :custom-style="{
                    width: '646rpx',
                    height: '80rpx',
                    fontSize: '28rpx',
                    fontWeight: 'bold',
                    color: '#FFF',
                    backgroundColor: '#DDDDDD',
                    border: 'none',
                  }"
                  >库存不足</u-button
                >
              </view>

              <view v-else class="fade-in">
                <u-button
                  shape="circle"
                  :hair-line="false"
                  :ripple="true"
                  ripple-bg-color="#FFF"
                  :custom-style="{
                    width: '646rpx',
                    height: '80rpx',
                    fontSize: '28rpx',
                    fontWeight: 'bold',
                    color: '#FFF',
                    backgroundColor: isCustomProductSelected ? '#E80404' : '#DDDDDD',
                    border: 'none',
                  }"
                  @click="confirm"
                  >{{
                    isCustomProductSelected ? '确定' : `请选择${packageInfo.custom_product_count || 0}个产品`
                  }}</u-button
                >
              </view>
            </view>
          </view>
        </u-popup>

        <!-- 评论弹框（旧） -->
        <!-- <u-popup v-model="showCommPop" mode="bottom" :duration="50" :border-radius="20">
                            <vh-comment :protocol-list="protocolList" :placeholder="commPlaceholder" :from="from" @on-comment-add="onCommentAdd" @on-img-send="onImgSend" />
                        </u-popup> -->
        <!-- 评论弹框（新） -->
        <view
          v-show="showCommPop"
          class="fade-in tran-1 p-fixed z-1000 top-0 left-0 w-p100 h-vh-100 bg-000-000-000-060 o-hid"
          @click="showCommPop = false"
        >
          <view class="p-abso bottom-0 w-p100 bg-ffffff b-tl-tr-rad-20" @click.stop>
            <vh-comment
              :protocol-list="protocolList"
              :placeholder="commPlaceholder"
              :from="from"
              @on-comment-add="onCommentAdd"
              @on-img-send="onImgSend"
            />
          </view>
        </view>

        <!-- 预约弹框 -->
        <u-modal
          v-model="showAppoModel"
          :width="490"
          :show-title="false"
          content="您将花费50兔头预约秒杀酒款"
          :content-style="{ fontSize: '28rpx', color: '#333' }"
          :show-cancel-button="true"
          cancel-text="狠心放弃"
          :cancel-style="{ fontSize: '28rpx', color: '#999' }"
          confirm-text="确定预约"
          :confirm-style="{ fontSize: '28rpx', color: '#E80404' }"
        >
        </u-modal>

        <!-- 预约成功弹框 -->
        <u-popup v-model="showAppoSuccPop" mode="center" width="518" border-radius="10">
          <view class="d-flex flex-column j-center a-center pt-64 pb-44">
            <image
              class="w-264 h-184"
              src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/app_succ.png"
              mode="aspectFill"
            />
            <view class="mt-40 font-40 font-wei text-3">恭喜您！预约成功！</view>
            <view class="mt-52">
              <u-button
                shape="circle"
                :hair-line="false"
                :ripple="true"
                ripple-bg-color="#FFF"
                :custom-style="{
                  width: '422rpx',
                  height: '58rpx',
                  fontSize: '28rpx',
                  color: '#FFF',
                  backgroundColor: '#E80404',
                  border: 'none',
                }"
                >分享好友</u-button
              >
            </view>
          </view>
        </u-popup>

        <!-- 跨境商品购买须知弹框 -->
        <u-popup
          v-model="showCrossBorderNoticePop"
          :z-index="100176"
          mode="center"
          :mask-close-able="false"
          height="80%"
          :border-radius="10"
        >
          <view class="w-582 h-p100 d-flex flex-column">
            <view class="ptb-00-plr-40 pt-40 o-hid-y">
              <scroll-view scroll-y="true" class="h-p100" style="overflow: scroll">
                <view class="d-flex j-center a-center font-32 font-wei text-3">跨境商品购买须知</view>
                <view class="pt-32 pb-32 l-h-50 font-28 text-3">
                  <view v-for="(notice, noticeIndex) in crossBorderNotices" :key="noticeIndex">
                    <view class="font-30">{{ notice.title }}</view>
                    <view v-for="(desc, descIndex) in notice.list" :key="descIndex">{{ desc }}</view>
                  </view>
                </view>
              </scroll-view>
            </view>
            <view class="pt-20 pb-20 b-sh-00021200-022">
              <view class="d-flex j-center">
                <u-button
                  shape="circle"
                  :hair-line="false"
                  :ripple="true"
                  ripple-bg-color="#FFF"
                  :custom-style="{
                    width: '440rpx',
                    height: '64rpx',
                    fontSize: '28rpx',
                    fontWeight: 'bold',
                    color: '#FFF',
                    backgroundColor: '#E80404',
                    border: 'none',
                  }"
                  :throttleTime="0"
                  @click="showCrossBorderNoticePop = false"
                  >我知道了</u-button
                >
              </view>
              <view class="d-flex j-center a-center mt-24" @click="notPromptingCrossBorderNotice()">
                <vh-check :checked="crossBorderNoticeNotPrompting === 1" @click="notPromptingCrossBorderNotice()" />
                <text class="ml-10 font-24 text-9">下次不再提醒</text>
              </view>
            </view>
          </view>
        </u-popup>

        <!-- 原箱发货弹框 -->
        <u-mask :show="showOriginalBoxMask" :z-index="10080" @click="showOriginalBoxMask = false">
          <view class="h-p100 d-flex j-center a-center">
            <view class="p-rela z-01 w-580 h-882" @click.stop>
              <!-- 1 = 安卓、2 = ios"、3 = pc -->
              <image
                v-if="!isShowLocalImage"
                class="p-abso z-01 w-p100 h-p100"
                :src="`${osip}/goods_detail/origin_box_bg.png`"
              ></image>
              <image v-else class="p-abso z-01 w-p100 h-p100" src="localimages-scheme://origin_box_bg.png"></image>
              <view class="p-abso bottom-0 z-04 w-p100 h-150 d-flex j-sa a-center ptb-00-plr-24">
                <u-button
                  shape="circle"
                  :hair-line="false"
                  :ripple="true"
                  ripple-bg-color="#FFF"
                  :custom-style="{
                    width: '210rpx',
                    height: '64rpx',
                    fontSize: '28rpx',
                    fontWeight: 'bold',
                    color: '#E80404',
                    border: '2rpx solid #E80404',
                  }"
                  @click="confirmIsOriginalShipment(1)"
                  >原箱发货</u-button
                >

                <u-button
                  v-if="!isForceOriginalBox"
                  shape="circle"
                  :hair-line="false"
                  :ripple="true"
                  ripple-bg-color="#FFF"
                  :custom-style="{
                    width: '210rpx',
                    height: '64rpx',
                    background: 'linear-gradient(117deg, #E80404 0%, #FF9279 100%)',
                    fontSize: '28rpx',
                    fontWeight: 'bold',
                    color: '#FFF',
                    border: 'none',
                  }"
                  @click="confirmIsOriginalShipment(0)"
                  >快递箱发货</u-button
                >
              </view>
            </view>
          </view>
        </u-mask>

        <!-- 再来一单弹框 -->
        <sp-one-more-order-mask
          :show="showOneMoreOrderMask"
          :customerInfo="customerInfo"
          @click="showOneMoreOrderMask = false"
          @reminder="shelfReminder"
        />

        <!-- 再来一单心愿模态框 -->
        <sp-one-more-order-wish-modal v-model="showOneMoreOrderWishModal" />

        <!-- 再来一单消息模态框 -->
        <sp-one-more-order-message-modal v-model="showOneMoreOrderMessageModal" />

        <!-- 秒发优惠券 -->
        <template>
          <!-- 秒发优惠券弹框 -->
          <SecGoodsDetailCouponPopup
            v-if="couponInfo.coupon_list || couponInfo.rabbit_coupon"
            v-model="couponPopupVisible"
            :list="couponInfo.coupon_list"
            :rabbitCoupon="couponInfo.rabbit_coupon"
            @receiveCoupon="jumpRabbitCou"
          />
          <!-- 秒发新人优惠券弹框 -->
          <SecGoodsDetailCouponNewPopup
            v-if="couponInfo.new_user_coupon_package"
            v-model="couponPopupNewVisible"
            :couponInfo="couponInfo.new_user_coupon_package"
            @receiveCoupon="getGoodsCouponInfo"
          />
        </template>
      </view>

      <!-- 底部操作栏 -->
      <view v-if="!basicFun" class="">
        <!-- 文案预览 -->
        <view
          v-if="from == '3'"
          class="p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff d-flex j-center a-center b-sh-00021200-022 font-32 font-wei text-e80404"
          >文案预览</view
        >

        <!-- 底部操作按钮 -->
        <view v-else class="">
          <view
            class="p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff d-flex j-sb a-center b-sh-00021200-022"
            v-safeBeautyBottom="$safeBeautyBottom"
          >
            <!-- 评论、收藏 -->
            <view class="d-flex j-sb a-center ml-28">
              <view class="d-flex flex-column a-center j-center w-114 h-104" @click="commentAdd(0)">
                <image
                  class="w-40 h-40"
                  src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/comm_bla.png"
                  mode="aspectFill"
                />
                <text class="font-22 text-9 l-h-32">评论</text>
              </view>

              <view
                v-if="!goodsInfo.is_seckill && !activityPeriods"
                class="d-flex flex-column a-center j-center w-114 h-104"
                @click="collection"
              >
                <image
                  class="w-40 h-40"
                  :src="`https://images.vinehoo.com/vinehoomini/v3/comm/${isCollect ? 'star_red' : 'star_black'}.png`"
                  mode="aspectFill"
                />
                <text class="font-22 text-9 l-h-32">收藏</text>
              </view>
            </view>

            <!-- 秒杀 -->
            <view v-if="false" class="">
              <view v-if="false" class="">
                <u-button
                  shape="circle"
                  :hair-line="false"
                  :ripple="true"
                  ripple-bg-color="#FFF"
                  :custom-style="{
                    width: '440rpx',
                    height: '64rpx',
                    fontSize: '28rpx',
                    fontWeight: 'bold',
                    color: '#FFF',
                    backgroundColor: '#E80404',
                    border: 'none',
                  }"
                  >立即预约</u-button
                >
              </view>

              <view v-if="false" class="">
                <u-button
                  shape="circle"
                  :hair-line="false"
                  :ripple="true"
                  ripple-bg-color="#FFF"
                  :custom-style="{
                    width: '440rpx',
                    height: '64rpx',
                    fontSize: '28rpx',
                    fontWeight: 'bold',
                    color: '#FFF',
                    backgroundColor: '#FF9127',
                    border: 'none',
                  }"
                  >已预约</u-button
                >
              </view>
            </view>
            <!-- 闪购、秒发、跨境、尾货 -->
            <view v-if="onSaleStatus !== null" class="d-flex a-center">
              <!-- 即将开售 -->
              <view v-if="openSaleSeconds > 0" class="mr-24">
                <!-- 支付订金 -->
                <GoodsDetailPayDepositBtn v-if="isDepositGoods" @click="buyNow(4)" />

                <!-- 即将开售 -->
                <!-- <u-button
                        v-else
                        shape="circle"
                        :hair-line="false"
                        :ripple="true"
                        ripple-bg-color="#FFF"
                        :custom-style="{
                          width: '440rpx',
                          height: '64rpx',
                          fontSize: '28rpx',
                          fontWeight: 'bold',
                          color: '#FFF',
                          backgroundColor: '#FFCDCD',
                          border: 'none',
                        }"
                        @click="feedback.toast({ title: '亲，该商品即将开售！' })"
                        >即将开售</u-button
                      > -->
                <u-button
                  v-else
                  shape="circle"
                  :hair-line="false"
                  :ripple="true"
                  ripple-bg-color="#FFF"
                  :custom-style="{
                    width: '440rpx',
                    height: '64rpx',
                    fontSize: '28rpx',
                    fontWeight: 'bold',
                    color: '#FFF',
                    backgroundColor: is_reservation ? '#FFCDCD' : '#E80404',
                    border: 'none',
                  }"
                  @click="appointment"
                  >{{ is_reservation ? '已预约' : '立即预约' }}</u-button
                >
              </view>

              <!-- 正常售卖中（两种情况 1：非秒发商品（秒发跟商家秒发） && 结束售卖时间大于0 && 售卖状态 == 2）2：秒发商品 || 商家秒发 && 售卖状态 == 2 -->
              <view
                v-if="
                  (goodsInfo.periods_type !== 1 &&
                    goodsInfo.periods_type !== 9 &&
                    openSaleSeconds == 0 &&
                    endSaleSeconds > 0 &&
                    onSaleStatus == 2) ||
                  ((goodsInfo.periods_type == 1 || goodsInfo.periods_type == 9) && onSaleStatus == 2)
                "
                class="fade-in"
              >
                <!-- 特殊期数80733显示联系客服 -->
                <view v-if="goodsInfo.id === 80792 || goodsInfo.id === 158949" class="mr-24">
                  <u-button
                    shape="circle"
                    :hair-line="false"
                    :ripple="true"
                    ripple-bg-color="#FFF"
                    :custom-style="{
                      width: '440rpx',
                      height: '64rpx',
                      fontSize: '28rpx',
                      fontWeight: 'bold',
                      color: '#FFF',
                      backgroundColor: '#E80404',
                      border: 'none',
                    }"
                    :throttleTime="0"
                    @click="openService"
                    >咨询客服</u-button
                  >
                </view>

                <!-- 其他商品正常显示 -->
                <view v-else>
                  <!-- 拼团 -->
                  <view v-if="isGroup" class="d-flex">
                    <view class="w-234 h-104 bg-ff9127 d-flex flex-column j-center a-center" @click="buyNow(0)">
                      <text class="font-28 text-ffffff">¥{{ packageList[0] && packageList[0].price }}</text>
                      <text class="font-28 font-wei text-ffffff">单独购买</text>
                    </view>

                    <view class="w-234 h-104 bg-e80404 d-flex flex-column j-center a-center" @click="buyNow(1)">
                      <text class="font-28 text-ffffff"
                        >¥{{ groupPackageList[0] && groupPackageList[0].group_price }}</text
                      >
                      <text class="font-28 font-wei text-ffffff">{{
                        groupId &&
                        groupInfo.group_status !== 3 &&
                        groupInfo.group_last_num > 0 &&
                        groupInfo.remaining_time > 0
                          ? '立即拼团'
                          : '发起拼团'
                      }}</text>
                    </view>
                  </view>

                  <!-- 新人 -->
                  <view v-else-if="isNewPeopleGoods" class="d-flex a-center mr-24">
                    <u-button
                      shape="circle"
                      :hair-line="false"
                      :ripple="true"
                      ripple-bg-color="#FFF"
                      :custom-style="{
                        width: '440rpx',
                        height: '64rpx',
                        fontSize: '28rpx',
                        fontWeight: 'bold',
                        color: '#FFF',
                        backgroundColor: '#E80404',
                        border: 'none',
                      }"
                      :throttleTime="0"
                      @click="buyNow(2)"
                      >立即购买</u-button
                    >
                  </view>

                  <!-- 正常普通商品 (闪购、秒发、跨境、尾货)-->
                  <view v-else class="mr-24">
                    <!-- 闪购、秒发、商家秒发 -->
                    <view v-if="goodsInfo.is_seckill" class="">
                      <u-button
                        shape="circle"
                        :hair-line="false"
                        :ripple="true"
                        ripple-bg-color="#FFF"
                        :custom-style="{
                          width: '440rpx',
                          height: '64rpx',
                          fontSize: '28rpx',
                          fontWeight: 'bold',
                          color: '#FFF',
                          backgroundColor: '#E80404',
                          border: 'none',
                        }"
                        :throttleTime="0"
                        @click="buyNow(0)"
                        >立即购买</u-button
                      >
                    </view>
                    <view
                      v-else-if="goodsInfo.periods_type <= 1 || goodsInfo.periods_type === 9"
                      class="d-flex a-center"
                    >
                      <view v-if="!activityPeriods" class="">
                        <u-button
                          shape="circle"
                          :hair-line="false"
                          :ripple="true"
                          ripple-bg-color="#FFF"
                          :custom-style="{
                            width: '208rpx',
                            height: '64rpx',
                            fontSize: '28rpx',
                            fontWeight: 'bold',
                            color: '#FFF',
                            backgroundColor: '#FF9127',
                            border: 'none',
                          }"
                          :throttleTime="0"
                          @click="addShoppingCart"
                          >加入购物车</u-button
                        >
                      </view>
                      <view class="ml-24">
                        <u-button
                          shape="circle"
                          :hair-line="false"
                          :ripple="true"
                          ripple-bg-color="#FFF"
                          :custom-style="{
                            width: '208rpx',
                            height: '64rpx',
                            fontSize: '28rpx',
                            fontWeight: 'bold',
                            color: '#FFF',
                            backgroundColor: '#E80404',
                            border: 'none',
                          }"
                          :throttleTime="0"
                          @click="buyNow(0)"
                          >立即购买</u-button
                        >
                      </view>
                    </view>

                    <!-- 跨境、尾货、非商家秒发 -->
                    <view v-if="goodsInfo.periods_type >= 2 && goodsInfo.periods_type !== 9" class="">
                      <u-button
                        shape="circle"
                        :hair-line="false"
                        :ripple="true"
                        ripple-bg-color="#FFF"
                        :custom-style="{
                          width: '440rpx',
                          height: '64rpx',
                          fontSize: '28rpx',
                          fontWeight: 'bold',
                          color: '#FFF',
                          backgroundColor: '#E80404',
                          border: 'none',
                        }"
                        :throttleTime="0"
                        @click="buyNow(0)"
                        >立即购买</u-button
                      >
                    </view>
                  </view>
                </view>
              </view>

              <!-- 商品售卖结束（结束售卖时间 == 0 && 非秒发商品 && 商家秒发商品） -->
              <view
                v-if="
                  (endSaleSeconds == 0 && goodsInfo.periods_type !== 1 && goodsInfo.periods_type !== 9) ||
                  onSaleStatus == 3 ||
                  onSaleStatus == 4
                "
                class="fade-in d-flex a-center mr-24"
              >
                <view class="">
                  <u-button
                    shape="circle"
                    :hair-line="false"
                    :ripple="true"
                    ripple-bg-color="#FFF"
                    :custom-style="{
                      width: '208rpx',
                      height: '64rpx',
                      fontSize: '28rpx',
                      fontWeight: 'bold',
                      color: '#FFF',
                      backgroundColor: '#DDDDDD',
                      border: 'none',
                    }"
                    @click="feedback.toast({ title: '亲，该商品已经售卖结束啦！' })"
                    >售卖结束</u-button
                  >
                </view>
                <view class="ml-24">
                  <u-button
                    shape="circle"
                    :hair-line="false"
                    :ripple="true"
                    ripple-bg-color="#FFF"
                    :custom-style="{
                      width: '208rpx',
                      height: '64rpx',
                      fontSize: '28rpx',
                      fontWeight: 'bold',
                      color: '#FFF',
                      backgroundColor: '#E80404',
                      border: 'none',
                    }"
                    @click="iWant"
                  >
                    我想要
                  </u-button>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 骨架屏 -->
    <vh-skeleton v-else :type="1" :has-navigation-bar="false" />
    <u-popup
      v-model="reservationPopStatus"
      mode="center"
      width="552rpx"
      :height="'248px'"
      border-radius="20"
      :mask-close-able="false"
    >
      <view class="p-rela wh-p100">
        <image
          :src="ossIcon('/auction/appointment.png')"
          style="width: 197px; height: 121px"
          class="p-abso appointment"
        />
        <view class="p-rela pt-84">
          <view class="font-32 appointment-text text-3 l-h-48 text-center mt-92 font-wei">
            <view>预约成功</view>
          </view>
          <view class="font-26 appointment-desc text-6 l-h-48 text-center">
            <view>{{ reservationPopContent }}</view>
          </view>
          <view
            style="
              height: 32px;
              border-radius: 16px;
              margin: 0 auto;
              margin-top: 90rpx;
              justify-content: space-evenly;
              align-items: center;
            "
            class="d-flex"
          >
            <view class="appointment-left-bt" @click="reservationPopStatus = false">知道了</view>
            <view v-if="from != ''" class="appointment-right-bt" @click="onAppShare">邀好友</view>
            <view v-else class="appointment-right-bt" @click="copy()">邀好友</view>
          </view>
        </view>
      </view>
    </u-popup>

    <!-- <u-modal :title="'预约成功'" v-model="reservationPopStatus" :content="reservationPopContent"></u-modal> -->
  </view>
</template>

<script>
import emoj from '@/common/js/data/rabbitEmoji.js'
import { mapState, mapMutations } from 'vuex'
import crossBorderNotices from '@/common/js/utils/crossBorderNotices'
import { CHOOSE_WINE_ACTIVITY_ID } from '@/common/js/fun/constant'
import userMixin from '@/common/js/mixins/userMixin'
let goodsCommentsObserver = null
let goodsRecommendsObserver = null
export default {
  name: 'goods-detail',

  mixins: [userMixin],

  data() {
    return {
      reservationPopContent: '薅羊毛的好事，怎能不叫上好友呢？',
      fetch_url: '',
      is_reservation: false,
      crossBorderNotices,
      osip: 'https://images.vinehoo.com/vinehoomini/v3', //oss静态图片地址前缀（oss static image prefix）
      loading: true, //加载状态
      pageLength: 0, //页面栈长度
      from: '', //从哪个端进入 1 = 安卓、2 = ios"、3 = pc
      tabList: [{ name: '商品' }, { name: '详情' }, { name: '评论' }], //tabs选项列表
      tabsCurrent: 0, //当前选中的tabs
      // tabsIndex:-1, //tabs索引
      goodsId: '', //商品id
      auth: '',
      version: '0',
      activityPeriods: '',
      fromSearch: '', //搜索关键字
      shoppingCartNum: 0, //购物车数量
      goodsInfo: {}, //商品信息
      crossBorderNoticeNotPrompting: 0, //跨境购买须知是否不再提示
      packageAllInfo: {}, //套餐所有信息
      openSaleTimer: null, //开始售卖定时器
      endSaleTimer: null, //结束售卖定时器
      openSaleSeconds: 0, //距离开始售卖的时间（秒）
      endSaleSeconds: 0, // 距离结束售卖的时间（秒）
      discountInfo: {}, //优惠区信息
      groupId: '', //拼团id
      groupInfo: {}, //拼团信息
      packageIndex: 0, //选中的套餐索引
      clickSelectPackage: 0, //是否点击显示套餐 0 = 点击购买、加入购物车、拼团、1 = 点击套餐选项
      packageType: 0, //套餐类型 0 = 普通套餐（闪购、秒发、跨境、尾货）、1 = 拼团、2 = 新人
      packageList: [], //普通套餐列表（闪购、秒发、跨境、尾货）
      groupPackageList: [], //拼团套餐列表
      newPeoplePackList: [], //新人套餐列表
      depositPackageList: [], //订金套餐列表
      packageInfo: {}, //套餐信息
      isForceOriginalBox: 0, // 是否必发原箱 1 是 0 否
      isOriginalBox: 0, // 0 = 非原箱、1 = 原箱发货
      limitInfo: {
        aleradyBuy: 0, //已购
        limitNumber: 0, //限量
      }, //限购信息
      onSaleStatus: null, //售卖状态 0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄
      purchaseNumbers: 1, //购买数量
      scrollTop: 0, //距离顶部的距离
      // estimatedDeliveryTime:'', //预计发货时间
      wineStyleTabsList: [], //酒款tabs选项列表
      currentWineStyleTabs: 0, //选中的酒款tabs
      wineStyleInfo: {}, //酒款信息
      reservationPopStatus: false,
      isCollect: 0, //是否收藏
      isShowLocalImage: false,

      // 评论板块
      commentList: [], //评论列表
      // commentTotal: 0, //评论总条数
      commentImgReg: '', //表情包图片正则
      isReply: 0, // 是否为回复 0 = 评论、1 = 回复
      replyObj: {}, //回复对象
      commPlaceholder: '', //评论占位
      protocolList: [], //协议列表
      page: 1, //第几页
      limit: 10, //每页显示多少条
      totalPage: 1, //总页数
      loadStatus: 'loadmore', //底部加载状态

      // 弹框
      showOnLineGoodsMask: false, //是否显示线上商品弹框提示
      showCommPop: false, //评论弹框
      isAddShoppingCart: false, //是否加入购物车
      showGoodsPackPop: false, //套餐弹框
      showAppoModel: false, //预约弹框
      showAppoSuccPop: false, //预约成功弹框
      showCrossBorderNoticePop: false, //跨境商品购买须知弹框
      showOriginalBoxMask: false, //是否为原箱弹框

      openDownLoadAppTimer: null,
      swiperPreviewIndex: 0,

      isHiddenPrice: false,

      basicFun: 0,

      // 再来一单
      wishId: '', //心愿清单id
      customerInfo: {}, //客服信息
      showOneMoreOrderMask: false, //是否显示再来一单遮罩层
      showOneMoreOrderWishModal: false, //是否显示再来一单心愿清单模态框
      showOneMoreOrderMessageModal: false, //是否显示再来一单消息通知模态框

      activityData: {},
      isNewUser: 1,
      couponPopupVisible: false,
      couponPopupNewVisible: false,
      couponInfo: {
        coupon_list: [],
        new_user_coupon_package: {},
      },
      couponInfoLoading: true,
      hasGotCouponInfo: 0,
      inventoryRemainInfo: [],
      isLoadedGoodsRecommends: false,
      isLoadedGoodsComments: false,
      isRecordedPreSalesSource: false,
      recordPreSalesSourceParams: null,
      oneSource: null,
      appSource: {},
      goodsFullReductionLabel: '',
      goodsFullReductionUrl: '',
      selectedProducts: [], // 已选择的产品ID列表
      matchedSubPackage: null, // 匹配到的子套餐
    }
  },
  computed: {
    //Vuex 辅助state函数
    ...mapState(['agreementPrefix', 'storeInfo', 'ossPrefix', 'dev']),

    // 获取状态栏高度
    statusBarHeight() {
      return this.system.getSysInfo().statusBarHeight
    },

    // 商品是否需要隐藏价格
    needHiddenPrice({ isHiddenPrice, onSaleStatus }) {
      return !!(isHiddenPrice == 1 || [3, 4].includes(onSaleStatus))
    },

    // 是否配置拼团商品
    isGroup() {
      if (this?.goodsInfo?.marketing_attribute?.includes('1')) {
        return true
      }
      return false
    },

    // 是否配置新人活动商品
    isConfNewPeopleGoods() {
      if (this?.goodsInfo?.marketing_attribute?.includes('2')) {
        return true
      }
      return false
    },

    // 是否为新人商品
    isNewPeopleGoods({ isConfNewPeopleGoods, isNewUser }) {
      return !!(isConfNewPeopleGoods && isNewUser)
    },

    // 是否为秒发新人优惠券板块
    isSecondNewPeopleGoods({ isMiaoFaOrBusinessMiaofa, packageAllInfo, isNewUser, couponInfo }) {
      const {
        new_user_coupon_package: { is_draw = 1 },
      } = couponInfo
      const { is_support_coupon = 0 } = packageAllInfo
      // return !!( is_support_coupon && isNewUser && !is_draw )
      return false //---关闭新人券
    },

    // 是否为秒发新人优惠券板块且配置了新人活动
    isSecondNewPeopleGoodsConfNewActivity({ needHiddenPrice, isNewUser, isConfNewPeopleGoods }) {
      // return !!( !needHiddenPrice && isNewUser && isConfNewPeopleGoods )
      return false //---关闭新人券
    },

    // 获取频道
    getChannel() {
      if (this.isGroup) {
        // 拼团商品（营销活动设置为拼团商品）前端默认定义为101，供封装的组件传参
        return 101
      }
      return this.goodsInfo.periods_type // 闪购、秒发、跨境、尾货商品
    },

    // 计算预计发货时间
    estimatedDeliveryTime() {
      const { warehouse_type = 0, msg: businessShipTips = '3小时内送达' } = this.goodsInfo //拿到商品详情信息
      const { predict_shipment_time } = this.packageAllInfo //所有套餐信息
      if (warehouse_type == 1) {
        //商家三小时达
        console.log('---------我是商家三小时达文案')
        console.log(businessShipTips)
        return businessShipTips
      } else {
        if (!predict_shipment_time) return
        let curDate = new Date() //当前时间
        let shipDate = new Date(predict_shipment_time.replace(/-/g, '/')) //结束时间
        let seconds = Math.floor((shipDate.getTime() - curDate.getTime()) / 1000) //得到两个日期之间的秒数
        if (seconds >= 0) {
          console.log('---------预计发货时间大于等于当前时间')
          return predict_shipment_time.substr(0, 10)
        } else {
          console.log('---------预计发货时间小于当前时间')
          let ruleShipmentDate = ''
          switch (this.goodsInfo.periods_type) {
            case 0:
            case 3:
              console.log('---------我是闪购或者尾货')
              ruleShipmentDate = this.$u.timeFormat(
                new Date(curDate.getTime() + 24 * 60 * 60 * 1000).getTime(),
                'yyyy-mm-dd'
              )
              break
            case 1:
            case 9:
              console.log('---------我是秒发')
              if (curDate.getHours() >= 16)
                ruleShipmentDate = this.$u.timeFormat(
                  new Date(curDate.getTime() + 28 * 60 * 60 * 1000).getTime(),
                  'yyyy-mm-dd'
                )
              else
                ruleShipmentDate = this.$u.timeFormat(
                  new Date(curDate.getTime() + 4 * 60 * 60 * 1000).getTime(),
                  'yyyy-mm-dd'
                )
              break
            case 2:
              console.log('---------我是跨境')
              ruleShipmentDate = this.$u.timeFormat(
                new Date(curDate.getTime() + 72 * 60 * 60 * 1000).getTime(),
                'yyyy-mm-dd'
              )
              break
          }
          console.log(ruleShipmentDate)
          return ruleShipmentDate
        }
      }
    },
    realPredictShipmentTime() {
      if (!this.inventoryRemainInfo.length) return this.estimatedDeliveryTime
      const currPackage = this.packageList[this.packageIndex]
      const productList = JSON.parse(currPackage.associated_products).map((item) => {
        item.nums = item.nums * this.purchaseNumbers
        return item
      })
      const predictShipmentTime = Math.floor(
        new Date(this.packageAllInfo.predict_shipment_time.replace(/-/g, '/')).getTime() / 1000
      )
      const filteredInventoryInfo = []
      productList.forEach((product) => {
        const findItem = this.inventoryRemainInfo.find(
          (item) =>
            product.product_id === item.product_id &&
            product.nums > item.residue_nums &&
            item.predict_shipment_time > predictShipmentTime
        )
        if (findItem) {
          filteredInventoryInfo.push(findItem)
        }
      })
      if (filteredInventoryInfo.length) {
        let timestamp = filteredInventoryInfo[0].predict_shipment_time * 1000
        const currDate = new Date()
        if (timestamp < currDate.getTime()) {
          switch (this.goodsInfo.periods_type) {
            case 0:
            case 3:
              timestamp = currDate.getTime() + 24 * 60 * 60 * 1000
              break
            case 1:
              if (currDate.getHours() >= 16) timestamp = currDate.getTime() + 28 * 60 * 60 * 1000
              else timestamp = currDate.getTime() + 4 * 60 * 60 * 1000
              break
            case 2:
              timestamp = currDate.getTime() + 72 * 60 * 60 * 1000
              break
          }
        }
        return this.$u.timeFormat(timestamp, 'yyyy-mm-dd')
      }
      return this.estimatedDeliveryTime
    },

    // 获取兔头表情包
    getEmojiMap() {
      return emoj
    },

    isMiaoFa({ getChannel }) {
      return getChannel === 1
    },

    isMiaoFaOrBusinessMiaofa({ getChannel }) {
      return !![1, 9].includes(getChannel)
    },

    isKuaJing({ getChannel }) {
      return getChannel === 2
    },
    isChooseWineActivity({ activityData }) {
      const { id = 0 } = activityData
      return CHOOSE_WINE_ACTIVITY_ID() === id
    },
    activityBorderImage({ activityData }) {
      const { title_map = '' } = activityData
      return title_map
    },
    activityCapsuleImage({ activityData }) {
      const { activity_url = '', list_back = '' } = activityData
      return activity_url && list_back ? list_back : ''
    },
    isGrouping({ getChannel }) {
      return getChannel === 101
    },
    currPrice({ isGrouping, packageList, groupPackageList }) {
      return isGrouping ? groupPackageList?.[0]?.group_price : packageList?.[0]?.price
    },
    currMarketPrice({ isGrouping, packageList, groupPackageList }) {
      return isGrouping ? groupPackageList?.[0]?.market_price : packageList?.[0]?.market_price
    },
    isShowSparkingAttention({ goodsInfo }) {
      const { periods_type = '', product_info = [] } = goodsInfo
      return (
        [0, 2, 3, 9].includes(periods_type) && product_info.some((item) => item.product_type_name.includes('起泡酒'))
      )
    },

    isDepositGoods({ depositPackageList }) {
      return !!depositPackageList.length
    },

    packagePrice({ packageInfo, packageInfo: { group_price, newcomer_price, deposit_price, price }, packageType }) {
      if (Object.keys(packageInfo).length) {
        console.log('packageInfo', packageInfo, 'packageType', packageType)
        if (packageType === 1) return group_price
        else if (packageType === 2) return newcomer_price
        else if (packageType === 4) return deposit_price
        else return price
      }
    },
    isShowSparkingAttention({ goodsInfo }) {
      const { periods_type = '', product_info = [] } = goodsInfo
      return (
        [0, 2, 3, 9].includes(periods_type) && product_info.some((item) => item.product_type_name.includes('起泡酒'))
      )
    },
    // 判断自选产品是否已选择足够数量
    isCustomProductSelected() {
      if (!this.packageInfo.is_custom_package) return true
      return this.selectedProducts.length >= this.packageInfo.custom_product_count
    },
    // 获取当前显示的价格（如果匹配到子套餐则使用子套餐价格）
    currentPackagePrice() {
      if (this.packageInfo.is_custom_package === 1 && this.matchedSubPackage) {
        return this.matchedSubPackage.price
      }
      return this.packagePrice
    },
    // 获取当前显示的市场价格
    currentMarketPrice() {
      if (this.packageInfo.is_custom_package === 1 && this.matchedSubPackage) {
        return this.matchedSubPackage.market_price
      }
      return this.packageInfo.market_price
    },
  },

  onLoad(options) {
    console.log('这是商品详情的options')
    console.log(options)
    this.system.setNavigationBarBlack()
    this.pageLength = this.pages.getPageLength()
    this.goodsId = parseInt(options.id) //商品id（商品期数）
    this.auth = options.auth
    this.groupId = parseInt(options.groupId) || '' //拼团id
    this.activityPeriods = options.activity_periods || ''
    if (options.from_search) this.fromSearch = decodeURIComponent(options.from_search) //来自搜索页面的关键字
    if (options.version) {
      this.version = options.version
    }
    if (options.from) {
      this.from = options.from
      this.muFrom(this.from) //保存来自哪个状态（解决用户在安卓、ios端登录失效的问题）
      if (options.basicFun) this.basicFun = +options.basicFun
    }
    // this.pages.getPrvePageFullPath()
    const { source_platform, source_event, source_user } = options
    if (source_platform === 'vinehoo' && source_event === 'share' && source_user) {
      this.recordPreSalesSourceParams = { period: this.goodsId, source_user }
    }
    if (source_platform && source_event) {
      this.appSource = {
        sourcePlatform: source_platform,
        sourceEvent: source_event,
        sourceUser: source_user,
      }
    }
    const { one_source_platform = '', one_source_event = '', one_source_user = '' } = options
    if (one_source_platform && one_source_event) {
      this.oneSource = {
        oneSourcePlatform: one_source_platform,
        oneSourceEvent: one_source_event,
        oneSourceUser: one_source_user,
      }
    }
    this.getVersion()
  },

  onShow() {
    this.login.isLoginV3(this.$vhFrom, 0).then((isLogin) => {
      if (isLogin) {
        this.isReservation()
      }
    })
    this.login.isLoginV3(this.$vhFrom, 0).finally(() => {
      this.init().finally(() => {
        this.openGoodsCommentsObserver()
        this.openGoodsRecommendsObserver()
      })
      this.recordPreSalesSource()
    })
  },
  onHide() {
    this.openDownLoadAppTimer && clearTimeout(this.openDownLoadAppTimer)
    this.closeGoodsCommentsObserver()
    this.closeGoodsRecommendsObserver()
    this.$refs?.goodsDetailDepositInfoRef?.endCountDown()
  },

  methods: {
    // Vuex mapMutations辅助函数
    ...mapMutations(['muOrderInfo', 'muFrom', 'muVersion']),
    ...mapMutations('shoppingCart', ['modifyAddShoppingCartStatus']),
    isReservation() {
      this.$u.api.isReservation({ period: this.goodsId }).then((res) => {
        console.log()
        this.is_reservation = res.data.is_reservation
      })
    },
    selectProduct(productId) {
      const index = this.selectedProducts.indexOf(productId)
      if (index > -1) {
        // 已选中，取消选择
        this.selectedProducts.splice(index, 1)
      } else {
        // 未选中，检查是否已达到最大选择数量
        if (this.selectedProducts.length < this.packageInfo.custom_product_count) {
          this.selectedProducts.push(productId)
        } else {
          this.feedback.toast({ title: `最多只能选择${this.packageInfo.custom_product_count}个产品` })
        }
      }
      // 选择产品后尝试匹配子套餐
      this.matchSubPackage()
    },

    // 匹配自选套餐的子套餐
    matchSubPackage() {
      // 如果不是自选套餐或者没有选择足够的产品，则清空匹配结果
      if (
        !this.packageInfo.is_custom_package ||
        this.selectedProducts.length !== this.packageInfo.custom_product_count
      ) {
        this.matchedSubPackage = null
        return
      }

      // 如果没有子套餐列表，则使用默认套餐信息
      if (!this.packageInfo.package_list || this.packageInfo.package_list.length === 0) {
        this.matchedSubPackage = null
        return
      }

      // 遍历子套餐列表，查找匹配的套餐
      for (const subPackage of this.packageInfo.package_list) {
        if (this.isPackageMatched(subPackage)) {
          this.matchedSubPackage = subPackage
          console.log('匹配到子套餐:', subPackage)
          return
        }
      }

      // 没有找到匹配的子套餐
      this.matchedSubPackage = null
      console.log('未找到匹配的子套餐，使用默认套餐')
    },

    // 检查子套餐是否与用户选择的产品匹配
    isPackageMatched(subPackage) {
      try {
        // 解析子套餐的关联产品
        let associatedProducts = []
        if (typeof subPackage.associated_products === 'string') {
          associatedProducts = JSON.parse(subPackage.associated_products)
        } else if (Array.isArray(subPackage.associated_products)) {
          associatedProducts = subPackage.associated_products
        } else {
          return false
        }

        // 提取子套餐中的产品ID列表（排除赠品）
        const subPackageProductIds = associatedProducts
          .filter((item) => item.isGift === 0 || item.isGift === false || item.is_gift === 0 || item.is_gift === false)
          .map((item) => item.product_id)
          .sort()

        // 用户选择的产品ID列表（排序后比较）
        const selectedProductIds = [...this.selectedProducts].sort()

        // 检查数量是否匹配
        if (subPackageProductIds.length !== selectedProductIds.length) {
          return false
        }

        // 检查产品ID是否完全匹配
        return subPackageProductIds.every((productId, index) => productId === selectedProductIds[index])
      } catch (error) {
        console.error('解析子套餐关联产品时出错:', error)
        return false
      }
    },

    copy() {
      uni.setClipboardData({
        data: window.location.href,
        success: function () {
          // 可以添加用户友好的提示，例如使用uni.showToast提示复制成功
          uni.showToast({
            title: '复制链接成功',
            icon: 'success',
            duration: 2000,
          })
        },
      })
    },
    // 初始化（接口聚合:  聚合商品详情、套餐详情、商品收藏状态...）
    compareVersions(v1, v2) {
      console.warn(v1, v2)
      const v1Parts = v1.split('.').map(Number)
      const v2Parts = v2.split('.').map(Number)

      for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
        const v1Part = v1Parts[i] || 0
        const v2Part = v2Parts[i] || 0

        if (v1Part > v2Part) return 1
        if (v1Part < v2Part) return -1
      }

      return 0
    },
    getVersion() {
      this.isShowLocalImage = false
      if (this.from == '1') {
        // Android
        if (this.compareVersions(this.version, '9.3.5') >= 0) {
          // 版本大于等于2.2.1的逻辑
          this.isShowLocalImage = true
        } else {
          // 版本小于2.2.1的逻辑
        }
      } else if (this.from == '2') {
        // iOS
        if (this.compareVersions(this.version, '9.42') >= 0) {
          this.isShowLocalImage = true
          // 版本大于等于3.21的逻辑
        } else {
          // 版本小于3.21的逻辑
        }
      }
    },
    async init() {
      this.showOneMoreOrderMask = false
      this.page = 1
      this.totalPage = 1
      this.isOriginalBox = 0
      try {
        await this.getGoodsDetail()
        this.loading = false
        // await this.getIsNewUser(), //是否为新用户
        if (this.goodsInfo.is_seckill) {
          await Promise.all([
            // this.getShoppingCartNum(), ---秒杀
            this.getPackageDetail(),
            this.getGroupInfo(),
            // this.getGoodsDiscount(), ---秒杀
            // this.getGoodsCommentList(),
            // this.getGoodsCollectionStatus(), ---秒杀
            // this.initActivityData() ---秒杀
          ])
        } else {
          await Promise.all([
            this.getShoppingCartNum(), //---秒杀
            this.getPackageDetail(),
            this.getGroupInfo(),
            this.getGoodsDiscount(), //---秒杀
            // this.getGoodsCommentList(),
            this.getGoodsCollectionStatus(), //---秒杀
            this.initActivityData(), //---秒杀
          ])
        }

        this.getNotPromptingCrossBorderNotice()
        uni.stopPullDownRefresh() //停止下拉刷新
      } catch (e) {
        console.log(e)
        this.goBack()
      }
    },

    // 获取是否不再提示跨境购买须知
    getNotPromptingCrossBorderNotice() {
      if (this.getChannel === 2) {
        this.crossBorderNoticeNotPrompting = uni.getStorageSync('crossBorderNoticeNotPrompting') || 0
      }
    },

    // 获取商品详情
    async getGoodsDetail() {
      const t = this.from === '3' ? Date.now() : 1
      let res = await this.$u.api.goodsDetailJson({ t, isJson: true, id: this.goodsId })
      if (Object.keys(this.goodsInfo).length) {
        const { warehouse_type, businessShipTips } = this.goodsInfo
        this.goodsInfo = Object.assign({}, this.goodsInfo, { warehouse_type, businessShipTips })
      } else {
        this.goodsInfo = res.data
      }
      this.feedback.hideLoading()
      this.goodsInfo.swiperList = [...this.goodsInfo.banner_img, ...this.goodsInfo.product_img]
      this.getWineStyleList()

      // 获取定位商家信息
      this.getBusinessLabel()
    },

    // 获取购物车数量
    async getShoppingCartNum() {
      if (this.login.isLogin(this.from, 0)) {
        let res = await this.$u.api.shoppingCartNum()
        console.log('-----------------------------我是购物车数量接口')
        console.log(res)
        this.shoppingCartNum = res.data
      }
    },

    // 是否新用户
    async getIsNewUser() {
      if (this.login.isLogin(this.from, 0)) {
        const {
          data: { is_new_user = 1 },
        } = await this.$u.api.userSpecifiedData({ field: 'is_new_user' })
        this.isNewUser = +is_new_user
      }
    },
    async appointment() {
      if (this.login.isLogin(this.from)) {
        if (!this.is_reservation) {
          const res = await this.$u.api.reservationGoods({
            period: this.goodsId, //期数
            periods_type: this.goodsInfo.periods_type, //频道
          })
          if (res.error_code === 0) {
            this.reservationPopStatus = true
            this.is_reservation = true
          }
        }
      }
    },
    // 获取商品优惠券信息
    async getGoodsCouponInfo() {
      // if( this.isMiaoFaOrBusinessMiaofa ) {
      if (this.hasGotCouponInfo) this.feedback.loading()
      const { id: periods, periods_type } = this.goodsInfo
      const { coupon_package_id } = this.packageAllInfo
      if (coupon_package_id) {
        const {
          data: { coupon_list, new_user_coupon_package },
        } = await this.$u.api.goodsCouponInfo({ periods, periods_type, is_new_user: this.isNewUser, coupon_package_id })
        this.couponInfo.coupon_list = coupon_list
        this.couponInfo.new_user_coupon_package = new_user_coupon_package
      }
      this.couponInfoLoading = false
      this.hasGotCouponInfo = 1
      // }
    },

    getGoodsFullReductionLabel() {
      if (!this.packageAllInfo.is_support_reduction) {
        this.goodsFullReductionLabel = ''
        return
      }
      try {
        const { periods_type } = this.goodsInfo
        this.$u.api
          .getGoodsFullReductionLabel({ data: [{ period: this.goodsId, period_type: periods_type }] })
          .then((res) => {
            const labelObj = res?.data?.activity || {}
            const { top_image = '', top_image_skip_url = '' } = labelObj[this.goodsId]?.[0] || {}
            this.goodsFullReductionLabel = top_image
            this.goodsFullReductionUrl = top_image_skip_url
          })
      } catch (e) {}
    },

    onGoodsFullReductionJump() {
      if (!this.goodsFullReductionUrl) return
      this.jump.h5Jump(this.goodsFullReductionUrl, this.$vhFrom, 4)
    },

    // 获取商家标签
    getBusinessLabel() {
      if (!['1', '2'].includes(this.from)) return
      const { id, periods_type } = this.goodsInfo
      if (periods_type === 9) {
        const reqFun = async (loationInfo) => {
          const { longitude = '', latitude = '' } = loationInfo
          if (longitude && latitude) {
            const params = {
              lonlat: `${longitude},${latitude}`,
              good_id: id,
            }
            const res = await this.$u.api.businessShipTips(params)
            this.goodsInfo = { ...this.goodsInfo, ...res.data }
          }
        }

        if (this.from === '1') {
          const appLocationInfoStr = wineYunJsBridge.getDataFromApp(2)
          const appLocationInfo = JSON.parse(appLocationInfoStr || '{}')
          const locationInfo = appLocationInfo?.locationM || {}
          console.log('locationInfo', locationInfo)
          reqFun(locationInfo)
        } else if (this.from === '2') {
          window.appLocationInfo = (appLocationInfoStr) => {
            const appLocationInfo = JSON.parse(appLocationInfoStr || '{}')
            const coordinate = appLocationInfo?.coordinate || ','
            const [longitude, latitude] = coordinate.split(',')
            const locationInfo = { longitude, latitude }
            console.log('locationInfo', locationInfo)
            reqFun(locationInfo)
          }
          wineYunJsBridge.openAppPage({ client_path: 'appLocationInfo', name: '获取位置经纬度信息' })
        }
      }
    },

    // 获取套餐详情
    async getPackageDetail() {
      try {
        if (this.hasGotCouponInfo) this.feedback.loading()
        let { periods_type } = this.goodsInfo //拿到商品信息
        let data = {
          t: new Date().getTime(),
        }
        data.period = this.goodsId //商品期数
        data.periods_type = periods_type //商品频道
        if (this.isGroup) data.is_group = 1 //有拼团配置
        if (this.isNewPeopleGoods) data.is_new_comer = 1 //有新人配置
        if (this.fromSearch) data.from_search = this.fromSearch //来自搜索页面的关键字
        if (this.activityPeriods) {
          data.activity_periods = this.activityPeriods
        }
        // let res = await this.$u.api.packageDetail(data)
        let res = {}
        if (this.goodsInfo.is_seckill) {
          window.p_id = this.goodsId
          res = await this.$u.api.packageDetailSeckill(data)
        } else if (periods_type === 1) {
          res = await this.$u.api.secondPackageDetail(data) // secondPackageDetail秒发套餐接口相对之前的接口多了（新人价格、折扣, 优惠券信息） packageDetail
        } else if (periods_type === 9) {
          res = await this.$u.api.vmallPackageDetail(data)
        } else {
          res = await this.$u.api.packageDetail(data)
        }
        // let res = periods_type == 9 ? await this.$u.api.vmallPackageDetail(data) : await this.$u.api.packageDetail(data)
        console.log('--------------------------------我是套餐信息')
        console.log(res)
        if (this.goodsInfo.is_seckill && res.data.fetch_url) {
          this.fetch_url = res.data.fetch_url
        }
        // this.packageIndex = 0 //重置索引状态
        let {
          packageList,
          purchased,
          limit_number,
          onsale_status,
          sell_time,
          sold_out_time,
          is_hidden_price,
          rabbit_coupon,
        } = res.data
        this.packageAllInfo = res.data //所有套餐信息
        this.couponInfo.rabbit_coupon = rabbit_coupon || {}
        if (!this.goodsInfo.is_seckill) {
          this.getGoodsCouponInfo() //---秒杀
          this.getGoodsFullReductionLabel() //---秒杀
        }

        // this.estimatedDeliveryTime = this.calEstimateTime() //预计发货时间
        this.startCountDown('openSale', this.date.getSeconds(sell_time)) //开始售卖倒计时
        this.startCountDown('endSale', this.date.getSeconds(sold_out_time)) //结束售卖倒计时

        this.onSaleStatus = onsale_status //售卖状态 0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄
        // 清空套餐
        this.packageList = []
        this.groupPackageList = []
        this.newPeoplePackList = []
        this.depositPackageList = []

        this.packageList = packageList.filter((v) => {
          return v.is_hidden == 0 && Number(v.price) > 0
        }) //普通套餐列表
        this.groupPackageList = packageList.filter((v) => {
          return v.is_hidden == 0 && Number(v?.group_price) > 0
        }) //拼团套餐列表
        this.newPeoplePackList = packageList.filter((v) => {
          return v.is_hidden == 0 && Number(v?.newcomer_price) > 0
        }) //新人套餐列表
        if (this.from === '3') {
          if ([0, 1].includes(onsale_status) && periods_type === 0)
            this.depositPackageList = packageList.filter((v) => v.is_deposit && Number(v?.deposit_price)) //订金套餐列表
        } else {
          if (onsale_status === 1 && periods_type === 0)
            this.depositPackageList = packageList.filter((v) => v.is_deposit && Number(v?.deposit_price)) //订金套餐列表
        }
        this.limitInfo = { aleradyBuy: purchased, limitNumber: limit_number }
        this.selectPackage(0, this.packageType)
        // this.packageInfo = packageList[this.packageIndex]
        // const { force_original_package } = this.packageInfo
        // this.isForceOriginalBox = force_original_package
        // this.isOriginalBox = force_original_package ? 1 : 0

        // this.purchaseNumbers = 1
        // this.clickSelectPackage = 1
        // this.packageIndex = index
        // const { force_original_package } = this.packageInfo
        // this.isForceOriginalBox = force_original_package
        // this.isOriginalBox = force_original_package ? 1 : 0

        this.isHiddenPrice = is_hidden_price
        if (this.goodsId == 80792 || this.goodsId == 158949) {
          this.isHiddenPrice = 1
        }
        this.$refs?.goodsDetailDepositInfoRef?.startCountDown()
      } catch (e) {
        //TODO handle the exception
      }
    },
    goJumpPurchasingSaid() {
      let path = ''
      if (!this.dev) {
        path = 'https://activity-cdn.vinehoo.com'
      } else {
        path = 'https://test-activity.wineyun.com'
      }

      this.jump.h5Jump(path + '/activities-v3/PurchaseGoods?id=' + this.goodsInfo.buyer_id, this.$vhFrom, 4)
    },
    // 获取拼团信息
    async getGroupInfo() {
      if (this.groupId) {
        //有拼团id时
        let res = await this.$u.api.groupInfo({ group_id: this.groupId })
        this.groupInfo = res.data
      }
    },

    // 获取商品详情优惠区
    async getGoodsDiscount() {
      let data = {}
      data.period = this.goodsId //商品期数
      data.periods_type = this.goodsInfo.periods_type //商品频道
      let res = await this.$u.api.goodsDiscountArea(data)
      console.log('------------------------我是商品优惠区接口返回数据')
      console.log(res)
      this.discountInfo = res.data
    },

    // 获取评论列表
    async getGoodsCommentList() {
      if (this.from != '3') {
        let res = await this.$u.api.goodsCommentList({ period: this.goodsId, page: this.page, limit: this.limit })
        const { total, list = [] } = res.data

        if (list.length) {
          const commentIdList = []
          const commentUidList = []
          list.forEach((item) => {
            commentIdList.push(item.id)
            commentUidList.push(item.uid)
            item.under_comment.forEach((replyItem) => {
              commentIdList.push(replyItem.id)
              commentUidList.push(replyItem.uid)
            })
          })
          const getCommentUserStatusParams = {
            period: this.goodsId,
            comment_id: commentIdList,
            comment_uid: commentUidList,
          }
          console.log('getCommentUserStatusParams', getCommentUserStatusParams)
          const commentUserStatusRes = await this.$u.api.getCommentUserStatus(getCommentUserStatusParams)
          const { like = [], buy = [], deposit = [] } = commentUserStatusRes.data
          list.forEach((item) => {
            const { id, uid } = item
            const { is_like } = like.find((likeItem) => likeItem.comment_id === id) || {}
            const { is_deposit } = deposit.find((depositItem) => depositItem.comment_uid === uid) || {}
            const { is_buy } = buy.find((buyItem) => buyItem.comment_uid === uid) || {}
            item.is_like = is_like
            item.is_buy = is_buy
            item.is_deposit = is_deposit
            item.under_comment?.forEach((replyItem) => {
              const { id, uid } = replyItem
              const { is_deposit } = like.find((depositItem) => depositItem.comment_id === id) || {}
              const { is_like } = like.find((likeItem) => likeItem.comment_id === id) || {}
              const { is_buy } = buy.find((buyItem) => buyItem.comment_uid === uid) || {}
              replyItem.is_like = is_like
              replyItem.is_buy = is_buy
              replyItem.is_deposit = is_deposit
            })
          })
        }

        // 兼容v2表情包格式
        list.forEach((item) => {
          let firstRegRes = /\[(rabbithead|rabbit)\_[\u4e00-\u9fa5]{0,}\]/gi.exec(item.content)
          // 评论列表兼容v2表情包
          if (firstRegRes) {
            let emoReg = this.getEmojiMap.get(firstRegRes[0])
            if (emoReg) {
              item.content = item.content.replace(firstRegRes[0], '')
              item.emoji_image = firstRegRes[0]
            }
          }
          // 回复列表兼容v2表情包
          if (item.under_comment.length) {
            item.under_comment.forEach((innItem) => {
              let secRegRes = /\[(rabbithead|rabbit)\_[\u4e00-\u9fa5]{0,}\]/gi.exec(innItem.content)
              if (secRegRes) {
                let emoReg = this.getEmojiMap.get(secRegRes[0])
                if (emoReg) {
                  innItem.content = innItem.content.replace(secRegRes[0], '')
                  innItem.emoji_image = secRegRes[0]
                }
              }
            })
          }
        })

        // 兼容v2表情包格式
        // list.forEach( item => {
        // 	let reg = /\[(rabbithead|rabbit)\_[\u4e00-\u9fa5]{0,}\]/ig
        // 	let firstRegRes = reg.exec(item.content)
        // 	// 评论列表兼容v2表情包
        // 	if(firstRegRes) {
        // 		let emoReg = this.getEmojiMap.get(firstRegRes[0])
        // 		if(emoReg) {
        // 			item.content = item.content.replace(firstRegRes[0], '')
        // 			item.emoji_image = firstRegRes[0]
        // 		}
        // 	}
        // 	// 回复列表兼容v2表情包
        // 	if(item.under_comment.length) {
        // 		item.under_comment.forEach(innItem => {
        // 			let secRegRes = reg.exec(innItem.content)
        // 			if(secRegRes) {
        // 				let emoReg = this.getEmojiMap.get(secRegRes[0])
        // 				if(emoReg) {
        // 					innItem.content = innItem.content.replace(secRegRes[0], '')
        // 					innItem.emoji_image = secRegRes[0]
        // 				}
        // 			}
        // 		})
        // 	}
        // })
        // this.commentTotal = total
        const commentList = list.map((item) => {
          const {
            id,
            uid,
            avatar_image,
            certified_info,
            nickname,
            user_level,
            user_type,
            is_buy,
            content,
            emoji_image,
            created_time,
            is_like,
            likenums,
            is_deposit,
            under_comment,
          } = item
          const replys = under_comment.map((replyItem) => {
            const {
              id,
              first_id,
              pid,
              uid,
              avatar_image,
              certified_info,
              nickname,
              user_level,
              user_type,
              is_buy,
              p_uid,
              reply_nickname,
              content,
              emoji_image,
              created_time,
              likenums,
              is_like,
              is_deposit,
            } = replyItem
            return {
              id,
              first_id,
              pid,
              uid,
              avatar_image,
              certified_info,
              nickname,
              user_level,
              user_type,
              is_buy,
              is_deposit,
              p_uid,
              p_nickname: reply_nickname,
              content,
              emoji_image: emoji_image && this.ossIcon(`/emoticon/${this.getEmojiMap.get(emoji_image)}.gif`),
              comment_time: created_time, // 暂无数据
              like_nums: likenums,
              like_status: is_like,
            }
          })
          return {
            id,
            uid,
            avatar_image,
            certified_info,
            nickname,
            user_level,
            user_type,
            is_deposit,
            is_buy,
            content,
            emoji_image: emoji_image && this.ossIcon(`/emoticon/${this.getEmojiMap.get(emoji_image)}.gif`),
            comment_time: created_time,
            like_status: is_like,
            like_nums: likenums,
            replys,
            $replysLen: replys.length || 0,
            $isShowAllReply: false,
          }
        })
        console.log('commentList', commentList)
        this.page == 1 ? (this.commentList = commentList) : (this.commentList = [...this.commentList, ...commentList])
        // this.page == 1 ? this.commentList = list : this.commentList = [...this.commentList, ...list]
        this.totalPage = Math.ceil(total / this.limit)
        this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
      }
    },

    // 获取收藏状态
    async getGoodsCollectionStatus() {
      if (this.login.isLogin(this.from, 0)) {
        if (this.from !== '3') {
          try {
            let res = await this.$u.api.goodsCollectionStatus({ period: this.goodsId })
            this.isCollect = res.data
          } catch (e) {
            //TODO handle the exception
          }
        }
      }
    },

    async initActivityData() {
      try {
        const res = await this.$u.api.getActivityByPeriods({ periods: this.goodsId })
        this.activityData = res?.data || {}
      } catch (e) {}
    },

    // 切换tabs状态栏
    changeTabs(index) {
      this.tabsCurrent = index
      uni
        .createSelectorQuery()
        .in(this)
        .select('#current' + this.tabsCurrent)
        .boundingClientRect((data) => {
          uni
            .createSelectorQuery()
            .in(this)
            .select('#outer-content')
            .boundingClientRect((res) => {
              uni.pageScrollTo({
                scrollTop: data.top - res.top - 80,
                duration: 0,
              })
            })
            .exec()
        })
        .exec()
    },

    // 监听页面滚动
    monitorScroll() {
      uni
        .createSelectorQuery()
        .in(this)
        .select('#current0')
        .boundingClientRect((goodsRes) => {
          uni
            .createSelectorQuery()
            .in(this)
            .select('#current1')
            .boundingClientRect((goodsDetailRes) => {
              uni
                .createSelectorQuery()
                .in(this)
                .select('#current2')
                .boundingClientRect((commentRes) => {
                  if (goodsRes.top >= 0 && goodsDetailRes.top >= 0 && commentRes.top >= 0) {
                    this.tabsCurrent = 0
                  } else if (goodsRes.top < 0 && goodsDetailRes.top < 0 && commentRes.top < 800) {
                    this.tabsCurrent = 2
                  } else {
                    this.tabsCurrent = 1
                  }
                })
                .exec()
            })
            .exec()
        })
        .exec()
    },

    // 发生错误返回首页
    goBack() {
      setTimeout(() => {
        if (this.comes.isFromApp(this.from)) {
          //判断是否从App过来 1 = 安卓 2 = ios
          wineYunJsBridge.openAppPage({
            client_path: { ios_path: 'goBack', android_path: 'goBack' },
          })
        } else {
          //h5端
          this.jump.navigateBack()
        }
      }, 1500)
    },

    // 打开客服
    openService() {
      if (this.comes.isFromApp(this.from)) {
        let timestamp = new Date().getTime()
        let { id, periods_type, title, brief, price, banner_img } = this.goodsInfo
        let obj = {
          title: title,
          des: brief,
          ishiddenPrice: this.isHiddenPrice,
          price: price,
          cover: banner_img[0],
          url: `/pages/goods-detail/goods-detail?id=${id}`,
        }
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'ShopDetailkefu', android_path: 'shangpin.kefu' },
          ad_path_param: [
            { ios_key: 'info', ios_val: JSON.stringify(obj), android_key: 'info', android_val: JSON.stringify(obj) },
          ],
        })
      } else {
        this.system.customerService()
      }
    },

    // 获取酒款列表
    getWineStyleList() {
      console.log('---------我是获取酒款列表')
      console.log(this.goodsInfo)
      this.wineStyleTabsList = [] //（防止重复push）
      if (this.goodsInfo.product_info && this.goodsInfo.product_info.length) {
        //判断产品信息列表存在，并有数据
        this.wineStyleInfo = this.goodsInfo.product_info[this.currentWineStyleTabs]
        if (this.goodsInfo.product_info.length >= 2) {
          //大于等于2时开始追加
          this.goodsInfo.product_info.forEach((item, index) => {
            this.wineStyleTabsList.push({ name: `产品信息(${index + 1})` })
          })
        }
      }
    },

    // 切换酒款tabs index = 酒款索引
    changeWineStyleTabs(index) {
      this.currentWineStyleTabs = index
      this.wineStyleInfo = this.goodsInfo.product_info[this.currentWineStyleTabs]
    },

    // 打开购物车
    openShoppingCar() {
      if (this.comes.isFromApp(this.from)) {
        wineYunJsBridge.openAppPage({
          client_path: {
            ios_path: 'ShoppingCartViewController',
            android_path: 'com.stg.rouge.activity.ShopCarActivity',
          },
          ad_path_param: [{ ios_key: 'login', android_key: 'login' }],
        })
      } else {
        if (this.login.isLogin(this.from)) {
          this.jump.navigateTo(`/packageB/pages/shopping-cart/shopping-cart`)
        }
      }
    },

    // 开始倒计时 type = 定时器类型 timesDiff = 两者时差（秒）
    startCountDown(type, timesDiff) {
      this.clearTimer(type)
      if (timesDiff <= 0) return
      if (type == 'openSale') {
        this.openSaleSeconds = Number(timesDiff)
        this.openSaleTimer = setInterval(() => {
          this.openSaleSeconds--
          if (this.openSaleSeconds <= 0) {
            this.feedback.loading({ title: '更新中，请稍后...' })
            setTimeout(() => {
              //延时处理
              this.packageType = 0
              this.getPackageDetail()
            }, 2500)
            this.clearTimer(type)
          }
        }, 1000)
      } else if (type == 'endSale') {
        this.endSaleSeconds = Number(timesDiff)
        this.endSaleTimer = setInterval(() => {
          this.endSaleSeconds--
          if (this.endSaleSeconds <= 0) {
            this.clearTimer(type)
          }
        }, 1000)
      }
    },

    // 清空定时器 type = 定时器类型
    clearTimer(type) {
      if (type == 'openSale') {
        if (this.openSaleTimer) {
          clearInterval(this.openSaleTimer)
          this.openSaleTimer = null
          this.openSaleSeconds = 0
        }
      } else if (type == 'endSale') {
        if (this.endSaleTimer) {
          clearInterval(this.endSaleTimer)
          this.endSaleTimer = null
          this.endSaleSeconds = 0
        }
      }
    },

    // 返回首页 1.当商品详情页面没有暴露成首页的时候，默认回到上一个页面，2.当暴露成首页的时候回到首页
    jumpBack() {
      if (this.pageLength <= 1 && this.from == '') {
        //当商品详情页被暴露成首页（分享）
        console.log('-----------------------------------------------我的页面栈 <= 1')
        this.jump.reLaunch('/pages/index/index')
      } else {
        //当商品详情页没有被暴露成首页
        if (this.comes.isFromApp(this.from)) {
          //判断是否从App过来 1 = 安卓 2 = ios
          wineYunJsBridge.openAppPage({
            client_path: { ios_path: 'goBack', android_path: 'goBack' },
          })
        } //h5端、微信小程序
        else this.jump.navigateBack()
      }
    },

    // 计算预计发货时间
    calEstimateTime() {
      const { predict_shipment_time } = this.packageAllInfo //所有套餐信息
      let curDate = new Date() //当前时间
      let shipDate = new Date(predict_shipment_time.replace(/-/g, '/')) //结束时间
      let seconds = Math.floor((shipDate.getTime() - curDate.getTime()) / 1000) //得到两个日期之间的秒数
      if (seconds >= 0) {
        console.log('---------预计发货时间大于等于当前时间')
        return predict_shipment_time.substr(0, 10)
      } else {
        console.log('---------预计发货时间小于当前时间')
        let ruleShipmentDate = ''
        switch (this.goodsInfo.periods_type) {
          case 0:
          case 3:
            console.log('---------我是闪购或者尾货')
            ruleShipmentDate = this.$u.timeFormat(
              new Date(curDate.getTime() + 24 * 60 * 60 * 1000).getTime(),
              'yyyy-mm-dd'
            )
            break
          case 1:
            console.log('---------我是秒发')
            if (curDate.getHours() + 4 < 16)
              ruleShipmentDate = this.$u.timeFormat(new Date(curDate.getTime()).getTime(), 'yyyy-mm-dd')
            else
              ruleShipmentDate = this.$u.timeFormat(
                new Date(curDate.getTime() + 24 * 60 * 60 * 1000).getTime(),
                'yyyy-mm-dd'
              )
            break
          case 2:
            console.log('---------我是跨境')
            ruleShipmentDate = this.$u.timeFormat(
              new Date(curDate.getTime() + 72 * 60 * 60 * 1000).getTime(),
              'yyyy-mm-dd'
            )
            break
        }
        console.log(ruleShipmentDate)
        return ruleShipmentDate
      }
    },

    // 跳转兔头优惠券
    jumpRabbitCou() {
      if (this.basicFun) return
      if (this.from == '3') return
      this.login.isLoginV3(this.$vhFrom).then((isLogin) => {
        if (!isLogin) return
        this.jump.appAndMiniJump(
          0,
          `${this.$routeTable.pBRabbitHeadShop}?loadType=coupon&jumpGoodsId=${this.goodsId}`,
          this.$vhFrom
        )
        // this.jump.navigateTo(`${this.$routeTable.pBRabbitHeadShop}?loadType=coupon&jumpGoodsId=${this.goodsId}`)
      })
    },

    // 全屏播放视频
    playVideo() {
      let videoUrl = this.goodsInfo.video
      if (this.comes.isFromApp(this.$vhFrom)) {
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'playVideo', android_path: 'com.stg.rouge.activity.VideoPreviewActivity' },
          ad_path_param: [{ ios_key: 'url', ios_val: videoUrl, android_key: 'url', android_val: videoUrl }],
        })
      } else {
        this.jump.navigateTo(`/packageF/pages/full-screen-video/full-screen-video?videoLink=${videoUrl}`)
      }
    },

    // 选择套餐 index = 列表索引、type 套餐类型（默认为 0） 0 = 普通商品（闪购、秒发、跨境、尾货）、1 = 拼团、2 = 新人
    selectPackage(index, type) {
      switch (type) {
        case 0:
          if (this.packageInfo.id === this.packageList[index].id) return
          this.packageInfo = this.packageList[index]
          break
        case 1:
          if (this.packageInfo.id === this.groupPackageList[index].id) return
          this.packageInfo = this.groupPackageList[index]
          break
        case 2:
          if (this.packageInfo.id === this.newPeoplePackList[index].id) return
          this.packageInfo = this.newPeoplePackList[index]
          break
        case 4:
          if (this.packageInfo.id === this.depositPackageList[index].id) return
          this.packageInfo = this.depositPackageList[index]
          break
      }
      this.purchaseNumbers = 1
      this.clickSelectPackage = 1
      this.packageIndex = index
      const { force_original_package } = this.packageInfo
      this.isForceOriginalBox = force_original_package
      this.isOriginalBox = force_original_package ? 1 : 0
      this.selectedProducts = []
      // 重置匹配的子套餐
      this.matchedSubPackage = null
    },

    // 确认是否为原箱发货 type: 0 = 快递箱发货、1 = 原箱发货
    confirmIsOriginalShipment(type) {
      console.log('-------------------confirmIsOriginalShipment')
      this.isOriginalBox = type
      this.showOriginalBoxMask = false
    },

    // 点赞 item = 评论列表某一项
    async thumbsUp(item) {
      if (this.basicFun) return
      if (this.login.isLogin(this.$vhFrom)) {
        this.feedback.loading({ title: '' })
        try {
          const { id, uid, like_status, content } = item
          const action = like_status ? 1 : 0
          await this.$u.api.goodsCommentThumbsUp({
            period: this.goodsId,
            comment_id: id,
            comment_uid: uid,
            content,
            action,
          })
          if (like_status) {
            item.like_status = 0
            item.like_nums--
          } else {
            item.like_status = 1
            item.like_nums++
          }
          this.system.vibrateShort()
        } finally {
          this.feedback.hideLoading()
        }
      }
    },

    // 添加评论 type = 评论类型 0 = 评论、1 = 回复, item = 评论列表对象
    commentAdd(type, item = {}) {
      if (this.basicFun) return
      if (this.login.isLogin(this.from)) {
        this.changeTabs(2)
        this.commPlaceholder = '说说你的观点~'
        this.isReply = type
        if (this.isReply) {
          this.replyObj = item
          this.commPlaceholder = `@${item.nickname}:`
        }
        this.checkAgreement() //检查是否勾选评论协议
      }
    },

    // 再来一单板块
    // 我想要
    async iWant() {
      const { id, periods_type, title, brief, price, banner_img } = this.goodsInfo
      if (this.login.isLogin(this.from)) {
        try {
          const {
            data: { rid },
          } = await this.$u.api.iWant({ period: id })
          if (rid) {
            // 心愿清单
            this.customerInfo = { id, periods_type, title, brief, price, banner_img }
            this.showOneMoreOrderMask = true
            this.wishId = rid
          }
        } catch (e) {
          //TODO handle the exception
        }
      }
    },

    // 上架提醒
    async shelfReminder() {
      try {
        this.feedback.loading()
        const res = await this.$u.api.addWishList({ id: this.wishId, is_msg: 1 })
        this.showOneMoreOrderMask = false
        this.showOneMoreOrderWishModal = true
        this.wishId = ''
        this.feedback.hideLoading()
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 检测用户是否勾选协议
    checkAgreement() {
      uni.getStorage({
        key: 'protocolList',
        success: (res) => {
          this.protocolList = res.data
        },
        fail: async () => {
          try {
            let res = await this.$u.api.userSpecifiedData({ field: 'protocol' })
            this.protocolList = res.data.protocol
            uni.setStorageSync('protocolList', this.protocolList)
          } catch (e) {
            //TODO handle the exception
          }
        },
        complete: () => {
          this.showCommPop = true
        },
      })
    },

    // 发送表情包 reg = 表情包图片正则
    onImgSend(reg) {
      this.commentImgReg = reg
    },

    // 发送评论/回复评论 cont = 评论内容
    async onCommentAdd(cont) {
      console.log('------------------------我是父组件接受的评论内容')
      console.log(cont)
      this.feedback.loading({ title: '评论中...' })
      let data = {}
      data.period = this.goodsId //期数
      data.title = this.goodsInfo.title //商品标题
      data.periods_type = this.goodsInfo.periods_type //商品频道
      data.content = cont //评论内容
      data.emoji_image = this.commentImgReg //表情包图片正则

      // 回复
      if (this.isReply) {
        console.log('-----------我是回复')
        const { id, first_id, uid, content } = this.replyObj
        console.log(id, first_id, uid)
        data.first_id = first_id ? first_id : id //一级评论ID
        data.pid = id //上级评论ID
        data.p_uid = uid //上级评论用户ID
        data.reply_content = content //被回复内容，回复时传入
      }
      console.log('-------------------这是需要上传的数据')
      console.log(data)
      try {
        let res = await this.$u.api.goodsCommentAdd(data)
        this.feedback.toast({ title: '评论成功~' })
        this.showCommPop = false
        this.commentImgReg = ''
        if (!this.protocolList.includes('1')) this.agreeCommAgreement()
      } catch (e) {
        this.commentImgReg = ''
      }
    },

    // 同意/取消同意协议
    async agreeCommAgreement() {
      try {
        await this.$u.api.agreeProtocol({ xid: 1 })
        uni.setStorageSync('protocolList', ['1'])
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 商品收藏
    async collection() {
      if (this.login.isLogin(this.from)) {
        try {
          let type = 0
          this.isCollect == 0 ? (type = 1) : (type = 0)
          await this.$u.api.goodsCollection({
            period_id: this.goodsId,
            periods_type: this.goodsInfo.periods_type,
            type,
          })
          this.isCollect = type
          this.system.vibrateShort()
          this.feedback.toast({ title: type == 0 ? '取消收藏成功' : '收藏成功' })
        } catch (e) {}
      }
    },

    // 加入购物车
    addShoppingCart() {
      if (this.login.isLogin(this.from)) {
        this.isAddShoppingCart = true
        this.showGoodsPackPop = true
        if (Object.keys(this.storeInfo).length && this.from == '') this.showOnLineGoodsMask = true
      }
    },

    // 确认加入购物车
    async confirmAddShoppingCart() {
      try {
        // 检查自选套餐是否已选择足够数量
        if (!this.isCustomProductSelected) {
          this.feedback.toast({ title: `请选择${this.packageInfo.custom_product_count}个产品` })
          return
        }

        let data = {}
        data.period = this.goodsId
        data.package_id = this.packageInfo.id
        data.periods_type = this.goodsInfo.periods_type
        data.nums = this.purchaseNumbers
        data.is_original_package = this.isOriginalBox //是否原箱发货

        // 如果是自选套餐，添加选择的产品信息到购物车数据
        if (this.packageInfo.is_custom_package === 1 && this.selectedProducts.length > 0) {
          data.selected_products = this.selectedProducts // 用户选择的产品ID列表
          data.matched_sub_package_id = this.matchedSubPackage?.id // 匹配到的子套餐ID

          // 打印加入购物车的套餐信息
          console.log('=== 自选套餐加入购物车信息 ===')
          console.log('主套餐ID:', this.packageInfo.id)
          console.log('主套餐名称:', this.packageInfo.package_name)
          console.log('用户选择的产品ID:', this.selectedProducts)
          if (this.matchedSubPackage) {
            data.package_id = this.matchedSubPackage.id
            console.log('匹配到的子套餐ID:', this.matchedSubPackage.id)
            console.log('匹配到的子套餐名称:', this.matchedSubPackage.package_name)
            console.log('子套餐价格:', this.matchedSubPackage.price)
          } else {
            console.log('未匹配到子套餐，使用默认套餐价格:', this.packageInfo.price)
          }
          console.log('============================')
        } else {
          // 普通套餐加入购物车信息
          console.log('=== 普通套餐加入购物车信息 ===')
          console.log('套餐ID:', this.packageInfo.id)
          console.log('套餐名称:', this.packageInfo.package_name)
          console.log('套餐价格:', this.currentPackagePrice)
          console.log('============================')
        }

        console.log('加入购物车数据:', data)
        // return this.feedback.toast({ title: '团团正在调试数据'})

        let res = await this.$u.api.addShoppingCart(data)
        if (this.comes.isFromApp(this.from)) {
          wineYunJsBridge.openAppPage({
            client_path: { ios_path: 'addShopNumber', android_path: 'addShopNumber' },
            ad_path_param: [
              {
                ios_key: 'num',
                ios_val: this.purchaseNumbers + '',
                android_key: 'num',
                android_val: this.purchaseNumbers + '',
              },
            ],
          })
        }
        this.feedback.toast({ title: '加入成功', icon: 'success' })
        this.getShoppingCartNum()
        this.modifyAddShoppingCartStatus(true)
      } catch (e) {
        //TODO handle the exception
      }
    },

    // 不再提示跨境购买须知
    notPromptingCrossBorderNotice() {
      if (this.crossBorderNoticeNotPrompting === 1) {
        uni.removeStorageSync('crossBorderNoticeNotPrompting')
        this.crossBorderNoticeNotPrompting = 0
      } else {
        uni.setStorage({
          key: 'crossBorderNoticeNotPrompting',
          data: 1,
          success: () => {
            this.crossBorderNoticeNotPrompting = 1
          },
        })
      }
    },

    // 立即购买 type购买类型 0 = 普通商品（闪购、秒发、跨境、尾货）、1 = 拼团、2 = 新人
    async buyNow(type) {
      if (this.login.isLogin(this.from)) {
        const { id, periods_type } = this.goodsInfo
        if ([0, 1, 2, 3].includes(periods_type)) {
          if (this.goodsInfo.is_seckill) {
            this.inventoryRemainInfo = []
          } else {
            const res = await this.$u.api.getInventoryRemainInfo({ period: id, periods_type, t: new Date().getTime() })
            this.inventoryRemainInfo = res?.data || []
          }
        }
        this.packageIndex = 0
        this.packageType = type
        this.isAddShoppingCart = false
        this.selectPackage(0, type)
        this.clickSelectPackage = 0
        this.showGoodsPackPop = true
        // if( Object.keys(this.storeInfo).length && this.from == '' ) this.showOnLineGoodsMask = true
        if (this.goodsInfo.periods_type == 2 && this.crossBorderNoticeNotPrompting === 0)
          this.showCrossBorderNoticePop = true //跨境商品提示
      }
    },

    // 确认立即购买
    async confirmBuyNow() {
      if (this.goodsInfo.is_seckill && new Date().getTime() >= new Date(this.goodsInfo.sell_time).getTime()) {
        const script = document.createElement('script')
        script.src = this.fetch_url
        // script.src = 'http://vinehoo-test.oss-cn-zhangjiakou.aliyuncs.com/test/fetch.js'
        script.onload = async () => {
          if (typeof window.fetchDataFromRemote === 'function') {
            try {
              const { uid } = uni.getStorageSync('loginInfo')
              const result = await window.fetchDataFromRemote(uid)
              console.log(this.$store.state.from, wineYunJsBridge)
              if (result.data.error_code === 401) {
                // this.feedback.toast({ title: '登录已失效，请重新登录' })
                setTimeout(() => {
                  if (
                    this.$store.state.from == '1' ||
                    this.$store.state.from == '2' ||
                    this.$store.state.from == 'next'
                  ) {
                    wineYunJsBridge.openAppPage({
                      client_path: { ios_path: 'login', android_path: 'login' },
                    })
                  } else {
                    if (this.$store.state.from == '3') {
                      this.jump.reLaunch(this.pages.getPrvePageFullPath()) //如果是pc端预览商品详情页出现登录失效的情况，重新刷新一下页面
                    } else {
                      this.$u.throttle(() => {
                        this.jump.navigateTo('/pages/login/login')
                      }, 500)
                    }
                  }
                }, 1500)
              } else if (result.data.error_code === 0) {
                if (result.data.data.status) {
                  this.payOrder()
                } else {
                  uni.showModal({ title: '提示', content: '系统错误(500)', showCancel: false, confirmText: '确定' })
                }
              } else {
                uni.showModal({ title: '提示', content: '系统错误(404)', showCancel: false, confirmText: '确定' })
              }
            } catch (e) {
              console.log(e)
            }
          }
        }
        document.body.appendChild(script)
      } else {
        this.payOrder()
      }
    },
    payOrder() {
      console.log('payOrder---')

      let orderGoodsInfo = {
        is_cart: 0,
        submit_type: this.goodsInfo.periods_type,
        coupons_id: 0,
        // is_group: this.isGroup && this.packageType ? 1 : 0, //是否拼团
        special_type: this.packageType, //特殊类型：0-普通 1-拼团 2-新人 4-订金
        group_id:
          this.isGroup &&
          this.groupInfo.group_status !== 3 &&
          this.packageType &&
          this.groupId &&
          this.groupInfo.remaining_time > 0 &&
          this.groupInfo.group_last_num > 0
            ? this.groupId
            : 0, //拼团id
        orderGoodsList: [],
      } //订单商品信息
      let item = {} //订单商品信息
      item.periods_type = this.goodsInfo.periods_type // 订单提交类型：0-闪购 1-秒发 2-跨境 3-尾货...
      item.is_cart = 0 //是否来自购物车
      item.period = this.goodsId //商品期数
      item.banner_img = this.goodsInfo.banner_img[0] //商品banner
      item.title = this.goodsInfo.title //商品标题
      item.package_name = this.packageInfo.package_name //套餐名
      item.price = this.currentPackagePrice //套餐价格（如果匹配到子套餐则使用子套餐价格）
      item.package_id = this.packageInfo.id //套餐id
      item.nums = this.purchaseNumbers //购买数量
      item.express_type = 0 //express_type快递方式 1-中通 2-顺丰快递 3-顺丰冷链 5-京东快递
      item.predict_time = this.realPredictShipmentTime //预计发货时间（前端算好的预计发货时间，但是还是建议后端计算）
      item.goods_is_ts = this.goodsInfo.is_support_ts //是否支持暂存
      item.is_checked_ts = false //是否勾选暂存 false否、true是
      item.is_ts = 0 //是否勾选暂存：0否 1是
      item.is_cold_chain = this.packageInfo.is_cold_chain //是否冷链
      item.$is_checked_cold_chain = false //是否勾选冷链 false否、true是
      item.is_original_package = this.isOriginalBox === 1 ? this.isOriginalBox : 2 //是否原箱：0 = 不是原箱、1 = 是原箱发货（注意：这里的是否原箱是对于商品而言，订单则是否原箱：1-是 2-否）

      // 如果是自选套餐，添加选择的产品信息
      if (this.packageInfo.is_custom_package === 1 && this.selectedProducts.length > 0) {
        item.selected_products = this.selectedProducts // 用户选择的产品ID列表
        item.matched_sub_package = this.matchedSubPackage // 匹配到的子套餐信息

        // 打印套餐选择信息
        console.log('=== 自选套餐购买信息 ===')
        console.log('主套餐ID:', this.packageInfo.id)
        console.log('主套餐名称:', this.packageInfo.package_name)
        console.log('用户选择的产品ID:', this.selectedProducts)
        if (this.matchedSubPackage) {
          item.package_id = this.matchedSubPackage.id
          console.log('匹配到的子套餐ID:', this.matchedSubPackage.id)
          console.log('匹配到的子套餐名称:', this.matchedSubPackage.package_name)
          console.log('子套餐价格:', this.matchedSubPackage.price)
        } else {
          console.log('未匹配到子套餐，使用默认套餐价格:', this.packageInfo.price)
        }
        console.log('========================')
      } else {
        // 普通套餐购买信息
        console.log('=== 普通套餐购买信息 ===')
        console.log('套餐ID:', this.packageInfo.id)
        console.log('套餐名称:', this.packageInfo.package_name)
        console.log('套餐价格:', this.currentPackagePrice)
        console.log(
          '套餐类型:',
          this.packageType === 0
            ? '普通'
            : this.packageType === 1
            ? '拼团'
            : this.packageType === 2
            ? '新人'
            : this.packageType === 4
            ? '订金'
            : '未知'
        )
        console.log('========================')
      }

      orderGoodsInfo.orderGoodsList.push(item)
      if (this.oneSource) {
        orderGoodsInfo.oneSource = this.oneSource
      }
      orderGoodsInfo.appSource = this.appSource
      this.muOrderInfo(orderGoodsInfo)

      console.log(orderGoodsInfo)
      // return this.feedback.toast({ title: '团团正在调试数据'})

      // 分条件跳转
      if (this.comes.isFromApp(this.from)) {
        console.log('app----------------', this.from)
        //判断是否从App过来 1 = 安卓 2 = ios
        if (this.from == 'next') {
          uni.setStorageSync('nextorderGoodsInfo', orderGoodsInfo)
          this.jump.appAndMiniJump(0, '/packageB/pages/order-confirm/order-confirm', this.$vhFrom)
        } else {
          wineYunJsBridge.openAppPage({
            client_path: { ios_path: 'confirmOrder', android_path: 'confirmOrder' },
            ad_path_param: [
              {
                ios_key: 'info',
                ios_val: JSON.stringify(orderGoodsInfo),
                android_key: 'info',
                android_val: JSON.stringify(orderGoodsInfo),
              },
            ],
          })
        }
      } else {
        console.log('h5----------------')

        //h5端
        this.jump.navigateTo('/packageB/pages/order-confirm/order-confirm')
      }
    },

    // 确认
    confirm() {
      if (this.packageList.length == 0) return this.feedback.toast({ title: '请选择套餐~' })

      // 检查自选套餐是否已选择足够数量
      if (!this.isCustomProductSelected) {
        this.feedback.toast({ title: `请选择${this.packageInfo.custom_product_count}个产品` })
        return
      }

      this.showGoodsPackPop = false
      if (this.isAddShoppingCart) {
        this.confirmAddShoppingCart()
      } else {
        this.confirmBuyNow()
      }
    },

    handleOpenApp() {
      this.openDownLoadAppTimer = setTimeout(() => {
        location.href = `${this.agreementPrefix}/downLoadApp`
      }, 2000)
      const path = 'GoodsDetail'
      const key = 'id'
      let val = this.goodsId
      const platform = navigator.userAgent.toLowerCase()
      const isAndroid = platform.indexOf('android') > -1
      const isIos = platform.indexOf('iphone') > -1 || platform.indexOf('ipad') > -1 || platform.indexOf('ipod') > -1
      if (isAndroid) {
        if (this.auth) val = `${val}&auth=${this.auth}`
        if (this.activityPeriods) val = `${val}&activity_periods=${this.activityPeriods}`
      }
      const toData = {
        client_path: { ios_path: path, android_path: path },
        ad_path_param: [{ android_key: key, android_val: val, ios_key: key, ios_val: val }],
      }
      if (isIos) {
        if (this.auth) {
          const key = 'auth'
          const val = this.auth
          toData.ad_path_param.push({ android_key: key, android_val: val, ios_key: key, ios_val: val })
        }
        if (this.activityPeriods) {
          const key = 'activity_periods'
          const val = this.activityPeriods
          toData.ad_path_param.push({ android_key: key, android_val: val, ios_key: key, ios_val: val })
        }
      }
      location.href = `winetalk://toAny?toData=${encodeURIComponent(JSON.stringify(toData))}`
    },

    handlePreviewIndex(index) {
      this.swiperPreviewIndex = index
    },

    handlePreviewSwiper() {
      console.log('swiperList', this.goodsInfo.swiperList)
      uni.previewImage({
        current: this.swiperPreviewIndex,
        indicator: true,
        urls: this.goodsInfo.swiperList,
      })
    },

    jumpUserHome(uid, from) {
      if (this.basicFun) return
      this.jump.jumpUserHome(uid, from)
    },

    onReply(item) {
      this.commentAdd(1, item)
    },
    onToggleReply(item) {
      const { id } = item
      const currComment = this.commentList.find((item) => item.id === id)
      if (!currComment) return
      currComment.$isShowAllReply = !currComment.$isShowAllReply
    },
    openGoodsCommentsObserver() {
      goodsCommentsObserver = uni.createIntersectionObserver(this)
      goodsCommentsObserver.relativeToViewport().observe('#current2', (res) => {
        if (res.intersectionRatio > 0) {
          if (!this.isLoadedGoodsComments) {
            this.getGoodsCommentList()
          }
          this.isLoadedGoodsComments = true
        }
      })
    },
    closeGoodsCommentsObserver() {
      if (goodsCommentsObserver) {
        goodsCommentsObserver.disconnect()
        goodsCommentsObserver = null
      }
    },
    openGoodsRecommendsObserver() {
      goodsRecommendsObserver = uni.createIntersectionObserver(this)
      goodsRecommendsObserver.relativeToViewport().observe('#goods-recommends', (res) => {
        if (res.intersectionRatio > 0) {
          if (!this.isLoadedGoodsRecommends) {
            this.$refs?.vhGoodsRecommendListRef?.getRecommendList()
          }
          this.isLoadedGoodsRecommends = true
        }
      })
    },
    closeGoodsRecommendsObserver() {
      if (goodsRecommendsObserver) {
        goodsRecommendsObserver.disconnect()
        goodsRecommendsObserver = null
      }
    },
    recordPreSalesSource() {
      if (this.isRecordedPreSalesSource || !this.recordPreSalesSourceParams) return
      this.$u.api.recordPreSalesSource(this.recordPreSalesSourceParams).then(() => {
        this.isRecordedPreSalesSource = true
      })
    },
    onAppShare() {
      const { id, title, brief, banner_img } = this.goodsInfo
      let path = `${this.$routeTable.pgGoodsDetail}?id=${id}&groupId=${this.groupId}`
      if (this.login.isLogin('', 0)) {
        const { uid } = uni.getStorageSync('loginInfo')
        path = `${path}&one_source_platform=vinehoo&one_source_event=detail_share&one_source_user=${uid}`
      }
      this.jump.appShare({
        title,
        des: brief,
        img: banner_img[0],
        path,
      })
    },
  },

  onUnload() {
    console.log('--------onUnload')
    if (this.openSaleTimer) {
      clearInterval(this.openSaleTimer)
      this.openSaleTimer = null
      this.openSaleSeconds = 0
    }
    if (this.endSaleTimer) {
      clearInterval(this.endSaleTimer)
      this.endSaleTimer = null
      this.endSaleSeconds = 0
    }
    this.closeGoodsCommentsObserver()
    this.closeGoodsRecommendsObserver()
  },

  beforeDestroy() {
    console.log('--------beforeDestroy')
    // // 清空开售定时器
    // clearInterval(this.openSaleTimer)
    // this.openSaleTimer = null
    // this.openSaleSeconds = 0
    // // 清空结束售卖定时器
    // clearInterval(this.endSaleTimer)
    // this.endSaleTimer = null
    // this.endSaleSeconds = 0
  },

  onPullDownRefresh() {
    this.init()
  },

  onShareAppMessage(res) {
    console.log(res)
    const { id, title, banner_img } = this.goodsInfo //拿到商品信息
    let url = `/pages/goods-detail/goods-detail?id=${id}&groupId=${this.groupId}`
    console.log(url)
    return {
      //按钮分享
      title,
      path: url,
      imageUrl: banner_img[0],
    }
  },

  onPageScroll(res) {
    this.scrollTop = res.scrollTop
    if (this.from != '3') this.monitorScroll()
  },

  onReachBottom() {
    if (this.page == this.totalPage || this.commentList.length == 0) return
    this.loadStatus = 'loading'
    this.page++
    this.getGoodsCommentList()
  },
}
</script>

<style scoped>
.appointment-left-bt {
  border-radius: 20px;
  border: 1px solid #999999;
  color: #999999;
  padding: 7px 37px;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
}
.appointment-right-bt {
  border-radius: 20px;
  color: #fff;
  padding: 7px 37px;
  background-color: #e80404;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
}
.appointment {
  top: -41px;
  left: 42px;
}
.appointment-desc {
  font-weight: 400;
  margin-top: 16px;
  font-size: 14px;
  color: #666666;
  line-height: 20px;
}
.appointment-text {
  font-weight: 500;
  font-size: 21px;
  color: #ca101a;
  line-height: 20px;
}
::v-deep .u-mode-center-box {
  overflow: initial !important;
}
::v-deep .uni-scroll-view {
  overflow: initial !important;
}
/* 新人背景 */
.new-peo-bg {
  background-image: url(https://images.vinehoo.com/vinehoomini/v3/goods_detail/new_peo_bg.png);
  background-size: cover;
}
/* .rabbit-cou-bg {
              background-image: url(https://images.vinehoo.com/vinehoomini/v3/goods_detail/rab_cou.png);
              background-size: cover;
          } */
.sha-fri-btn-bla {
  background: url(https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_sha_bla.png) center center no-repeat;
  background-size: contain;
}
.sha-fri-btn-whi {
  background: url(https://images.vinehoo.com/vinehoomini/v3/goods_detail/nav_sha_whi.png) center center no-repeat;
  background-size: contain;
}
</style>

<style lang="scss" scoped>
.tab-list {
  ::v-deep .u-scroll-box {
    align-items: baseline;

    .u-tab-item:not(:first-child) {
      margin-left: 8rpx;
    }
  }
}

.under-comment {
  view:not(:last-child) {
    margin-bottom: 16rpx;
  }
}

.unumberbox {
  ::v-deep {
    .u-numberbox {
      border: 1px solid #eeeeee;
      border-radius: 12rpx;
    }

    .u-number-input {
      margin: 0;
      padding: 0 3px;
    }

    .u-icon-minus,
    .u-icon-plus {
      width: 50rpx !important;
      background-color: #fff !important;

      &.u-icon-disabled {
        .uicon-minus,
        .uicon-plus {
          color: #ddd !important;
        }
      }
    }

    .uicon-minus,
    .uicon-plus {
      font-size: 24rpx !important;
      color: #666 !important;
    }
  }
}
</style>
