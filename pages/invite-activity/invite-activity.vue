<template>
  <view v-if="loading && activityDetail" class="activity">
    <view v-if="loading && activityDetail" class="activity__content" :style="contentStyle">
      <image v-if="activityDetail.picture" :src="activityDetail.picture" mode="widthFix" class="activity__img" />
      <button class="activity__btn" :style="btnStyle" @click="handleJump">{{ activityDetail.button_desc || '我要报名' }}</button>
      <view class="activity__goods-list">
        <activity-goods-list :list="list" />
      </view>
      <view class="activity__rbtn-wrapper">
        <button v-if="activityDetail.share_button_show" class="activity__rbtn is-share" :style="rbtnStyle" @click="feedback.toast({ title: isWxProcess ? '请点击右上角分享' : '请在微信浏览器中打开' })">分享</button>
        <button class="activity__rbtn" :style="rbtnStyle" @click="showRule = true">规则</button>
      </view>
      <view class="activity__home" @click.stop="jump.reLaunch(routeTable.pgIndex)">
        <image src="https://images.vinehoo.com/vinehoomini/v3/activity/activity_home.png" class="activity__home-img" />
      </view>

      <u-modal v-model="showRule" title="规则">
        <view class="activity__rule">
          <text>{{ activityDetail.rule }}</text>
        </view>
      </u-modal>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import wx from 'weixin-js-sdk'
import { WX_APPID_PROD, WX_APPID_DEV } from '@/common/js/fun/constant'
const PAGE_SIZE = 10

export default {
  name: 'activity',
  data: () => ({
    loading: false,
    query: {
      invite_activity_id: 0,
      page: 1,
      limit: PAGE_SIZE,
      onsale_status_ids: 2
    },
    activityDetail: {},
    toatalPage: 0,
    list: [],
    showRule: false,
    isWxProcess: false,
  }),
  computed: {
    ...mapState(['routeTable']),
    contentStyle ({ activityDetail: { theme_color } }) {
      const styleObj = {}
      if (theme_color) styleObj.background = theme_color
      return styleObj
    },
    btnStyle ({ activityDetail: { button_color, button_font_color, button_distance_above } }) {
      const styleObj = {}
      if (button_color) styleObj.background = button_color
      if (button_font_color) styleObj.color = button_font_color
      if (+button_distance_above) {
        Object.assign(styleObj, {
          position: 'absolute',
          top: `${+button_distance_above}px`,
          transform: 'translateX(-50%)',
          margin: '0 0 0 50%',
        })
      }
      return styleObj
    },
    rbtnStyle ({ activityDetail: { rule_button_color, rule_button_font_color } }) {
      const styleObj = {}
      if (rule_button_color) styleObj.background = rule_button_color
      if (rule_button_font_color) styleObj.color = rule_button_font_color
      return styleObj
    },
    WX_APPID ({ $isDev }) {
      return $isDev ? WX_APPID_DEV : WX_APPID_PROD
    },
  },
  methods: {
    queryActivityDetail () {
      const params = {
        id: this.query.invite_activity_id
      }
      return this.$u.api.inviteActivityDetail(params).then(res => {
        this.activityDetail = res?.data || {}
        return res
      })
    },
    queryActivityGoodsList () {
      const { invite_activity_id, page, limit, onsale_status_ids } = this.query
      const params = {
        invite_activity_id,
        page,
        limit,
        onsale_status_ids
      }
      return this.$u.api.inviteActivityGoodsList(params).then(res => {
        const { list = [], total = 0 } = res?.data || {}
        list.forEach(item => {
          item.quota_rule = JSON.parse(item.quota_rule || '{}')
        })
        this.list = this.list.concat(list)
        this.totalPage = Math.ceil(total / PAGE_SIZE)
        return res
      })
    },
    async load () {
      await Promise.all([
        this.queryActivityDetail(),
        this.queryActivityGoodsList(),
      ])
      this.loading = true
    },
    handleJump () {
      if (this.login.isLogin()) {
        const loginInfo = uni.getStorageSync('loginInfo') || {}
        const { invite_activity_id } = this.query
        const params = {
          id: invite_activity_id,
          uid: loginInfo.uid
        }
        this.$u.api.getInviteActivityTicket(params).then(() => {
          this.feedback.toast({ title: '领取成功', duration: 2500 })
        }).finally(() => {
          setTimeout(() => {
            location.href = this.activityDetail.jump_link
          }, 2.5 * 1000)
        })
      }
    },
    wxConfig () {
      const { origin, pathname, search } = location
      const url = `${origin}${pathname}${search}`
      const data = { url, appid: this.WX_APPID }
      this.$u.api.getJsapiSign(data).then(res => {
        const { appid = '', noncestr = '', sign = '', timestamp = '' } = res || {}
        const configData = {
          debug: this.$isDev,
          appId: appid,
          nonceStr: noncestr,
          signature: sign,
          timestamp,
          jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
        }
        wx.config(configData)
        wx.ready(() => {
          const { share_image, main_title, sub_title, share_url } = this.activityDetail
          const data = {
            title: main_title,
            desc: sub_title,
            link: share_url,
            imgUrl: share_image,
          }
          wx.updateAppMessageShareData(data)
          wx.updateTimelineShareData(data)
        })
      })
    },
  },
  onLoad (options) {
    const { id = 0 } = options
    this.query.invite_activity_id = id
    if (!id) return
    this.load().then(() => {
      const ua = window.navigator.userAgent.toLowerCase()
      this.isWxProcess = /micromessenger/.test(ua)
      if (this.isWxProcess) {
        this.wxConfig()
      }
    })
  },
  onReachBottom() {
    if (this.query.page === this.totalPage || !this.totalPage) return
    this.query.page++
    this.queryActivityGoodsList()
  }
}
</script>

<style lang="scss" scoped>
  .activity {
    @include flex-col(flex-start, stretch);
    height: 100vh;

    &__content {
      flex: 1;
      background: #E6A7B9;
    }

    &__img {
      width: 100%;
    }

    &__btn {
      @include flex-row;
      margin: 20rpx auto;
      padding: 0;
      @include size(440rpx, 72rpx);
      @include font(500, 30rpx, #fff);
      background: #000;
      box-shadow: 0 0 8rpx 0 rgba(0, 0, 0, 0.2);
      border-radius: 36rpx;
      opacity: 0.49;
    }

    &__goods-list {
      padding: 24rpx;
    }

    &__rbtn {
      @include flex-row;
      padding: 0 0 0 24rpx;
      @include size(92rpx, 46rpx);
      @include font(500, 24rpx, #fff);
      background: #000;
      box-shadow: 0 0 8rpx 0 rgba(0, 0, 0, 0.2);
      border-radius: 30rpx 0 0 30rpx;
      opacity: 0.35;

      &.is-share {
        margin-bottom: 10rpx;
      }

      &-wrapper {
        position: fixed;
        top: 38rpx;
        right: 0;
      }
    }

    &__home {
      position: fixed;
      bottom: 124rpx;
      right: 14rpx;

      &-img {
        @include size(94rpx);
      }
    }

    &__rule {
      padding: 48rpx;
    }
  }
</style>
