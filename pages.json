{
	// 配置easycom模式，无需引入组件即可使用
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	
	// 主包（存放tabsBar的内容以及一些公共页面（登录页、商品详情页））
	"pages": [
		// Tabbar页面 + 登录 + 商品详情
		{
			"path": "pages/index/index", //首页（启动页）
			"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
		},
		{
			"path": "pages/flash-purchase/flash-purchase", // 闪购
			"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
		},
		{
			"path": "pages/community/community", // 社区
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/miaofa/miaofa", // 秒发 （原本应该以英文名叫做second-hair，因v2门店绑定的桌子二维码对应的页面路径叫做pages/miaofa/miaofa，所以这个页面特殊处理名称定为miaofa）
			"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
		},
        {
			"path": "pages/miaofa/cardDetail", 
			"style": { "navigationStyle": "custom", "enablePullDownRefresh": false }
		}, {
			"path": "pages/miaofa/cardDetailtemporarily", 
			"style": { "navigationStyle": "custom", "enablePullDownRefresh": false }
		},
		{
			"path": "pages/mine/mine", // 我的（个人中心）
			"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
		},
		
		// 登录
		{
			"path": "pages/login/login", //登录
			"style": { "navigationStyle": "custom" }
		},
		
		// 商品详情
		{
			"path": "pages/goods-detail/goods-detail", //商品详情
			"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
		},
		{
			"path": "pages/channel-goods-detail/channel-goods-detail", //商品详情
			"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
		},
		
		// 门店商品详情
		{
			"path": "pages/store-goods-detail/store-goods-detail", //门店商品详情（门店商品详情页面本应该在子包B里面，因v2门店绑定的酒瓶二维码对应的页面为pages/store-goods-detail/store-goods-detail，所以这个页面也存放在主包）
			"style": { "navigationStyle": "custom" }
		},
		{
			"path": "pages/activity/activity",
			"style": { "navigationStyle": "custom" }
		},
		{
			"path": "pages/invite-activity/invite-activity",
			"style": { "navigationStyle": "custom" }
		},
		{
			"path": "pages/pay-success-jump/pay-success-jump",
			"style": { "navigationStyle": "custom" }
		}
	],
	
	// 子包（存放除tabBar以外的包）
	"subPackages": [
		// 首页
		{
			"root": "packageA",
			 "pages": [
				 {
				 	"path": "pages/news-detail/news-detail", //快报详情
				 	"style": { "navigationStyle": "custom", "navigationBarTitleText": "快报详情"}
				 },
				{
					"path": "pages/more-than-wine/more-than-wine", //不只有酒（ 卡片跳转的列表详情 ）
					"style": { "navigationStyle": "custom", "navigationBarTitleText": "" }
				},
				{
					"path": "pages/basic-fun-introduce/basic-fun-introduce",
					"style": { "navigationStyle": "custom", "navigationBarTitleText": "" }
				}
			  ]
		},
		
		// 商城（闪购、秒发、兔头商城）
		{
			"root": "packageB",
			"pages": [
				// 购物车
				{
					"path": "pages/shopping-cart/shopping-cart", //购物车
					"style": { "navigationStyle": "custom" }
				},
				
				// 订单确认页
				{
					"path": "pages/order-confirm/order-confirm", //订单确认页（普通商品）
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/order-deposit/order-deposit", //订金订单
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/order-deposit-detail/order-deposit-detail", //订金订单详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/order-detail/order-detail", //订单详情
					"style": { "navigationStyle": "custom" }
				},
				
				// 支付相关
				{
					"path": "pages/payment/payment", //支付页面（收银台）
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/payment-status-query/payment-status-query", //支付查询订单状态
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/pay-success/pay-success", //支付成功抽奖页
					"style": { "navigationStyle": "custom" }
				},
				// 物流
				{
					"path": "pages/logistics-detail/logistics-detail", //物流详情
					"style": { "navigationStyle": "custom" }
				},
				
				// 开票
				{
					"path": "pages/order-invoice/order-invoice", //订单可开票列表
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/order-invoice-history-list/order-invoice-history-list", //订单开票历史列表
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/order-invoice-history-detail/order-invoice-history-detail", //订单开票历史详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/order-invoice-browse/order-invoice-browse", //发票预览
					"style": { "navigationStyle": "custom" }
				},
				
				// 申请售后
				{
					"path": "pages/after-sale-goods-service/after-sale-goods-service",//选择申请售后服务
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/after-sale-apply/after-sale-apply", //申请售后
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/after-sale-detail/after-sale-detail", //售后详情
					"style": { "navigationStyle": "custom" }
				},
				
				// 秒发
				{
					"path": "pages/second-hair-second/second-hair-second", //秒发二级页面
					"style": { "navigationStyle": "custom" }
				},
				
				// 兔头商城
				{
					"path": "pages/rabbit-head-shop/rabbit-head-shop", //兔头商城首页
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/rabbit-head-record/rabbit-head-record", //兔头兑换记录
					"style": { "navigationStyle": "custom", "navigationBarTitleText": "兔头记录" }
				},
				{
					"path": "pages/rabbit-head-coupon/rabbit-head-coupon", //兔头兑换优惠券
					"style": { "navigationStyle": "custom", "navigationBarTitleText": "兔头商店" }
				},
				{
					"path": "pages/rabbit-head-goods-detail/rabbit-head-goods-detail", //兔头商品详情
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true, "navigationBarTitleText": "商品详情" }
				},
				{
					"path": "pages/rabbit-head-order-confirm/rabbit-head-order-confirm", //兔头确认兑换页
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/rabbit-exchange-success/rabbit-exchange-success", //兔头兑换成功
					"style": { "navigationStyle": "custom", "navigationBarTitleText": "兑换成功"}
				},
				{
					"path": "pages/rabbit-head-order-detail/rabbit-head-order-detail", //兔头订单详情
					"style": { "navigationStyle": "custom" }
				},
				
				// 门店
				{
					"path": "pages/store-detail/store-detail", //门店详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/store-goods-search/store-goods-search", //门店商品搜索
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/store-history-invoice/store-history-invoice", //门店历史发票
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/store-shopping-cart/store-shopping-cart", //门店购物车
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/store-order-confirm/store-order-confirm", //门店确认订单
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/store-order/store-order", //门店订单列表
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/store-order-detail/store-order-detail", //门店订单详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/store-order-refund/store-order-refund", //门店订单退款
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/store-order-invoice/store-order-invoice", //门店可开票订单
					"style": { "navigationStyle": "custom" }
				}
				
			]
		},
	
		{
			"root": "packageC",
			"pages": [
				// 团战
				// {
				// 	"path": "pages/battle-all/battle-all", //全部话题
				// 	"style": { "navigationStyle": "custom" }
				// },
				// {
				// 	"path": "pages/battle-detail/battle-detail", //话题详情
				// 	"style": { "navigationStyle": "custom" }
				// },
				// {
				// 	"path": "pages/battle-comment-detail/battle-comment-detail", //话题评论详情
				// 	"style": { "navigationStyle": "custom" }
				// },
				// {
				// 	"path": "pages/live-broadcast/live-broadcast", //社区直播
				// 	"style": { "navigationStyle": "custom" }
				// },
				// // 话题
				{
					"path": "pages/more-topic-list/more-topic-list", //更多话题列表
					"style": {"navigationStyle": "custom"}
				},
				{
					"path": "pages/topic-detail/topic-detail", //话题详情
					"style": { 
						"navigationStyle": "custom",
						"enablePullDownRefresh": true,
						"backgroundTextStyle": "dark"
					}
				},
				// // 发帖
				{
					"path": "pages/send-post/send-post", //发送帖子
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/my-post/my-post", //我的帖子
					"style": {"navigationStyle": "custom"}
				},
				// {
				// 	"path": "pages/post-detail/post-detail", //帖子详情
				// 	"style": {"navigationStyle": "custom"}
				// },
				// // 评论
				// {
				// 	"path": "pages/user-comments/user-comments", //用户评论
				// 	"style": { "navigationStyle": "custom" }
				// },
				{
					"path": "pages/user-comments-more-topic/user-comments-more-topic", //用户评论更多话题
					"style": { "navigationStyle": "custom" }
				},
				
				// 发酒评
				{
					"path": "pages/wine-comment-send/wine-comment-send", //发酒评
					"style": { "navigationStyle": "custom" }
				},
				// 酒评
				{
					"path": "pages/wine-comment/wine-comment", //酒评
					"style": { "navigationStyle": "custom" }
				},
				// 酒评详情
				{
					"path": "pages/wine-comment-detail/wine-comment-detail",
					"style": {"navigationStyle": "custom","enablePullDownRefresh": true }
				},
				{
					"path": "pages/topic-more/topic-more",
					"style": {"navigationStyle": "custom"}
				}
			]
		},
	
	    // 资讯（酒会、酒闻）
		{
			"root": "packageD",
			"pages": [
				// 酒会
				{
					"path": "pages/wine-party-boutique/wine-party-boutique", //精品酒会
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/wine-party-detail/wine-party-detail", //酒会详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/wine-party-order-confirm/wine-party-order-confirm", //酒会确认订单
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/wine-party-order-list/wine-party-order-list", //酒会订单列表
					"style": {"navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/wine-party-order-detail/wine-party-order-detail", //酒会订单详情
					"style": {"navigationStyle": "custom" }
				},
				// 酒闻
				{
					"path": "pages/wine-smell-news/wine-smell-news", //酒闻资讯（酒闻列表）
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/wine-smell-detail/wine-smell-detail", // 酒闻详情
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true}
				}
			]
		},
		
		// 我的（个人中心）
		{
			"root": "packageE",
			"pages": [
				{
					"path": "pages/address-add/address-add", //新建地址
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/address-new-label/address-new-label", //地址添加标签
					"style": {"navigationStyle": "custom"}
				},
				{
					"path": "pages/address-management/address-management", //地址管理
					"style": {"navigationStyle": "custom"}
				},
				{
					"path": "pages/certification-apply/certification-apply", //申请认证
					"style": {"navigationStyle": "custom"}
				},
				{
					"path": "pages/certification-detail/certification-detail", //认证详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/coupon-list/coupon-list", //优惠券列表
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/coupon-history/coupon-history",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/daily-tasks/daily-tasks", //每日任务
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/invoice-mangement/invoice-mangement", //发票管理
					"style": {"navigationStyle": "custom"}
				},
				{
					"path": "pages/invoice-head-add/invoice-head-add", //发票抬头添加
					"style": {"navigationStyle": "custom"}
				},
				{
					"path": "pages/invoice-head-update/invoice-head-update", //发票抬头修改
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/large-turntable/large-turntable", //大转盘
					"style": {"navigationStyle": "custom" }
				},
				{
					"path": "pages/large-turntable-prize-record/large-turntable-prize-record", //大转盘中奖记录
					"style": {"navigationStyle": "custom", "navigationBarTitleText": "大转盘"}
				},
				{
					"path": "pages/message-center/message-center", //消息中心
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/my-collection/my-collection", //我的收藏
					"style": {"navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				// 体验券
				{
					"path": "pages/experience-coupon/experience-coupon", //体验券
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/experience-coupon-list/experience-coupon-list", //体验券列表
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/experience-coupon-detail/experience-coupon-detail", //体验券详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/my-footprint/my-footprint", //我的足迹
					"style": {"navigationStyle": "custom", "enablePullDownRefresh": true}
				},
				{
					"path": "pages/my-fans-list/my-fans-list", //我的粉丝列表
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/my-attention-list/my-attention-list", //我的关注列表
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true}
				},
				{
					"path": "pages/my-grade/my-grade", //我的等级
					"style": { "navigationStyle": "custom", "navigationBarTitleText": "我的等级" }
				},
				{
					"path": "pages/my-order/my-order", //我的订单
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/system-setting/system-setting", //系统设置
					"style": { "navigationStyle": "custom" }
				},
				{
					"path" : "pages/update-nickname/update-nickname", //修改用户昵称
					"style": {"navigationStyle": "custom"}
				},
				{
					"path" : "pages/user-info/user-info", //用户信息
					"style": { "navigationStyle": "custom" }
				},
				{
					"path" : "pages/wish-list/wish-list", //心愿清单
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path" : "pages/store-invoice-apply/store-invoice-apply",
					"style": { "navigationStyle": "custom" }
				}
			]
		},
		
		// 工具（ 全屏播放视频、无网络监听、web-view ）
		{
			"root": "packageF",
			"pages": [
				{
					"path": "pages/components-demo/components-demo", //组件测试
					"style": {"navigationStyle": "custom" }
				},
				{
					"path": "pages/full-screen-video/full-screen-video", //全屏播放视频
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/global-search/global-search", //全局搜索
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/no-network/no-network", //无网络
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/web-view/web-view", //web-view（小程序嵌套的h5页面）
					"style" : { "navigationBarTitleText": "酒云网" }
				}
			]
		},
		
		// 营销活动（等级、新人）
		{
			"root": "packageG",
			"pages": [
				{
					"path": "pages/auction-share/auction-share", //拍卖分享
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/grade-zone/grade-zone", //等级福利
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/newcomer-welfare-zone/newcomer-welfare-zone", //新人活动
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/about/about",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/large-turntable/large-turntable", //大转盘
					"style": {"navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/large-turntable-prize-record/large-turntable-prize-record", //大转盘中奖记录
					"style": {"navigationStyle": "custom", "navigationBarTitleText": "大转盘"}
				}
			]
		},

		{
			"root": "packageH",
			"pages": [
				{
					"path": "pages/auction-index/auction-index",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-index-new/auction-index-new",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-mine/auction-mine",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-goods-create/auction-goods-create",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-goods-create-step/auction-goods-create-step",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-goods-list/auction-goods-list",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-goods-detail/auction-goods-detail",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-bid-records/auction-bid-records",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-msg/auction-msg",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-msg-detail/auction-msg-detail",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-enjoy/auction-enjoy",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-my-participation/auction-my-participation",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-remind/auction-remind",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-real-name/auction-real-name",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-rn-personal/auction-rn-personal",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-rn-personal-new/auction-rn-personal-new",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-rn-company/auction-rn-company",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-funds-list/auction-funds-list",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-funds-detail/auction-funds-detail",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-funds-list-new/auction-funds-list-new",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-funds-detail-new/auction-funds-detail-new",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-earnest-list/auction-earnest-list",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-earnest-detail/auction-earnest-detail",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-my-goods-list/auction-my-goods-list",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-my-goods-detail/auction-my-goods-detail",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-credit-values/auction-credit-values",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-credit-values-intro/auction-credit-values-intro",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-bank-list/auction-bank-list",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-bank-add/auction-bank-add",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-payee-account/auction-payee-account",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-payee-account-edit/auction-payee-account-edit",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-goods-drafts/auction-goods-drafts",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-search/auction-search",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-certificate-list/auction-certificate-list",
					"style": { "navigationStyle": "custom" }
				},
				// 订单板块
				{
					"path": "pages/auction-buyer-order-list/auction-buyer-order-list", //我拍到的订单列表
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-buyer-order-detail/auction-buyer-order-detail", //我拍到的订单详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-order-detail/auction-order-detail", //拍卖订单详情(新)
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-buyer-after-sale-detail/auction-buyer-after-sale-detail", //我拍到的售后详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-seller-order-list/auction-seller-order-list", //我卖出的订单列表
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-seller-order-detail/auction-seller-order-detail", //我卖出的订单详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-seller-after-sale-detail/auction-seller-after-sale-detail", //我卖出的售后详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-logistics-info/auction-logistics-info", //物流信息
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-order-evaluate/auction-order-evaluate", //订单评价
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-order-evaluate-new/auction-order-evaluate-new", //拍卖订单评价（新）
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-select-service/auction-select-service", //选择服务
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-order-evaluate-list/auction-order-evaluate-list", //订单评价列表
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-buyer-after-sale-list/auction-buyer-after-sale-list", //买家申请售后列表
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-seller-after-sale-list/auction-seller-after-sale-list", //卖家申请售后列表
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-after-sale-progress/auction-after-sale-progress", //售后进度
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-ad-detail/auction-ad-detail",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-my-buying-list/auction-my-buying-list",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-goods-create-new/auction-goods-create-new",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-entrust-earnest-pay/auction-entrust-earnest-pay",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-entrust-earnest-pay-success/auction-entrust-earnest-pay-success",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-my-create-list/auction-my-create-list",
					"style": { "navigationStyle": "custom", "enablePullDownRefresh": true }
				},
				{
					"path": "pages/auction-my-create-list-search/auction-my-create-list-search",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-my-create-drafts/auction-my-create-drafts",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/auction-navigation/auction-navigation",
					"style": { "navigationStyle": "custom" }
				}
			]
		},
		// 学堂
		{
			"root": "packageI",
			"pages": [
				{
					"path": "pages/index/index", //学堂首页
					"style": { "navigationStyle": "custom" }
				},
                {
					"path": "pages/course/details/details", //学堂详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/index/ranking-list", //学堂首页
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/course/index", //课程
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/school-test/index", //测试列表
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/school-test/test-detail", //测试入口
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/school-test/answer-list", //答题页面
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/school-test/answer-finish", //答题结算
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/school-test/errorQuestionList", //错题列表
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/school-test/questionDetail", //错题详情
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/my-favorites/index",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/learning-records/index", //学习记录
					"style": {"navigationStyle": "custom"}
				},
                {
					"path": "pages/my-favorites/index", //收藏
					"style": {"navigationStyle": "custom"}
				},
                {
					"path": "pages/my-favorites/favoritesQuestion", //收藏试题详情
					"style": {"navigationStyle": "custom"}
				},
                {
					"path": "pages/my-certificate/index", //我的证书列表
					"style": {"navigationStyle": "custom"}
				},
                {
					"path": "pages/my-certificate/details", //我的证书详情
					"style": {"navigationStyle": "custom"}
				}
			]
		},
		// 余额礼品卡
		{
			"root": "packageJ",
			"pages": [
				{
					"path": "pages/my-balance/index", //我的余额
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/balance-recharge/balance-recharge",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/card-exchange/card-exchange",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/gift-card/gift-card",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/gift-card/select-gift-card",
					"style": { "navigationStyle": "custom" }
				},
				{
					"path": "pages/gift-card/send-gift-card",
					"style": { "navigationStyle": "custom" }
				}
				
			]
		}

	],
	
	// 全局配置样式
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "酒云网",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#FFFFFF"
	},
	
	// tabBar
	"tabBar": {
	    "list": [
			{ "pagePath": "pages/index/index" }, 
		    { "pagePath": "pages/flash-purchase/flash-purchase" },
			// { "pagePath": "pages/community/community" },
			{ "pagePath": "pages/miaofa/miaofa" },
			{ "pagePath": "pages/mine/mine" }
		]
	}
}
