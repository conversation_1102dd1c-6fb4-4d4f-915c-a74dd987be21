<template>
  <view class="">
    <!-- 顶部导航 -->
    <view v-if="$vhFrom == '' " class="">
      <vh-navbar title="余额充值" />
    </view>
    
    <!-- 内容区域 -->
    <view v-if="!loading" class="fade-in">
      <!-- 余额卡片 - 使用渐变背景和阴影效果 -->
      <!-- <view class="balance-card">
        <view class="font-26 text-ffffff">余额（元）</view>
					<view class="font-56 font-wei text-ffffff mt-16">{{balance || '0.00'}}</view>
		</view> -->
    
      <!-- 充值优惠选项 - 使用网格布局和更现代的卡片设计 -->
      <view class="recharge-section">
        <!-- <view class="section-title">选择充值金额</view> -->
        <view class="recharge-grid">
          <view
            v-for="(item, index) in rechargeOptions"
            :key="index"
            :class="{ 'selected': selectedOption === index }"
            class="recharge-option"
            @click="selectOption(index)"
          >
            <view class="recharge-amount">充￥{{item.price}}</view>
            <view v-if="item.gift_amount>0" class="bonus-amount">赠￥{{item.gift_amount}}</view>
          </view>
        </view>
      </view>
      
      <!-- 礼品卡部分 - 新增的功能区块 -->
      <view class="gift-card-section ">
        <view class="gift-card-prompt">为朋友送上一份惊喜</view>
        <button class="gift-card-button" @click="purchaseGiftCard">
          <text class="gift-icon">🎁</text>
          购买礼品卡
        </button>
      </view>
    </view>
    <!-- 底部协议与按钮 -->
    <view class="p-fixed bottom-0 w-p100 bg-ffffff b-sh-00001002-007 p-b-safe-area">
      <view class="d-flex a-center ptb-16-plr-24 bt-s-01-f8f8f8">
       
        <vh-check :checked="isAgree" @click="toggleAgree" />
        <view class="font-24 text-9 ml-10">
          资金由商家收取，我已阅读并同意
          <text class="text-e80404" @click="jump.h5Jump(`${agreementPrefix}/storedValueCardProtocol`, $vhFrom)">《储值协议》</text>
        </view>
      </view>
      
      <view class="pl-24 pr-24 pb-62">
        <u-button 
         :ripple="true" ripple-bg-color="#ffffff"
          :custom-style="{ width:'646rpx',color:'#fff',backgroundColor: !canRecharge ? '#FCE4E3' : '#E80404', border:'none'}"

          :disabled="!canRecharge"
          shape="circle"
          @click="recharge"
        >立即充值</u-button>
      </view>
    </view>
    <!-- 骨架屏 -->
    
    <!-- Custom Agreement Modal -->
    <view v-if="showAgreementModal" class="custom-modal-overlay">
      <view class="custom-modal">
        <view class="custom-modal-content">
          <view class="mb-16 text-center font-32">提示</view>
          <view class="custom-modal-text">
            充值即表示您已阅读并同意
            <text class="text-e80404" @click="viewAgreement">《储值协议》</text>
          </view>
          <view class="custom-modal-buttons">
            <view class="custom-modal-button cancel" @click="closeAgreementModal">取消</view>
            <view class="custom-modal-button confirm" @click="confirmRecharge">立即充值</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
export default {
  data() {
    return {
      balance: '0',
      rechargeOptions: [],
      selectedOption: -1,
      isAgree: false,
      loading: true,
      showAgreementModal: false
    }
  },
  
  onLoad() {
    this.getBalanceInfo();
    this.requestGiftCardList();
    uni.setNavigationBarTitle({
      title: '余额充值',
    })
  },
  
  computed: {
    ...mapState(['routeTable', 'agreementPrefix']),
    
    canRecharge() {
      return this.selectedOption !== -1 ;
    }
  },
  methods: {
    ...mapMutations(['muPayInfo']),
    async getBalanceInfo() {
      let res = await this.$u.api.myCurrentBalance()
      const data = res.data;
      this.balance = (data.recharge_balance + data.bonus_balance).toFixed(2);
    },
   
    selectOption(index) {
      this.selectedOption = index;
    },
    
    toggleAgree() {
      this.isAgree = !this.isAgree;
    },
    
    purchaseGiftCard() {
      this.jump.appAndMiniJump(2, this.routeTable.pJGiftCard, this.$vhFrom)
      // 可以添加跳转到礼品卡购买页面的逻辑
    },

    async requestGiftCardList(){
      this.loading = true;
      let res = await this.$u.api.giftCardList({page:1, limit:20, type:1})
      const {total, list} = res.data;
      this.loading = false;
      this.rechargeOptions = list;
    },
    
    goToExchangeCard() {
      this.jump.appAndMiniJump(0, this.routeTable.pJCardExchange, this.$vhFrom)
    },
    
    async recharge() {
      if (!this.isAgree) {
        this.showAgreementModal = true;
      } else {
        this.processRecharge();
      }
    },
    
    closeAgreementModal() {
      this.showAgreementModal = false;
    },
    
    viewAgreement() {
      this.jump.h5Jump(`${this.agreementPrefix}/storedValueCardProtocol`, this.$vhFrom);
      // Keep modal open so user can still choose after viewing agreement
    },
    
    confirmRecharge() {
      this.isAgree = true;
      this.showAgreementModal = false;
      this.processRecharge();
    },
    
    async processRecharge() {
      const option = this.rechargeOptions[this.selectedOption];
      
      this.feedback.loading({ title: '正在下单...' });
      try {
        const data = {
          goods_id: option.id,
          order_from: this.$client,
          type: 1,
          order_qty: 1
        };
        let res = await this.$u.api.giftCardOrderCreate(data);
        let payInfo = {
          payPlate: 60,
          ...res.data, //下单信息
        };
        
        if(this.$app){
          const appData = {
            paylmfor: payInfo,
            type: 60,
            priceString: payInfo.payment_amount,
            androidMainOrderNo: payInfo.order_no,
            androidFrom: '60'
          };
          this.jump.jumpAppPayment(this.$vhFrom, appData);
          if(this.$vhFrom != 'next'){
            wineYunJsBridge.openAppPage({
              client_path: { "ios_path":"finish", "android_path":"finish" }
            });
          }
        } else {
          this.muPayInfo(payInfo);
          this.jump.appAndMiniJump(1, this.routeTable.pBPayment, this.$vhFrom, 1);
        }
      } catch (e) {
        console.log(e);
        this.feedback.hideLoading();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* 基础样式重置 */
* {
  box-sizing: border-box;
}

/* 淡入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
 
 padding-bottom: 180rpx;
}


/* 充值部分样式 */
.recharge-section {
  background: white;
  margin: 24rpx 32rpx 24rpx;
  padding: 36rpx 32rpx;
  border-radius: 32rpx;
  // box-shadow: 0 4rpx 32rpx rgba(0, 0, 0, 0.04);
  // border: 1px solid #f5f5f5;
  // animation: fadeInUp 0.6s ease-out;
  // animation-delay: 0.1s;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
  margin-bottom: 32rpx;
  color: #1a1a1a;
}

.recharge-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
}

.recharge-option {
  border: 2px solid #f0f0f0;
  border-radius: 24rpx;
  padding: 40rpx 32rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  position: relative;
  overflow: hidden;
}

.recharge-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 71, 87, 0.05), transparent);
  transition: left 0.5s;
}

.recharge-option:hover::before {
  left: 100%;
}

.recharge-option:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(255, 71, 87, 0.15);
}

.recharge-option.selected {
  border-color: #ff4757;
  background: linear-gradient(135deg, #fff5f5, #fff0f0);
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(255, 71, 87, 0.2);
}

.recharge-amount {
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #1a1a1a;
}

.bonus-amount {
  font-size: 26rpx;
  color: #ff4757;
  font-weight: 600;
}

/* 礼品卡部分样式 */
.gift-card-section {
  background: linear-gradient(135deg, #fff8f0, #fff5eb);
  margin: 0 68rpx 48rpx 68rpx;
  padding: 32rpx 48rpx;
  border-radius: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 1px solid #ffeaa7;
  position: relative;
  
}

.gift-card-section::before {
  content: '✨';
  position: absolute;
  top: 32rpx;
  right: 40rpx;
  font-size: 40rpx;
  opacity: 0.6;
}

.gift-card-prompt {
  font-size: 28rpx;
  color: #8b4513;
  margin-bottom: 28rpx;
  font-weight: 500;
}

.gift-card-button {
  background: linear-gradient(135deg, #ffa726, #ff8f00);
  color: white;
  border: none;
  padding: 0rpx 56rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 167, 38, 0.3);
}

.gift-card-button:active {
  transform: translateY(-6rpx);
  box-shadow: 0 12rpx 40rpx rgba(255, 167, 38, 0.4);
}

.gift-icon {
  font-size: 36rpx;
}

/* 底部部分样式 */
.bottom-section {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: white;
  padding: 40rpx;
  border-top: 1px solid #f0f0f0;
  backdrop-filter: blur(10px);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.agreement {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.agreement input[type="checkbox"] {
  margin-right: 20rpx;
  margin-top: 4rpx;
  transform: scale(1.1);
}

.agreement-link {
  color: #ff4757;
  text-decoration: none;
  font-weight: 500;
}

.recharge-button {
  width: 100%;
  background: linear-gradient(135deg, #ff4757, #ff3838);
  color: white;
  border: none;
  padding: 32rpx;
  border-radius: 24rpx;
  font-size: 34rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 32rpx rgba(255, 71, 87, 0.3);
}

.recharge-button:not(:disabled):active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(255, 71, 87, 0.4);
}

.recharge-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式优化 */
@media (max-width: 320px) {
  .recharge-grid {
    gap: 24rpx;
  }
  
  .recharge-option {
    padding: 32rpx 24rpx;
  }
  
  .balance-card {
    margin: 40rpx 16rpx;
    padding: 48rpx 40rpx;
  }
  
  .recharge-section,
  .gift-card-section {
    margin: 0 16rpx 40rpx;
    padding: 48rpx 40rpx;
  }
}

/* Custom Modal Styles */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-modal {
  width: 80%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.custom-modal-content {
  padding:  40rpx 30rpx 0 30rpx;
  
}

.custom-modal-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 40rpx;
  text-align: center;
}

.text-e80404 {
  color: #E80404;
}

.custom-modal-buttons {
  display: flex;
  border-top: 1px solid #eee;
}

.custom-modal-button {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
}

.custom-modal-button.cancel {
  color: #666;
  border-right: 1px solid #eee;
}

.custom-modal-button.confirm {
  color: #E80404;
  font-weight: 500;
}
</style>
