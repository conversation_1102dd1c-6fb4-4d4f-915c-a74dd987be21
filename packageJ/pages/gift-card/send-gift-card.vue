<template>
  <view class="send-gift-card-container bg-ffffff">
    <!-- 导航栏 -->
   
    <view v-if="$vhFrom == '' " class=""> 
      <vh-navbar title="赠送礼品卡" />
    </view>
    <!-- 礼品卡展示 -->
    <view class="gift-card-display">
      <view class="gift-card "  :style="{ backgroundImage: `url(${background})`, backgroundRepeat: 'no-repeat' }">
        <!-- <text class="card-amount text-e80404 font-wei">{{amount}}元充值卡</text> -->
      </view>
    </view>
    
    <!-- 赠送方式选择 -->
    <!-- <view class="send-method-container">
      <view class="text-center ">
        <text class="text-center font-30 text-3">选择赠送方式</text>
      </view>
      
      <view class="w-p100 flex-c-c">
        <view class=" h-180 d-flex j-sb a-center w-300">
				<view class="d-flex flex-column a-center">
					<button class="sha-fri-btn w-88 h-88 b-rad-p50"></button>
					<view class="mt-16 font-24 text-3 l-h-34">微信好友</view>
				</view>
				<view class="d-flex flex-column a-center">
					<button class="sha-fri-cir-btn w-88 h-88 b-rad-p50"></button>
					<view class="mt-16 font-24 text-3 l-h-34">朋友圈</view>
				</view>
			</view>
      </view>
    </view> -->
    
    <!-- 赠送注意事项 -->
   
    <view class="send-notice">
      <view class="notice-title font-28 text-9">赠送须知</view>
      <view class="notice-items">
        <view class="notice-item font-26 text-9">1.此礼品卡是酒云网虚拟卡，可免费送给好友。对方领取后即可登录酒云网APP或小程序进行充值。</view>
        <view class="notice-item font-26 text-9">2.此礼品卡只可赠送别人，只需把链接，通过微信分享给你想赠与的人即可。您可在【我的礼品卡】-【已送出】中查看卡片领取详情。</view>
        <view class="notice-item font-26 text-9">3.此礼品卡送出后不允许进行退款。</view>
      </view>
    </view>
    <view class="pl-24 pr-24 pb-62">
        <u-button 
        
         :ripple="true" ripple-bg-color="#ffffff"
          :custom-style="{ width:'646rpx',color:'#fff',backgroundColor: shareCode==''? '#FCE4E3' : '#E80404' , border:'none'}"
          shape="circle"
          @click="immediatelySend"
          :disabled="shareCode==''"
        >立即赠送</u-button>
      </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      amount: 0, // 礼品卡金额
      cardNo: '', // 礼品卡卡号
      sendMethod: 'wx', // 赠送方式：wx-微信，mobile-用户编号
      showUserIdPopup: false, // 是否显示输入用户编号弹窗
      userId: '', // 用户编号
      loading: false ,// 是否正在加载,
      shareCode:'',
      background:''
    }
  },
  onLoad(options) {
    if (options) {
      // 获取传入的参数
      this.amount = options.amount || 0
      this.cardNo = options.card_no || ''
     
    }
    this.requestGiftCardList();
    // 设置导航栏标题
    uni.setNavigationBarTitle({
      title: '赠送礼品卡'
    })
    this.getShareCode();
  },
  methods: {
    // 切换赠送方式
    changeSendMethod(method) {
      this.sendMethod = method
      if (method === 'mobile') {
        this.showUserIdPopup = true
      }
    },
    
    // 确认通过用户编号赠送
    async confirmSendToUserId() {
      if (!this.userId) {
        this.feedback.toast({ title: '请输入用户编号' })
        return
      }
      
      this.showUserIdPopup = false
      this.sendGiftCard()
    },
      // 礼品卡详情
      async requestGiftCardList() {
      let res = await this.$u.api.giftCardsDetail({card_no: this.cardNo});
      this.background = res.data.background;
      
    },
    async getShareCode(){
      let res = await this.$u.api.giftCardSend({ card_no: this.cardNo })
      this.shareCode = res.data.share_code;
      console.log('分享码:', res.data.share_code, this.cardNo);
    },
    immediatelySend(){
      if( this.$app ) {
					if(this.$vhFrom == 'next'){
						
					} else{
            console.log('33333333-----------');
            
            let path = `packageJ/pages/gift-card-redeem/gift-card-redeem?card_no=${this.cardNo}&share_code=${this.shareCode}` 
            this.jump.appShare({
              title:'酒云网',
              des: '您有礼品卡待领取',
              img: this.background,
              path,
            })
					}
					
				}else {
					this.feedback.toast({ title: '请前往APP或小程序查看此功能~'})
				}
     
     
    },

   
  }
}
</script>

<style scoped>
.send-gift-card-container {
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 礼品卡展示样式 */
.gift-card-display {
  /* padding: 40rpx 30rpx; */
  display: flex;
  justify-content: center;
}

.gift-card {
  width: 690rpx;
  aspect-ratio: 503/320; 
  border-radius: 16rpx;
  position: relative;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  /* padding: 20rpx 30rpx; */
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  background-size: contain;
  background-position: center;
}

.bg-gradient {
  background: linear-gradient(90deg, #58cdc2, #c2eae2, #f9e8df, #f7c1c7);
}

.card-amount {
  font-size: 32rpx;
}

/* 赠送方式选择样式 */
.send-method-container {
  padding: 30rpx;
}

.send-method-title {
  margin-bottom: 30rpx;
}

.send-method-options {
  display: flex;
  justify-content: center;
}

.method-option {
  width: 45%;
  height: 150rpx;
  border-radius: 12rpx;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.active-method {
  background-color: #feeced;
  border: 2rpx solid #e80404;
}

.method-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 12rpx;
}

/* 赠送注意事项样式 */
.send-notice {
  padding: 30rpx;
  margin-top: 20rpx;
}

.notice-title {
  margin-bottom: 20rpx;
}

.notice-items {
  padding-left: 10rpx;
}

.notice-item {
  margin-bottom: 16rpx;
  line-height: 1.5;
}

/* 底部按钮样式 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 30rpx;
  background-color: #ffffff;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
}

/* 用户编号输入弹窗样式 */
.user-id-popup {
  padding: 40rpx 30rpx;
  background-color: #ffffff;
}

.popup-title {
  text-align: center;
  margin-bottom: 30rpx;
}

.popup-actions {
  margin-top: 30rpx;
}

.mt-30 {
  margin-top: 30rpx;
}
.sha-fri-btn{
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/comm/sha_fir.png);
		background-size: cover;
	}
	.sha-fri-cir-btn{
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/comm/sha_fri_cir.png);
		background-size: cover;
	}
</style> 