<template>
  <view class="">
    <!-- 顶部导航 -->
   <!-- 顶部导航 -->
   <view v-if="$vhFrom == '' || $vhFrom == 'next'" class="">
      <vh-navbar title="实体卡充值" />
    </view>
    
    <!-- 余额显示 -->
    <view class="balance-card-strict">
			<view class="balance-card-content">
				<view class="flex-col-sb-s w-p100"> 
					<view class="font-26 text-3">账号余额（元）</view>
					<view class="font-56 font-wei text-3 mt-16">{{balance.toFixed(2) || '0.00'}}</view>
				</view>

			</view>
		</view>
    
    
    <!-- 充值表单 -->
    <view class="bg-ffffff b-rad-12 b-sh-00021200-022 ptb-30-plr-24 ml-24 mr-24 mt-20 mb-20">
      <view class="form-header mb-30">
        <view class="form-title font-28 text-3 font-wei text-center">充值兑换码</view>
      </view>
      
      <!-- 卡号输入框 -->
      <view class="input-item mb-24">
        <view class="input-label font-28 text-3 mb-12">卡号</view>
        <view class="input-wrapper flex-sb-c">
          <input 
            type="text" 
            v-model="cardNo"
            placeholder="请输入" 
            placeholder-class="font-28 text-9" 
            class="input-control bg-f5f5f5 ptb-20-plr-24 font-28 text-3 b-rad-08"
          />
         
        </view>
      </view>
      
      <!-- 卡密输入框 -->
      <view class="input-item mb-36">
        <view class="input-label font-28 text-3 mb-12">卡密</view>
        <input 
          type="text" 
          v-model="cardPwd"
          placeholder="请输入" 
          placeholder-class="font-28 text-9" 
          class="input-control bg-f5f5f5 ptb-20-plr-24 font-28 text-3 b-rad-08"
        />
      </view>
      
      <!-- 提示说明 -->
      <view class="tips-text font-24 text-9 mb-36 text-center">
        请输入卡号和卡密，点击兑换即可充值到账户
      </view>
      
      <!-- 充值按钮 -->
      <view class="pl-24 pr-24 pb-32 mt-12">
        <u-button 
         :ripple="true" ripple-bg-color="#ffffff"
          :custom-style="{color:'#fff',backgroundColor:  '#E80404', border:'none'}"
          
          shape="circle"
          @click="exchangeCard"
        >立即兑换</u-button>
      </view>
     
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area-inset-bottom"></view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      balance: '0',
      cardNo: '',
      cardPwd: ''
    }
  },
  onLoad() {
    // 获取状态栏高度
    this.getStatusBarHeight()
    // 获取余额
    this.getBalanceInfo()
    uni.setNavigationBarTitle({
        title: '实体卡充值',
      })
  },
  methods: {
    // 获取状态栏高度
    getStatusBarHeight() {
      const systemInfo = uni.getSystemInfoSync()
      this.statusBarHeight = systemInfo.statusBarHeight || 20
    },
    
    // 获取余额
    async getBalanceInfo() {
        let res = await this.$u.api.myCurrentBalance()
            const data = res.data;
            this.balance = data.recharge_balance + data.bonus_balance;
		},
    
    // 扫码输入
    scanCode() {
      uni.scanCode({
        success: (res) => {
          console.log('扫码结果：', res)
          // 将扫码结果赋值给卡号
          if (res.result) {
            this.cardNo = res.result
          }
        },
        fail: (err) => {
          console.log('扫码失败：', err)
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 提交兑换
    exchangeCard() {
      if (!this.cardNo) {
        uni.showToast({
          title: '请输入卡号',
          icon: 'none'
        })
        return
      }
      
      if (!this.cardPwd) {
        uni.showToast({
          title: '请输入卡密',
          icon: 'none'
        })
        return
      }
      
      // 调用充值接口
      uni.showLoading({
        title: '充值中...'
      })
      
      // 模拟请求
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '充值成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 更新余额
            let newBalance = parseFloat(this.balance) + 50
            this.balance = newBalance.toFixed(2)
            
            // 清空输入
            this.cardNo = ''
            this.cardPwd = ''
            
            // 充值成功后返回
            setTimeout(() => {
              uni.navigateBack()
            }, 2000)
          }
        })
      }, 1500)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/comm.css';
.balance-card-strict {
  margin: 32rpx 24rpx 0 24rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0,0,0,0.08);
}
.balance-card-content {
  padding: 36rpx 32rpx 24rpx 32rpx;
  display: flex;
}
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.status-bar {
  width: 100%;
  background-color: #ffffff;
}

.nav-bar {
  height: 88rpx;
  position: relative;
  box-shadow: 0 2rpx 6rpx 0 rgba(0, 0, 0, 0.05);
}

.nav-title {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.balance-box {
  width: 750rpx;
  padding-bottom: 36rpx;
  border-bottom: 1px solid #f5f5f5;
}

.form-box {
  width: 702rpx;
  margin: 0 auto;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.05);
  flex: 1;
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.input-control {
  width: 654rpx;
  height: 88rpx;
  box-sizing: border-box;
}

.scan-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 80rpx;
  height: 80rpx;
  z-index: 2;
}

.tips-text {
  width: 654rpx;
  margin: 0 auto;
  line-height: 36rpx;
}

.submit-btn {
  width: 654rpx;
  margin: 0 auto;
}

.btn-hover {
  opacity: 0.8;
  transform: scale(0.98);
}

.safe-area-inset-bottom {
  height: 0;
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
  background-color: #ffffff;
}

/* 兼容暗黑模式 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }
  
  .status-bar,
  .nav-bar, 
  .balance-box, 
  .form-box,
  .safe-area-inset-bottom {
    background-color: #2a2a2a;
  }
  
  .input-control {
    background-color: #333333;
  }
  
  .text-3, .text-6, .text-9 {
    color: #f5f5f5;
  }
}
</style>
