<template>
	<view class="page-container">
		<!-- Header -->
		<view class="header-section">
			<view v-if="$vhFrom == '' || $vhFrom == 'next'" class="header-nav">
				<vh-navbar back-icon-color="#fff" title="我的余额" title-size="36" :title-bold="true" title-color="#fff" />
			</view>
		</view>

		<!-- Main Content -->
		<view class="main-content">
			<!-- Hero Section with Animated Background -->
			<view class="hero-section">
				<!-- Floating Elements Background -->
				<view class="floating-elements">
					<view class="floating-circle circle-1"></view>
					<view class="floating-circle circle-2"></view>
					<view class="floating-circle circle-3"></view>
					<view class="floating-circle circle-4"></view>
				</view>

				<!-- Central Icon with Pulse Animation -->
				<view class="central-icon-container">
					<view class="pulse-ring pulse-ring-1"></view>
					<view class="pulse-ring pulse-ring-2"></view>
					<view class="pulse-ring pulse-ring-3"></view>

					<view class="main-icon-wrapper">
						<view class="icon-background">
							<view class="icon-inner">
								💳
							</view>
						</view>
						<view class="sparkle-effect">
							<view class="sparkle sparkle-1">✨</view>
							<view class="sparkle sparkle-2">✨</view>
							<view class="sparkle sparkle-3">✨</view>
						</view>
					</view>
				</view>

				<!-- Main Title -->
				<view class="hero-title">
					<view class="title-main">余额/充值卡</view>
					<view class="title-sub">（礼品卡）</view>
					<view class="title-status">功能即将上线</view>
				</view>

				<!-- Status Badge -->
				<view class="status-container">
					<view class="status-badge">
						<view class="status-icon">🚀</view>
						<text class="status-text">敬请期待</text>
						<view class="status-dot"></view>
					</view>
				</view>
			</view>

			<!-- Features Preview Section -->
			<view class="features-preview">
				<view class="preview-title">即将为您带来</view>

				<view class="features-list">
					<view
						v-for="(feature, index) in features"
						:key="index"
						class="feature-card"
						:style="{ animationDelay: (index * 0.2) + 's' }"
					>
						<view class="feature-icon-container">
							<view class="feature-icon">{{ feature.icon }}</view>
						</view>
						<view class="feature-content">
							<view class="feature-title">{{ feature.title }}</view>
							<view class="feature-desc">{{ feature.desc }}</view>
						</view>
						
					</view>
				</view>
			</view>


			<!-- Footer -->
			<view class="footer-section">
				<view class="footer-text">感谢您的耐心等待 · 精彩功能即将与您见面</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	export default{
		name: 'my-balance',

		data(){
			return{
				loading: false,
				features: [
					{
						icon: "💰",
						title: "余额充值",
						desc: "随时随地为账户充值"
					},
					{
						icon: "🎁",
						title: "赠送礼品卡",
						desc: "为亲友送上贴心礼品，传递温暖心意"
					},
					{
						icon: "⚡",
						title: "便捷余额支付",
						desc: "一键支付，享受快速结账体验"
					}
				]
			}
		},

		computed: {
			...mapState(['routeTable'])
		},
		onLoad() {
			uni.setNavigationBarTitle({
				title: '我的余额'
			})
		},

		methods: {
			// 可以在这里添加未来的功能方法
		}
	}
</script>

<style>
@import '@/common/css/comm.css';

/* Page Container */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #CA101B 0%, #8B0000 50%, #CA101B 100%);
  position: relative;
  overflow: hidden;
}

/* Header */
.header-section {
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-nav {
  background: rgba(202, 16, 27, 0.95);
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

/* Main Content */
.main-content {
  padding: 40rpx 32rpx 80rpx;
  position: relative;
  z-index: 10;
}

/* Hero Section */
.hero-section {
  text-align: center;
  padding: 60rpx 0 80rpx;
  position: relative;
}

/* Floating Background Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 80rpx;
  height: 80rpx;
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 60rpx;
  height: 60rpx;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.circle-4 {
  width: 100rpx;
  height: 100rpx;
  bottom: 20%;
  right: 10%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20rpx) rotate(180deg); }
}

/* Central Icon */
.central-icon-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 60rpx;
}

.pulse-ring {
  position: absolute;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s ease-out infinite;
}

.pulse-ring-1 {
  width: 200rpx;
  height: 200rpx;
  animation-delay: 0s;
}

.pulse-ring-2 {
  width: 240rpx;
  height: 240rpx;
  animation-delay: 0.5s;
}

.pulse-ring-3 {
  width: 280rpx;
  height: 280rpx;
  animation-delay: 1s;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.main-icon-wrapper {
  position: relative;
  z-index: 10;
}

.icon-background {
  width: 160rpx;
  height: 160rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.icon-inner {
  font-size: 80rpx;
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

/* Sparkle Effects */
.sparkle-effect {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.sparkle {
  position: absolute;
  font-size: 24rpx;
  animation: sparkle 3s ease-in-out infinite;
}

.sparkle-1 {
  top: 20rpx;
  right: 30rpx;
  animation-delay: 0s;
}

.sparkle-2 {
  bottom: 30rpx;
  left: 20rpx;
  animation-delay: 1s;
}

.sparkle-3 {
  top: 50%;
  right: 10rpx;
  animation-delay: 2s;
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0); }
  50% { opacity: 1; transform: scale(1); }
}

/* Hero Title */
.hero-title {
  margin-bottom: 40rpx;
}

.title-main {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.title-sub {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16rpx;
}

.title-status {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* Status Badge */
.status-container {
  display: flex;
  justify-content: center;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20rpx);
}

.status-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.status-text {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
  margin-right: 12rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  background: #00ff88;
  border-radius: 50%;
  animation: blink 2s ease-in-out infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

/* Features Preview */
.features-preview {
  margin-bottom: 60rpx;
}

.preview-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  text-align: center;
  margin-bottom: 40rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.feature-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30rpx);
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card:active {
  transform: scale(0.98);
}

.feature-icon-container {
  flex-shrink: 0;
  margin-right: 24rpx;
}

.feature-icon {
  font-size: 48rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.feature-content {
  flex: 1;
  min-width: 0;
}

.feature-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.feature-arrow {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-left: 16rpx;
  transition: transform 0.3s ease;
}

.feature-card:active .feature-arrow {
  transform: translateX(8rpx);
}

/* Progress Section */
.progress-section {
  margin-bottom: 60rpx;
}

.progress-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  text-align: center;
  margin-bottom: 32rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 85%;
  background: linear-gradient(90deg, #00ff88, #00cc6a);
  border-radius: 6rpx;
  animation: progressFill 2s ease-out;
}

@keyframes progressFill {
  from { width: 0%; }
  to { width: 85%; }
}

.progress-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #00ff88;
  min-width: 60rpx;
}

.progress-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1.4;
}

/* Notification Section */
.notification-section {
  margin-bottom: 60rpx;
}

.notification-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.notification-card:active {
  transform: scale(0.98);
}

.notification-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  animation: ring 3s ease-in-out infinite;
}

@keyframes ring {
  0%, 100% { transform: rotate(0deg); }
  10%, 30% { transform: rotate(-10deg); }
  20% { transform: rotate(10deg); }
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.notification-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* Footer */
.footer-section {
  text-align: center;
  padding-top: 40rpx;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 640rpx) {
  .main-content {
    padding: 32rpx 24rpx 60rpx;
  }

  .hero-section {
    padding: 40rpx 0 60rpx;
  }

  .title-main {
    font-size: 40rpx;
  }

  .title-sub {
    font-size: 28rpx;
  }

  .title-status {
    font-size: 24rpx;
  }

  .icon-background {
    width: 120rpx;
    height: 120rpx;
  }

  .icon-inner {
    font-size: 60rpx;
  }

  .pulse-ring-1 {
    width: 160rpx;
    height: 160rpx;
  }

  .pulse-ring-2 {
    width: 200rpx;
    height: 200rpx;
  }

  .pulse-ring-3 {
    width: 240rpx;
    height: 240rpx;
  }

  .feature-card {
    padding: 24rpx;
  }

  .feature-icon {
    font-size: 40rpx;
    width: 64rpx;
    height: 64rpx;
  }

  .feature-title {
    font-size: 28rpx;
  }

  .feature-desc {
    font-size: 24rpx;
  }

  .preview-title {
    font-size: 32rpx;
  }

  .progress-title {
    font-size: 28rpx;
  }

  .notification-card {
    padding: 24rpx;
  }

  .notification-icon {
    font-size: 40rpx;
  }

  .notification-title {
    font-size: 26rpx;
  }

  .notification-desc {
    font-size: 22rpx;
  }

  .floating-circle {
    display: none;
  }
}
</style>
