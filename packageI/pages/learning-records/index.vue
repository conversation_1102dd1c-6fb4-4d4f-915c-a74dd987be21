<template>
  <view class="container">
    <view>
      <vh-navbar v-if="!$appStatusBarHeight" back-icon-color="#fff" title="" :background="background" />
      <!-- <view slot="right">
          <u-icon
            @click="addCollection"
            class="header-right-icon"
            :color="in_collection ? '#D60C0C' : '#999'"
            :name="in_collection ? 'star-fill' : 'star'"
            size="44"
          ></u-icon>
        </view>
      </vh-navbar> -->

      <!-- app（安卓、ios） -->
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: '#D60C0C' }">
          <view :style="{ height: $appStatusBarHeight + 'px' }" />
          <view class="p-rela ml-20 h-px-48 d-flex a-center">
            <view class="h-p100 d-flex a-center" @click="jump.navigateBack()">
              <u-icon name="nav-back" color="#fff" :size="44" />
            </view>
            <view class="font-36 font-wei text-333333"></view>
          </view>
        </view>
      </view>
    </view>
    <u-tabs
      :style="{ top: !$appStatusBarHeight ? '44px' : parseInt($appStatusBarHeight) + 48 + 'px' }"
      style="position: fixed; width: 100%; z-index: 11112"
      :list="tabs"
      :custom-back="gotoBack"
      active-color="#fff"
      font-size="34"
      inactive-color="#FF8383"
      bg-color="#D60C0C"
      :is-scroll="false"
      :current="currentIndex"
      @change="switchTab"
    ></u-tabs>

    <!-- <u-icon class="back-icon" name="arrow-left" @click="gotoBack" color="#fff" size="42"></u-icon> -->
    <view
      class=""
      v-if="list.length"
      :style="{ marginTop: !$appStatusBarHeight ? '44px' : parseInt($appStatusBarHeight) + 98 + 'px' }"
    >
      <view class="favorites-list-item" v-if="!currentIndex">
        <!-- 试卷 -->
        <view class="list" v-for="(item, index) in list" :key="index">
          <view class="test-title"> {{ item.title }} </view>
          <view class="atc-line"></view>
          <view class="test-time">
            <view class="test-time-flex" style="margin-bottom: 16rpx">
              <view class="test-time-title">开始时间</view>
              <view class="test-time-number">{{ item.start }}</view>
            </view>
            <view class="test-time-flex">
              <view class="test-time-title">成绩</view>
              <view class="score"
                ><text style="color: #41cc8e" v-if="item.status === 1">{{ item.score }}</text
                ><text v-if="item.status !== 1">-</text>/{{ item.total_score }}</view
              >
            </view>
          </view>
          <view class="test-footer" v-if="item.total_score !== item.score"
            ><view class="atc-line"> </view>
            <view class="test-footer-flex">
              <view class="test-footer-text">
                <view v-if="item.status !== 1"
                  >还剩<text style="color: #41cc8e"> {{ item.total_num - item.done + 1 }} </text>道题</view
                >
                <view v-if="item.status === 1"
                  >错<text style="color: #e80404"> {{ item.incorrect }} </text>道题</view
                >
                <view></view>
              </view>
              <view>
                <u-button
                  @click="goTestResume(item)"
                  v-if="item.status !== 1"
                  :custom-style="customStyle"
                  size="mini"
                  type="error"
                  >继续测试</u-button
                >
                <u-button v-else :custom-style="customStyle" @click="viewErrorQuestionList(item)" size="mini"
                  >查看错题</u-button
                >
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="favorites-list-item" v-else>
        <!-- 章节 -->
        <view class="list" v-for="(item, index) in list" :key="index">
          <view class="atc" @click="goDetails(item)">
            <img class="atc-list-item-img" :src="item.cover_img" alt="" />
            <view class="atc-list-item-content">
              <view class="atc-list-item-content-title">{{ item.title }}</view>
              <view class="atc-list-item-content-desc">{{ item.subtitle }}</view>
            </view>
          </view>
          <view class="atc-line"></view>
          <view class="list-footer">
            <view class="opr">{{ item.topic }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="empty" v-else>
      <view class="box">
        <img src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/empty.png" alt="" />
        <view class="empty-text"> 目前还没有任何学习记录哦 </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      background: {
        backgroundColor: '#D60C0C',
      },
      tabs: [{ name: '测试' }, { name: '章节' }],
      currentIndex: 0,
      list: [],
      appStatusBarHeight: 0,
      customStyle: {
        width: '130rpx',
        borderRadius: '100rpx',
      },
    }
  },
  onShow() {
    this.search()
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
      },
    })
  },
  watch: {
    currentIndex(val) {
      this.list = []
      if (!val) {
        this.getExamScanRecord()
      } else {
        this.getChapterScanRecord()
      }
    },
  },
  methods: {
    viewErrorQuestionList(item) {
      this.jump.navigateTo(`${this.$routeTable.PITestErrorQuestionList}?paper_id=${item.paper_id}`)
    },
    goTestResume(row) {
      console.log(row)
      this.jump.navigateTo(`${this.$routeTable.PIAnswerList}?id=${row.exam_id}&paper_id=${row.paper_id}&from=records`)
    },
    goDetails(item) {
      this.jump.navigateTo(`${this.$routeTable.PICourseDetails}?id=${item.id}`)
    },
    search() {
      if (!this.currentIndex) {
        this.getExamScanRecord()
      } else {
        this.getChapterScanRecord()
      }
    },
    async cancelFav(item) {
      let data = {}
      if (this.currentIndex) {
        data = {
          chapter_id: item.id,
        }
      } else {
        data = {
          question_id: item.id,
        }
      }
      const res = await this.$u.api.addCollection(data)
      this.feedback.toast({
        title: '操作成功',
      })
      this.search()
    },
    switchTab(index) {
      this.currentIndex = index
    },

    async getChapterScanRecord() {
      const data = {
        page: 1,
        limit: 999,
      }
      const res = await this.$u.api.getChapterScanRecord(data)
      console.log(res)
      this.list = res.data.list
    },
    async getExamScanRecord() {
      const data = {
        page: 1,
        limit: 999,
      }
      const res = await this.$u.api.getExamScanRecord(data)
      console.log(res)
      this.list = res.data.list
    },

    gotoBack() {
      uni.reLaunch({
        url: '/packageI/pages/index/index',
      })
      //   this.jump.appAndMiniJump(1, '/packageI/pages/index/index', this.$vhFrom, 0)
    },
  },
}
</script>

<style scoped lang="scss">
.container {
  padding-bottom: 20rpx;
  background-color: #f5f5f5;
}
::v-deep .u-back-wrap {
  z-index: 1111;
}
.slot-wrap {
  display: flex;
  align-items: center;
  /* 如果您想让slot内容占满整个导航栏的宽度 */
  flex: 1;
  /* 如果您想让slot内容与导航栏左右有空隙 */
  padding: 0 30rpx;
}
.favorites-header {
  z-index: 1111;
  background-color: #f5f5f5;
  background-image: url('http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/learning-record.png');
  background-repeat: no-repeat;
  background-size: cover;
  position: fixed;
  top: 40px;
  height: 168rpx;
  width: 100%;
}

.empty {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  .box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .empty-text {
    font-size: 24rpx;
    color: #999999;
    line-height: 34rpx;
    margin-top: 20rpx;
    text-align: center;
    font-style: normal;
  }
}
.test-footer-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.test-footer-text {
  font-family: PingFangSC;
  font-size: 24rpx;
  letter-spacing: 0.5rpx;
  //   color: #666666;
  line-height: 34rpx;
  text-align: left;
  font-style: normal;
}
.header-search {
  width: 85%;
  margin: 0 auto !important;
}
.back-icon {
  margin-top: 48rpx;
  margin-left: 14rpx;
}
::v-deep .uni-scroll-view-content {
  padding-bottom: 20rpx;
}
.favorites-list-item {
  min-height: 100vh;
  padding-top: 30rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
  .list {
    background: #ffffff;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    padding: 24rpx 20rpx 20rpx 20rpx;
    min-height: 142rpx;
    .test-time {
      .test-time-flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .test-time-number {
        font-size: 26rpx;
        color: #333333;
        line-height: 36rpx;
        text-align: left;
        font-style: normal;
      }
      .test-time-title {
        font-size: 26rpx;
        margin-bottom: 10rpx;
        color: #666666;
        line-height: 36rpx;
        text-align: left;
        font-style: normal;
      }
    }
    .test-title {
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
    }
    .atc-line {
      width: 646rpx;
      height: 2rpx;
      margin: 20rpx 0 16rpx 0;
      background: #f5f5f5;
    }
    .atc {
      display: flex;

      .atc-list-item-img {
        margin-right: 20rpx;
        width: 180rpx;
        height: 180rpx;
        border-radius: 6rpx;
      }
      .atc-list-item-content {
        width: 446rpx;
        .atc-list-item-content-title {
          display: -webkit-box;
          -webkit-line-clamp: 1; /* 显示几行文本 */
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 20rpx;
          font-weight: 500;
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
        }
        .atc-list-item-content-desc {
          display: -webkit-box;
          -webkit-line-clamp: 3; /* 显示几行文本 */
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 26rpx;
          color: #666666;
          line-height: 36rpx;
          text-align: justify;
          font-style: normal;
        }
      }
    }

    .list-footer {
      margin-top: 28rpx;

      .time,
      .opr {
        font-size: 24rpx;
        color: #666666;
        line-height: 34rpx;
        text-align: left;
        font-style: normal;
      }
    }
    .list-title {
      font-weight: 500;
      font-size: 28rpx;
      color: #555;
      line-height: 40rpx;
      text-align: left;
    }
  }
}
.tabs {
  display: flex;
  width: 100%;
  justify-content: space-evenly;
  align-items: center;
  .tabs-title {
    font-size: 36rpx;
    color: #ff8383;
    line-height: 50rpx;
    text-align: center;
    font-style: normal;
  }
}
.active {
  color: #fff !important;
}
</style>
