<template>
  <view class="error-question-list-box">
    <view>
      <vh-navbar
        v-if="!$appStatusBarHeight"
        :background="background"
        :title="'错题列表(' + total + ')'"
        back-icon-color="#fff"
        title-color="#fff"
      >
      </vh-navbar>

      <!-- app（安卓、ios） -->
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: '#D60C0C' }">
          <view :style="{ height: $appStatusBarHeight + 'px' }" />
          <view class="p-rela h-px-48 d-flex j-center a-center">
            <view class="p-abso left-24 h-p100 d-flex a-center" @click="jump.navigateBack()">
              <u-icon name="nav-back" color="#fff" :size="44" />
            </view>
            <view class="font-36 font-wei text-ffffff">错题列表({{ total }})</view>
          </view>
        </view>
      </view>
    </view>

    <view
      class="error-list-item"
      :style="{ paddingTop: !$appStatusBarHeight ? '0px' : parseInt($appStatusBarHeight) + 58 + 'px' }"
    >
      <view class="item-list" v-for="(item, index) in list" :key="index" @click="viewDetails(item)">
        <view class="item-list-title">
          {{ item.question }}
        </view>
        <view class="footer">
          <view class="answer-bottom">
            <view class="success-answer">
              <text class="success-answer-title">答案</text><text class="success-answer-option">{{ item.answer }}</text>
            </view>
            <view class="error-answer">
              <text class="success-answer-title">您选</text
              ><text class="error-answer-option">{{ item.option_selected }}</text>
            </view>
          </view>
          <view class="view-details"> 查看详情 </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      total: 0,
      paper_id: '',
      list: [],
      appStatusBarHeight: 0,
      background: {
        backgroundColor: '#D30808',
      },
    }
  },
  onLoad(option) {
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
      },
    })
    this.paper_id = option.paper_id
  },
  onShow() {
    this.getQuestionList()
  },
  methods: {
    viewDetails(row) {
      this.jump.navigateTo(`${this.$routeTable.PITestErrorQuestionDetails}?paper_id=${this.paper_id}&id=${row.id}`)
    },
    async getQuestionList() {
      const data = {
        score: 0,
        paper_id: this.paper_id,
      }
      const res = await this.$u.api.getExamQuestionList(data)
      this.total = res.data.total
      this.list = res.data.list
    },
  },
}
</script>

<style lang="scss" scoped>
.error-question-list-box {
  min-height: 100vh;
  background-color: #f5f5f5;
  .error-list-item {
    padding: 32rpx;
    .item-list {
      margin-bottom: 20rpx;
      padding: 24rpx 20rpx;
      background-color: #fff;
      border-radius: 10rpx;
      .item-list-title {
        font-weight: 500;
        font-size: 28rpx;
        color: #555;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
.footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
}
.view-details {
  color: #666;
  font-size: 24rpx;
  line-height: 34rpx;
}
.answer-bottom {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .success-answer-title {
    color: #666;
    font-size: 24rpx;
    line-height: 34rpx;
  }

  .success-answer-option {
    font-size: 24rpx;
    font-weight: bold;
    margin: 0 16rpx 0 4rpx;
    color: #41cc8e;
  }

  .error-answer-option {
    font-size: 24rpx;
    font-weight: bold;
    margin: 0 16rpx 0 4rpx;
    color: #e80404;
  }
}
.header {
  position: fixed;
  background-color: #f5f5f5;
  top: 0;
  width: 100%;
  left: 0;
  .title {
    position: absolute;
    top: 90rpx;
    left: 40.3%;
    font-weight: 600;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;

    z-index: 111;
  }

  .icon {
    position: absolute;
    top: 48rpx;
    left: 14rpx;
    z-index: 111;
  }

  img {
    width: 100%;
  }
}
</style>
