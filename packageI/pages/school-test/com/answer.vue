<template>
  <view class="container">
    <view class="question-block">
      <view class="top-bar">
        <view v-if="hiddenIndex === 1" class="test-title"> 随堂测试 </view>
        <view v-else-if="hiddenIndex === 0">
          <text style="color: #e80404; font-size: 28rpx">{{ info.now }}</text>
          <text style="color: #999999; font-size: 24rp">/{{ info.total }}</text>
        </view>
        <u-icon
          v-if="hiddenIndex !== 2"
          @click="addCollection"
          class="star-icon"
          :color="info.in_collection ? '#D60C0C' : '#999'"
          :name="info.in_collection ? 'star-fill' : 'star'"
          size="44"
        ></u-icon>
      </view>
      <text class="question-title">{{ info.question }}</text>
      <view
        class="option"
        v-for="(option, index) in info.option"
        :class="{ 'success-option': successIndex === index, 'error-option': errorIndex === index }"
        :key="index"
        @click="selectOption(index)"
      >
        <text>{{ option.key }}. {{ option.val }}</text>
        <view class="icon-answer">
          <u-icon name="close-circle-fill" v-if="errorIndex === index" color="#E80404" size="34"></u-icon>
          <u-icon name="checkmark-circle" v-if="successIndex === index" color="#41CC8E" size="34"></u-icon>
        </view>
      </view>
      <view class="answer-bottom">
        <view class="success-answer" v-if="successOption || displayAnalysis">
          <text class="success-answer-title">答案</text><text class="success-answer-option">{{ successOption }}</text>
        </view>
        <view class="error-answer" v-if="errorOption">
          <text class="success-answer-title">您选</text><text class="error-answer-option">{{ errorOption }}</text>
        </view>
      </view>
    </view>
    <view class="answer-analysis" v-if="successOption || displayAnalysis">
      <img
        class="answer-icon"
        src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/analysis-icon.png"
        alt=""
      />
      <view class="answer-analysis-title">试题解析</view>
      <rich-text class="answer-analysis-analysis" :nodes="analysis"></rich-text>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    displayAnalysis: {
      type: Boolean,
      default: false,
    },
    hiddenIndex: {
      default: 0,
      type: Number,
    },
  },

  data() {
    return {
      successOption: '',
      errorOption: '',
      analysis: '',
      in_collection: 0,
      successIndex: '',
      errorIndex: '',
    }
  },
  watch: {
    info: {
      immediate: true,
      handler(val, oldVal) {
        console.log(11)
        if (val.option_selected || this.displayAnalysis) {
          this.emitShowButtonValue()
          if (val.option_selected === val.answer) {
            this.successOption = val.option_selected
          } else {
            this.errorOption = val.option_selected
            this.successOption = val.answer
          }
          val.option.map((item, index) => {
            if (item.key === this.successOption) {
              this.successIndex = index
            }
            if (item.key === this.errorOption) {
              this.errorIndex = index
            }
          })
          this.analysis = val.analysis
        } else {
          this.errorIndex = ''
          this.errorOption = ''
          this.successOption = ''
          this.successIndex = ''
        }
      },
    },
  },
  methods: {
    emitShowButtonValue() {
      this.$emit('update:showButton', 1)
    },
    async selectOption(index) {
      if (this.displayAnalysis) {
        return
      }
      if (!this.info.option_selected && !this.successOption) {
        const data = {
          id: this.info.id,
          option_selected: this.info.option[index].key,
        }
        try {
          const res = await this.$u.api.submitAnswer(data)

          if (res.error_code == 0) {
            this.emitShowButtonValue()
            this.analysis = res.data.analysis
            if (res.data.is_right) {
              this.successOption = res.data.option_selected
            } else {
              this.errorOption = res.data.option_selected
              this.successOption = res.data.answer
            }
            this.info.option.map((item, index) => {
              if (item.key === this.successOption) {
                this.successIndex = index
              }
              if (item.key === this.errorOption) {
                this.errorIndex = index
              }
            })
          }
        } catch (e) {
          if (e.data.error_code == 500) {
            this.$emit('timeout')
          }
        }
      }
    },
    async addCollection() {
      // 切换收藏状态的逻辑
      const data = {
        question_id: this.info.question_id,
      }
      const res = await this.$u.api.addCollection(data)
      this.info.in_collection = !this.info.in_collection
      this.feedback.toast({
        title: '操作成功',
      })
    },
  },
}
</script>

<style scoped lang="scss">
.answer-analysis {
  margin-top: 20rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  background-color: #fff;
  border-radius: 10rpx;
  padding: 24rpx 20rpx;
  position: relative;

  .answer-icon {
    right: 30rpx;
    top: 44rpx;
    position: absolute;
  }

  .answer-analysis-title {
    font-weight: bold;
    color: #333;
    font-size: 28rpx;
    line-height: 40rpx;
    margin-bottom: 24rpx;
  }

  .answer-analysis-analysis {
    color: #666;
    font-size: 26rpx;
    line-height: 36rpx;
  }
}

.top-bar {
  padding: 10rpx 10rpx 10rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.star-icon {
  width: 30rpx;
  height: 30rpx;
}

.question-block {
  padding: 20rpx;
  background-color: #f5f5f5;

  padding: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.question-title {
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  text-align: left;
  font-style: normal;
}

.option {
  margin-top: 20rpx;
  /* padding: 20rpx; */
  line-height: 32rpx;
  padding: 12rpx 12rpx 12rpx 24rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  margin-bottom: 28rpx;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option text {
  color: #333;
  font-size: 26rpx;
}

.icon-answer {
}

.success-option {
  background-color: #fff;

  border: 4rpx solid #41cc8e;

  text {
    color: #41cc8e;
  }
}

.error-option {
  background-color: #fff;

  border: 4rpx solid #e80404;

  text {
    color: #e80404;
  }
}

.answer-bottom {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .success-answer-title {
    color: #333;
    font-size: 24rpx;
    line-height: 34rpx;
  }

  .success-answer-option {
    font-size: 24rpx;
    font-weight: bold;
    margin: 0 16rpx 0 4rpx;
    color: #41cc8e;
  }

  .error-answer-option {
    font-size: 24rpx;
    font-weight: bold;
    margin: 0 16rpx 0 4rpx;
    color: #e80404;
  }
}
.test-title {
  margin-bottom: 20rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #666;
  line-height: 44rpx;
  text-align: left;
}
</style>
