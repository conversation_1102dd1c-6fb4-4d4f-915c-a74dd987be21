<template>
  <view class="error-question-list-box">
    <view>
      <vh-navbar
        v-if="!$appStatusBarHeight"
        :background="background"
        title="错题详情"
        back-icon-color="#fff"
        title-color="#fff"
      >
      </vh-navbar>

      <!-- app（安卓、ios） -->
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: '#D60C0C' }">
          <view :style="{ height: $appStatusBarHeight + 'px' }" />
          <view class="p-rela h-px-48 d-flex j-center a-center">
            <view class="p-abso left-24 h-p100 d-flex a-center" @click="jump.navigateBack()">
              <u-icon name="nav-back" color="#fff" :size="44" />
            </view>
            <view class="font-36 font-wei text-ffffff">错题详情</view>
          </view>
        </view>
      </view>
    </view>
    <answerItem
      :style="{ paddingTop: !$appStatusBarHeight ? '0px' : parseInt($appStatusBarHeight) + 58 + 'px' }"
      :hiddenIndex="2"
      class="answer"
      ref="answerItem"
      v-if="info.category_id == 1"
      :info="info"
    >
    </answerItem>
  </view>
</template>

<script>
import answerItem from './com/answer.vue'
export default {
  components: {
    answerItem,
  },
  data() {
    return {
      paper_id: '',
      info: {},
      id: '',
      background: {
        backgroundColor: '#D30808',
      },
    }
  },
  onLoad(option) {
    this.id = option.id
    this.paper_id = option.paper_id
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
      },
    })
  },
  onShow() {
    this.getQuestionDetails()
  },
  methods: {
    async getQuestionDetails() {
      const data = {
        id: this.id,
        score: 0,
        paper_id: this.paper_id,
      }
      const res = await this.$u.api.getExamQuestionList(data)
      console.log(res.data.list)
      this.info = res.data.list[0] ? res.data.list[0] : {}
    },
  },
}
</script>

<style lang="scss" scoped>
.answer {
  padding: 32rpx;
}
.error-question-list-box {
  min-height: 100vh;
  background-color: #f5f5f5;
  .error-list-item {
    margin-top: 160rpx;
    padding: 32rpx;
    .item-list {
      margin-bottom: 20rpx;
      padding: 24rpx 20rpx;
      background-color: #fff;
      border-radius: 10rpx;
      .item-list-title {
        font-weight: 500;
        font-size: 28rpx;
        color: #555;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
.footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
}
.view-details {
  color: #666;
  font-size: 24rpx;
  line-height: 34rpx;
}
.answer-bottom {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .success-answer-title {
    color: #666;
    font-size: 24rpx;
    line-height: 34rpx;
  }

  .success-answer-option {
    font-size: 24rpx;
    font-weight: bold;
    margin: 0 16rpx 0 4rpx;
    color: #41cc8e;
  }

  .error-answer-option {
    font-size: 24rpx;
    font-weight: bold;
    margin: 0 16rpx 0 4rpx;
    color: #e80404;
  }
}
.header {
  position: fixed;
  //   background-color: #f5f5f5;
  top: 0;
  width: 100%;
  left: 0;
  z-index: 111;
  .title {
    position: absolute;
    top: 90rpx;
    left: 40.3%;
    font-weight: 600;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;

    z-index: 111;
  }

  .icon {
    position: absolute;
    top: 40px;
    left: 14rpx;
    z-index: 111;
  }

  img {
    width: 100%;
  }
}
</style>
