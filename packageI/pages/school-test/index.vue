<template>
  <view class="test-body">
    <view>
      <vh-navbar
        v-if="!$appStatusBarHeight"
        :background="background"
        title="测试"
        back-icon-color="#fff"
        title-color="#fff"
        :titleWidth="400"
      >
      </vh-navbar>

      <!-- app（安卓、ios） -->
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: '#D60C0C' }">
          <view :style="{ height: $appStatusBarHeight + 'px' }" />
          <view class="p-rela h-px-48 d-flex j-center a-center">
            <view class="p-abso left-24 h-p100 d-flex a-center" @click="jump.navigateBack()">
              <u-icon name="nav-back" color="#fff" :size="44" />
            </view>
            <view class="font-36 font-wei text-ffffff">测试</view>
          </view>
        </view>
      </view>
    </view>
    <view
      class="content"
      v-if="list.length"
      :style="{ paddingTop: !$appStatusBarHeight ? '10px' : parseInt($appStatusBarHeight) + 58 + 'px' }"
    >
      <view class="content-item" v-for="(item, index) in list" :key="index">
        <view class="content-item-title">
          {{ item.title }}
        </view>
        <view class="content-item-desc">
          {{ item.description }}
        </view>
        <view class="content-item-bottom">
          <view class="left">
            <view class="left-count" v-if="item.status === 2">
              <text style="color: #d30808">{{ item.done }}</text
              >/{{ item.total_num }}
            </view>
            <view class="left-count-new" v-if="item.status === 0"> 共{{ item.total_num }}道题 </view>
            <view class="left-count-old" v-if="item.status === 1">
              共{{ item.total_num }}道题 ｜ <text>上次得分</text>
              <text style="color: #6bd7a7; margin-left: 8rpx">{{ item.score }}</text
              >/
              <text style="font-weight: 400; color: #000">{{ item.total_score }}</text>
            </view>
          </view>
          <view class="right">
            <u-button
              @click="goTestResume(item)"
              v-if="item.status === 2"
              :custom-style="customStyle"
              shape="square"
              size="mini"
              plain
              type="error"
              class="cont-test"
              >继续测试</u-button
            >
            <u-button
              style="border-radius: 100rpx"
              @click="goTest(item)"
              v-else
              :custom-style="customStyle"
              size="mini"
              type="error"
              >开始测试</u-button
            >
          </view>
        </view>
      </view>
    </view>
    <view class="empty" v-else>
      <view class="box">
        <img src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/empty.png" alt="" />
        <view class="empty-text"> 目前还没有任何测试 </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  computed: {
    ...mapState(['routeTable']),
  },
  data: () => ({
    list: [],
    background: {
      backgroundColor: '#D30808',
    },
    customStyle: {
      width: '130rpx',
    },
    appStatusBarHeight: 0,
  }),
  onShow() {
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
      },
    })
    console.log(this.comes)
    this.getExamList()
    this.getStatusBarHeight()
  },
  methods: {
    goTestResume(row) {
      // PIAnswerList
      this.jump.navigateTo(`${this.$routeTable.PIAnswerList}?id=${row.id}&paper_id=${row.paper_id}`)
      // this.jump.navigateTo(`${this.$routeTable.PITestDetails}?id=${row.id}&title=${row.title}&status=${row.status}&total_num=${row.total_num}&total_score=${row.total_score}&score=${row.score}`)
    },
    getStatusBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight
        },
      })
    },
    goTest(row) {
      this.jump.navigateTo(
        `${this.$routeTable.PITestDetails}?id=${row.id}&title=${row.title}&status=${row.status}&total_num=${row.total_num}&total_score=${row.total_score}&score=${row.score}&paper_id=${row.paper_id}`
      )
    },
    gotoBack() {
      if (this.comes.isFromApp(this.from)) {
        //判断是否从App过来 1 = 安卓 2 = ios
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'goBack', android_path: 'goBack' },
        })
      } else {
        //h5端
        this.jump.navigateBack()
      }
    },
    async getExamList() {
      const data = {
        limit: 999,
        page: 1,
      }
      const res = await this.$u.api.getExamList(data)
      this.list = res.data.list
    },
  },
}
</script>
<style scoped lang="scss">
.header {
  position: fixed;
  z-index: 111;
  background-color: #f5f5f5;
  top: 0;
  width: 100%;
  left: 0;

  .title {
    position: absolute;
    top: 90rpx;
    left: 45.4%;
    font-weight: 600;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;

    z-index: 111;
  }

  .icon {
    position: absolute;
    top: 48rpx;
    left: 14rpx;
    z-index: 111;
  }

  img {
    width: 100%;
  }
}
.empty {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  .box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .empty-text {
    font-size: 24rpx;
    color: #999999;
    line-height: 34rpx;
    margin-top: 20rpx;
    text-align: center;
    font-style: normal;
  }
}
.test-body {
  min-height: 100vh;
  background-color: #f5f5f5;

  .content {
    padding: 0 32rpx 22rpx 32rpx;

    .content-item {
      margin-bottom: 20rpx;
      background-color: #fff;
      padding: 24rpx 20rpx;
      border-radius: 6rpx;

      .content-item-bottom {
        display: flex;
        justify-content: space-between;

        align-items: center;
        color: #666666;
      }

      .content-item-title {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
      }
      .left-count-new,
      .left-count-old,
      .left-count {
        font-size: 26rpx;
        color: #666666;
      }
      .content-item-desc {
        margin: 20rpx 0;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -moz-box-orient: vertical;
        word-wrap: break-word;
        word-break: break-all;
        white-space: normal;
        max-height: 72rpx;
        font-size: 26rpx;
        color: #666666;
        line-height: 36rpx;
        text-align: justify;
        font-style: normal;
        text-overflow: ellipsis;
      }
    }
  }
}
::v-deep .uni-button:after,
.u-hairline-border:after {
  height: 0;
  width: 0;
}
::v-deep .cont-test {
  border: 1px solid #d30808;
  width: 130rpx !important;
  background-color: #fff !important;
  border-radius: 1000px;
}
</style>
