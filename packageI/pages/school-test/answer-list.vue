<template>
  <view class="answer-box">
    <view>
      <vh-navbar
        v-if="!$appStatusBarHeight"
        :background="background"
        title="答题环节"
        back-icon-color="#fff"
        :custom-back="openModal"
        title-color="#fff"
      >
      </vh-navbar>

      <!-- app（安卓、ios） -->
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: '#D60C0C' }">
          <view :style="{ height: $appStatusBarHeight + 'px' }" />
          <view class="p-rela h-px-48 d-flex j-center a-center">
            <view class="p-abso left-24 h-p100 d-flex a-center" @click="openModal()">
              <u-icon name="nav-back" color="#fff" :size="44" />
            </view>
            <view class="font-36 font-wei text-ffffff">答题环节</view>
          </view>
        </view>
      </view>
    </view>

    <view
      class="answer"
      :style="{ paddingTop: !$appStatusBarHeight ? '10px' : parseInt($appStatusBarHeight) + 58 + 'px' }"
    >
      <answerItem
        ref="answerItem"
        @update:showButton="handleShowButtonUpdate"
        v-if="info.category_id == 1"
        @timeout="timeoutSelect"
        :info="info"
      >
      </answerItem>
    </view>
    <view class="bottom-nav">
      <text class="timer">测试时间 {{ formattedTime }}</text>
      <view class="buttons" v-if="status == 3">
        <view class="prev-btn" @click="prevQuestion">上一题</view>
        <view :class="{ disable: is_select === 0 }" class="next-btn" @click="nextQuestion">下一题</view>
      </view>
      <view class="buttons" v-if="status == 2">
        <view :class="{ disable: is_select === 0 }" class="next-btn finish" @click="finish">提交试卷</view>
      </view>
      <view class="buttons" v-if="status == 1">
        <view :class="{ disable: is_select === 0 }" class="next-btn finish" @click="nextQuestion">下一题</view>
      </view>
    </view>
    <u-modal
      :show-cancel-button="true"
      cancel-text="中止测试"
      @cancel="cancel"
      :mask-close-able="true"
      confirm-color="#E53935"
      confirm-text="重置测试"
      v-model="show"
      title="温馨提示"
      @confirm="confirm"
      ref="uModal"
      :async-close="true"
    >
      <view class="slot-content">
        您已作答<text style="color: #e53935; font-weight: bold">{{ mint }}</text
        >分钟，还剩下<text style="color: #e53935; font-weight: bold">{{ info.total - info.now + 1 }}</text
        >道题，是否确认退出？
      </view>
    </u-modal>
  </view>
</template>

<script>
import answerItem from './com/answer.vue'
export default {
  components: {
    answerItem,
  },
  computed: {
    formattedTime() {
      const hrs = String(Math.floor(this.seconds / 3600)).padStart(2, '0')
      const mins = String(Math.floor((this.seconds % 3600) / 60)).padStart(2, '0')
      const secs = String(this.seconds % 60).padStart(2, '0')
      return `${hrs}:${mins}:${secs}`
    },
  },
  onLoad(option) {
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
      },
    })
    this.id = option.id
    if (option.from) {
      this.from = option.from
    }
    if (option.paper_id) {
      this.paper_id = option.paper_id
    }
  },
  onShow() {
    const data = {
      exam_id: this.id,
      paper_id: this.paper_id,
      action: 3,
    }
    this.getQuestion(data)
    if (!this.timer) {
      this.timer = setInterval(() => {
        this.seconds++
      }, 1000)
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  watch: {
    info: {
      deep: true,
      handler(val, oldVal) {
        // 1、当第一题的时，并且不是最后一题的时候，只显示“下一题”按钮。
        // 2、如果是最后一题则只显示提交试卷。
        // 3、如果不是第一题，也不是最后题的时候，则显示“上一题、下一题”
        if (val.total === val.now) {
          this.status = 2
        } else if (val.total > val.now && val.now !== 1) {
          this.status = 3
        } else {
          this.status = 1
        }
      },
    },
  },
  data: () => ({
    show: false,
    seconds: 0,
    paper_id: 0,
    from: '',
    appStatusBarHeight: 0,
    timer: null,
    status: 1,
    mint: 0,
    background: {
      background: '#D30808',
    },
    id: '',
    is_select: 0,
    info: {},
  }),
  methods: {
    openModal() {
      this.show = true
      this.mint = Math.floor(this.seconds / 60)
    },
    async cancel() {
      const data = {
        paper_id: this.info.paper_id,
      }
      const res = await this.$u.api.cancelExam(data)
      this.show = false
      if (this.from === 'reset') {
        uni.navigateBack({
          delta: 3, // 返回上两级页面
        })
      } else {
        this.jump.navigateBack()
      }

      //   }
    },
    async confirm() {
      const data = {
        paper_id: this.info.paper_id,
      }
      const res = await this.$u.api.resetExam(data)
      this.paper_id = res.data.paper_id
      this.show = false
      this.getQuestion({
        exam_id: this.id,
        paper_id: 0,
        action: 3,
      })
    },
    async finish() {
      if (this.is_select) {
        const data = {
          paper_id: this.info.paper_id,
        }
        const res = await this.$u.api.submitExam(data)
        this.show = false
        this.jump.navigateTo(
          `${this.$routeTable.PITestFinish}?badge_id=${res.data.badge_id}&exam_title=${res.data.exam_title}&total_score=${res.data.total_score}&use_tm=${res.data.use_tm}&score=${res.data.score}&exam_id=${res.data.exam_id}&paper_id=${res.data.paper_id}`
        )
      }
    },
    timeoutSelect() {
      const data = {
        exam_id: this.id,
        action: 4,
        id: this.info.id,
        paper_id: this.paper_id,
      }
      this.getQuestion(data)
    },
    nextQuestion() {
      if (this.is_select) {
        const data = {
          exam_id: this.id,
          action: 1,
          id: this.info.id,
          paper_id: this.paper_id,
        }
        this.getQuestion(data)
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0, // 动画持续时间，单位 ms
        })
      }
    },
    prevQuestion() {
      const data = {
        exam_id: this.id,
        action: 2,
        paper_id: this.paper_id,
        id: this.info.id,
      }
      this.getQuestion(data)
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0, // 动画持续时间，单位 ms
      })
    },
    handleShowButtonUpdate(newValue) {
      this.is_select = 1
    },
    async getQuestion(data) {
      try {
        const res = await this.$u.api.getExamQuestion(data)
        this.info = res.data
        if (data.action === 3) {
          this.seconds = this.info.use_tm
        }
      } catch (e) {
        if (e.data.error_code === 10004) {
          setTimeout(() => {
            uni.navigateBack()
          }, 2000)
        }
      }
    },
  },
}
</script>

<style scoped lang="scss">
.answer-box {
  padding-bottom: 176rpx;
  background-color: #f5f5f5;
  min-height: 100vh;

  .answer {
    padding: 32rpx;
  }
}

.header {
  position: fixed;
  background-color: #f5f5f5;
  top: 0;
  width: 100%;
  left: 0;

  .title {
    position: absolute;
    top: 90rpx;
    left: 42%;
    font-weight: 600;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;

    z-index: 111;
  }

  .icon {
    position: absolute;
    top: 40px;
    left: 14rpx;
    z-index: 111;
  }

  img {
    width: 100%;
  }
}

.disable {
  background-color: #dddddd !important;
  color: #ffffff !important;
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: white;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.timer {
  color: #f45e5e;
  font-size: 22rpx;
  margin-bottom: 20rpx;
}

.buttons {
  display: flex;
  width: 100%;
  justify-content: space-around;
}

.prev-btn {
  width: 216rpx;
  border-radius: 100rpx;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  background-color: white;
  border: 1rpx solid #e0e0e0;
  color: #666666;
}

.finish {
  width: 622rpx !important;
}

.next-btn {
  width: 406rpx;
  border-radius: 100rpx;
  background-color: #e53935;
  color: white;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  border: 1rpx solid #e0e0e0;
}

.slot-content {
  text-align: center;
  font-size: 28rpx;
  padding: 40rpx 60rpx;
}
</style>
