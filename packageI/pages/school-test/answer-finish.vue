<template>
  <view class="test-finish">
    <view class="header p-rela">
      <view class="ml-24 d-flex a-center" @click="jump.navigateBack({ delta: 2 })">
        <u-icon
          style="height: 48px"
          @click="goBack"
          :style="{ top: !$appStatusBarHeight ? '0px' : parseInt($appStatusBarHeight) + 'px' }"
          class="back-icon"
          name="nav-back"
          color="#fff"
          size="44"
        ></u-icon>
      </view>
      <img
        class="finish-header-bg"
        style="width: 100%"
        src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/test-finish.png"
        alt=""
      />
    </view>
    <view class="test-finish-title">
      {{ exam_title }}
    </view>
    <view class="test-finish-box">
      <img
        class="test-finish-box-icon"
        src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/test-finish-icon.png"
        alt=""
      />
      <text class="test-finish-txt">测试成绩</text>
      <view>
        <text class="score">{{ score }}</text
        ><text class="total-score">/{{ total_score }}分</text>
      </view>
      <view class="" style="margin-top: 40rpx">
        测试时间
        <text class="use-tm" style="color: #e80404">
          {{ use_tm }}
        </text>
      </view>
    </view>
    <view class="finish-footer">
      <view class="d-flex" style="width: 100%; justify-content: space-evenly">
        <view class="reset" :class="[badge_id ? '' : 'reset-w100']" @click="reset"> 重新作答 </view
        ><view class="reset" v-if="badge_id" @click="viewCert"> 查看证书 </view>
      </view>

      <view class="view-check" v-if="score != total_score" @click="viewErrorQuestionList"> 查看错题 </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  ...mapState(['routeTable']),
  onLoad(option) {
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
      },
    })
    this.exam_title = option.exam_title
    this.score = option.score
    this.badge_id = option.badge_id
    this.use_tm = option.use_tm
    this.total_score = option.total_score
    this.paper_id = option.paper_id
    this.exam_id = option.exam_id
  },
  data: () => ({
    appStatusBarHeight: 0,
    exam_title: '',
    paper_id: '',
    badge_id: '',
    score: 0,
    exam_id: '',
    use_tm: '',
    total_score: 0,
  }),
  methods: {
    viewErrorQuestionList() {
      this.jump.navigateTo(`${this.$routeTable.PITestErrorQuestionList}?paper_id=${this.paper_id}`)
    },
    async reset() {
      const data = {
        paper_id: this.paper_id,
      }
      try {
        const res = await this.$u.api.resetExam(data)
        this.jump.navigateTo(`${this.$routeTable.PIAnswerList}?id=${this.exam_id}&from=reset`)
      } catch (e) {
        if (e.data.error_code === 10003) {
          setTimeout(() => {
            this.goBack()
          }, 2000)
        }
      }
    },
    viewCert() {
      this.jump.navigateTo(`${this.$routeTable.PICertDetails}?id=${this.badge_id}`)
    },
    goBack() {
      uni.navigateBack({
        delta: 2, // 返回上两级页面
      })
      // this.jump.reLaunch(`${this.$routeTable.PITestList}`)
    },
  },
}
</script>

<style scoped lang="scss">
.reset-w100 {
  width: 660rpx !important;
}
.finish-footer {
  display: flex;
  margin-top: 20rpx;
  flex-direction: column;
  align-items: center;
  .reset {
    width: 310rpx;
    height: 80rpx;
    border: 2rpx solid #e80404;
    color: #e80404;
    border-radius: 40rpx;
    font-weight: 500;
    font-size: 28rpx;

    line-height: 80rpx;
    text-align: center;
    font-style: normal;
  }
  .view-check {
    margin-top: 20rpx;
    width: 660rpx;
    height: 80rpx;
    border-radius: 40rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #ffffff;
    background: #e80404;
    line-height: 80rpx;
    text-align: center;
    font-style: normal;
  }
}
.use-tm {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  text-align: center;
  font-style: normal;
}
.test-finish-box {
  box-shadow: 0 -2rpx 4rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  .score {
    font-weight: 500;
    font-size: 90rpx;
    color: #41cc8e;
    line-height: 126rpx;
    text-align: center;
    font-style: normal;
  }
  .total-score {
    font-weight: 500;
    font-size: 72rpx;
    color: #333333;
    line-height: 100rpx;
    text-align: center;
    font-style: normal;
  }
}
.test-finish-txt {
  font-weight: 500;
  font-size: 42rpx;
  color: #333333;
  line-height: 58rpx;
  text-align: center;
  font-style: normal;
}
.test-finish-box-icon {
  position: absolute;
  top: -110rpx;
  width: 218rpx;
  left: 33.3%;
}
.test-finish-box {
  position: relative;
  width: 686rpx;
  height: 534rpx;
  margin: 0 auto;
  margin-top: 168rpx;
  background: #ffffff;
  border-radius: 20rpx;
}
.test-finish-title {
  text-align: center;
  margin: 0 auto;
  width: 86%;
  font-size: 44rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 66rpx;
  text-align: center;
  font-style: normal;
  padding-top: 110rpx;
}
.header {
  position: relative;
  .finish-header-bg,
  .back-icon {
    position: absolute;
  }
  .finish-header-bg {
    top: 0;
    z-index: -1;
  }
  .back-icon {
    z-index: 0;
    left: 24rpx;
  }
}
.test-finish {
  // background-color: #f5f5f5;
}
</style>
