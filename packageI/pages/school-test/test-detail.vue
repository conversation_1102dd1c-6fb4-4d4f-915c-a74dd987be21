<template>
  <view class="container">
    <image
      src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/test-bg.png"
      class="background-img"
    />
    <view class="header">
      <view
        class="ml-24 d-flex a-center"
        :style="{ marginTop: !$appStatusBarHeight ? '0px' : parseInt($appStatusBarHeight) + 'px' }"
        @click="jump.navigateBack()"
      >
        <u-icon style="height: 48px" name="nav-back" color="#fff" :size="44" />
      </view>
      <!-- <u-icon name="arrow-left" class="back" @click="back" color="#fff" size="40"></u-icon> -->
      <text class="title">{{ title }}</text>
      <text class="subtitle">( 共{{ total_num }}道题 )</text>
      <text class="score" v-if="status == 1">
        <text class="score-title">上次得分</text>
        <text style="margin-left: 6rpx; color: #41cc8e" class="score-number">{{ score }}</text>
        <text class="score-number">/{{ total_score }}</text>
      </text>
    </view>
    <view class="content">
      <button class="start-btn" @click="startQuiz(status)">{{ status == 0 ? '开始答题' : '重新作答' }}</button>
    </view>
  </view>
</template>

<script>
export default {
  data: () => ({
    id: '',
    title: '',
    status: 0,
    paper_id: '',
    total_num: 0,
    total_score: 0,
    score: 0,
    appStatusBarHeight: 0,
  }),

  onLoad(option) {
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
      },
    })
    this.paper_id = option.paper_id
    this.title = option.title
    this.id = option.id
    this.status = option.status
    this.total_num = option.total_num
    this.total_score = option.total_score
    this.score = option.score
  },
  methods: {
    back() {
      if (this.comes.isFromApp(this.from)) {
        //判断是否从App过来 1 = 安卓 2 = ios
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'goBack', android_path: 'goBack' },
        })
      } else {
        //h5端
        this.jump.navigateBack()
      }
    },
    async startQuiz(status) {
      if (Number(status)) {
        const data = {
          paper_id: this.paper_id,
        }
        const res = await this.$u.api.resetExam(data)
        this.jump.redirectTo(`${this.$routeTable.PIAnswerList}?id=${this.id}`)
      } else {
        this.jump.redirectTo(`${this.$routeTable.PIAnswerList}?id=${this.id}`)
      }

      // 开始答题的逻辑
    },
  },
}
</script>

<style lang="scss" scoped>
.back {
  position: absolute;
}
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: white;
  position: relative;
  overflow: hidden; /* 确保背景图片超出视口时不会显示滚动条 */
}

.background-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 40%; /* 设置为所需背景高度的百分比 */
  z-index: 0; /* 确保背景图片在所有其他元素后面 */
}

.score {
  width: 320rpx;
  padding: 12rpx 50rpx;
  margin: 40rpx auto;
  display: inline;
  background: #ffffff;
  border-radius: 100rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.35);
  .score-title {
    font-size: 24rpx;
    color: #666666;
    line-height: 34rpx;
    text-align: center;
    font-style: normal;
  }
  .score-number {
    font-size: 28rpx;
    font-weight: bold;
    line-height: 40rpx;
    text-align: center;
    font-style: normal;
  }
}
.header {
  height: 46%;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  //   justify-content: center;
}

.back-arrow {
  position: absolute;
  left: 20rpx;
  top: 20rpx;
  width: 30rpx;
  height: 30rpx;
}

.title,
.subtitle {
  color: white;
}

.title {
  font-weight: 600;
  font-size: 62rpx;
  color: #ffffff;
  line-height: 88rpx;
  text-align: center;
  font-style: normal;
}

.subtitle {
  font-size: 32rpx;
  color: #ffffff;
  line-height: 44rpx;
  text-align: center;
  font-style: normal;
  margin-top: 10rpx;
}

.content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.start-btn {
  background-color: #e80404;
  color: white;
  padding: 14rpx 0;
  border-radius: 100rpx;
  width: 90%;
  font-size: 30rpx;
}
</style>
