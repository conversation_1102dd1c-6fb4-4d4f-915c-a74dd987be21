<template>
  <view class="content">
    <!-- 导航栏 -->
    <view class="">
      <!-- 微信小程序、h5导航栏 -->
      <vh-navbar
        v-if="from == ''"
        back-icon-color="#333"
        title="学堂"
        title-color="#333"
        :background="{ background: navBackgroundColor }"
      />

      <!-- app（安卓、ios） -->
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: navBackgroundColor }">
          <view :style="{ height: $appStatusBarHeight + 'px' }" />
          <view class="p-rela h-px-48 d-flex j-center a-center">
            <view class="p-abso left-24 h-p100 d-flex a-center" @click="jumpBack()">
              <u-icon name="nav-back" color="#333" :size="44" />
            </view>
            <view class="font-36 font-wei text-333333">学堂</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 轮播图 -->
    <view class="p-rela ml-32 mr-32 grayscale-100" :style="dynamicStyle">
      <view class="b-sh-02021200-015 b-rad-10 o-hid">
        <vh-swiper :list="swiperList" name="picture" :height="356" @click="jumpBanner($event)" />
      </view>
    </view>
    <view class="d-flex j-sb ml-38 mr-32 mt-16">
      <view
        v-for="(item, index) in toolList"
        :key="index"
        class="flex-c-c flex-column mt-32"
        @click="jumpToModel(item)"
      >
        <image :src="ossIcon(item.icon)" class="w-92 h-92"></image>
        <view class="mt-12 font-24 text-3 l-h-36">{{ item.name }}</view>
      </view>
    </view>
    <!-- 兔头商品列表（有数据 ）-->
    <view v-if="projectList.length" class="pt-48 pb-20 pl-32 pr-32">
      <view class="d-flex flex-wrap j-sb">
        <view
          class="p-rela bg-ffffff w-330 b-rad-10 o-hid mb-26"
          v-for="(item, index) in projectList"
          :key="index"
          @click="jumpCourse(item)"
        >
          <vh-image :src="item.cover_img" :height="330" />
          <view class="h-128 bg-eeeeee flex-c-c">
            <view class="font-28 text-3 l-h-40 ml-24 mr-24 text-center">{{ item.title }}</view>
          </view>
        </view>
      </view>

      <u-loadmore :status="loadStatus" />
    </view>

    <!-- 兔头商品列表（无数据） -->
    <vh-empty
      v-else
      :padding-top="52"
      :padding-bottom="400"
      image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_goods.png"
      text="暂无数据"
      :text-bottom="0"
    />
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  data: () => ({
    swiperList: [],
    toolList: [
      { name: '排行榜', icon: '/study/study_home_rank.png', pathName: '/packageI/pages/index/ranking-list' },
      // { name: '专属VIP客服', icon: '/mine_vip_36_36.png', pathName: 'pgGoodsCreateList' },
      { name: '测试', icon: '/study/study_home_test.png', pathName: '/packageI/pages/school-test/index' },
      {
        name: '我的证书',
        icon: '/study/study_home_certificate.png',
        pathName: '/packageI/pages/my-certificate/index',
      },
      {
        name: '我的收藏',
        icon: '/study/study_home_collection.png',
        pathName: '/packageI/pages/my-favorites/index',
      },
      {
        name: '学习记录',
        icon: '/study/study_home_record.png',
        pathName: '/packageI/pages/learning-records/index',
      },
    ],
    projectList: [],
    page: 1, //第几页
    limit: 10, //每页显示多少条
    totalPage: 1, //总页数
    loadStatus: 'loadmore', //加载状态,
    navBackgroundColor: '#FFF', //导航栏背景
    from: '', //从哪个端进入 1 = 安卓、2 = ios"
    appStatusBarHeight: '', //状态栏高度
  }),
  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable']),

    // 获取导航栏高度
    // getNavigationBarHeight() {
    //   return this.system.getSysInfo().statusBarHeight + 44
    // },
    dynamicStyle() {
      // 计算 marginTop 样式
      if (this.from == '') {
        return {
          marginTop: 20 + 'px',
        }
      } else {
        return {
          marginTop: this.$appStatusBarHeight + 68 + 'px',
        }
      }
    },
  },

  onLoad(options) {
    if (options.from && options.statusBarHeight) {
      this.from = options.from
      this.appStatusBarHeight = options.statusBarHeight //app状态栏高度
      this.muFrom(this.from) //保存来自哪个状态（解决用户在安卓、ios端登录失效的问题）
    }
    this.getBannerList()
    this.getProjectList()
  },
  onShow() {},
  methods: {
    // 初始化
    async init() {
      try {
        this.page = 1
        await this.getBannerList()
        await this.getProjectList()
        uni.stopPullDownRefresh() //停止下拉刷新
        this.loading = false
      } catch (e) {
        this.goBack()
      }
    },
    // 获取课程列表
    async getProjectList() {
      let res = await this.$u.api.studyTopicList({ page: this.page, limit: this.limit })
      this.page == 1 ? (this.projectList = res.data.list) : (this.projectList = [...this.projectList, ...res.data.list])
      this.totalPage = Math.ceil(res.data.total / this.limit)
      this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
    },
    async getBannerList() {
      let res = await this.$u.api.studyBannerList({})
      this.swiperList = res.data.list
    },
    // 返回首页 1.当酒闻详情页面没有暴露成首页的时候，默认回到上一个页面，2.当暴露成首页的时候回到首页
    jumpBack() {
      if (this.pageLength <= 1 && this.$vhFrom == '') {
        //当酒闻详情页被暴露成首页（分享）
        console.log('-----------------------------------------------我的页面栈 <= 1')
        this.jump.reLaunch('/pages/index/index')
      } else {
        //当商品详情页没有被暴露成首页
        if (this.comes.isFromApp(this.$vhFrom)) {
          //判断是否从App过来 1 = 安卓 2 = ios
          wineYunJsBridge.openAppPage({
            client_path: { ios_path: 'goBack', android_path: 'goBack' },
          })
        } //h5端、微信小程序
        else this.jump.navigateBack()
      }
    },
    onShow() {
      this.login.isLoginV2(this.$vhFrom).then(() => {
        this.init()
      })
    },

    jumpCourse(item) {
      this.jump.navigateTo(`${this.$routeTable.PICourse}?topic_id=${item.id}`)
    },
    jumpBanner(item) {
      console.log('banner', item)
      this.jump.navigateTo(`${this.$routeTable.PICourse}?topic_id=${item.goods_id}`)
    },
    jumpToModel(item) {
      this.jump.navigateTo(item.pathName)
    },

    onPullDownRefresh() {
      this.page = 1
      this.init()
      this.getProjectList()
    },

    onReachBottom() {
      if (this.page == this.totalPage || this.totalPage == 0) return
      this.loadStatus = 'loading'
      this.page++
      this.getProjectList()
    },
  },
}
</script>
