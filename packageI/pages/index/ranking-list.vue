<template>
  <view class="content bg-f5f5f5 h-min-vh-100">
    <!-- 导航栏 -->
    <view>
      <vh-navbar
        v-if="!$appStatusBarHeight"
        :background="background"
        back-icon-color="#fff"
        title-color="#fff"
      >
      </vh-navbar>

      <!-- app（安卓、ios） -->
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: '#E80404' }">
          <view :style="{ height: $appStatusBarHeight + 'px' }" />
          <view class="p-rela h-px-48 d-flex j-center a-center">
            <view class="p-abso left-24 h-p100 d-flex a-center" @click="jump.navigateBack()">
              <u-icon name="nav-back" color="#fff" :size="44" />
            </view>
            <view class="font-36 font-wei text-ffffff"></view>
          </view>
        </view>
      </view>
    </view>
    <!-- 轮播图 -->
    <view :style="{ paddingTop: $appStatusBarHeight  + 'px' }">
      <image :src="ossIcon('/study/rank_list_bg.png')" class="h-600 w-p100"></image>
      <!-- <view class="p-abso top-50 w-68 h-76 left-12 flex-c-c" @click="goback">
        <u-icon name="nav-back" color="#fff" :size="44"></u-icon>
      </view> -->
    </view>
    <view class="mt-n-290 ml-32 mr-32 b-rad-10 bg-ffffff z-10 p-rela">
      <view class="h-112 d-flex">
        <view class="font-28 text-9 l-h-40 mt-32 ml-20">排名</view>
        <view class="font-28 text-9 l-h-40 mt-32 ml-32">用户</view>
        <view class="font-28 text-9 l-h-40 mt-32 ml-200">耗时</view>
        <view class="font-28 text-9 l-h-40 mt-32 ml-190">分数</view>
      </view>
      <view class="d-flex h-100 d-flex a-center mb-40">
        <image :src="ossIcon('/study/study_rank_my_bg.png')" class="w-p100 h-p100"> </image>
        <view class="d-flex ml-28 p-abso">
          <image
            v-if="myRank.rank <= 3"
            :src="ossIcon(`/study/study_rank_${myRank.rank}.png`)"
            class="w-40 h-52"
          ></image>
          <view v-else class="w-40 h-52 l-h-52 font-28 text-e80404 text-center font-wei-600">{{ myRank.rank }}</view>
          <image class="ml-40 w-52 h-52 bg-eeeeee b-rad-p50" :src="myRank.avatar"></image>
          <view class="ml-06 w-188 l-h-52 font-28 text-e80404 font-wei-600  text-hidden-1">{{ myRank.nickname }}</view>
          <view class="ml-10 w-228 l-h-52 font-26 text-e80404">{{ myRank.use_tm }}</view>
          <view class="ml-10 l-h-52 text-center font-28 text-e80404 font-wei-600">{{ myRank.score }}</view>
        </view>
      </view>
      <view class="d-flex flex-column ">
        <view class="d-flex mb-40" v-for="item in rankList" :key="item.rank">
          <view class="d-flex ml-28">
            <image v-if="item.rank <= 3" :src="ossIcon(`/study/study_rank_${item.rank}.png`)" class="w-40 h-52"></image>
            <view v-else class="w-40 h-52 l-h-52 font-28 text-3 text-center font-wei-600">{{ item.rank }}</view>
            <image class="ml-40 w-52 h-52 bg-eeeeee b-rad-p50" :src="item.avatar"></image>
            <view class="ml-06 w-188 l-h-52 font-28 text-3  text-hidden-1">{{ item.nickname }}</view>
            <view class="ml-10 w-228 l-h-52 font-26 text-3">{{ item.use_tm }}</view>
            <view class="ml-10 l-h-52 text-center font-28 text-3 font-wei-600">{{ item.score }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data: () => ({
    rankList: [],
    myRank: {},
    background: {
        backgroundColor: '#E80404',
      },
      appStatusBarHeight: 0,
  }),
  computed: {
			// 获取状态栏高度
			navigationBarHeight() {
				return this.system.getSysInfo().statusBarHeight + 48
			}
		},
  methods: {
    goback() {
      if (this.comes.isFromApp(this.from)) {
        //判断是否从App过来 1 = 安卓 2 = ios
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'goBack', android_path: 'goBack' },
        })
      } else {
        //h5端
        this.jump.navigateBack()
      }
    },
    // onLoad() {
    //   this.getRankingList()
    // },
    onShow() {
    
    console.log(this.comes)
    this.getRankingList()
  },
    // 获取课程列表
    async getRankingList() {
      let res = await this.$u.api.studyRankingList({ page: 1, limit: 10 })
      this.rankList = res.data.list
      if (this.rankList.length >= 1) {
        this.myRank = this.rankList[0]
        this.rankList = this.rankList.slice(1)
      }
      console.log('ranklist', this.rankList)
    },
  },
}
</script>
