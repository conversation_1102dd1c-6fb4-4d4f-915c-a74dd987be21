<template>
  <view class="error-question-list-box">
    <!-- 微信小程序、h5导航栏 -->
    <view>
      <vh-navbar
        v-if="!$appStatusBarHeight"
        back-icon-color="#fff"
        title="试题详情"
        title-color="#fff"
        :background="background"
      />

      <!-- app（安卓、ios） -->
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: '#D30808' }">
          <view :style="{ height: $appStatusBarHeight + 'px' }" />
          <view class="p-rela h-px-48 d-flex j-center a-center">
            <view class="p-abso left-24 h-p100 d-flex a-center" @click="jump.navigateBack()">
              <u-icon name="nav-back" color="#fff" :size="44" />
            </view>
            <view class="font-36 font-wei" style="color: #fff">试题详情</view>
          </view>
        </view>
      </view>
    </view>
    <answerItem
      :style="{ paddingTop: !$appStatusBarHeight ? '10px' : parseInt($appStatusBarHeight) + 58 + 'px' }"
      :displayAnalysis="true"
      :hiddenIndex="2"
      class="answer"
      ref="answerItem"
      v-if="info.category_id == 1"
      :info="info"
    >
    </answerItem>
  </view>
</template>

<script>
import answerItem from '../school-test/com/answer.vue'
export default {
  components: {
    answerItem,
  },
  data() {
    return {
      info: {},
      appStatusBarHeight: 0,
      background: {
        backgroundColor: '#D30808',
      },
    }
  },
  onLoad(option) {
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
      },
    })
    const decodedString = decodeURIComponent(option.info)
    const dataObject = JSON.parse(decodedString)
    this.info = dataObject
  },
  methods: {
    back() {
      uni.navigateBack()
    },
  },
}
</script>

<style lang="scss" scoped>
.answer {
  padding: 32rpx;
}
.error-question-list-box {
  min-height: 100vh;
  background-color: #f5f5f5;
  .error-list-item {
    margin-top: 160rpx;
    padding: 32rpx;
    .item-list {
      margin-bottom: 20rpx;
      padding: 24rpx 20rpx;
      background-color: #fff;
      border-radius: 10rpx;
      .item-list-title {
        font-weight: 500;
        font-size: 28rpx;
        color: #555;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
.footer {
  margin-top: 30rpx;
  display: flex;
  justify-content: space-between;
}
.view-details {
  color: #666;
  font-size: 24rpx;
  line-height: 34rpx;
}
.answer-bottom {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .success-answer-title {
    color: #666;
    font-size: 24rpx;
    line-height: 34rpx;
  }

  .success-answer-option {
    font-size: 24rpx;
    font-weight: bold;
    margin: 0 16rpx 0 4rpx;
    color: #41cc8e;
  }

  .error-answer-option {
    font-size: 24rpx;
    font-weight: bold;
    margin: 0 16rpx 0 4rpx;
    color: #e80404;
  }
}
.header {
  position: fixed;
  background-color: #f5f5f5;
  top: 0;
  width: 100%;
  left: 0;

  .title {
    position: absolute;
    top: 90rpx;
    left: 40.3%;
    font-weight: 600;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;

    z-index: 111;
  }

  .icon {
    position: absolute;
    top: 48rpx;
    left: 14rpx;
    z-index: 111;
  }

  img {
    width: 100%;
  }
}
</style>
