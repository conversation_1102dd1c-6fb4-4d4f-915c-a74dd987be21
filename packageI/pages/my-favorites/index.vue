<template>
  <view class="container">
    <view>
      <vh-navbar v-if="!$appStatusBarHeight" back-icon-color="#fff" title="" :background="background">
        <view class="slot-wrap">
          <view class="favorites-header" style="top: 0px">
            <!-- <u-icon class="back-icon" name="arrow-left" @click="gotoBack" color="#fff" size="42"></u-icon> -->
            <view style="padding-top: 80rpx">
              <u-search
                class="header-search"
                :show-action="false"
                @search="search"
                placeholder="请输入关键字"
                v-model="keyword"
              ></u-search>
            </view>
            <view class="tabs">
              <view v-for="(item, index) in tabs" :key="index" @click="switchTab(index)">
                <text class="tabs-title" :class="[currentIndex === index ? 'active' : '']">
                  {{ item }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </vh-navbar>
      <!-- app（安卓、ios） -->
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: '#D60C0C' }">
          <view :style="{ height: $appStatusBarHeight + 'px' }" />
          <view class="p-rela h-px-48 d-flex a-center" style="z-index: 1111111">
            <view class="ml-20 h-p100 d-flex a-center" @click="jump.navigateBack()">
              <u-icon name="nav-back" color="#fff" :size="44" />
            </view>
            <view class="font-36 font-wei text-333333"></view>
          </view>
          <view class="favorites-header" :style="{ top: $appStatusBarHeight + 18 + 'px' }">
            <!-- <u-icon class="back-icon" name="arrow-left" @click="gotoBack" color="#fff" size="42"></u-icon> -->
            <view :style="{ paddingTop: 34 + 'px' }">
              <u-search
                class="header-search"
                :show-action="false"
                @search="search"
                placeholder="请输入关键字"
                v-model="keyword"
              ></u-search>
            </view>
            <view class="tabs">
              <view v-for="(item, index) in tabs" :key="index" @click="switchTab(index)">
                <text class="tabs-title" :class="[currentIndex === index ? 'active' : '']">
                  {{ item }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view
      :style="{ paddingTop: !$appStatusBarHeight ? '108px' : parseInt($appStatusBarHeight) + 171 + 'px' }"
      v-if="list.length"
    >
      <view class="favorites-list-item" v-if="!currentIndex">
        <view class="list" v-for="(item, index) in list" :key="index">
          <view class="list-title" @click="goDetails(item)">{{ item.question }}</view>
          <view class="list-footer">
            <view class="time">收藏时间 {{ item.create_time.split(' ')[0] }}</view>
            <view class="opr" @click="cancelFav(item)">取消收藏</view>
          </view>
        </view>
      </view>
      <view class="favorites-list-item" v-else>
        <view class="list" v-for="(item, index) in list" :key="index">
          <view class="atc" @click="goDetails(item)">
            <img class="atc-list-item-img" :src="item.cover_img" alt="" />
            <view class="atc-list-item-content">
              <view class="atc-list-item-content-title">{{ item.title }}</view>
              <view class="atc-list-item-content-desc">{{ item.subtitle }}</view>
            </view>
          </view>
          <view class="atc-line"></view>
          <view class="list-footer">
            <view class="time">收藏时间 {{ item.create_time.split(' ')[0] }}</view>
            <view class="opr" @click="cancelFav(item)">取消收藏</view>
          </view>
        </view>
      </view>
    </view>
    <view class="empty" v-else>
      <view class="box">
        <img src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/empty.png" alt="" />
        <view class="empty-text"> 您还没有收藏任何东西哦 </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      tabs: ['收藏的试题', '收藏的章节'],
      currentIndex: 0,
      keyword: '',
      appStatusBarHeight: 0,
      background: {
        backgroundColor: '#D60C0C',
      },
      list: [],
    }
  },
  onShow() {
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
        // this.appStatusBarHeight = 38
      },
    })
    this.search()
  },
  watch: {
    currentIndex(val) {
      this.list = []
      this.keyword = ''
      if (!val) {
        this.getTest()
      } else {
        this.getChapterCollection()
      }
    },
  },
  methods: {
    goDetails(item) {
      if (!this.currentIndex) {
        const jsonString = JSON.stringify(item)
        const encodedString = encodeURIComponent(jsonString)
        this.jump.navigateTo(`${this.$routeTable.PIFavoritesQuestion}?info=${encodedString}`)
      } else {
        this.jump.navigateTo(`${this.$routeTable.PICourseDetails}?id=${item.id}`)
      }
    },
    search() {
      if (!this.currentIndex) {
        this.getTest()
      } else {
        this.getChapterCollection()
      }
    },
    async cancelFav(item) {
      let data = {}
      if (this.currentIndex) {
        data = {
          chapter_id: item.id,
        }
      } else {
        data = {
          question_id: item.id,
        }
      }
      const res = await this.$u.api.addCollection(data)
      this.feedback.toast({
        title: '操作成功',
      })
      this.search()
    },
    switchTab(index) {
      this.currentIndex = index
    },

    async getChapterCollection() {
      const data = {
        page: 1,
        limit: 999,
        keyword: this.keyword,
      }
      const res = await this.$u.api.getChapterCollection(data)
      console.log(res)
      this.list = res.data.list
    },
    async getTest() {
      const data = {
        page: 1,
        limit: 999,
        keyword: this.keyword,
      }
      const res = await this.$u.api.getQuestionCollection(data)
      console.log(res)
      this.list = res.data.list
    },

    gotoBack() {
      if (this.comes.isFromApp(this.from)) {
        //判断是否从App过来 1 = 安卓 2 = ios
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'goBack', android_path: 'goBack' },
        })
      } else {
        //h5端
        this.jump.navigateBack()
      }
    },
  },
}
</script>

<style scoped lang="scss">
.slot-wrap {
  display: flex;
  align-items: center;
  /* 如果您想让slot内容占满整个导航栏的宽度 */
  flex: 1;
  /* 如果您想让slot内容与导航栏左右有空隙 */
  padding: 0 30rpx;
}
.container {
  background-color: #f5f5f5;
}
::v-deep .u-back-wrap {
  z-index: 1111;
}
.favorites-header {
  background-color: #f5f5f5;
  background-image: url('http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/collect-header.png');
  background-repeat: no-repeat;
  background-size: cover;
  position: fixed;
  left: 0;
  height: 282rpx;
  width: 100%;
}
::v-deep .vh-back-wrap {
  z-index: 111111;
}
.empty {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  .box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .empty-text {
    font-size: 24rpx;
    color: #999999;
    line-height: 34rpx;
    margin-top: 20rpx;
    text-align: center;
    font-style: normal;
  }
}
.header-search {
  width: 70%;
  margin: 0 auto !important;
}
.back-icon {
  margin-top: 48rpx;
  margin-left: 14rpx;
}
.favorites-list-item {
  min-height: 100vh;
  //   padding-top: 80rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
  .list {
    background: #ffffff;
    border-radius: 10rpx;
    padding: 24rpx 20rpx;
    margin-bottom: 20rpx;
    min-height: 142rpx;
    .atc-line {
      width: 646rpx;
      height: 2rpx;
      margin: 20rpx 0 16rpx 0;
      background: #f5f5f5;
    }
    .atc {
      display: flex;

      .atc-list-item-img {
        margin-right: 20rpx;
        width: 180rpx;
        height: 180rpx;
        border-radius: 6rpx;
      }
      .atc-list-item-content {
        width: 446rpx;
        .atc-list-item-content-title {
          display: -webkit-box;
          -webkit-line-clamp: 1; /* 显示几行文本 */
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 20rpx;
          font-weight: 500;
          font-size: 28rpx;
          color: #333333;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
        }
        .atc-list-item-content-desc {
          display: -webkit-box;
          -webkit-line-clamp: 3; /* 显示几行文本 */
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 26rpx;
          color: #666666;
          line-height: 36rpx;
          text-align: justify;
          font-style: normal;
        }
      }
    }

    .list-footer {
      margin-top: 28rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .time,
      .opr {
        font-size: 24rpx;
        color: #666666;
        line-height: 34rpx;
        text-align: left;
        font-style: normal;
      }
    }
    .list-title {
      font-weight: 500;
      font-size: 28rpx;
      color: #555;
      line-height: 40rpx;
      text-align: left;
    }
  }
}
.tabs {
  display: flex;
  margin-top: 40rpx;
  justify-content: space-evenly;
  align-items: center;
  .tabs-title {
    font-size: 36rpx;
    color: #ff8383;
    line-height: 50rpx;
    text-align: center;
    font-style: normal;
  }
}
.active {
  color: #fff !important;
}
</style>
