<template>
  <view class="page">
    <view class="content">
      <view>
        <vh-navbar
          v-if="!$appStatusBarHeight"
          back-icon-color="#000"
          :title="title"
          title-color="#000"
          :titleWidth="400"
        >
          <view slot="right">
            <u-icon
              @click="addCollection"
              class="header-right-icon"
              :color="in_collection ? '#D60C0C' : '#999'"
              :name="in_collection ? 'star-fill' : 'star'"
              size="44"
            ></u-icon>
          </view>
        </vh-navbar>

        <!-- app（安卓、ios） -->
        <view v-else>
          <view class="p-fixed z-980 top-0 w-p100" :style="{ background: '#fff' }">
            <view :style="{ height: $appStatusBarHeight + 'px' }" />
            <view class="p-rela h-px-48 d-flex j-sa a-center">
              <view class="h-p100 d-flex a-center" @click="jump.navigateBack()">
                <u-icon name="nav-back" color="#000" :size="44" />
              </view>
              <view class="font-36 font-wei text-333333 details-title">{{ title }}</view>
              <u-icon
                @click="addCollection"
                class="header-right-icon"
                :color="in_collection ? '#D60C0C' : '#999'"
                :name="in_collection ? 'star-fill' : 'star'"
                size="44"
              ></u-icon>
            </view>
          </view>
        </view>
      </view>
      <!-- <u-navbar :immersive="true" :title="title" title-color="#000" :title-size="36" :title-width="400"> </u-navbar> -->
      <view
        class="course-detail-content"
        :style="{ paddingTop: !$appStatusBarHeight ? '0px' : parseInt($appStatusBarHeight) + 48 + 'px' }"
      >
        <view class="title">
          {{ detailTitle }}
        </view>
        <view class="desc">
          {{ desc }}
        </view>
        <view class="other">
          <view class="time">{{ time }}</view>
          <view class="author"> 作者：{{ author }} </view>
        </view>
        <view class="line"></view>
        <rich-text :nodes="articleContent"></rich-text>
      </view>
      <view class="tencent-video-box" v-if="vid">
        <div id="video-container" style="width: 100%; height: 200px"></div>
      </view>
    </view>
    <u-popup v-model="popShow" mode="bottom" border-radius="14" @close="closePop">
      <view style="padding-top: 10rpx">
        <answerItem :hiddenIndex="1" ref="answerItem" :info="info"> </answerItem>
      </view>
    </u-popup>
    <u-popup v-model="authShow" mode="center" :border-radius="10" closeable :mask-close-able="false">
      <view class="auth-box">
        <view class="auth-title"> 温馨提示 </view>
        <view class="auth-content">
          <view class="auth-content-1"> 请填写显示在认证证书上的姓名【】 </view>
          <view class="auth-content-2"> *不支持修改，请谨慎填写并仔细校对 </view>
          <u-input v-model="authName" :maxlength="14" input-align="center" placeholder="请输入姓名" :border="true" />
        </view>
        <view class="auth-line"></view>
        <view class="auth-footer">
          <view class="auth-cancel" @click="authShow = false">取消</view>
          <view class="footer-line"></view>
          <view class="auth-submit" @click="passAuth">确认</view>
        </view>
      </view>
    </u-popup>
    <view :class="{ 'bottom-div': true, show: showBottomDiv }" v-if="exam_status !== 1">
      <img
        v-if="question_total > 0"
        @click="goTest"
        style="width: 100%"
        src="https://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/atc-banner.png"
        alt=""
      />
    </view>
  </view>
</template>

<script>
import answerItem from '../../school-test/com/answer.vue'
export default {
  components: {
    answerItem,
  },
  data: () => ({
    showBottomDiv: false,
    authName: '',
    windowHeight: 0,
    authShow: false,
    isAuth: false,
    id: '',
    vid: '',
    articleContent: '',
    popShow: false,
    desc: '',
    paper_id: 0,
    exam_status: 0,
    info: {},
    in_collection: 0,
    detailTitle: '',
    question_total: 0,
    title: '',
    time: '',
    exam_id: '',
    appStatusBarHeight: 0,
    author: '',
  }),
  onLoad(option) {
    this.getappStatusBarHeight()

    this.id = option.id
  },
  onPageScroll(event) {
    const scrollTop = event.scrollTop
    const screenHeight = this.windowHeight
    const px = this.vid ? 100 : 300
    // 获取文档高度并处理

    this.getDocumentHeight().then((documentHeight) => {
      if (scrollTop + screenHeight >= documentHeight - px) {
        if (this.exam_status !== 1) {
          this.showBottomDiv = true
        }
      }
    })
  },
  onShow() {
    this.getDetails()
  },
  methods: {
    getDocumentHeight() {
      return new Promise((resolve) => {
        uni
          .createSelectorQuery()
          .select('.content')
          .boundingClientRect((data) => {
            resolve(data.height)
          })
          .exec()
      })
    },
    getappStatusBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
        },
      })
    },
    async addCollection() {
      const data = {
        chapter_id: this.id,
      }
      const res = await this.$u.api.addCollection(data)
      this.in_collection = !this.in_collection
      this.feedback.toast({
        title: '操作成功',
      })
    },
    async passAuth() {
      if (!this.authName) {
        return
      }
      const res = await this.$u.api.PostUserAuth({
        real_name: this.authName,
      })
      this.authShow = false
      setTimeout(() => {
        this.isAuth = true
        this.goTest()
      }, 300)
    },
    async goTest() {
      if (!this.isAuth) {
        this.authShow = true
      } else {
        if (this.question_total === 1) {
          let data = {
            exam_id: this.exam_id,
            paper_id: this.paper_id,
            action: 3,
          }
          const res = await this.$u.api.getExamQuestion(data)
          this.info = res.data
          this.popShow = true
        } else {
          // 跳转试卷
          this.jump.navigateTo(`${this.$routeTable.PIAnswerList}?id=${this.exam_id}&from=course`)
        }
      }
    },
    async closePop() {
      const data = {
        chapter_id: this.id,
      }
      const res = await this.$u.api.getChapterDetail(data)
      if (this.question_total === 1 && res.data.question_list.length) {
        this.info = res.data.question_list[0]
      }
    },
    async getDetails() {
      const data = {
        chapter_id: this.id,
      }
      const res = await this.$u.api.getChapterDetail(data)
      console.log(res.data)
      this.author = res.data.author
      this.title = res.data.title
      this.desc = res.data.subtitle
      this.articleContent = res.data.content
      this.detailTitle = res.data.title
      this.in_collection = res.data.in_collection
      this.time = res.data.time
      this.question_total = res.data.question_total
      this.exam_status = res.data.exam_status
      this.isAuth = !res.data.badge_status || res.data.is_auth
      this.exam_id = res.data.exam_id
      if (res.data.video_url) {
        this.vid = res.data.video_url
        const script = document.createElement('script')
        script.src = 'https://vm.gtimg.cn/tencentvideo/txp/js/txplayer.js'
        script.onload = () => {
          let player = new Txplayer({
            containerId: 'video-container',
            vid: this.vid, // 替换为你的视频ID
            autoplay: false,
            width: '100%',
            height: '200px',
          })
        }
        document.body.appendChild(script)
      }
      this.paper_id = res.data.paper_id ? res.data.paper_id : 0
      this.windowHeight = uni.getSystemInfoSync().windowHeight
      if (this.exam_status !== 1 && this.windowHeight <= 1000) {
        this.showBottomDiv = true
      }
    },
  },
}
</script>

<style scoped lang="scss">
.auth-box {
  padding-top: 40rpx;
  .auth-footer {
    // padding: 20rpx 0;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    .footer-line {
      width: 1rpx;
      height: 90rpx;
      background-color: #eee;
    }
    .auth-cancel {
      font-weight: 400;
      font-size: 30rpx;
      color: #999999;
      line-height: 42rpx;
      text-align: left;
      font-style: normal;
    }
    .auth-submit {
      font-weight: 500;
      font-size: 30rpx;
      color: #e80404;
      line-height: 42rpx;
      text-align: left;
      font-style: normal;
    }
  }
  .auth-content {
    padding: 0 36rpx;
    margin-top: 32rpx;
    .auth-content-1 {
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
      margin-bottom: 10rpx;
      text-align: center;
      font-style: normal;
    }
    .auth-content-2 {
      font-family: PingFangSC;
      font-size: 22rpx;
      color: #e80404;
      line-height: 32rpx;
      margin-bottom: 28rpx;
      text-align: center;
      font-style: normal;
    }
  }
  .auth-line {
    width: 100%;
    background-color: #eee;
    margin-top: 48rpx;
    height: 1rpx;
  }
  .auth-title {
    font-weight: 600;
    font-size: 30rpx;
    color: #333333;
    line-height: 42rpx;
    text-align: center;
    font-style: normal;
  }
}
.tencent-video-box {
  padding: 20rpx;
  width: 100%;
  margin-bottom: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.page {
  position: relative;
}
.content {
  padding-bottom: 200rpx;
  position: relative;
}
.bottom-div {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 9999;
  bottom: -116px; /* 初始位置，隐藏在页面底部 */
  background-color: #fff;
  padding: 16rpx 20rpx 10rpx 20rpx;
  border-top: 1px solid #ddd;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  transition: bottom 0.51s ease;
}
.bottom-div.show {
  bottom: 0; /* 显示位置 */
}
.header-right-icon {
  margin-right: 14rpx;
  margin-top: 6rpx;
}

.course-detail-content {
  padding: 20rpx 32rpx 32rpx 32rpx;

  .line {
    width: 100%;
    margin: 24rpx 0;
    height: 2rpx;
    background: #eeeeee;
  }

  .other {
    margin-top: 16rpx;
    display: flex;
    color: #666666;
    font-size: 24rpx;
    justify-content: space-between;
    align-items: center;
  }

  .desc {
    font-size: 26rpx;
    color: #666666;
    line-height: 36rpx;
    text-align: justify;
    margin-top: 16rpx;
  }

  .title {
    font-weight: 500;
    font-size: 32rpx;

    color: #333333;
    line-height: 44rpx;
    text-align: left;
  }

  img {
    width: 100%;
  }
}
.details-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 260px;
  text-align: center;
}
</style>
