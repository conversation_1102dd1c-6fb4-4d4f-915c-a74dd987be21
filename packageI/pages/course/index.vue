<template>
  <view class="course-body">
    <view class="course-header">
      <view class="">
        <!-- 微信小程序、h5导航栏 -->
        <vh-navbar
          v-if="!$appStatusBarHeight"
          back-icon-color="#fff"
          title=""
          title-color="#fff"
          :background="background"
        />

        <!-- app（安卓、ios） -->
        <view v-else>
          <view class="p-fixed z-980 top-0 w-p100" :style="{ background: background.backgroundColor }">
            <view :style="{ height: $appStatusBarHeight + 'px' }" />
            <view class="p-rela h-px-48 d-flex j-center a-center">
              <view class="p-abso left-24 h-p100 d-flex a-center" @click="gotoBack()">
                <u-icon name="nav-back" color="#fff" :size="44" />
              </view>
              <view class="font-36 font-wei text-333333"></view>
            </view>
          </view>
        </view>
      </view>
      <!-- <u-navbar back-icon-color="#fff" :custom-back="gotoBack" :background="background"></u-navbar> -->
      <view
        class="course-header-content"
        :style="{ paddingTop: !$appStatusBarHeight ? '0px' : parseInt($appStatusBarHeight) + 48 + 'px' }"
      >
        <img
          class="img"
          :style="{ top: !$appStatusBarHeight ? '0px' : parseInt($appStatusBarHeight) + 48 + 'px' }"
          :src="ossIcon('/study/course-list-header.png')"
        />
        <view>
          <view class="title">
            {{ title }}
          </view>
          <view class="cate"> 课程目录 </view>
        </view>
      </view>
    </view>
    <view class="course-list-content">
      <view class="course-list-item" v-for="(item, index) in list" :key="index" @click="goDetails(item)">
        <view class="course-list-item-image">
          <img :src="item.cover_img" alt="" />
        </view>
        <view class="course-list-item-content">
          <view class="course-list-item-title">{{ item.title }}</view>
          <view class="course-list-item-desc">{{ item.subtitle }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  onLoad(options) {
    this.topic_id = options.topic_id
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
      },
    })
  },
  computed: {
    ...mapState(['routeTable']),
    dynamicStyle() {
      // 计算 marginTop 样式
      if (!this.appStatusBarHeight) {
        return {
          marginTop: 20 + 'px',
        }
      } else {
        return {
          marginTop: this.appStatusBarHeight + 68 + 'px',
        }
      }
    },
  },
  onShow() {
    this.getChapterList()
  },
  data: () => ({
    list: [],
    topic_id: '',
    title: '',
    appStatusBarHeight: 0, //状态栏高度
    background: {
      backgroundColor: '#BD0000',
    },
  }),
  methods: {
    gotoBack() {
      //   if (this.appStatusBarHeight) {
      //     //判断是否从App过来 1 = 安卓 2 = ios
      //     wineYunJsBridge.openAppPage({
      //       client_path: { ios_path: 'goBack', android_path: 'goBack' },
      //     })
      //   } else {
      //h5端
      this.jump.navigateBack()
      //   }
    },
    goDetails(row) {
      this.jump.navigateTo(`${this.$routeTable.PICourseDetails}?id=${row.id}`)
    },
    async getChapterList() {
      const data = {
        limit: 999,
        page: 1,
        topic_id: this.topic_id,
      }
      const res = await this.$u.api.getChapterList(data)
      this.title = res.data.title
      this.list = res.data.list
    },
  },
}
</script>

<style lang="scss" scoped>
.course-body {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.course-list-content {
  margin-top: 110rpx;
  display: flex;

  align-items: center;
  flex-direction: column;
  .course-list-item {
    border-radius: 10rpx;
    margin: 20rpx 0;
    height: 220rpx;
    background-color: #fff;
    width: 686rpx;
    display: flex;
    align-items: center;
    padding: 20rpx;

    .course-list-item-content {
      display: flex;
      flex-direction: column;
      //   justify-content: space-between;

      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -moz-box-orient: vertical;
      word-wrap: break-word;
      word-break: break-all;
      white-space: normal;
      height: 90px;
      overflow: hidden;

      .course-list-item-desc {
        margin-top: 12rpx;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 24rpx;
        color: #666666;
        line-height: 24rpx;
        text-align: justify;
      }
      .course-list-item-title {
        font-size: 28rpx;
        color: #333333;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: bold;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
      }
    }
    .course-list-item-image {
      img {
        width: 180rpx;
        height: 180rpx;
        margin-right: 20rpx;
        border-radius: 6rpx;
      }
    }
  }
}

.course-header {
  .course-header-content {
    z-index: 0;
    position: relative;

    .img {
      z-index: -1;
      width: 100%;

      position: absolute;
    }

    .title {
      font-weight: 600;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      font-size: 50rpx;
      color: #ffffff;
      padding: 40rpx 40rpx 20rpx 40rpx;
      line-height: 80rpx;
      text-align: center;
      font-style: normal;
    }

    .cate {
      font-weight: 500;
      font-size: 32rpx;
      color: #ffffff;
      line-height: 44rpx;
      text-align: center;
      font-style: normal;
    }
  }
}
</style>
