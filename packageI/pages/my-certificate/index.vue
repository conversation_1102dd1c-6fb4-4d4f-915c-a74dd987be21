<template>
  <view class="test-body">
    <view>
      <vh-navbar
        v-if="!$appStatusBarHeight"
        :background="background"
        title="我的证书"
        back-icon-color="#fff"
        title-color="#fff"
        :titleWidth="400"
      >
      </vh-navbar>

      <!-- app（安卓、ios） -->
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: '#D60C0C' }">
          <view :style="{ height: $appStatusBarHeight + 'px' }" />
          <view class="p-rela h-px-48 d-flex j-center a-center">
            <view class="p-abso left-24 h-p100 d-flex a-center" @click="jump.navigateBack()">
              <u-icon name="nav-back" color="#fff" :size="44" />
            </view>
            <view class="font-36 font-wei text-ffffff">我的证书</view>
          </view>
        </view>
      </view>
    </view>
    <view
      class="content"
      v-if="list.length"
      :style="{ paddingTop: !$appStatusBarHeight ? '10px' : parseInt($appStatusBarHeight) + 58 + 'px' }"
    >
      <view class="content-item" v-for="(item, index) in list" :key="index" @click="goDetails(item)">
        <image style="width: 100%; border-radius: 10rpx" :src="item.picture" alt="" />
        <img
          v-if="item.is_get"
          class="authentication"
          src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/authentication-icon.png"
          alt=""
        />
        <view class="mask" v-else>
          <img
            class="lock"
            src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/lock-auth.png"
            alt=""
          />
          <view class="mask-text">去认证</view>
        </view>
        <view class="content-bottom">
          <view class="cert-title"> {{ item.title }} </view>
          <view class="content-bottom-footer">
            <img
              class="cert-icon"
              src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/cert-icon.png"
              alt=""
            />

            <view class="cert-text">{{ item.user_get }}人已获取</view>
          </view>
        </view>
      </view>
    </view>
    <view class="empty" v-else>
      <view class="box">
        <img src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/empty.png" alt="" />
        <view class="empty-text"> 目前还没有任何证书 </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  computed: {
    ...mapState(['routeTable']),
  },
  data: () => ({
    list: [],
    background: {
      backgroundColor: '#D30808',
    },
    customStyle: {
      width: '130rpx',
    },
    appStatusBarHeight: 0,
  }),
  onShow() {
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
      },
    })
    this.getCertList()
    this.getStatusBarHeight()
  },
  methods: {
    goDetails(row) {
      console.log(row, this.$routeTable.PICertDetails)
      this.jump.navigateTo(`${this.$routeTable.PICertDetails}?id=${row.id}`)
    },
    getStatusBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight
        },
      })
    },
    gotoBack() {
      if (this.comes.isFromApp(this.from)) {
        //判断是否从App过来 1 = 安卓 2 = ios
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'goBack', android_path: 'goBack' },
        })
      } else {
        //h5端
        this.jump.navigateBack()
      }
    },
    async getCertList() {
      const data = {
        limit: 999,
        page: 1,
      }
      const res = await this.$u.api.getBadgeList(data)
      this.list = res.data.list
    },
  },
}
</script>
<style scoped lang="scss">
.header {
  position: fixed;
  z-index: 111;
  background-color: #f5f5f5;
  top: 0;
  width: 100%;
  left: 0;

  .title {
    position: absolute;
    top: 90rpx;
    left: 45.4%;
    font-weight: 600;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;

    z-index: 111;
  }

  .icon {
    position: absolute;
    top: 48rpx;
    left: 14rpx;
    z-index: 111;
  }

  img {
    width: 100%;
  }
}
.empty {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  .box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .empty-text {
    font-size: 24rpx;
    color: #999999;
    line-height: 34rpx;
    margin-top: 20rpx;
    text-align: center;
    font-style: normal;
  }
}
.test-body {
  min-height: 100vh;
  background-color: #f5f5f5;

  .content {
    // padding: 0 0rpx 22rpx 0rpx;
    padding: 10rpx;
    display: grid;
    grid-template-columns: 1fr 1fr;

    .content-item {
      margin: 0 13rpx 20rpx 13rpx;
      //   margin-bottom: 20rpx;
      background-color: #fff;
      position: relative;
      //   margin: 0 13rpx 20rpx 0;
      .mask {
        position: absolute;
        width: 100%;
        top: 0;
        border-radius: 10rpx;
        height: 100%;
        background-color: rgba($color: #000000, $alpha: 0.6);

        .mask-text {
          background: #e80404;
          font-size: 24rpx;
          position: absolute;
          top: 40%;
          left: 29%;
          color: #ffffff;
          border-radius: 25rpx;
          height: 50rpx;
          line-height: 50rpx;
          text-align: center;
          width: 150rpx;
        }
        .lock {
          width: 50rpx;
          height: 50rpx;
          float: right;
          margin-top: 20rpx;
          margin-right: 20rpx;
        }
      }
      .authentication {
        position: absolute;
        top: 0;
        right: 0;
        // z-index: 1;
        width: 80rpx;
        height: 80rpx;
      }
      .content-bottom {
        height: 132rpx;
        display: flex;
        flex-direction: column;
        border-radius: 0 0 10rpx 10rpx; /* 顺时针，从左上开始 */

        padding: 10rpx 20rpx 20rpx 20rpx;
        justify-content: center;
        .cert-title {
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          font-weight: bold;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
          height: 40rpx;
          font-family: PingFangSC, PingFang SC;
          font-size: 26rpx;
          margin-bottom: 10rpx;
          color: #333333;
          text-align: left;
          font-style: normal;
        }
        .content-bottom-footer {
          display: flex;
          align-items: center;
          .cert-text {
            color: #666666;
            font-size: 24rpx;
          }
          .cert-icon {
            margin-right: 10rpx;
            width: 30rpx;
            height: 30rpx;
          }
        }
      }
    }
  }
}
::v-deep .uni-button:after,
.u-hairline-border:after {
  height: 0;
  width: 0;
}
::v-deep .cont-test {
  border: 1px solid #d30808;
  width: 130rpx !important;
  background-color: #fff !important;
  border-radius: 1000px;
}
</style>
