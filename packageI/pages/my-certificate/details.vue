<template>
  <view class="test-body">
    <view>
      <vh-navbar
        v-if="!$appStatusBarHeight"
        :background="background"
        title="证书详情"
        back-icon-color="#fff"
        title-color="#fff"
        :titleWidth="400"
      >
      </vh-navbar>

      <!-- app（安卓、ios） -->
      <view v-else>
        <view class="p-fixed z-980 top-0 w-p100" :style="{ background: '#D60C0C' }">
          <view :style="{ height: $appStatusBarHeight + 'px' }" />
          <view class="p-rela h-px-48 d-flex j-center a-center">
            <view class="p-abso left-24 h-p100 d-flex a-center" @click="jump.navigateBack()">
              <u-icon name="nav-back" color="#fff" :size="44" />
            </view>
            <view class="font-36 font-wei text-ffffff">证书详情</view>
          </view>
        </view>
      </view>
    </view>
    <view
      class="content"
      :style="{ paddingTop: !$appStatusBarHeight ? '10px' : parseInt($appStatusBarHeight) + 58 + 'px' }"
    >
      <view v-if="!is_get">
        <view class="not-get-box">
          <img class="cert-template" :src="certUrl" alt="" />
          <view class="mask">
            <img
              class="cert-template-lock"
              src="http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/study/cert-template-lock.png"
              alt=""
            />
          </view>
        </view>
        <view class="type-items">
          <view class="items-list">
            <view class="items-first-title"> 获得条件 </view>
            <view class="items-value"
              >完成学习后，测试达到<text style="color: #e80404">{{ badge_score }}分</text>以上</view
            >
          </view>
          <view class="items-first"></view>
          <view class="items-list">
            <view class="items-first-title"> 相关课题 </view>
            <view class="items-value">{{ chapter_title }}</view>
          </view>
        </view>
      </view>

      <view v-else>
        <view class="not-get-box">
          <img class="cert-template" :src="certUrl" alt="" />
        </view>
        <view class="type-items">
          <view class="items-list">
            <view class="items-first-title"> 颁发时间 </view>
            <view class="items-value">{{ create_time }}</view>
          </view>
          <view class="items-first"></view>
          <view class="items-list">
            <view class="items-first-title"> 测试分数 </view>
            <view class="items-value">{{ score }}</view>
          </view>
          <view class="items-first"></view>
          <view class="items-list">
            <view class="items-first-title"> 相关课题 </view>
            <view class="items-value" @click="viewDetails"
              >{{ chapter_title }}

              <view class="view">去看看</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="cert-footer">
      <view class="cert-footer-text" @click="h5Download" v-if="is_get">下载证书</view>
      <view class="cert-footer-text" @click="viewDetails" v-else>学习课程</view>
      <!-- <view class="cert-footer-text" @click="h5Download" v-else>学习课程</view> -->
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  computed: {
    ...mapState(['routeTable']),
  },
  data: () => ({
    id: '',
    is_get: 0,
    chapter_title: '',
    certUrl: '',
    create_time: '',
    chapter_id: '',
    badge_score: 0,
    score: 0,
    background: {
      backgroundColor: '#D30808',
    },
    customStyle: {
      width: '130rpx',
    },
    appStatusBarHeight: 0,
  }),
  onLoad(options) {
    this.id = options.id
  },
  onShow() {
    uni.getSystemInfo({
      success: (res) => {
        this.appStatusBarHeight = res.statusBarHeight ? res.statusBarHeight : 48
      },
    })
    this.getCertDetails()
    this.getStatusBarHeight()
  },
  methods: {
    viewDetails() {
      this.jump.navigateTo(`${this.$routeTable.PICourseDetails}?id=${this.chapter_id}`)
    },
    getStatusBarHeight() {
      uni.getSystemInfo({
        success: (res) => {
          this.statusBarHeight = res.statusBarHeight
        },
      })
    },
    h5Download() {
      console.log(this.$app)
      if (!this.$appStatusBarHeight) {
        const link = document.createElement('a')
        link.href = this.certUrl
        link.download = 'image.jpg' // 设置下载文件名
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else {
        this.downLoadCert()
      }
    },
    downLoadCert() {
      const url = this.certUrl
      wineYunJsBridge.openAppPage({
        client_path: { ios_path: 'downloadFile', android_path: 'downloadFile' },
        ad_path_param: [
          { ios_key: 'url', ios_val: url, android_key: 'url', android_val: url },
          { android_key: 'suffix', android_val: 'png' },
        ],
      })
    },
    gotoBack() {
      if (this.comes.isFromApp(this.from)) {
        //判断是否从App过来 1 = 安卓 2 = ios
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'goBack', android_path: 'goBack' },
        })
      } else {
        //h5端
        this.jump.navigateBack()
      }
    },
    async getCertDetails() {
      const data = {
        badge_id: this.id,
      }
      const res = await this.$u.api.getBadgeDetails(data)
      this.is_get = res.data.is_get
      this.badge_score = res.data.badge_score
      this.create_time = res.data.create_time
      this.chapter_title = res.data.chapter_title
      this.score = res.data.score
      this.certUrl = res.data.url
      this.chapter_id = res.data.chapter_id
    },
  },
}
</script>
<style scoped lang="scss">
.cert-footer {
  position: fixed;
  bottom: 0;
  z-index: 11111;
  height: 120rpx;
  box-shadow: 0rpx 2rpx 12rpx 0rpx rgba(0, 0, 0, 0.22);
  width: 100%;
  background-color: #fff;
  .cert-footer-text {
    margin-top: 24rpx;
    background: #e80404;
    margin-left: 5%;
    text-align: center;
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90%;
    border-radius: 36rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #ffffff;
    line-height: 40rpx;
    text-align: center;
    font-style: normal;
  }
}
.header {
  position: fixed;
  z-index: 111111;
  background-color: #f5f5f5;
  top: 0;
  width: 100%;
  left: 0;

  .title {
    position: absolute;
    top: 90rpx;
    left: 45.4%;
    font-weight: 600;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;

    z-index: 111;
  }

  .icon {
    position: absolute;
    top: 48rpx;
    left: 14rpx;
    z-index: 111;
  }

  img {
    width: 100%;
  }
}
.empty {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  .box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .empty-text {
    font-size: 24rpx;
    color: #999999;
    line-height: 34rpx;
    margin-top: 20rpx;
    text-align: center;
    font-style: normal;
  }
}
.test-body {
  min-height: 100vh;
  background-color: #f5f5f5;

  .content {
    // padding: 0 0rpx 22rpx 0rpx;
    padding: 32rpx;
    padding-bottom: 152rpx;
    .type-items {
      margin-top: 32rpx;
      background-color: #fff;
      border-radius: 10rpx;
      padding: 0 24rpx;
      width: 100%;
      .items-first {
        width: 100%;
        height: 2rpx;
        background-color: #f5f5f5;
      }
      .items-list {
        display: flex;
        padding: 24rpx 0;
        align-items: center;
        .items-first-title {
          word-break: keep-all;
          font-size: 28rpx;
          color: #666666;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
        }
        .items-value {
          font-weight: 500;
          font-size: 28rpx;
          color: #333333;
          line-height: 2;
          margin-left: 48rpx;
          text-align: left;
          .view {
            // width: 98rpx;
            // height: 34rpx;
            font-size: 24rpx;
            display: inline-block;
            padding: 2rpx 22rpx;
            border-radius: 100rpx;
            margin-left: 10rpx;
            border: 2rpx solid #e80404;
            color: #e80404;
            line-height: 32rpx;
            text-align: center;
            font-style: normal;
          }
        }
      }
    }
    .not-get-box {
      position: relative;
      .cert-template {
        width: 100%;
      }

      .mask {
        background-color: rgba($color: #000000, $alpha: 0.7);
        z-index: 11;
        position: absolute;
        width: 100%;
        top: 0;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        .cert-template-lock {
          width: 110rpx;
          height: 110rpx;
        }
      }
    }
  }
}
::v-deep .uni-button:after,
.u-hairline-border:after {
  height: 0;
  width: 0;
}
::v-deep .cont-test {
  border: 1px solid #d30808;
  width: 130rpx !important;
  background-color: #fff !important;
  border-radius: 1000px;
}
</style>
