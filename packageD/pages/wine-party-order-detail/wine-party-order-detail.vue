<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		
		<!-- 有数据 -->
		<view v-if="!loading" class="fade-in bg-f5f5f5">
			<!-- 导航栏 -->
			<u-navbar back-icon-color="#FFF" title="订单详情" title-size="36" :title-bold="true" title-color="#FFF" :background="{ background: navBackgroundColor }" />
			
			<!-- banner -->
			<image class="p-abso z-0 top-0 w-p100 h-400" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_order_detail/bg.png" mode="aspectFill" />
			
			<!-- 退款信息（status = 5时需要显示） -->
			<view v-if="winePartyOrderInfo.status === 5" class="p-rela z-01 bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="d-flex">
						<image class="w-44 h-44" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_order_detail/ref_bla.png" mode="widthFix"></image>
						<view class="ml-08">
							<view class="font-32 font-wei text-3">已退款</view>
							<view class="mt-12 font-24 text-9 l-h-34">{{winePartyOrderInfo.refund_end_time}}</view>
						</view>
					</view>
				</view>
				
				<view class="pt-32 pb-32 ml-60">
					<view class="font-28 text-3">退款金额：<text class="text-e80404">¥{{winePartyOrderInfo.refund_amount}}</text></view>
					<view class="mt-12 font-28 text-3">退款账户：{{getRefundAccount}}</view>
				</view>
			</view>
			
			<!-- 拼团信息 -->
			<view v-if="Object.keys(groupInfo).length && groupInfo.group_status == 1 || groupInfo.group_status == 3" class="p-rela z-01 bg-ffffff b-rad-10 d-flex flex-column a-center j-center mt-20 ml-24 mr-24 pt-32 pb-32">
				<view v-if="groupInfo.group_status == 1 && groupInfo.remaining_time > 0" class="d-flex">
					<view class="font-28 text-9">拼团中，还差<text class="text-e80404 font-wei">{{groupInfo.group_last_num}}人</text>可拼成</view>
					<view class="d-flex">
						<vh-count-down :show-days="false" :has-day-margin-right="false" :has-separator-distance="false" 
						:timestamp="groupInfo.remaining_time" bg-color="transparent" :all-font-bold="true" color="#333" separator-color="#333"
						@end="groupInfo.remaining_time = 0" />
						<view class="font-28 text-9">后结束</view>
					</view>
				</view>
				<view v-if="groupInfo.remaining_time == 0" class="font-28 font-wei text-3">哎呀，拼团时间已过，人数不够！</view>
				
				<view class="w-648 d-flex a-center mt-40 o-scr-x" :class="groupInfo.user_head_img.length > 5 ? '' : 'j-center'">
					<view class="p-rela w-112 w-112 bg-ffffff d-flex j-center a-center b-rad-p50 mr-n-20" :style="{zIndex : groupInfo.user_head_img.length - index }" v-for="(item, index) in groupInfo.user_head_img" :key="index">
						<image class="w-108 h-108 b-rad-p50" :src="item" mode="aspectFill" />
						<view v-if="index == 0" class="p-abso z-03 bottom-0 w-110 d-flex j-center">
							<text class="bg-ff9127 b-rad-16 b-s-02-ffffff ptb-02-plr-16 font-20 text-ffffff">团长</text>
						</view>
					</view>
					
					<view class="p-rela w-112 w-112 bg-ffffff d-flex j-center a-center b-rad-p50 ml-54">
						<image class="w-108 h-108 b-rad-p50" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_order_detail/que_mark.png" mode="aspectFill" />
					</view>
				</view>
				
				<view class="mt-50 d-flex j-center a-center">
					<view v-if="groupInfo.group_status == 1 && groupInfo.remaining_time > 0" class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'546rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" open-type="share">邀请好友拼团</u-button>
					</view>
					<view v-if="groupInfo.remaining_time == 0" class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'546rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}"
						@click="feedback.toast({title: '哎呀，拼团时间已过，人数不够！'})">邀请好友拼团</u-button>
					</view>
				</view>
				
				<view class="mt-32 font-24 text-9">拼团时长超出24小时会被取消</view>
			</view>
			
			<!-- 核销 -->
			<!-- status 状态：0-待使用 1-已使用 2-待拼团 3-已超时 4-已过期 5-已退费 -->
			<!-- status 不等于2 && 不等于5 -->
			<view v-if="winePartyOrderInfo.status !== 2 && winePartyOrderInfo.status !== 5" class="p-rela z-01 bg-ffffff d-flex b-rad-10 mt-20 mr-24 ml-24 p-40">
				<view v-if="winePartyOrderInfo.review_status !== 1 && winePartyOrderInfo.status == 0" @click="showCodeMask = true">
					<vh-image :loading-type="2" :src="writeOffCode" :width="160" :height="160" />
				</view>
				
				<image v-else class="w-160 h-160" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_order_detail/cod_inv.png" mode="aspectFill"></image>
				
				<view class="d-flex flex-column j-center ml-32">
					<view class="font-40 font-wei text-3 l-h-56">核销码</view>
					<view class="mt-32 font-36 text-6">{{winePartyOrderInfo.entry_code}}</view>
				</view>
			</view>
			
			<!-- 酒会信息 -->
			<view class="bg-ffffff d-flex b-rad-10 mt-20 ml-24 mr-24 ptb-28-plr-20">
				<view class="w-316 h-160 b-rad-10 o-hid">
					<vh-image :loading-type="2" :src="winePartyOrderInfo.thumb_image" :height="160" />
				</view>
				<view class="flex-1 d-flex flex-column j-sb ml-20">
					<view class="font-28 text-3 l-h-40 text-hidden-2">{{winePartyOrderInfo.title}}</view>
					<view class="d-flex j-sb a-center">
						<text class="font-20 text-6">x1</text>
						<text class="font-28 text-3">¥{{winePartyOrderInfo.payment_amount}}</text>
					</view>
				</view>
			</view>
			
			<!-- 活动时间 + 活动地址 -->
			<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
				<!-- 活动时间 -->
				<view class="d-flex pt-46 pr-24 pb-32 pl-24">
					<image class="w-28 h-32 mt-04" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_order_detail/time.png" mode="widthFix" />
					
					<view class="flex-1 ml-12">
						<view class="font-28 font-wei text-3">活动时间</view>
						<view class="mt-08 font-24 text-9">{{winePartyOrderInfo.activity_time}}</view>
					</view>
				</view>
				
				<!-- 活动地址 -->
				<view class="d-flex bt-s-01-eeeeee pt-32 pr-24 pb-40 pl-24">
					<image class="w-28 h-32" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_order_detail/add.png" mode="widthFix" />
					
					<view class="flex-1 ml-12">
						<view class="font-28 font-wei text-3">活动地址</view>
						<view class="d-flex j-sb mt-08">
							<view class="font-24 text-9">{{winePartyOrderInfo.address}}</view>
							<view class="d-flex a-center" @click="gps.openGps(winePartyOrderInfo.latitude, winePartyOrderInfo.longitude, winePartyOrderInfo.address)">
								<view class="w-02 h-40 bg-e7e7e7"></view>
								<image class="w-32 h-32 ml-32" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_order_detail/gps.png" mode="aspectFill" />
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 订单明细 -->
			<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<text class="font-28 font-wei text-3">订单编号</text>
					<text class="font-24 text-3" @click.stop="copy.copyText( winePartyOrderInfo.main_order_no )">{{winePartyOrderInfo.main_order_no}}</text>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<text class="font-28 font-wei text-3">支付时间</text>
					<text class="font-24 text-3">{{winePartyOrderInfo.payment_time}}</text>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<text class="font-28 font-wei text-3">支付方式</text>
					<text v-if="winePartyOrderInfo.payment_method == 0" class="font-24 text-3">支付宝APP</text>
					<text v-else-if="winePartyOrderInfo.payment_method == 1" class="font-24 text-3">支付宝H5</text>
					<text v-else-if="winePartyOrderInfo.payment_method == 2" class="font-24 text-3">支付宝PC</text>
					<text v-else-if="winePartyOrderInfo.payment_method == 3" class="font-24 text-3">微信APP</text>
					<text v-else-if="winePartyOrderInfo.payment_method == 4" class="font-24 text-3">微信小程序</text>
					<text v-else-if="winePartyOrderInfo.payment_method == 5" class="font-24 text-3">微信H5</text>
					<text v-else-if="winePartyOrderInfo.payment_method == 6" class="font-24 text-3">抖音支付宝</text>
					<text v-else-if="winePartyOrderInfo.payment_method == 7" class="font-24 text-3">微信JSAPI(公众号支付)</text>
					<text v-else-if="winePartyOrderInfo.payment_method == 8" class="font-24 text-3">抖音微信</text>
					<text v-else-if="winePartyOrderInfo.payment_method == 201" class="font-24 text-3">兔头</text>
					<text v-else-if="winePartyOrderInfo.payment_method == 202" class="font-24 text-3">礼品卡</text>
					<text v-else class="font-24 text-3">未知</text>
				</view>


				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32">
					<text class="font-28 font-wei text-3">优惠券</text>
					<view class="font-24 font-wei text-e80404">-¥{{ winePartyOrderInfo.coupon_value }}</view>
				</view>
			</view>
			
			<!-- 猜你喜欢 -->
			<view class="">
				<!-- 分割线 -->
				<vh-split-line :padding-top="52" :padding-bottom="32" :margin-left="10" :margin-right="10" text="猜你喜欢" :font-bold="true" :font-size="36" text-color="#333333"
				:show-image="true" image-src="https://images.vinehoo.com/vinehoomini/v3/comm/guess_love.png" />
					
				<!-- 猜你喜欢列表 -->
				<vh-goods-recommend-list />
			</view>
		    
			<!-- 底部按钮 -->
			<!-- <view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022">
				<view class="h-104 d-flex j-end a-center pr-24">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#999', border:'1rpx solid #999'}">取消订单</u-button>
				</view>
			</view> -->
		    
			<!-- 弹框 -->
			<view class="">
				<u-mask :show="showCodeMask" @click="showCodeMask = false">
					<view class="h-p100 d-flex j-center a-center">
						<view class="code-bg w-530 h-750">
							<view class="w-530 h-580 d-flex flex-column j-center a-center">
								<!-- <canvas style="width: 150px; height: 150px;" canvas-id="winePartyOrderMaskQrcode"></canvas> -->
								<vh-image :loading-type="2" :src="writeOffCode" :width="300" :height="300" />
								<text class="mt-40 font-36 font-wei text-3">{{winePartyOrderInfo.entry_code}}</text>
							</view>
							<view class="w-530 h-150 d-flex j-center a-center font-40 font-wei text-3">未使用</view>
						</view>
					</view>
				</u-mask>
			</view>
		</view>
		
		<!-- 骨架屏 -->
		<view v-else class="fade-in">
			<u-navbar back-icon-color="#FFF" title="订单详情" title-size="36" :title-bold="true" title-color="#FFF" :background="{ background: '#E80404' }" />
			<vh-skeleton :type="3" loading-color="#E80404" />
		</view>
	</view>
</template>

<script>
	export default {
		name: 'wine-party-order-detail',
		
		data() {
			return {
				loading: true, //加载状态 true = 加载中、false = 结束加载
				navBackgroundColor:'rgba(224, 20, 31, 0)', //导航栏背景
				orderNo:'', //订单编号
				winePartyOrderInfo: {}, //订单详情信息
				groupInfo:{}, //拼团信息
				writeOffCode:'', //核销码
				showCodeMask: false, //是否展示核销码遮罩层
			}
		},
		
		computed: {
			// 获取退款账户
			getRefundAccount() {
				let { payment_method } = this.winePartyOrderInfo //酒会订单信息
				let refundAccount = '' //退款账户
				switch( payment_method ) {
					case 0:
					case 1:
					case 2:
					case 6:
					refundAccount = '支付宝'
					break
					case 3:
					case 4:
					case 5:
					case 7:
					case 8:
					refundAccount = '微信'
					break
					default:
					refundAccount = '其他'
				}
				return refundAccount
			}
		},
		
		onLoad(options) {
			this.orderNo = options.orderNo
			this.init()
		},
		
		methods: {
			// 初始化
			async init() {
				await this.getWinePartyOrderInfo()
				await this.getWriteOffCode()
				this.loading = false
			},
			
			// 获取酒会订单详情
			async getWinePartyOrderInfo() {
				let res = await this.$u.api.winePartyOrderDetail({ main_order_no: this.orderNo })
				this.winePartyOrderInfo = res.data
				this.winePartyOrderInfo.groupInfo ? this.groupInfo  = this.winePartyOrderInfo.groupInfo : this.groupInfo = {}
			},
			
			// 获取核销码
			async getWriteOffCode() { 
				let { review_status, status } = this.winePartyOrderInfo
				if(review_status !== 1 && status == 0) {
					let res = await this.$u.api.winePartyWriteOffCode({code: this.winePartyOrderInfo.entry_code})
					this.writeOffCode = res.data
				}
			},
		},
		
		onShareAppMessage(res) {
			if(this.groupInfo.group_status == 1) { 
				const { party_id, thumb_image, title } = this.winePartyOrderInfo //酒会订单信息
				const { group_id, package_id } = this.groupInfo //拼团信息
				
				console.log(party_id, thumb_image, title, group_id, package_id)
				let path = `/pages/wine-party-detail/wine-party-detail?id=${party_id}&gid=${group_id}&package_id=${package_id}`
				console.log(path)
				return { //按钮分享
				  title,
				  path,
				  imageUrl: thumb_image,
				}
			}else {
				return {
				  title: '酒云网 与百万发烧友一起淘酒aaa',
				  path: '/pages/index/index',
				}
			}
		},
		
		onPageScroll(res) {
			res.scrollTop <= 100 ? 
			this.navBackgroundColor = `rgba(224, 20, 31, ${res.scrollTop/100})` : 
			this.navBackgroundColor = `rgba(224, 20, 31, 1)`
		}
	}
</script>

<style scoped>
	.code-bg {
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/wine_party_order_detail/code_bg.png);
		background-size: cover;
	}
</style>
