<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<view class="">
			<!-- 微信小程序、h5导航栏 -->
			<view v-if="from == ''" class="fade-in">
				<u-navbar :is-back="false" back-icon-color="#333" title="酒会详情" :title-bold="true" title-color="#333">
					<view class="pt-12 pr-12 pb-12 pl-24" @click="jumpBack()">
						<u-icon :name="pageLength > 1 ? 'nav-back' : 'home'" color="#333" :size="44" />
					</view>
					<!-- <view v-if="!loading" slot="right" class="d-flex a-center">
						<button class="nav-sha-btn w-42 h-42 mr-24" open-type="share" />
					</view> -->
				</u-navbar>
			</view>
			
			<!-- app（安卓、ios） -->
			<view v-else>
				<view class="p-fixed z-980 top-0 w-p100 bg-ffffff">
					<view :style="{ height: appStatusBarHeight + 'px'}" />
					<view class="p-rela h-px-48 d-flex j-sb a-center">
						<view class="h-p100 d-flex a-center ml-24" @click="jumpBack()">
							<u-icon name="nav-back" color="#333" :size="44" />
						</view>
						<view class="font-36 font-wei text-3">酒会详情</view>
						<view  class="w-68"> 
							<view v-if="this.$vhFrom != 'next'" class="h-p100 d-flex j-center a-center" @click="jump.appShare({title: winePartyInfo.title, des: winePartyInfo.title, img: winePartyInfo.thumb_image, path:`${routeTable.pDWinePartyDetail}?id=${winePartyInfo.id}`})">
							<image class="h-p100 mr-24 w-44 h-44" src="https://images.vinehoo.com/vinehoomini/v3/comm/sha_black.png" mode="widthFix" />
						</view>
						</view>
						
					</view>
				</view>
			</view>
		</view>
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="">
			<!-- banner -->
			<view class="pl-40 pr-40 pb-32" :style="{ paddingTop: from == '' ? '12rpx' : parseInt(appStatusBarHeight) + 54 + 'px' }">
				<view class="d-flex flex-column j-center a-center">
					<view class="w-580 h-18 bg-dedede b-tl-tr-rad-06 op-015" />
					<view class="w-620 h-18 bg-dedede b-tl-tr-rad-06 op-040" />
					<view class="w-670 b-rad-10 o-hid">
						<vh-image :loading-type="2" :src="winePartyInfo.thumb_image" :height="340" />
					</view>
				</view>
				
				<view class="mt-22">
					<text class="ml-12 font-30 font-wei text-3">{{winePartyInfo.title}}</text>
				</view>
				
				<view class="d-flex j-sb a-center mt-28">
					<view v-if="winePartyInfo.money == '0.00'" class="font-44 font-wei text-e80404">免费</view>
					<view v-else class="">
						<text class="font-24 font-wei text-e80404">¥ </text>
						<text class="font-44 font-wei text-e80404">{{winePartyInfo.money}}</text>
						<text class="ml-04 font-24 text-9">起</text>
					</view>
					
					<view class="">
						<text class="font-24 text-6">剩余名额</text>
						<text class="font-24 text-e80404">{{winePartyInfo.last_num}}</text>
					</view>
				</view>
			</view>
			
			<!-- 酒会详情 -->
			<view class="bg-f5f5f5 pt-20 pl-24 pb-124 pr-24">
				<!-- 拼团信息 -->
				<view v-if="groupId" class="">
					<!-- 拼团人数未满 -->
					<view v-if="groupInfo.group_last_num > 0" class="bg-ffffff d-flex flex-column j-center a-center b-rad-10 mb-20 ptb-32-plr-00">
						<!-- 可以拼团标题 -->
						<view v-if="groupInfo.remaining_time > 0" class="d-flex mt-32">
							<view class="font-28 text-9">还差</view>
							<view class="font-28 font-wei text-e80404">{{groupInfo.group_last_num}}人</view>
							<view class="font-28 text-9">即可拼成，</view>
							<vh-count-down :timestamp="groupInfo.remaining_time" :all-font-bold="true" :show-days="false" bg-color="transparent" 
							:has-separator-distance="false" :separator-size="28" separator-color="#333" :font-size="28" color="#333" @end="groupEnd()"/>
							<view class="font-28 text-9">后结束</view>
						</view>
						
						<!-- 不可以拼团标题 -->
						<view v-else class="mt-32 font-28 font-wei text-3"> 哎呀，拼单时间已过，人数不够 </view>
						
						<!-- 拼团头像 -->
						<view class="w-648 d-flex a-center mt-40 o-scr-x" :class="groupInfo.user_head_img.length > 5 ? '' : 'j-center'">
							<view class="p-rela w-112 w-112 bg-ffffff d-flex j-center a-center b-rad-p50 mr-n-20" 
							:style="{zIndex : groupInfo.user_head_img.length - index }" v-for="(item, index) in groupInfo.user_head_img" :key="index">
								<image class="w-108 h-108 b-rad-p50" :src="item" mode="aspectFill" />
								<view v-if="index == 0" class="p-abso z-03 bottom-0 w-110 d-flex j-center">
									<text class="bg-ff9127 b-rad-16 b-s-02-ffffff ptb-02-plr-16 font-20 text-ffffff">团长</text>
								</view>
							</view>
							
							<view class="p-rela w-112 w-112 bg-ffffff d-flex j-center a-center b-rad-p50 ml-54">
								<image class="w-108 h-108 b-rad-p50" src="https://images.vinehoo.com/vinehoomini/v3/order_detail/que_mark.png" mode="aspectFill" />
							</view>
						</view>
					</view>
					
					<!-- 拼团人数已满 -->
					<view v-else class="bg-ffffff d-flex flex-column j-center a-center b-rad-10 mb-20 ptb-32-plr-00">
						<!-- 拼团已满标题 -->
						<view class="mt-32 font-28 font-wei text-3">抱歉，此团人已满！</view>
						
						<!-- 拼团头像 -->
						<view class="w-648 d-flex a-center mt-40 o-scr-x" :class="groupInfo.user_head_img.length > 5 ? '' : 'j-center'">
							<view class="p-rela w-112 w-112 bg-ffffff d-flex j-center a-center b-rad-p50 ml-10 mr-10" v-for="(item, index) in groupInfo.user_head_img" :key="index">
								<image class="w-108 h-108 b-rad-p50" :src="item" mode="aspectFill" />
								<view v-if="index == 0" class="p-abso z-03 bottom-0 w-110 d-flex j-center">
									<text class="bg-ff9127 b-rad-16 b-s-02-ffffff ptb-02-plr-16 font-20 text-ffffff">团长</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 活动地址 -->
				<view class="bg-ffffff d-flex j-sb a-center b-rad-10 ptb-30-plr-24">
					<view class="d-flex">
						<image class="w-28 h-32" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_detail/add.png" mode="widthFix" />
						<view class="ml-12 font-24 text-9 w-max-520">{{getWinePartyAddress}}</view>
					</view>
					<view class="d-flex a-center" @click="openGps()">
						<view class="w-02 h-40 bg-e7e7e7" />
						<image class="w-32 h-32 ml-32" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_detail/gps.png" mode="aspectFill" />
					</view>
				</view>
				
				<!-- 活动详情 -->
				<view class="bg-ffffff b-rad-10 mt-20 pt-32 pl-24 pb-16 pr-24">
					<view class="mb-24 font-36 font-wei text-3">活动详情</view>
					
					<vh-param title="活动时间" :introduce="winePartyInfo.activity_time" />
					<vh-param title="截止报名" :introduce="winePartyInfo.endtime" />
					<vh-param title="地点" :introduce="getWinePartyAddress" />
					<vh-param title="主办方" :introduce="winePartyInfo.company_name" />
					<view v-if="winePartyInfo.company_phone" class="" @click="copy.copyText(winePartyInfo.company_phone)">
						<vh-param title="联系方式" :introduce="winePartyInfo.company_phone" />
					</view>
				</view>
				
				<!-- 酒会详情 -->
				<view class="bg-ffffff b-rad-10 mt-20 ptb-40-plr-24">
					<view class="font-36 font-wei text-3">酒会详情</view>
					<view class="mt-32 w-b-b-w">
						<u-parse :html="winePartyInfo.description" :show-with-animation="true" />
					</view>
				</view>
			</view>
			
			<!-- 弹框 -->
			<view class="">
				<!-- 套餐弹框 -->
				<u-popup v-model="showRecCollPop" mode="bottom" :border-radius="20">
					<view class="pt-32 pr-24 pb-48 pl-24">
						<view class="d-flex">
							<view class="w-300 h-152 b-rad-06 o-hid">
								<vh-image :loading-type="2" :src="winePartyInfo.thumb_image" :height="152" />
							</view>
							
							<view class="flex-1 d-flex flex-column j-sb ml-16">
								<view class="font-28 text-3 l-h-40 o-hid text-hidden-2">{{winePartyInfo.title}}</view>
								<text v-if="packageInfo.money == '0.00'" class="font-44 font-wei text-e80404">免费</text>
								<text v-else class="font-44 font-wei text-e80404"><text class="font-24">¥ </text>{{packageInfo.money}}</text>
							</view>
						</view>
						
						<view class="mt-60 font-32 font-wei text-3">规格</view>
						
						<view class="d-flex flex-wrap ml-n-24">
							<view v-for="(item, index) in packageList" :key="index" >
								<!--该套餐的id == 分享链接的拼团套餐id && 拼团失败  -->
								<view v-if="item.id == groupPackageId && (groupInfo.remaining_time == 0 || groupInfo.group_last_num == 0)" class="bg-f7f7f7 mt-32 ml-24 ptb-04-plr-32 b-rad-24 b-d-02-dbdbdb font-28 text-c9c9c9">{{item.package_name}}</view>
								
								<view v-else class="bg-f6f6f6 mt-36 ml-24 ptb-04-plr-32 b-rad-24 font-28 text-3" :class="packageIndex == index ? 'bg-fce4e3 b-s-01-e80404 text-e80404' : ''" 
								@click="selectPackage(index)">{{item.package_name}}</view>
							</view>
						</view>
						
						<view class="mt-92 d-flex j-center a-center">
							<u-button :disabled="endSignUpSeconds == 0" shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
							:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', backgroundColor: endSignUpSeconds == 0 ? '#DDDDDD' : '#E80404', border:'none'}" @click="signUpNow">立即报名</u-button>
						</view>
					</view>
				</u-popup>
				
				<!-- 分享弹框 -->
				<u-popup v-model="showShaPop" border-radius="20" mode="bottom" :mask-custom-style="{background: 'rgba(0, 0, 0, 0.3)'}">
					<view class="bg-eeeeee h-202 d-flex j-sa a-center">
						<view class="d-flex flex-column a-center">
							<button class="sha-fri-btn w-88 h-88 b-rad-p50"></button>
							<view class="mt-16 font-24 text-3 l-h-34">微信好友</view>
						</view>
						<view class="d-flex flex-column a-center">
							<button class="sha-fri-cir-btn w-88 h-88 b-rad-p50"></button>
							<view class="mt-16 font-24 text-3 l-h-34">朋友圈</view>
						</view>
					</view>
					<view class="w-p100 h-104 bg-ffffff d-flex j-center a-center font-32 text-3" @click="showShaPop=false">取消</view>
				</u-popup>
			</view>
			
			<!-- 底部按钮 -->
			<view class="p-fixed bottom-0 z-999 bg-ffffff w-p100 h-104 d-flex j-center a-center b-sh-00021200-022" v-safeBeautyBottom="$safeBeautyBottom">
				<view v-if="winePartyInfo.last_num == 0" class="">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}" 
					@click="feedback.toast({title:'酒会报名人数已满~'})">报名人数已满</u-button>
				</view>
				
				<view v-else class="">
					<view v-if="endActivitySeconds == 0" class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}" 
						@click="feedback.toast({title:'酒会已结束了喔~'})">活动已结束</u-button>
					</view>
					<view v-else-if="endSignUpSeconds  == 0 || ( groupInfo.remaining_time == 0 && packageList.length == 1 )" class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}" 
						@click="feedback.toast({title:'酒会报名已结束了喔~'})">报名已结束</u-button>
					</view>
					<view v-else-if="winePartyInfo.status == 0" class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}" 
						@click="feedback.toast({title:'酒会已结束了喔~'})">酒会已结束</u-button>
					</view>
					<view v-else>
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'646rpx', height:'64rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
						@click="openSignPop">立即报名</u-button>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 骨架屏 -->
		<vh-skeleton v-else :type="9" bg-color="#FFF" />
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		name:"wine-party-detail",
		
		data() {
			return {
				loading: true, //加载状态
				pageLength: 0, //页面栈长度
				from:'', //从哪个端进入 1 = 安卓、2 = ios"、3 = pc
				appStatusBarHeight:'', //状态栏高度
				winePartyId:'', //酒会id
				winePartyInfo: {}, //酒会信息
				groupId: '', //拼团id
				groupPackageId:'', //拼团的套餐id（ 解决酒会详情设置多个拼团套餐的问题 ）
				groupInfo: {}, //拼团信息
				packageList: [], //酒会套餐列表
				packageIndex: 0, //选中的套餐索引
				packageInfo:{}, //套餐信息
				
				// 定时器
				endSignUpTimer: null , //报名结束定时器
				endActivityTimer: null, //活动结束定时器
				endSignUpSeconds: 0, // 距离结束报名的时间（秒）
				endActivitySeconds: 0, //距离活动结束的时间（秒）
				showShaPop: false,//是否展示分享弹窗
				showRecCollPop: false,//是否展示酒会套餐弹框
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable','winePartyOrderInfo']),
			
			// 获取地址信息
			getWinePartyAddress() {
				const { province_name, city_name, district_name, address } = this.winePartyInfo
				return province_name + city_name + district_name + address
			}
		},
		
		onLoad(options) {
			this.system.setNavigationBarBlack()
			this.pageLength = this.pages.getPageLength() //计算页面栈长度
			this.winePartyId = parseInt(options.id)
			if(options.from && options.statusBarHeight){
				this.from = options.from
				this.appStatusBarHeight = options.statusBarHeight //app状态栏高度
				this.muFrom(this.from) //保存来自哪个状态（解决用户在安卓、ios端登录失效的问题）
			}
			
			if( options.gid && options.package_id ) {
				this.groupId = parseInt(options.gid)
				this.groupPackageId = parseInt(options.package_id)
			}
			this.init()
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muWinePartyOrderInfo', 'muFrom']),
			
			// 初始化
			async init() {
				try{
					await Promise.all([this.getWinePartyDetail(), this.getWinePartyGroupInfo()])
					this.loading = false
				}catch(e) { 
					console.log(e)
					this.goBack() 
				}
			},
			
			// 获取酒会详情
			async getWinePartyDetail() {
				let res = await this.$u.api.winePartyDetail({party_id: this.winePartyId})
				this.winePartyInfo = res.data //酒会详情
				this.packageList = res.data.packages //酒会套餐列表
				this.packageInfo = this.packageList[this.packageIndex] //选中的酒会套餐
				this.startCountDown('signUpEnd', this.date.getSeconds(this.winePartyInfo.endtime)) //计算酒会报名结束时间
				this.startCountDown('activityEnd', this.date.getSeconds(this.winePartyInfo.etime)) //计算酒会活动结束时间
				
				// this.startCountDown('signUpEnd', this.date.getSeconds('2022-05-23 10:54:55')) //计算酒会报名结束时间
				// this.startCountDown('activityEnd', this.date.getSeconds('2022-05-23 10:48:55')) //计算酒会活动结束时间
			},
			
			// 获取酒会拼团信息
			async getWinePartyGroupInfo() {
				if(this.groupId) { //有拼团id时
					let res = await this.$u.api.winePartyOroupInfo({group_id: this.groupId})
					this.groupInfo = res.data
				}
			},
			
			// 发生错误返回首页
			goBack() {
				setTimeout(() => {
					if(this.comes.isFromApp(this.from)){ //判断是否从App过来 1 = 安卓 2 = ios
						wineYunJsBridge.openAppPage({
							client_path: { "ios_path":"goBack", "android_path":"goBack" }
						})
					}else{//h5端
						this.jump.navigateBack()
					}
				}, 1500)
			},
			
			// 返回首页 1.当酒闻详情页面没有暴露成首页的时候，默认回到上一个页面，2.当暴露成首页的时候回到首页
			jumpBack() {
				if(this.pageLength <= 1 && this.from == '') { //当酒闻详情页被暴露成首页（分享）
				    console.log('-----------------------------------------------我的页面栈 <= 1')
					this.jump.reLaunch('/pages/index/index')
				}else{ //当商品详情页没有被暴露成首页
					if(this.comes.isFromApp(this.from)) { //判断是否从App过来 1 = 安卓 2 = ios
						wineYunJsBridge.openAppPage({
							client_path: { "ios_path":"goBack", "android_path":"goBack" }
						});
					}
					else //h5端、微信小程序
						this.jump.navigateBack()
				}
			},
			
			// 拼团结束
			groupEnd() {
				this.groupInfo.remaining_time = 0
				this.packageInfo = {}
			},
			
			// 开始倒计时 type = 定时器类型 timesDiff = 两者时差（秒）
			startCountDown(type, timesDiff) {
				this.clearTimer(type)
				if(timesDiff <= 0) return
				if(type == 'signUpEnd'){
					this.endSignUpSeconds = Number(timesDiff)
					this.endSignUpTimer = setInterval(() => {
						this.endSignUpSeconds --
						if(this.endSignUpSeconds < 0){
							this.clearTimer(type)
						}
					}, 1000)
				}else if(type == 'activityEnd'){
					this.endActivitySeconds = Number(timesDiff)
					this.endActivityTimer = setInterval(() => {
						this.endActivitySeconds --
						if(this.endActivitySeconds < 0){
							this.clearTimer(type)
						}
					}, 1000)
				}
			},
			
			// 清空定时器 type = 定时器类型
			clearTimer(type) {
				if (type == 'signUpEnd'){
					if(this.endSignUpTimer) {
						clearInterval(this.endSignUpTimer)
						this.endSignUpTimer = null
						this.endSignUpSeconds = 0
					}
				}else if(type == 'activityEnd'){
					if(this.endActivityTimer) {
						clearInterval(this.endActivityTimer)
						this.endActivityTimer = null
						this.endActivitySeconds = 0
					}
				}
			},
			
			// 打开gps
			openGps() {
				if(this.comes.isFromApp(this.from)){
					wineYunJsBridge.openAppPage({
						client_path: { "ios_path":"showChooseMapDialog", "android_path":"showChooseMapDialog" },
						ad_path_param: [
							{ 
								"ios_key":"longitude_latitude", "ios_val": `${this.winePartyInfo.latitude},${this.winePartyInfo.longitude}`,  
							    "android_key":"longitude_latitude", "android_val": this.winePartyInfo.longitude_latitude,
							},
							{
								"ios_key":"NullKey", "ios_val": "",  
							    "android_key":"address", "android_val": this.getWinePartyAddress,
							},
						]
					});
				}else{
					this.gps.openGps(this.winePartyInfo.latitude, this.winePartyInfo.longitude, this.getWinePartyAddress)
				}
			},
			
			// 打开套餐弹框
			openSignPop() {
				if(this.login.isLogin(this.from)) {
					this.showRecCollPop = true
				}
			},
			
			// 选择套餐 index = 酒会列表索引
			selectPackage(index) {
				console.log(index)
				this.packageIndex = index
				this.packageInfo = this.packageList[this.packageIndex] //选中的酒会套餐
			},
			
			// 立即报名
			signUpNow() {
				if( Object.keys(this.packageInfo).length == 0 ) return this.feedback.toast({ title: '您还没选择套餐哟~' })
				const { uid, thumb_image, title, longitude_latitude, latitude, longitude, activity_time, is_pure_pt } = this.winePartyInfo //酒会信息
				const { id, party_id, type, money, package_name } = this.packageInfo //酒会套餐信息
				let winePartyInfo = { 
					latitude, //纬度
					longitude, //经度
					longitude_latitude, //经纬度
					money:`${money}`, //酒会价格
					wineparty_owner_uid: uid, //酒会拥有者id
					party_id, //酒会id
					package_id: id, //酒会套餐id
					coupon_id: 0, // 酒会优惠券id（ 默认为0 ）
					group_status: type == 3 ? 1 : 0, //是否拼团：0不拼团 1拼团中
					gid: this.groupId && (this.groupInfo.remaining_time !== 0 && this.groupInfo.group_last_num !== 0) && this.groupPackageId == this.packageInfo.id ? this.groupId : 0, //拼团id
					activity_time, //活动时间
					activity_address: this.getWinePartyAddress, //活动地址
					winePartyList: [], //酒会列表
				}
				let item = {
					package_id: id,
					package_name,
					nums: 1
				} //订单酒会信息
				item.thumb_image = thumb_image //酒会封面图
				item.title = title //酒会标题
				item.money = this.packageInfo.money //套餐金额
				winePartyInfo.winePartyList.push(item)
				console.log('--------------------------这是立即报名需要的数据')
				console.log(winePartyInfo)
				this.muWinePartyOrderInfo(winePartyInfo)
				
				// 分条件跳转
				if(this.comes.isFromApp(this.from)){ //判断是否从App过来 1 = 安卓 2 = ios
					if(this.from == 'next'){
          
						uni.setStorageSync('winePartyInfo',winePartyInfo);
						this.jump.appAndMiniJump(1, '/packageD/pages/wine-party-order-confirm/wine-party-order-confirm', this.$vhFrom)
					} else {
						wineYunJsBridge.openAppPage({
						client_path: {"ios_path":"winePartyConfirmOrder", "android_path":"winePartyConfirmOrder" }, 
						ad_path_param: [{"ios_key":"info", "ios_val": JSON.stringify(winePartyInfo), "android_key":"info", "android_val": JSON.stringify(winePartyInfo) }]
					});
					}
					
				}else{//h5端
					this.jump.navigateTo('../wine-party-order-confirm/wine-party-order-confirm')
				}
			}
			
		},
		
		onUnload() {
			console.log('--------onUnload')
			this.clearTimer('signUpEnd')
			this.clearTimer('activityEnd')
		},
		
		beforeDestroy() {
			console.log('--------beforeDestroy')
		},
		
		onShareAppMessage(res) {
			const { id, title, thumb_image } = this.winePartyInfo //酒会信息
			let path = `/packageD/pages/wine-party-detail/wine-party-detail?id=${id}&gid=${this.groupId}&package_id=${this.groupPackageId}`
			console.log(path)
		    return {
		      title,
		      path,
			  imageUrl: thumb_image
		    }
		},
		
	}
</script>

<style scoped>
	.nav-sha-btn {
		 background: url(https://images.vinehoo.com/vinehoomini/v3/comm/sha_black.png) center center no-repeat;
		 background-size: contain;
	}
	.sha-fri-btn{
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/comm/sha_fir.png);
		background-size: cover;
	}
	.sha-fri-cir-btn{
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/comm/sha_fri_cir.png);
		background-size: cover;
	}
</style>
