<template>
	<view class="content h-vh-100 bg-f5f5f5">
		<!-- 导航栏 -->
		<vh-navbar title="我的酒会" :show-border="true" />
		
		<!-- 收藏切换栏 -->
		<view class="p-stic z-980 bb-s-01-f5f5f5" :style="{top: system.navigationBarHeight() + 'px'}">
			<u-tabs :list="tabList" :current="currentTabs" :height="92" :font-size="32" inactive-color="#333" active-color="#E80404"
			 :bar-width="36" :bar-height="8" :bar-style="{background:'linear-gradient(214deg, #FF8383 0%, #E70000 100%)'}" :is-scroll="false" @change="changeTabs" />
		</view>
		
		<!-- 酒会列表 -->
		<view class="">
			<!-- 酒会列表有数据 -->
			<view v-if="winePartyList.length > 0 && !changeLoading" class="pb-20">
				<view class="fade-in bg-ffffff mt-20 mr-24 mb-20 ml-24 b-rad-10" v-for="(item, index) in winePartyList" :key="index" 
				@click="jump.navigateTo(`../wine-party-order-detail/wine-party-order-detail?orderNo=${item.main_order_no}`)">
					<view class="p-24">
						<view class="d-flex j-sb">
							<view class="w-478">
								<text v-if="item.type == 3" class="b-rad-04 mr-08 ptb-00-plr-08 font-20 text-ffffff font-wei-500" :class="currentTabs == 2 ? 'bg-li-14' : 'bg-li-13'">拼团</text>
								<text class="font-28 font-wei l-h-44" :class="currentTabs == 2 ? 'text-6' : 'text-3'">{{item.title}}</text>
							</view>
							<view v-if="currentTabs == 0" class="font-26 text-e80404 font-wei l-h-36">
								{{ item.review_status == 1 ? '待审核' : '待使用' }}
							</view>
							<view v-if="currentTabs == 1">
								<view v-if="item.status == 2 && item.countdown > 0" class="font-26 text-e80404 font-wei l-h-36">待拼团</view>
								<view v-else class="font-26 text-9 font-wei l-h-36">已超时</view>
							</view>
							<view v-if="currentTabs == 2">
								<view v-if="item.status == 1" class="font-26 text-3 font-wei l-h-36">已使用</view>
								<view v-if="item.status == 4" class="font-26 text-3 font-wei l-h-36">已过期</view>
								<view v-if="item.status == 5" class="font-26 text-3 font-wei l-h-36">已退费</view>
							</view>
						</view>
						
						<view class="mt-12 font-24 l-h-34" :class="currentTabs == 2 ? 'text-9' : 'text-6'">{{item.activity_time}}</view>
					
						<view class="d-flex j-sb a-center mt-24">
							<view class="">
								<text class="font-32 text-3 font-wei l-h-28"><text class="font-20">¥ </text>{{item.money}}</text>
								<text class="ml-04 font-20 text-6 l-h-28">/{{item.package_name}}</text>
							</view>
							
							<!-- 酒会核销 -->
							<view v-if="currentTabs == 0">
								<u-button shape="circle" :ripple="true" ripple-bg-color="gray" :custom-style="{width:'140rpx', height:'38rpx',fontSize:'24rpx', color:'#999', backgroundColor: '#fff', border:'none'}"
								@click="jump.navigateTo(`../wine-party-order-detail/wine-party-order-detail?orderNo=${item.main_order_no}`)">酒会核销</u-button>
							</view>
							
							<!-- 去分享 -->
							<view v-if="currentTabs == 1 && item.countdown > 0">
								<button class="w-160 h-54 bg-e80404 d-flex j-center a-center b-rad-26 b-s-01-e80404 font-24 font-wei text-ffffff" :data-item="item" open-type="share" @click.stop>去分享</button>
								
								<!-- <u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" :custom-style="{width:'120rpx', height:'54rpx', fontSize:'24rpx' ,fontWeight:'bold',color:'#FFF', backgroundColor: '#E80404', border:'none'}">去分享</u-button> -->
							</view>
						</view>
					</view>
					
					<view v-if="currentTabs == 1" class="p-rela">
						<image class="p-abso z-01 w-p100 h-82" src="https://images.vinehoo.com/vinehoomini/v3/my_reception/spell_bg.png" mode="aspectFill" />
						<view class="p-rela z-02 w-p100 h-82 d-flex j-sb a-center pl-24 pr-24">
							<view class="d-flex">
								<view class="text-3 font-24 mr-12">拼团倒计时</view>
								<vh-count-down :has-day-margin-right="false" :has-separator-distance="false" :timestamp="item.countdown" bg-color="transparent" color="#E80404"
								@end="winePartyList[index].countdown = 0" />
							</view>
							<view class="text-0 font-24">
								还差<text class="text-ff0013">{{item.group_last_num}}人</text>拼成
							</view>
						</view>
					</view>
				</view>
			    <u-loadmore :status="loadStatus" />
			</view>
		    
			<!-- 酒会列表无数据 -->
			<vh-empty v-else :padding-top="280" :padding-bottom="700" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png" text="暂无酒会" />
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default{
		name:"wine-party-order-list",
		
		data(){
			return{
				tabList: [ //状态栏选项
					{ name: '未使用' }, 
					{ name: '待拼团' }, 
					{ name: '已失效' }, 
				],
				currentTabs: 0, //当前选中tabs
				winePartyList:[], //酒会列表
				changeLoading:false, //切换loading
				page: 1, //当前页
				limit: 10, //每页限制多少条
				totalPage: 1, //总页数
				loadStatus: 'nomore', //加载状态
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
		
		onLoad() {
			this.init()
		},
		
		methods:{
			// 初始化
			async init() {
				this.page = 1
				await Promise.all([ this.getMyWinePartyList() ])
				uni.stopPullDownRefresh() //停止下拉刷新
			},
			
			// 获取我的酒会列表
			async getMyWinePartyList() {
				let res = await this.$u.api.myWinePartyList({
					type: this.currentTabs + 1, //类型：1-未使用 2-待拼团 3-已失效
					page: this.page, //当前页
					limit: this.limit //每页条数
				})
				// this.winePartyList = res.data.list
				let { list, total } = res.data
				this.page == 1 ? this.winePartyList = list : this.winePartyList = [ ...this.winePartyList, ...list ]
				this.totalPage = Math.ceil(total / this.limit)
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				this.changeLoading = false
				console.log(res)
			},
			
			// 切换状态栏
			changeTabs(index){
				this.changeLoading = true
				this.page = 1
				this.currentTabs = index
				this.getMyWinePartyList()
			},
		},
		
		onPullDownRefresh() {
			this.init()
		},
		
		onReachBottom() {
			if ( this.page == this.totalPage ) return
			this.loadStatus = 'loading'
			this.page ++
			this.getMyWinePartyList()
		},
		
		onShareAppMessage(res) {
			console.log(res)
			if(res.from == 'menu') { //顶部菜单分享
				return {
				  title: '酒云网 与百万发烧友一起淘酒aaa',
				  path: '/pages/index/index',
				}
			}else {
				const { party_id, gid, package_id , title, thumb_image, group_last_num } = res.target.dataset.item //拿到拼团订单信息
				console.log(party_id, gid, package_id, title, thumb_image, group_last_num)
				let shareTitle = `【帮我拼】还差${group_last_num}人`
				let path = `/packageD/pages/wine-party-detail/wine-party-detail?id=${party_id}&gid=${gid}&package_id=${package_id}`
				console.log(path)
				return { //按钮分享
				  title: shareTitle, //分享标题
				  path, //分享路径
				  imageUrl: thumb_image //分享图片
				}
			}
		},
	}
</script>

<style scoped></style>
