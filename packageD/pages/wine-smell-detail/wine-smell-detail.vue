<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<view class="">
			<view v-if="from == ''" class="">
				<vh-navbar title="酒闻详情" :show-border="true">
					<!-- <view slot="right" class="d-flex a-center">
						<button class="nav-sha-btn w-42 h-42 mr-24" open-type="share" />
					</view> -->
				</vh-navbar>
			</view>
			
			<!-- app（安卓、ios） -->
			<view v-else class="">
				<view class="p-fixed z-980 top-0 w-p100 bg-ffffff">
					<view :style="{ height: appStatusBarHeight + 'px'}"></view>
					<view class="p-rela h-px-48 d-flex j-sb a-center bb-s-01-eeeeee">
						<view class="h-p100 d-flex a-center ml-24" @click="jumpBack()">
							<u-icon name="nav-back" color="#333" :size="44"></u-icon>
						</view>
						<view class="font-36 font-wei text-3">酒闻详情</view>
						
						<view class="w-68">
							<view v-if="this.$vhFrom != 'next'" class="h-p100 d-flex j-center a-center" @click="jump.appShare({title: wineSmallinfo.title, des: wineSmallinfo.abst, img: wineSmallinfo.img, path:`${routeTable.pDWineSmellDetail}?id=${wineSmallinfo.id}`})">
								<image class="h-p100 mr-24 w-44 h-44" src="https://images.vinehoo.com/vinehoomini/v3/comm/sha_black.png" mode="widthFix" />
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" id="outer-content" class="fade-in">
			<!-- 酒闻详情 -->
			<view class="pr-40 pb-32 pl-40" :style="{ paddingTop: from == '' ? '38rpx' : parseInt(appStatusBarHeight) + 67 + 'px' }">
				<view class="">
					<text class="p-rela bottom-04 bg-li-1 b-tl-tr-br-rad-18 ptb-02-plr-12 font-24 text-ffffff l-h-34">{{wineSmallinfo.catename}}</text>
					<text class="ml-06 font-34 font-wei text-3">{{wineSmallinfo.title}}</text>
				</view>
				<view class="mt-12 font-24 text-9 l-h-34">{{wineSmallinfo.abst}}</view>
				<view class="mt-12 font-24 text-9 l-h-34">{{wineSmallinfo.add_time}}</view>
				<view v-if="wineSmallinfo.topic_id" class="d-flex mt-24">
					<view class="bg-eaf0fb d-flex a-center b-rad-28 pt-10 pr-28 pb-10 pl-14">
						<image class="w-40 h-36" src="https://images.vinehoo.com/vinehoomini/v3/wine_smell_detail/top_ico.png" mode="aspectFill" />
						<view class="w-max-510 ml-06 font-24 font-wei text-2e7bff text-hidden-1">{{wineSmallinfo.topic_info.title}}</view>
						<image v-if="wineSmallinfo.topic_info.ishot" class="w-46 h-28 ml-06" src="https://images.vinehoo.com/vinehoomini/v3/comm/hot.png" mode="aspectFill" />
					</view>
				</view>
				<view class="mt-28 w-b-b-w o-hid">
					<u-parse :html="wineSmallinfo.info" :show-with-animation="true" />
				</view>
				<view v-if="wineSmallinfo.source" class="d-flex j-end mt-32 font-26 text-99845f">
					文 | {{wineSmallinfo.source}}
				</view>
				<view class="mt-60 d-flex j-center a-center font-28 font-wei text-d2d2d2">- THE END -</view>
				
				<!-- <view class="p-rela d-flex b-rad-10 b-sh-00001002-007 mt-64 pt-92 pl-24 pr-24 pb-24">
					<image class="p-abso left-n-10 top-20 w-148 h-52" src="https://images.vinehoo.com/vinehoomini/v3/wine_smell_detail/gue_you_like.png" mode="aspectFill"></image>
					<image class="w-274 h-172 b-rad-06" src="https://images.vinehoo.com/vinehoomini/v3/mine/rab_rec_bg.png" mode="aspectFill"></image>
					<view class="flex-1 ml-20">
						<view class="h-72 font-26 text-3 l-h-36 o-hid text-hidden-2">必囤火腿来了 Montecastro 伊比利亚黑猪火腿 散养谷…</view>
						
						<view class="mt-08 font-20 text-9 o-hid text-hidden-1">伊比利亚黑猪，36个月谷饲火腿…</view>
						
						<view class="d-flex j-sb mt-24">
							<view class="l-h-34">
								<text class="font-36 text-e80404"><text class="font-24">¥</text>218</text>
							</view>
							<text class="bg-e80404 b-rad-06 ptb-02-plr-10 font-24 text-ffffff">去购买</text>
						</view>
					</view>
				</view> -->
			</view>
			
			<!-- 关联酒款 -->
			<WineNewsRelatedWineList :list="wineSmallinfo.period_info"/>
			
			<!-- 间隔槽 -->
			<vh-gap height="16" bg-color="#F5F5F5" />
			
			<!-- 热门推荐 -->
			<view class="pt-32 pb-10  pl-16 pr-16">
				<view class="pb-10 pl-24 font-36 font-wei text-3">热门推荐</view>
				<vh-goods-recommend-list :from="from" :outer-padding-bottom="0" :inn-margin-left="0" :inn-margin-right="0" />
			</view>
			
			<!-- 间隔槽 -->
			<vh-gap height="16" bg-color="#F5F5F5" />
			
			<!-- 评论 -->
			<view id="comment" class="p-40 pb-124">
				<view class="font-wei-600 font-36 text-3 l-h-50">评论</view>
				<view v-if="commentList.length" class="mt-32">
					<VhCommentList :list="commentList" @enjoy="thumbsUp" @reply="onReply" @toggleReply="onToggleReply"></VhCommentList>
					<view class="mt-20">
						<u-loadmore :status="loadStatus" />
					</view>
				</view>
				<view v-else class="flex-c-c mt-24 font-28 text-d8d8d8">- 暂无评价 -</view>
			</view>
			
			<!-- 弹框 -->
			<view class="">
				<!-- 酒闻分享弹出层 -->
				<!-- <u-popup v-model="showShaPop" border-radius="20" mode="bottom" :mask-custom-style="{background: 'rgba(0, 0, 0, 0.3)'}">
					<view class="bg-eeeeee h-202 d-flex j-sa a-center">
						<view class="d-flex flex-column a-center">
							<button class="sha-fri-btn w-88 h-88 b-rad-p50"></button>
							<view class="mt-16 font-24 text-3 l-h-34">微信好友</view>
						</view>
						<view class="d-flex flex-column a-center">
							<button class="sha-fri-cir-btn w-88 h-88 b-rad-p50"></button>
							<view class="mt-16 font-24 text-3 l-h-34">朋友圈</view>
						</view>
					</view>
					<view class="w-p100 h-104 bg-ffffff d-flex j-center a-center font-32 text-3" @click="showShaPop=false">取消</view>
				</u-popup> -->
	
				<!-- 评论弹框（旧） -->
				<!-- <u-popup v-model="showCommPop" :safe-area-inset-bottom="true" mode="bottom" :duration="100" :border-radius="20">
					<vh-comment :protocol-list="protocolList" :placeholder="commPlaceholder" :from="from" @on-comment-add="onCommentAdd" @on-img-send="onImgSend" />
				</u-popup> -->
				<!-- 评论弹框（新） -->
				<view v-show="showCommPop" class="fade-in tran-1 p-fixed z-1000 top-0 left-0 w-p100 h-vh-100 bg-000-000-000-060 o-hid" @click="showCommPop = false">
					<view class="p-abso bottom-0 w-p100 bg-ffffff b-tl-tr-rad-20" @click.stop>
						<vh-comment :protocol-list="protocolList" :placeholder="commPlaceholder" :from="from" @on-comment-add="onCommentAdd" @on-img-send="onImgSend" />
					</view>
				</view>
				
			</view>
			
			<!-- 底部按钮 -->
			<view class="p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff d-flex j-sb a-center b-sh-00021200-022" v-safeBeautyBottom="$safeBeautyBottom">
				<view class="d-flex a-center ml-66">
					<view class="d-flex flex-column a-center" @click="collection">
						<image class="w-40 h-40" :src="`https://images.vinehoo.com/vinehoomini/v3/comm/${isCollect ? 'star_red' : 'star_black' }.png`" mode="aspectFill" />
						<text class="font-22 text-9 l-h-32">收藏</text>
					</view>
					
					<view class="d-flex flex-column a-center ml-70">
						<image class="w-36 h-38" src="https://images.vinehoo.com/vinehoomini/v3/comm/fire.png" mode="aspectFill" />
						<text class="font-22 text-9 l-h-32">{{wineSmallinfo.viewnums | numToThousands}}</text>
					</view>
				</view>
				
				<view class="mr-24">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'438rpx', height:'64rpx', fontSize:'28rpx', color:'#fff', backgroundColor: '#E80404', border:'none'}"
					@click="commentAdd(0)">发表你的评论</u-button>
				</view>
			</view>
		</view>
	    
		<!-- 骨架屏 -->
		<vh-skeleton v-else :type="10" loading-color="#2E7BFF" bg-color="#FFF" />
	</view>
</template>

<script>
	import emoj from "@/common/js/data/rabbitEmoji.js"
	import { mapState, mapMutations } from 'vuex'
	
	export default{
		name:"wine-smell-detail",
		
		data(){
			return{
				loading: true, //加载状态
				pageLength: 0, //页面栈长度
				from:'', //从哪个端进入 1 = 安卓、2 = ios"、3 = pc
				appStatusBarHeight:'', //状态栏高度
				wineSmallId:'', //酒闻id
				wineSmallinfo:{}, //酒闻信息
				isCollect: 0, //是否收藏
				showShaPop: false,//酒闻分享弹框
				
				// 评论板块
				commentList:[], //评论列表
				commentImgReg:'', //表情包图片正则
				isReply:0, // 是否为回复 0 = 评论、1 = 回复
				replyObj: {}, //回复对象
				commPlaceholder:'', //评论占位
				protocolList:[], //协议列表
				page: 1, //第几页
				limit: 10, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //底部加载状态
				showCommPop: false, //评论弹框
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
			
			// 获取兔头表情包
			getEmojiMap() {
				return emoj
			}
		},
		
		onLoad(options) {
			this.pageLength = this.pages.getPageLength() //计算页面栈长度
			this.wineSmallId = parseInt(options.id)
			if(options.from && options.statusBarHeight){
				this.from = options.from
				this.appStatusBarHeight = options.statusBarHeight //app状态栏高度
				this.muFrom(this.from) //保存来自哪个状态（解决用户在安卓、ios端登录失效的问题）
			}
		},
		
		onShow() {
			this.init()
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muFrom']),
			
			// 初始化（接口聚合:  聚合商品详情、套餐详情、商品收藏状态...）
			async init() {
				this.page = 1
				this.totalPage = 1
				try {
					Promise.all([ this.getwineSmellDetail(), this.getWineSmellCollectionStatus() ])
					await this.getGoodsCommentList()
					this.loading = false
					uni.stopPullDownRefresh() //停止下拉刷新
				}catch(e) { 
					this.goBack() 
				}
			},
			
			// 获取酒闻详情
			async getwineSmellDetail() {
				// let res = await this.$u.api.wineSmellDetail({id: this.wineSmallId})
				let res = await this.$u.api.wineSmellDetailJson({ t:1, isJson:true, id: this.wineSmallId })
				this.wineSmallinfo = res.data
			},
			
			// 获取收藏状态
			async getWineSmellCollectionStatus() {
				if(this.login.isLogin(this.from, 0)){
					let res = await this.$u.api.wineSmellCollectionStatus({ id: this.wineSmallId })
					const { is_collect } = res.data
					this.isCollect = is_collect
				}
			},
			
			// 获取评论列表
			async getGoodsCommentList() {
				let res = await this.$u.api.wineSmellCommentList({ id: this.wineSmallId, page: this.page, limit: this.limit })
				const {total, list} = res.data
				
				// 兼容v2表情包格式
				
				list.forEach( item => {
					let firstRegRes = /\[(rabbithead|rabbit)\_[\u4e00-\u9fa5]{0,}\]/ig.exec(item.content)
					// 评论列表兼容v2表情包
					if(firstRegRes) {
						let emoReg = this.getEmojiMap.get(firstRegRes[0])
						if(emoReg) {
							item.content = item.content.replace(firstRegRes[0], '')
							item.emoji_image = firstRegRes[0]
						}
					}
					// 回复列表兼容v2表情包
					if(item.replyCommentInfo.length > 0) {
						item.replyCommentInfo.forEach(innItem => {
							let secRegRes = /\[(rabbithead|rabbit)\_[\u4e00-\u9fa5]{0,}\]/ig.exec(innItem.content)
							if(secRegRes) {
								let emoReg = this.getEmojiMap.get(secRegRes[0])
								if(emoReg) {
									innItem.content = innItem.content.replace(secRegRes[0], '')
									innItem.emoji_image = secRegRes[0]
								}
							}
						})
					}
				})
				const commentList = list.map(item => {
					const { boxid, comid, uid, avatar_image, certification_level, nickname, ulevel, type, content, emoji_image, addtime_keyword, is_digg, diggnums, replyCommentInfo } = item
					const replys = replyCommentInfo.map(replyItem => {
						const { boxid, comid, recomid, uid, avatar_image, certification_level, nickname, ulevel, type, reuid, article_username, content, emoji_image, addtime_keyword, is_digg, diggnums } = replyItem
						return {
							boxid,
							id: comid,
							pid: recomid,
							uid,
							avatar_image,
							certified_info: certification_level,
							nickname,
							user_level: ulevel || 0,
							user_type: type,
							p_uid: reuid,
							p_nickname: article_username,
							content,
							emoji_image: emoji_image && this.ossIcon(`/emoticon/${this.getEmojiMap.get(emoji_image)}.gif`),
							comment_time: addtime_keyword,
							like_status: is_digg,
							like_nums: diggnums
						}
					})
					return {
						boxid,
						id: comid,
						uid,
						avatar_image,
						certified_info: certification_level,
						nickname,
						user_level: ulevel || 0,
						user_type: type,
						content,
						emoji_image: emoji_image && this.ossIcon(`/emoticon/${this.getEmojiMap.get(emoji_image)}.gif`),
						comment_time: addtime_keyword,
						like_status: is_digg,
						like_nums: diggnums,
						replys,
						$replysLen: replys.length || 0,
						$isShowAllReply: false
					}
				})
				// this.page == 1 ? this.commentList = list : this.commentList = [...this.commentList, ...list]
				console.log('commentList', commentList)
				this.page == 1 ? this.commentList = commentList : this.commentList = [...this.commentList, ...commentList]
				this.totalPage = Math.ceil(total / this.limit)
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
			},
			
			// 酒闻收藏
			async collection(){
				if(this.login.isLogin(this.from)){
					try{
						let status = 0 // 收藏状态 0取消收藏 1收藏
						this.isCollect == 0 ? status = 1 : status = 0
						await this.$u.api.wineSmellCollection({ 
							id: this.wineSmallId, //酒闻id
							status //收藏状态
						})
						this.isCollect = status
						this.system.vibrateShort()
						this.feedback.toast({ title: status == 0 ? '取消收藏成功~' : '收藏成功~' })
					}catch(e){
						
					}
				}
			},
		    
			// 发生错误返回首页
			goBack() {
				setTimeout(() => {
					if(this.comes.isFromApp(this.from)){ //判断是否从App过来 1 = 安卓 2 = ios
						wineYunJsBridge.openAppPage({
							client_path: { "ios_path":"goBack", "android_path":"goBack" }
						})
					}else{//h5端
						this.jump.navigateBack()
					}
				}, 1500)
			},
			
			// 返回首页 1.当酒闻详情页面没有暴露成首页的时候，默认回到上一个页面，2.当暴露成首页的时候回到首页
			jumpBack() {
				if(this.pageLength <= 1 && this.from == '') { //当酒闻详情页被暴露成首页（分享）
				    console.log('-----------------------------------------------我的页面栈 <= 1')
					this.jump.reLaunch('/pages/index/index')
				}else{ //当商品详情页没有被暴露成首页
					if(this.comes.isFromApp(this.from)) { //判断是否从App过来 1 = 安卓 2 = ios
						wineYunJsBridge.openAppPage({
							client_path: { "ios_path":"goBack", "android_path":"goBack" }
						});
					}
					else //h5端、微信小程序
						this.jump.navigateBack()
				}
			},
			
			// 点赞 item = 评论列表某一项
			async thumbsUp(item) {
				console.log(item)
				if(this.login.isLogin(this.from)){
					this.feedback.loading({ title: '' })
					try {
						const { id, like_status } = item
						if (like_status) {
							await this.$u.api.cancelEnjoyWineComment({ id, type: 2 })
							item.like_status = 0
							item.like_nums--
						} else {
							await this.$u.api.wineSmellThumbsUp({ id, type: 2 })
							item.like_status = 1
							item.like_nums++
						}
						this.system.vibrateShort()
					} finally {
						this.feedback.hideLoading()
					}
				}
			},
			
			// 滚动到评论
			scrollToComment() {
				uni.createSelectorQuery().in(this).select('#comment').boundingClientRect(data=>{
					uni.createSelectorQuery().in(this).select('#outer-content').boundingClientRect(res=>{
						uni.pageScrollTo({
							scrollTop: data.top - res.top - 80,
							duration: 0,
						})
					}).exec()
				}).exec()
			},
			
			// 添加评论 type = 评论类型 0 = 评论、1 = 回复, item = 评论列表对象
			commentAdd(type, item = {}){
				if(this.login.isLogin(this.from)){
					this.scrollToComment()
					this.commPlaceholder = '说说你的观点~'
					this.commentImgReg = ''
					this.isReply = type
					if ( this.isReply ) {
						this.replyObj = item
						this.commPlaceholder = `@${item.nickname}:`
					}
					this.checkAgreement() //检查是否勾选评论协议
				}
			},
			
			// 检测用户是否勾选协议
			checkAgreement() {
				uni.getStorage({
					key:'protocolList',
					success: res => { this.protocolList = res.data },
					fail: async () => {
						try{
							let res = await this.$u.api.userSpecifiedData({field: 'protocol'})
							this.protocolList = res.data.protocol
							uni.setStorageSync('protocolList', this.protocolList )
						}catch(e){
							//TODO handle the exception
						}
					},
					complete: () => {
						this.showCommPop = true
					}
				})
			},
			
			// 发送表情包 reg = 表情包图片正则
			onImgSend( reg ) {
				this.commentImgReg = reg
			},
			
			// 发送评论/回复评论 cont = 评论内容
			async onCommentAdd( cont ) {
				console.log('------------------------我是父组件接受的评论内容')
				console.log( cont )
				// this.feedback.loading({title: '评论中...'})
				let data = {}
				data.id = this.wineSmallId //期数
				data.content = cont //评论内容
				data.emoji_image = this.commentImgReg //表情包图片正则

				// 回复
				if(this.isReply) {
					console.log('-----------我是回复')
					console.log(this.replyObj)
					const { id, content, uid } = this.replyObj
					data.recontent = content
					data.recomid = id
					data.reuid = uid
					// console.log(id, first_id, uid)
					// data.first_id = first_id ? first_id : id
					// data.pid = id
					// data.p_uid = uid
				}
				console.log('-------------------这是需要上传的数据')
				console.log(data)
				try {
					let res = await this.$u.api.wineSmellCommentAdd(data)
					this.feedback.toast({title:'评论成功~'})
					this.showCommPop = false
					this.commentImgReg = ''
					if( !this.protocolList.includes('1') ) this.agreeCommAgreement()
				}catch(e) {
					this.commentImgReg = ''
				}
			},
			
			// 同意/取消同意协议
			async agreeCommAgreement() {
				try{
					await this.$u.api.agreeProtocol({ xid: 1 })
					uni.setStorageSync('protocolList', ['1'] )
				}catch(e){
					//TODO handle the exception
				}
			},
			onReply (item) {
				this.commentAdd(1, item)
			},
			onToggleReply (item) {
				const { id } = item
				const currComment = this.commentList.find(item => item.id === id)
				if (!currComment) return
				currComment.$isShowAllReply = !currComment.$isShowAllReply
			},
		},
		
		onShareAppMessage(res) {
			const { id, title, img } = this.wineSmallinfo //酒闻信息
		    return {
		      title,
		      path: `/packageD/pages/wine-smell-detail/wine-smell-detail?id=${id}`,
			  imageUrl: img
		    }
		},
		
		// onShareTimeline(res) {
		// 	const { id, title, img } = this.wineSmallinfo //酒闻信息
		//     return {
		//       title,
		//       path: `/pages/wine-smell-detail/wine-smell-detail?id=${id}`,
		// 	  imageUrl: img
		//     }
		// },
	    
		onPullDownRefresh() {
			this.init()
		},
		
		onReachBottom() {
			if (this.page == this.totalPage || this.totalPage == 0) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getGoodsCommentList()
		}
		
	}
</script>

<style scoped>
	.nav-sha-btn {
		 background: url(https://images.vinehoo.com/vinehoomini/v3/comm/sha_black.png) center center no-repeat;
		 background-size: contain;
	}
	/* .sha-fri-btn{
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/comm/sha_fir.png);
		background-size: cover;
	}
	.sha-fri-cir-btn{
		background-image: url(https://images.vinehoo.com/vinehoomini/v3/comm/sha_fri_cir.png);
		background-size: cover;
	} */
	
	/* 顶部导航栏添加白线 */
	::v-deep .u-border-bottom:after {
		border-bottom: 0.5px solid #EEEEEE;
	}
</style>
