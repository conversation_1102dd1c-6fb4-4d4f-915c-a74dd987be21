<template>
	<view class="content">
		<!-- 导航栏 -->
		<u-navbar back-icon-color="#333" title="确认订单" title-size="36" :title-bold="true" title-color="#333" />
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in bg-f5f5f5 pt-20 pb-270">
			<!-- 订单商品列表 -->
			<view class="bg-ffffff b-rad-10 mb-20 ml-24 mr-24 ptb-28-plr-20 d-flex" v-for="(item, index) in winePartyOrderInfo.winePartyList" :key="index">
				<view class="w-316 h-160 b-rad-10 o-hid">
					<vh-image :loading-type="2" :src="item.thumb_image" :height="160" />
				</view>
				<view class="flex-1 d-flex flex-column j-sb ml-20">
					<view class="font-24 text-0 l-h-34 text-hidden-2">{{item.title}}</view>
					<view class="d-flex j-sb a-center">
						<text class="font-20 text-6">x1</text>
						<text class="font-28 text-3">¥{{item.money}}</text>
					</view>
				</view>
			</view>
			
			<!-- 活动时间 + 活动地址 -->
			<view class="bg-ffffff b-rad-10 mb-20 ml-24 mr-24">
				<!-- 活动时间 -->
				<view class="d-flex pt-46 pr-24 pb-32 pl-24">
					<image class="w-28 h-32 mt-04" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_order_confirm/time.png" mode="widthFix" />
					
					<view class="flex-1 ml-12">
						<view class="font-28 font-wei text-3">活动时间</view>
						<view class="mt-08 font-24 text-9">{{winePartyOrderInfo.activity_time}}</view>
					</view>
				</view>
				
				<!-- 活动地址 -->
				<view class="d-flex bt-s-01-eeeeee pt-32 pr-24 pb-40 pl-24">
					<image class="w-28 h-32" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_order_confirm/add.png" mode="widthFix" />
					
					<view class="flex-1 ml-12">
						<view class="font-28 font-wei text-3">活动地址</view>
						<view class="d-flex j-sb mt-08">
							<view class="font-24 text-9">{{winePartyOrderInfo.activity_address}}</view>
							<view class="d-flex a-center" @click="gps.openGps(winePartyOrderInfo.latitude, winePartyOrderInfo.longitude, winePartyOrderInfo.activity_address)">
								<view class="w-02 h-40 bg-e7e7e7"></view>
								<image class="w-32 h-32 ml-32" src="https://images.vinehoo.com/vinehoomini/v3/wine_party_order_confirm/gps.png" mode="aspectFill" />
							</view>
						</view>
					</view>
				</view>
			</view>
		    
			<!-- 商品金额明细 -->
			<view class="bg-ffffff b-rad-10 mb-20 ml-24 mr-24">
				<view class="ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee font-28 text-9">温馨提示：不支持7天无理由退货</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<text class="font-28 font-wei text-3">商品金额</text>
					<text class="font-28 font-wei text-3">¥{{priceInfo.package_money}}</text>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee" @click="showCouPop = true">
					<view class="font-28 font-wei text-3">优惠券</view>
					
					<!-- <view class="d-flex a-center">
						<view class="font-24 font-wei text-e80404">-¥{{priceInfo.coupon_value}}</view>
						<view class="ml-08">
							<u-icon name="arrow-right" :size="24" color="#666" />
						</view>
					</view> -->
					<view class="font-24 font-wei text-e80404">-¥{{priceInfo.coupon_value}}</view>
				</view>
				
				<view class="d-flex j-end a-center ml-24 mr-24 pt-32 pb-32">
					<text class="font-28 font-wei text-3">合计：</text>
					<text class="font-32 font-wei text-e80404">¥{{priceInfo.payment_amount}}</text>
				</view>
			</view>
			
			<!-- 联系客服 -->
			<view class="d-flex j-center mb-46">
				<text class="font-24 text-9">如有特殊需求，请</text>
				<text class="font-24 text-2e7bff" @click="system.customerService($vhFrom)">联系客服</text>
				<text class="font-24 text-9">。</text>
			</view>
		
		    <!-- 立即支付按钮 -->
			<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-32 pr-24">
				<view class="d-flex a-center">
					<text class="font-28 font-wei text-3">合计：</text>
					<text class="font-40 font-wei text-e80404"><text class="font-28">¥</text>{{priceInfo.payment_amount}}</text>
				</view>
				<view class="">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none'}" 
					@click="payImmediately">立即支付</u-button>
				</view>
			</view>
		    
			<OrderConfirmCouponPopup
				v-model="showCouPop"
				:canUseCouList="canUseCouList"
				:canNotUseCouList="canNotUseCouList"
				:canUseSelCouId="canUseSelCouId"
				@changeCouId="changeCouId"
			></OrderConfirmCouponPopup>
		</view>
		
		<!-- 骨架屏 -->
		<view v-else class="fade-in">
			<vh-skeleton :type="2" loading-color="#2E7BFF" />
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default {
		name: 'wine-party-order-confirm',
		
		data() {
			return {
				loading: true, //加载状态 true = 加载中、false = 结束加载
				priceInfo: {}, //金额信息
				showCouPop: false, //是否展示优惠券弹框
				canUseCouList: [], //可以使用的优惠券列表
				canUseSelCouId: 0, //选中的优惠券Id 默认为0
				canNotUseCouList: [], //不可使用的优惠券列表
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable', 'winePartyOrderInfo']),
			
			hasWinePartyOrderInfo({ winePartyOrderInfo }) {
				return !!(typeof winePartyOrderInfo === 'object' && Object.keys(winePartyOrderInfo).length)
			}
		},
		
		onShow() {
			const winePartyInfo = uni.getStorageSync('winePartyInfo');
			if(winePartyInfo){
				this.muWinePartyOrderInfo(winePartyInfo)
				uni.removeStorageSync('winePartyInfo');
			}
			if (!this.hasWinePartyOrderInfo) {
				this.jump.reLaunch('/')
				return
			}
			Promise.all([this.getPrice(), this.getOrderCoupon()])
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muPayInfo', 'muWinePartyOrderInfo']),
			
			// 获取金额
			async getPrice() {
				const { party_id, package_id } = this.winePartyOrderInfo //酒会信息
				let data = {
					party_id, //酒会ID
					package_id, //酒会套餐ID
					coupon_id: this.canUseSelCouId, //优惠券ID
				}
				
				console.log('-----------这是要上传的数据')
				console.log(data)
				try {
					let res = await this.$u.api.winePartyCalculateOrderMoney(data)
					this.priceInfo = res.data
					this.loading = false
					this.showCouPop = false
				}catch(e) {
					setTimeout(() => {
						this.jump.jumpPrePage(this.$vhFrom)
					}, 1500)
				}
			},
			
			// 立即支付
			async payImmediately() {
				console.log('-------------------------我是立即支付事件')
				this.feedback.loading()
				const { wineparty_owner_uid, party_id, package_id, group_status, gid } = this.winePartyOrderInfo //酒会订单信息
				const { payment_amount } = this.priceInfo
				let data = {
					wineparty_owner_uid, //酒会拥有者用户ID
					party_id, //酒会ID
					package_id, //酒会套餐id
					order_from: 2, //来源: 0-IOS 1-Android 2-H5 3-PC 4-Wxapp
					coupon_id: this.canUseSelCouId, //优惠券ID
					money_off_value: 0, //满减金额
					invoice_progress: 0, //发票是否开票 0不开票 1开票
					invoice_id: 0, //发票抬头ID
					gid, //拼团ID(发起拼团人的订单ID)
					group_status, //是否拼团：0不拼团 1拼团中
					review_status: 0, //是否审核：0不审核 1审核中
				}
				console.log('-------------------------------这是创建酒会订单需要上传的参数')
				console.log(data)
				
				try{
					  let res = await this.$u.api.winePartyCreateOrder(data)
					  console.log('----aaaaaaaaa')
					  console.log(res)
					  let payInfo = {
							winePartyInfo: { // 微信环境下取消支付需要该参数
								party_id,
								package_id,
								gid
							},
						  payPlate: 1, //支付板块 0 = 普通商品，1 = 酒会，2 = 门店
						  payment_amount, //支付金额
						  payment_method: 4, //支付方式: 0:支付宝APP 1:支付宝H5 2:微信h5 3:微信APP 4:微信小程序 5:PC扫码
						  order_type: 2, //订单类型(来源)：1:v3支付 2:v3酒云 20:门店 30:老外卖酒 40:nft
						  is_cross: 0, //是否跨境 0:非跨境 1:跨境 不传默认非跨境
						  ...res.data //下单信息
					  }
					  if( payment_amount > 0 ) { //下单金额大于0
						if(this.$vhFrom == 'next'){
				const { main_order_no} = payInfo
						const params = {
							client_path: {
								"android_path": "toPay"
							},
							ad_path_param: [
								{ "android_key": "main_order_no", "android_val": `${main_order_no}` },
								{ "android_key": "payOkUrl", "android_val": `${this.routeTable.pDWinePartyOrderList}` },
							]
						}
						console.warn(params);
						
						wineYunJsBridge.openAppPage(params)
						} else {
							this.muPayInfo(payInfo)
						  
							this.jump.appAndMiniJump(0, this.routeTable.pBPayment, this.$vhFrom, 1)
						}
						 
					  }else {
						 this.feedback.toast({ title: '支付成功~' })
						 setTimeout(() => {
							this.jump.appAndMiniJump(0, this.routeTable.pDWinePartyOrderList, this.$vhFrom, 1)
							 
						 }, 1500)
					  }
				}catch(e){
					// setTimeout(()=> {
					// 	this.jump.jumpPrePage(this.$vhFrom)
					// }, 1500)
				}
			},

			async getOrderCoupon() {
				const { party_id, winePartyList } = this.winePartyOrderInfo
				const items_info = winePartyList.map(({ package_id, nums, money }) => ({
					id: party_id,
					package_id,
					nums,
					price: money
				}))
				const params = {
					type: 2000,
					items_info
				}
				const res = await this.$u.api.getCouponListByOrder(params)
				this.canUseCouList = res.data.in
				this.canNotUseCouList = res.data.notIn
			},

			changeCouId( item ) {
				let { id, coupon_type } = item
				this.couponType = coupon_type
				if( this.canUseSelCouId == id ) {
					this.canUseSelCouId = 0
				}else {
					this.canUseSelCouId = id
				}
				this.getPrice()
			},
		}
	}
</script>

<style scoped></style>
