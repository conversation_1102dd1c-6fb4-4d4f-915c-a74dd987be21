<template>
	<view class="content">
		<!-- 导航栏 -->
		<view class="p-rela z-02 b-sh-00021200-022">
			<u-navbar :is-back="true" title="酒闻咨讯" title-color="#333" :title-size="36" :title-bold="true" />
		</view>
		
		<!-- 酒闻咨讯列表 -->
		<view class="">
			<!-- 酒闻咨讯列表有数据 -->
			<view v-if="wineSmellList.length > 0" class="fade-in bg-f5f5f5 ptb-20-plr-00">
				<view class="bg-ffffff mr-24 mb-20 ml-24 p-20 b-rad-10 d-flex j-sb" v-for="(item,index) in wineSmellList" :key="index" @click="jump.navigateTo(`../wine-smell-detail/wine-smell-detail?id=${item.id}`)">
					<view class="w-188 b-rad-06 o-hid">
						<vh-image :loading-type="4" :src="item.img" :height="188" />
					</view>
					
					<view class="ml-20 flex-1 d-flex flex-column j-sb">
						<view class="">
							<view class="font-28 text-3 l-h-36 text-hidden-2">{{item.title}}</view>
							<view class="mt-12 font-24 text-9 l-h-34 o-hid text-hidden-1">{{item.abst}}</view>
						</view>
						
						<view class="mt-36 d-flex j-end">
							<view class="d-flex a-center mr-56">
								<image class="w-26 h-26" src="https://images.vinehoo.com/vinehoomini/v3/comm/view.png" mode="widthFix" />
								<text class="ml-06 font-24 text-9 l-h-34">{{item.viewnums | numToThousands}}</text>
							</view>
							<view class="d-flex a-center">
								<image class="w-26 h-26" src="https://images.vinehoo.com/vinehoomini/v3/comm/comm.png" mode="widthFix" />
								<text class="ml-06 font-24 text-9 l-h-34">{{item.commentnums | numToThousands}}</text>
							</view>
						</view>
					</view>
				</view>
				<u-loadmore :status="loadStatus" />
			</view>
			
			<!-- 酒闻咨讯列表无数据 -->
			<view v-if="wineSmellList.length == 0" class="fade-in mt-100">
				<vh-empty :padding-top="270" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_goods.png" text="暂无酒闻" />
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name:"my-wine-smell-list",
		
		data(){
			return{
				wineSmellList:[], //酒闻列表
				page: 1, //第几页
				limit: 10, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //加载状态
			}
		},
		
		onLoad() {
			this.getwineSmellList()
		},
		
		methods:{
			// 获取酒闻列表
			async getwineSmellList() {
				let res = await this.$u.api.wineSmellList({ page: this.page, limit: this.limit })
				this.page == 1 ? this.wineSmellList = res.data.list : this.wineSmellList = [...this.wineSmellList, ...res.data.list]
				this.totalPage = Math.ceil(res.data.total / this.limit)
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
			},
		},
		
		onReachBottom() {
			if ( this.page == this.totalPage ) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getwineSmellList()
		}
	}
</script>

<style scoped></style>
