<template>
	<view class="content" :class="showReaWinePartMask ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<vh-navbar title="精品酒会">
			<text class="ptb-14-plr-24 font-24 font-wei text-3" @click="showReaWinePartMask = true">发布酒会</text>
		</vh-navbar>
		
		<!-- 搜索框 -->
		<view class="p-stic z-980 h-92 bg-ffffff d-flex j-sb a-center pl-24 pr-24" :style="{top: getNavigationBarHeight + 'px'}">
			<view class="bg-f7f7f7 d-flex j-sb a-center b-rad-40 pl-26 pr-26">
				<view class="p-rela w-570 h-68 d-flex a-center">
					<image class="w-44 h-44" src="https://images.vinehoo.com/vinehoomini/v3/comm/ser_gray.png" mode="aspectFill" />
					<input class="w-460 h-p100 ml-10 font-28 text-3" type="text" v-model="keyword" @input="" @confirm="getWinePartList"/>
					<view v-if="keyword.length" class="p-abso right-0 top-0 w-40 h-p100 d-flex j-center a-center" @click="keyword = ''">
						<image class="w-40 h-40" src="https://images.vinehoo.com/vinehoomini/v3/comm/del_gray.png" mode="aspectFill" />
					</view>
				</view>
			</view>
			
			<view class="font-28 font-wei text-6" @click="getWinePartList">搜索</view>
		</view>
		
		<!-- 精品酒会列表 -->
		<view class="">
			<!-- 精品酒会列表有数据 -->
			<view v-if="winePartyList.length > 0" class="fade-in bg-f5f5f5 ptb-20-plr-00">
				<view class="b-rad-10 mr-24 mb-20 ml-24 o-hid" v-for="(item, index) in winePartyList" :key="index" @click="openWinePartyDetail(item.id)">
					<vh-image :src="item.thumb_image" :height="356" />
					
					<view class="bg-ffffff p-24">
						<view class="">
							<!-- <text v-if="item.source == 1" class="bg-li-1 ptb-02-plr-08 b-rad-06 mr-12 font-26 text-ffffff l-h-30">官方</text> -->
							<text class="font-28 font-wei text-3 l-h-40">{{item.title}}</text>
						</view>
						
						<view class="d-flex a-center mt-20">
							<image class="w-30 h-30" src="https://images.vinehoo.com/vinehoomini/v3/comm/tim_red.png" mode="aspectFill"></image>
							<text class="ml-10 text-6 font-24">{{item.activity_time}}</text>
						</view>
						
						<view class="d-flex a-center mt-16">
							<image class="w-30 h-30" src="https://images.vinehoo.com/vinehoomini/v3/comm/add_red.png" mode="aspectFill"></image>
							<view class="flex-1 ml-10 text-6 font-24 text-hidden-1">{{item.province_name}}{{item.city_name}}{{item.district_name}}{{item.address}}</view>
						</view>
						
						<view class="d-flex j-sb a-center mt-20">
							<view class="l-h-50">
								<text class="font-44 font-wei text-e80404"><text class="mr-06 font-24">¥</text>{{item.money}}</text>
								<text class="ml-06 font-28 text-9">起</text>
							</view>
							
							<view class="">
								<view v-if="item.last_num == 0" class="font-28 font-wei text-3">报名人数已满</view>
								<view v-else class="">
									<view v-if="item.status == 0" class="">
										<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
										:custom-style="{width:'150rpx', height:'42rpx', fontSize:'24rpx' ,fontWeight:'500', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
										@click="openWinePartyDetail(item.id)">立即报名</u-button>
									</view>
									<view v-if="false" class="">
										<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
										:custom-style="{width:'150rpx', height:'42rpx', fontSize:'24rpx' ,fontWeight:'500', color:'#666', backgroundColor: '#FFF', border:'1rpx solid #979797'}">已报名</u-button>
									</view>
									<view v-if="item.status == 1" class="">
										<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
										:custom-style="{width:'150rpx', height:'42rpx', fontSize:'24rpx' ,fontWeight:'500', color:'#FFF', backgroundColor: '#DDDDDD', border:'none'}"
										@click="feedback.toast({title:'报名已结束~', icon: 'error'})">已结束</u-button>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<u-loadmore :status="loadStatus" />
			</view>
			
			<!-- 精品酒会列表无数据 -->
			<view v-if="winePartyList.length == 0" class="fade-in">
				<vh-empty :padding-top="148" image-src="https://images.vinehoo.com/vinehoomini/v3/empty/emp_order.png" text="暂无酒会" />
			</view>
		</view>
	    
		<!-- 发布酒会弹框 -->
		<view class="">
			<u-mask :show="showReaWinePartMask">
				<view class="h-p100 d-flex flex-column j-center a-center">
					<view class="w-506 b-rad-24 o-hid">
						<view class="p-rela w-506 h-336 d-flex j-center a-end">
							<image class="p-abso top-0 w-506 h-336" src="https://images.vinehoo.com/vinehoomini/v3/boutique_wine_party/pub_wine.png" mode="aspectFill" />
							<view class="w-506 h-168 bg-ffffff" />
						</view>
						<view class="w-506 h-260 bg-ffffff d-flex flex-column j-center a-center">
							<view class="font-28 text-3 l-h-40">请将你的酒会信息发送到</view>
							<view class="font-28 font-wei text-3 l-h-40">{{mail}}</view>
							<view class="mt-20 font-20 text-9 l-h-40">工作人员将在3个工作日内处理</view>
						</view>
						
						<view class="w-506 d-flex h-78 bg-ffffff bt-s-01-eeeeee">
							<view class="w-252 h-78 d-flex j-center a-center font-28 text-9" @click="system.phoneCall(phone)">联系客服</view>
							<view class="w-252 h-78 d-flex j-center a-center font-28 font-wei text-3" @click="copy.copyText(mail)">复制邮箱</view>
						</view>
					</view>
					
					<view class="mt-60" @click="showReaWinePartMask = false">
						<image class="w-54 h-54" src="https://images.vinehoo.com/vinehoomini/v3/boutique_wine_party/clo_whi.png" mode="aspectFill" />
					</view>
				</view>
			</u-mask>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	
	export default {
		name: 'wine-party-boutique', 
		
		data() {
			return {
				showReaWinePartMask: false, //是否展示发布酒会弹框
				phone:'8888888', //客服电话
				mail:'<EMAIL>', //邮箱地址
				keyword:'', //搜索关键字
				winePartyList: [], //酒会列表
				page: 1, //第几页
				limit: 10, //每页显示多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //加载状态
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['winePartyCityInfo']),
			
			// 获取导航栏高度
			getNavigationBarHeight(){
				return this.system.getSysInfo().statusBarHeight + 44
			}
		},
		
		onLoad() {
			this.getWinePartList()
		},
		
		methods: {
			// 获取酒会列表
			async getWinePartList() {
				let res = await this.$u.api.winePartyList({ page: this.page, limit: this.limit, keywords: this.keyword })
				this.page == 1 ? this.winePartyList = res.data.list : this.winePartyList = [...this.winePartyList, ...res.data.list]
				this.totalPage = Math.ceil(res.data.total / this.limit)
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
			},
			
			// 打开酒会详情 id = 酒会id
			openWinePartyDetail(id) {
				this.jump.navigateTo(`../wine-party-detail/wine-party-detail?id=${id}`)
			}
		},
		
		onReachBottom() {
			if ( this.page == this.totalPage ) return
			this.loadStatus = 'loading'
			this.page ++
			this.getWinePartList()
		}
	}
</script>

<style scoped></style>
