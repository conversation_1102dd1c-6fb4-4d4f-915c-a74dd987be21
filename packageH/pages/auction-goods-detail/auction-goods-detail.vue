<template>
  <view id="goods-detail">
    <vh-navbar title="详情页" height="46">
      <view v-if="$app" slot="right" class="d-flex">
        <image class="p-24 w-44 h-44" :src="ossIcon('/auction/icon_share.png')" @click="onShare" />
      </view>
    </vh-navbar>
    <view v-if="!loading" :class="isPendPutaway ? '' : 'pb-104'">
      <view class="auction-gdetail__swiper">
        <vh-swiper
          :loading-type="2"
          :list="goodsDetail.$swiperList"
          height="464"
          border-radius="0"
          img-mode="aspectFit"
          @change="onSwiperChange"
          @click="onSwiperClick"
        />
      </view>
      <view class="p-rela">
        <image :src="ossIcon(goodsStatusBg)" class="p-abso top-0 w-p100 h-70" />
        <view class="p-rela flex-sb-c pl-50 pr-24 h-70">
          <text class="font-wei-500 font-28 text-ffffff l-h-42">{{ goodsStatusText }}</text>
          <view v-if="!isPendPutaway" class="flex-c-c">
            <text class="font-24 l-h-34" :class="goodsStatusTimeTextClazz">{{ goodsStatusTimeText }}</text>
            <view class="flex-c-c ml-06">
              <text class="font-wei-500 font-24 l-h-34" :class="goodsStatusTimeTextClazz">{{ goodsStatusTimeValue | date('mm月dd日hh:MM:ss') }}</text>
              <!-- <view class="flex-c-c">
                <view class="flex-c-c w-36 h-36 font-wei-500 font-24 text-ffffff b-rad-04" :class="goodsStatusTimeTextBgClazz">{{ goodsStatusTimeValue | date('hh') }}</view>
                <view class="flex-c-c w-22 h-36 font-wei-600 font-24" :class="goodsStatusTimeTextClazz">:</view>
                <view class="flex-c-c w-36 h-36 font-wei-500 font-24 text-ffffff b-rad-04" :class="goodsStatusTimeTextBgClazz">{{ goodsStatusTimeValue | date('MM') }}</view>
                <view class="flex-c-c w-22 h-36 font-wei-600 font-24" :class="goodsStatusTimeTextClazz">:</view>
                <view class="flex-c-c w-36 h-36 font-wei-500 font-24 text-ffffff b-rad-04" :class="goodsStatusTimeTextBgClazz">{{ goodsStatusTimeValue | date('ss') }}</view>
              </view> -->
            </view>
          </view>
        </view>
      </view>
      <view v-if="isShowFixedTime" :style="{ top: `${46 + $appStatusBarHeight}px` }" class="p-fixed left-0 flex-c-c w-p100 h-70 font-24 text-ffffff l-h-34 z-9999" :class="goodsStatusTimeTextBgClazz">
        <text>{{ goodsStatusTimeText }}</text>
        <text class="ml-06 font-wei-500">{{ goodsStatusTimeValue | date('mm月dd日hh:MM:ss') }}</text>
      </view>
      <view class="ptb-32-plr-24">
        <view class="flex-sb-c">
          <view>
            <text class="font-28 text-e80404">{{ goodsStatusObj.pricePrefixText }}</text>
            <text class="ml-06 font-52 text-e80404"><text class="font-28">¥</text>{{ goodsDetail.final_auction_price }}</text>
          </view>
          <view class="flex-c-c font-wei-500 font-28 text-3">
            <text>卖家报价</text>
            <text class="ml-06">¥{{ goodsDetail.quote }}</text>
          </view>
        </view>
        <view class="flex-s-c mt-06">
          <view
            class="flex-c-c w-84 h-40 font-wei-500 font-26 text-3 b-s-02-d8d8d8 b-rad-06"
            @click="freeShipmentPopupVisible = true"
          >包邮</view>
          <view
            class="flex-c-c ml-20 w-166 h-40 font-wei-500 font-26 b-rad-06"
            :class="isExistReservePrice ? 'text-e80404 b-s-02-fce4e3' : 'text-ff9127 b-s-02-ffe7cf'"
            @click="reversePricePopupVisible = true"
          >
            <span>{{ isExistReservePrice ? '有保留价' : '无保留价' }}</span>
            <image :src="ossIcon('/auction/tips_28.png')" class="ml-06 w-24 h-24"></image>
          </view>
        </view>
        <view class="mt-16 font-wei-500 font-30 text-3 l-h-44">{{ goodsDetail.title }}</view>
        <view class="flex-sb-c mt-30 font-wei-500 font-28 text-9">
          <text>围观<text class="text-3">{{ ' ' }}{{ goodsDetail.pageviews || 0 }} 次</text></text>
          <text>设置提醒<text class="text-3">{{ ' ' }}{{ goodsDetail.msg_count || 0 }}{{ ' ' }}</text>人</text>
        </view>
      </view>
      <view class="ptb-20-plr-24 bg-f5f5f5">
        <view v-if="bidRecords.length" class="mb-20 ptb-28-plr-24 pb-08 bg-ffffff b-rad-10">
          <view class="flex-sb-c" @click="jump.navigateTo(`${routeTable.pHAuctionBidRecords}?id=${id}&onsale_status=${goodsDetail.onsale_status}`)">
            <text class="font-wei-500 font-32 text-3">拍卖记录</text>
            <view class="flex-c-c">
              <text class="font-24 text-3">{{ allBidRecordsLength }}次</text>
              <image :src="ossIcon('/after_sale_detail/arrow_right_12x20.png')" class="ml-04 w-12 h-20">
            </view>
          </view>
          <AuctionBidList :list="bidRecords" :uid="userInfo.uid" :onsaleStatus="goodsDetail.onsale_status" isFromDetail class="mt-16" />
        </view>

        <view class="ptb-28-plr-24 bg-ffffff b-rad-10">
          <AuctionGoodsSetting :goods="goodsDetail" isShowNonsupport :earnestStatus="earnestStatus" :earnestOrderNo="earnestOrderNo" />
        </view>

        <view class="mt-20 ptb-28-plr-24 bg-ffffff b-rad-10">
          <AuctionGoodsParams :goods="goodsDetail" />
        </view>

        <template v-if="goodsDetail.label === 1">
          <view v-if="goodsDetail.brief" class="mt-20 ptb-28-plr-24 bg-ffffff b-rad-10">
            <view class="font-wei-500 font-32 text-3">拍品详情</view>
            <view class="mt-12 font-28 text-3 l-h-40">{{ goodsDetail.brief }}</view>
          </view>
        </template>

        <view v-else class="mt-20 ptb-28-plr-24 bg-ffffff b-rad-10">
          <AuctionGoodsDesc :goods="goodsDetail" />
        </view>

        <view v-if="goodsDetail.label === 1" class="mt-20 ptb-28-plr-24 bg-ffffff b-rad-10">
          <AuctionGoodsTips :goods="goodsDetail"></AuctionGoodsTips>
        </view>

        <view id="auction-goods-comments" class="mt-20 ptb-32-plr-24 pt-40 bg-ffffff b-rad-10">
          <AuctionGoodsComments ref="auctionGoodsCommentsRef" :goodsId="id" />
        </view>

        <view id="auction-goods-recommends">
          <AuctionRecommendGoodsList ref="auctionGoodsRecommendsRef" class="mt-70" />
        </view>

        <view class="pt-60 h-118 font-wei-500 font-18 text-9 l-h-26 text-center">哼！谁还没有点底线</view>
      </view>

      <view v-if="!isPendPutaway" class="p-fixed left-0 bottom-0 flex-sb-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-9999" v-safeBeautyBottom="$safeBeautyBottom">
        <view class="d-flex ml-24 h-p100">
          <view class="flex-c-c flex-column mr-30 w-84 h-p100" @click="onEnjoy">
            <image :src="ossIcon(`/auction/enjoy${goodsDetail.is_like ? '_h' : ''}_40.png`)" class="w-40 h-40" />
            <text class="font-22 l-h-32" :class="goodsDetail.is_like ? 'text-ff9127' : 'text-9'">收藏</text>
          </view>
          <view class="flex-c-c flex-column mr-30 w-84 h-p100" @click="onRemind">
            <image v-if="goodsDetail.is_msg" :src="ossIcon(`/auction/remind_h_40.png`)" class="w-40 h-40" />
            <image v-else :src="ossIcon(`/auction/remind_66.gif`)" class="w-40 h-40" />
            <text class="font-22 l-h-32" :class="goodsDetail.is_msg ? 'text-ff9127' : 'text-9'">提醒</text>
          </view>
          <view class="flex-c-c flex-column w-84 h-p100" @click="onComment">
            <image :src="ossIcon('/auction/comment_40.png')" class="w-40 h-40" />
            <text class="font-22 text-9 l-h-32">评论</text>
          </view>
        </view>
        <button
          class="vh-btn flex-c-c mr-24 w-352 h-64 font-wei-500 font-28 b-rad-32"
          :class="goodsDetail.onsale_status !== MAuctionGoodsStatus.OnAuction ? 'text-d8d8d8 bg-ffffff b-s-02-d8d8d8' : 'text-ffffff bg-e80404'"
          @click="handleBid"
        >{{ goodsDetail.onsale_status === MAuctionGoodsStatus.AuctionAbort ? '拍卖已结束' : '我要出价' }}</button>
      </view>
    </view>

    <AuctionWarnPopup v-model="warnPopupVisible" @agree="onAuctionWarnAgree" />
    <AuctionEarnestPayPopup ref="auctionEarnestPayPopupRef" v-model="earnestPayPopupVisible" :goods="goodsDetail" :earnestCouponInfo="earnestCouponInfo" @orderCreateSuccess="onEarnestOrderCreateSuccess" @orderCreateError="onEarnestOrderCreateError" />
    <AuctionEarnestPaySelectPopup ref="auctionEarnestPaySelectPopupRef" v-model="earnestPaySelectPopupVisible" :orderInfo="earnestOrderInfo" @paySuccess="onEarnestOrderPaySuccess" />
    <AuctionEarnestPaySuccessPopup v-model="earnestPaySuccessPopupVisible" />
    <AuctionBidPopup v-model="bidPopupVisible" :goods="goodsDetail" :isBidPrice="!!allBidRecordsLength" :anonymousName="anonymousName" :provinceName="provinceName" />
    <AuctionBidSuccessPopup v-model="bidSuccessPopupVisible" />
    <AuctionPAWarnPopup v-model="PAWarnPopupVisible" isBuyer />
    <AEUseCouponPopup v-model="EUseCouponPopupVisible" @cancel="onAEUseCouponCanel" @use="onAEUseCouponUse"></AEUseCouponPopup>
    <AuctionFreeShipmentPopup v-model="freeShipmentPopupVisible"></AuctionFreeShipmentPopup>
    <AuctionReversePricePopup v-model="reversePricePopupVisible"></AuctionReversePricePopup>
  </view>
</template>

<script>
import { MAuctionGoodsStatus, MAuctionEarnestType, MAuctionRemindOperation } from '@/common/js/utils/mapperModel'
import { MAuctionGoodsStatusObjMapper } from '@/common/js/utils/mapper'
import { AUCTION_SOCKET_URL } from '@/common/js/fun/constant.js'
import sjcl from 'sjcl'
import { mapState, mapGetters, mapMutations, mapActions } from 'vuex'

let goodsCommentsObserver = null
let goodsRecommendsObserver = null

export default {
  name: 'auctionGoodsDetail', // 拍品详情
  data: () => ({
    MAuctionGoodsStatus,
    loading: true,
    id: '',
    goodsDetail: {},
    swiperIndex: 0,
    isLogin: false,
    loginInfo: {},
    bidRecords: [],
    allBidRecordsLength: 0,
    warnPopupVisible: false,
    earnestPayPopupVisible: false,
    earnestPaySelectPopupVisible: false,
    earnestPaySuccessPopupVisible: false,
    bidPopupVisible: false,
    bidSuccessPopupVisible: false,
    EUseCouponPopupVisible: false,
    PAWarnPopupVisible: false,
    freeShipmentPopupVisible: false,
    reversePricePopupVisible: false,
    earnestOrderInfo: null,
    earnestPayStatus: false,
    earnestStatus: 0,
    earnestOrderNo: '',
    anonymousName: '',
    provinceName: '',
    priceChangeToastParams: {
      duration: 1.5 * 1000,
      visible: false,
      timer: null
    },
    isExistPayeeAccount: false,
    isShowFixedTime: false,
    isLoadedGoodsComments: false,
    isLoadedGoodsRecommends: false,
    earnestCouponInfo: {},
  }),
  computed: {
    ...mapState(['routeTable', 'userInfo']),
    ...mapGetters('auctionUser', ['isDisabledAuctionUser']),
    isPendPutaway ({ goodsDetail }) {
      return goodsDetail.onsale_status === MAuctionGoodsStatus.PendPutaway
    },
    goodsStatusObj ({ goodsDetail }) {
      return MAuctionGoodsStatusObjMapper[goodsDetail.onsale_status] || MAuctionGoodsStatusObjMapper[MAuctionGoodsStatus.Unsuccessful]
    },
    goodsStatusText ({ goodsStatusObj }){
      return goodsStatusObj.statusText
    },
    goodsStatusBg ({ goodsStatusObj }) {
      return goodsStatusObj.statusBg
    },
    goodsStatusTimeText ({ goodsStatusObj }) {
      return goodsStatusObj.timeText
    },
    goodsStatusTimeTextClazz ({ goodsStatusObj }) {
      return goodsStatusObj.timeTextClazz
    },
    goodsStatusTimeTextBgClazz ({ goodsStatusObj }) {
      return goodsStatusObj.timeTextBgClazz
    },
    goodsStatusTimeValue ({ goodsDetail, goodsStatusObj }) {
      return goodsDetail[goodsStatusObj.timeKeyValue]
    },
    fixedTimeShowTop () {
      return uni.upx2px(464 + 70)
    },
    navbarAndFixedTimeHeight () {
      return 46 + uni.upx2px(70)
    },
    isExistReservePrice ({ goodsDetail }) {
      return !!goodsDetail.is_reserve_price
    }
  },
  methods: {
    ...mapMutations(['muAddressInfoState']),
    ...mapActions(['getUserInfo']),
    ...mapActions('auctionUser', ['getAuctionUserInfo']),
    async init () {
      try {
        const reqList = []
        if (this.isLogin) {
          await this.getUserInfo() // 出价需要用户的字段nickname、avatar_image，ios下storage没存
          this.loginInfo = uni.getStorageSync('loginInfo') || {}
          reqList.push(this.loadEarnestPayStatus(), this.getAuctionUserInfo())
        }
        reqList.push(this.loadAuctionGoodsDetail(), this.loadBidRecords())
        await Promise.all(reqList)
        this.loading = false
        this.connectSocket()
      } catch (err) {
        console.log('err', err)
        if (this.pages.getPageLength() > 1) this.jump.navigateBack()
      }
    },
    async loadUserForbidden () {
      const res = await this.$u.api.getAuctionUserInfo()
      const { status = 1 } = res?.data?.info || {}
      this.isForbidden = !status
    },
    async loadPayeeAccount () {
      // const res = await this.$u.api.searchAuctionPayeeAccounts()
      // const list = res?.data?.list || []
      // this.isExistPayeeAccount = !!list.length
    },
    async loadAuctionGoodsDetail (isSource = true) {
      const params = { id: this.id }
      if (isSource) params.source = 1
      if (this.userInfo.uid) params.uid = this.userInfo.uid
      const res = await this.$u.api.getAuctionGoodsDetail(params)
      const goodsDetail = res?.data || {}
      const { sell_time, closing_auction_time } = goodsDetail
      goodsDetail.sell_time = typeof sell_time === 'string' ? sell_time.replace(/-/g, '/') : sell_time
      goodsDetail.closing_auction_time = typeof closing_auction_time === 'string' ? closing_auction_time.replace(/-/g, '/') : closing_auction_time
      const { product_img = [], final_auction_price, price } = goodsDetail
      goodsDetail.$swiperList = product_img
      goodsDetail.final_auction_price = final_auction_price === '0.00' ? price : final_auction_price
      this.goodsDetail = goodsDetail
    },
    async loadBidRecords () {
      try {
        const res = await this.$u.api.getAuctionBidRecords({ id: this.id })
        const list = res?.data?.list || []
        this.bidRecords = list.slice(0, 3)
        this.allBidRecordsLength = list.length
      } catch (e) {
      }
    },
    async loadEarnestPayStatus () {
      if (!this.isLogin) return
      const res = await this.$u.api.getAuctionEarnestStatus({ type: MAuctionEarnestType.Bidding, goods_id: this.id })
      const { is_payment = 0, province_name, status = 0, main_order_no = '' } = res?.data || {}
      this.earnestPayStatus = !!is_payment
      this.anonymousName = `${province_name || '酒云'}用户`
      this.provinceName = province_name || '未知'
      this.earnestStatus = status
      this.earnestOrderNo = main_order_no
    },
    onShare () {
      const { id, title, brief, product_img } = this.goodsDetail
      this.jump.appShare({
        dataType: 1,
        title,
        des: brief,
        img: (product_img && product_img[0]) || '',
        path: `${this.routeTable.pHAuctionGoodsDetail}?id=${id}`
      })
    },
    onSwiperChange (index) {
      this.swiperIndex = index
    },
    onSwiperClick () {
      uni.previewImage({
        current: this.swiperIndex,
        indicator: true,
        urls: this.goodsDetail.$swiperList
      })
    },
    async onEnjoy () {
      const isLogin = await this.login.isLoginV3(this.$vhFrom)
      if (isLogin) {
        const { is_like } = this.goodsDetail
        if (is_like) {
          await this.$u.api.cancelEnjoyAuctionGoods({ goods_id: this.id })
          this.feedback.toast({ title: '已取消收藏' })
          this.goodsDetail.is_like = 0
        } else {
          await this.$u.api.addEnjoyAuctionGoods({ goods_id: this.id })
          this.feedback.toast({ title: '已添加至收藏' })
          this.goodsDetail.is_like = 1
        }
      }
    },
    async onRemind () {
      const isLogin = await this.login.isLoginV3(this.$vhFrom)
      if (isLogin) {
        const { is_msg } = this.goodsDetail
        const params = { uid: this.userInfo.uid, goods_id: this.id, type: is_msg ? MAuctionRemindOperation.Delete : MAuctionRemindOperation.Add }
        await this.$u.api.editRemindAuctionGoods(params)
        this.feedback.toast({ title: is_msg ? '已取消提醒' : '已添加至提醒' })
        this.goodsDetail.is_msg = is_msg ? 0 : 1
        let { msg_count = 0 } = this.goodsDetail
        this.goodsDetail.msg_count = is_msg ? --msg_count : ++msg_count
      }
    },
    async onComment () {
      const isLogin = await this.login.isLoginV3(this.$vhFrom)
      if (isLogin) {
        uni.createSelectorQuery().in(this).select('#auction-goods-comments').boundingClientRect(agcData => {
        uni.createSelectorQuery().in(this).select('#goods-detail').boundingClientRect(gdData => {
            uni.pageScrollTo({
              scrollTop: agcData.top - gdData.top - this.navbarAndFixedTimeHeight
            })
          }).exec()
        }).exec()
        this?.$refs?.auctionGoodsCommentsRef?.open()
      }
    },
    async handleBid () {
      const isLogin = await this.login.isLoginV3(this.$vhFrom)
      if (!isLogin) return
      if (this.isDisabledAuctionUser) {
        this.feedback.toast({ title: '账号异常，请联系客服' })
        return
      }
      // if (!this.isExistPayeeAccount) {
      //   this.PAWarnPopupVisible = true
      //   return
      // }
      if (this.goodsDetail.onsale_status !== MAuctionGoodsStatus.OnAuction) return
      if (this.earnestPayStatus) {
        this.bidPopupVisible = true
      } else {
        this.warnPopupVisible = true
      }
    },
    onEarnestOrderCreateSuccess (orderInfo) {
      this.earnestOrderInfo = orderInfo
      this.earnestPaySelectPopupVisible = true
    },
    onEarnestOrderCreateError () {
      this.earnestPayPopupVisible = false
    },
    onEarnestOrderPaySuccess () {
      this.earnestPayPopupVisible = false
      this.earnestPaySelectPopupVisible = false
      this.earnestPaySuccessPopupVisible = true
      this.earnestPayStatus = true
      this.earnestOrderInfo = this.$options.data().earnestOrderInfo // 防止onShow时一直queryPayStatus
    },
    connectSocket () {
      const { PendAuction, OnAuction } = MAuctionGoodsStatus
      if (![PendAuction, OnAuction].includes(this.goodsDetail.onsale_status)) return
      const uid = this.userInfo.uid || uni.getStorageSync('uniqueId') || this.$u.guid()
      const token = this.loginInfo.token || ''
      const timestamp = Date.now()
      const key = `${timestamp}${token}`
      const value = JSON.stringify({ action: 'reg', uid, auction_id: +this.id })
      const data =  sjcl.encrypt(key, value)
      console.log('key', key)
      console.log('data', data)
      let client = 4
      if (this.$android) client = 1
      else if (this.$ios) client = 2
      const msg = JSON.stringify({ timestamp, uid, client, data })
      console.log('msg', msg)
      const decryptData =  sjcl.decrypt(key, data)
      console.log('decryptData', decryptData, AUCTION_SOCKET_URL())
      uni.connectSocket({
        url: AUCTION_SOCKET_URL(),
      })
      uni.onSocketOpen(() => {
        uni.sendSocketMessage({
          data: msg
        })
        // this.loadAuctionGoodsDetail(false)
        // this.loadBidRecords()
        // this.loadEarnestPayStatus()
      })
      uni.onSocketMessage((res) => {
        console.log('收到服务器内容 res', res)
        if (res.data === 'heartbeats') {
          this.isSocketDeath()
          return
        }
        const dataJson = JSON.parse(res.data || '{}')
        const { code, data } = dataJson
        switch (code) {
          case 1002:
            this.onBidSuccess()
            break
          case 2000:
            this.onPriceChange(data)
            break
          case 2001:
            this.onAuctionDelay(data)
            break
          case 3000:
            this.onAuctionStart()
            break
          case 3001:
            this.onAuctionAbort()
            break
        }
      })
      uni.onSocketClose(() => {
        console.log('连接关闭')
      })
    },
    isSocketDeath () {
      this.heartBeatTimer && clearTimeout(this.heartBeatTimer)
      this.heartBeatTimer = setTimeout(() => {
        console.log('socket death')
        uni.closeSocket()
      }, 70 * 1000)
    },
    onBidSuccess () {
      this.bidSuccessPopupVisible = true
    },
    onPriceChange (data) {
      const { bid_price, uid } = data
      this.goodsDetail.final_auction_price = bid_price
      this.bidRecords = [data, ...this.bidRecords.slice(0, 2)]
      this.allBidRecordsLength++
      if (this.bidPopupVisible && +this.userInfo.uid !== +uid && !this.priceChangeToastParams.visible) {
        const duration = this.priceChangeToastParams.duration
        this.feedback.toast({ title: '当前价已变动', duration })
        this.toastVisible = true
        this.priceChangeToastParams.timer && clearTimeout(this.priceChangeToastParams.timer)
        this.priceChangeToastParams.timer = setTimeout(() => {
          this.priceChangeToastParams.visible = false
        }, duration)
      }
    },
    onAuctionDelay (data) {
      const { bid_end_time } = data
      const timestamp = +bid_end_time * 1000
      this.goodsDetail.closing_auction_time = timestamp
    },
    onAuctionStart () {
      this.goodsDetail.onsale_status = MAuctionGoodsStatus.OnAuction
    },
    onAuctionAbort () {
      this.goodsDetail.onsale_status = MAuctionGoodsStatus.AuctionAbort
      this.bidPopupVisible = false
      uni.closeSocket()
    },
    onClosePage () {
      uni.closeSocket()
      this.heartBeatTimer && clearTimeout(this.heartBeatTimer)
      this.priceChangeToastParams.timer && clearTimeout(this.priceChangeToastParams.timer)
      this.closeGoodsCommentsObserver()
      this.closeGoodsRecommendsObserver()
    },
    openGoodsCommentsObserver () {
      goodsCommentsObserver = uni.createIntersectionObserver(this)
			goodsCommentsObserver.relativeToViewport().observe('#auction-goods-comments', (res) => {
        if (res.intersectionRatio > 0) {
          if (!this.isLoadedGoodsComments) {
            this?.$refs?.auctionGoodsCommentsRef?.load()
          }
          this.isLoadedGoodsComments = true
				}
			})
    },
    closeGoodsCommentsObserver () {
      if (goodsCommentsObserver) {
        goodsCommentsObserver.disconnect()
        goodsCommentsObserver = null
      }
    },
    openGoodsRecommendsObserver () {
      goodsRecommendsObserver = uni.createIntersectionObserver(this)
			goodsRecommendsObserver.relativeToViewport().observe('#auction-goods-recommends', (res) => {
        if (res.intersectionRatio > 0) {
          if (!this.isLoadedGoodsRecommends) {
            this?.$refs?.auctionGoodsRecommendsRef?.load()
          }
          this.isLoadedGoodsRecommends = true
				}
			})
    },
    closeGoodsRecommendsObserver () {
      if (goodsRecommendsObserver) {
        goodsRecommendsObserver.disconnect()
        goodsRecommendsObserver = null
      }
    },
    onAuctionWarnAgree () {
      this.$u.api.getAuctionEarnestCouponList().then(res => {
        const list = res?.data || []
        this.warnPopupVisible = false
        if (list.length) {
          this.EUseCouponPopupVisible = true
          this.earnestCouponInfo = list[0]
        } else {
          this.earnestPayPopupVisible = true
          this.earnestCouponInfo = this.$options.data().earnestCouponInfo
        }
      })
    },
    onAEUseCouponCanel () {
      this.EUseCouponPopupVisible = false
      this.earnestCouponInfo = this.$options.data().earnestCouponInfo
      this.earnestPayPopupVisible = true
    },
    onAEUseCouponUse () {
      this.EUseCouponPopupVisible = false
      this.earnestPayPopupVisible = true
    },
  },
  onLoad (options) {
    const { id, aePaySuccess } = options
    this.id = id
    this.muAddressInfoState({}) // 进页面清空，防止持久化导致错乱
    if (aePaySuccess) { // 拍卖保证金支付成功
      this.onEarnestOrderPaySuccess()
      const { search } = location || {}
      window?.history?.replaceState(null, '', search.replace('aePaySuccess=1', ''))
    }
  },
  onShow () {
    this.login.isLoginV3(this.$vhFrom, 0).then(isLogin => {
      this.isLogin = isLogin
      this.init().finally(() => {
        if (this.isLogin) {
          if (this.earnestOrderInfo) {
            this?.$refs?.auctionEarnestPaySelectPopupRef?.queryPayStatus()
          }
          this?.$refs?.auctionGoodsCommentsRef?.loadAgreementStatus()
        }
        this.openGoodsCommentsObserver()
        this.openGoodsRecommendsObserver()
      })
      if (this.isLogin) this?.$refs?.auctionEarnestPayPopupRef?.initAddressInfo()
    })
  },
  onHide () {
    console.log('onHide')
    this.onClosePage()
  },
  onUnload () {
    console.log('onUnload')
    this.onClosePage()
  },
  onPullDownRefresh() {
    this.onClosePage()
    this?.$refs?.auctionGoodsCommentsRef?.load()
    this.init().finally(() => {
      uni.stopPullDownRefresh()
    })
  },
  onPageScroll (res) {
    if (!this.isPendPutaway) {
      this.isShowFixedTime = res.scrollTop > this.fixedTimeShowTop
    }
  },
}
</script>

<style lang="scss" scoped>
  .auction-gdetail {
    &__swiper {
      ::v-deep {
        .vh-indicator-item-round {
          margin: 0 6rpx;
          @include size(8rpx);
          background-color: #e0e0e0;

          &-active {
            width: 22rpx;
            background-color: #e80404;
            border-radius: 5rpx;
          }
        }
      }
    }
  }
</style>
