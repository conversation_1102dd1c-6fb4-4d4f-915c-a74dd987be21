<template>
  <view class="h-min-vh-100 bg-f5f5f5">
    <vh-navbar title="我的" height="46" :background="{ background: navbarBackground }" back-icon-color="#FFF" title-color="#FFF" :customStatusBarHeight="20" :customBack="$customBack" />
    <view v-if="!loading" class="pb-188">
      <image class="p-abso top-0 w-p100 h-536" :src="ossIcon('/auction/mine_bg_750_536.png')" />
      <view class="p-rela ptb-00-plr-24">
        <view class="p-rela mtb-00-mlr-auto mt-100 h-360 bg-ffffff b-rad-10">
          <image class="p-abso top-0 w-p100 h-360" :src="ossIcon('/auction/mine_card_702_360.png')" />
          <view class="auction-mine__cv flex-sb-c pl-16 pr-04 w-154 h-54" @click="jump.navigateTo(routeTable.pHAuctionCreditValues)">
            <text class="font-24 text-ffffff l-h-34">信用值</text>
            <view class="flex-c-c w-50 h-50 font-wei-500 font-24 text-e80404 l-h-34 bg-ffddcf b-rad-p50">{{ creditValues }}</view>
          </view>
          <view class="p-rela">
          <view class="flex-c-c">
            <view class="mt-n-86">
              <vh-image :src="userInfo.avatar_image" :loading-type="5" :width="172" :height="172" :shape="'circle'"/>
            </view>
          </view>
          <view class="flex-c-c mt-28">
            <text class="font-wei-500 font-32 text-3 l-h-44">{{ userInfo.nickname }}</text>
            <view class="p-rela d-flex ml-10">
              <image :src="ossIcon('/auction/lv_bg_82_28.png')" class="w-82 h-28" />
              <view style="top: -2rpx; left: 30rpx;" class="p-abso font-wei-500 font-24 text-e79a31 l-h-32">Lv{{ userInfo.user_level }}</view>
            </view>
          </view>
          <view class="flex-c-c mt-16">
            <view v-if="isRealName" class="flex-c-c w-112 h-42 font-wei-500 font-24 text-ff9127 b-rad-10 b-s-02-ff9127">已认证</view>
            <text v-else class="font-wei-500 font-24 text-6 l-h-34">您还未认证哦～</text>
          </view>
          <view class="flex-c-c mt-80 font-24 text-6">
            <view class="w-210 text-center" @click="jump.navigateTo(routeTable.pHAuctionEnjoy)">
              <text>收藏</text>
              <text class="ml-16">{{ statsInfo.goods_like_nums }}</text>
            </view>
            <view class="w-02 h-34 bg-d8d8d8"></view>
            <view class="w-226 text-center" @click="jump.navigateTo(routeTable.pHAuctionRemind)">
              <text>提醒</text>
              <text class="ml-16">{{ statsInfo.user_follow_nums }}</text>
            </view>
            <view class="w-02 h-34 bg-d8d8d8"></view>
            <view class="w-262 text-center" @click="jump.navigateTo(routeTable.pHAuctionCertificateList)">
              <text>我的证书</text>
              <text class="ml-16">{{ statsInfo.goods_cert_nums }}</text>
            </view>
          </view>
          </view>
        </view>

        <view v-if="!isRealName" class="p-rela mt-20 b-rad-10" @click="jump.navigateTo(routeTable.pHAuctionRealName)">
          <image :src="ossIcon('/auction/mine_bg_702_68.png')" class="p-abso w-702 h-68" />
          <view class="p-rela flex-s-c ptb-00-plr-24 h-68">
            <image :src="ossIcon('/auction/tips_28_30.png')" class="w-28 h-30"></image>
            <text class="ml-10 font-24 text-a24700">偷偷告诉你，实名认证后可以出售自己的藏品哦~</text>
          </view>
        </view>

        <view class="mt-18 ptb-00-plr-24 bg-ffffff b-rad-16">
          <view class="pt-40 h-116 font-wei-600 font-32 text-3">交易管理</view>
          <view class="h-02 bg-eeeeee"></view>
          <view class="d-flex flex-wrap">
            <view
              v-for="(item, index) in transactionList"
              :key="index"
              class="flex-c-c flex-column h-164"
              :class="[isRealName ? 'w-96' : 'w-168',  isRealName ? (index % 4 === 3 ? 'mr-0' : 'mr-90') : (index % 3 === 2 ? 'mr-0' : 'mr-75')]"
              @click="onJump(item)"
            >
              <view class="p-rela font-wei-600 font-32 text-3">
                <text>{{ statsInfo[item.keyValue] }}</text>
                <image v-if="item.isShowPendPay && pendPayCount" :src="ossIcon('/auction/pend_pay_88_36.png')" class="p-abso mt-n-16 ml-06 w-88 h-36" />
              </view>
              <text class="mt-06 font-24 text-6">{{ item.name }}</text>
            </view>
          </view>
        </view>

        <view v-if="isRealName && drafts.size" style="background: rgba(255,145,39,0.42);" class="flex-sb-c mt-20 ptb-00-plr-24 h-138 b-rad-16" @click="jump.navigateTo(routeTable.pHAuctionGoodsDrafts)">
          <view class="flex-c-c">
            <vh-image v-if="drafts.img" :loading-type="4" :src="drafts.img" :width="92" :height="98" :border-radius="6" />
            <view v-else class="flex-c-c bg-ffffff b-rad-10">
              <image :src="ossIcon('/auction/none_img_92_98.png')" class="w-92 h-98" />
            </view>
            <view class="ml-20">
              <view class="font-wei-600 font-28 text-ffffff l-h-40">{{ drafts.size }}个草稿待发布</view>
              <view class="mt-10 font-24 text-ffffff l-h-34">完善草稿箱，立即发布</view>
            </view>
          </view>
          <button class="vh-btn flex-c-c w-112 h-54 bg-ffffff font-wei-500 font-24 text-ff9127 b-rad-27">去看看</button>
        </view>

        <view class="mt-20 ptb-00-plr-24 bg-ffffff b-rad-16">
          <view class="pt-26 h-102 font-wei-600 font-32 text-3">我的工具</view>
          <view class="h-02 bg-eeeeee"></view>
          <view class="d-flex flex-wrap pb-28">
            <view v-for="(item, index) in toolList" :key="index" :style="{ marginRight: item.mr, marginTop: item.mt }" class="flex-c-c flex-column mt-40 w-114" @click="onJump(item)">
              <image :src="ossIcon(item.icon)" class="w-74 h-74"></image>
              <text class="mt-10 font-24 text-6">{{ item.name }}</text>
            </view>
          </view>
        </view>
      </view>
      <AuctionTabbar ref="auctionTabbarRef" />
    </view>
    <AuctionKefuPopup v-model="kefuPopupVisible" />
  </view>
</template>

<script>
import { MAuctionRNType } from '@/common/js/utils/mapperModel'
import { mapState, mapActions } from 'vuex'

export default {
  name: 'auctionMine', // 拍卖我的页面
  data: () => ({
    loading: true,
    navbarBackground: 'transparent',
    isRealName: false,
    transactionList: [],
    toolList: [
      { name: '保证金', icon: '/auction/tool1_74.png', pathName: 'pHAuctionEarnestList', mr: '66rpx' },
      { name: '实名认证', icon: '/auction/tool2_74.png', pathName: 'pHAuctionRealName', mr: '66rpx' },
      { name: '我发布的', icon: '/auction/tool3_74.png', pathName: 'pHAuctionMyGoodsList', mr: '66rpx', isCheck: true },
      { name: '资金明细', icon: '/auction/tool4_74.png', pathName: 'pHAuctionFundsListNew', mt: '36rpx' },
      { name: '专属客服', icon: '/auction/tool5_75.png', popupKeyValue: 'kefuPopupVisible'  },
    ],
    statsInfo: {
      goods_like_nums: 0,
      user_follow_nums: 0,
      goods_cert_nums: 0,
      i_buy_nums: 0,
      i_sale_nums: 0,
      evaluate_nums: 0,
      after_sale_nums: 0,
      seller_after_sale_nums: 0
    },
    creditValues: 0,
    pendPayCount: 0,
    drafts: {
      size: 0,
      img: ''
    },
    kefuPopupVisible: false,
  }),
  computed: {
    ...mapState(['routeTable', 'userInfo'])
  },
  methods: {
    ...mapActions(['getUserInfo']),
    async load () {
      await Promise.all([
        this.getUserInfo(),
        this.loadAuctionUserInfo(),
        this.loadStats(),
        this.loadCreditValues(),
        this.loadPendPayCount()
      ])
    },
    async loadAuctionUserInfo () {
      const res = await this.$u.api.getAuctionUserInfo()
      const { ac_type = 0 } = res?.data?.info || {}
      this.isRealName = MAuctionRNType.NotRN !== ac_type
      if (this.isRealName) {
        this.transactionList = [
          { name: '我拍到的', pathName: 'pHAuctionBuyerOrderList', keyValue: 'i_buy_nums', isShowPendPay: true },
          { name: '我卖出的', pathName: 'pHAuctionSellerOrderList', keyValue: 'i_sale_nums' },
          { name: '拍品评价', pathName: 'pHAuctionOrderEvaluateList', keyValue: 'evaluate_nums' },
          { name: '退货售后', pathName: 'pHAuctionBuyerAfterSaleList', keyValue: 'after_sale_nums' },
          { name: '卖家售后', pathName: 'pHAuctionSellerAfterSaleList', keyValue: 'seller_after_sale_nums' }
        ]
      } else {
        this.transactionList = [
          { name: '我拍到的', pathName: 'pHAuctionBuyerOrderList', keyValue: 'i_buy_nums', isShowPendPay: true },
          { name: '拍品评价', pathName: 'pHAuctionOrderEvaluateList', keyValue: 'evaluate_nums' },
          { name: '退货售后', pathName: 'pHAuctionBuyerAfterSaleList', keyValue: 'after_sale_nums' }
        ]
      }
    },
    async loadStats () {
      const res = await this.$u.api.getAuctionMineStats()
      const data = res?.data || {}
      this.statsInfo = Object.assign({}, this.statsInfo, data)
    },
    async loadCreditValues () {
      const res = await this.$u.api.userSpecifiedData({field: 'auction_credit_score'})
      this.creditValues = res?.data?.auction_credit_score || 0
    },
    async loadPendPayCount () {
      const res = await this.$u.api.auctionBuyerOrderList({ type: 1, page: 1, limit: 1 })
      this.pendPayCount = res?.data?.total || 0
    },
    loadDrafts () {
      const list = uni.getStorageSync('auctionGoodsDrafts') || []
      if (list.length) {
        const imgList = list[0].product_img.split(',')
        this.drafts = { size: list.length, img: imgList[0] }
      } else {
        this.drafts = this.$options.data().drafts
      }
    },
    async onJump (item) {
      const { pathName, isCheck, popupKeyValue } = item
      if (isCheck) {
        this.$refs.auctionTabbarRef.funHiddenPopupVisible = true
        return
        if (!this.isRealName) {
          this.$refs.auctionTabbarRef.RNWarnPopupVisible = true
          return
        } else {
          // const res = await this.$u.api.searchAuctionPayeeAccounts()
          // const list = res?.data?.list || []
          // if (!list.length) {
          //   this.$refs.auctionTabbarRef.PAWarnPopupVisible = true
          //   return
          // }
        }
      }
      if (pathName) this.jump.navigateTo(this.routeTable[pathName])
      if (popupKeyValue) {
        if (popupKeyValue === 'kefuPopupVisible' && this.$app) {
          wineYunJsBridge.openAppPage({
            client_path: { "ios_path":"ShopDetailkefu", "android_path": "shangpin.kefu" }, 
          })
          return
        }
        this[popupKeyValue] = !this[popupKeyValue]
      }
    }
  },
  onShow () {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.load().finally(() => {
        this.loading = false
      })
      this.loadDrafts()
    })
  },
  onPullDownRefresh() {
    this.load().finally(() => {
      uni.stopPullDownRefresh()
    })
  },
  onPageScroll (res) {
    if(res.scrollTop <= 100){
      this.navbarBackground = `rgba(224, 20, 31, ${res.scrollTop / 100})`
    }else{
      this.navbarBackground = `rgba(224, 20, 31, 1)`
    }
  }
}
</script>

<style lang="scss" scoped>
  .auction-mine {
    &__cv {
      position: absolute;
      top: -30rpx;
      left: -24rpx;
      transform: translateY(-100%);
      background: linear-gradient(90deg, #FF8A47 0%, #FFB074 100%);
      border-radius: 0 35rpx 35rpx 0;

      > view {
        box-shadow: 0 0 4rpx 2rpx rgba(216, 80, 23, 0.3);
      }
    }
  }
</style>
