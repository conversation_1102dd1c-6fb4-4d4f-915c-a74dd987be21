<template>
  <view :class="['h-min-p100', list.length ? 'bg-f5f5f5' : '']">
    <vh-navbar title="我买过的" height="46" show-border :borderStyle="{ borderBottom: '2rpx solid #eee' }"></vh-navbar>
    <view class="bg-ffffff">
      <view class="flex-c-c ptb-00-plr-24 h-110">
        <view class="flex-1 flex-c-c pl-26 pr-18 h-68 bg-f7f7f7 b-rad-40">
          <image class="w-36 h-36" :src="ossIcon('/comm/search_36.png')" />
					<input class="flex-1 ml-08 h-p100 font-28 text-3" type="text" v-model="query.keyword" placeholder="输入您要查询的内容" placeholder-style="color:#999;font-size:28rpx;" @confirm="load"/>
					<image v-if="query.keyword" class="p-08 w-40 h-40" :src="ossIcon('/comm/del_gray.png')" @click="query.keyword = ''" />
        </view>
        <view class="flex-c-c ml-24 w-68 h-68 font-wei-500 font-28 text-6" @click="reload">搜索</view>
      </view>
      <!-- <view class="tabs">
        <u-tabs :list="tabsList" :current="currentTabIndex" height="92" font-size="32" active-color="#e80404" inactive-color="#666" bar-width="36" bar-height="8" gutter="24" @change="changeTabIndex" />
      </view> -->
    </view>
    <view v-if="!loading">
      <view v-if="list.length" class="ptb-20-plr-24">
        <view v-for="item in list" :key="item.period" class="p-rela d-flex j-sb mb-20 ptb-28-plr-20 pt-70 bg-ffffff b-rad-10">
          <view class="p-abso top-16 left-20 font-wei-500 font-28 text-6 l-h-40">{{ item.created_time }}</view>
          <vh-image :loading-type="4" :src="item.banner_img" :width="152" :height="152" :border-radius="6" />
          <view class="flex-1 d-flex flex-column j-sb ml-20">
            <view class="font-wei-500 font-24 text-3 l-h-34">{{ item.title }}</view>
            <view class="flex-sb-c">
              <view class="font-24 text-e80404 l-h-34">
                <text>买入价</text>
                <text class="ml-06">¥<text class="font-32">{{ item.payment_amount }}</text></text>
              </view>
              <button
                class="vh-btn flex-c-c w-148 h-50 font-wei-500 font-24 text-ffffff bg-e80404 b-rad-28"
                @click="onJumpAuctionGoodsCreate(item)"
              >一键转拍</button>
            </view>
          </view>
        </view>
      </view>
      <view v-else class="pt-120">
        <AuctionNone title="空空如也" desc="暂无可转拍商品，快去商城选购吧" />
      </view>
    </view>
  </view>
</template>

<script>
import listMixin from '@/common/js/mixins/listMixin'
import auctionMyCreateDraftsUtil from '@/common/js/utils/auctionMyCreateDrafts'

export default {
  mixins: [listMixin],
  data: () => ({
    query: {
      type: 0,
      keyword: ''
    },
    tabsList: [
      { name: '全部', type: 0 },
      { name: '一个月内', type: 1 },
      { name: '半年内', type: 2 },
      { name: '一年内', type: 3 },
      { name: '一年前', type: 4 }
    ],
  }),
  onLoad () {
    if (this.login.isLogin()) {
      this.load()
    }
  },
  methods: {
    async load (query, currentTabIndex) {
      query.type = this.tabsList[currentTabIndex].type
      const res = await this.$u.api.searchAuctionMyBuyingList(query)
      const { list = [] } = res?.data || {}
      this.list = query.page === 1 ? list : this.list.concat(list)
      return res
    },
    onJumpAuctionGoodsCreate (item) {
      const draftId = auctionMyCreateDraftsUtil.getDraftIdByOrderNo(item.sub_order_no)
      if (draftId) {
        this.jump.navigateTo(`${this.$routeTable.pHAuctionGoodsCreateNew}?draftId=${draftId}`)
        return
      }
      this.jump.navigateTo(`${this.$routeTable.pHAuctionGoodsCreateNew}?isWine=1&period=${item.period}&periodType=${item.order_type}&mainOrderNo=${item.sub_order_no}&orderType=${item.order_type}&packageId=${item.package_id}&buyingPrice=${item.payment_amount}`)
    }
  },
  onPullDownRefresh() {
    this.pullDownRefresh()
  },
  onReachBottom () {
    this.reachBottomLoad()
  },
}
</script>

<style>
  page {
    height: 100%;
	}
</style>

<style lang="scss" scoped>
  .tabs {
    ::v-deep {
      .u-tab {
        &-bar {
          bottom: auto;
        }
      }
    }
  }
</style>