<template>
  <view>
    <vh-navbar title="实名认证" height="46"></vh-navbar>
    <view v-if="!loading">
      <view v-if="rnInfo.status === MAuctionRNStatus.OnCheck" class="pt-332">
        <view class="flex-c-c">
          <image :src="ossIcon('/auction/rn_on_check_440_400.png')" class="w-440 h-400" />
        </view>
        <view class="mt-60 font-wei-500 font-36 text-3 l-h-66 text-center">审核中</view>
        <view class="mt-20 font-28 text-6 l-h-44 text-center">实名认证审核中, 请兔子君耐心等待哦～</view>
      </view>
      <view v-else-if="rnInfo.status === MAuctionRNStatus.Passed" class="pt-200">
        <view class="flex-c-c">
          <image :src="ossIcon('/auction/rn_passed_442_400.png')" class="w-442 h-400" />
        </view>
        <view class="mt-72 font-wei-500 font-36 text-3 l-h-50 text-center">您已完成实名认证</view>
        <view class="mt-28 font-28 text-6 l-h-40 text-center">{{ rnInfo.name | toAsteriskStr }}  {{ rnInfo.id_card | toAsteriskStr }}</view>
      </view>
      <view v-else-if="rnInfo.status === MAuctionRNStatus.Rejected" class="pt-332">
        <view class="flex-c-c">
          <image :src="ossIcon('/auction/rn_reject_440_400.png')" class="w-440 h-400" />
        </view>
        <view class="mt-60 font-wei-500 font-26 text-3 l-h-50 text-center">很抱歉，实名认证未通过</view>
        <view class="mt-20 font-28 text-6 l-h-40 text-center">{{ rnInfo.reject_reason }}</view>
        <view class="flex-c-c mt-230">
          <button class="vh-btn flex-c-c w-646 h-80 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-40" @click="rnInfo.status = MAuctionRNStatus.NotSubmit">重新认证</button>
        </view>
      </view>
      <view v-else>
        <view class="p-32 font-28 text-6 l-h-40">为保障平台交易安全和对您进行合法有效的保护，根据《中华人民共和国网络安全法》《中华人民共和国未成年人保护法》等相关法律法规，以确保您实名认证信息与您本人真实身份信息的一致性，身份认证由阿里云提供的API认证接口实现对您身份的肴效核定。</view>
        <view class="mt-40 ptb-00-plr-32">
          <image :src="ossIcon('/auction/editor_34.png')" class="w-34 h-34" />
          <view class="flex-s-c h-110 bb-s-02-eeeeee">
            <view class="w-174 font-wei-500 font-32 text-3">真实姓名</view>
            <input v-model="rnInfo.name" placeholder="*请输入您的真实姓名" placeholder-style="font-size:28rpx; color:#999" class="flex-1 font-32 text-3 text-right" />
          </view>
          <view class="flex-s-c h-110 bb-s-02-eeeeee">
            <view class="w-174 font-wei-500 font-32 text-3">身份证号</view>
            <input v-model="rnInfo.id_card" placeholder="*请输入您的身份证号码" placeholder-style="font-size:28rpx; color:#999" class="flex-1 font-32 text-3 text-right" />
          </view>
          <view class="p-fixed left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-9999">
            <button class="vh-btn flex-c-c w-646 h-80 font-wei-500 font-28 text-ffffff b-rad-40" :class="disabled ? 'bg-fce4e3' : 'bg-e80404'" @click="onSubmit">提交审核</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionRNType, MAuctionRNStatus } from '@/common/js/utils/mapperModel'

export default {
  name: 'auctionRNPersional', // 拍卖个人认证
  data: () => ({
    MAuctionRNStatus,
    loading: true,
    rnInfo: {
      type: MAuctionRNType.PersonalRN,
      name: '',
      id_card: '',
    },
  }),
  computed: {
    disabled ({ rnInfo }) {
      return !(rnInfo.name && rnInfo.id_card)
    }
  },
  filters: {
    toAsteriskStr (input) {
      const len = input.length
      if (len === 2) {
        return `*${input[len - 1]}`
      }
      return `${input[0]}${Array(len - 2).fill('*').join('')}${input[len - 1]}`
    }
  },
  methods: {
    async load () {
      const res = await this.$u.api.getAuctionRNInfo({ type: MAuctionRNType.PersonalRN })
      const data = res?.data || {}
      this.rnInfo = Object.assign({}, this.rnInfo, data)
    },
    async onSubmit () {
      if (this.disabled) return
      const { id, type, name, id_card } = this.rnInfo
      if (!this.$u.test.idCard(id_card)) {
        this.feedback.toast({ title: '请填写正确的身份证号码' })
        return
      }
      this.feedback.loading({ title: '提交审核中...' })
      const params = { type, name, id_card }
      if (id) params.id = id
      if (id) {
        await this.$u.api.editAuctionRNInfo(params)
      } else {
        await this.$u.api.addAuctionRNInfo(params)
      }
      this.load()
    },
  },
  onLoad () {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.load().finally(() => {
        this.loading = false
      })
    })
  }
}
</script>

<style lang="scss" scoped>
</style>
