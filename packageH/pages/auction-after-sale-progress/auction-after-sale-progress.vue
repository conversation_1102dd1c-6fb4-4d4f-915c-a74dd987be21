<template>
	<view class="content pb-20">
		<!-- 导航栏 -->
		<vh-navbar title='售后进度' :show-border="true"/>
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="">
			<!-- 退款金额 -->
			<view class="bg-ffffff mt-20 mr-24 ml-24 ptb-00-plr-20">
				<view class="flex-column flex-c-c pt-40 pb-60">
					<view class="font-wei text-e80404 l-h-50">
						<text class="font-36">¥</text>
						<text class="font-72">{{ afterSaleProgressInfo.refund_money }}</text>
					</view>
					<view class="font-28 text-6">退款金额</view>
				</view>
				<view class="flex-sb-c pb-32">
					<text class="font-28 font-wei text-6">退回方向</text>
					<text class="font-24 text-3">原路退回</text>
				</view>
				<view class="flex-sb-c bt-s-01-eeeeee ptb-32-plr-00">
					<text class="font-28 font-wei text-6">订单编号</text>
					<view class="" @click="copy.copyText(afterSaleProgressInfo.order_no)">
						<text class="font-24 text-3">{{ afterSaleProgressInfo.order_no }}</text>
						<text class="bg-eeeeee b-rad-18 ml-20 ptb-02-plr-10 font-18 text-3">复制</text>
					</view>
				</view>
			</view>
			
			<!-- 退款进度 -->
			<view v-if="afterSaleProgressInfo.speed && afterSaleProgressInfo.speed.length" class="list">
			  <view v-for="(item, index) in afterSaleProgressInfo.speed" :key="index" class="list__item">
				<view class="list__item-title" :class="index === 0 ? 'text-e80404' : 'text-9'" @click="">
				  <text class="font-wei">{{ item.title }}</text>
				  <text class="ml-20">{{ item.time }}</text>
				</view>
				<view v-if="item.msg" class="list__item-content">
				  <text>{{ item.msg }}</text>
				</view>
				<view class="list__item-timeline">
				  <view class="list__item-dot"></view>
				</view>
			  </view>
			</view>
		</view>
		
		<!-- 骨架屏 -->
		<vh-skeleton v-else bgColor="#FFF" :showLoading="false"/>
	</view>
</template>

<script>
	export default {
		name: 'auction-after-sale-progress',
		
		data() {
			return {
				loading: true, //数据是否加载完成
				refundOrderNo: '', //退款单号
				afterSaleProgressInfo: {}, //退款进度信息
			}
		},
		
		onLoad(options) {
			this.refundOrderNo = options.refundOrderNo
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) this.init()
			})
		},
		
		methods: {
			// 初始化板块
			async init() {
				await this.getAfterSaleProgressInfo()
				this.loading = false
			},
			
			// 获取售后进
			async getAfterSaleProgressInfo() {
				const res = await this.$u.api.auctionAfterSalesSpeed({refund_order_no: this.refundOrderNo})
				this.afterSaleProgressInfo = res?.data || {}
			}
		}
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>

<style lang="scss" scoped>
  .list {
	background-color: #FFF;
	border-radius: 10rpx;
	margin: 20rpx 24rpx 20rpx 24rpx;
	padding: 32rpx 26rpx;

    &__item {
      position: relative;
      padding: 0 32rpx 50rpx 52rpx;

      &:first-of-type {
        .list {
          &__item-dot {
            background: #FACECE;

            &::before {
              background: #E80404;
            }
          }
        }
      }

      &:last-of-type {
        .list {
          &__item-timeline {
            &::before {
              display: none;
            }
          }
        }
      }

      &-title {
        @include flex-row(flex-start);
        // @include font(500, 28rpx, #e80404);
        line-height: 40rpx;
      }

      &-content {
        @include flex-row(space-between);
        margin: 12rpx 0 0 0;
        @include font(400, 24rpx, #999);
        line-height: 34rpx;
      }

      &-timeline {
        position: absolute;
        top: 0;
        left: 0;
        @include size(28rpx, 100%);

        &::before {
          content: '';
          position: absolute;
          top: 12rpx;
          left: 50%;
          transform: translateX(-50%);
          display: block;
          @include size(2rpx, 100%);
          background: #eee;
        }
      }

      &-dot {
        position: absolute;
        top: 6rpx;
        @include flex-row;
        @include size(26rpx);
        background: transparent;
        border-radius: 50%;

        &::before {
          content: '';
          display: block;
          @include size(14rpx);
          background: #ccc;
          border-radius: 50%;
        }
      }
    }
  }
</style>