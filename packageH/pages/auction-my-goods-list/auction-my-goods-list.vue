<template>
  <view :class="goodsList.length ? 'h-min-vh-100 bg-f5f5f5' : ''">
    <vh-navbar title="我的拍品" height="46"></vh-navbar>
    <view v-if="!loading">
      <view class="auction-mglist__tabs flex-c-c bg-ffffff p-stic" style="top: 46px;">
        <u-tabs :list="tabsList" :current="currentTabIndex" height="92" font-size="32" active-color="#e80404" inactive-color="#666" bar-width="36" bar-height="8" gutter="22" @change="onTabChange" />
      </view>
      <view v-if="goodsList.length" class="d-flex flex-wrap ptb-20-plr-24">
        <view
          v-for="(item, index) in goodsList"
          :key="index"
          class="flex-sb-c flex-column mb-20 p-20 w-340 h-478 bg-ffffff b-rad-10"
          :class="index % 2 ? '' : 'mr-22'"
          @click="jump.navigateTo(`${routeTable.pHAuctionMyGoodsDetail}?id=${item.id}`)"
        >
          <view class="w-p100">
            <vh-image :loading-type="4" :src="item.product_img && item.product_img[0]" :width="300" :height="300" :border-radius="6" />
            <view class="mt-20 font-wei-500 font-24 text-3 l-h-34 text-hidden-2">{{ item.title }}</view>
          </view>
          <view class="flex-sb-c w-p100">
            <text class="font-24 text-3">卖家评估价</text>
            <text class="font-wei-500 font-32 text-e80404"><text class="font-18">¥</text>{{ item.quote }}</text>
          </view>
        </view>
      </view>
      <view v-else class="pt-108">
        <view class="flex-c-c">
          <image :src="ossIcon('/auction/none_440_400.png')" class="w-440 h-400" />
        </view>
        <view class="mt-72 font-wei-500 font-36 text-3 l-h-50 text-center">空空如也</view>
        <view class="mt-20 font-28 text-6 l-h-40 text-center">别急早晚会有的～</view>
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionGoodsStatus, MAuctionGoodsReviewStatus } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'

export default {
  name: 'auctionMyGoodsList', // 我的拍品
  data: () => ({
    loading: true,
    tabsList: [
      { name: '审核中', reviewStatus: MAuctionGoodsReviewStatus.PendBind },
      { name: '待拍卖', status: MAuctionGoodsStatus.PendAuction },
      { name: '正在拍', status: MAuctionGoodsStatus.OnAuction },
      { name: '已结束', status: `${MAuctionGoodsStatus.Disabled},${MAuctionGoodsStatus.AuctionAbort},${MAuctionGoodsStatus.Unsuccessful}` },
      { name: '被驳回', reviewStatus: MAuctionGoodsReviewStatus.Rejected }
    ],
    currentTabIndex: 0,
    query: { page: 1, limit: 10, uid: 0, status: '', reviewStatus: '' },
    totalPage: 0,
    goodsList: [],
    draftGoodsId: 0,
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    async load () {
      const { page, limit, uid, status, reviewStatus } = this.query
      const params = { page, limit, uid }
      if (status !== '') params.onsale_status = status
      if (reviewStatus !== '') params.onsale_review_status = reviewStatus
      if (reviewStatus !== MAuctionGoodsReviewStatus.Rejected) params.entrusted_margin = 1
      const res = await this.$u.api.searchAuctionMyGoodsList(params)
      const { list = [], total = 0 } = res?.data || {}
      this.goodsList = this.query.page === 1 ? list : this.goodsList.concat(list)
      this.totalPage = Math.ceil(total / this.query.limit)
    },
    onTabChange (index) {
      this.currentTabIndex = index
      const { status = '', reviewStatus = '' } = this.tabsList[this.currentTabIndex]
      const { page, limit } = this.$options.data().query
      this.query = Object.assign({}, this.query, { page, limit, status, reviewStatus })
      this.load()
    }
  },
  onLoad (options) {
    const loginInfo = uni.getStorageSync('loginInfo') || {}
    const { status = '', reviewStatus = '' } = this.tabsList[this.currentTabIndex]
    this.query = Object.assign({}, this.query, { uid: loginInfo.uid, status, reviewStatus })
    this.draftGoodsId = options.draftGoodsId
    if (this.draftGoodsId) {
      const { search } = location || {}
      window?.history?.replaceState(null, '', search.replace(`draftGoodsId=${this.draftGoodsId}`, ''))
    }
  },
  onShow () {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.load().finally(() => {
        this.loading = false
      })
      const auctionGoodsDrafts = uni.getStorageSync('auctionGoodsDrafts') || []
      const findIndex = auctionGoodsDrafts.findIndex(item => item.id === +this.draftGoodsId)
      if (findIndex !== -1) {
        auctionGoodsDrafts.splice(findIndex, 1)
        uni.setStorageSync('auctionGoodsDrafts', auctionGoodsDrafts)
      }
    })
  },
  onReachBottom () {
    if (this.query.page === this.totalPage || !this.totalPage) return
    this.query.page++
    this.load()
  }
}
</script>

<style lang="scss" scoped>
  .auction-mglist__tabs {
    ::v-deep {
      .u-tab {
        &-item {
          font-weight: 600 !important;
          vertical-align: top;
        }

        &-bar {
          bottom: auto;
          background: linear-gradient(214deg, #FF8383 0%, #E70000 100%);
        }
      }
    }
  }
</style>
