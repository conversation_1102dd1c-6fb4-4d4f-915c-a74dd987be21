<template>
  <view :class="['h-min-p100', list.length ? 'bg-f5f5f5' : '']">
    <vh-navbar title="草稿箱" height="46" :showBorder="!list.length" />
    <view v-if="list.length" class="ptb-00-plr-24">
      <view class="flex-sb-c pt-38 pb-32">
        <view class="font-28 text-3 l-h-40">{{ list.length }}个草稿待发布</view>
        <view class="font-wei-500 font-28 text-9 l-h-40">保存最近50个</view>
      </view>
      <view>
        <view v-for="(item, index) in list" :key="index" class="mb-20 ptb-28-plr-20 bg-ffffff b-rad-10">
          <view class="d-flex">
            <vh-image v-if="item.product_img" :loading-type="4" :src="item.product_img && item.product_img.split(',')[0]" :width="152" :height="152" :border-radius="6" />
            <image v-else :src="ossIcon('/auction/none_img_120.png')" class="w-152 h-152" />
            <view class="flex-1 d-flex flex-column j-sb ml-20 o-hid">
              <view class="font-wei-500 font-28 text-3 l-h-34 text-hidden-2">{{ item.title }}</view>
              <view class="font-24 text-9 l-h-34 text-hidden">{{ item.brief || '完善信息，立即发布～' }}</view>
            </view>
          </view>
          <view class="mt-28 mb-28 h-02 bg-eeeeee"></view>
          <view class="flex-e-c">
            <button class="vh-btn flex-c-c w-148 h-52 font-wei-500 font-24 text-6 bg-ffffff b-s-01-666666 b-rad-40" @click="onDelete(index)">删除</button>
            <button class="vh-btn flex-c-c ml-20 w-148 h-52 font-wei-500 font-24 text-6 bg-ffffff b-s-01-666666 b-rad-40" @click="onEdit(item)">编辑</button>
          </view>
        </view>
      </view>
    </view>
    <view v-else class="pt-198">
      <AuctionNone title="空空如也" />
    </view>
    <u-popup v-model="popupVisible" mode="center" width="552rpx" height="414rpx" border-radius="20">
      <view class="p-rela w-552 h-414">
        <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-552 h-414" />
        <view class="p-rela pt-86">
          <view class="font-wei-500 font-32 text-3 l-h-44 text-center">确认删除这个草稿？</view>
          <view class="mt-20 font-28 text-3 l-h-34 text-center">这可是兔子君辛苦编辑的呢</view>
          <view class="flex-c-c mt-80">
            <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32" @click="onConfirm">确认</button>
            <button class="vh-btn flex-c-c ml-60 w-180 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="popupVisible = false">取消</button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import auctionMyCreateDraftsUtil from '@/common/js/utils/auctionMyCreateDrafts'

export default {
  data: () => ({
    list: [],
    currentIndex: 0,
    popupVisible: false
  }),
  methods: {
    onDelete (index) {
      this.currentIndex = index
      this.popupVisible = true
    },
    onEdit (item) {
      this.jump.navigateTo(`${this.$routeTable.pHAuctionGoodsCreateNew}?draftId=${item.id}`)
    },
    onConfirm () {
      this.list.splice(this.currentIndex, 1)
      auctionMyCreateDraftsUtil.saveDraftList(this.list)
      this.popupVisible = false
    }
  },
  onShow () {
    this.list = auctionMyCreateDraftsUtil.getDraftList()
  }
}
</script>

<style>
  page {
    height: 100%;
	}
</style>

<style lang="scss" scoped>
</style>
