<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<vh-navbar title='评价' :show-border="true"/>
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in">
			<!-- tabs选项栏 -->
			<view class="p-stic z-980" :style="{top: system.navigationBarHeight() + 'px'}">
				<u-tabs :list="tabList" :current="currentTabs" :height="92" :font-size="28" inactive-color="#333" active-color="#E80404"
				 :bar-width="36" :bar-height="8" :bar-style="{ background:'linear-gradient(214deg, #FF8383 0%, #E70000 100%)' }" :is-scroll="false" @change="changeTabs" />
			</view>
			
			<!-- 列表 -->
			<view class="">
				<view v-if="evaluateList.length" class="ptb-20-plr-00">
					<view class="bg-ffffff b-rad-20 mb-20 mr-24 ml-24 ptb-28-plr-20" v-for="( item, index ) in evaluateList" :key="index">
						<view class="d-flex j-sb">
							<view class="d-flex a-center">
								<vh-image :loading-type="5" :src="item.avatar_image" :width="88" :height="88" shape="circle" />
								<view class="ml-10">
									<view class="d-flex a-center">
										<text class="font-28 font-wei text-2d2d2d l-h-40">{{ item.nickname }}</text>
										<VhIconGrade :grade="item.user_level"/>
									</view>
									
									<view class="d-flex mt-10">
										<image class="w-20 h-20 mr-02" :src="ossIcon(`/auction_order_evaluate_list/star_${ item.grade > index1 ? 'ora' : 'gra'}.png`)" mode="aspectFill" v-for="( item1, index1 ) in 5" :key="index1"></image>
									</view>
								</view>
							</view>
							
							<view class="font-24 text-9">{{ item.created_time }}</view>
						</view>
						
						<view class="mt-28 font-24 text-3 l-h-40">{{ item.content }}</view>
						
						<view v-if="item.images.length" class="d-flex flex-nowrap a-center">
							<view class="w-210 h-210 b-rad-10 mt-20 mr-18" v-for="(item1, index1) in item.images" :key="index1" @click="image.previewImageList(item.images, index1)">
								<vh-image :loading-type="2" :src="item1" :width="210" :height="210" :border-radius="10" />
							</view>
						</view>
						
						<view class="flex-sb-c mt-28">
							<text class="bg-fff0e2 ptb-02-plr-22 b-rad-26 font-24 text-ff9127 l-h-34">{{ item.category_name }} / {{ item.net_content }}L /{{ item.alcoholic_strength}}度</text>
							
							<view class="flex-c-c" @click="thumbsUp(index)">
								<image class="w-26 h-26" :src="ossIcon(`/comm/${item.like_status === 1 ? '' : 'u'}zan.png`)" mode="widthFix"></image>
								<text class="ml-06 font-24" :class="item.like_status === 1 ? 'text-e80404' : 'text-9'">{{ item.like_nums }}</text>
							</view>
						</view>
						
					</view>
					<u-loadmore :status="loadStatus" />
				</view>
				
				<!-- 列表为空 -->
				<AuctionEmpty v-else :imageSrc="ossIcon(`/auction_empty/emp1.png`)" :text="'暂无数据'" :paddingTop="160" :paddingBottom="780"/>
			</view>
		</view>
		
		<!-- 骨架屏 -->
		<vh-skeleton v-else bgColor="#FFF" :showLoading="false"/>
	</view>
</template>

<script>
	export default {
		name: 'auction-order-evaluate',
		data() {
			return {
				loading: true, //数据是否加载完成
				tabList: [ //状态栏选项
					{ name: '我买到的' }, 
					{ name: '我卖出的' }, 
				],
				currentTabs: 0, //当前选中tabs
				hasGotEvaluateList: 0, //是否请求过订单列表
				evaluateList: [], //评价列表
				page: 1, //当前页
				limit: 10, //每页限制多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //加载状态
			}
		},
		
		onLoad(options) {
			if( options.currentTabs ) this.currentTabs = +options.currentTabs
		},
		
		onShow() {
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) this.init()
			})
		},
		
		methods: {
			// 初始化
			async init() {
				this.page = 1
				await this.getEvaluateList()
				this.loading = false
			},
			
			// 获取评价列表
			async getEvaluateList() {
				if(this.hasGotEvaluateList) this.feedback.loading()
				const { data: { list, total }} = await this.$u.api.auctionOrderEvaluateList({
					type: this.currentTabs + 1, //当前tabs
					page: this.page, //页码
					limit: this.limit, //每页限制
				})
				this.page == 1 ? this.evaluateList = list : this.evaluateList = [...this.evaluateList, ...list]
				this.totalPage = Math.ceil(total / this.limit)
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				this.hasGotEvaluateList = 1
				uni.stopPullDownRefresh() //停止下拉刷新
				this.feedback.hideLoading()
			},
			
			// 切换分类
			changeTabs(index) {
				this.currentTabs = index
				this.page = 1
				this.getEvaluateList()
			},
			
			// 点赞/取消点赞 idx = 评价列表索引
			async thumbsUp(idx) {
				try{
					// like_status: 点赞状态:0=未点赞,1=赞
					this.feedback.loading()
					const { id, like_status, like_nums } = this.evaluateList[idx]
					if( like_status ) {
						await this.$u.api.auctionOrderEvaluateListDislike({evaluate_id: id})
						this.evaluateList[idx].like_status = 0
						this.evaluateList[idx].like_nums = like_nums - 1
					}else{
						await this.$u.api.auctionOrderEvaluateListLike({evaluate_id: id})
						this.evaluateList[idx].like_status = 1
						this.evaluateList[idx].like_nums = like_nums + 1
					}
					this.feedback.hideLoading()
				}catch(e){
					//TODO handle the exception
				}
				
			}
		},
		
		onPullDownRefresh() {
			this.init()
		},
		
		onReachBottom() {
			if ( this.page == this.totalPage || this.totalPage == 0) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getEvaluateList()
		}
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>