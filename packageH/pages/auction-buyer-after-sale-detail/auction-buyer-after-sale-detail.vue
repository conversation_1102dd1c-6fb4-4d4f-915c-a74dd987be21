<template>
	<view class="content pb-124" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<vh-navbar title='售后详情' />
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in">
			<!-- 状态 -->
			<view class="bg-ffffff b-rad-10 o-hid mt-20 ml-24 mr-24">
				<view class="w-p100 h-222 bg-li-31 flex-sb-c pl-24 pr-48">
					<view class="">
						<view class="font-36 font-wei text-ffffff">{{ afterSaleStatus[afterSaleInfo.status].text }}</view>
						<view class="mt-14 font-24 text-ffffff">{{ afterSaleStatus[afterSaleInfo.status].subText }}</view>
					</view>
					<vh-image :loading-type="2" :src="ossIcon(`/auction_buyer_after_sale_detail/icon${afterSaleInfo.status}.png`)" :width="112" :height="112" />
				</view>
				
				<!-- 售后订单明细 -->
				<ActionAfterSaleOrderDetail :afterSaleInfo="afterSaleInfo"/>
			</view>
			
			<!-- 上门取件 -->
			<view v-if="showPickUpInfo" class="bg-ffffff b-rad-10 o-hid mt-20 ml-24 mr-24 ptb-00-plr-24">
				<!-- 选择退货方式 -->
				<view class="flex-sb-c bb-s-01-f8f8f8 pt-44 pb-32">
					<view class="font-28 font-wei text-3">选择退货方式</view>
					<view class="font-28 text-3">上门取件</view>
				</view>
				
				<!-- 取件地址 -->
				<view class="flex-sb-c bb-s-01-f8f8f8 ptb-32-plr-00" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=7`)">
					<view class="font-30 text-3">取件地址</view>
					<view class="flex-c-c">
						<view class="w-474 mr-16">
							<view class="text-right">{{ orderInfo.province_name }} {{ orderInfo.city_name }} {{ orderInfo.district_name }} {{ orderInfo.address }}</view>
							<view class="text-right font-24 text-9">{{ orderInfo.consignee }} {{ orderInfo.consignee_phone }}</view>
						</view>
						<u-icon name="arrow-right" :size="24" color="#333" />
					</view>
				</view>
				
				<!-- 上门时间 -->
				<view class="flex-sb-c ptb-32-plr-00" @click="openPickUpTimePopup">
					<view class="font-30 text-3">上门时间</view>
					<view class="flex-c-c">
						<view class="mr-16 font-30 text-3">{{ `${findPickUpDate.day} ${findPickUpDate.timeRange}` }}</view>
						<u-icon name="arrow-right" :size="24" color="#333" />
					</view>
				</view>
			</view>
			
			<!-- 售后明细 -->
			<AuctionAfterSaleDetail :afterSaleInfo="afterSaleInfo" />
			
			<!-- 底部按钮 -->
			<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff flex-e-c b-sh-00021200-022 flex-c-c ptb-00-plr-24">
				<view v-if="showPickUpInfo" class="flex-e-c">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'208rpx', height:'64rpx', backgroundColor: '#E80404', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', border:'none'}" 
					@click="submitPickupInfo">提交</u-button>
				</view>
				<view v-else class="flex-e-c">
					<!-- 售后进度 -->
					<view class="">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#666', border:'1rpx solid #666'}" 
						@click="jump.navigateTo(`${routeTable.pHAuctionAfterSaleProgress}?refundOrderNo=${afterSaleInfo.refund_order_no}`)">售后进度</u-button>
					</view>
					<!-- 提醒处理 -->
					<view v-if="[0].includes(afterSaleInfo.status)" class="ml-20">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#E80404', border:'1rpx solid #E80404'}" 
						@click="reminderHandle">提醒处理</u-button>
					</view>
					
					<!-- 去寄件 -->
					<view v-if="[1].includes(afterSaleInfo.status)" class="ml-20">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#E80404', border:'1rpx solid #E80404'}" 
						@click="showDeliveryMethodPop = true">去寄件</u-button>
					</view>
				</view>
			</view>
			
			<!-- 弹框 -->
			<view class="">
				<!-- 寄件方式弹框 -->
				<u-popup v-model="showDeliveryMethodPop" mode="bottom" height="588" :border-radius="20">
					<view class="">
						<view class="ptb-00-plr-48">
							<!-- 标题 -->
							<view class="mt-36 text-center font-36 font-wei text-3">寄件方式</view>
							<!-- 内容 -->
							<view class="mt-60">
								<view class="font-28 font-wei text-3">上门取件</view>
								<view class="mt-10 font-24 text-ff9127 l-h-34">请选择上门取件时间，我们将为您上门取件，可随时修改或取消时间</view>
								<view class="mt-24">
									<view v-if="pickUpDateList.length" class="flex-sb-c">
										<view class="d-flex">
											<view
												v-for="(item, index) in pickUpDateList.slice(0, pickUpDateShowNum)" :key="index"
												class="w-260 h-94 d-flex flex-column j-center a-center b-rad-10 font-24 text-9 l-h-40 mr-20"
												:class="pickUpDateId === item.pickUpDateId ? 'bg-ffffff b-s-02-ff9127 text-ff9127' : 'bg-f7f7f7'"
												@click="pickUpDateId = item.pickUpDateId"
											>
												<text>{{ item.day }}</text>
												<text>{{ item.timeRange }}</text>
											</view>
										</view>
										<view v-if="pickUpDateList.length > pickUpDateShowNum" class="d-flex a-center" @click="openPickUpTimePopup">
											<view class="mr-10 font-28 text-9 l-h-34">更多</view>
											<u-icon name="arrow-right" :size="24" color="#999" />
										</view>
									</view>
									<view v-else class="font-28 text-9">暂无上门取件时间</view>
								</view>
							</view>
						</view>
						<!-- 底部按钮 -->
						<view v-if="pickUpDateList.length" class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff flex-c-c b-sh-00021200-022">
							<u-button 
							:disabled="!pickUpDateId"
							shape="circle" 
							:hair-line="false" 
							:ripple="true" 
							ripple-bg-color="#FFF"
							:custom-style="{width:'646rpx', height:'64rpx', backgroundColor: !pickUpDateId ? '#FCE4E3' : '#E80404', fontSize:'28rpx', fontWeight:'bold', color:'#FFF'}" 
							@click="confirmDeliveryMethod">确认</u-button>
						</view>
					</view>
				</u-popup>
				
				<!-- 选择上门时间弹框 -->
				<u-popup v-model="showPickerDatePop" mode="bottom" height="614" :border-radius="20">
					<view class="d-flex j-center a-center h-122 font-wei-600 font-36 text-3">选择上门时间段</view>
					<view class="h-388">
						<picker-view indicator-class="picker-view-padding" :value="pickUpDatePicker" @change="handlePickerChange" class="h-p100">
							<picker-view-column>
								<view class="d-flex j-center a-center ptb-00-plr-08 font-32 text-3" v-for="(day, index) in pickUpDayList" :key="index">
									<view class="o-hid w-s-now t-o-ell">{{ day }}</view>
								</view>
							</picker-view-column>
							<picker-view-column>
								<view class="d-flex j-center a-center ptb-00-plr-08 font-32 text-3" v-for="(time, index) in pickUpTimeList" :key="index">
									<view class="o-hid w-s-now t-o-ell">{{ time }}</view>
								</view>
							</picker-view-column>
						</picker-view>
					</view>
					<view class="d-flex j-center a-center h-104 b-sh-00021200-022" @click="confirmPickerDate">
						<button class="d-flex j-center a-center w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32">{{ pickUpPickerBtnText }}</button>
					</view>
				</u-popup>
			</view>
		</view>
		
		<!-- 骨架屏 -->
		<vh-skeleton v-else bgColor="#FFF" :showLoading="false"/>
	
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		name: 'auction-buyer-after-sale-detail',
		
		data() {
			return {
				loading: true, //数据是否加载完成
				refundOrderNo: '', //退款单号
				toSend: '', //去寄件
				afterSaleInfo: {}, //售后信息
				orderInfo: {}, //订单详情
				
				pickUpDateId: '', //日期id
				pickUpDate: [], // 完整的日期列表（后端返回）
				pickUpDateList: [], //日期列表（二级日期列表）
				pickUpDateShowNum: 2, //默认显示数量
				pickUpDatePicker: [0, 0], //列索引
				pickUpDateListLoading: false, //列表是否加载中
				showDeliveryMethodPop: false, //是否展示寄件方式弹框
				showPickerDatePop: false, //是否展示上门时间弹框
				confirmPickupInfo: false, //确认过上门信息
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable', 'addressInfoState']),
			
			// 售后状态
			afterSaleStatus() {
				return {
					0: { text: '卖家待处理' },
					1: { text: '卖家同意退货' },
					2: { text: '待卖家收货' },
					3: { text: '退款成功' },
					5: { text: '卖家拒绝退款', subText: '不符合退款条件' },
					6: { text: '卖家拒绝收货', subText: '已同意退货' },
				}
			},
			
			//上门取件时间段板块
			// 显示上门信息
			showPickUpInfo() {
				if([1].includes(this.afterSaleInfo.status) && this.pickUpDateId && this.confirmPickupInfo && !this.pickUpDateListLoading ) return true
				return false
			},
			
			findPickUpDate ({ pickUpDateId, pickUpDateList }) {
				return pickUpDateList.find(item => item.pickUpDateId === pickUpDateId)
			},
			pickUpDayList ({ pickUpDate }) {
				return pickUpDate.map(item => item.day)
			},
			pickUpTimeList ({ pickUpDate, pickUpDatePicker }) {
				const findItem = pickUpDate[pickUpDatePicker[0]]
				return findItem ? findItem.timeList.map(item => item.timeRange) : []
			},
			pickUpPickerBtnText ({ pickUpDayList, pickUpTimeList, pickUpDatePicker }) {
				const [dayIndex, timeIndex] = pickUpDatePicker
				return `${pickUpDayList[dayIndex]} ${pickUpTimeList[timeIndex]}`
			}
		},
		
		onLoad(options) {
			this.refundOrderNo = options.refundOrderNo
			if( options.toSend ) this.toSend = options.toSend
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) this.initOnLoad()
			})
		},
		
		onShow() {
			this.initOnShow()
		},
		
		methods:{ 
			// 初始化板块
			// 初始化
			async initOnLoad() {
				try{
					await this.getAfterSaleDetail()
					await this.getOrderDetail()
					await this.getPickupPeriod()
					this.loading = false
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 初始化onShow
			initOnShow() {
				this.changeAddress()
			},
			
			// 修改地址
			changeAddress() {
				if( Object.keys(this.addressInfoState).length && Object.keys(this.afterSaleInfo).length && this.afterSaleInfo.status === 1) {
					const { province_id, province_name, city_id, city_name, town_id, town_name, address, consignee, consignee_phone} = this.addressInfoState
					this.orderInfo.province_id = province_id
					this.orderInfo.province_name = province_name
					this.orderInfo.city_id = city_id
					this.orderInfo.city_name = city_name
					this.orderInfo.district_id = town_id
					this.orderInfo.district_name = town_name
					this.orderInfo.address = address
					this.orderInfo.consignee = consignee
					this.orderInfo.consignee_phone = consignee_phone
				}
			},
			
			// 获取售后详情
			async getAfterSaleDetail(){
				let res = await this.$u.api.auctionAfterSaleDetail({ refund_order_no: this.refundOrderNo })
				this.afterSaleInfo = res.data
			},
			
			// 获取订单详情
			async getOrderDetail(){
				let res = await this.$u.api.auctionOrderDetail({ order_no: this.afterSaleInfo.order_no })
				this.orderInfo = res.data
			},
			
			// 获取取件时段
			async getPickupPeriod() {
				try{
					if( Object.keys(this.orderInfo).length && Object.keys(this.afterSaleInfo).length && this.afterSaleInfo.status === 1) {
						this.pickUpDateListLoading = true
						const { order_no, province_name, city_name, district_name, address } = this.orderInfo
						let data = {}
						data.type = 0 //类型：0-卖家发货 1-买家售后
						data.order_no = order_no //订单号
						data.senderProvince = province_name //寄件人省
						data.senderCity = city_name //寄件人市
						data.senderDistrict = district_name //寄件人区
						data.senderDetailAddress = address //寄件人详细地址
						const res = await this.$u.api.auctionPickupPeriod(data)
						if( !res.data.length ) return
						const list = res.data	
						this.pickUpDate = list.filter(item => item.timeList.length)
						this.pickUpDateList = list.map(item => {
							const { day } = item
							return item.timeList.map(({ startTime, endTime, timeRange }) => ({
								pickUpDateId: `${day}&${timeRange}`,
								timeRange,
								day,
								startTime,
								endTime
							}))
						}).flat()
						if (!this.pickUpDateList.some(item => item.pickUpDateId === this.pickUpDateId)) {
							this.pickUpDateId = ''
						}
						this.pickUpDateListLoading = false
						if( this.toSend ) this.showDeliveryMethodPop = true
						console.log(res)
					}
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 按钮操作板块
			// 提醒处理
			async reminderHandle() {
				try{
					await this.$u.api.auctionRemindNotice({order_no: this.afterSaleInfo.order_no, type: 2 })
					this.feedback.toast({ title: '提醒处理成功~'})
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 填写上门信息板块
			// 打开上门时间段选择弹框
			openPickUpTimePopup () {
				if (!this.pickUpDateList.length) return
				if (this.pickUpDateId) {
					const [day, time] = this.pickUpDateId.split('&')
					const dayIndex = this.pickUpDayList.findIndex(item => item === day)
					const timeIndex = this.pickUpTimeList.findIndex(item => item === time)
					this.pickUpDatePicker = [dayIndex, timeIndex]
				}
				this.showPickerDatePop = true
			},
			
			// 选择日期
			handlePickerChange (e) {
				this.pickUpDatePicker = e.detail.value
			},
			
			// 确认寄件方式
			confirmDeliveryMethod() {
				console.log('============我是确认寄件方式')
				this.afterSaleStatus[this.afterSaleInfo.status].text = '请确认上门取件信息'
				this.confirmPickupInfo = true
				this.showDeliveryMethodPop = false
			},
			
			// 确认选择时间
			confirmPickerDate() {
				console.log('============我是确认确认选择时间')
				this.afterSaleStatus[this.afterSaleInfo.status].text = '请确认上门取件信息'
				const [dayIndex, timeIndex] = this.pickUpDatePicker
				this.pickUpDateId = `${this.pickUpDayList[dayIndex]}&${this.pickUpTimeList[timeIndex]}`
				this.confirmPickupInfo = true
				this.showDeliveryMethodPop = false
				this.showPickerDatePop = false
			},
			
			// 提交上门信息
			async submitPickupInfo() {
				try{
					const { order_no, province_name, province_id, city_name, city_id, district_name, district_id, address, consignee, consignee_phone } = this.orderInfo
					const { day, startTime, endTime } = this.pickUpDateList.find(item => item.pickUpDateId === this.pickUpDateId)
					let data = {}
					data.type = 1//类型：0-卖家发货 1-买家售后
					data.order_no = order_no //订单号
					data.senderProvince = province_name //寄件人省
					data.senderProvinceId = province_id //寄件人省ID
					data.senderCity = city_name //寄件人市
					data.senderCityId = city_id //寄件人市ID
					data.senderDistrict = district_name //寄件人区
					data.senderDistrictId = district_id //寄件人区ID
					data.senderDetailAddress = address //寄件人详细地址
					data.consignee = consignee //买家姓名
					data.consignee_phone = consignee_phone //买家电话号码
					data.pick_up_start_time = `${day} ${startTime}` //上门取件开始时间
					data.pick_up_end_time = `${day} ${endTime}` //上门取件结束时间
					console.log( data )
					// this.feedback.toast({title: '团团正在给测试调试数据'})
					await this.$u.api.auctionSubmitPickupInfo(data)
					this.getAfterSaleDetail()
				}catch(e){
					//TODO handle the exception
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .picker-view-padding {
		padding: 10rpx 0;
	}
</style>

<style>
	@import "@/common/css/page.css";
</style>