<template>
  <view>
    <vh-navbar height="46">
      <view class="flex-e-c w-p100">
        <view class="flex-s-c ptb-00-plr-26 w-560 h-68 bg-f7f7f7 b-rad-40">
          <image :src="ossIcon('/auction/search_38_36.png')" class="w-38 h-36" />
          <input v-model="query.title" placeholder="请输入" placeholder-style="color: #999;" class="ml-10 flex-1 font-28 text-3 l-h-40" @confirm="onSearch" />
          <image v-if="query.title" :src="ossIcon('/auction/del_40.png')" class="ml-10 w-40 h-40" @click="onClear" />
        </view>
        <button class="vh-btn flex-c-c ml-12 w-104 h-80 font-wei-500 font-28 text-6 bg-ffffff" @click="onSearch">搜索</button>
      </view>
    </vh-navbar>
    <view v-if="!isInSearch && historyList.length" class="ptb-40-plr-32">
      <view class="flex-sb-c">
        <text class="font-wei-500 font-32 text-3 l-h-44">历史搜索</text>
        <image :src="ossIcon('/auction/del_26.png')" class="w-26 h-26" @click="onClearHistoryList" />
      </view>
      <view class="d-flex flex-wrap mt-32">
        <view v-for="(item, index) in historyList" :key="index" class="mr-20 mb-24 ptb-02-plr-24 font-24 text-6 l-h-40 bg-f5f5f5 b-rad-24 text-hidden-1" @click="onSearchByHistory(item)">{{ item }}</view>
      </view>
    </view>
    <view v-if="isInSearch && !loading">
      <view v-if="list.length" class="ptb-20-plr-24 pb-0">
        <AuctionWGoodsList ref="auctionWGoodsListRef" :list="list" :addTime="50" />
      </view>
      <view v-else class="pt-200">
        <view class="flex-c-c">
          <image :src="ossIcon('/auction/search_none_440_400.png')" class="w-440 h-400" />
        </view>
        <view class="mt-146 font-32 text-6 l-h-44 text-center">搜索无结果，请试试其他关键词～</view>
      </view>
    </view>
  </view>
</template>

<script>
import listMixin from '@/common/js/mixins/listMixin'
const HISTORY_LIST_KEY = 'auctionSearchHistoryList'

export default {
  name: 'auctionSearch', // 拍卖搜索
  mixins: [listMixin],
  data: () => ({
    isInSearch: false,
    historyList: [],
    query: {
      title: '',
    },
  }),
  methods: {
    async load (query) {
      this.isInSearch = true
      const res = await this.$u.api.searchAuctionIndexGoodsList(query)
      const { list = [] } = res?.data || {}
      list.forEach(item => {
        item.detail = ''
      })
      this.list = query.page === 1 ? list : this.list.concat(list)
      this.changeBodyClassList()
      return res
    },
    onClear () {
      this.query.title = ''
      this.isInSearch = false
      this.list = []
      this.changeBodyClassList()
    },
    onSearch () {
      if (!this.query.title) return
      if (!this.historyList.includes(this.query.title)) {
        this.historyList.unshift(this.query.title)
        uni.setStorageSync(HISTORY_LIST_KEY, this.historyList)
      }
      this.loading = true
      this.reload().finally(() => {
        this.loading = false
      })
    },
    onClearHistoryList () {
      this.historyList = []
      uni.setStorageSync(HISTORY_LIST_KEY, this.historyList)
    },
    onSearchByHistory (item) {
      this.query.title = item
      this.onSearch()
    },
    changeBodyClassList () {
      this.$nextTick(() => {
        const classList = document?.body?.classList
        if (classList) classList[this.list.length ? 'add' : 'remove']('bg-f5f5f5')
      })
    },
  },
  onLoad () {
    this.historyList = uni.getStorageSync(HISTORY_LIST_KEY) || []
  },
  onShow () {
    this.changeBodyClassList()
  },
  onReachBottom () {
    this.reachBottomLoad()
  }
}
</script>

<style lang="scss" scoped>
</style>
