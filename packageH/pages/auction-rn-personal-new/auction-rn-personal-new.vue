<template>
  <view class="pb-104">
    <vh-navbar :title="title" height="46" :background="{ background: 'transparent' }" back-icon-color="#333" title-color="#333" />
    <view v-if="currStatus === 0">
      <view class="p-abso top-0 w-p100">
        <image :src="ossIcon('/auction/real_name_bg.png')" class="w-p100 h-1300"></image>
      </view>
      <view class="p-fixed left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-999">
        <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="popupVisible = true">立即认证</button>
      </view>
    </view>
    <view v-else-if="currStatus === 1">
      <view class="flex-c-c h-152 bg-feffff b-sh-00041800-009">
        <image :src="ossIcon('/auction/real_name_step1.png')" class="w-590 h-72"></image>
      </view>
      <view class="p-fixed left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-999">
        <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff bg-999999 b-rad-32">10s 请上滑看完本条款再同意</button>
      </view>
    </view>
    <view v-else-if="currStatus === 2">
      <view class="flex-c-c h-152 bg-feffff b-sh-00041800-009">
        <image :src="ossIcon('/auction/real_name_step2.png')" class="w-590 h-72"></image>
      </view>
      <view class="ptb-40-plr-32">
        <view class="font-20 text-9 l-h-28">为保障平台交易安全和对您进行合法有效的保护，根据《中华人民共和国网络安全法》《中华人民共和国未成年人保护法》等相关法律法规，以确保您实名认证信息与您本人真实身份信息的一致性。</view>
        <view class="mt-40">
          <view class="font-wei-500 font-28 text-3 l-h-40">真实姓名（必填）</view>
          <input placeholder="请输入您的真实姓名" placeholder-style="font-size:28rpx; color:#999" class="mt-24 font-32 text-3 l-h-40" />
          <view class="mt-24 h-02 bg-dddddd"></view>
        </view>
        <view class="mt-48">
          <view class="font-wei-500 font-28 text-3 l-h-40">请输入您的身份证号</view>
          <input placeholder="请输入您的真实姓名" placeholder-style="font-size:28rpx; color:#999" class="mt-24 font-32 text-3 l-h-40" />
          <view class="mt-24 h-02 bg-dddddd"></view>
        </view>
        <view class="mt-48">
          <view class="font-wei-500 font-28 text-3 l-h-40">请输入您的支付宝账号</view>
          <input placeholder="请输入您的真实姓名" placeholder-style="font-size:28rpx; color:#999" class="mt-24 font-32 text-3 l-h-40" />
          <view class="mt-24 h-02 bg-dddddd"></view>
        </view>
        <view class="d-flex a-start mt-70">
          <view class="flex-c-c">
            <image :src="ossIcon('/auction/tips_24.png')" class="w-24 h-24"></image>
            <text class="font-24 text-ff9127 l-h-34">温馨提示：</text>
          </view>
          <view class="flex-1 font-24 text-ff9127 l-h-34">
            <view>1.成交金额将自动提现到您的支付宝账号</view>
            <view>2.身份信息和支付宝账户信息保持一致</view>
            <view>3.未成年人不能成为酒云网拍卖卖家</view>
          </view>
        </view>
      </view>
      <view class="p-fixed left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-999">
        <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff bg-fce4e3 b-rad-32">保存</button>
      </view>
    </view>
    <view v-else-if="currStatus === 3">
      <view class="flex-c-c h-152 bg-feffff b-sh-00041800-009">
        <image :src="ossIcon('/auction/real_name_step3.png')" class="w-590 h-72"></image>
      </view>
      <view class="mt-160">
        <AuctionNone
          title="认证成功"
          titleClazz="mt-40"
          desc="欢迎您成为酒云网拍卖卖家"
          descClazz="mt-20"
        />
      </view>
      <view class="p-fixed left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-999">
        <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32">发布拍品</button>
      </view>
    </view>

    <u-popup v-model="popupVisible" mode="center" :mask-close-able="false" width="552" height="420" border-radius="20">
      <view class="p-rela wh-p100">
        <image :src="ossIcon('/auction/popup_bg_552_420.png')" class="p-abso wh-p100" />
        <view class="p-rela pt-84">
          <view class="font-wei-600 font-32 text-3 l-h-44 text-center">温馨提示</view>
          <view class="mt-24 ptb-00-plr-36 font-26 text-3 l-h-36 text-center">酒类商品仅支持在本平台购买的。</view>
          <view class="flex-sb-c ptb-00-plr-76 mt-84">
            <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32" @click="popupVisible = false">取消</button>
            <button class="vh-btn flex-c-c w-180 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32">确认</button>
          </view>
        </view>
      </view>
    </u-popup>

    <u-popup v-model="errPopupVisible" mode="center" :mask-close-able="false" width="552" height="380" border-radius="20">
      <view class="p-rela wh-p100">
        <image :src="ossIcon('/auction/popup_bg_552_380.png')" class="p-abso wh-p100" />
        <view class="p-rela pt-84">
          <!-- pt-88 -->
          <!-- ptb-00-plr-50 font-28 您的身份信息和支付宝绑定的身份信息不一致，请检查并重新输入。 -->
          <!-- mt-70 -->
          <view class="ptb-00-plr-84 font-wei-500 font-32 text-6 l-h-48 text-center">抱歉！暂不支持未成年人认证成为卖家。</view>
          <view class="mt-74 h-02 bg-dedede"></view>
          <view class="mt-32 font-wei-500 font-28 text-e80404 text-center" @click="errPopupVisible = false">知道了</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data: () => ({
    currStatus: 3,
    popupVisible: false,
    errPopupVisible: false,
  }),
  computed: {
    title ({ currStatus }) {
      if (currStatus === 3) return '认证成功'
      return '申请认证'
    }
  },
  methods: {
  },
  onShow () {
  }
}
</script>

<style lang="scss" scoped>
</style>
