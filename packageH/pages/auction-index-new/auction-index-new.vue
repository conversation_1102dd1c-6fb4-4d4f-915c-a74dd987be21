<template>
  <view>
    <vh-navbar height="46" title="拍卖列表" :customBack="$customBack" />
    <view v-if="!loading" class="pb-116">
      <view class="ptb-20-plr-24 pb-0">
        <AuctionWGoodsList ref="auctionWGoodsListRef" v-model="goodsList" :addTime="50" />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data: () => ({
    loading: true,
    query: { page: 1, limit: 10 },
    totalPage: 0,
    goodsList: [],
    hideCount: 0,
  }),
  methods: {
    async init (isLoadGoodsList = true) {
      try {
        const isLogin = await this.login.isLoginV3(this.$vhFrom, 0)
        const reqList = []
        if (isLoadGoodsList) {
          reqList.push(this.loadGoodsList())
        }
        await Promise.all(reqList)
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
        this.hideCount = this.$options.data().hideCount
      }
    },
    async loadGoodsList () {
      let { page, limit } = this.query
      if (this.hideCount) {
        limit = page * this.$options.data().query.limit
        page = 1
      }
      let res = await this.$u.api.searchAuctionIndexGoodsList({ page, limit, label: 3 })
      const { list = [], total = 0 } = res?.data || {}
      console.log('hideCount', this.hideCount)
      list.forEach(item => {
		item.$isShowPageviews = true
        item.detail = ''
      })
	  if( this.query.page === 1 ) {
		  const bannerList = await this.loadBnnerList()
		   console.log('bannerList', bannerList)
		  if( bannerList.length ) {
			  const combineList = [...[{ $isFirstBanner: true, list: bannerList }], ...list]
			  this.goodsList = combineList
		  }else {
			  this.goodsList = list
		  }
	  }else {
		  this.goodsList = this.goodsList.concat(list)
	  }
      // this.goodsList = this.query.page === 1 ? list : this.goodsList.concat(list)
      this.totalPage = Math.ceil(total / this.query.limit)
      this.hideCount = this.$options.data().hideCount
    },
	async loadBnnerList () {
		const res = await this.$u.api.auctionIndexGoodsListFirstBanner()
		const list = res?.data || []
		return list
	},
  },
  onLoad (options) {
    this.init()
  },
  onShow () {
    if (this.hideCount) {
      this.init(false)
    }
  },
  onHide () {
    this.hideCount++
  },
  onPullDownRefresh () {
    this.query = this.$options.data().query
    this.goodsList = []
    this?.$refs?.auctionWGoodsListRef?.clear()
    this.init()
  },
  onReachBottom () {
    if (this.query.page === this.totalPage || !this.totalPage) return
    this.feedback.loading()
    this.query.page++
    this.loadGoodsList().finally(() => {
      this.feedback.hideLoading()
    })
  }
}
</script>

<style>
  page {
    background: #f5f5f5;
  }
</style>
