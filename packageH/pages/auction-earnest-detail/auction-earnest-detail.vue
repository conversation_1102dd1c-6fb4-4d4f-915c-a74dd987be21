<template>
  <view>
    <vh-navbar :title="type | toText('MAuctionEarnestTypeText')" height="46" :background="{ background: navbarBackground }" back-icon-color="#FFF" title-color="#FFF" :customStatusBarHeight="20"></vh-navbar>
    <view v-if="!loading">
      <image class="p-abso top-0 w-p100 h-536" :src="ossIcon('/auction/mine_bg_750_536.png')" />
      <view class="p-rela">
        <view class="w-702 mtb-00-mlr-auto mt-20 bg-ffffff b-rad-10 b-sh-00101600-001">
          <view class="flex-c-c flex-column h-254">
            <text class="font-wei-500 font-72 text-e80404"><text class="font-36">¥</text>{{ earnest }}</text>
            <text class="font-28 text-6">当前总金额</text>
          </view>
          <view>
            <view class="ptb-00-plr-20 font-28 text-6">占用名额({{ list.length }})</view>
            <view class="ptb-08-plr-00">
              <view v-for="(item, index) in list" :key="index" class="" @click="jumpFundsDetail(item)">
                <view v-if="index" class="mtb-00-mlr-auto w-622 h-02 bg-eeeeee"></view>
                <view class="flex-sb-c ptb-32-plr-20">
                  <view class="w-630">
                    <view class="flex-sb-c">
                      <view class="w-538 font-wei-500 font-28 text-3 l-h-40 text-hidden">保证金-{{ item.goods_title }}</view>
                      <view class="font-32 text-3"><text class="font-24">¥</text>{{ item.payment_amount }}</view>
                    </view>
                    <view class="flex-sb-c">
                      <view class="font-24 text-9 l-h-30">{{ item.payment_time }}</view>
                      <!-- <view class="font-24 text-e80404 l-h-34">{{ item.status | toText('MAuctionEarnestStatusText') }}</view> -->
                      <view class="font-24 text-e80404 l-h-34">{{ item.status_cn }}</view>
                    </view>
                  </view>
                  <image :src="ossIcon('/about/arrow_r_12_20.png')" class="ml-20 w-12 h-20" />
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-if="introList.length" class="mt-60 ptb-00-plr-48">
          <view v-for="(item, index) in introList" :key="index" class="mb-52">
            <view class="font-26 text-6 l-h-36">{{ index + 1 }}. {{ item.title }}</view>
            <view class="mt-16 font-24 text-9 lh-34">{{ item.desc }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { MAuctionEarnestType } from '@/common/js/utils/mapperModel'

const BIDDING_EARNEST_INTRO_LIST = [
  { title: '竞拍保证金说明', desc: '竞拍保证金为买家所缴纳的仅针对“竞拍商品”收取不针对“人”来收取。“竞拍商品”特指通过本平台拍卖出售的商品。如果买家希望同时竞价多件商品，那么则需要在支付每个商品的保证金后方可参与竞拍，每个商品的保证金由卖方在送拍时确定的金额，平台方不做干涉。', },
  { title: '竞拍保证金退还原则', desc: '拍卖结束后，竞拍保证金会在72小时内自动退回您的支付账户中，您收到款项的时间以银行实际到账时间为准。', },
]

const ENTRUST_EARNEST_INTRO_LIST = [
  { title: '委托保证金说明', desc: '委托保证金为卖家所缴纳的仅针对“送拍商品”收取不针对“人”来收取。如果卖家希望同时送拍多件商品，那么则需要在支付每个商品的保证金后方可申请上拍，每个商品的保证金按照《保证金规则》条款要求的金额。', },
  { title: '委托保证金退还原则', desc: '前提为不存在交易纠纷，且订单已完成（以买家点击“确认收货”为准，或发货后15天），委托保证金会在72小时内自动退回您的支付账户中，您收到款项的时间以银行实际到账时间为准。 如卖家交易违规（包含延期发货、成交不卖、虚假发货、交易争议等违规情形），保证金将扣除。', },
]

export default {
  name: 'auctionEarnestDetail', // 拍卖保证金详情
  data: () => ({
    loading: true,
    navbarBackground: 'transparent',
    type: '',
    earnest: 0,
    list: []
  }),
  computed: {
    ...mapState(['routeTable']),
    introList ({ type }) {
      switch (type) {
        case MAuctionEarnestType.Bidding:
          return BIDDING_EARNEST_INTRO_LIST
        case MAuctionEarnestType.Entrust:
          return ENTRUST_EARNEST_INTRO_LIST
        default:
          return []
      }
    }
  },
  methods: {
    ...mapMutations('auctionFunds', ['UPDATE_FUNDS_DETAIL']),
    async load () {
      const res = await this.$u.api.searchAuctionEarnestList({ type: this.type, page: 1, limit: 9999 })
      const { earnest_money = 0, list = [] } = res?.data || {}
      this.earnest = earnest_money
      this.list = list
    },
    jumpFundsDetail (item) {
      console.log('jumpFundsDetail', item)
      this.UPDATE_FUNDS_DETAIL(item)
      this.jump.navigateTo(this.routeTable.pHAuctionFundsDetail)
    }
  },
  onLoad (options) {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.type = +options.type
      this.load().finally(() => {
        this.loading = false
      })
    })
  },
  onPageScroll (res) {
    if(res.scrollTop <= 100){
      this.navbarBackground = `rgba(224, 20, 31, ${res.scrollTop / 100})`
    }else{
      this.navbarBackground = `rgba(224, 20, 31, 1)`
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
