<template>
  <view :class="['h-min-p100', list.length ? 'bg-f5f5f5' : '']">
    <vh-navbar title="我发布的" height="46" showBorder>
      <view slot="right" class="d-flex">
        <image class="p-24 w-44 h-44" :src="ossIcon('/auction/search_44.png')" @click="jump.navigateTo($routeTable.pHAuctionMyCreateListSearch)" />
      </view>
    </vh-navbar>
    <view class="tabs ptb-00-plr-32 bg-ffffff">
      <u-tabs :list="tabsList" :current="currentTabIndex" :is-scroll="false" height="92" font-size="28" active-color="#e80404" inactive-color="#666" bar-width="36" bar-height="8" @change="onTabChange" />
    </view>
    <view v-if="!loading">
      <AuctionCreatedGoodsList ref="auctionCreatedGoodsListRef" :list="list" @reload="reload" />
    </view>
  </view>
</template>

<script>
import { mapMutations } from 'vuex'
import listMixin from '@/common/js/mixins/listMixin'
import { MAuctionCreatedStatusMapper } from '@/common/js/utils/mapper'

export default {
  mixins: [listMixin],
  data: () => ({
    query: {
      type: 1,
    },
    tabsList: [
      { name: '全部', type: 1, count: 0 },
      { name: '待上拍', type: 2, count: 0 },
      { name: '待发货', type: 3, count: 0 },
      { name: '待收货', type: 4, count: 0 },
      { name: '已完成', type: 5, count: 0 }
    ],
  }),
  onLoad () {
    this.muAddressInfoState({}) // 进页面清空，防止持久化导致错乱
  },
  onShow () {
    if (this.login.isLogin()) {
      this.load().finally(() => {
        this.$nextTick(() => {
          this.$refs?.auctionCreatedGoodsListRef?.$refs?.auctionCreatedDeliverGoodsPopupRef?.initAddressInfo()
        })
      })
    }
  },
  methods: {
    ...mapMutations(['muAddressInfoState']),
    async load (query, currentTabIndex) {
      const type = this.tabsList[currentTabIndex].type
      const resList = await Promise.all([
        this.$u.api.searchAuctionMyCreateList({ ...query, type }),
        this.$u.api.getPendDeliverCount({ ...query, type })
      ])
      const { list = [] } = resList[0]?.data || {}
      const { await_deliver = 0 } = resList[1]?.data || {}
      list.forEach(item => {
        if (item.status === 2 && item.express_number) item.status = 9999
        Object.assign(item, MAuctionCreatedStatusMapper[item.status] || {})
      })
      this.list = query.page === 1 ? list : this.list.concat(list)
      this.tabsList = this.tabsList.map(item => item.type === 3 ? Object.assign({}, item, { count: await_deliver }) : item)
      return resList[0]
    },
    onTabChange (index) {
      this.changeTabIndex(index)
    },
  },
  onPullDownRefresh() {
    this.pullDownRefresh()
  },
  onReachBottom () {
    this.reachBottomLoad()
  },
}
</script>

<style>
  page {
    height: 100%;
	}
</style>

<style lang="scss" scoped>
  .tabs {
    ::v-deep {
      .u-tab {
        &-item {
          flex: 0 1 auto !important;
          overflow: visible;

          .u-badge {
            @include flex-row;
            @include size(24rpx);
            font-size: 18rpx !important;
            transform: none !important;
            top: 20rpx !important;
            right: -12rpx !important;
          }
        }
        &-bar {
          top: 100%;
        }
      }
    }
  }
</style>
