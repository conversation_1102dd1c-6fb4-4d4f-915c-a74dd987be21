<template>
  <view>
    <vh-navbar height="46" v-bind="navbarParams"></vh-navbar>
    <view v-if="!loading">
      <template v-if="isEarnest">
        <view class="ptb-20-plr-44 bg-db2523">
          <view class="font-wei-500 font-32 text-ffffff l-h-44">{{ fundsDetail.earnest_enlighten }}</view>
          <view class="mt-04 font-24 text-ffffff l-h-34">{{ fundsDetail.status_cn }}</view>
        </view>
        <view class="d-flex j-sb ptb-32-plr-44 bg-ffffff">
          <vh-image :loading-type="4" :src="fundsDetail.product_img" :width="152" :height="152" :border-radius="6" />
          <view class="flex-1 ml-20">
            <view class="h-68 font-wei-500 font-24 text-3 l-h-34 text-hidden-2">{{ fundsDetail.goods_title }}</view>
            <view class="mt-10 font-24 text-9 l-h-34">保证金金额：{{ fundsDetail.payment_amount }}</view>
            <view class="flex-s-c mt-06" @click="copy.copyText(fundsDetail.main_order_no)">
              <text class="font-24 text-9 l-h-34 text-hidden-1">交易编号：{{ fundsDetail.main_order_no }}</text>
              <button class="vh-btn flex-shrink flex-c-c ml-10 w-65 h-30 font-18 text-3 bg-eeeeee b-rad-15">复制</button>
            </view>
          </view>
        </view>
      </template>
      <view v-if="isAuctionOrder" class="mt-20 ptb-00-plr-24">
        <view class="flex-c-c flex-column h-588 bg-ffffff b-rad-10">
          <vh-image :loading-type="4" :src="fundsDetail.product_img" :width="300" :height="300" :border-radius="6" />
          <view class="mt-20 w-514 font-wei-500 font-24 text-3 l-h-34 text-center text-hidden">{{ fundsDetail.goods_title }}</view>
          <view class="flex-c-c mt-14" @click="copy.copyText(fundsDetail.main_order_no)">
            <text class="font-24 text-9 l-h-34 text-hidden-1">交易编号：{{ fundsDetail.main_order_no }}</text>
            <button class="vh-btn flex-c-c ml-10 w-56 h-30 font-18 text-3 bg-eeeeee b-rad-15">复制</button>
          </view>
          <view class="flex-c-c mt-18">
            <text class="font-28 text-9 l-h-40">成交价</text>
            <text class="ml-20 font-wei-600 font-36 text-3 l-h-50">{{ fundsDetail.payment_amount }}</text>
          </view>
        </view>
      </view>
      <view v-if="isAuctionOrder || isEarnest" class="mt-20" :class="isAuctionOrder ? 'ptb-00-plr-24' : ''">
        <view class="bg-ffffff" :class="isAuctionOrder ? 'ptb-32-plr-20 b-rad-10' : 'ptb-32-plr-44'">
          <view class="d-flex h-60 font-28 text-6 l-h-40 bb-s-02-d8d8d8">
            <view class="flex-1">状态</view>
            <view class="w-204">金额（元）</view>
            <view :class="isAuctionOrder ? 'w-118' : 'w-126'">渠道</view>
          </view>
          <view class="list mt-40" :class="{ 'is-one': isOne }">
            <view v-for="(item, index) in fundsDetail.info" :key="index" class="list__item d-flex">
              <view class="list__item-timeline">
                <view class="list__item-dot"></view>
              </view>
              <view class="flex-1 pl-48">
                <view class="l-h-40" :class="index ? 'text-6' : 'font-wei-500 text-e80404'">
                  <text>{{ item.status_cn }}</text>
                  <text v-if="item.explain" class="font-18 l-h-26">（{{ item.explain }}）</text>
                </view>
                <view class="mt-12 l-h-34" :class="index ? 'text-9' : 'text-6'">{{ item.payment_time | date('yyyy.mm.dd hh:MM') }}</view>
              </view>
              <view class="w-204 l-h-44" :class="index ? 'text-6' : 'font-wei-500 text-3'">
                <template v-if="item.payment_amount"> {{ item.source_type === MAuctionFundsType.Income ? '+' : '-' }}{{ item.payment_amount }}</template>
              </view>
              <view class="font-wei-500 font-24 text-9 l-h-34" :class="isAuctionOrder ? 'w-118' : 'w-126'">{{ item.payment_method | toText('MPaymentMethodText') }}</view>
            </view>
          </view>
        </view>
      </view>
      <view v-if="isEarnest" class="ptb-32-plr-44">
        <view class="font-wei-500 font-28 text-6 l-h-40">温馨提示</view>
        <view class="mt-24 font-24 text-6 l-h-34">
          <view>1.若竞拍失败（含未出价、未竞拍成功），保证金会在拍卖结束后<text class="text-3">72小时内</text>释放并自动退回到您的付款渠道。</view>
          <view class="mt-06">2. 如遇问题，请联系客服帮您处理。</view>
        </view>
      </view>
      <view v-if="isEarnest && fundsDetail.earnest_payment_status" class="p-fixed left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-9999">
        <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="onJump">{{ fundsDetail.earnest_payment_status }}</button>
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionFundsType, MAuctionFundsOrderType } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'

export default {
  name: 'auctionFundsDetailNew', // 新拍卖资金详情
  data: () => ({
    MAuctionFundsType,
    MAuctionFundsOrderType,
    loading: true,
    orderNo: '',
    isFromAuctionGoods: '',
    fundsDetail: {},
  }),
  computed: {
    ...mapState(['routeTable']),
    navbarParams ({ fundsDetail }) {
      switch (fundsDetail.type) {
        case MAuctionFundsOrderType.Earnest:
          return { title: '保证金详情' }
        case MAuctionFundsOrderType.AuctionOrder:
          return { title: '拍卖明细', background: { background: '#e80404' }, backIconColor: '#fff', titleColor: '#fff' }
        default:
          return {}
      }
    },
    isEarnest ({ fundsDetail }) {
      return fundsDetail.type === MAuctionFundsOrderType.Earnest
    },
    isAuctionOrder ({ fundsDetail }) {
      return fundsDetail.type === MAuctionFundsOrderType.AuctionOrder
    },
    isOne ({ fundsDetail }) {
      return (fundsDetail?.info?.length || 0) === 1
    },
  },
  methods: {
    async load () {
      const res = await this.$u.api.getAuctionFundsDetail({ main_order_no: this.orderNo })
      const data = res?.data || {}
      if (this.isFromAuctionGoods && data.info) {
        const lastItem = data.info.pop()
        data.info = [lastItem]
      }
      data.info.forEach(item => {
        item.payment_time = item.payment_time.replace(/-/g, '/')
      })
      this.fundsDetail = data
    },
    onJump () {
      const { earnest_payment_status, goods_id } = this.fundsDetail
      const { pHAuctionGoodsDetail, pHAuctionBuyerOrderDetail } = this.routeTable
      switch (earnest_payment_status) {
        case '去参拍':
          this.jump.navigateTo(`${pHAuctionGoodsDetail}?id=${goods_id}`)
          break
        case '去付款':
          this.jump.navigateTo(`${pHAuctionBuyerOrderDetail}?goodsId=${goods_id}`)
          break
      }
    }
  },
  onLoad (options) {
    const { orderNo = '', isFromAuctionGoods = '' } = options
    this.orderNo = orderNo
    this.isFromAuctionGoods = isFromAuctionGoods
    this.load().finally(() => {
      this.loading = false
    })
  }
}
</script>

<style>
  page {
    background: #f5f5f5;
  }
</style>

<style lang="scss" scoped>
  .list {
    &.is-one {
      .list__item {
        padding-bottom: 24rpx !important;

        &-timeline {
          &::before {
            display: block !important;
          }
        }
      }
    }

    &__item {
      position: relative;
      padding-bottom: 58rpx;

      &:first-of-type {
        .list {
          &__item {
            &-dot {
              background: #FACECE;

              &::before {
                background: #E80404;
              }
            }
          }
        }
      }

      &:last-of-type {
        padding-bottom: 0;

        .list {
          &__item {
            &-timeline {
              &::before {
                display: none;
              }
            }
          }
        }
      }

      &-timeline {
        position: absolute;
        top: 0;
        left: 0;
        @include size(28rpx, 100%);

        &::before {
          content: '';
          position: absolute;
          top: 12rpx;
          left: 50%;
          transform: translateX(-50%);
          display: block;
          @include size(4rpx, 100%);
          background: #eee;
        }
      }

      &-dot {
        position: absolute;
        top: 6rpx;
        @include flex-row;
        @include size(28rpx);
        background: transparent;
        border-radius: 50%;

        &::before {
          content: '';
          display: block;
          @include size(16rpx);
          background: #ccc;
          border-radius: 50%;
        }
      }
    }
  }
</style>
