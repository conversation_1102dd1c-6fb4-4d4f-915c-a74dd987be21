<template>
  <view>
    <vh-navbar v-if="!isFromMyCollection" title="我的收藏" height="46">
      <view v-if="enjoyedList.length" slot="right" class="flex-c-c w-108 h-92 font-30 text-3" @click="onRightTextClick">{{ rightText }}</view>
    </vh-navbar>
    <view v-if="!loading" class="ptb-00-plr-24 o-hid" :class="isEdit ? 'pb-128' : 'pb-24'">
      <view v-if="list.length" :class="isEdit ? 't-trans-x-72' : ''">
        <view v-for="item in list" :key="item.time" class="p-rela mt-20 bg-ffffff b-rad-10">
          <view class="p-abso top-16 left-20 font-wei-500 font-28 text-6">{{ item.time }}</view>
          <view v-for="(item, index) in item.list" :key="index" class="p-rela ptb-00-plr-20" @click="onJump(item)">
            <view v-if="item.like_status" class="p-abso top-50 left-0 t-trans-x-m100 flex-c-c w-96">
              <image :src="ossIcon(`/auction/radio${item.$checked ? '_h' : ''}_32.png`)" class="ptb-20-plr-16 w-32 h-32" @click.stop="item.$checked = !item.$checked" />
            </view>
            <view class="p-abso top-0 right-0 w-176 h-46">
              <image :src="ossIcon(item.$erStatusBg)" class="p-abso w-p100 h-p100" />
              <view class="p-rela flex-c-c w-p100 h-p100">
                <image :src="ossIcon('/auction/hammer_36_34.png')" class="w-36 h-34" />
                <text class="ml-10 font-24 l-h-34" :class="item.$erStatusTextClazz">{{ item.$erStatusText }}</text>
              </view>
            </view>
            <view v-show="index" class="h-02 bg-eeeeee"></view>
            <view class="d-flex j-sb pt-70 pb-24">
              <vh-image :loading-type="4" :src="item.product_img && item.product_img[0]" :width="152" :height="152" :border-radius="6" />
              <view class="w-490">
                <view class="h-68 font-wei-500 font-24 l-h-34 text-hidden-2" :class="item.$erTitleTextClazz">{{ item.title }}</view>
                <text class="mt-04 ptb-00-plr-08 font-22 l-h-32 b-rad-04" :class="item.$erTimeTextClazz">
                  <template v-if="item[item.$eTimeKeyValue]">{{ item[item.$eTimeKeyValue] }} {{ item.$erTimeSuffixText }}</template>
                  <template v-else>{{ item.$erTimeSuffixText }}</template>
                </text>
                <view class="flex-sb-c">
                  <view class="flex-c-c l-h-34">
                    <text class="font-24" :class="item.$erPricePrefixClazz">{{ item.pricePrefixText }}</text>
                    <text class="ml-06 font-32" :class="item.$erPriceClazz"><text class="font-24">¥</text>{{ item.final_auction_price === '0.00' ? item.price : item.final_auction_price }}</text>
                  </view>
                  <image :src="ossIcon(`/auction/enjoy${item.like_status ? '_h' : ''}_24_26.png`)" class="w-24 h-26" @click.stop="onEnjoy(item)" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <AuctionNone v-else title="空空如也" desc="快去发现宝贝吧～" class="pt-200" />
      <view v-if="enjoyedList.length && isEdit" class="p-fixed left-0 bottom-0 flex-sb-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-999" v-safeBeautyBottom="$safeBeautyBottom">
        <view class="flex-sb-c ml-24" @click="onCheckAllChange">
          <image :src="ossIcon(`/auction/radio${checkAll ? '_h' : ''}_32.png`)" class="ml-08 w-32 h-32" />
          <text class="ml-16 font-32 text-3">全选</text>
        </view>
        <button class="vh-btn flex-c-c mr-24 w-208 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="checkSome ? 'bg-e80404' : 'bg-fce4e3'" @click="onBatchCancel">删除收藏</button>
      </view>
    </view>

    <u-popup v-model="popupVisible" mode="center" width="552rpx" height="414rpx" border-radius="20">
      <view class="p-rela w-552 h-414">
        <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-552 h-414" />
        <view class="p-rela pt-84">
          <view class="font-wei-500 font-32 text-3 l-h-44 text-center">真的不喜欢我了嘛</view>
          <view class="mt-20 font-28 text-6 l-h-34 text-center">哼，善变的兔子君！</view>
          <view class="flex-sb-c mt-84 ptb-00-plr-70">
            <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32" @click="onConfirm">确认</button>
            <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="popupVisible = false">取消</button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { MAuctionGoodsStatusObjMapper } from '@/common/js/utils/mapper'
import { mapState } from 'vuex'
import listMixin from '@/common/js/mixins/listMixin'

export default {
  name: 'auctionEnjoy', // 拍卖喜欢页面
  mixins: [listMixin],
  props: {
    isFromMyCollection: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    isLastIdPattern: true,
    isEdit: false,
    popupVisible: false,
    currentItem: null,
  }),
  computed: {
    ...mapState(['routeTable']),
    rightText ({ isEdit }) {
      return isEdit ? '完成' : '编辑'
    },
    enjoyedList ({ list }) {
      const goodsList = []
      list.forEach(item => {
        goodsList.push(...item.list)
      })
      return goodsList.filter(item => item.like_status)
    },
    checkAll ({ enjoyedList }) {
      return !!enjoyedList.length && enjoyedList.every(item => item.$checked)
    },
    checkSome ({ enjoyedList }) {
      return enjoyedList.some(item => item.$checked)
    },
  },
  methods: {
    async load (query) {
      const res = await this.$u.api.searchEnjoyAuctionGoodsList(query)
      const { list = [] } = res?.data || {}
      const checkAll = this.checkAll
      list.forEach(ditem => {
        ditem.list = ditem.list.map(item => Object.assign({}, MAuctionGoodsStatusObjMapper[item.onsale_status], item, { $checked: checkAll }))
      })
      let isFirst = query.page === 1
      if (this.isLastIdPattern) {
        isFirst = !query.last_id
      }
      if (isFirst) {
        this.list = list
      } else {
        const { length, [length - 1]: originLastItem } = this.list
        const targetFirstItem = list[0]
        if (originLastItem && targetFirstItem) {
          if (originLastItem.time === targetFirstItem.time) {
            originLastItem.list.push(...targetFirstItem.list)
            this.list.push(...list.slice(1))
          } else {
            this.list.push(...list)
          }
        }
      }
      this.changeBodyClassList()
      return res
    },
    onRightTextClick () {
      this.isEdit = !this.isEdit
      this.list.forEach(ditem => {
        ditem.list.forEach(item => {
          item.$checked = false
        })
      })
    },
    async onEnjoy (item) {
      if (this.isEdit) return
      const { like_status, goods_id } = item
      if (like_status) {
        this.popupVisible = true
        this.currentItem = item
      } else {
        await this.$u.api.addEnjoyAuctionGoods({ goods_id })
        this.feedback.toast()
        item.like_status = 1
      }
    },
    async onConfirm () {
      await this.$u.api.cancelEnjoyAuctionGoods({ goods_id: this.currentItem.goods_id })
      this.popupVisible = false
      this.currentItem.like_status = 0
    },
    onCheckAllChange () {
      const checkAll = this.checkAll
      this.list.forEach(ditem => {
        ditem.list.forEach(item => {
          item.$checked = !checkAll
        })
      })
    },
    onBatchCancel () {
      if (!this.checkSome) return
      this.feedback.showModal({
        content:'确认删除吗？',
        confirm: () => {
          const goods_ids = this.list.map(ditem => ditem.list.filter(item => item.like_status && item.$checked).map(item => item.goods_id)).reduce((a, b) => a.concat(b), [])
          this.$u.api.batchCancelEnjoyAuctionGoods({ goods_ids }).then(() => {
            if (!this.isFromMyCollection) {
              this.reload().then(() => {
                this.onChangeToScroll()
              }).finally(() => {
                this.feedback.toast()
              })
            } else {
              this.feedback.toast()
            }
            this.isEdit = false
            if (this.isFromMyCollection) {
              this.$emit('hiddenEdit')
            }
          })
        }
      })
    },
    onJump (item) {
      if (this.isEdit) return
      this.jump.navigateTo(`${this.routeTable.pHAuctionGoodsDetail}?id=${item.goods_id}`)
    },
    changeBodyClassList () {
      this.$nextTick(() => {
        document?.body?.classList?.[this.list.length ? 'add' : 'remove']('bg-f5f5f5')
      })
    }
  },
  onLoad () {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.load()
    })
  },
  onShow () {
    this.changeBodyClassList()
  },
  onPullDownRefresh() {
    if (this.isEdit) {
      uni.stopPullDownRefresh()
      return
    }
    this.pullDownRefresh()
  },
  onReachBottom () {
    this.reachBottomLoad()
  },
}
</script>

<style lang="scss" scoped>
</style>
