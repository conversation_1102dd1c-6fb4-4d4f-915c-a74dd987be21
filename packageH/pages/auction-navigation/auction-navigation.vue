<template>
  <view>
    <vh-navbar title="拍卖记录" height="46" show-border />
    <view class="ptb-00-plr-40">
      <view v-for="(item, index) in list" :key="index" class="flex-sb-c h-110 bb-s-02-eeeeee" @click="jump.navigateTo($routeTable[item.page])">
        <view class="font-wei-500 font-32 text-3">{{ item.name }}</view>
        <image :src="ossIcon('/about/arrow_r_12_20.png')" class="w-12 h-20" />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data: () => ({
    list: [
      {
        name: '我发布的',
        page: 'pHAuctionMyCreateList'
      },
      {
        name: '我的参拍',
        page: 'pHAuctionMyParticipation'
      },
      {
        name: '资金明细',
        page: 'pHAuctionFundsListNew'
      },
      {
        name: '拍卖证书',
        page: 'pHAuctionCertificateListFromMine'
      },
      {
        name: '我关注的',
        page: 'pHAuctionRemindFromMine'
      }
    ]
  })
}
</script>

<style lang="scss" scoped>
</style>