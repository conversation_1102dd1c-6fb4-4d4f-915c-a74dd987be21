<template>
	<view class="content pb-124" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<vh-navbar title='售后详情' />
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in">
			<!-- 状态 -->
			<view class="bg-ffffff b-rad-10 o-hid mt-20 ml-24 mr-24">
				<view class="w-p100 h-222 bg-li-30 flex-sb-c pl-24 pr-48">
					<view class="font-36 font-wei text-ffffff">{{ afterSaleStatus[afterSaleInfo.status].text }}</view>
					<vh-image :loading-type="2" :src="ossIcon(`/auction_seller_after_sale_detail/icon${afterSaleInfo.status}.png`)" :width="112" :height="112" />
				</view>
				
				<ActionAfterSaleOrderDetail :type="1" :afterSaleInfo="afterSaleInfo"/>
			</view>
			
			<!-- 售后明细 -->
			<AuctionAfterSaleDetail :type="1" :afterSaleInfo="afterSaleInfo" />
			
			<!-- 底部按钮 -->
			<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff flex-e-c b-sh-00021200-022 ptb-00-plr-24">
				<!-- 待处理-->
				<view v-if="[0, 2].includes(afterSaleInfo.status)" class="flex-c-c">
					<view class="">
						<u-button 
						shape="circle" 
						:hair-line="false" 
						:ripple="true" 
						ripple-bg-color="#FFF"
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#666', border:'1rpx solid #666'}" 
						@click="showRejectMask = true">拒绝</u-button>
					</view>
					<view class="ml-20">
						<u-button 
						shape="circle" 
						:hair-line="false" 
						:ripple="true" 
						ripple-bg-color="#FFF"
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#2E7BFF', border:'1rpx solid #2E7BFF'}" 
						@click="handleAfterSaleType(1)">同意</u-button>
					</view>
				</view>
				
				<!-- 售后进度-->
				<view v-if="[1, 3, 5, 6].includes(afterSaleInfo.status)" class="">
					<u-button 
					shape="circle" 
					:hair-line="false" 
					:ripple="true" 
					ripple-bg-color="#FFF"
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#999', border:'1rpx solid #999'}" 
					 @click="jump.navigateTo(`${routeTable.pHAuctionAfterSaleProgress}?refundOrderNo=${afterSaleInfo.refund_order_no}`)">售后进度</u-button>
				</view>
			</view>
			
			<!-- 弹框 -->
			<view class="">
				<!-- 拒绝原因 -->
				<u-mask :show="showRejectMask" :zoom="false" @click="showRejectMask = false">
					<view class="h-p100 flex-column flex-c-c">
						<view class="flex-column flex-c-c">
							<view class="p-rela w-624 h-572 bg-ffffff b-rad-30 mb-46 o-hid" @click.stop>
								<image class="p-abso w-p100 h-p100 b-rad-30" :src="ossIcon(`/auction_seller_after_sale_detail/reason_bg.png`)" />
								<view class="p-rela z-02 w-p100 h-p100 d-flex flex-column j-center b-rad-30 ptb-00-plr-40">
									<view class="font-32 font-wei text-3">拒绝原因</view>
									<view class="w-544 bg-fffafa b-rad-20 b-s-02-efc1cd mt-24 p-16">
										<textarea v-model="rejectReason" :maxlength="50" class="w-p100 h-168 font-24 text-9 l-h-34" placeholder="请填写您的备注" placeholder-style="color:#999;font-size:28rpx;" @input="rejectReasonChange"/>
										<view class="d-flex j-end mt-20 font-24 text-9">{{ rejectReason.length > 50 ? 50 : rejectReason.length }}/50</view>
									</view>
									<view class="flex-sb-c mt-50">
										<view class="">
											<u-button
												shape="circle" 
												:hair-line="false" 
												:ripple="true" 
												ripple-bg-color="#FFF"
												:custom-style="{ width:'240rpx', height:'64rpx', backgroundColor: '#FFF', fontSize:'28rpx' ,fontWeight:'bold', color:'#666', border:'1px solid #666666' }"
												@click="showRejectMask = false"
											>取消</u-button>
										</view>
										
										<view class="">
											<u-button
											    :disabled="!canConfirmReject"
												shape="circle" 
												:hair-line="false" 
												:ripple="true" 
												ripple-bg-color="#FFF"
												:custom-style="{ width:'240rpx', height:'64rpx', backgroundColor: !canConfirmReject ? '#FCE4E3' : '#E80404', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', border:'none' }"
												@click="handleAfterSaleType(0)"
											>确认</u-button>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</u-mask>
			</view>
		</view>
	    
		<!-- 骨架屏 -->
		<vh-skeleton v-else bgColor="#FFF" :showLoading="false"/>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		name: 'auction-seller-after-sale-detail',
		
		data() {
			return {
				loading: true, //数据是否加载完成
				refundOrderNo: '', //退款单号
				afterSaleInfo: {}, //售后信息
				showRejectMask: false, //是否显示拒绝弹框
				rejectReason: '', //拒绝原因
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
			
			// 售后状态
			afterSaleStatus() {
				return {
					0: { text: '待处理' },
					1: { text: '等待买家寄回' },
					2: { text: '等待收货' },
					3: { text: '退款成功' },
					5: { text: '拒绝退款' },
					6: { text: '拒绝收货' },
				}
			},
			
			// 是否可以确认拒绝
			canConfirmReject() {
				if( this.$u.trim(this.rejectReason, 'all') !== '' ) return true
				return false
			}
		},
		
		onLoad(options) {
			this.refundOrderNo = options.refundOrderNo
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) this.init()
			})
		},
		
		methods:{ 
			// 初始化板块
			// 初始化
			async init() {
				await this.getAfterSaleDetail()
				this.loading = false
			},
			
			// 获取售后详情
			async getAfterSaleDetail(){
				let res = await this.$u.api.auctionAfterSaleDetail({refund_order_no: this.refundOrderNo })
				this.afterSaleInfo = res.data
			},
			
			// 同意/拒绝申请售后 type = 处理类型：0-拒绝 1-同意
			async afterSaleAudit(type) {
				try{
					let data = {}
					data.order_no = this.afterSaleInfo.order_no //订单号
					data.deal_type = type //处理类型：0-拒绝 1-同意
					if( type === 0 ) data.refuse_reason = this.rejectReason //拒绝原因
					await this.$u.api.auctionAfterSaleDetailAudit(data)
					this.feedback.toast({ title: '操作成功~'})
				}catch(e){
					//TODO handle the exception
					console.log(e)
				}
			},
			
			// 卖家同意/拒绝收货 type = 处理类型：0-拒绝 1-同意
			async afterSaleSellerReceiptDeal(type) {
				try{
					let data = {}
					data.refund_order_no = this.afterSaleInfo.refund_order_no //退款单号
					data.deal_type = type //处理类型：0-拒绝 1-同意
					if( type === 0 ) data.refuse_receipt_reason = this.rejectReason //拒绝原因
					await this.$u.api.auctionAfterSaleDetailSellerReceiptDeal(data)
					this.feedback.toast({ title: '操作成功~'})
				}catch(e){
					//TODO handle the exception
				}
			},
			
		   // 处理售后 type = 处理类型：0-拒绝 1-同意
		   async handleAfterSaleType( type ) {
			   try {
					const { status } = this.afterSaleInfo
					if( status === 0 ) { //待处理
						await this.afterSaleAudit(type)
					}else if( status === 2 ) { //待收货
						await this.afterSaleSellerReceiptDeal(type)
					}
					this.getAfterSaleDetail()
			   } catch(e) {
			   	//TODO handle the exception
			   } finally {
				    this.showRejectMask = false
			   }
		   },
			
			// 变更备注
			rejectReasonChange(e) {
				console.log( e )
				this.rejectReason = e.detail.value.substr(0, 50)
			},
		}
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>