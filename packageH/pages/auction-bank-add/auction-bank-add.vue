<template>
  <view>
    <vh-navbar title="增加银行卡" height="46" />
    <view class="h-20 bg-f5f5f5"></view>
    <view class="ptb-28-plr-32 pb-124">
      <view class="font-wei-600 font-24 text-6 l-h-34">请绑定持卡人本人的银行卡</view>
      <view class="flex-sb-c h-110 bb-s-02-eeeeee">
        <text class="font-wei-500 font-32 text-3 l-h-44">持卡人</text>
        <input v-model="bank.account_holder" placeholder="持卡人本人姓名" placeholder-style="font-size:28rpx; color:#999" class="w-400 font-32 text-3 text-right" />
      </view>
      <view class="flex-sb-c h-110 bb-s-02-eeeeee">
        <text class="font-wei-500 font-32 text-3 l-h-44">银行卡号</text>
        <input v-model="bank.card_no" placeholder="持卡人本人银行卡号" placeholder-style="font-size:28rpx; color:#999" class="w-400 font-32 text-3 text-right" />
      </view>
      <view class="flex-sb-c h-110 bb-s-02-eeeeee">
        <text class="font-wei-500 font-32 text-3 l-h-44">身份证号</text>
        <input v-model="bank.idcard" placeholder="持卡人本人身份证号" placeholder-style="font-size:28rpx; color:#999" class="w-400 font-32 text-3 text-right" />
      </view>
      <view class="flex-sb-c h-110 bb-s-02-eeeeee">
        <text class="font-wei-500 font-32 text-3 l-h-44">预留手机号</text>
        <input v-model="bank.mobile" placeholder="银行卡预留手机号" placeholder-style="font-size:28rpx; color:#999" class="w-400 font-32 text-3 text-right" />
      </view>
      <view class="flex-sb-c mt-32">
        <text class="font-wei-500 font-32 text-3 l-h-44">设置默认</text>
        <image v-if="bank.is_default" :src="ossIcon('/auction/switch_h_56_26.png')" class="w-56 h-26" @click="bank.is_default = 0" />
        <image v-else :src="ossIcon('/auction/switch_56_26.png')" class="w-56 h-26" @click="bank.is_default = 1" />
      </view>
      <view class="mt-20 font-24 text-6 l-h-34">系统会默认使用该账户作为您的收款账户</view>
    </view>
    <view class="p-fixed left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-********-022 z-9999">
      <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="disabled ? 'bg-fce4e3' : 'bg-e80404'" @click="onSubmit">提交绑定</button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'auctionBankAdd', // 增加收款账户
  data: () => ({
    bank: {
      account_holder: '',
      card_no: '',
      idcard: '',
      mobile: '',
      is_default: 0
    }
  }),
  computed: {
    disabled ({ bank }) {
      return !(bank.account_holder && bank.card_no && bank.idcard && bank.mobile)
    }
  },
  methods: {
    onSubmit () {
      if (this.disabled) return
      this.feedback.loading({ title: '添加中...' })
      this.$u.api.addAuctionBank(this.bank).then(() => {
        this.feedback.toast({ title: '添加成功' })
        uni.navigateBack()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
