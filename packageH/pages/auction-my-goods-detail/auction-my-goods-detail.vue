<template>
  <view>
    <vh-navbar :title="title" height="46"></vh-navbar>
    <view v-if="!loading" class="pb-104">
      <view class="p-rela">
        <image :src="ossIcon('/auction/reason_bg_750_170.png')" class="p-abso w-750 h-170" />
        <view class="p-rela flex-s-c ptb-00-plr-44 h-170 font-wei-600 font-36 text-ffffff">
          <text>{{ reasonTitle }}{{ reasonDesc ? ':' : '' }}</text>
          <text class="ml-10 font-28">{{ reasonDesc }}</text>
        </view>
      </view>
      <view class="flex-c-c ptb-28-plr-00">
        <vh-image :loading-type="4" :src="goodsDetail.product_img && goodsDetail.product_img[0]" :width="300" :height="300" :border-radius="6" />
      </view>
      <view class="ptb-00-plr-24">
        <view class="font-28 text-e80404">
          <text>卖家报价</text>
          <text class="ml-06 font-wei-500">¥<text class="font-52">{{ goodsDetail.quote }}</text></text>
        </view>
        <view class="mt-08 font-wei-500 font-30 text-3 l-h-44">{{ goodsDetail.title }}</view>
        <view class="mt-28">
          <AuctionGoodsSetting :goods="goodsDetail" />
        </view>
        <view class="mt-76">
          <AuctionGoodsDesc :goods="goodsDetail" />
        </view>
      </view>
      <view
        v-if="goodsDetail.onsale_review_status === MAuctionGoodsReviewStatus.Rejected || (goodsDetail.onsale_review_status === MAuctionGoodsReviewStatus.PassReview && [MAuctionGoodsStatus.OnAuction, MAuctionGoodsStatus.AuctionAbort, MAuctionGoodsStatus.Unsuccessful].includes(goodsDetail.onsale_status))"
        class="auction-mgdetail__btns p-fixed left-0 bottom-0 flex-e-c ptb-00-plr-24 w-p100 h-104 bg-ffffff b-sh-00021200-022 z-9999"
      >
        <template v-if="goodsDetail.onsale_review_status === MAuctionGoodsReviewStatus.Rejected">
          <button class="vh-btn flex-c-c w-336 h-64 font-wei-500 font-28 text-9 bg-ffffff b-s-02-999999 b-rad-32" @click="onDelete">取消拍卖</button>
          <button class="vh-btn flex-c-c w-336 h-64 font-wei-500 font-28 text-e80404 bg-ffffff b-s-02-e80404 b-rad-32" @click="onReshelf">重新上架</button>
        </template>
        <template v-else-if="goodsDetail.onsale_review_status === MAuctionGoodsReviewStatus.PassReview">
          <button v-if="goodsDetail.onsale_status === MAuctionGoodsStatus.OnAuction" class="vh-btn flex-c-c w-336 h-64 font-wei-500 font-28 text-9 bg-ffffff b-s-02-999999 b-rad-32" @click="jump.navigateTo(`${routeTable.pHAuctionGoodsDetail}?id=${id}`)">查看详情</button>
          <template v-else-if="goodsDetail.onsale_status === MAuctionGoodsStatus.AuctionAbort">
            <button class="vh-btn flex-c-c w-336 h-64 font-wei-500 font-28 text-9 bg-ffffff b-s-02-999999 b-rad-32" @click="jump.navigateTo(`${routeTable.pHAuctionSellerOrderDetail}?orderNo=${orderDetail.order_no}`)">查看订单</button>
            <button v-if="this.orderDetail.order_status === 0" class="vh-btn flex-c-c w-336 h-64 font-wei-500 font-28 text-e80404 bg-ffffff b-s-02-e80404 b-rad-32" @click="onReshelf">重新上架</button>
          </template>
          <button v-else-if="goodsDetail.onsale_status === MAuctionGoodsStatus.Unsuccessful" class="vh-btn flex-c-c w-336 h-64 font-wei-500 font-28 text-e80404 bg-ffffff b-s-02-e80404 b-rad-32" @click="onReshelf">重新上架</button>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionGoodsStatus, MAuctionGoodsReviewStatus } from '@/common/js/utils/mapperModel'
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'auctionMyGoodsDetail', // 我的拍品详情
  data: () => ({
    MAuctionGoodsStatus,
    MAuctionGoodsReviewStatus,
    loading: true,
    id: '',
    goodsDetail: {},
    orderDetail: {}
  }),
  computed: {
    ...mapState(['routeTable']),
    title ({ goodsDetail }) {
      const { onsale_status, onsale_review_status } = goodsDetail
      if (onsale_review_status !== MAuctionGoodsReviewStatus.PassReview) {
        switch (onsale_review_status) {
          case MAuctionGoodsReviewStatus.PendBind:
            return '审核中'
          case MAuctionGoodsReviewStatus.Rejected:
            return '被驳回'
          default:
            return ''
        }
      }
      switch (onsale_status) {
        case MAuctionGoodsStatus.PendAuction:
          return '待拍卖'
        case MAuctionGoodsStatus.OnAuction:
          return '正在拍'
        case MAuctionGoodsStatus.AuctionAbort:
          return '已结束'
        case MAuctionGoodsStatus.Unsuccessful:
          return '已结束'
        default:
          return ''
      }
    },
    reasonTitle ({ goodsDetail }) {
      const { onsale_status, onsale_review_status } = goodsDetail
      if (onsale_review_status !== MAuctionGoodsReviewStatus.PassReview) {
        switch (onsale_review_status) {
          case MAuctionGoodsReviewStatus.PendBind:
            return '平台审核中'
          case MAuctionGoodsReviewStatus.Rejected:
            return '被拒绝原因'
          default:
            return ''
        }
      }
      switch (onsale_status) {
        case MAuctionGoodsStatus.PendAuction:
          return '审核已通过'
        case MAuctionGoodsStatus.OnAuction:
          return '正在拍卖中'
        case MAuctionGoodsStatus.AuctionAbort:
          return '拍卖结束'
        case MAuctionGoodsStatus.Unsuccessful:
          return '拍卖结束'
        default:
          return ''
      }
    },
    reasonDesc ({ goodsDetail }) {
      const { onsale_status, onsale_review_status, reject_remark, sell_time } = goodsDetail
      if (onsale_review_status !== MAuctionGoodsReviewStatus.PassReview) {
        switch (onsale_review_status) {
          case MAuctionGoodsReviewStatus.PendBind:
            return '请兔子君耐心等待～'
          case MAuctionGoodsReviewStatus.Rejected:
            return reject_remark
          default:
            return ''
        }
      }
      switch (onsale_status) {
        case MAuctionGoodsStatus.PendAuction:
          return `预计${sell_time}上线`
        case MAuctionGoodsStatus.AuctionAbort:
          return `竞拍成功-${[1, 2, 3].includes(this.orderDetail.order_status) ? '买家已付款' : '买家未付款'}`
        case MAuctionGoodsStatus.Unsuccessful:
          return '流拍'
        default:
          return ''
      }
    }
  },
  methods: {
    ...mapMutations('auctionGoods', ['SET_AUCTION_GOODS']),
    async loadAuctionGoodsDetail () {
      const params = { id: this.id }
      const res = await this.$u.api.getAuctionGoodsDetail(params)
      const goodsDetail = res?.data || {}
      this.goodsDetail = goodsDetail
      if (this.goodsDetail.onsale_status === MAuctionGoodsStatus.AuctionAbort) {
        await this.loadAuctionOrderDetail()
      }
      this.loading = false
    },
    async loadAuctionOrderDetail () {
      const res = await this.$u.api.auctionOrderDetail({ goods_id: this.id })
      const orderDetail = res?.data || {}
      this.orderDetail = orderDetail
    },
    async onDelete () {
      const loginInfo = uni.getStorageSync('loginInfo') || {}
      await this.$u.api.delAuctionGoods({ id: this.id, uid: loginInfo.uid })
      this.feedback.toast({ title: '取消成功' })
      uni.navigateBack()
    },
    onReshelf () {
      this.SET_AUCTION_GOODS(this.goodsDetail)
      this.jump.navigateTo(this.routeTable.pHAuctionGoodsCreate)
    }
  },
  onLoad (options) {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.id = options.id
      this.loadAuctionGoodsDetail()
    })
  }
}
</script>

<style lang="scss" scoped>
  .auction-mgdetail__btns {
    .vh-btn:not(:first-of-type) {
      margin-left: 30rpx;
    }
  }
</style>
