<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<vh-navbar title='退货售后' :show-border="true"/>
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in">
			<!-- tabs选项栏 -->
			<view class="p-stic z-980" :style="{top: system.navigationBarHeight() + 'px'}">
				<u-tabs :list="tabList" :current="currentTabs" :height="92" :font-size="28" inactive-color="#333" active-color="#E80404"
				 :bar-width="36" :bar-height="8" :bar-style="{ background:'linear-gradient(214deg, #FF8383 0%, #E70000 100%)' }" :is-scroll="false" @change="changeTabs" />
			</view>
			
			<!-- 列表 -->
			<view class="">
				<view v-if="afterSaleList.length" class="ptb-20-plr-00">
					<view class="bg-ffffff b-rad-20 mb-20 mr-24 ml-24 ptb-00-plr-24" v-for="( item, index ) in afterSaleList" :key="index"
					@click="jump.navigateTo(`${routeTable.pHAuctionBuyerAfterSaleDetail}?refundOrderNo=${item.refund_order_no}`)">
						<!-- 列表 -->
						<AuctionAfterSaleListItem :item="item" />
						
						<!-- 按钮信息 -->
						<view class="flex-e-c ptb-28-plr-00">
							<!-- 售后进度 -->
							<view class="ml-20">
								<u-button 
								shape="circle" 
								:hair-line="false" 
								:ripple="true" 
								ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'52rpx', fontSize:'28rpx', color:'#666', border:'1rpx solid #666'}" 
								@click="jump.navigateTo(`${routeTable.pHAuctionAfterSaleProgress}?refundOrderNo=${item.refund_order_no}`)">售后进度</u-button>
							</view>
							<!-- 提醒处理 -->
							<view v-if="[0].includes(item.status)" class="ml-20">
								<u-button 
								shape="circle" 
								:hair-line="false" 
								:ripple="true" 
								ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'52rpx', fontSize:'28rpx', color:'#e80404', border:'1rpx solid #e80404'}"
								@click="reminderHandle(item)">提醒处理</u-button>
							</view>
							<!-- 去寄件 -->
							<view v-if="[1].includes(item.status)" class="ml-20">
								<u-button 
								shape="circle" 
								:hair-line="false" 
								:ripple="true" 
								ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'52rpx', fontSize:'28rpx', color:'#e80404', border:'1rpx solid #e80404'}" 
								@click="jump.navigateTo(`${routeTable.pHAuctionBuyerAfterSaleDetail}?refundOrderNo=${item.refund_order_no}&toSend=1`)">去寄件</u-button>
							</view>
						</view>
					</view>
						
					<u-loadmore :status="loadStatus" />
				</view>
				
				<AuctionEmpty v-else :imageSrc="ossIcon(`/auction_empty/emp1.png`)" :text="'暂无数据'" :paddingTop="155" :paddingBottom="780"/>
			</view>
		</view>
		
		<!-- 骨架屏 -->
		<vh-skeleton v-else bgColor="#FFF" :showLoading="false"/>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default {
		name: 'auction-buyer-after-sale-list',
		
		data() {
			return {
				loading: true, //数据是否加载完成
				tabList: [ //状态栏选项
					{ name: '处理中' }, 
					{ name: '已完成' }, 
				],
				currentTabs: 0, //当前选中tabs
				hasGotAfterSaleList: 0, //是否获取过售后列表
				afterSaleList: [], //售后列表
				page: 1, //当前页
				limit: 10, //每页限制多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //加载状态
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
		
		onShow() {
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) this.init()
			})
		},
		
		
		methods: {
			// 初始化
			async init() {
				this.page = 1
				await this.getAfterSaleList()
				this.loading = false
			},
			
			// 切换状态栏
			changeTabs(index) {
				this.currentTabs = index
				this.page = 1
				this.getAfterSaleList()
			},
			
			// 获取售后列表
			async getAfterSaleList() {
				if(this.hasGotAfterSaleList) this.feedback.loading()
				const { data: { list, total }} = await this.$u.api.auctionBuyerAfterSaleList({
					type: this.currentTabs, //当前tabs
					page: this.page, //页码
					limit: this.limit, //每页限制
				})
				this.page == 1 ? this.afterSaleList = list : this.afterSaleList = [...this.afterSaleList, ...list]
				this.totalPage = Math.ceil(total / this.limit)
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				this.hasGotAfterSaleList = 1
				uni.stopPullDownRefresh() //停止下拉刷新
				this.feedback.hideLoading()
			},
		    
			// 提醒处理
			async reminderHandle({ order_no }) {
				console.log('--------我是提醒处理')
				try{
					await this.$u.api.auctionRemindNotice({ order_no, type: 2 })
					this.feedback.toast({ title: '提醒处理成功~'})
				}catch(e){
					//TODO handle the exception
				}
			}
		},
		
		onPullDownRefresh() {
			this.init()
		},
		
		onReachBottom() {
			if ( this.page == this.totalPage || this.totalPage == 0) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getAfterSaleList()
		}
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>