<template>
  <view>
    <vh-navbar title="保证金" height="46" :background="{ background: '#E80404' }" back-icon-color="#FFF" title-color="#FFF">
      <!-- <view slot="right" class="flex-c-c p-24 font-30 text-ffffff" @click="jump.navigateTo(routeTable.pHAuctionPayeeAccount)">收款账户</view> -->
    </vh-navbar>
    <view v-if="!loading" class="ptb-00-plr-24">
      <view class="auction-el__item mt-20 ptb-32-plr-20 b-rad-10" @click="jump.navigateTo(`${routeTable.pHAuctionEarnestDetail}?type=${MAuctionEarnestType.Bidding}`)">
        <view class="flex-sb-c">
          <text class="font-28 text-3">竞拍保证金</text>
          <view class="flex-c-c">
            <text class="font-wei-500 font-36 text-e80404"><text class="font-32">¥</text>{{ biddingEarnest }}</text>
            <image :src="ossIcon('/about/arrow_r_12_20.png')" class="ml-10 w-12 h-20" />
          </view>
        </view>
        <view class="mt-08 font-24 text-9 l-h-34">适用于购买竞价商品时，参与竞拍的“买家”群体</view>
      </view>

      <view class="auction-el__item mt-20 ptb-32-plr-20 b-rad-10" @click="jump.navigateTo(`${routeTable.pHAuctionEarnestDetail}?type=${MAuctionEarnestType.Entrust}`)">
        <view class="flex-sb-c">
          <text class="font-28 text-3">委托保证金</text>
          <view class="flex-c-c">
            <text class="font-wei-500 font-36 text-e80404"><text class="font-32">¥</text>{{ entrustEarnest }}</text>
            <image :src="ossIcon('/about/arrow_r_12_20.png')" class="ml-10 w-12 h-20" />
          </view>
        </view>
        <view class="mt-08 font-24 text-9 l-h-34">适用于希望通过酒云网出售拍品的“卖家”群体</view>
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionEarnestType } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'

export default {
  name: 'auctionEarnestList', // 拍卖保证金列表
  data: () => ({
    MAuctionEarnestType,
    loading: true,
    biddingEarnest: 0,
    entrustEarnest: 0
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    async load () {
      const [res1, res2] = await Promise.all([
        this.$u.api.searchAuctionEarnestList({ type: MAuctionEarnestType.Bidding }),
        this.$u.api.searchAuctionEarnestList({ type: MAuctionEarnestType.Entrust })
      ])
      this.biddingEarnest = res1?.data?.earnest_money || 0
      this.entrustEarnest = res2?.data?.earnest_money || 0
    }
  },
  onShow () {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (isLogin) {
        this.load().finally(() => {
          this.loading = false
        })
      }
    })
  }
}
</script>

<style lang="scss" scoped>
  .auction-el {
    &__item {
      box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.08);
    }
  }
</style>
