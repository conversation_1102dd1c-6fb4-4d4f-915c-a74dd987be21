<template>
	<view class="content pb-124" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<vh-navbar title='订单详情' :customBack="customBack" />
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in">
			<!-- 订单状态 -->
			<view class="w-p100 h-356 flex-column flex-c-c bg-li-29">
				<vh-image :loading-type="2" :src="ossIcon(`/auction_order_detail/icon${orderInfo.status}.png`)" :width="112" :height="112" />
				
				<!-- 待付款 -->
				<view v-if="[0].includes(orderInfo.status)" class="flex-column flex-c-c">
					<view class="mt-26 font-32 font-wei text-6">待付款</view>
					<view class="flex-c-c mt-20">
						<view class="font-28 text-6">需付款：<text class="font-wei">¥{{ orderInfo.payment_amount }}</text></view>
						<view class="d-flex ml-20">
							<view class="font-28 text-e80404">剩余：</view>
							<vh-count-down :show-days="false" :timestamp="orderInfo.countdown" separator="zh" bg-color="transparent" color="#E80404" @end="onCountDownEnd" />
						</view>
					</view>
				</view>
				
				<!-- 待发货 -->
				<view v-if="[1].includes(orderInfo.status)" class="flex-column flex-c-c">
					<view class="mt-26 font-32 font-wei text-6">待发货</view>
					<view class="mt-10 font-28 text-6">买家已付款，请等待商家发货哦</view>
				</view>
				
				<!-- 待收货 -->
				<view v-if="[2].includes(orderInfo.status)" class="flex-column flex-c-c">
					<view class="mt-26 font-32 font-wei text-6">待收货</view>
					<view class="flex-c-c mt-20">
						<view class="font-28 text-6">自动收货时间：</view>
						<view class="d-flex">
							<vh-count-down 
								:timestamp="orderInfo.auto_receipt_countdown" 
								:show-seconds="false"
								separator="zh" 
								bg-color="transparent" 
								:has-day-margin-right="false" 
								day-color="#666" 
								font-size="28" 
								color="#666" 
								separator-size="28" 
								separator-color="#666"
								 @end="" 
							 />
						</view>
					</view>
				</view>
				
				<!-- 已完成 -->
				<view v-if="[3, 5].includes(orderInfo.status)" class="flex-column flex-c-c">
					<view class="mt-26 font-32 font-wei text-6">已完成</view>
				</view>
				
				<!-- 订单关闭 -->
				<view v-if="[4].includes(orderInfo.status)" class="flex-column flex-c-c">
					<view class="mt-26 font-32 font-wei text-6">交易关闭</view>
				</view>
				
				<!-- 待评价 -->
				<!-- <view v-if="[5].includes(orderInfo.status)" class="flex-column flex-c-c">
					<view class="mt-26 font-32 font-wei text-6">待评价</view>
					<view class="mt-10 font-28 text-6">交易已完成，快来评价吧！</view>
				</view> -->
				
				<!-- 处理中 -->
				<view v-if="[7].includes(orderInfo.status)" class="flex-column flex-c-c">
					<view class="mt-26 font-32 font-wei text-6">处理中</view>
				</view>
				
				<!-- 已退款 -->
				<view v-if="[8].includes(orderInfo.status)" class="flex-column flex-c-c">
					<view class="mt-26 font-32 font-wei text-6">已退款</view>
				</view>
			</view>
			
			<!-- 用户信息 -->
			<view class="bg-ffffff">
				<!-- 物流信息 -->
				<view v-if="[2,3,5,7,8].includes(orderInfo.status) && logisticInfo.traces && logisticInfo.traces.length" class="d-flex j-sb a-start bb-d-01-eeeeee mr-32 ml-32 ptb-32-plr-00"
				 @click="viewLogistics">
				 <image class="w-44 h-44 mt-06" :src="ossIcon(`/auction_order_detail/ship.png`)" mode="aspectFill"></image>
				 <view class="flex-1 d-flex j-sb a-center ml-16">
				 	<view class="w-580">
				 		<view class="font-32 font-wei text-3 text-hidden-3">{{logisticInfo.traces[0].context}}</view>
				 		<view class="mt-12 font-24 text-9 l-h-34">{{logisticInfo.traces[0].ftime}}</view>
				 	</view>
				 	<u-icon name="arrow-right" :size="24" color="#999" />
				 </view>
				</view>
				
				<!-- 收货地址 -->
				<view class="p-rela d-flex p-32">
					<image class="w-44 h-44 mt-06" :src="ossIcon(`/auction_order_detail/add.png`)" mode="aspectFill"></image>
					<view class="flex-1 flex-sb-c ml-16 ">
						<view class="">
							<view class="">
								<text class="font-32 font-wei text-3">{{ orderInfo.consignee }}</text>
								<text class="ml-32 font-28 text-3">{{ orderInfo.consignee_phone }}</text>
							</view>
							<view class="w-480 mt-14 font-24 text-9">{{ orderInfo.province_name }} {{ orderInfo.city_name }} {{ orderInfo.district_name }} {{ orderInfo.address }}</view>
						</view>
					</view>
					<button
						v-if="[1].includes(orderInfo.status)"
						style="top: 50%; right: 32rpx; transform: translateY(-50%);"
						class="p-abso vh-btn flex-c-c w-108 h-52 font-26 text-6 b-rad-26 bg-ffffff b-s-01-d8d8d8"
						@click="oaucPopupVisible = true"
					>修改</button>
					<image class="p-abso left-0 bottom-0 w-p100 h-06" :src="ossIcon(`/auction_order_detail/add_line.png`)" mode=""></image>
				</view>
			</view>
			
			<!-- 订单商品 -->
			<OrderDetailAuctionGoods :orderInfo="orderInfo" />
			
			<!-- 价格明细 -->
			<OrderDetailAuctionPriceInfo :orderInfo="orderInfo" />
			
			<!-- 保证金 -->
			<OrderDetailAuctionBond :bond="orderInfo.earnest_money" :bondMsg="orderInfo.earnest_msg"/>
			
			<!-- 订单明细 -->
			<OrderDetailAuctionDetails :orderInfo="orderInfo" />
			
			<!-- 关注公众号 -->
			<!-- <image class="w-p100 bg-li-29 mt-20" :src="ossIcon(`/auction_order_detail/code.png`)" mode="widthFix" @lonpress="onLongpress" /> -->

            <!-- 热门推荐 -->
			<view class="mt-50 ptb-00-plr-24">
				<AuctionRecommendGoodsList isInit />
			</view>
			
			<!-- 底部按钮 -->
			<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff flex-e-c b-sh-00021200-022 ptb-00-plr-24">
				<!-- 立即支付-->
				<view v-if="[0].includes(orderInfo.status)" class="ml-20">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#E80404', border:'1rpx solid #E80404'}" 
					@click="payment">立即支付</u-button>
				</view>
				
				<!-- 提醒发货-->
				<view v-if="[1].includes(orderInfo.status)" class="ml-20">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#E80404', border:'1rpx solid #E80404'}" 
					@click="remindShipment">提醒发货</u-button>
				</view>
				
				<!-- 删除订单 -->
				<view v-if="[3,4,5,8].includes(orderInfo.status)" class="ml-20">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#999', border:'1rpx solid #999'}" 
					@click="deleteOrder">删除订单</u-button>
				</view>
				
				<!-- 查看物流 -->
				<view v-if="[2,3,5].includes(orderInfo.status)" class="ml-20">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#999', border:'1rpx solid #999'}" 
					@click="viewLogistics">查看物流</u-button>
				</view>
				
				<!-- 确认收货-->
				<view v-if="[2].includes(orderInfo.status)" class="ml-20">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#E80404', border:'1rpx solid #E80404'}" 
					@click="showConfirmReceiptModal = true">确认收货</u-button>
				</view>
				
				<!-- 评价 -->
				<view v-if="[3,5].includes(orderInfo.status) && orderInfo.buyer_is_evaluate === 0" class="ml-20">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#E80404', border:'1rpx solid #E80404'}" 
					@click="jump.navigateTo(`${routeTable.pHAuctionOrderEvaluateNew}?orderNo=${orderInfo.order_no}&goodsImage=${orderInfo.goods_img}`)">评价</u-button>
				</view>
				
				<!-- 售后详情 -->
				<!-- <view v-if="[8].includes(orderInfo.status) && orderInfo.after_sale_status === 2" class="ml-20">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#999', border:'1rpx solid #999'}" 
					@click="applyAfterSale">售后详情</u-button>
				</view> -->
			</view>
			
			<!-- 弹框 -->
			<view class="">
				<!-- 确认收货弹框 -->
				<AuctionConfirmReceiptModal :show="showConfirmReceiptModal" 
				@close="e => { showConfirmReceiptModal = e }"
				@confirm="confirmReceipt()"/>
			</view>
		</view>
        
		<!-- 骨架屏 -->
		<vh-skeleton v-else bgColor="#FFF" :showLoading="false"/>

		<OrderAddressUpdateConfirmPopup v-model="oaucPopupVisible" :orderDetail="orderInfo" />
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		name: 'auction-buyer-order-detail',
		
		data() {
			return {
				loading: true, //数据是否加载完成
				orderNo:'', //订单编号
				goodsId: '',
				orderInfo: {}, //订单信息
				logisticInfo:{}, //物流信息
				
				// 弹框
				showConfirmReceiptModal: false, //是否展示确认收货弹框

				from: '',
				oaucPopupVisible: false,
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
			customBack () {
				if (this.comes.isFromApp(this.from)) {
					return () => {
						wineYunJsBridge.openAppPage({
							client_path: { "ios_path":"goBack", "android_path":"goBack" }
						})
					}
				}
				return null
			},
		},
		
		onLoad(options) {
			this.orderNo = options.orderNo
			this.goodsId = options.goodsId
			this.from = options.from
		},

		onShow () {
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) this.init()
			})
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations(['muAfterSaleGoodsInfo','muLogisticsInfo','muPayInfo']),
			
			// 初始化板块
			// 初始化
			async init() {
				await this.getOrderDetail()
				await this.getLogisticsDetail()
				this.loading = false
			},
			
			// 获取订单详情
			async getOrderDetail(){
				let params = {}
				if (this.goodsId) {
					params = { goods_id: this.goodsId }
				} else {
					params = { order_no: this.orderNo }
				}
				let res = await this.$u.api.auctionOrderDetail(params)
				this.orderInfo = res.data
			},
			
			// 查询物流轨迹
			async getLogisticsDetail() {
				const {order_no, express_type, express_number, status} = this.orderInfo
				if( express_number ) {
					let res = await this.$u.api.logisticsDetails({
						logisticCode: express_number,
						expressType: express_type
					})
					this.logisticInfo = res.data
				}
			},
			
			// 保存图片
			onLongpress () {
				if (this.$app) {
					const url = this.ossIcon('/auction_order_detail/code.png')
					wineYunJsBridge.openAppPage({
						client_path: { ios_path: 'downloadFile', android_path: 'downloadFile' }, 
						ad_path_param: [
							{ ios_key: 'url', ios_val: url, android_key: 'url', android_val: url },
							{ android_key: 'suffix', android_val: 'png' }
						]
					})
				}
			},
			
			
			// 按钮操作板块
			// 付款
			payment() {
				console.log('---------我是立即支付')
				const pageFullPath = this.pages.getPageFullPath()
				if (this.$app) {
					const appData = {
						paylmfor: this.orderInfo,
						type: 5,
						priceString: this.orderInfo.payment_amount,
						androidMainOrderNo: this.orderInfo.order_no,
						androidFrom: '4'
					}
					this.jump.jumpAppPayment(this.$vhFrom, appData)
				} else {
					// const { order_no, payment_amount, countdown } = this.orderInfo
					// this.muPayInfo({
					// 	payPlate: 4, // 买家拍品订单列表
					// 	main_order_no: order_no,
					// 	payment_amount,
					// 	countdown,
					// 	is_cross: 0,
					// 	pageFullPath,
					// 	$isAppPay: true,
					// })
					// this.jump.navigateTo(this.routeTable.pBPayment)
					this.jump.navigateTo(`${this.routeTable.pBPayment}?orderNo=${this.orderInfo.order_no}&payPlate=4`)
				}
			},
			
			// 删除订单
			deleteOrder(){
				this.feedback.showModal({
					content: `确认删除该订单吗？`,
					confirm: async () => {
						try{
							await this.$u.api.cancelDeleteOrder({ type: 2, order_no: this.orderInfo.order_no })
							this.feedback.toast({ title: '删除成功', icon: 'success' })
							setTimeout(() => { 
								if(this.$app) {
									this.jump.removeAppOrderListItem()
								}else{
									this.jump.navigateBack()
								}
							}, 1500)
						}catch(e){
							
						}
					}
				})
			},
			
			// 这是提醒发货 
			async remindShipment(){
				try{
					await this.$u.api.auctionRemindNotice({order_no: this.orderInfo.order_no, type: 0 })
					this.feedback.toast({ title: '提醒发货成功~'})
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 申请售后/售后详情
			// applyAfterSale(){
			// 	const { after_sale_status } = this.orderInfo
			// 	this.muAfterSaleGoodsInfo(this.orderInfo)
			// 	after_sale_status == 2 ? this.jump.navigateTo( this.routeTable.pBAfterSaleDetail ) : 
			// 	this.jump.navigateTo( this.routeTable.pBAfterSaleGoodsService )
			// },
			
			// 查看物流
			viewLogistics() {
				const { goods_img, express_type, express_number, consignee_phone, province_name, city_name, district_name, address } = this.orderInfo
				this.muLogisticsInfo({ 
					image: goods_img, 
					expressType: express_type, 
					logisticCode: express_number, 
					// phone: consignee_phone,  
					// address: province_name + city_name + district_name + address
					})
				this.jump.navigateTo(this.routeTable.pBLogisticsDetail)
			},
			
			// 确认收货
			async confirmReceipt() {
				this.showConfirmReceiptModal = false
				try{
					console.log('======================我是确认收货')
					let data = {}
					data.order_no = this.orderInfo.order_no
					let res = await this.$u.api.auctionConfirmReceipt(data)
					this.feedback.toast({ title: '确认成功', icon: 'success' })
					if(this.$app) {
						this.jump.noticeAppOrderListRefresh(false)
					}
					this.orderInfo.status = 3
				}catch(e){
					
				}
			},

			onCountDownEnd () {
				this.orderInfo.status = 4
			}
		}
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>