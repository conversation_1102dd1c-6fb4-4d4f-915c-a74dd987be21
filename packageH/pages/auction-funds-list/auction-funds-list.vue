<template>
  <view>
    <vh-navbar title="资金明细" height="46" :background="{ background: navbarBackground }" back-icon-color="#FFF" title-color="#FFF" :customStatusBarHeight="20">
      <!-- <view slot="right" class="flex-c-c p-24 font-30 text-ffffff" @click="jump.navigateTo(routeTable.pHAuctionPayeeAccount)">收款账户</view> -->
    </vh-navbar>
    <view v-if="!loading">
      <image :src="ossIcon('/auction/funds_list_bg_750_516.png')" class="p-abso top-0 w-p100 h-516" />
      <view class="p-rela">
        <view class="auction-fdetail__card p-rela d-flex a-start mtb-00-mlr-auto mt-60 pt-98 w-702 h-352" @click="fundsIntroPopupVisible = true">
          <view class="flex-c-c flex-column w-350">
            <text class="font-wei-500 font-60 text-ffffff l-h-50"><text class="font-48">¥</text>{{ ' ' }}{{ stats.total_consume }}</text>
            <view class="flex-c-c mt-10">
              <text class="font-24 text-ffffff l-h-34">总消费</text>
              <image :src="ossIcon('/auction/tips_24_22.png')" class="ml-06 w-24 h-22" />
            </view>
          </view>
          <view style="border: 2rpx solid rgba(255, 255, 255, 0.26);" class="w-02 h-136"></view>
          <view class="flex-c-c flex-column w-350">
            <text class="font-wei-500 font-60 text-ffffff l-h-50"><text class="font-48">¥</text>{{ ' ' }}{{ stats.pending_income }}</text>
            <view class="flex-c-c mt-10">
              <text class="font-24 text-ffffff l-h-34">待收益</text>
              <image :src="ossIcon('/auction/tips_24_22.png')" class="ml-06 w-24 h-22" />
            </view>
            <view class="mt-04 font-24 text-ffffff l-h-34">总收益：{{ stats.total_profit }}元</view>
          </view>
          <view class="auction-fdetail__ecard p-abso flex-c-c w-134 h-54" @click.stop="jump.navigateTo(routeTable.pHAuctionEarnestList)">
            <image :src="ossIcon('/auction/arrow_l_8_16.png')" class="w-08 h-16" />
            <text class="ml-06 font-24 text-ffffff l-h-34">保证金</text>
          </view>
        </view>
        <view class="auction-fdetail__tabs flex-c-c mt-20">
          <u-tabs :list="tabsList" :current="currentTabIndex" height="92" font-size="32" active-color="#e80404" inactive-color="#666" bar-width="36" bar-height="8" gutter="92" @change="onTabChange" />
        </view>
        <view v-if="monthList.length">
          <view v-for="(monthItem, monthIndex) in monthList" :key="monthIndex">
            <view class="flex-s-c ptb-00-plr-38 h-74 font-28 text-6 bg-f2f2f2">{{ monthItem.monthTime }}</view>
            <view v-for="(item, index) in monthItem.list" :key="index">
              <view v-if="index" class="mtb-00-mlr-auto w-686 h-02 bg-eeeeee"></view>
              <view class="flex-c-c p-32 pr-24" @click="jumpFundsDetail(item)">
                <image :src="ossIcon(`/auction/${item.type === MAuctionFundsOrderType.Earnest ? 'funds_earnest_72' : 'funds_order_72'}.png`)" class="w-72 h-72" />
                <view class="ml-24 w-576">
                  <view class="flex-sb-c">
                    <view class="w-448 font-wei-500 font-28 text-3 l-h-40">{{ item.type | toText('MAuctionFundsOrderTypeText') }}-{{ item.title }}</view>
                    <view class="font-wei-600 font-32 text-3 l-h-44">{{ item.source_type === MAuctionFundsType.Income ? '+' : '-' }}{{ item.payment_amount }}</view>
                  </view>
                  <view class="flex-sb-c">
                    <view class="font-24 text-9">{{ item.created_time }}</view>
                    <!-- <view class="font-24 text-e80404 l-h-34">{{ item.status | toText('MAuctionFundsStatusText') }}</view> -->
                    <view class="font-24 text-e80404 l-h-34">{{ item.status_cn }}</view>
                  </view>
                </view>
                <view class="ml-10 w-12 h-20"></view>
              </view>
            </view>
          </view>
        </view>
        <AuctionNone v-else class="pt-160" img="/auction/none_442_400.png" imgClazz="w-442 h-400" desc="暂无记录～" descClazz="" />
      </view>
    </view>
    <AuctionFundsIntro v-model="fundsIntroPopupVisible" />
    <u-popup v-model="tipsPopupVisible" mode="center" width="552rpx" height="414rpx" border-radius="20">
      <view class="p-rela w-552 h-414">
        <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-552 h-414" />
        <view class="p-rela d-flex flex-column h-p100">
          <view class="flex-1 ptb-00-plr-38 pt-52 text-center">
            <view class="font-wei-600 font-32 text-3">温馨提示</view>
            <view class="mt-50 font-28 text-6 l-h-48">
              <view>绑定收款账户请先通过</view>
              <view>实名认证后再申请</view>
            </view>
          </view>
          <view class="h-92 font-wei-500 font-28 text-e80404 text-center" @click="tipsPopupVisible = false">知道了</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { MAuctionFundsType, MAuctionFundsOrderType } from '@/common/js/utils/mapperModel'
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'auctionFundsList', // 拍卖资金明细
  data: () => ({
    MAuctionFundsType,
    MAuctionFundsOrderType,
    loading: true,
    navbarBackground: 'transparent',
    stats: {},
    monthList: [],
    query: { page: 1, limit: 10 },
    totalPage: 0,
    tabsList: [
      { name: '全部' },
      { name: '收入', type: MAuctionFundsType.Income },
      { name: '支出', type: MAuctionFundsType.Expend }
    ],
    currentTabIndex: 0,
    fundsIntroPopupVisible: false,
    tipsPopupVisible: false,
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    ...mapMutations('auctionFunds', ['UPDATE_FUNDS_DETAIL']),
    async init () {
      await Promise.all([
        this.loadList(),
        this.loadStats()
      ])
      this.loading = false
    },
    async loadList () {
      const { type } = this.tabsList[this.currentTabIndex]
      const params = { ...this.query }
      if (type) params.source_type = type
      const res = await this.$u.api.searchAuctionFundsList(params)
      const { list = [], total = 0 } = res?.data || {}
      if (this.query.page === 1) {
        this.monthList = []
      }
      list.forEach(item => {
        const monthTime = this.$u.timeFormat(item.created_time, 'yyyy年mm月dd日')
        const findMonthItem = this.monthList.find(item => item.monthTime === monthTime)
        if (findMonthItem) {
          findMonthItem.list.push(item)
        } else {
          this.monthList.push({ monthTime, list: [item] })
        }
      })
      // this.list = this.query.page === 1 ? list : this.list.concat(list)
      this.totalPage = Math.ceil(total / this.query.limit)
    },
    async loadStats () {
      const res = await this.$u.api.getAuctionFundsStats()
      this.stats = res?.data || {}
    },
    onTabChange (index) {
      this.currentTabIndex = index
      this.query = this.$options.data().query
      this.loadList()
    },
    jumpFundsDetail (item) {
      console.log('jumpFundsDetail', item)
      item.$isFromFundsList = true
      this.UPDATE_FUNDS_DETAIL(item)
      this.jump.navigateTo(this.routeTable.pHAuctionFundsDetail)
    }
  },
  onShow () {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.init()
    })
  },
  onPageScroll (res) {
    if(res.scrollTop <= 100){
      this.navbarBackground = `rgba(224, 20, 31, ${res.scrollTop / 100})`
    }else{
      this.navbarBackground = `rgba(224, 20, 31, 1)`
    }
  },
  onReachBottom () {
    if (this.query.page === this.totalPage || !this.totalPage) return
    this.query.page++
    this.loadList()
  }
}
</script>

<style lang="scss" scoped>
  .auction-fdetail {
    &__card {
      @include iconBgImg('/auction/funds_list_card_706_352.png');
      background-size: 702rpx 352rpx;
      border-radius: 10rpx;
    }

    &__ecard {
      bottom: 30rpx;
      left: 0;
      background: linear-gradient(90deg, #FFE275 0%, #FFC451 100%);
      box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0,0.19);
      border-radius: 0 35rpx 35rpx 0;
    }

    &__tabs {
      ::v-deep {
        .u-tab {
          &-item {
            font-weight: 600 !important;
            vertical-align: top;
          }

          &-bar {
            bottom: auto;
            background: linear-gradient(214deg, #FF8383 0%, #E70000 100%);
          }
        }
      }
    }
  }
</style>
