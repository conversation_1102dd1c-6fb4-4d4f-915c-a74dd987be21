<template>
  <view>
    <vh-navbar v-if="!isFromMsgCenter" title="消息" height="46"></vh-navbar>
    <view v-if="!disabledMsg" class="flex-sb-c ptb-00-plr-24 h-76 bg-f4f4f4">
      <view class="flex-c-c">
        <image :src="ossIcon('/auction/close_circle_32.png')" class="w-32 h-32" />
        <view class="ml-22 font-size4 text-6 l-h-34">开启通知，不错过任何潜在的成交机会</view>
      </view>
      <button class="vh-btn flex-c-c w-108 h-44 font-wei-500 font-20 text-ffffff bg-e80404 b-rad-23" @click="onEnableMsg">去开启</button>
    </view>
    <view v-if="!loading">
      <view v-for="(item, index) in list" :key="index">
        <view class="flex-c-c ptb-32-plr-24" @click="jump.navigateTo(`${routeTable.pHAuctionMsgDetail}?type=${item.type}`)">
          <image :src="ossIcon(item.icon)" class="mr-20 w-80 h-80" />
          <view class="flex-1">
            <view class="flex-sb-c">
              <text class="font-wei-500 font-28 text-3 l-h-40">{{ item.title }}</text>
              <text class="font-24 text-3 l-h-34">{{ item.time }}</text>
            </view>
            <view class="flex-sb-c mt-06 pr-10">
              <view class="w-528 font-24 text-6 l-h-34 text-hidden-2">{{ item.desc }}</view>
              <view
                v-if="item.unreadNum"
                class="flex-c-c font-wei-500 font-18 text-ffffff bg-e80404 b-rad-14"
                :class="item.unreadNum > 1 ? 'ml-36 w-28 h-28' : 'ml-48 w-16 h-16'"
              >{{ item.unreadNum > 1 ? item.unreadNum : '' }}</view>
            </view>
          </view>
        </view>
        <view v-if="item.isShowBorder" class="ml-124 h-02 bg-eeeeee"></view>
        <view v-if="item.isShowDivide" class="h-12 bg-eeeeee"></view>
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionNoticeType } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'

export default {
  name: 'auctionMsg', // 拍卖消息
  props: {
    isFromMsgCenter: {
      type: Boolean,
      default: false,
    }
  },
  data: () => ({
    loading: true,
    disabledMsg: true,
    list: [
      { title: '拍卖', type: MAuctionNoticeType.Auction, icon: '/auction/auction_80.png', desc: '', time: '', unreadNum: 0, isShowBorder: true },
      { title: '通知', type: MAuctionNoticeType.Notice, icon: '/auction/notice_80.png', desc: '', time: '', unreadNum: 0, isShowBorder: true },
      { title: '订单', type: MAuctionNoticeType.Order, icon: '/auction/order_80.png', desc: '', time: '', unreadNum: 0, isShowDivide: true },
      { title: '评论', type: MAuctionNoticeType.Comment, icon: '/auction/comment_80.png', desc: '', time: '', unreadNum: 0 },
    ]
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    async load () {
      const res = await this.$u.api.getAuctionMsg()
      const data = res?.data || {}
      const keyValues = ['auction', 'notice', 'order', 'comment']
      keyValues.forEach((keyValue, index) => {
        const { title, content, createTime, ct } = data[keyValue]
        Object.assign(this.list[index], { desc: ['notice', 'comment'].includes(keyValue) ? title : content, time: createTime, unreadNum: ct > 99 ? 99 : ct })
      })
    },
    async loadDisabledMsg () {
      const res = await this.$u.api.userSpecifiedData({field: 'is_pushsysbtn'})
      const { is_pushsysbtn = 0 } = res?.data || {}
      this.disabledMsg = !!(+is_pushsysbtn)
    },
    onEnableMsg () {
      const { $app, $android, $ios } = this
      if ($app) {
        // if ($android) wineYunJsBridge.getDataFromApp(5)
        // else if ($ios) {
        //   wineYunJsBridge.openAppPage({
        //     client_path: 'opentNotificationAlert'
        //   })
        // }
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'SystemPushViewController', android_path: 'com.stg.rouge.activity.NoticeSettingActivity' } 
        })
      }
    }
  },
  onShow () {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      const reqList = [this.load()]
      const { $app, $android, $ios } = this
      if ($app) {
        // if ($android) {
        //   const res = wineYunJsBridge.getDataFromApp(4)
        //   this.disabledMsg = res === '1'
        // } else if ($ios) {
        //   window.appMsgInfo = ({ isEnable = false }) => {
        //     this.disabledMsg = isEnable
        // 	}
        //   wineYunJsBridge.openAppPage({ client_path: 'isUserNotificationEnable' })
        // }
        reqList.push(this.loadDisabledMsg())
      }
      Promise.all(reqList).finally(() => {
        this.loading = false
      })
    })
  },
  onPullDownRefresh() {
    this.load().finally(() => {
      uni.stopPullDownRefresh()
    })
  },
}
</script>

<style lang="scss" scoped>
</style>
