<template>
	<view class="content pb-124" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<vh-navbar title='订单详情' :customBack="customBack" />
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in">
			<!-- 订单状态 -->
			<view class="w-p100 h-356 flex-column flex-c-c bg-li-28">
				<vh-image :loading-type="2" :src="ossIcon(`/auction_seller_order_detail/icon${orderInfo.status}.png`)" :width="112" :height="112" />
				
				<!-- 待付款 -->
				<view v-if="[0].includes(orderInfo.status)" class="flex-column flex-c-c mt-26">
					<view class="font-32 font-wei text-6">待付款</view>
					<view class="flex-c-c mt-20">
						<view class="font-28 text-6">需付款：¥{{ orderInfo.payment_amount }}</view>
						<view class="d-flex ml-20">
							<view class="font-28 text-e80404">剩余：</view>
							<vh-count-down :show-days="false" :timestamp="orderInfo.countdown" separator="zh" bg-color="transparent" color="#E80404" @end="" />
						</view>
					</view>
				</view>
				
				<!-- 待发货 -->
				<view v-if="[1].includes(orderInfo.status)" class="">
					<view v-if="showPickUpInfo" class="flex-column flex-c-c mt-20">
						<view class="font-32 font-wei text-6">请确认上门取件信息</view>
					</view>
					<view v-else class="flex-column flex-c-c mt-26">
						<view class="font-32 font-wei text-6">待发货</view>
						<view class="mt-10 font-28 text-6">买家已付款，请等待商家发货哦</view>
					</view>
				</view>
				
				<!-- 待收货 -->
				<view v-if="[2].includes(orderInfo.status)" class="flex-column flex-c-c mt-26">
					<view class="font-32 font-wei text-6">待收货</view>
					<view class="flex-c-c mt-20">
						<view class="font-28 text-6">自动收货时间：</view>
						<view class="d-flex">
							<vh-count-down 
								:timestamp="864000" 
								:show-seconds="false"
								separator="zh" 
								bg-color="transparent" 
								:has-day-margin-right="false" 
								day-color="#666" 
								font-size="28" 
								color="#666" 
								separator-size="28" 
								separator-color="#666"
								 @end="" 
							 />
						</view>
					</view>
				</view>
				
				<!-- 已完成 -->
				<view v-if="[3].includes(orderInfo.status)" class="mt-26 font-32 font-wei text-6">已完成</view>
				
				<!-- 订单关闭 -->
				<view v-if="[4].includes(orderInfo.status)" class="mt-26 font-32 font-wei text-6">订单关闭</view>
				
				<!-- 已退款 -->
				<view v-if="[7].includes(orderInfo.status)" class="flex-column flex-c-c mt-26">
					<view class="font-32 font-wei text-6">已退款</view>
					<view class="mt-10 font-28 text-6">已退回到-支付宝/微信</view>
				</view>
			</view>
			
			<!-- 上门取件 -->
			<view v-if="showPickUpInfo" class="bg-ffffff o-hid mb-20 ptb-00-plr-44">
				<!-- 寄件方式 -->
				<view class="flex-sb-c bb-s-01-f8f8f8 pt-44 pb-32">
					<view class="font-28 font-wei text-3">寄件方式</view>
					<view class="font-28 text-3">上门取件</view>
				</view>
				
				<!-- 取件地址 -->
				<view class="flex-sb-c bb-s-01-f8f8f8 ptb-32-plr-00" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=6`)">
					<view class="font-30 text-3">取件地址</view>
					<view class="flex-c-c">
						<view class="w-474 mr-16">
							<view class="text-right">{{ orderInfo.return_province_name }} {{ orderInfo.return_city_name }} {{ orderInfo.return_district_name }} {{ orderInfo.return_address }}</view>
							<view class="text-right font-24 text-9">{{ orderInfo.return_consignee }} {{ orderInfo.return_consignee_phone }}</view>
						</view>
						<u-icon name="arrow-right" :size="24" color="#333" />
					</view>
				</view>
				
				<!-- 上门时间 -->
				<view class="flex-sb-c ptb-32-plr-00" @click="openPickUpTimePopup">
					<view class="font-30 text-3">上门时间</view>
					<view class="flex-c-c">
						<view class="mr-16 font-30 text-3">{{ `${findPickUpDate.day} ${findPickUpDate.timeRange}` }}</view>
						<u-icon name="arrow-right" :size="24" color="#333" />
					</view>
				</view>
			</view>
			
			<!-- 用户信息 -->
			<view class="bg-ffffff">
				<!-- 物流信息 -->
				<view v-if="[2,3,7].includes(orderInfo.status) && logisticInfo.traces && logisticInfo.traces.length" class="d-flex j-sb a-start bb-d-01-eeeeee mr-32 ml-32 ptb-32-plr-00"
				@click="viewLogistics">
					<image class="w-44 h-44 mt-06" :src="ossIcon(`/auction_buyer_order_detail/ship.png`)" mode="aspectFill"></image>
					<view class="flex-1 d-flex j-sb a-center ml-16">
						<view class="w-580">
							<view class="font-32 font-wei text-3 text-hidden-3">{{logisticInfo.traces[0].context}}</view>
							<view class="mt-12 font-24 text-9 l-h-34">{{logisticInfo.traces[0].ftime}}</view>
						</view>
						<u-icon name="arrow-right" :size="24" color="#999" />
					</view>
				</view>
				
				<!-- 收货地址 -->
				<view class="p-rela d-flex p-32">
					<image class="w-44 h-44 mt-06" :src="ossIcon(`/auction_seller_order_detail/add.png`)" mode="aspectFill"></image>
					<view class="ml-16">
						<view class="">
							<text class="font-32 font-wei text-3">{{ orderInfo.consignee }}</text>
							<text class="ml-32 font-28 text-3">{{ orderInfo.consignee_phone }}</text>
						</view>
						
						<view class="mt-14 font-24 text-9">{{ orderInfo.province_name }} {{ orderInfo.city_name }} {{ orderInfo.district_name }} ****</view>
					</view>
					<image class="p-abso left-0 bottom-0 w-p100 h-06" :src="ossIcon(`/auction_seller_order_detail/add_line.png`)" mode=""></image>
				</view>
			</view>
			
			<!-- 订单商品 -->
			<AuctionOrderDetailGoods :orderInfo="orderInfo" />
			
			<!-- 价格明细 -->
			<AuctionOrderDetailPriceInfo :type="1" :orderInfo="orderInfo" />
			
			<!-- 保证金 -->
			<AuctionOrderDetailBond :bond="orderInfo.earnest_money" :bondMsg="orderInfo.earnest_msg"/>
			
			<!-- 订单明细 -->
			<AuctionOrderDetails :orderInfo="orderInfo" />
			
			<!-- 底部按钮 -->
			<view v-if="![4].includes(orderInfo.status)" class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff flex-e-c b-sh-00021200-022 ptb-00-plr-24">
				<view v-if="showPickUpInfo" class="flex-e-c">
					<u-button
					shape="circle" 
					:hair-line="false" 
					:ripple="true" 
					ripple-bg-color="#FFF" 
					:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#2E7BFF', border:'1rpx solid #2E7BFF'}" 
					@click="submitPickupInfo">提交</u-button>
				</view>
				
				<view v-else class="flex-e-c">
					<!-- 提醒付款-->
					<view v-if="[0].includes(orderInfo.status)" class="">
						<u-button 
						shape="circle" 
						:hair-line="false" 
						:ripple="true" 
						ripple-bg-color="#FFF" 
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#2E7BFF', border:'1rpx solid #2E7BFF'}" 
						@click="remindPayment">提醒付款</u-button>
					</view>
					
					<!-- 去发货 -->
					<view v-if="[1].includes(orderInfo.status)" class="">
						<u-button 
						shape="circle" 
						:hair-line="false" 
						:ripple="true" 
						ripple-bg-color="#FFF" 
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#2E7BFF', border:'1rpx solid #2E7BFF'}" 
						@click="showDeliveryMethodPop = true">去发货</u-button>
					</view>
					
					<!-- 查看物流 -->
					<view v-if="[2].includes(orderInfo.status)" class="">
						<u-button 
						shape="circle" 
						:hair-line="false" 
						:ripple="true"
						ripple-bg-color="#FFF" 
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#2E7BFF', border:'1rpx solid #2E7BFF'}" 
						@click="viewLogistics">查看物流</u-button>
					</view>
					
					<!-- 查看评价 -->
					<view v-if="[3].includes(orderInfo.status)" class="">
						<u-button 
						shape="circle" 
						:hair-line="false" 
						:ripple="true" 
						ripple-bg-color="#FFF" 
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#999', border:'1rpx solid #999'}" 
						@click="jump.navigateTo(`${routeTable.pHAuctionOrderEvaluateList}?currentTabs=1`)">查看评价</u-button>
					</view>
					
					<!-- 售后详情 -->
					<view v-if="[7].includes(orderInfo.status)" class="">
						<u-button 
						shape="circle" 
						:hair-line="false" 
						:ripple="true" 
						ripple-bg-color="#FFF" 
						:custom-style="{width:'208rpx', height:'64rpx', fontSize:'28rpx', fontWeight:'bold', color:'#999', border:'1rpx solid #999'}" 
						@click="jump.navigateTo(`${routeTable.pHAuctionSellerAfterSaleDetail}?refundOrderNo=${orderInfo.refund_order_no}`)">售后详情</u-button>
					</view>
				</view>
			</view>
			
			<!-- 弹框 -->
			<view class="">
				<!-- 寄件方式弹框 -->
				<u-popup v-model="showDeliveryMethodPop" mode="bottom" height="588" :border-radius="20">
					<view class="">
						<view class="ptb-00-plr-48">
							<!-- 标题 -->
							<view class="mt-36 text-center font-36 font-wei text-3">寄件方式</view>
							<!-- 内容 -->
							<view class="mt-60">
								<view class="font-28 font-wei text-3">上门取件</view>
								<view class="mt-10 font-24 text-ff9127 l-h-34">请选择上门取件时间，我们将为您上门取件，可随时修改或取消时间</view>
								<view class="mt-24">
									<view v-if="pickUpDateList.length" class="flex-sb-c">
										<view class="d-flex">
											<view
												v-for="(item, index) in pickUpDateList.slice(0, pickUpDateShowNum)" :key="index"
												class="w-260 h-94 d-flex flex-column j-center a-center b-rad-10 font-24 text-9 l-h-40 mr-20"
												:class="pickUpDateId === item.pickUpDateId ? 'bg-ffffff b-s-02-ff9127 text-ff9127' : 'bg-f7f7f7'"
												@click="pickUpDateId = item.pickUpDateId"
											>
												<text>{{ item.day }}</text>
												<text>{{ item.timeRange }}</text>
											</view>
										</view>
										<view v-if="pickUpDateList.length > pickUpDateShowNum" class="d-flex a-center" @click="openPickUpTimePopup">
											<view class="mr-10 font-28 text-9 l-h-34">更多</view>
											<u-icon name="arrow-right" :size="24" color="#999" />
										</view>
									</view>
									<view v-else class="font-28 text-9">暂无上门取件时间</view>
								</view>
							</view>
						</view>
						<!-- 底部按钮 -->
						<view v-if="pickUpDateList.length" class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff flex-c-c b-sh-00021200-022">
							<u-button 
							:disabled="!pickUpDateId"
							shape="circle" 
							:hair-line="false" 
							:ripple="true" 
							ripple-bg-color="#FFF"
							:custom-style="{width:'646rpx', height:'64rpx', backgroundColor: !pickUpDateId ? '#FCE4E3' : '#E80404', fontSize:'28rpx', fontWeight:'bold', color:'#FFF'}" 
							@click="confirmDeliveryMethod">确认</u-button>
						</view>
					</view>
				</u-popup>
			    
				<!-- 选择上门时间弹框 -->
				<u-popup v-model="showPickerDatePop" mode="bottom" height="614" :border-radius="20">
					<view class="d-flex j-center a-center h-122 font-wei-600 font-36 text-3">选择上门时间段</view>
					<view class="h-388">
						<picker-view indicator-class="picker-view-padding" :value="pickUpDatePicker" @change="handlePickerChange" class="h-p100">
							<picker-view-column>
								<view class="d-flex j-center a-center ptb-00-plr-08 font-32 text-3" v-for="(day, index) in pickUpDayList" :key="index">
									<view class="o-hid w-s-now t-o-ell">{{ day }}</view>
								</view>
							</picker-view-column>
							<picker-view-column>
								<view class="d-flex j-center a-center ptb-00-plr-08 font-32 text-3" v-for="(time, index) in pickUpTimeList" :key="index">
									<view class="o-hid w-s-now t-o-ell">{{ time }}</view>
								</view>
							</picker-view-column>
						</picker-view>
					</view>
					<view class="d-flex j-center a-center h-104 b-sh-00021200-022" @click="confirmPickerDate">
						<button class="d-flex j-center a-center w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32">{{ pickUpPickerBtnText }}</button>
					</view>
				</u-popup>
			</view>
		</view>
	    
		<!-- 骨架屏 -->
		<vh-skeleton v-else bgColor="#FFF" :showLoading="false"/>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default {
		name: 'auction-seller-order-detail',
		
		data() {
			return {
				from: '', //来自哪个端
				loading: true, //数据是否加载完成
				orderNo:'', //订单编号
				toShip: '', //去发货
				orderInfo: {}, //订单信息
				logisticInfo:{}, //物流信息
				
				pickUpDateId: '', //日期id
				pickUpDate: [], // 完整的日期列表（后端返回）
				pickUpDateList: [], //日期列表（二级日期列表）
				pickUpDateShowNum: 2, //默认显示数量
				pickUpDatePicker: [0, 0], //列索引
				pickUpDateListLoading: false, //列表是否加载中
				showDeliveryMethodPop: false, //是否展示寄件方式弹框
				showPickerDatePop: false, //是否展示上门时间弹框
				confirmPickupInfo: false, //确认过上门信息
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable', 'addressInfoState']),
			
			// 显示上门信息
			showPickUpInfo() {
				if([1].includes(this.orderInfo.status) && this.pickUpDateId && this.confirmPickupInfo && !this.pickUpDateListLoading ) return true
				return false
			},
			
			findPickUpDate ({ pickUpDateId, pickUpDateList }) {
				return pickUpDateList.find(item => item.pickUpDateId === pickUpDateId)
			},
			pickUpDayList ({ pickUpDate }) {
				return pickUpDate.map(item => item.day)
			},
			pickUpTimeList ({ pickUpDate, pickUpDatePicker }) {
				const findItem = pickUpDate[pickUpDatePicker[0]]
				return findItem ? findItem.timeList.map(item => item.timeRange) : []
			},
			pickUpPickerBtnText ({ pickUpDayList, pickUpTimeList, pickUpDatePicker }) {
				const [dayIndex, timeIndex] = pickUpDatePicker
				return `${pickUpDayList[dayIndex]} ${pickUpTimeList[timeIndex]}`
			},

			customBack () {
				if (this.comes.isFromApp(this.from)) {
					return () => {
						wineYunJsBridge.openAppPage({
							client_path: { "ios_path":"goBack", "android_path":"goBack" }
						})
					}
				}
				return null
			},
		},
		
		onLoad(options) {
			this.orderNo = options.orderNo
			if( options.toShip ) this.toShip = options.toShip
			this.from = options.from
		},
		
		onShow() {
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) {
					this.initOnLoad()
					this.initOnShow()
				}
			})
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations('auction', ['muLogisticsInfo']),
			
			// 初始化板块
			// 初始化onLoad
			async initOnLoad() {
				await this.getOrderDetail()
				await this.getLogisticsDetail()
				await this.getPickupPeriod()
				this.loading = false
			},
			
			// 初始化onShow
			initOnShow() {
				this.changeAddress()
			},
			
			// 改变地址
			changeAddress() {
				if( Object.keys(this.addressInfoState).length && Object.keys(this.orderInfo).length && this.orderInfo.status === 1 ) {
					const { province_id, province_name, city_id, city_name, town_id, town_name, address, consignee, consignee_phone} = this.addressInfoState
					this.orderInfo.return_province_id = province_id
					this.orderInfo.return_province_name = province_name
					this.orderInfo.return_city_id = city_id
					this.orderInfo.return_city_name = city_name
					this.orderInfo.return_district_id = town_id
					this.orderInfo.return_district_name = town_name
					this.orderInfo.return_address = address
					this.orderInfo.return_consignee = consignee
					this.orderInfo.return_consignee_phone = consignee_phone
				}
			},
			
			// 获取订单详情
			async getOrderDetail(){
				try{
					let res = await this.$u.api.auctionOrderDetail({order_no: this.orderNo})
					this.orderInfo = res.data
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 查询物流轨迹
			async getLogisticsDetail() {
				const {order_no, express_type, express_number, status} = this.orderInfo
				if( express_number ) {
					let res = await this.$u.api.logisticsDetails({
						logisticCode: express_number,
						expressType: express_type
					})
					this.logisticInfo = res.data
				}
			},
			
			// 获取取件时段
			async getPickupPeriod() {
				try{
					if( Object.keys(this.orderInfo).length &&  this.orderInfo.status === 1) {
						this.pickUpDateListLoading = true
						const { order_no, province_name, city_name, district_name, address } = this.orderInfo
						let data = {}
						data.type = 0 //类型：0-卖家发货 1-买家售后
						data.order_no = order_no //订单号
						data.senderProvince = province_name //寄件人省
						data.senderCity = city_name //寄件人市
						data.senderDistrict = district_name //寄件人区
						data.senderDetailAddress = address //寄件人详细地址
						const res = await this.$u.api.auctionPickupPeriod(data)
						if( !res.data.length ) return
						const list = res.data
						this.pickUpDate = list.filter(item => item.timeList.length)
						this.pickUpDateList = list.map(item => {
							const { day } = item
							return item.timeList.map(({ startTime, endTime, timeRange }) => ({
								pickUpDateId: `${day}&${timeRange}`,
								timeRange,
								day,
								startTime,
								endTime
							}))
						}).flat()
						if (!this.pickUpDateList.some(item => item.pickUpDateId === this.pickUpDateId)) {
							this.pickUpDateId = ''
						}
						this.pickUpDateListLoading = false
						if( this.toShip ) this.showDeliveryMethodPop = true
						console.log(res)
					}
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 保存图片
			saveImage(){
				this.feedback.toast({ title: 'h5暂不支持保存~' })
				// uni.saveImageToPhotosAlbum({
				// 	filePath: this.ossIcon(`/auction_i_got_order_detail/code.png`),
				// 	success:res=>{
				// 		this.feedback.toast({ title: '保存成功~' })
				// 	}
				// })
			},
			
			
			// 按钮操作板块
			// 提醒付款
			async remindPayment() {
				try{
					await this.$u.api.auctionRemindNotice({order_no: this.orderInfo.order_no, type: 1 })
					this.feedback.toast({ title: '提醒付款成功~'})
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 查看物流
			viewLogistics() {
				const { goods_img, express_type, express_number, consignee_phone, province_name, city_name, district_name, address } = this.orderInfo
				this.muLogisticsInfo({ 
					image: goods_img, 
					expressType: express_type, 
					logisticCode: express_number, 
					phone: consignee_phone,  
					address: province_name + city_name + district_name + address
					})
				this.jump.navigateTo(this.routeTable.pHAuctionLogisticsInfo)
			},
			
			
			// 填写上门信息板块
			// 打开上门时间段选择弹框
			openPickUpTimePopup () {
				if (!this.pickUpDateList.length) return
				if (this.pickUpDateId) {
					const [day, time] = this.pickUpDateId.split('&')
					const dayIndex = this.pickUpDayList.findIndex(item => item === day)
					const timeIndex = this.pickUpTimeList.findIndex(item => item === time)
					this.pickUpDatePicker = [dayIndex, timeIndex]
				}
				this.showPickerDatePop = true
			},
			
			// 选择日期
			handlePickerChange (e) {
				this.pickUpDatePicker = e.detail.value
			},
			
			// 确认寄件方式
			confirmDeliveryMethod() {
				console.log('============我是确认寄件方式')
				this.confirmPickupInfo = true
				this.showDeliveryMethodPop = false
			},
			
			// 确认选择时间
			confirmPickerDate() {
				console.log('============我是确认确认选择时间')
				const [dayIndex, timeIndex] = this.pickUpDatePicker
				this.pickUpDateId = `${this.pickUpDayList[dayIndex]}&${this.pickUpTimeList[timeIndex]}`
				this.confirmPickupInfo = true
				this.showDeliveryMethodPop = false
				this.showPickerDatePop = false
			},
			
			// 提交上门信息
			async submitPickupInfo() {
				try{
					const { order_no, province_name, province_id, city_name, city_id, district_name, district_id, address, consignee, consignee_phone } = this.orderInfo
					const { day, startTime, endTime } = this.pickUpDateList.find(item => item.pickUpDateId === this.pickUpDateId)
					let data = {}
					data.type = 0 //类型：0-卖家发货 1-买家售后
					data.order_no = order_no //订单号
					data.senderProvince = province_name //寄件人省
					data.senderProvinceId = province_id //寄件人省ID
					data.senderCity = city_name //寄件人市
					data.senderCityId = city_id //寄件人市ID
					data.senderDistrict = district_name //寄件人区
					data.senderDistrictId = district_id //寄件人区ID
					data.senderDetailAddress = address //寄件人详细地址
					data.consignee = consignee //买家姓名
					data.consignee_phone = consignee_phone //买家电话号码
					data.pick_up_start_time = `${day} ${startTime}` //上门取件开始时间
					data.pick_up_end_time = `${day} ${endTime}` //上门取件结束时间
					console.log( data )
					// this.feedback.toast({title: '团团正在给测试调试数据'})
					await this.$u.api.auctionSubmitPickupInfo(data)
					this.getOrderDetail()
				}catch(e){
					//TODO handle the exception
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .picker-view-padding {
		padding: 10rpx 0;
	}
</style>

<style>
	@import "@/common/css/page.css";
</style>