<template>
  <view>
    <vh-navbar
      height="46"
      :customStatusBarHeight="20"
      :background="{
        backgroundImage: `url(${ossIcon('/auction/navbar_750_132.png')})`,
        backgroundSize: '100% 66px',
        backgroundColor: '#e80404',
        backgroundRepeat: 'no-repeat'
      }"
      back-icon-color="#FFF"
      :customBack="$customBack"
    >
      <view class="flex-e-c w-p100">
        <view class="flex-s-c ptb-00-plr-32 w-520 h-66 bg-f7f7f7 b-rad-40" @click="jump.navigateTo(routeTable.pHAuctionSearch)">
          <image :src="ossIcon('/auction/search_38_36.png')" class="w-38 h-36" />
          <text class="ml-10 font-28 text-9 l-h-40">请输入</text>
        </view>
        <view class="flex-c-c ml-24 mr-02 w-88 h-88" @click="jumpMsg">
          <view class="p-rela flex-c-c w-44 h-44">
			  <template v-if="!fromFriendCircle">
			  	<image :src="ossIcon('/auction/notice_44.png')" class="w-p100 h-p100" />
			  	 <view v-if="unreadMsgCount" class="p-abso flex-c-c w-26 h-26 font-wei-500 font-18 text-ffffff bg-ff9127 b-rad-13" style="top: -8rpx; right: -8rpx;">{{ unreadMsgCount }}</view>
			  </template>
          </view>
        </view>
      </view>
    </vh-navbar>
    <view v-if="!loading" class="pb-116">
      <view v-if="swiperList.length" class="auction-index__swiper ptb-20-plr-24 bg-ffffff">
        <vh-swiper
          :loading-type="2"
          :list="swiperList"
          height="464"
          img-mode="aspectFit"
          name="picture"
          @click="onSwiperClick"
        />
      </view>
      <!-- <view class="auction-index__tabs" :class="{ 'auction-index__tabs-a1': !currentTabIndex }">
        <u-tabs :list="tabsList" :current="currentTabIndex" height="92" font-size="32" active-color="#e80404" inactive-color="#666" bar-width="36" bar-height="8" gutter="34" @change="onTabChange" />
      </view> -->
      <view class="ptb-20-plr-24 pb-0">
        <view v-if="currentTabIndex === 1" class="b-rad-10 bg-ffffff" :class="goodsList.length ? 'p-24' : ''">
          <view v-for="(item, index) in goodsList" :key="index" class="d-flex j-sb" :class="index == 0 ? '' : 'mt-24'" @click="jump.appAndMiniJump(1, `${routeTable.pgGoodsDetail}?id=${item.id}`, $vhFrom)">
            <vh-image :loading-type="2" :src="item.banner_img" :width="288" :height="180" :border-radius="6"/>
            <view class="flex-1 d-flex flex-column j-sb ml-20">
              <view class="text-hidden-3">
                <text class="ml-06 font-24 text-3 l-h-36">{{item.title}}</text>
              </view>
              <view class="mt-22 d-flex j-sb">
                <text v-if="item.is_hidden_price == 1 || [3, 4].includes(item.onsale_status)" class="font-32 text-ff0013 l-h-28">价格保密</text>
                <text v-else class="font-32 text-ff0013 l-h-28"><text class="font-20">¥</text>{{item.price}}</text>
                <text class="font-22 text-9 l-h-34">已售{{item.purchased + item.vest_purchased}}份</text>
              </view>
            </view>
          </view>
        </view>
        <AuctionWGoodsList v-else ref="auctionWGoodsListRef" v-model="goodsList" :addTime="50" />
      </view>
      <AuctionTabbar v-if="!fromFriendCircle" />
    </view>
	<!-- 登录弹框 -->
	<SpLoginPopup v-model="showLoginPop" /> 
  </view>
</template>

<script>
import { MAuctionIndexBannerJumpType, MAuctionIndexBannerInnerLinkType, MAuctionIndexAreaType, MAuctionTypeToLabel, MAuctionAreaFixedType } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'

export default {
  name: 'auctionIndex', // 拍卖首页
  data: () => ({
    loading: true,
    swiperList: [],
    tabsList: [{ name: '', type: MAuctionIndexAreaType.Fixed, fixed_model_type: MAuctionAreaFixedType.Recommend }, { name: '商城精选', type: MAuctionIndexAreaType.MiaoFa, }],
    currentTabIndex: 0,
    query: { page: 1, limit: 10 },
    totalPage: 0,
    goodsList: [],
    unreadMsgCount: 0,
    hideCount: 0,
	showLoginPop: false, 
	fromFriendCircle: '',
	firstEnter: ''
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    async init (isLoadGoodsList = true) {
      try {
        const isLogin = await this.login.isLoginV3(this.$vhFrom, 0)
        const reqList = [
          // this.loadTabsList(),
          this.loadBannerList(),
        ]
        if (isLoadGoodsList) {
          reqList.push(this.loadGoodsList())
        }
        if (isLogin) {
          reqList.push(this.loadUnreadMsgCount())
        }
        await Promise.all(reqList)
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
        this.hideCount = this.$options.data().hideCount
      }
    },
    async loadTabsList () {
      const res = await this.$u.api.getAuctionIndexAreaList()
      const tabsList = res?.data?.list || []
      this.tabsList = [...this.tabsList.slice(0, 2), ...tabsList]
    },
    async loadGoodsList () {
      const { type, fixed_model_type, activity_id } = this.tabsList[this.currentTabIndex]
      let { page, limit } = this.query
      if (this.hideCount) {
        limit = page * this.$options.data().query.limit
        page = 1
      }
      let res
      switch (type) {
        case MAuctionIndexAreaType.Fixed:
          res = await this.$u.api.searchAuctionIndexGoodsList({ page, limit, label: MAuctionTypeToLabel[fixed_model_type] })
          break
        case MAuctionIndexAreaType.Activity:
          res = await this.$u.api.searchAuctionActivityGoodsList({ page, limit, activity_id })
          break
        case MAuctionIndexAreaType.MiaoFa:
          res = await this.$u.api.flashGoodsList({ page, limit, filters: {}, order: 'desc', periods_type: [1], sort_type: 'sort', })
          break
        default:
          res = {}
      }
      const { list = [], total = 0 } = res?.data || {}
      console.log('hideCount', this.hideCount)
      // if (this.hideCount) {
      //   const auctionWGoodsListRef = this?.$refs?.auctionWGoodsListRef
      //   const goodsListIds = this.goodsList.map(({ id }) => id)
      //   const listIds = list.map(({ id }) => id)
      //   goodsListIds.forEach(id => {
      //     if (!listIds.includes(id)) auctionWGoodsListRef.remove(id)
      //   })
      //   list.forEach(goods => {
      //     if (goodsListIds.includes(goods.id)) {
      //       Object.keys(goods).map(key => {
      //         auctionWGoodsListRef.modify(goods.id, key, goods[key])
      //       })
      //     }
      //   })
      // }
      list.forEach(item => {
        item.detail = ''
      })
      this.goodsList = this.query.page === 1 ? list : this.goodsList.concat(list)
      this.totalPage = Math.ceil(total / this.query.limit)
      this.hideCount = this.$options.data().hideCount
    },
    async loadBannerList () {
      const res = await this.$u.api.getAuctionIndexBannerList()
      this.swiperList = res?.data?.list || []
    },
    async loadUnreadMsgCount () {
      const res = await this.$u.api.getAuctionMsg()
      const unreadMsgCount = res?.data?.ct || 0
      this.unreadMsgCount = unreadMsgCount > 99 ? 99 : unreadMsgCount
    },
    onSwiperClick (item) {
      const { jump_type, inner_link, goods_id, external_link } = item
      switch (jump_type) {
        case MAuctionIndexBannerJumpType.InnerLink:
          switch (inner_link) {
            case MAuctionIndexBannerInnerLinkType.AuctionGoodsDetail:
              this.jump.navigateTo(`${this.routeTable.pHAuctionGoodsDetail}?id=${goods_id}`)
              break
          }
          break
        case MAuctionIndexBannerJumpType.OuterLink:
          this.jump.h5Jump(external_link, this.$vhFrom, 4)
          break
      }
    },
    onTabChange (index) {
      this.feedback.loading()
      this.currentTabIndex = index
      this.query = this.$options.data().query
      this.goodsList = []
      this?.$refs?.auctionWGoodsListRef?.clear()
      this.loadGoodsList()
    },
    async jumpMsg () {
	  if( this.fromFriendCircle ) return
      const isLogin = await this.login.isLoginV3(this.$vhFrom)
      if (isLogin) {
        this.jump.navigateTo(this.routeTable.pHAuctionMsg)
      }
    },
	async checkLogin(type) {
		if( this.fromFriendCircle ) {
			const isLogin = await this.login.isLoginV3(this.$vhFrom, 0)
			if( type === 'onShow') {
				if (isLogin) this.showLoginPop = false
			}else{
				if (this.firstEnter && !isLogin) this.showLoginPop = true
				uni.setStorageSync('hasRecordedFriendCircle', 1)
			}
		}
	}
  },
  onLoad (options) {
	  if( options.fromFriendCircle ) {
		  this.fromFriendCircle = options.fromFriendCircle
		  this.firstEnter = options.firstEnter
	  }else {
		  uni.removeStorageSync('hasRecordedFriendCircle'); 
	  }
	
    this.init()
	this.checkLogin('onLoad')
  },
  onShow () {
    if (this.hideCount) {
      this.init(false)
    }
	this.checkLogin('onShow')
  },
  onHide () {
    this.hideCount++
  },
  onPullDownRefresh () {
    this.query = this.$options.data().query
    this.goodsList = []
    this?.$refs?.auctionWGoodsListRef?.clear()
    this.init()
  },
  onReachBottom () {
    if (this.query.page === this.totalPage || !this.totalPage) return
    this.feedback.loading()
    this.query.page++
    this.loadGoodsList().finally(() => {
      this.feedback.hideLoading()
    })
  }
}
</script>

<style>
  page {
    background: #f5f5f5;
  }
</style>

<style lang="scss" scoped>
  .auction-index {
    &__swiper {
      ::v-deep {
        .vh-indicator-item-round {
          margin: 0 6rpx;
          @include size(8rpx);
          background-color: #e0e0e0;

          &-active {
            width: 22rpx;
            background-color: #e80404;
            border-radius: 5rpx;
          }
        }
      }
    }
    &__tabs {
      ::v-deep {
        #u-tab-item-0 {
          display: inline-flex;
          align-items: center;

          &::after {
            content: '';
            display: block;
            width: 82rpx !important;
            height: 34rpx !important;
            @include iconBgImg('/auction/recommend_82_34.png');
            background-size: 82rpx 34rpx;
          }
        }

        .u-tab {
          &-item {
            font-weight: 600 !important;
            vertical-align: top;

            &:first-of-type {
              &::before {
                display: none;
              }
            }

            &::before {
              content: '';
              position: absolute;
              top: 50%;
              left: 0;
              transform: translateY(-50%);
              display: block;
              @include size(2rpx, 28rpx);
              background-color: #f5f5f5;
            }
          }

          &-bar {
            bottom: auto;
            background: linear-gradient(214deg, #FF8383 0%, #E70000 100%);
          }
        }
      }

      &-a1 {
        ::v-deep {
          #u-tab-item-0 {
            &::after {
              @include iconBgImg('/auction/recommend_h_82_34.png');
            }
          }
        }
      }
    }
  }
</style>
