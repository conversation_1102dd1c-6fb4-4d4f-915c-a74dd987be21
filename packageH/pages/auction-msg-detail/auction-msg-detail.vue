<template>
  <view>
    <vh-navbar :title="query.notice_type | toText('MAuctionNoticeTypeText')" height="46"></vh-navbar>
    <view v-if="!viewLoading">
      <template v-if="list && list.length">
        <AuctionMsgAuctionList v-if="query.notice_type === MAuctionNoticeType.Auction" :list="list" />
        <AuctionMsgNoticeList v-else-if="query.notice_type === MAuctionNoticeType.Notice" :list="list" />
        <AuctionMsgOrderList  v-else-if="query.notice_type === MAuctionNoticeType.Order" :list="list" />
        <AuctionMsgCommentList v-else-if="query.notice_type === MAuctionNoticeType.Comment" :list="list" />
      </template>
      <view v-else class="bt-s-02-eeeeee">
        <AuctionNone class="pt-200" desc="暂无记录～" descClazz="mt-60" />
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionNoticeType } from '@/common/js/utils/mapperModel'
import { mapState, mapActions } from 'vuex'
import listMixin from '@/common/js/mixins/listMixin'

export default {
  name: 'auctionMsgDetail', // 拍卖消息详情
  mixins: [listMixin],
  data: () => ({
    MAuctionNoticeType,
    viewLoading: true,
    query: { notice_type: '' },
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    ...mapActions(['getUserInfo']),
    async load (query) {
      const res = await this.$u.api.getAuctionMsgDetail(query)
      const { list = [] } = res?.data || {}
      this.list = query.page === 1 ? list : this.list.concat(list)
      this.changeBodyClassList()
      return res
    },
    changeBodyClassList () {
      if (this?.list?.length) {
        this.$nextTick(() => {
          document?.body?.classList?.add('bg-f5f5f5')
        })
      }
    },
  },
  onLoad (options) {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.query.notice_type = +options.type
      const reqList = [this.load()]
      if (this.query.notice_type === MAuctionNoticeType.Comment) {
        reqList.push(this.getUserInfo())
      }
      Promise.all(reqList).then(() => {
        this.viewLoading = false
      })
    })
  },
  onShow () {
    this.changeBodyClassList()
  },
  onPullDownRefresh() {
    this.pullDownRefresh()
  },
  onReachBottom () {
    this.reachBottomLoad()
  }
}
</script>

<style lang="scss" scoped>
</style>
