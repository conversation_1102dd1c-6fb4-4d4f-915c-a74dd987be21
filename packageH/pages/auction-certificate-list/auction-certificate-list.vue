<template>
  <view>
    <vh-navbar title="收藏证书" height="46" showBorder />
    <view v-if="!loading">
      <!-- <view class="flex-c-c bg-ffffff">
        <view
          class="flex-c-c flex-1 h-92 font-wei-600 font-28 l-h-40"
          :class="currentTab === MAuctionCertificateType.Buyer ? 'text-e80404' : 'text-3'"
          @click="currentTab = MAuctionCertificateType.Buyer"
        >我买到的（{{ buyerList.length }}）</view>
        <view class="w-02 h-26 bg-d8d8d8"></view>
        <view
          class="flex-c-c flex-1 h-92 font-wei-600 font-28 l-h-40"
          :class="currentTab === MAuctionCertificateType.Seller ? 'text-e80404' : 'text-3'"
          @click="currentTab = MAuctionCertificateType.Seller"
        >我卖出的（{{ sellerList.length }}）</view>
      </view> -->
      <view v-if="list.length" class="d-flex flex-wrap ptb-00-plr-24 pt-28 pb-48">
        <view v-for="(item, index) in list" :key="index" class="p-rela mt-20 w-346 h-528" :class="index % 2 ? 'mr-0' : 'mr-10'">
          <image :src="ossIcon('/auction/certificate_346_528.png')" class="p-abso w-p100 h-p100" />
          <view class="p-abso w-p100 h-p100" @click="onPreview(item)">
            <view class="flex-sb-c mt-136 ptb-00-plr-30 font-12 text-3 l-h-16">
              <text>{{ item.buyer_name }}</text>
              <text>{{ item.collection_value }}</text>
            </view>
            <view class="flex-c-c mt-20">
              <vh-image :loading-type="4" :src="item.product_img" :width="140" :height="140" />
            </view>
            <view class="mtb-00-mlr-auto mt-20 w-292 font-12 text-3 l-h-16 text-center">
              <view>{{ item.title }}</view>
              <view class="mt-06">{{ item.unique_int }}</view>
              <view class="mt-06">{{ item.order_time }}</view>
              <view class="mt-06">{{ item.net_content }}</view>
              <view class="mt-06">{{ item.category_name }}</view>
              <view class="mt-06">{{ item.years }}</view>
            </view>
          </view>
          <view v-if="!item.is_buyer_unlock && !item.is_ash" style="background: rgba(0,0,0,0.5);" class="p-abso flex-c-c w-p100 h-p100" @click="onUnlock(item)">
            <image :src="ossIcon(isFromMine ? '/auction/share_66.png' : '/auction/lock_66.png')" class="w-66 h-66" />
          </view>
        </view>
      </view>
      <AuctionNone v-else :title="isFromAppMine ? '' : '暂无数据'" :desc="isFromAppMine ? '取得属于自己的收藏证书～' : ''" :descClazz="isFromAppMine ? 'mt-30 font-24 l-h-36' : ''" class="pt-200" />
    </view>
    <u-popup v-model="unlockPopupVisible" mode="center" width="552rpx" height="414rpx" border-radius="20">
      <view class="p-rela w-p100 h-p100">
        <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-p100 h-p100" />
        <view class="p-rela pt-130">
          <image :src="ossIcon('/auction/close_44.png')" class="p-abso top-24 right-24 w-44 h-44" @click="unlockPopupVisible = false" />
          <view class="font-wei-600 font-32 text-6 l-h-44 text-center">评价解锁您的证书哦！</view>
          <view class="flex-c-c mt-92">
            <button class="vh-btn flex-c-c w-180 h-64 font-wei-500 font-28 text-e80404 bg-ffffff b-s-02-e80404 b-rad-32" @click="toEvaluate">去评价</button>
          </view>
        </view>
      </view>
    </u-popup>

    <u-popup v-model="previewPopupVisible" mode="center" width="100%" height="100%" :mask-custom-style="{ background: 'rgba(0, 0, 0, 0.3)' }" class="bg-transparent-popup" @close="currentItem.$isFirstPreview = false">
      <view class="p-rela pt-140 pb-80" @click="previewPopupVisible = false">
        <canvas :canvas-id="canvasId" class="p-abso" style="top: -100%; visibility: hidden; width: 750px; height: 1146px;"></canvas>
        <canvas :canvas-id="qrcodeCanvasId" class="p-abso w-88 h-88" style="visibility: hidden;"></canvas>
        <template v-if="$isFirstPreview">
          <view v-if="$app" class="mb-50 font-wei-600 font-40 text-ffffff l-h-56 text-center">分享证书赢得兔头！</view>
          <view class="p-rela mtb-00-mlr-auto w-614 h-934">
            <image :src="ossIcon('/auction/close2_44.png')" class="p-abso top-n-64 right-04 w-44 h-44" />
            <image :src="ossIcon('/auction/certificate_614_934.png')" class="p-abso w-p100 h-p100" />
            <view class="p-abso w-p100 h-p100">
              <view class="flex-sb-c mt-230 ptb-00-plr-62 font-18 l-h-26 text-3">
                <text>{{ currentItem.buyer_name }}</text>
                <text>{{ currentItem.collection_value }}</text>
              </view>
              <view class="flex-c-c mt-52">
                <vh-image :loading-type="4" :src="currentItem.product_img" :width="234" :height="234" />
              </view>
              <view class="mtb-00-mlr-auto mt-40 w-490 font-18 text-3 l-h-26 text-center">
                <view>{{ currentItem.title }}</view>
                <view class="mt-06">{{ currentItem.unique_int }}</view>
                <view class="mt-06">{{ currentItem.order_time }}</view>
                <view class="mt-06">{{ currentItem.net_content }}</view>
                <view class="mt-06">{{ currentItem.category_name }}</view>
                <view class="mt-06">{{ currentItem.years }}</view>
              </view>
              <view class="p-abso bottom-54 right-108 flex-c-c w-80 h-80 bg-f5f5f5 b-rad-10">
                <canvas :canvas-id="smallQrcodeCanvasId" class="w-70 h-70"></canvas>
              </view>
            </view>
          </view>
          <view v-if="$app" class="flex-c-c mt-120">
            <button class="vh-btn flex-c-c w-538 h-82 font-wei-500 font-28 text-ffffff bg-ff9127 b-rad-41" @click.stop="onPostCard">分享到帖子</button>
          </view>
        </template>
        <template v-else>
          <view class="p-rela h-1146">
            <image :src="ossIcon('/auction/certificate_750_1146.png')" class="p-abso w-p100 h-p100" />
            <view class="p-abso w-p100 h-p100">
              <view class="flex-sb-c mt-304 ptb-00-plr-92 font-24 text-3 l-h-34">
                <text>{{ currentItem.buyer_name }}</text>
                <text>{{ currentItem.collection_value }}</text>
              </view>
              <view class="flex-c-c mt-64">
                <vh-image :loading-type="4" :src="currentItem.product_img" :width="272" :height="272" />
              </view>
              <view class="mtb-00-mlr-auto mt-44 w-588 font-24 text-3 l-h-34 text-center">
                <view>{{ currentItem.title }}</view>
                <view class="mt-12">{{ currentItem.unique_int }}</view>
                <view class="mt-12">{{ currentItem.order_time }}</view>
                <view v-if="currentItem.net_content" class="mt-12">{{ currentItem.net_content }}</view>
                <view class="mt-12">{{ currentItem.category_name }}</view>
                <view v-if="currentItem.years" class="mt-12">{{ currentItem.years }}</view>
              </view>
              <view class="p-abso bottom-60 right-130 flex-c-c w-100 h-100 bg-f5f5f5 b-rad-10">
                <canvas :canvas-id="bigQrcodeCanvasId" class="w-88 h-88"></canvas>
              </view>
            </view>
          </view>
        </template>
      </view>
      <view v-if="$app && !$isFirstPreview" class="d-flex j-center h-258 bg-f5f5f5">
        <view v-if="isFromMine" class="d-flex j-sb ptb-00-plr-48 w-p100 h-p100">
          <view class="pt-40 h-p100" @click="onShare">
            <view class="flex-c-c">
              <image :src="ossIcon('/auction/wx_92_90.png')" class="w-92 h-90" />
            </view>
            <view class="mt-20 font-28 text-3 l-h-34 text-center">分享好友</view>
          </view>
          <view class="pt-40 h-p100" @click="onMoments">
            <view class="flex-c-c">
              <image :src="ossIcon('/auction/friends_92_90.png')" class="w-92 h-90" />
            </view>
            <view class="mt-20 font-28 text-3 l-h-34 text-center">分享朋友圈</view>
          </view>
          <view class="pt-40 h-p100" @click="onDownload">
            <view class="flex-c-c">
              <image :src="ossIcon('/auction/photo_album_92_90.png')" class="w-92 h-90" />
            </view>
            <view class="mt-20 font-28 text-3 l-h-34 text-center">保存相册</view>
          </view>
          <view class="pt-40 h-p100" @click="onPostCard">
            <view class="flex-c-c">
              <image :src="ossIcon('/auction/post_card_92_90.png')" class="w-92 h-90" />
            </view>
            <view class="mt-20 font-28 text-3 l-h-34 text-center">分享帖子</view>
          </view>
        </view>
        <template v-else>
          <view class="pt-40 h-p100" @click="onShare">
            <view class="flex-c-c">
              <image :src="ossIcon('/auction/wx_90.png')" class="w-90 h-90" />
            </view>
            <view class="mt-20 font-wei-500 font-28 text-3 l-h-40 text-center">分享给好友</view>
          </view>
          <view class="ml-200 pt-40 h-p100" @click="onDownload">
            <view class="flex-c-c">
              <image :src="ossIcon('/auction/photo_ album_90.png')" class="w-90 h-90" />
            </view>
            <view class="mt-20 font-wei-500 font-28 text-3 l-h-40 text-center">保存至相册</view>
          </view>
        </template>
      </view>
    </u-popup>

    <!-- 预加载 -->
    <image :src="ossIcon('/auction/certificate_614_934.png')" style="display: none;" />
    <image :src="ossIcon('/auction/certificate_750_1146.png')" style="display: none;" />
  </view>
</template>

<script>
import { MAuctionCertificateType } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'
import UQRCode from 'uqrcodejs'

const CERTIFICATE_W = 750
const CERTIFICATE_H = 1146

const getTempFilePathByCanvas = (canvasId, destWidth, destHeight) => {
  return new Promise((resolve) => {
    uni.canvasToTempFilePath({
      canvasId,
      destWidth,
      destHeight,
      success: (res) => resolve(res.tempFilePath),
      fail: () => resolve(''),
    })
  })
}

export default {
  name: 'auctionCertificateList', // 拍卖证书列表
  data: () => ({
    MAuctionCertificateType,
    loading: true,
    currentTab: MAuctionCertificateType.Buyer,
    buyerList: [],
    sellerList: [],
    unlockPopupVisible: false,
    previewPopupVisible: false,
    currentItem: {
      buyer_name: '持有人：张三',
      collection_value: '收藏价值：123',
      product_img: '',
      title: '拍品名称：${title}',
      unique_int: '独立编号：${unique_int}',
      order_time: '收藏时间：${order_time}',
      net_content: '容量：${net_content}ml',
      category_name: '类型：${category_name}',
      years: '年份：${years}年',
    },
    canvasId: 'myCanvas',
    qrcodeCanvasId: 'qrcode',
    qrcodeSize: 88,
    bigQrcodeCanvasId: 'bigQrcode',
    bigQrcodeSize: 88,
    smallQrcodeCanvasId: 'smallQrcode',
    smallQrcodeSize: 70,
    isFromMine: 0,
  }),
  computed: {
    ...mapState(['routeTable']),
    list ({ currentTab, buyerList, sellerList }) {
      switch (currentTab) {
        case MAuctionCertificateType.Buyer:
          return buyerList
        case MAuctionCertificateType.Seller:
          return sellerList
        default:
          return []
      }
    },
    isFromAppMine ({ isFromMine, $app }) {
      return isFromMine && $app
    },
    $isFirstPreview ({ isFromMine, currentItem }) {
      return isFromMine && currentItem.$isFirstPreview
    }
  },
  methods: {
    onUnlock (item) {
      if (this.isFromMine) {
        this.feedback.loading()
        this.$u.api.updateAuctionCertificateShareStatus({ id: item.id, is_ash: 1 }).then(() => {
          item.is_ash = 1
          this.onPreview(item)
        })
        return
      }
      this.unlockPopupVisible = true
      this.currentItem = item
    },
    toEvaluate () {
      const { order_id, product_img } = this.currentItem
      this.jump.navigateTo(`${this.routeTable.pHAuctionOrderEvaluate}?orderId=${order_id}&goodsImage=${product_img}`)
      this.unlockPopupVisible = false
    },
    onPreview (item) {
      this.currentItem = item
      this.previewPopupVisible = true
      this.$nextTick(() => {
        this.createQrcode()
        if (this.$isFirstPreview) {
          this.createQrcode(this.smallQrcodeCanvasId, this.smallQrcodeSize)
        } else {
          this.createQrcode(this.bigQrcodeCanvasId, this.bigQrcodeSize)
        }
      })
    },
    onShare () {
      const cb = () => {
        this.jump.appShare({
          type: this.$android ? 0 : 2,
          dataType: this.$android ? 4 : 2,
          img: this.currentItem.$certificateImgUrl,
          toType: 1,
        })
      }
      this.createCertificate(cb)
    },
    onDownload () {
      const cb = () => {
        const { $certificateImgUrl } = this.currentItem
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'downloadFile', android_path: 'downloadFile' }, 
          ad_path_param: [
            { ios_key: 'url', ios_val: $certificateImgUrl, android_key: 'url', android_val: $certificateImgUrl },
            { android_key: 'suffix', android_val: 'png' }
          ]
        })
      }
      this.createCertificate(cb)
    },
    onMoments () {
      const cb = () => {
        this.jump.appShare({
          type: this.$android ? 0 : 3,
          dataType: this.$android ? 4 : 2,
          img: this.currentItem.$certificateImgUrl,
          toType: 2,
        })
      }
      this.createCertificate(cb)
    },
    onPostCard () {
      const cb = () => {
        const { $certificateImgUrl, goods_id } = this.currentItem
        const sendImg = `${$certificateImgUrl}?w=${CERTIFICATE_W}&h=${CERTIFICATE_H}`
        wineYunJsBridge.openAppPage({
          client_path: { 'ios_path': 'ShareCertificateViewController', 'android_path': 'com.stg.rouge.activity.SendPostActivity' },
          ad_path_param: [
            { ios_key: 'imageUrl', ios_val: sendImg, android_key: 'login', android_val: '1' },
            { ios_key: 'goods_id', ios_val: goods_id, android_key: 'oldData', android_val: JSON.stringify({ from: 3, sendImg, goods_id }) }
          ]
        })
      }
      this.createCertificate(cb)
    },
    async createCertificate (cb) {
      if (this.currentItem.$certificateImgUrl) {
        cb && cb()
        return
      }
      this.feedback.loading({ title: '证书生成中...' })
      const FONT = '400 24px/34px PingFangSC-Regular, PingFang SC'

      const getTempFilePathByDownload = (url) => {
        return new Promise((resolve) => {
          uni.downloadFile({
            url,
            success: ({ statusCode, tempFilePath }) => {
              if (statusCode === 200) resolve(tempFilePath)
              else resolve('')
            },
            fail: () => resolve('')
          })
        })
      }

      const getStrRows = (ctx, str, maxWidth = 588) => {
        const rows = [{ text: '', width: 0 }]
        let rowIndex = 0
        const strArr = str.split('')
        strArr.forEach(strItem => {
          const { text } = rows[rowIndex]
          const joinStr = `${text}${strItem}`
          const joinStrWidth = ctx.measureText(joinStr).width
          if (joinStrWidth > maxWidth) {
            rowIndex++
            rows[rowIndex] = { text: strItem, width: joinStrWidth }
          } else {
            rows[rowIndex] = { text: joinStr, width: joinStrWidth }
          }
        })
        return rows
      }

      const uploadFile = (file) => {
        return new Promise((resolve, reject) => {
          this.$u.api.ossUpload({ dir: 'vinehoo/client/auctionCertificate/' }).then(ossUploadRes => {
            const uploadInfo = ossUploadRes?.data
            const fileName = Math.random().toString(36).substr(2,4) + '_' + Date.now()
            const { dir, policy, accessid, signature, host, img_url } = uploadInfo
            const key = `${dir}${fileName}.png`
            const formData = {
              key,
              policy,
              OSSAccessKeyId: accessid,
              signature,
              success_action_status: 200,
            }
            uni.uploadFile({
              url: host,
              file,
              name: 'file',
              formData,
              success: () => resolve(`${img_url}/${key}`),
              fail: () => reject()
            })
          }).catch(() => reject())
        })
      }

      const success = () => {
        this.feedback.hideLoading()
        this.feedback.toast({ title: '证书生成成功' })
      }

      const fail = () => {
        this.feedback.hideLoading()
        this.feedback.toast({ title: '证书生成失败，请稍候再试' })
      }

      const drawRoundedRect = (ctx, strokeStyle, fillStyle, x, y, width, height, radius) => {
        ctx.beginPath()
        ctx.moveTo(x + radius, y)
        ctx.arcTo(x + width, y, x + width, y + height, radius)
        ctx.arcTo(x + width, y + height, x, y + height, radius)
        ctx.arcTo(x, y + height, x, y, radius)
        ctx.arcTo(x, y, x + radius, y, radius)
        ctx.strokeStyle = strokeStyle
        ctx.fillStyle = fillStyle
        ctx.stroke()
        ctx.fill()
      }

      try {
        const bgTempFilePath = await getTempFilePathByDownload(this.ossIcon('/auction/certificate_750_1146.png'))
        if (!bgTempFilePath) {
          fail()
          return
        }
        let productImgTempFilePath = await getTempFilePathByDownload(this.ossIcon(this.currentItem.product_img))
        if (!productImgTempFilePath) {
          productImgTempFilePath = await getTempFilePathByDownload(this.ossIcon('/occupy_img/img4.png'))
        }
        const ctx = uni.createCanvasContext(this.canvasId, this)
        ctx.drawImage(bgTempFilePath, 0, 0, CERTIFICATE_W, CERTIFICATE_H)
        ctx.font = FONT
        ctx.fillStyle = '#333'
        ctx.fillText(`${this.currentItem.buyer_name}`, 92, 304 + 34)
        const [strRow] = getStrRows(ctx, this.currentItem.collection_value)
        ctx.fillText(strRow.text, 658 - strRow.width, 304 + 34)
        const productImgSize = 272
        if (productImgTempFilePath) ctx.drawImage(productImgTempFilePath, (CERTIFICATE_W - productImgSize) / 2, 402, productImgSize, productImgSize)
        const descRowHeight = 34
        const descMarginTop = 12
        const descKeyValueList = ['title', 'unique_int', 'order_time', 'net_content', 'category_name', 'years']
        const descList = descKeyValueList.map(keyValue => this.currentItem[keyValue]).filter(desc => desc)
        let startY = 718 + descRowHeight
        descList.forEach(desc => {
          getStrRows(ctx, desc).forEach((row) => {
            const { text, width } = row
            ctx.fillText(text, (CERTIFICATE_W - width) / 2, startY)
            startY = startY + descRowHeight
          })
          startY = startY + descMarginTop
        })
        const qrcodeTempFilePath = await getTempFilePathByCanvas(this.qrcodeCanvasId, this.qrcodeBgSize, this.qrcodeBgSize)
        if (qrcodeTempFilePath) {
          const qrcodeBgSize = 100
          const rectW = 522
          const rectH = 984
          const rectPadding = 6
          drawRoundedRect(ctx, '#f5f5f5', '#f5f5f5', rectW, rectH, qrcodeBgSize, qrcodeBgSize, 10)
          const qrcodeSize = this.qrcodeSize
          ctx.drawImage(qrcodeTempFilePath, rectW + rectPadding, rectH + rectPadding, qrcodeSize, qrcodeSize)
        }
        ctx.draw()
        setTimeout(async () => {
          const base64 = await getTempFilePathByCanvas(this.canvasId, CERTIFICATE_W, CERTIFICATE_H)
          if (!base64) {
            fail()
            return
          }
          // const img = new Image()
          // img.src = base64
          // const newTab = window.open('', '_blank')
          // newTab.document.write(img.outerHTML)
          const arr = base64.split(',')
          const bytes = atob(arr[1])
          const ab = new ArrayBuffer(bytes.length)
          const ia = new Uint8Array(ab)
          for (let i = 0; i < bytes.length; i++) {
            ia[i] = bytes.charCodeAt(i)
          }
          const blob = new Blob([ia], { type: 'image/png' })
          const file = new File([blob], 'abTempFile', { type: 'image/png' })
          const certificateImgUrl = await uploadFile(file)
          console.log('certificateImgUrl', certificateImgUrl)
          this.currentItem.$certificateImgUrl = certificateImgUrl
          success()
          cb && cb()
        }, 300)
      } catch (err) {
        fail()
        console.log('err', err)
      }
    },
    createQrcode (canvasId = this.qrcodeCanvasId, size = this.qrcodeSize) {
      try {
        const qr = new UQRCode()
        const { goods_id } = this.currentItem
        const data = `${location.origin}${this.routeTable.pHAuctionGoodsDetail}?id=${goods_id}`
        qr.data = data
        qr.size = uni.upx2px(size)
        qr.make()
        const qrcodect = uni.createCanvasContext(canvasId, this)
        qr.canvasContext = qrcodect
        qr.drawCanvas()
      } catch (err) {
        console.log('createQrcode', err)
      }
    },
    changeBodyClassList () {
      if (this.buyerList.length) {
        this.$nextTick(() => {
          document?.body?.classList?.add('bg-f5f5f5')
        })
      }
    },
  },
  onLoad (options) {
    const { isFromMine = 0 } = options
    this.isFromMine = +isFromMine
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      const loginInfo = uni.getStorageSync('loginInfo') || {}
      Promise.all([
        this.$u.api.searchAuctionCertificateList({ type: MAuctionCertificateType.Buyer, uid: loginInfo.uid }),
        // this.$u.api.searchAuctionCertificateList({ type: MAuctionCertificateType.Seller, uid: loginInfo.uid })
      ]).then(([buyerRes, sellerRes]) => {
        const buyerList = buyerRes?.data || []
        buyerList.forEach(item => {
          const { buyer_name, collection_value, product_img, title, unique_int, order_time, net_content, category_name, years, is_buyer_unlock, is_ash } = item
          const isPersonNotWine = [2, 3, 4, 5, 6].includes(item.product_type)
          Object.assign(item, {
            buyer_name: `持有人：${buyer_name}`,
            collection_value: `收藏价值：${collection_value}`,
            product_img: product_img?.[0] || '',
            title: `拍品名称：${title}`,
            unique_int: `独立编号：${unique_int}`,
            order_time: `收藏时间：${order_time}`,
            net_content: isPersonNotWine ? '' : `容量：${net_content}ml`,
            category_name: `类型：${category_name}`,
            years: isPersonNotWine ? '' : `年份：${years}`,
            $certificateImgUrl: '',
            $isFirstPreview: !is_buyer_unlock && !is_ash,
          })
        })
        this.buyerList = buyerList
        this.sellerList = sellerRes?.data || []
        this.changeBodyClassList()
      }).finally(() => {
        this.loading = false
      })
    })
  },
}
</script>

<style lang="scss" scoped>
</style>
