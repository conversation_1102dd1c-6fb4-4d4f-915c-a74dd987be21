<template>
	<view :class="list.length ? 'h-min-vh-100 bg-f5f5f5' : ''">
		<vh-navbar title="我的参拍" height="46" show-border>
			<view v-if="list.length && currentTabIndex !== TabType.Bidding" slot="right" class="flex-c-c w-108 h-92 font-30 text-3"
				@click="onRightTextClick">{{ rightText }}</view>
		</vh-navbar>
		
		<view v-if="!loading" :class="isEdit ? 'pb-128' : 'pb-24'">
			<view class="p-stic z-980" :style="{top: system.navigationBarHeight() + 'px'}">
				<u-tabs :list="tabList" :current="currentTabIndex" :height="92" :font-size="32" inactive-color="#333" active-color="#E80404"
				 :bar-width="36" :bar-height="8" :bar-style="{ background:'linear-gradient(214deg, #FF8383 0%, #E70000 100%)' }" :is-scroll="false" @change="changeTabs" />
			</view>

			<view class="o-hid">
			
			<view v-if="list.length" class="ptb-00-plr-24" :class="isEdit ? 't-trans-x-72' : ''">
				<view v-for="(item, index) in list" :key="index" class="p-rela bg-ffffff b-rad-10 mtb-20-mlr-00">
					<view class="p-abso top-16 left-20 font-wei-500 font-28 text-6"> {{ item.time }} </view>
					<view v-for="(item1, index1) in item.list" :key="index1" class="p-rela ptb-00-plr-20"
						@click="jump.navigateTo(`${routeTable.pHAuctionGoodsDetail}?id=${item1.goods_id}`)">
						<view class="p-abso top-84 left-0 t-trans-x-m100 flex-c-c w-96">
							<image :src="ossIcon(`/auction/radio${item1.$checked ? '_h' : ''}_32.png`)" class="w-32 h-32"
								@click.stop="item1.$checked = !item1.$checked" />
						</view>
						<view class="p-abso top-0 right-0 w-176 h-46">
							<image :src="ossIcon(`/auction_my_participation/status_bg${ [TabType.Shot, TabType.NotTaken].includes(tabList[currentTabIndex].type) ? item1.$statusBgIndex : item1[item1.is_lead].$statusBgIndex }.png`)" 
							class="p-abso w-p100 h-p100" />
							<view class="p-rela flex-c-c w-p100 h-p100">
								<image :src="ossIcon('/auction_my_participation/hammer.png')" class="w-36 h-34" />
								<text class="ml-10 font-22 l-h-34 text-ffffff">{{ [TabType.Shot, TabType.NotTaken].includes(tabList[currentTabIndex].type) ? item1.$statusText : item1[item1.is_lead].$statusText }}</text>
							</view>
						</view>
						<view v-show="index1" class="h-02 bg-eeeeee"></view>
						<view class="d-flex pt-70 pb-24">
							<vh-image :loading-type="4" :src="item1.product_img && item1.product_img[0]" :width="152" :height="152" :border-radius="6" />
							<view class="flex-1 flex-col-sb-s ml-20">
								<view class="">
									<view class="font-wei-500 font-24 text-3 l-h-34 text-hidden-2">{{ item1.title }} </view>
									<text class="mt-04 ptb-00-plr-08 font-22 l-h-32 b-rad-04" :class="[item1.$tagBgClazz, item1.$tagTextClazz]">
										<template v-if="currentTabIndex === TabType.Bidding">{{ item1.closing_auction_time_text }}{{ item1.$tagText }}</template>
										<template v-else>{{ item1.$tagText }}</template>
									</text>
								</view>
								<view class="w-p100 flex-sb-c">
									<view class="flex-c-c text-e80404">
										<text class="font-24">{{ item1.$priceName }}</text>
										<text class="ml-06 font-24">
											<text>¥</text>
											<text class="font-32 font-wei">{{ item1.final_auction_price }}</text>
										</text>
									</view>
									<image :src="ossIcon(`/auction_my_participation/${item1.is_like ? '' : 'un'}like.png`)" class="w-24 h-24" @click.stop="onEnjoy(item1)" />
								</view>
							</view>
						</view>
					</view>
				</view>
				<u-loadmore :status="reachBottomLoadStatus" />
			</view>
			
			<AuctionNone v-else :title="$app ? '' : '空空如也'" desc="做名副其实的捡漏王～" 
			:descClazz="$app ? 'mt-30' : 'mt-20'" class="pt-200" />
			
			<view v-if="list.length && isEdit" class="p-fixed left-0 bottom-0 flex-sb-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-9999" v-safeBeautyBottom="$safeBeautyBottom">
				<view class="flex-sb-c ml-24" @click="onCheckAllChange">
					<image :src="ossIcon(`/auction/radio${checkAll ? '_h' : ''}_32.png`)" class="ml-08 w-32 h-32" />
					<text class="ml-16 font-32 text-3">全选</text>
				</view>
				<button :disabled="!checkSome" class="vh-btn flex-c-c mr-24 w-208 h-64 bg-e80404 font-wei-500 font-28 text-ffffff b-rad-32" 
				@click="onBatchCancel">删除</button>
			</view>
			</view>
		</view>

		<u-popup v-model="popupVisible" mode="center" width="552rpx" height="414rpx" border-radius="20">
			<view class="p-rela w-552 h-414">
				<image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-552 h-414" />
				<view class="p-rela pt-84">
					<view class="font-wei-500 font-32 text-3 l-h-44 text-center">真的不喜欢我了嘛</view>
					<view class="mt-20 font-28 text-6 l-h-34 text-center">哼，善变的兔子君！</view>
					<view class="flex-sb-c mt-84 ptb-00-plr-70">
						<button
							class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32"
							@click="onConfirm">确认</button>
						<button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32"
							@click="popupVisible = false">取消</button>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import { MAuctionMyParticipationMapper } from '@/common/js/utils/mapper'
	import { MAuctionMyParticipationStatus as TabType } from '@/common/js/utils/mapperModel'
	import { mapState } from 'vuex'
	import listMixin from '@/common/js/mixins/listMixin'
	export default {
		name: 'auctionMyParticipation',
		mixins: [listMixin],
		data: () => ({
			TabType,
			tabList: [{ name: '竞拍中', type: TabType.Bidding }, { name: '已拍中', type: TabType.Shot }, { name: '未拍中', type: TabType.NotTaken }],
			isEdit: false,
			popupVisible: false,
			currentItem: null,
		}),
		computed: {
			...mapState(['routeTable']),
			rightText({ isEdit }) {
				return isEdit ? '完成' : '编辑'
			},
			checkAll({ list }) {
				return !!list.length && Object.keys(list).every(index => list[index].list.every(item => item.$checked))
			},
			checkSome({ list }) {
				return !!list.length && Object.keys(list).some(index => list[index].list.some(item => item.$checked))
			}
		},
		methods: {
			async load(query, index) {
				const data = { ...query }
				const { type } = this.tabList[index]
				if( type === TabType.Bidding ) data.onsale_status = 2
				else if( type === TabType.Shot ) data.is_auction = 1
				else if( type === TabType.NotTaken ) data.is_auction = 2
				const res = await this.$u.api.auctionParticipateList(data)
				const { list = [], total = 0 } = res?.data || {}
				Object.keys(list).forEach(idx => {
					list[idx].list = list[idx].list.map(
					    item => Object.assign({}, MAuctionMyParticipationMapper[type], item, { $checked: false })
					)
				})
				this.list = query.page === 1 ? list : this.list.concat(list)
				this.currentTabIndex = index
				return res
			},
			changeTabs(index) {
				this.isEdit = false
				this.changeTabIndex(index)
			},
			onRightTextClick() {
				this.isEdit = !this.isEdit
			},
			async onEnjoy(item) {
				const {
					is_like,
					goods_id
				} = item
				if (is_like) {
					this.popupVisible = true
					this.currentItem = item
				} else {
					await this.$u.api.addEnjoyAuctionGoods({
						goods_id
					})
					this.feedback.toast()
					item.is_like = 1
				}
			},
			async onConfirm() {
				await this.$u.api.cancelEnjoyAuctionGoods({
					goods_id: this.currentItem.goods_id
				})
				this.popupVisible = false
				this.currentItem.is_like = 0
			},
			onCheckAllChange() {
				const checkAll = this.checkAll
				Object.keys(this.list).forEach(index => {
					this.list[index].list.forEach(item => {
						item.$checked = !checkAll
					})
				})
			},
			onBatchCancel() {
				this.feedback.showModal({
					content:'确认删除吗？',
					confirm: async () => {
						const ids = Object.keys(this.list).map(index =>
						this.list[index].list.filter(item => 
						item.$checked ).map(item => item.id)).reduce((a, b) => a.concat(b), []).join(',')
						if( ids ) {
							await this.$u.api.auctionParticipateListDel({id: ids})
							this.feedback.toast({ title: '删除成功', icon: 'success' })
							this.feedback.loading()
							this.load({ ...this.query, page: 1 }).then(() => {
								this.$nextTick(() => {
									this.system.pageScrollTo(0, 0)
								})
							})
						}
					}
				})
			}
		},
		onLoad() {
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (!isLogin) return
				this.load().finally(() => {
					this.loading = false
				})
			})
		},
		onPullDownRefresh() {
			this.pullDownRefresh()
		},
		onReachBottom () {
			this.reachBottomLoad()
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-tab-item {
		font-weight: 600;
	}
</style>