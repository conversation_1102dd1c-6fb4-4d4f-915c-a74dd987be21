<template>
  <view>
    <vh-navbar title="商户认证" height="46"></vh-navbar>
    <view v-if="!loading">
      <view v-if="rnInfo.status === MAuctionRNStatus.OnCheck" class="pt-332">
        <view class="flex-c-c">
          <image :src="ossIcon('/auction/rn_on_check_440_400.png')" class="w-440 h-400" />
        </view>
        <view class="mt-60 font-wei-500 font-36 text-3 l-h-66 text-center">审核中</view>
        <view class="mt-20 font-28 text-6 l-h-44 text-center">需1-3个工作日, 请兔子君耐心等待～</view>
      </view>
      <view v-else-if="rnInfo.status === MAuctionRNStatus.Rejected" class="pt-332">
        <view class="flex-c-c">
          <image :src="ossIcon('/auction/rn_reject_442_400.png')" class="w-442 h-400" />
        </view>
        <view class="mt-72 font-wei-500 font-36 text-3 l-h-50 text-center">未通过</view>
        <view class="mt-20 font-28 text-6 l-h-40 text-center">{{ rnInfo.reject_reason }}</view>
        <view class="flex-c-c mt-230">
          <button class="vh-btn flex-c-c w-646 h-80 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-40" @click="rnInfo.status = MAuctionRNStatus.NotSubmit">重新认证</button>
        </view>
      </view>
      <view v-else>
        <view class="h-20 bg-f5f5f5"></view>
        <view class="p-rela">
          <view class="ptb-28-plr-32">
            <view class="d-flex">
              <image :src="ossIcon('/auction/editor_34.png')" class="w-34 h-34" />
              <text class="ml-20 font-28 text-6 l-h-40">企业信息</text>
            </view>
            <view class="flex-s-c h-110 bb-s-02-eeeeee">
              <view class="w-174 font-wei-500 font-32 text-3">企业名称</view>
              <input v-model="rnInfo.title" placeholder="请填写营业执照上的企业全称" placeholder-style="font-size:28rpx; color:#999" class="flex-1 font-32 text-3 text-right" />
            </view>
            <view class="flex-s-c h-110 bb-s-02-eeeeee">
              <view class="w-174 font-wei-500 font-32 text-3">营业执照号</view>
              <input v-model="rnInfo.business_license_number" placeholder="请输入营业执照注册号" placeholder-style="font-size:28rpx; color:#999" class="flex-1 font-32 text-3 text-right" />
            </view>
            <view class="mt-32">
              <view class="font-wei-500 font-32 text-3 l-h-44">请上传营业执照</view>
              <view class="flex-c-c mt-20">
                <AuctionUpload
                  :plate="96"
                  directory="vinehoo/client/auctionRNCompany/"
                  :max-count="1"
                  :fileList="businessLicenseImageFileList"
                  @on-list-change="onListChange($event, 'business_license_image')"
                  @on-uploaded="onUploaded($event, 'business_license_image')"
                />
              </view>
            </view>
            <view class="d-flex mt-50">
              <image :src="ossIcon('/auction/editor_34.png')" class="w-34 h-34" />
              <text class="ml-20 font-28 text-6 l-h-40">管理员信息</text>
            </view>
            <view class="flex-s-c h-110 bb-s-02-eeeeee">
              <view class="w-174 font-wei-500 font-32 text-3">管理员姓名</view>
              <input v-model="rnInfo.name" placeholder="请输入" placeholder-style="font-size:28rpx; color:#999" class="flex-1 font-32 text-3 text-right" />
            </view>
            <view class="flex-s-c h-110 bb-s-02-eeeeee">
              <view class="w-174 font-wei-500 font-32 text-3">身份证号码</view>
              <input v-model="rnInfo.id_card" placeholder="请输入" placeholder-style="font-size:28rpx; color:#999" class="flex-1 font-32 text-3 text-right" />
            </view>
            <view class="flex-s-c h-110 bb-s-02-eeeeee">
              <view class="w-174 font-wei-500 font-32 text-3">手机号码</view>
              <input v-model="rnInfo.phone" placeholder="请输入" placeholder-style="font-size:28rpx; color:#999" class="flex-1 font-32 text-3 text-right" />
            </view>
            <view class="flex-s-c h-110 bb-s-02-eeeeee">
              <view class="w-174 font-wei-500 font-32 text-3">电子邮箱</view>
              <input v-model="rnInfo.email" placeholder="请输入" placeholder-style="font-size:28rpx; color:#999" class="flex-1 font-32 text-3 text-right" />
            </view>
            <view class="mt-32">
              <view class="font-wei-500 font-32 text-3 l-h-44">请上传管理员身份证</view>
              <view class="flex-c-c mt-20">
                <AuctionUpload
                  :plate="95"
                  directory="vinehoo/client/auctionRNCompany/"
                  :max-count="1"
                  :fileList="idCardPortraitFileList"
                  @on-list-change="onListChange($event, 'id_card_portrait')"
                  @on-uploaded="onUploaded($event, 'id_card_portrait')"
                />
              </view>
              <view class="flex-c-c mt-20">
                <AuctionUpload
                  :plate="94"
                  directory="vinehoo/client/auctionRNCompany/"
                  :max-count="1"
                  :fileList="idCardEmblemFileList"
                  @on-list-change="onListChange($event, 'id_card_emblem')"
                  @on-uploaded="onUploaded($event, 'id_card_emblem')"
                />
              </view>
            </view>
            <view class="mt-50">
              <view class="font-wei-500 font-32 text-3 l-h-44">注意事项</view>
              <view class="mt-24 font-24 text-6 l-h-34">
                <view><text class="text-e80404">*</text>1. 请上传清晰、完整、无遮挡的营业执照和身份证图片</view>
                <view><text class="text-e80404">*</text>2. 要求营业执原件红章清晰</view>
              </view>
            </view>
            <view class="flex-c-c mt-100">
              <button class="vh-btn flex-c-c w-646 h-80 font-wei-500 font-28 text-ffffff b-rad-40" :class="disabled ? 'bg-fce4e3' : 'bg-e80404'" @click="onSubmit">提交审核</button>
            </view>
          </view>
          <view v-if="rnInfo.status === MAuctionRNStatus.Passed" class="p-abso top-0 w-p100 h-p100" style="background: rgba(255,255,255,0.53);">
            <image :src="ossIcon('/auction/rn_finish_240_218.png')" class="p-abso top-0 right-108 w-240 h-218" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionRNType, MAuctionRNStatus } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'

export default {
  name: 'auctionRNCompany', // 拍卖商户认证
  data: () => ({
    MAuctionRNStatus,
    loading: true,
    rnInfo: {
      type: MAuctionRNType.CompanyRN,
      title: '',
      business_license_number: '',
      business_license_image: '',
      name: '',
      id_card: '',
      phone: '',
      email: '',
      id_card_portrait: '',
      id_card_emblem: '',
    },
    businessLicenseImageFileList: [],
    idCardPortraitFileList: [],
    idCardEmblemFileList: [],
  }),
  computed: {
    ...mapState(['ossPrefix']),
    disabled ({ rnInfo }) {
      const { title, business_license_number, business_license_image, name, id_card, phone, email, id_card_portrait, id_card_emblem } = rnInfo
      return !(title && business_license_number && business_license_image && name && id_card && phone && email && id_card_portrait && id_card_emblem)
    }
  },
  methods: {
    async load () {
      const res = await this.$u.api.getAuctionRNInfo({ type: MAuctionRNType.CompanyRN })
      const data = res?.data || {}
      this.rnInfo = Object.assign({}, this.rnInfo, data)
      if (this.rnInfo.id) {
        const delImgHost = (str) => {
          return str.replace(this.ossPrefix, '')
        }
        const imgKeyValueList = ['business_license_image', 'id_card_portrait', 'id_card_emblem']
        imgKeyValueList.forEach(keyValue => {
          this.rnInfo[keyValue] = delImgHost(this.rnInfo[keyValue])
        })
        const getFileList = (str) => {
          return str.split(',').map(item => ({
            fileType:'image',
            url: item.includes('http') ? item : `${this.ossPrefix}${item}`,
            response: item.includes('http') ? item : `${this.ossPrefix}${item}`,
            progress: 100,
            error: false,
            file: {}
          }))
        }
        const { business_license_image, id_card_portrait, id_card_emblem } = this.rnInfo
        this.businessLicenseImageFileList = getFileList(business_license_image)
        this.idCardPortraitFileList = getFileList(id_card_portrait)
        this.idCardEmblemFileList = getFileList(id_card_emblem)
      }
    },
    async onSubmit () {
      if (this.disabled) return
      const { id, type, title, business_license_number, business_license_image, name, id_card, phone, email, id_card_portrait, id_card_emblem } = this.rnInfo
      if (!this.$u.test.idCard(id_card)) {
        this.feedback.toast({ title: '请填写正确的身份证号码' })
        return
      }
      if (!this.$u.test.mobile(phone)) {
        this.feedback.toast({ title: '请填写正确的手机号' })
        return
      }
      if (!this.$u.test.email(email)) {
        this.feedback.toast({ title: '请填写正确电子邮箱' })
        return
      }
      this.feedback.loading({ title: '提交审核中...' })
      const params = { type, title, business_license_number, business_license_image, name, id_card, phone, email, id_card_portrait, id_card_emblem }
      if (id) params.id = id
      if (id) {
        await this.$u.api.editAuctionRNInfo(params)
      } else {
        await this.$u.api.addAuctionRNInfo(params)
      }
      this.load()
    },
    onListChange (list, keyValue) {
      this.rnInfo[keyValue] = list.map(({ response, url }) => response || url.replace(this.ossPrefix, '')).join()
    },
    onUploaded (list, keyValue) {
      this.rnInfo[keyValue] = list.map(({ response }) => response).join()
      uni.hideLoading()
    },
  },
  onLoad () {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.load().finally(() => {
        this.loading = false
      })
    })
  }
}
</script>

<style lang="scss" scoped>
</style>
