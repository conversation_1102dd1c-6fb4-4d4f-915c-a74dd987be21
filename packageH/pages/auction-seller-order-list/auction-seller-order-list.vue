<template>
	<view class="content" :class="loading ? 'h-vh-100 o-hid' : ''">
		<!-- 导航栏 -->
		<vh-navbar :title="showSearch ? '' : '我卖出的'" :show-border="true">
			<template v-if="!showSearch">
				<image slot="right" class="fade-in p-24 w-44 h-44" :src="ossIcon(`/comm/ser_black.png`)" @click="search"></image>
			</template>
			
			<view v-if="showSearch" class="fade-in d-flex a-center">
				<view class="p-12" @click="closeSearch">
					<u-icon name="close" :size="34" color="#333"></u-icon>
				</view>
				<view class="bg-f7f7f7 d-flex j-sb a-center b-rad-40 pl-26 pr-26">
					<view class="p-rela w-472 h-68 d-flex a-center">
						<image class="w-44 h-44" :src="ossIcon(`/comm/ser_gray.png`)" mode="aspectFill" />
						<input class="w-352 h-p100 ml-10 font-28 text-3" type="text" v-model="keyword" :placeholder="'请输入关键字'" placeholder-style="color:#999;font-size:28rpx;" @confirm="search"/>
						<view v-show="$u.trim(keyword, 'all') !== ''" class="p-abso right-0 top-0 w-40 h-p100 d-flex j-center a-center" @click="keyword = ''">
							<image class="w-40 h-40" src="https://images.vinehoo.com/vinehoomini/v3/comm/del_gray.png" mode="aspectFill" />
						</view>
					</view>
				</view>
				<view class="p-12 font-28 font-wei text-6 w-s-now" @click="search">搜索</view>
			</view>
		</vh-navbar>
		
		<!-- 数据加载完成 -->
		<view v-if="!loading" class="fade-in">
			<!-- tabs选项栏 -->
			<view class="p-stic z-980" :style="{top: system.navigationBarHeight() + 'px'}">
				<u-tabs :list="tabList" :current="currentTabs" :height="92" :font-size="28" inactive-color="#333" active-color="#2E7BFF"
				 :bar-width="36" :bar-height="8" :bar-style="{background:'linear-gradient(214deg, #A8C8FF 0%, #2E7BFF 100%);'}" :is-scroll="false" @change="changeTabs" />
			</view>
			
			<!-- 订单列表 -->
			<view class="">
				<!-- 订单列表不为空 -->
				<view v-if="orderList.length > 0" class="pb-20">
				    <view class="bg-ffffff b-rad-16 mt-20 mr-24 mb-20 ml-24 pl-24 pr-24" v-for="(item, index) in orderList" :key="index"
					@click="jump.navigateTo(`${routeTable.pHAuctionSellerOrderDetail}?orderNo=${item.order_no}`)">
						<!-- 订单列表 -->
						<AuctionOrderListItem :item="item" :type="1" />
						
						<!-- 按钮信息 -->
						<view class="flex-e-c ptb-28-plr-00">
							<!-- 提醒付款 -->
							<view v-if="[0].includes(item.status)" class="">
								<u-button 
								shape="circle" 
								:hair-line="false" 
								:ripple="true" 
								ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'52rpx', fontSize:'28rpx', color:'#2E7BFF', border:'1rpx solid #2E7BFF'}" 
								@click="remindPayment(item)">提醒付款</u-button>
							</view>
							<!-- 去发货 -->
							<view v-if="[1].includes(item.status)" class="">
								<u-button 
								shape="circle" 
								:hair-line="false" 
								:ripple="true" 
								ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'52rpx', fontSize:'28rpx', color:'#2E7BFF', border:'1rpx solid #2E7BFF'}" 
								@click="jump.navigateTo(`${routeTable.pHAuctionSellerOrderDetail}?orderNo=${item.order_no}&toShip=1`)">去发货</u-button>
							</view>
							<!-- 查看物流 -->
							<view v-if="[2].includes(item.status)" class="">
								<u-button 
								shape="circle" 
								:hair-line="false" 
								:ripple="true" 
								ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'52rpx', fontSize:'28rpx', color:'#2E7BFF', border:'1rpx solid #2E7BFF'}"
								@click="viewLogistics(item)">查看物流</u-button>
							</view>
							<!-- 评价 -->
							<view v-if="[3].includes(item.status)" class="">
								<u-button 
								shape="circle" 
								:hair-line="false" 
								:ripple="true" 
								ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'52rpx', fontSize:'28rpx', color:'#666', border:'1rpx solid #666'}" 
								@click="jump.navigateTo(`${routeTable.pHAuctionOrderEvaluateList}?currentTabs=1`)">查看评价</u-button>
							</view>
							<!-- 查看详情 -->
							<view v-if="[4].includes(item.status)" class="">
								<u-button 
								shape="circle" 
								:hair-line="false" 
								:ripple="true" 
								ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'52rpx', fontSize:'28rpx', color:'#666', border:'1rpx solid #666'}" 
								@click="jump.navigateTo(`${routeTable.pHAuctionSellerOrderDetail}?orderNo=${item.order_no}`)">查看详情</u-button>
							</view>
							<!-- 售后详情 -->
							<view v-if="[7].includes(item.status)" class="">
								<u-button 
								shape="circle" 
								:hair-line="false" 
								:ripple="true" 
								ripple-bg-color="#FFF"
								:custom-style="{width:'180rpx', height:'52rpx', fontSize:'28rpx', color:'#666', border:'1rpx solid #666'}" 
								@click="jump.navigateTo(`${routeTable.pHAuctionSellerAfterSaleDetail}?refundOrderNo=${item.refund_order_no}`)">售后详情</u-button>
							</view>
						</view>
					</view>
					
					<u-loadmore :status="loadStatus" />
				</view>
				
				<!-- 订单列表为空 -->
				<AuctionEmpty v-else :imageSrc="ossIcon(`/auction_empty/emp1.png`)" :text="'空空如也'" subText="别急早晚会有的～" :paddingTop="108" :paddingBottom="780"/>
			</view>
		</view>
		
		<!-- 骨架屏 -->
		<vh-skeleton v-else bgColor="#FFF" :showLoading="false"/>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default {
		name: 'auction-seller-order-list',
		
		data() {
			return {
				loading: true, //数据是否加载完成
				showSearch: false, //是否显示搜索
				keyword:'', //搜索关键字
				tabList: [ //状态栏选项
					{ name: '全部' }, 
					{ name: '待付款' }, 
					{ name: '待发货' },
					{ name: '待收货' },
					{ name: '已完成' },
				],
				currentTabs: 0, //当前选中tabs
				hasGotOrderList: 0, //是否请求过订单列表
				orderList: [], //订单列表
				page: 1, //当前页
				limit: 10, //每页限制多少条
				totalPage: 1, //总页数
				loadStatus: 'loadmore', //加载状态
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
			...mapState('auction', ['logisticsInfo']),
		},
		
		onShow() {
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) this.init()
			})
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations('auction', ['muLogisticsInfo']),
			
			// 初始化
			async init() {
				this.page = 1
				await this.getOrderList()
				this.loading = false
			},
			
			// 切换状态栏
			changeTabs(index) {
				this.currentTabs = index
				this.page = 1
				this.getOrderList()
			},
			
			// 获取订单列表
			async getOrderList() {
				if(this.hasGotOrderList) this.feedback.loading()
				const { data: { list, total }} = await this.$u.api.auctionSellerOrderList({
					type: this.currentTabs, //当前tabs
					page: this.page, //页码
					limit: this.limit, //每页限制
					keyword: this.keyword //搜索关键字
				})
				this.page == 1 ? this.orderList = list : this.orderList = [...this.orderList, ...list]
				this.totalPage = Math.ceil(total / this.limit)
				this.loadStatus = this.page == this.totalPage ? 'nomore' : 'loadmore'
				this.hasGotOrderList = 1
				uni.stopPullDownRefresh() //停止下拉刷新
				this.feedback.hideLoading()
			},
			
			// 搜索
			search() {
				this.showSearch = true
				this.init()
			},
			
			// 确认搜索
			confirmSearch() {
				this.init()
			},
			
			// 关闭搜索
			closeSearch() {
				this.keyword = ''
				this.showSearch = false
				this.init()
			},
			
			// 按钮操作板块
			// 这是提醒付款 item = 订单列表每一项
			async remindPayment({ order_no }){
				try{
					await this.$u.api.auctionRemindNotice({order_no, type: 1 })
					this.feedback.toast({ title: '提醒付款成功~'})
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 查看物流 item = 订单列表某一项
			viewLogistics(item) {
				const { goods_img, express_type, express_number, consignee_phone, province_name, city_name, district_name, address } = item
				this.muLogisticsInfo({ 
					image: goods_img, 
					expressType: express_type, 
					logisticCode: express_number, 
					phone: consignee_phone,  
					address: province_name + city_name + district_name + address
					})
				this.jump.navigateTo(this.routeTable.pHAuctionLogisticsInfo)
			},
		},
		
		onPullDownRefresh() {
			this.init()
		},
		
		onReachBottom() {
			if ( this.page == this.totalPage || this.totalPage == 0) return;
			this.loadStatus = 'loading'
			this.page ++
			this.getOrderList()
		}
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>
