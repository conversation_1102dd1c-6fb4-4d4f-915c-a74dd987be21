<template>
  <view>
    <vh-navbar title="发布拍品" height="46" :customBack="customBack"></vh-navbar>
    <view v-if="!loading">
      <view v-if="id && rejectRemark" class="p-rela d-flex ptb-20-plr-24 bg-db2523">
        <image :src="ossIcon('/auction/close_24.png')" class="w-24 h-24"></image>
        <view class="flex-1 ml-10 font-24 text-ffffff l-h-34 w-b-b-w">{{ rejectRemark }}</view>
      </view>
      <view v-if="step === 1" class="pb-116">
        <view class="ptb-00-plr-24 pt-20">
          <view v-if="productList.length > 1" class="flex-sb-c mb-20 ptb-24-plr-20 bg-ffffff b-rad-10" @click="productSelectPopupVisible = true">
            <view class="flex-1 font-24 text-3 l-h-34">{{ product.cn_product_name }}</view>
            <view class="flex-c-c ml-10">
              <text class="font-24 text-9 l-h-34">选择商品</text>
              <image :src="ossIcon('/about/arrow_r_12_20.png')" class="ml-10 w-12 h-20" />
            </view>
          </view>

          <view v-if="!isWine" class="mb-20 ptb-32-plr-24 bg-ffffff">
            <view class="font-wei-500 font-32 text-6 l-h-44">选择分类</view>
            <view class="d-flex flex-wrap mt-04">
              <view v-for="item in notWineProductTypeList"
                :key="item.value"
                class="flex-c-c mt-20 ptb-00-plr-24 h-44 font-22 b-rad-23"
                :class="[{ 'mr-20': item.value !== 6 }, [item.value === productType ? 'text-ffffff bg-e80404' : 'text-6 b-s-02-999999']]"
                @click="productType = item.value"
              >{{ item.text }}</view>
            </view>
          </view>

          <view class="ptb-28-plr-24 bg-ffffff b-rad-10">
            <textarea v-model="title" placeholder="请输入拍品的标题（必填）" placeholder-style="text-d8d8d8" :maxlength="titleMaxLength" auto-height class="w-p100 h-min-110 font-28 text-3 l-h-40" />
            <view class="flex-sb-c">
              <view class="mt-10 font-24 text-9 l-h-34"><text :class="title ? 'text-e80404' : ''">{{ titleLength }}</text>/{{ titleMaxLength }}</view>
              <view v-if="isWine && productId" class="p-rela">
                <button class="vh-btn flex-c-c w-96 h-34 font-20 text-6 bg-ffffff b-s-02-d8d8d8 b-rad-06" @click="extractTitleShowStatus = !extractTitleShowStatus">提取标题</button>
                <view v-show="extractTitleShowStatus" style="top: 100%; right: -26rpx; transform: translateY(-10rpx);" class="p-abso w-210 h-210 z-999">
                  <image :src="ossIcon('/auction/drop_bg_210.png')" class="p-abso top-0 left-0 wh-p100"></image>
                  <view class="p-rela d-flex j-center pt-38 wh-p100">
                    <view class="d-flex flex-column a-center w-164">
                      <view class="ptb-16-plr-00 font-26 text-6 l-h-36" @click="onExtractTitle('cn_product_name')">中文标题</view>
                      <view class="w-112 h-02 bg-eeeeee"></view>
                      <view class="ptb-16-plr-00 font-26 text-6 l-h-36" @click="onExtractTitle('en_product_name')">英文标题</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <view class="mt-20 ptb-28-plr-24 bg-ffffff b-rad-10">
            <textarea v-model="brief" placeholder="请简单描述拍品，包括特点、瑕疵说明等（必填）" placeholder-style="text-d8d8d8" :maxlength="briefMaxLength" auto-height class="w-p100 h-min-160 font-28 text-3 l-h-40" />
            <view class="mt-10 font-24 text-9 l-h-34"><text :class="brief ? 'text-e80404' : ''">{{ briefLength }}</text>/{{ briefMaxLength }}</view>
          </view>

          <view class="mt-20 p-24 bg-ffffff b-rad-10">
            <view v-if="isWine" class="d-flex flex-wrap">
              <view>
                <AuctionUpload
                  :plate="88"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="productImgFileList"
                />
              </view>
              <view class="ml-12">
                <AuctionUpload
                  :plate="87"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="frontMarkImgFileList"
                  @on-list-change="onListChange($event, 'frontMarkImg')"
                  @on-uploaded="onUploaded($event, 'frontMarkImg')"
                />
              </view>
              <view class="ml-12">
                <AuctionUpload
                  :plate="86"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="backMarkImgFileList"
                  @on-list-change="onListChange($event, 'backMarkImg')"
                  @on-uploaded="onUploaded($event, 'backMarkImg')"
                />
              </view>
              <view class="mt-12">
                <AuctionUpload
                  :plate="85"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="bottleneckImgFileList"
                  @on-list-change="onListChange($event, 'bottleneckImg')"
                  @on-uploaded="onUploaded($event, 'bottleneckImg')"
                />
              </view>
              <view class="mt-12 ml-12">
                <AuctionUpload
                  :plate="84"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="wineLocationImgFileList"
                  @on-list-change="onListChange($event, 'wineLocationImg')"
                  @on-uploaded="onUploaded($event, 'wineLocationImg')"
                />
              </view>
            </view>
            <view v-else class="d-flex flex-wrap">
              <view>
                <AuctionUpload
                  :plate="83"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="detailImgFileList1"
                  @on-list-change="onListChange($event, 'detailImg1')"
                  @on-uploaded="onUploaded($event, 'detailImg1')"
                />
              </view>
              <view class="ml-12">
                <AuctionUpload
                  :plate="82"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="detailImgFileList2"
                  @on-list-change="onListChange($event, 'detailImg2')"
                  @on-uploaded="onUploaded($event, 'detailImg2')"
                />
              </view>
              <view v-if="detailImg2.length" class="ml-12">
                <AuctionUpload
                  :plate="81"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="detailImgFileList3"
                  @on-list-change="onListChange($event, 'detailImg3')"
                  @on-uploaded="onUploaded($event, 'detailImg3')"
                />
              </view>
            </view>
            <view class="flex-sb-c mt-20 ptb-00-plr-20 h-80 bg-f8f8f8 b-rad-10" @click="sampleGraphPopupVisible = true">
              <text class="font-24 text-3 l-h-34">{{ isWine ? '正标、背标、瓶口、酒液图片示意图示例' : '封面图及更多图片示意图示例' }}</text>
              <button class="vh-btn flex-c-c w-100 h-40 font-20 text-ff9127 b-s-01-ff9127 bg-transp b-rad-08">查看示例</button>
            </view>
          </view>

          <view v-if="isWine && productId" class="mt-20 ptb-40-plr-24 bg-ffffff b-rad-10">
            <view class="font-wei-500 font-32 text-6 l-h-44">酒类</view>
            <view class="d-flex mt-24">
              <view class="flex-c-c ptb-00-plr-32 h-46 font-24 text-3 l-h-34 bg-f4f4f4 b-rad-23">{{ product.product_type_name }}</view>
            </view>
            <view class="mt-32 h-02 bg-ececec"></view>
            <view class="mt-32">
              <view v-for="(item, index) in wineFieldList" :key="index" class="d-flex" :class="index ? 'mt-24' : ''">
                <view class="pt-24 w-68 font-wei-500 font-24 text-6 l-h-34">{{ item.label }}</view>
                <view class="flex-1 ptb-24-plr-16 font-24 text-3 l-h-34 bg-f4f4f4 b-rad-10">{{ item.value }}</view>
              </view>
            </view>
          </view>
        </view>
        <view class="p-fixed left-0 bottom-0 flex-c-c w-p100 h-96 bg-ffffff b-sh-00021200-022 z-999">
          <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="stepBtnDisabled ? 'bg-fce4e3' : 'bg-e80404'" @click="onStep">下一步</button>
        </view>
      </view>
      <view v-else-if="step === 2" class="pb-116">
        <view v-if="!(id && rejectRemark)" class="d-flex ptb-20-plr-24 bg-ffecd3">
          <view class="w-34">
            <image :src="ossIcon('/auction/tips_24.png')" class="w-24 h-24"></image>
          </view>
          <view class="flex-1 font-24 text-d76a00 l-h-34">{{ config.reminder }}</view>
        </view>
        <view class="mt-20 ptb-00-plr-24">
          <view class="ptb-00-plr-24 bg-ffffff b-rad-12">
            <view class="ptb-32-plr-00 bb-s-02-eeeeee">
              <view class="p-rela flex-sb-c">
                <text class="font-wei-500 font-28 text-3 l-h-40">卖家报价</text>
                <input
                  v-model="quote"
                  type="tel"
                  :focus="quoteInputFocus"
                  @focus="onPriceInputFocus('quote')"
                  @blur="onPriceInputBlur('quote')"
                  placeholder="请谨慎填写报价"
                  :placeholder-style="priceInputPlaceholderStyle"
                  :class="priceInputClass"
                />
                <view class="p-abso right-0 w-300 h-p100" @click="quoteInputFocus = true">
                </view>
              </view>
              <view v-if="buyingPrice" class="font-22 text-6 l-h-32">买入价¥{{ buyingPrice }}</view>
            </view>
            <view class="ptb-32-plr-00 bb-s-02-eeeeee">
              <view class="p-rela flex-sb-c">
                <text class="font-wei-500 font-28 text-3 l-h-40">起拍价</text>
                <input
                  v-model="price"
                  type="tel"
                  :focus="priceInputFocus"
                  @focus="onPriceInputFocus('price')"
                  @blur="onPriceInputBlur('price')"
                  placeholder="请输入起拍价"
                  :placeholder-style="priceInputPlaceholderStyle"
                  :class="priceInputClass"
                />
                <view class="p-abso right-0 w-300 h-p100" @click="priceInputFocus = true">
                </view>
              </view>
              <view class="font-22 text-6 l-h-32">不超卖家报价的{{ config.price_ratio }}%</view>
            </view>
            <view class="ptb-32-plr-00 bb-s-02-eeeeee">
              <view class="p-rela flex-sb-c">
                <text class="font-wei-500 font-28 text-3 l-h-40">加价幅度</text>
                <input
                  v-model="markup"
                  type="tel"
                  :focus="markupInputFocus"
                  @focus="onPriceInputFocus('markup')"
                  @blur="onPriceInputBlur('markup')"
                  placeholder="请输入加价幅度"
                  :placeholder-style="priceInputPlaceholderStyle"
                  :class="priceInputClass"
                />
                <view class="p-abso right-0 w-300 h-p100" @click="markupInputFocus = true">
                </view>
              </view>
              <view class="font-22 text-6 l-h-32">卖家报价的5%</view>
            </view>
            <view class="ptb-32-plr-00 bb-s-02-eeeeee">
              <view class="flex-sb-c">
                <text class="font-wei-500 font-28 text-3 l-h-40">委托保证金</text>
                <text class="font-wei-500 font-28 text-3 l-h-40">
                  <template v-if="entrustEarnest">{{ entrustEarnest | toPriceText }}</template>
                  <template v-else>-¥--</template>
                </text>
              </view>
              <view class="font-22 text-6 l-h-32">报价的{{ config.margin_ratio }}%</view>
            </view>
            <view class="ptb-32-plr-00 bb-s-02-eeeeee">
              <view class="flex-sb-c">
                <text class="font-wei-500 font-28 text-3 l-h-40">平台服务费<text class="ml-10 font-wei-500 font-24 text-6 l-h-34">(含税)</text></text>
                <text v-if="config.active_service_charge.id" class="font-wei-500 font-24 text-3 l-h-34">
                  <text>{{ config.active_service_charge.name }}</text>
                  <text class="text-e80404">{{ config.active_service_charge.ratio ? `${config.active_service_charge.ratio}%` : '免服务费' }}</text>
                </text>
                <text v-else class="font-wei-500 font-24 text-3 l-h-34">{{ config.default_service_charge.name }}{{ config.default_service_charge.ratio }}%</text>
              </view>
              <view v-if="config.active_service_charge.id" class="font-20 text-6 l-h-28">{{ config.default_service_charge.name }}{{ config.default_service_charge.ratio }}%</view>
            </view>
            <view class="ptb-32-plr-00 bb-s-02-eeeeee">
              <view class="flex-sb-c">
                <text class="font-wei-500 font-28 text-3 l-h-40">保留价</text>
                <view class="flex-c-c">
                  <view v-if="is_reserve_price === 1" class="p-rela w-320 mr-60 ptb-04-plr-00 bb-s-02-eeeeee">
                    <input
                      v-model="reserve_price"
                      type="tel"
                      :focus="reserve_priceInputFocus"
                      @focus="onPriceInputFocus('reserve_price')"
                      @blur="onPriceInputBlur('reserve_price')"
                      placeholder="请填写保留价"
                      placeholder-style="color: #aaa;"
                      class="w-p100 font-wei-500 font-28 text-3 l-h-40 text-center"
                    />
                    <view class="p-abso top-0 left-0 wh-p100" @click="reserve_priceInputFocus = true">
                  </view>
                  </view>
                  <view v-else class="flex-c-c mr-104" @click="onIsReservePriceChange(1)">
                    <image :src="ossIcon(`/auction/radio${is_reserve_price === 1 ? '_h' : ''}_26.png`)" class="w-26 h-26"></image>
                    <text class="ml-12 font-wei-500 font-24 text-3 l-h-34">是</text>
                  </view>
                  <view class="flex-c-c" @click="onIsReservePriceChange(0)">
                    <image :src="ossIcon(`/auction/radio${is_reserve_price === 0 ? '_h' : ''}_26.png`)" class="w-26 h-26"></image>
                    <text class="ml-12 font-wei-500 font-24 text-3 l-h-34">否</text>
                  </view>
                </view>
              </view>
              <view class="mt-24 font-22 text-9 l-h-32">
                <text v-if="reserve_price === '' || !is_reserve_price">温馨提示：保留价设置后，若结拍价小于保留价，则该拍品流拍。</text>
                <text v-else>温馨提示：您已设置<text class="text-e80404">保留价{{ reserve_price }}元</text>，您的拍品只有在<text class="text-e80404">大于等于{{ reserve_price }}元</text>时才会成交，其他情况视为流拍。</text>
              </view>
            </view>
            <view class="ptb-32-plr-00 bb-s-02-eeeeee">
              <view class="flex-sb-c">
                <text class="font-wei-500 font-28 text-3 l-h-40">发货方式</text>
                <text class="font-wei-500 font-24 text-3 l-h-34">包邮</text>
              </view>
              <view class="d-flex a-center mt-24">
                <view class="flex-1 font-22 text-9 l-h-32">为保证买家个人隐私，拍品发货由平台指定物流公司上门取件，<text class="text-e80404">运费将从拍卖成交金额中扣除。</text></view>
                <view class="ml-20 mr-20 w-02 h-42 bg-d8d8d8"></view>
                <button class="vh-btn flex-c-c w-92 h-34 font-18 text-6 bg-ffffff b-s-01-d8d8d8 b-rad-08" @click="freightEstimatePopupVisible = true">运费预估</button>
              </view>
            </view>
            <view class="ptb-32-plr-00">
              <view class="font-wei-500 font-28 text-3 l-h-40">竞拍时间</view>
              <view class="flex-s-c mt-24">
                <view class="w-192 font-24 text-3 l-h-34">预计开拍时间</view>
                <view v-if="auctionStartTimeList.length" class="flex-1 d-flex j-sb" @click="startTimePickerShow = true">
                  <view v-for="(item, index) in auctionStartTimeList" :key="index" class="d-flex a-center pl-16 pr-12 h-50 b-s-01-d8d8d8 b-rad-06">
                    <text class="font-24 text-3 l-h-34">{{ item }}</text>
                    <image :src="ossIcon('/invoices/arrow_d_20_12.png')" class="ml-10 w-20 h-12"></image>
                  </view>
                </view>
              </view>
              <view class="flex-s-c mt-28">
                <view class="w-192 font-24 text-3 l-h-34">选择拍卖时长</view>
                <view class="d-flex a-center pl-32 pr-12 h-50 b-s-01-d8d8d8 b-rad-06" @click="cyclePickerPopupVisible = true">
                  <text class="font-24 l-h-34" :class="auctionCycle ? 'text-3' : 'text-9'">{{ auctionCycle ? auctionCycle.name : '请选择' }}</text>
                  <image :src="ossIcon('/invoices/arrow_d_20_12.png')" class="ml-14 w-20 h-12"></image>
                </view>
              </view>
              <view class="flex-s-c mt-28">
                <text class="w-192 font-24 text-3 l-h-34">预计截拍时间</text>
                <view class="font-24 l-h-34" :class="auctionAbortTime ? 'text-e80404' : 'text-9'">{{ auctionAbortTime || '0000-00-00 00:00' }}</view>
              </view>
            </view>
          </view>
        </view>
        <view class="p-fixed left-0 bottom-0 flex-c-c w-p100 h-96 bg-ffffff b-sh-00021200-022 z-999">
          <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="submitBtnDisabled ? 'bg-fce4e3' : 'bg-e80404'" @click="onSubmit">提交</button>
        </view>
      </view>
    </view>
    <AuctionCreateProductSelectPopup v-model="productSelectPopupVisible" :list="productList" :productId="productId" @confirm="productId = $event" />
    <AuctionCreateSampleGraphPopup v-model="sampleGraphPopupVisible" :isWine="isWine" />
    <u-picker
      mode="time"
      v-model="startTimePickerShow"
      :params="startTimePickerParams"
      :defaultTime="auctionStartTime"
			:startYear="new Date().getFullYear()"
      @confirm="onConfirmUpdateStartTime"
    />
    <AuctionCreateCyclePickerPopup v-model="cyclePickerPopupVisible" :list="config.cycles" @confirm="onConfirmUpdateCycle" />
    <AuctionCreateFreightEstimatePopup v-model="freightEstimatePopupVisible" />
    <AuctionCreateResubmitTipsPopup v-model="resubmitTipsPopupVisible" :tips="resubmitTips" @confirm="onConfirmResubmit" />
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { nzPricePattern } from '@/common/js/utils/pattern'
import computedNumber from '@/common/js/utils/computedNumber'
import auctionMyCreateDraftsUtil from '@/common/js/utils/auctionMyCreateDrafts'
const PRICE_PRFIX = '¥'
const priceUtils = {
  addPrefix: (price) => {
    price = price.replaceAll(PRICE_PRFIX, '')
    return price && `${PRICE_PRFIX}${price}`
  },
  delPrefix: (price) => {
    price = price.replaceAll(PRICE_PRFIX, '')
    return price
  }
}
export default {
  data: () => ({
    loading: true,
    isWine: true,
    period: '',
    periodType: '',
    mainOrderNo: '',
    packageId: '',
    buyingPrice: '',
    id: '',
    rejectRemark: '',
    draftId: '',
    step: 1,
    productType: 0,
    productList: [],
    productId: 0,
    config: {},
    title: '',
    brief: '',
    frontMarkImgFileList: [],
    frontMarkImg: '',
    backMarkImgFileList: [],
    backMarkImg: '',
    bottleneckImgFileList: [],
    bottleneckImg: '',
    wineLocationImgFileList: [],
    wineLocationImg: '',
    detailImg1: '',
    detailImgFileList1: [],
    detailImg2: '',
    detailImgFileList2: [],
    detailImg3: '',
    detailImgFileList3: [],
    quote: '',
    quoteInputFocus: false,
    price: '',
    priceInputFocus: false,
    markup: '',
    markupInputFocus: false,
    entrustEarnest: '',
    is_reserve_price: 0,
    reserve_price: '',
    reserve_priceInputFocus: false,
    auctionStartTime: '',
    auctionCycle: null,
    productSelectPopupVisible: false,
    extractTitleShowStatus: false,
    sampleGraphPopupVisible: false,
    resubmitTipsPopupVisible: false,
    startTimePickerShow: false,
    startTimePickerParams: {
      year: true,
      month: true,
      day: true,
      hour: true,
      minute: true,
    },
    cyclePickerPopupVisible: false,
    freightEstimatePopupVisible: false,
    goodsId: '',
    resubmitTips: [],
    titleMaxLength: 80,
    briefMaxLength: 1000,
    notWineProductTypeList: [
      { value: 2, text: '酒标' },
      { value: 3, text: '酒塞' },
      { value: 4, text: '酒瓶' },
      { value: 5, text: '酒具' },
      { value: 6, text: '其他酒类周边' },
    ],
    priceInputPlaceholderStyle: 'color: #aaa;',
    priceInputClass: 'w-300 font-wei-500 font-28 text-3 l-h-40 text-right',
  }),
  computed: {
    ...mapState(['ossPrefix', 'agreementPrefix']),
    ...mapState('personAuctionGoods', ['entrustEarnestPayStatus']),
    customBack () {
      if (this.step === 2) {
        return () => {
          this.step--
        }
      }
      return null
    },
    product ({ productList, productId }) {
      const findItem = productList.find(item => item.id === productId)
      return findItem || {}
    },
    productImgFileList ({ product }) {
      const { product_img = '' } = product
      if (!product_img) return []
      return this.encodeImgFileList(product_img)
    },
    wineFieldList ({ product }) {
      const { cn_product_name, country_name, capacity, producing_area_name, grape } = product
      return [
        { label: '名称', value: cn_product_name },
        { label: '产国', value: country_name },
        { label: '容量', value: capacity },
        { label: '产区', value: producing_area_name },
        { label: '品种', value: grape }
      ]
    },
    auctionStartTimeList ({ auctionStartTime }) {
      if (!auctionStartTime) return []
      const [date, time] = auctionStartTime?.split(' ') || []
      const [year, month, day] = date?.split('-') || []
      return [year, month, day, time]
    },
    auctionAbortTime ({ auctionStartTime, auctionCycle }) {
      if (!auctionStartTime) return ''
      if (!auctionCycle) return ''
      let timestamp = new Date(auctionStartTime).getTime()
      const { unit, count } = auctionCycle
      switch (unit) {
        case 1:
          timestamp += count * 60 * 60 * 1000
          break
        case 2:
          timestamp += count * 24 * 60 * 60 * 1000
          break
        case 3:
          timestamp += count * 7 * 24 * 60 * 60 * 1000
          break
      }
      return this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM')
    },
    stepBtnDisabled ({ isWine, productType, title, brief, frontMarkImg, backMarkImg, bottleneckImg, wineLocationImg, detailImg1 }) {
      if (isWine) {
        return !title || !brief || !frontMarkImg || !backMarkImg || !bottleneckImg || !wineLocationImg
      }
      return !productType || !title || !brief || !detailImg1
    },
    submitBtnDisabled ({ quote, price, markup, is_reserve_price, reserve_price, auctionCycle }) {
      return !quote || !price || !markup || is_reserve_price === '' || (is_reserve_price === 1 && !reserve_price) || !auctionCycle
    },
    titleLength ({ title, titleMaxLength }) {
      let count = 0
      for (const letter of title) { count++ }
      return count > titleMaxLength ? titleMaxLength : count
    },
    briefLength ({ brief, briefMaxLength }) {
      let count = 0
      for (const letter of brief) { count++ }
      return count > briefMaxLength ? briefMaxLength : count
    }
  },
  watch: {
    quote (newVal, oldVal) {
      if (priceUtils.delPrefix(newVal) === priceUtils.delPrefix(oldVal)) return
      this.quoteWatchFun()
    }
  },
  filters: {
    toPriceText (value) {
      value = value.replace(PRICE_PRFIX, '')
      if (!value) return ''
      return `${PRICE_PRFIX}${value}`
    }
  },
  onLoad (options) {
    const { isWine, period, periodType, mainOrderNo, packageId, buyingPrice, id = '', draftId = '' } = options
    this.isWine = !!isWine
    this.period = period
    this.periodType = periodType
    this.mainOrderNo = mainOrderNo
    this.packageId = packageId
    this.buyingPrice = buyingPrice
    if (this.isWine) this.productType = 1
    this.id = +id
    this.draftId = +draftId
    this.load()
  },
  onShow () {
    if (this.goodsId && this.entrustEarnestPayStatus) {
      this.SET_ENTRUST_EARNEST_PAYSTATUS(false)
      this.jump.redirectTo(this.$routeTable.pHAuctionEntrustEarnestPaySuccess)
    }
  },
  onHide () {
    console.log('onHide')
    this.saveDraft()
  },
  onUnload () {
    console.log('onUnload')
    this.saveDraft()
  },
  methods: {
    ...mapMutations('personAuctionGoods', ['SET_ENTRUST_EARNEST_PAYSTATUS']),
    async load () {
      if (this.id || this.draftId) {
        await this.getGoodsDetail()
      }
      const reqs = [this.getConfig()]
      if (this.isWine) {
        reqs.push(this.getProductList())
      }
      Promise.all(reqs).then(() => {
        this.loading = false
      })
    },
    async getProductList () {
      const params = {
        period: this.period,
        periods_type: this.periodType,
        main_order_no: this.mainOrderNo,
        package_id: this.packageId
      }
      const res = await this.$u.api.findProductListByPeriod(params)
      this.productList = res?.data?.list || []
      if (!this.id && this.productList.length === 1) {
        this.productId = this.productList[0].id
      }
    },
    async getConfig () {
      const res = await this.$u.api.getPersonAuctionConfig()
      const config = res?.data || {}
      this.config = { markup_ratio: 5, ...config }
      this.quoteWatchFun()
      this.auctionStartTime = this.$u.timeFormat(this.config.earliest_auction_time * 1000, 'yyyy-mm-dd hh:MM')
    },
    async getGoodsDetail () {
      let data
      if (this.id) {
        const res = await this.$u.api.getPersonAuctionGoods({ id: this.id })
        data = res?.data || {}
      } else if (this.draftId) {
        data = auctionMyCreateDraftsUtil.getGoodsByDraftId(this.draftId)
      }
      const { reject_remark, other_parameter, product_type, product_id, title, brief, product_img, front_mark_img, back_mark_img, bottleneck_img, wine_location_img, detailImg, quote, price, markup, is_reserve_price, reserve_price } = data
      const { period, periods_type, main_order_no, package_id, payment_amount } = other_parameter
      this.rejectRemark = reject_remark
      this.period = period
      this.periodType = periods_type
      this.mainOrderNo = main_order_no
      this.packageId = package_id
      this.buyingPrice = payment_amount
      this.isWine = product_type === 1
      this.productType = product_type
      this.productId = product_id
      this.title = title
      this.brief = brief
      if (this.isWine) {
        this.frontMarkImgFileList = this.encodeImgFileList(front_mark_img),
        this.frontMarkImg = front_mark_img
        this.backMarkImgFileList = this.encodeImgFileList(back_mark_img)
        this.backMarkImg = back_mark_img
        this.bottleneckImgFileList = this.encodeImgFileList(bottleneck_img)
        this.bottleneckImg = bottleneck_img
        this.wineLocationImgFileList = this.encodeImgFileList(wine_location_img)
        this.wineLocationImg = wine_location_img
      } else {
        this.detailImgFileList1 = this.encodeImgFileList(product_img)
        this.detailImg1 = product_img
        const [detailImg2 = '', detailImg3 = ''] = detailImg?.split(',') || []
        this.detailImgFileList2 = this.encodeImgFileList(detailImg2)
        this.detailImg2 = detailImg2
        this.detailImgFileList3 = this.encodeImgFileList(detailImg3)
        this.detailImg3 = detailImg3
      }
      this.quote = priceUtils.addPrefix(`${quote}`)
      this.price = priceUtils.addPrefix(`${price}`)
      this.markup = priceUtils.addPrefix(`${markup}`)
      this.is_reserve_price = is_reserve_price
      this.reserve_price = reserve_price ? priceUtils.addPrefix(`${reserve_price}`) : ''
    },
    onStep () {
      if (this.stepBtnDisabled) return
      this.step = 2
    },
    onSubmit () {
      if (this.submitBtnDisabled) return
      this.feedback.loading({ title: '' })
      const params = this.isWine ? this.getWineParams() : this.getNotWineParams()
      if (this.id) {
        this.$u.api.preResubmitPersonAuctionGoods({ ...params, id: this.id }).then(res => {
          const { id, entrusted_margin, entrusted_remark1, entrusted_remark2 } = res.data
          if (entrusted_margin !== 1) {
            this.resubmitTipsPopupVisible = true
            this.resubmitTips = [entrusted_remark1, entrusted_remark2]
          } else {
            this.$u.api.resubmitPersonAuctionGoods({ ...params, id: this.id }).then(() => {
              this.jump.redirectTo(this.$routeTable.pHAuctionEntrustEarnestPaySuccess)
            })
          }
        })
        return
      }
      this.$u.api.createPersonAuctionGoods(params).then(res => {
        this.saveDraft()
        this.goodsId = res.data.id
        this.jump.navigateTo(`${this.$routeTable.pHAuctionEntrustEarnestPay}?goodsId=${this.goodsId}&draftGoodsId=${this.draftId}&earnest=${this.entrustEarnest}&earnestRatio=${this.config.margin_ratio}`)
      })
    },
    getWineParams () {
      const { title, brief, productImgFileList, frontMarkImg, backMarkImg, bottleneckImg, wineLocationImg, quote, price, markup, is_reserve_price, reserve_price, } = this
      const params = {
        issue_type: 1,
        label: 1,
        source: 1,
        freight_type: 1,
        product_id: this.productId,
        period: this.period,
        period_type: this.periodType,
        product_type: this.productType,
        title,
        brief,
        product_img: this.decodeImgFileList(productImgFileList),
        front_mark_img: frontMarkImg,
        back_mark_img: backMarkImg,
        bottleneck_img: bottleneckImg,
        wine_location_img: wineLocationImg,
        quote: priceUtils.delPrefix(quote),
        price: priceUtils.delPrefix(price),
        markup: priceUtils.delPrefix(markup),
        service_charge_id: this.config.active_service_charge.id || this.config.default_service_charge.id,
        is_reserve_price,
        reserve_price: priceUtils.delPrefix(reserve_price),
        sell_time: this.$u.timeFormat(this.auctionStartTime, 'yyyy-mm-dd hh:MM:ss'),
        closing_auction_time: this.$u.timeFormat(this.auctionAbortTime, 'yyyy-mm-dd hh:MM:ss'),
        category_arr: {
          category_name: this.product.product_type_name,
          country: this.product.country_name,
          net_content: this.product.capacity,
          place: this.product.producing_area_name,
          grape_variety: this.product.grape,
          alcoholic_strength: this.product.alcohol,
          years: this.product.products_years
        },
        other_parameter: {
          period: this.period,
          periods_type: this.periodType,
          main_order_no: this.mainOrderNo,
          package_id: this.packageId,
          payment_amount: this.buyingPrice
        }
      }
      return params
    },
    getNotWineParams () {
      const { title, brief, detailImg1, detailImg2, detailImg3, quote, price, markup, is_reserve_price, reserve_price } = this
      const params = {
        issue_type: 1,
        label: 1,
        source: 1,
        freight_type: 1,
        product_type: this.productType,
        title,
        brief,
        product_img: detailImg1,
        detailImg: [detailImg2, detailImg3].filter(img => img).join(),
        quote: priceUtils.delPrefix(quote),
        price: priceUtils.delPrefix(price),
        markup: priceUtils.delPrefix(markup),
        service_charge_id: this.config.active_service_charge.id || this.config.default_service_charge.id,
        is_reserve_price,
        reserve_price: priceUtils.delPrefix(reserve_price),
        sell_time: this.$u.timeFormat(this.auctionStartTime, 'yyyy-mm-dd hh:MM:ss'),
        closing_auction_time: this.$u.timeFormat(this.auctionAbortTime, 'yyyy-mm-dd hh:MM:ss'),
        category_arr: {
          category_name: this.notWineProductTypeList.find(item => item.value === this.productType)?.text || '',
        },
        other_parameter: {}
      }
      return params
    },
    onExtractTitle (key) {
      this.title = this.product[key]
      this.extractTitleShowStatus = false
    },
    onListChange (list, key) {
      this[key] = this.decodeImgFileList(list)
      if (key === 'detailImg2' && !list.length && this.detailImg3) {
        this.detailImgFileList2 = this.encodeImgFileList(`${this.ossPrefix}${this.detailImg3}`)
        this.detailImg3 = ''
        this.detailImgFileList3 = []
      }
    },
    onUploaded (list, key) {
      this[key] = this.decodeImgFileList(list)
      uni.hideLoading()
    },
    quoteWatchFun () {
      const price = priceUtils.delPrefix(this.quote)
      if (!price || !nzPricePattern.test(price)) {
        if (this.quoteInputFocus) this.markup = ''
        this.entrustEarnest = ''
        return
      }
      if (this.quoteInputFocus) {
        const markup = Math.round(computedNumber(+price, '*', this.config.markup_ratio / 100).result)
        this.markup = markup < 1 ? '' : `¥${markup}`
      }
      this.entrustEarnest = `${computedNumber(+price, '*', this.config.margin_ratio / 100).result}`
    },
    onPriceInputFocus (key) {
      this[key] = priceUtils.delPrefix(this[key])
      this[`${key}InputFocus`] = true
    },
    onPriceInputBlur (key) {
      this[key] = priceUtils.addPrefix(this[key])
      this[`${key}InputFocus`] = false
    },
    onIsReservePriceChange (value) {
      this.is_reserve_price = value
      if (value) {
        this.reserve_priceInputFocus = true
      }
    },
    onConfirmUpdateStartTime (value) {
      const { year, month, day, hour, minute } = value
      this.auctionStartTime = `${year}-${month}-${day} ${hour}:${minute}`
    },
    onConfirmUpdateCycle (cycle) {
      this.auctionCycle = cycle
    },
    onConfirmResubmit () {
      this.feedback.loading({ title: '' })
      const params = this.isWine ? this.getWineParams() : this.getNotWineParams()
      this.$u.api.resubmitPersonAuctionGoods({ ...params, id: this.id }).then(res => {
        this.resubmitTipsPopupVisible = false
        this.goodsId = res.data.id
        this.jump.navigateTo(`${this.$routeTable.pHAuctionEntrustEarnestPay}?goodsId=${this.goodsId}&earnest=${this.entrustEarnest}&earnestRatio=${this.config.margin_ratio}`)
      })
    },
    encodeImgFileList (url) {
      if (!url) return []
      return [{ url: url.includes('http') ? url : `${this.ossPrefix}${url}` }]
    },
    decodeImgFileList (list) {
      return list.map(({ response, url }) => response || url.replace(this.ossPrefix, '')).join()
    },
    saveDraft () {
      if (this.id) return
      const commonBool = () => {
        return !this.title && !this.brief && !this.quote && !this.price
      }
      if (this.isWine) {
        if (!this.frontMarkImg && !this.backMarkImg && !this.bottleneckImg && !this.wineLocationImg && commonBool()) return
      } else {
        if (!this.productType && !this.detailImg1 && !this.detailImg2 && !this.detailImg3 && commonBool()) return
      }
      const params = this.isWine ? this.getWineParams() : this.getNotWineParams()
      if (this.draftId) {
        params.id = this.draftId
        auctionMyCreateDraftsUtil.updateGoods(params)
      } else {
        params.id = Date.now()
        auctionMyCreateDraftsUtil.unshiftGoods(params)
        this.draftId = params.id
      }
    }
  },
}
</script>

<style>
  page {
    background: #f5f5f5;
  }
</style>

<style lang="scss" scoped>
</style>
