<template>
  <view>
    <vh-navbar title="绑定支付宝" height="46" />
    <view>
      <view class="h-20 bg-f5f5f5"></view>
      <view class="ptb-28-plr-32">
        <view class="font-wei-600 font-24 text-6 l-h-34">请绑定本人支付宝账号</view>
        <view class="flex-sb-c h-108 bb-s-02-eeeeee">
          <text class="font-wei-500 font-32 text-3 l-h-44">真实姓名</text>
          <input v-model="name" placeholder="请输入本人姓名" placeholder-style="font-size:28rpx; color:#999" class="w-400 font-32 text-3 text-right" />
        </view>
        <view class="flex-sb-c h-108 bb-s-02-eeeeee">
          <text class="font-wei-500 font-32 text-3 l-h-44">支付宝号</text>
          <input v-model="account" type="number" placeholder="请输入支付宝绑定手机号" placeholder-style="font-size:28rpx; color:#999" class="w-400 font-32 text-3 text-right" />
        </view>
        <view class="flex-sb-c h-108 bb-s-02-eeeeee">
          <text class="font-wei-500 font-32 text-3 l-h-44">验证码</text>
          <view class="flex-c-c">
            <input v-model="code" type="number" placeholder="请输入" placeholder-style="font-size:28rpx; color:#999" class="w-300 font-32 text-3 text-right" />
            <button class="vh-btn flex-c-c ml-32 w-160 h-62 font-wei-500 font-24 bg-d8d8d8 b-rad-32" :class="countDownInterval ? 'text-3' : 'text-ffffff'" @click="onSendCode">{{ btnText }}</button>
          </view>
        </view>
      </view>
      <view class="p-fixed left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-********-022 z-9999">
        <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="disabled ? 'bg-fce4e3' : 'bg-e80404'" @click="onSubmit">提交绑定</button>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'auctionPayeeAccountEdit', // 收款账户编辑
  data: () => ({
    name: '',
    account: '',
    code: '',
    btnText: '获取验证码',
    countDown: 60,
    countDownInterval: null
  }),
  computed: {
    ...mapState(['routeTable']),
    ...mapState('auctionPayeeAccount', ['auctionPayeeAccount']),
    disabled ({ name, account, code }) {
      return !(name && account && code)
    }
  },
  methods: {
    async onSubmit () {
      if (this.disabled) return
      if (!this.$u.test.mobile(this.account)) {
        this.feedback.toast({ title: '请输入正确的支付宝号' })
        return
      }
      this.feedback.loading({ title: '添加中...' })
      const { id } = this.auctionPayeeAccount
      const params = { name: this.name, account: this.account, code: this.code }
      if (id) {
        params.id = id
        await this.$u.api.updateAuctionPayeeAccount(params)
      } else {
        await this.$u.api.createAuctionPayeeAccount(params)
      }
      this.feedback.toast({ title: '添加成功' })
      uni.navigateBack()
    },
    async onSendCode () {
      if (this.countDown !== this.$options.data().countDown) return
      if (!this.account) {
        this.feedback.toast({ title: '请输入支付宝绑定手机号' })
        return
      }
      const params = { telephone: this.account, randstr: this.$u.guid(), ticket: 'vinehoo-lt-sms-ticket' }
      await this.$u.api.sendSmsCode(params)
      this.startCountDown()
    },
    startCountDown() {
      this.btnText = `${this.countDown}s`
      this.countDownInterval = setInterval(() => {
        this.countDown--
        if (this.countDown > 0) {
          this.btnText = `${this.countDown}s`
        } else {
          const { countDown, countDownInterval } = this.$options.data()
          this.btnText = '重新获取'
          this.countDown = countDown
          clearInterval(this.countDownInterval)
          this.countDownInterval = countDownInterval
        }
      }, 1000)
    },
  },
  onLoad () {
    if (!this.auctionPayeeAccount) {
      this.jump.redirectTo(this.routeTable.pHAuctionPayeeAccount)
      return
    }
  },
  onUnload() {
    this.countDownInterval && clearInterval(this.countDownInterval)
  }
}
</script>

<style lang="scss" scoped>
</style>
