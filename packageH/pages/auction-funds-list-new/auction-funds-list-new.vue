<template>
  <view>
    <vh-navbar title="资金明细" height="46" :background="{ background: navbarBackground }" back-icon-color="#FFF" title-color="#FFF" :customStatusBarHeight="20"></vh-navbar>
    <view v-if="!loading">
      <view class="p-abso top-0 w-p100">
        <view v-if="$appStatusBarHeight" :style="{ height: `${$appStatusBarHeight - 20}px`, background: isSeller ? '#FF883C' : '#D80E0E' }"></view>
        <image :src="ossIcon(isSeller ? '/auction/funds_list_bg_750_588.png' : '/auction/funds_list_bg_750_516.png')" style="vertical-align: top;" class="w-p100" :class="[isSeller ? 'h-588' : 'h-516']" />
      </view>
      <view class="p-rela pb-32">
        <view
          v-if="isAuctionSeller"
          class="p-abso top-0 right-0 d-flex a-center pl-24 w-152 h-50"
          :style="[
            { transform: 'translateY(-50%)' },
            { background: isSeller ? 'rgba(198,98,35,0.65)' : 'rgba(166,6,6,0.65)' },
            { borderRadius: '200rpx 0rpx 0rpx 200rpx' }
          ]"
          @click="onTypeChange"
        >
          <text class="font-wei-500 font-24 text-ffffff">{{ isSeller ? '去买家' : '去卖家' }}</text>
          <image :src="ossIcon('/auction/switch_22.png')" class="ml-14 w-22 h-22"></image>
        </view>
        <view :style="{ height: `${cardHeight}px` }" class="flex-c-e pb-24">
          <view v-if="isSeller" class="ptb-00-plr-20 w-p100 h-380" @click="fundsIntroPopupVisible = true">
            <view class="pt-42">
              <view class="flex-c-c">
                <text class="font-28 text-3 l-h-34">总收益</text>
                <image :src="ossIcon('/auction/tips_22.png')" class="ml-10 w-22 h-22" />
              </view>
              <view class="font-wei-500 font-72 text-3 l-h-100 text-center"><text class="font-36">¥</text>{{ stats.total_earnings }}</view>
            </view>
            <view class="flex-c-c mt-48 ptb-00-plr-60">
              <view class="flex-1 flex-sb-c">
                <view class="flex-c-c flex-column">
                  <view class="flex-c-c">
                    <text class="font-28 text-3 l-h-34">待入账</text>
                    <image :src="ossIcon('/auction/tips_22.png')" class="ml-06 w-22 h-22" />
                  </view>
                  <view class="font-wei-500 font-40 text-e8040 l-h-56"><text class="font-28">¥</text>{{ stats.await_earnings }}</view>
                </view>
                <view class="flex-c-c flex-column">
                  <view class="flex-c-c">
                    <text class="font-28 text-3 l-h-34">已入账</text>
                    <image :src="ossIcon('/auction/tips_22.png')" class="ml-06 w-22 h-22" />
                  </view>
                  <view class="font-wei-500 font-40 text-3 l-h-56"><text class="font-28">¥</text>{{ stats.account_earnings }}</view>
                </view>
              </view>
              <view class="ml-40 mr-40 w-02 h-90 bg-e0e1e0"></view>
              <view class="flex-c-c flex-column">
                <view class="flex-c-c">
                  <text class="font-28 text-3 l-h-34">待释放保证金</text>
                  <image :src="ossIcon('/auction/tips_22.png')" class="ml-06 w-22 h-22" />
                </view>
                <view class="font-wei-500 font-40 text-3 l-h-56"><text class="font-28">¥</text>{{ stats.total_earnest }}</view>
              </view>
            </view>
          </view>
          <view v-else class="ptb-00-plr-20 w-p100 h-306" @click="fundsIntroPopupVisible = true">
            <view class="p-rela flex-c-c pt-86">
              <view class="flex-1 flex-c-c flex-column">
                <view class="flex-c-c">
                  <text class="font-24 text-6 l-h-34">总消费</text>
                  <image :src="ossIcon('/auction/tips_22.png')" class="ml-06 w-22 h-22" />
                </view>
                <text class="mt-12 font-wei-500 font-60 text-3 l-h-50"><text class="font-48">¥</text>{{ ' ' }}{{ stats.total_consume }}</text>
              </view>
              <view style="border: 2rpx solid rgba(255, 255, 255, 0.97);" class="w-02 h-130"></view>
              <view class="flex-1 flex-c-c flex-column">
                <view class="flex-c-c">
                  <text class="font-24 text-6 l-h-34">待释放保证金</text>
                  <image :src="ossIcon('/auction/tips_22.png')" class="ml-06 w-22 h-22" />
                </view>
                <text class="mt-12 font-wei-500 font-60 text-3 l-h-50"><text class="font-48">¥</text>{{ ' ' }}{{ stats.total_earnest }}</text>
              </view>
            </view>
          </view>
        </view>
        <view v-if="monthList.length" style="margin-top: -32rpx;">
          <view v-for="(monthItem, monthIndex) in monthList" :key="monthIndex" class="ptb-00-plr-24">
            <view class="flex-s-c h-104 font-28 text-6 l-h-40">{{ monthItem.monthTime }}</view>
            <view style="background: rgba(255,255,255,0.63);" class="ptb-00-plr-24 b-rad-10">
              <view v-for="(item, index) in monthItem.list" :key="index" class="list__ifunds d-flex pt-32" @click="jump.navigateTo(`${routeTable.pHAuctionFundsDetailNew}?orderNo=${item.main_order_no}`)">
                <image :src="ossIcon(`/auction/${item.type === MAuctionFundsOrderType.Earnest ? 'funds_earnest_72' : 'funds_order_72'}.png`)" class="mt-04 w-56 h-56" />
                <view class="ml-24 flex-1">
                  <view class="flex-sb-c">
                    <view class="flex-1 font-wei-500 font-28 text-3 l-h-40 text-hidden-1">{{ item.type | toText('MAuctionFundsOrderTypeText') }}-{{ item.title }}</view>
                    <button class="vh-btn flex-c-c ml-40 w-146 h-46 font-24 text-6 bg-ffffff b-s-02-d8d8d8 b-rad-16">查看明细</button>
                  </view>
                  <view>
                    <view v-for="(infoItem, infoIndex) in item.info" :key="infoIndex" class="flex-sb-c pb-12" :class="infoIndex ? 'pt-14 bt-d-02-d8d8d8' : 'pt-16'">
                      <text class="font-24 text-9 l-h-34">{{ infoItem.payment_time }}</text>
                      <view class="text-right">
                        <view class="font-wei-500" :class="[infoIndex ? 'mb-04 font-24 l-h-34' : 'mb-02 font-28 l-h-40', infoItem.$textClazz || 'text-6']">
                          <text v-if="infoItem.explain" class="font-18 l-h-26">（{{ infoItem.explain }}）</text>
                          <text>{{ infoItem.status_cn }}</text>
                        </view>
                        <view v-if="infoItem.payment_amount" class="font-24 l-h-34" :class="infoItem.$textClazz || 'text-3'">{{ infoItem.source_type === MAuctionFundsType.Income ? '+' : '-' }}{{ infoItem.payment_amount }}</view>
                      </view>
                    </view>
                  </view>
                  <view class="list__ifunds-divide"></view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <AuctionNone v-else class="pt-248" img="/auction/none_442_400.png" imgClazz="w-442 h-400" desc="暂无记录～" />
      </view>
    </view>
    <AuctionFundsIntro v-model="fundsIntroPopupVisible" isNew :isSeller="isSeller" />
  </view>
</template>

<script>
import { MAuctionFundsType, MAuctionFundsOrderType } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'
import listMixin from '@/common/js/mixins/listMixin'

export default {
  name: 'auctionFundsListNew', // 新拍卖资金明细
  mixins: [listMixin],
  data: () => ({
    MAuctionFundsType,
    MAuctionFundsOrderType,
    navbarBackground: 'transparent',
    query: { type: 1 },
    stats: {},
    monthList: [],
    fundsIntroPopupVisible: false,
    isAuctionSeller: 0
  }),
  computed: {
    ...mapState(['routeTable']),
    isSeller ({ query }) {
      return query.type === 2
    },
    cardHeight ({ isSeller }) {
      return uni.upx2px(isSeller ? 588 : 516) - 20 - 46
    }
  },
  methods: {
    async getIsAuctionSeller() {
			if(this.login.isLogin(this.$vhFrom, 0)){
				const { data: { is_auction_seller = 0 } } = await this.$u.api.userSpecifiedData({ field: 'is_auction_seller' })
				this.isAuctionSeller = +is_auction_seller
			} else {
				this.isAuctionSeller = 1
			}
		},
    async load (query) {
      const res = await this.$u.api.searchAuctionFundsListNew(query)
      const { list = [], total_consume = 0, total_earnest = 0, await_earnings = 0, total_earnings = 0, account_earnings = 0 } = res?.data || {}
      this.stats = {
        total_consume: total_consume || 0,
        total_earnest: total_earnest || 0,
        await_earnings: await_earnings || 0,
        total_earnings: total_earnings || 0,
        account_earnings: account_earnings || 0
      }
      if (query.page === 1) {
        this.monthList = []
      }
      list.forEach(item => {
        const monthTime = this.$u.timeFormat(item.created_time, 'yyyy年mm月')
        if (item.info && item.info.length === 1 ) {
          item.info.forEach(infoItem => {
            infoItem.$textClazz = 'text-ff9127'
          })
        }
        const findMonthItem = this.monthList.find(item => item.monthTime === monthTime)
        if (findMonthItem) {
          findMonthItem.list.push(item)
        } else {
          this.monthList.push({ monthTime, list: [item] })
        }
      })
      return res
    },
    onTypeChange () {
      const type = this.isSeller ? 1 : 2
      this.load({ ...this.query, page: 1, type })
    }
  },
  onLoad (options) {
    const { type = 1 } = options
    this.query.type = type
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.load()
      this.getIsAuctionSeller()
    })
  },
  onPageScroll (res) {
    if(res.scrollTop <= 100){
      this.navbarBackground = this.isSeller ? `rgba(255, 136, 60, ${res.scrollTop / 100})` : `rgba(224, 20, 31, ${res.scrollTop / 100})`
    }else{
      this.navbarBackground = this.isSeller ? `rgba(255, 136, 60, 1)` : `rgba(224, 20, 31, 1)`
    }
  },
  onReachBottom () {
    this.reachBottomLoad()
  },
}
</script>

<style>
  page {
    background: #f5f5f5;
  }
</style>

<style lang="scss" scoped>
  .list {
    &__ifunds {
      &:last-of-type {
        .list__ifunds-divide {
          display: none;
        }
      }

      &-divide {
        margin-top: 20rpx;
        height: 2rpx;
        background: #d8d8d8;
      }
    }
  }
</style>
