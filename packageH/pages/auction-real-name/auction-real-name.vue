<template>
  <view>
    <vh-navbar title="实名认证" height="46" :background="{ background: '#e80404' }" back-icon-color="#FFF" title-color="#FFF" />
    <view v-if="!loading" class="pt-28">
      <view v-for="(item, index) in list" :key="index" @click="onJump(item)">
        <view v-if="index" class="mtb-00-mlr-auto w-686 h-02 bg-eeeeee"></view>
        <view class="flex-sb-c p-32">
          <view class="flex-c-c">
            <image :src="ossIcon(item.icon)" class="w-72 h-72" />
            <view class="ml-24">
              <view class="flex-s-c">
                <view class="font-wei-500 font-28 text-3 l-h-40">{{ item.name }}</view>
                <view v-if="item.status" class="flex-c-c ml-20 w-112 h-38 font-24 text-ff9127 l-h-34 b-s-02-ff9127 b-rad-10">已认证</view>
              </view>
              <view class="mt-06 font-24 text-9 l-h-34">{{ item.desc }}</view>
            </view>
          </view>
          <image :src="ossIcon('/about/arrow_r_12_20.png')" class="w-12 h-20" />
        </view>
      </view>
    </view>
    <u-popup v-model="popupVisible" mode="center" :mask-close-able="false" width="552" height="414" border-radius="20">
      <view class="p-rela w-552 h-414">
        <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-552 h-414" />
        <view class="p-rela pt-24">
          <view class="font-wei-600 font-32 text-3 l-h-44 text-center">温馨提示</view>
          <view class="mt-20 ptb-00-plr-36 font-26 text-6 l-h-48 text-center">您当前已通过实名认证。如更改认证类型并通过审核，将会<text class="text-e80404">注销您已通过认证的实名信息（该操作不可撤销）</text>。您确认要继续更改认证类型吗？</view>
          <view class="flex-sb-c ptb-00-plr-76 mt-40">
            <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32" @click="popupVisible = false">再想想</button>
            <button class="vh-btn flex-c-c w-180 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="jumpRNCompany">确定</button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { MAuctionRNType } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'

export default {
  name: 'auctionRealName', // 拍卖实名认证
  data: () => ({
    loading: true,
    list: [],
    companyRNInfo: null,
    popupVisible: false
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    async onJump (item) {
      const { type, path } = item
      if (type === MAuctionRNType.PersonalRN && this.list[1].status) {
        this.feedback.toast({ title: '您已完成商户认证，无需个人认证了' })
        return
      }
      if (type === MAuctionRNType.CompanyRN && this.list[0].status) {
        if (!this.companyRNInfo) {
          const res = await this.$u.api.getAuctionRNInfo({ type: MAuctionRNType.CompanyRN })
          this.companyRNInfo = res?.data || {}
        }
        if (!this.companyRNInfo || !Object.keys(this.companyRNInfo).length) {
          this.popupVisible = true
          return
        }
      }
      this.jump.navigateTo(path)
    },
    jumpRNCompany () {
      this.popupVisible = false
      this.jump.navigateTo(this.routeTable.pHAuctionRNCompany)
    },
    async loadAuctionUserInfo () {
      const { pHAuctionRNPersonal, pHAuctionRNCompany } = this.routeTable
      const list = [
        { name: '个人认证', desc: '个人真实身份认证，完成后可发布拍品', icon: '/auction/rn_personal_72.png', path: pHAuctionRNPersonal, status: false, type: MAuctionRNType.PersonalRN },
        { name: '商户认证', desc: '通过商户认证后，可申请商户发布拍品', icon: '/auction/rn_company_72.png', path: pHAuctionRNCompany, status: false, type: MAuctionRNType.CompanyRN }
      ]
      const res = await this.$u.api.getAuctionUserInfo()
      const { ac_type = 0 } = res?.data?.info || {}
      switch (ac_type) {
        case MAuctionRNType.PersonalRN:
          list[0].status = true
          break
        case MAuctionRNType.CompanyRN:
          list[1].status = true
          break
      }
      this.list = list
    },
  },
  onShow () {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.loadAuctionUserInfo().finally(() => {
        this.loading = false
      })
    })
  }
}
</script>

<style lang="scss" scoped>
</style>
