<template>
  <view>
    <vh-navbar title="支付保证金" height="46" showBorder></vh-navbar>
    <view class="pt-140">
      <view class="flex-c-c">
        <text class="font-36 text-0 l-h-50">保证金¥</text>
        <text class="ml-08 font-wei-600 font-56 text-3 l-h-80">{{ earnest }}</text>
      </view>
      <view class="pt-20 ptb-00-plr-34">
        <view class="mt-20 font-28 text-3 l-h-40">1.委托保证金为委托人拍品报价的{{ earnestRatio }}%；</view>
        <view class="mt-20 font-28 text-3 l-h-40">2.若拍卖标的流拍、放弃上拍或卖家确认收货<text class="text-e80404">72小时内</text>，平台将<text class="text-e80404">退还保证金</text>至原支付账户；</view>
        <view class="mt-20 font-28 text-3 l-h-40">3.若委托人交易<text class="text-e80404">违规</text>（包含延期发货、成交不卖、虚假发货、交易争议等违规情形），<text class="text-e80404">保证金将扣除</text>。</view>
      </view>
      <view class="flex-c-c ptb-00-plr-52 mt-100">
        <button v-if="hideCount && earnestOrderInfo" class="vh-btn flex-1 flex-c-c mr-30 h-64 font-wei-500 font-28 text-e80404 bg-ffffff b-s-02-e80404 b-rad-32" @click="onQueryPayStatus">我已支付</button>
        <button class="vh-btn flex-1 flex-c-c h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="checked ? 'bg-e80404' : 'bg-fce4e3'" @click="onPay">立即支付</button>
      </view>
      <view class="flex-c-c mt-34" @click="checked = !checked">
        <image :src="ossIcon(`/auction/radio${checked ? '_h' : ''}_32.png`)" class="w-32 h-32"></image>
        <view class="ml-10 font-24 text-9 l-h-34">
          <text>我同意并遵守本平台</text>
          <text class="text-83a6cc" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/auctionEarnestRules`, $vhFrom)">《保证金规则》</text>
          <text class="text-83a6cc" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/auctionGiveStatement`, $vhFrom)">《送拍声明书》</text>
        </view>
      </view>
    </view>

    <AuctionEarnestPaySelectPopup
      ref="auctionEarnestPaySelectPopupRef"
      v-model="earnestPaySelectPopupVisible"
      :orderInfo="earnestOrderInfo"
      @paySuccess="onEarnestOrderPaySuccess"
      @pullPayFail="onEarnestOrderPullPayFail"
    />
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { MAuctionEarnestType } from '@/common/js/utils/mapperModel'
import auctionMyCreateDraftsUtil from '@/common/js/utils/auctionMyCreateDrafts'

export default {
  data: () => ({
    goodsId: 0,
    draftGoodsId: 0,
    earnest: '',
    earnestRatio: 10,
    checked: false,
    earnestPaySelectPopupVisible: false,
    earnestOrderInfo: null,
    earnestPayStatus: false,
    hideCount: 0
  }),
  computed: {
    ...mapState(['routeTable', 'agreementPrefix']),
  },
  onLoad (options) {
    const { goodsId, draftGoodsId = '', earnest, earnestRatio } = options
    this.goodsId = goodsId
    this.draftGoodsId = draftGoodsId
    this.earnest = earnest
    if (earnestRatio) this.earnestRatio = earnestRatio
    if (!this.goodsId || !this.earnest) {
      this.jump.redirectTo(this.routeTable.pgIndex)
      return
    }
  },
  onShow () {
    if (this.earnestOrderInfo) {
      this?.$refs?.auctionEarnestPaySelectPopupRef?.queryPayStatus()
    }
  },
  onHide () {
    this.hideCount++
  },
  methods: {
    ...mapMutations('personAuctionGoods', ['SET_ENTRUST_EARNEST_PAYSTATUS']),
    async onPay () {
      if (!this.checked) return
      this.feedback.loading()
      const res = await this.$u.api.getPersonAuctionOrderInfo({ id: this.goodsId })
      const { id, main_order_no } = res.data || {}
      let orderInfo = {}
      if (id) {
        orderInfo = { main_order_no }
      } else {
        this.feedback.loading()
        const res = await this.$u.api.createAuctionEarnestOrder({
          order_from: 2,
          type: MAuctionEarnestType.Entrust,
          goods_id: this.goodsId
        })
        orderInfo = res?.data
      }
      orderInfo.$paySuccessReturnUrl = `${this.routeTable.pHAuctionEntrustEarnestPaySuccess}?draftGoodsId=${this.draftGoodsId}`
      this.earnestOrderInfo = orderInfo
      this.earnestPaySelectPopupVisible = true
    },
    onEarnestOrderPaySuccess () {
      auctionMyCreateDraftsUtil.removeGoodsByDraftId(+this.draftGoodsId)
      this.SET_ENTRUST_EARNEST_PAYSTATUS(true)
      if (this.pages.getPageLength() <= 1) {
        this.jump.redirectTo(this.$routeTable.pHAuctionEntrustEarnestPaySuccess)
      } else {
        this.jump.navigateBack(1)
      }
    },
    onEarnestOrderPullPayFail () {
      this.earnestPaySelectPopupVisible = false
    },
    async onQueryPayStatus () {
      if (this.earnestOrderInfo) {
        const res = await this?.$refs?.auctionEarnestPaySelectPopupRef?.queryPayStatus()
        const { status = 0 } = res?.data || {}
        if (![1, 2, 3, 5].includes(status)) {
          uni.showModal({ title: '提示', content: '该订单暂未支付', showCancel: false, confirmText: '确定', })
        }
      }
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
