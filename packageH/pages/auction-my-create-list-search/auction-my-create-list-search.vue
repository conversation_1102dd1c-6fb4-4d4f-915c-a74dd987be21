<template>
  <view :class="['h-min-p100', list.length ? 'bg-f5f5f5' : '']">
    <vh-navbar title="我发布的" height="46" showBorder>
    </vh-navbar>
    <view class="flex-c-c h-112 bg-ffffff">
      <view class="flex-1 pl-24">
        <view class="flex-c-c pl-26 pr-18 h-68 bg-f7f7f7 b-rad-40">
          <image class="mr-08 w-36 h-36" :src="ossIcon('/comm/search_36.png')" />
					<input class="flex-1 h-p100 font-28 text-3" type="text" v-model="query.keywords" placeholder="请输入关键词" placeholder-style="color:#999;font-size:28rpx;" @confirm="load()"/>
					<image v-if="query.keywords" class="p-08 w-40 h-40" :src="ossIcon('/comm/del_gray.png')" @click="query.keywords = ''" />
        </view>
      </view>
      <view class="flex-c-c w-116 font-wei-500 font-28 text-6 l-h-40" @click="onSearch">搜索</view>
    </view>
    <AuctionCreatedGoodsList ref="auctionCreatedGoodsListRef" :list="list" />
  </view>
</template>

<script>
import { mapMutations } from 'vuex'
import listMixin from '@/common/js/mixins/listMixin'
import { MAuctionCreatedStatusMapper } from '@/common/js/utils/mapper'

export default {
  mixins: [listMixin],
  data: () => ({
    query: {
      keywords: '',
    },
  }),
  onLoad () {
    this.muAddressInfoState({}) // 进页面清空，防止持久化导致错乱
  },
  onShow () {
    if (this.login.isLogin()) {
      this.$nextTick(() => {
        this.$refs?.auctionCreatedGoodsListRef?.$refs?.auctionCreatedDeliverGoodsPopupRef?.initAddressInfo()
      })
    }
  },
  methods: {
    ...mapMutations(['muAddressInfoState']),
    async load (query) {
      const res = await this.$u.api.searchAuctionMyCreateList({ ...query })
      const { list = [] } = res?.data || {}
      list.forEach(item => {
        if (item.status === 2 && item.express_number) item.status = 9999
        Object.assign(item, MAuctionCreatedStatusMapper[item.status] || {})
      })
      this.list = query.page === 1 ? list : this.list.concat(list)
      this.changeBodyClassList()
      return res
    },
    onSearch () {
      if (!this.query.keywords) return
      this.reload()
    },
    changeBodyClassList () {
      this.$nextTick(() => {
        document?.body?.classList?.[this.list.length ? 'add' : 'remove']('bg-f5f5f5')
      })
    },
  },
  onReachBottom () {
    this.reachBottomLoad()
  },
}
</script>

<style>
  page {
    height: 100%;
	}
</style>

<style lang="scss" scoped>
</style>
