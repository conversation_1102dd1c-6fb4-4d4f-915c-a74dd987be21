<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar title='评价' :show-border="true">
			<view slot="right" class="mr-40">
				<u-button 
				:disabled="!canRelease"
				shape="circle" 
				:hair-line="false" 
				:ripple="true" 
				ripple-bg-color="#FFF"
				:custom-style="{width:'88rpx', height:'52rpx', backgroundColor: !canRelease ? '#FCE4E3' : '#E80404', fontSize:'20rpx', color:'#FFF', border:'none'}" 
				@click="release">发布</u-button>
			</view>
		</vh-navbar>
		
		<!-- 评价内容 -->
		<view class="mtb-00-mlr-44">
			<!-- 评分 -->
			<view class="flex-s-c pt-40 pb-32">
				<vh-image :loading-type="2" :src="goodsImage" :width="76" :height="76" />
				<view class="mtb-00-mlr-20 font-28 font-wei text-6">交易评价</view>
				<u-rate v-model="params.grade" :colors="colors" :size="40" inactive-icon="star"></u-rate>
				<view class="ml-20 font-24 text-6">{{ params.grade ? gradeText[params.grade - 1] : '暂未选择评分' }}</view>
			</view>
			
			<!-- 评价 -->
			<view class="p-rela bt-s-01-eeeeee">
				<view v-if="isShowPlaceholder && !params.content" class="p-abso top-24 d-flex a-center">
					<image :src="ossIcon(`/auction_order_evaluate/edit.png`)" class="mr-10 w-34 h-34" />
					<text class="font-24 text-9">快快评论，赢得兔头奖励哦～</text>
				</view>
				<textarea class="ptb-28-plr-00 w-662 h-200 font-28 font-wei text-6 l-h-40" v-model="params.content" @focus="onWeTextareaFocus" @blur="isShowPlaceholder = true"/>
				<view class="font-24 text-6">
					<text class="">已写</text>
					<text class="text-e80404">{{ params.content.length }}</text>
					<text class="">个字</text>
				</view>
			</view>
			
			<!-- 上传 -->
			<view class="">
				<AuctionUpload
					ref="uUpload" 
					:directory="'vinehoo/client/auctionOrderEvaluate/'" 
					:auto-upload="false" 
					:max-count="3" 
					@on-list-change="onListChange"
					@on-uploaded="onUploaded" 
				/>
				<view class="mt-20 font-24 text-6">您的评价内容经审核后会在商品页面显示</view>
			</view>
			
			<!-- 《酒云网内容发布规则》 -->
			<view class="bg-ffffff p-rela d-flex a-center pt-530 pb-28" @click="agreeCommAgreement()">
				<vh-select :checked="protocolList.includes('1')" />
				<text class="ml-10 font-24 text-6">我已阅读并接受</text>
				<text class="font-24 text-2b8cf7" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/ContentPubRules` ,$vhFrom)">《酒云网内容发布规则》</text>
			</view>
		</view>
		
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		data() {
			return {
				protocolList:[], //协议列表
				goodsImage: '', //商品图片
				params: {
					order_sn: '', //订单号
					grade: 5, //星星值
					content: '', //评论内容
					images: '', //评价图片(多个图片,分割)
				}, //评价参数
				gradeText: ['非常差', '差', '一般', '好', '非常好'], //分数文本
				colors: ['#FF9127', '#FF7729', '#FE5B0C', '#FF4A00', '#F40A0A'],
				isShowPlaceholder: true ,//是否显示占位符
				uploadFileList: [], //上传完成的文件列表
				hasAgreeProtocol: 0, //是否同意过协议
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable', 'agreementPrefix']),
			
			// 是否可以发布
			canRelease() {
				const { order_sn, grade, content } = this.params
				if( order_sn && grade && this.$u.trim(content, 'all') !== '' && this.uploadFileList.length ) return true
				return false
			}
		},
		
		onLoad(options) {
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) {
					if( options.orderNo && options.goodsImage ) {
						this.params.order_sn = options.orderNo
						this.goodsImage = options.goodsImage
						this.checkAgreement()
					}
				}
			})
		},
		
		methods: {
			// 检测用户是否勾选协议
			checkAgreement() {
				uni.getStorage({
					key:'protocolList',
					success: res => { this.protocolList = res.data },
					fail: async () => {
						try{
							let res = await this.$u.api.userSpecifiedData({field: 'protocol'})
							this.protocolList = res.data.protocol
							uni.setStorageSync('protocolList', this.protocolList )
							this.feedback.hideLoading()
						}catch(e){
							//TODO handle the exception
						}
					}
				})
			},
			
			// 同意/取消同意协议
			async agreeCommAgreement() {
				try{
					if(!this.protocolList.includes('1') && !this.hasAgreeProtocol) {
						this.feedback.loading()
						await this.$u.api.agreeProtocol({ xid: 1 })
						this.hasAgreeProtocol = 1
						this.checkAgreement()
					}else{
						if( !this.protocolList.includes('1') ) {
							this.protocolList = ['1']
						}else {
							this.protocolList = []
						}
					}
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 聚焦
			onWeTextareaFocus () {
				this.isShowPlaceholder = false
				// uni.createSelectorQuery().in(this).select('#weTextarea').boundingClientRect(data=>{
				// 	uni.createSelectorQuery().in(this).select('#outer-content').boundingClientRect(res=>{
				// 		uni.pageScrollTo({
				// 			scrollTop: data.top - res.top,
				// 			duration: 0
				// 		})
				// 	}).exec()
				// }).exec()
			},
			
			// 上传列表发生改变
			onListChange(list) {
				console.log('-----------------------上传列表发生改变', list)
				this.uploadFileList = list
				this.params.images = list.map(({ response }) => response).join()
			},
			
			// 所有图片上传成功
			onUploaded(list, index) {
				console.log('-------上传所有文件成功')
				console.log( list )
				console.log( index )
				this.feedback.toast({ title: '所有图片上传成功~'})
				this.params.images = list.map(({ response }) => response).join()
				this.evaluate()
			},
			
			// 发布
			release() {
				if( !this.protocolList.includes('1') ) return this.feedback.toast({title: '请勾选协议~'})
				this.$refs.uUpload.upload()
				// const uploadFileListLength = this.uploadFileList.length
				// if (uploadFileListLength) {
				// 	this.$refs.uUpload.upload()
				// } else {
				// 	this.evaluate()
				// }
			},
			
			// 评价
			async evaluate() {
				this.feedback.loading({title:'提交中...'})
				try {
					let res = await this.$u.api.auctionOrderEvaluate(this.params)
					this.feedback.toast({ title: '评价成功~'})
					setTimeout(() => { 
						if(this.$app) {
							this.jump.noticeAppOrderListRefresh(true)
						}else {
							this.jump.redirectTo(this.routeTable.pEMyOrder)
						}
					}, 1500 )
				}catch(e) {
					console.log(e)
				}
			}
		}
	}
</script>

<style>
</style>