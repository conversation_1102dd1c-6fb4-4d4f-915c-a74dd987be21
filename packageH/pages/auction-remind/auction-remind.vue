<template>
  <view>
    <vh-navbar :title="title" height="46">
      <view v-if="remindedList.length" slot="right" class="flex-c-c w-108 h-92 font-30 text-3" @click="onRightTextClick">{{ rightText }}</view>
    </vh-navbar>
    <view v-if="!loading" class="ptb-00-plr-24 o-hid" :class="isEdit ? 'pb-128' : 'pb-24'">
      <view v-if="list.length" :class="isEdit ? 't-trans-x-72' : ''">
        <view v-for="item in list" :key="item.time" class="p-rela mt-20 bg-ffffff b-rad-10">
          <view class="p-abso top-16 left-20 font-wei-500 font-28 text-6">{{ item.time }}</view>
          <view v-for="(item, index) in item.list" :key="index" class="p-rela ptb-00-plr-20" @click="onJump(item)">
            <view v-if="item.$remindStatus" class="p-abso top-50 left-0 t-trans-x-m100 flex-c-c w-96">
              <image :src="ossIcon(`/auction/radio${item.$checked ? '_h' : ''}_32.png`)" class="ptb-20-plr-16 w-32 h-32" @click.stop="item.$checked = !item.$checked" />
            </view>
            <view class="p-abso top-0 right-0 w-176 h-46">
              <image :src="ossIcon(item.$erStatusBg)" class="p-abso w-p100 h-p100" />
              <view class="p-rela flex-c-c w-p100 h-p100">
                <image :src="ossIcon('/auction/hammer_36_34.png')" class="w-36 h-34" />
                <text class="ml-10 font-24 l-h-34" :class="item.$erStatusTextClazz">{{ item.$erStatusText }}</text>
              </view>
            </view>
            <view v-show="index" class="h-02 bg-eeeeee"></view>
            <view class="d-flex j-sb pt-70 pb-24">
              <vh-image :loading-type="4" :src="item.product_img && item.product_img[0]" :width="152" :height="152" :border-radius="6" />
              <view class="w-490">
                <view class="h-68 font-wei-500 font-24 l-h-34 text-hidden-2" :class="item.$erTitleTextClazz">{{ item.title }}</view>
                <text class="mt-04 ptb-00-plr-08 font-22 l-h-32 b-rad-04" :class="item.$erTimeTextClazz">
                  <template v-if="item[item.$rTimeKeyValue]">{{ item[item.$rTimeKeyValue] }} {{ item.$erTimeSuffixText }}</template>
                  <template v-else>{{ item.$erTimeSuffixText }}</template>
                </text>
                <view class="flex-sb-c mt-04">
                  <view class="flex-c-c l-h-34">
                    <text class="font-24" :class="item.$erPricePrefixClazz">{{ item.pricePrefixText }}</text>
                    <text class="ml-06 font-32" :class="item.$erPriceClazz"><text class="font-24">¥</text>{{ item.final_auction_price === '0.00' ? item.price : item.final_auction_price }}</text>
                  </view>
                  <button class="vh-btn flex-c-c w-124 h-34 font-24 text-9 bg-ffffff b-rad-16 b-s-02-979797" @click.stop="onRemind(item)">{{ item.$remindStatus ? '取消提醒' : '添加提醒' }}</button>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <AuctionNone v-else :title="isFromMine && $app ? '' : '空空如也'" desc="添加开拍提醒，不错过任何美好～" :descClazz="isFromMine && $app ? 'mt-30' : 'mt-20'" class="pt-200" />
      <view v-if="remindedList.length && isEdit" class="p-fixed left-0 bottom-0 flex-sb-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-999" v-safeBeautyBottom="$safeBeautyBottom">
        <view class="flex-sb-c ml-24" @click="onCheckAllChange">
          <image :src="ossIcon(`/auction/radio${checkAll ? '_h' : ''}_32.png`)" class="ml-08 w-32 h-32" />
          <text class="ml-16 font-32 text-3">全选</text>
        </view>
        <button class="vh-btn flex-c-c mr-24 w-208 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="checkSome ? 'bg-e80404' : 'bg-fce4e3'" @click="onBatchCancel">删除提醒</button>
      </view>
    </view>

    <u-popup v-model="popupVisible" mode="center" width="540rpx" height="414rpx" border-radius="20">
      <view class="p-rela w-552 h-414">
        <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-552 h-414" />
        <view class="p-rela pt-84">
          <view class="font-wei-500 font-32 text-3 l-h-44 text-center">确认取消提醒嘛</view>
          <view class="mt-20 font-28 text-6 l-h-34 text-center">Hi，过这个村没这个店了哦！</view>
          <view class="flex-sb-c mt-84 ptb-00-plr-70">
            <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32" @click="onConfirm">确认</button>
            <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="popupVisible = false">取消</button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { MAuctionRemindOperation } from '@/common/js/utils/mapperModel'
import { MAuctionGoodsStatusObjMapper } from '@/common/js/utils/mapper'
import { mapState } from 'vuex'
import listMixin from '@/common/js/mixins/listMixin'

export default {
  name: 'auctionRemind', // 拍卖提醒页面
  mixins: [listMixin],
  data: () => ({
    loading: true,
    loginInfo: {},
    list: [],
    isEdit: false,
    popupVisible: false,
    currentItem: null,
    isFromMine: 0,
  }),
  computed: {
    ...mapState(['routeTable']),
    title ({ isFromMine }) {
      return isFromMine ? '拍卖提醒' : '我的提醒'
    },
    rightText ({ isEdit }) {
      return isEdit ? '完成' : '编辑'
    },
    remindedList ({ list }) {
      const goodsList = []
      list.forEach(item => {
        goodsList.push(...item.list)
      })
      return goodsList.filter(item => item.$remindStatus)
    },
    checkAll ({ remindedList }) {
      return !!remindedList.length && remindedList.every(item => item.$checked)
    },
    checkSome ({ remindedList }) {
      return remindedList.some(item => item.$checked)
    },
  },
  methods: {
    async load (query) {
      const { page, limit } = query
      const res = await this.$u.api.searchRemindAuctionGoodsList({ uid: this.loginInfo.uid, page, limit })
      const { list = [] } = res?.data || {}
      const checkAll = this.checkAll
      list.forEach(ditem => {
        ditem.list = ditem.list.map(item => Object.assign({}, MAuctionGoodsStatusObjMapper[item.onsale_status], item, { $remindStatus: true, $checked: checkAll }))
      })
      if (query.page === 1) {
        this.list = list
      } else {
        const { length, [length - 1]: originLastItem } = this.list
        const targetFirstItem = list[0]
        if (originLastItem.time === targetFirstItem.time) {
          originLastItem.list.push(...targetFirstItem.list)
          this.list.push(...list.slice(1))
        } else {
          this.list.push(...list)
        }
      }
      this.changeBodyClassList()
      return res
    },
    onRightTextClick () {
      this.isEdit = !this.isEdit
      this.list.forEach(ditem => {
        ditem.list.forEach(item => {
          item.$checked = false
        })
      })
    },
    onJump (item) {
      if (this.isEdit) return
      this.jump.navigateTo(`${this.routeTable.pHAuctionGoodsDetail}?id=${item.id}`)
    },
    async onRemind (item) {
      if (this.isEdit) return
      const { $remindStatus, id } = item
      if ($remindStatus) {
        this.popupVisible = true
        this.currentItem = item
      } else {
        const params = { uid: this.loginInfo.uid, goods_id: id, type: MAuctionRemindOperation.Add }
        await this.$u.api.editRemindAuctionGoods(params)
        this.feedback.toast()
        item.$remindStatus = true
      }
    },
    async onConfirm () {
      const params = { uid: this.loginInfo.uid, goods_id: this.currentItem.id, type: MAuctionRemindOperation.Delete }
      await this.$u.api.editRemindAuctionGoods(params)
      this.popupVisible = false
      this.currentItem.$remindStatus = false
    },
    onCheckAllChange () {
      const checkAll = this.checkAll
      this.list.forEach(ditem => {
        ditem.list.forEach(item => {
          item.$checked = !checkAll
        })
      })
    },
    async onBatchCancel () {
      if (!this.checkSome) return
      this.feedback.showModal({
        content:'确认删除吗？',
        confirm: () => {
          const goods_ids = this.list.map(ditem => ditem.list.filter(item => item.$remindStatus && item.$checked).map(item => item.id)).reduce((a, b) => a.concat(b), [])
          const params = { uid: this.loginInfo.uid, goods_id: goods_ids.join(), type: MAuctionRemindOperation.Delete }
          this.$u.api.editRemindAuctionGoods(params).then(() => {
            this.reload().then(() => {
              this.onChangeToScroll()
            }).finally(() => {
              this.feedback.toast()
            })
            this.isEdit = false
          })
        }
      })
    },
    changeBodyClassList () {
      this.$nextTick(() => {
        document?.body?.classList?.[this.list.length ? 'add' : 'remove']('bg-f5f5f5')
      })
    },
  },
  onLoad (options) {
    const { isFromMine = 0 } = options
    this.isFromMine = +isFromMine
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.loginInfo = uni.getStorageSync('loginInfo') || {}
      this.load().finally(() => {
        this.loading = false
      })
    })
  },
  onShow () {
    this.changeBodyClassList()
  },
  onPullDownRefresh() {
    if (this.isEdit) {
      uni.stopPullDownRefresh()
      return
    }
    this.pullDownRefresh()
  },
  onReachBottom () {
    this.reachBottomLoad()
  },
}
</script>

<style lang="scss" scoped>
</style>
