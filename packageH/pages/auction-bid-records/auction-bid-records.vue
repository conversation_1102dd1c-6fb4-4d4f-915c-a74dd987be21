<template>
  <view class="records">
    <vh-navbar title="拍卖记录" height="46" :background="{ background: 'transparent' }" back-icon-color="#FFF" titleColor="#fff" />
    <template v-if="!loading">
      <view class="records__header" :class="isAuctionAbort ? 'h-500' : 'h-484'">
        <view class="p-rela w-706 h-338 bg-ffffff" :class="{ 'mb-22': isAuctionAbort }">
          <image :src="ossIcon('/auction/crown_706_338.png')" class="w-p100 h-p100"></image>
          <view class="p-abso bottom-0 left-p-50 t-trans-x-m50 w-324 h-386 bg-ffffff"></view>
          <view class="p-abso top-0 w-p100 h-p100 text-center">
            <view class="p-rela mtb-00-mlr-auto flex-c-c w-136 h-136">
              <view class="p-rela flex-c-c w-p100 h-p100">
                <vh-image :loading-type="4" :src="!(+firstRecord.is_anonymous) ? firstRecord.avatar_image : 'https://images.vinehoo.com/avatars/rabbit.png'" :width="136" :height="136" shape="circle" />
                <image :src="ossIcon('/auction/circle_138.png')" class="p-abso w-138 h-138"></image>
              </view>
              <image :src="ossIcon(isAuctionAbort ? '/auction/abl_success_60_36.png' : '/auction/abl_lead_60_36.png')" class="p-abso w-60 h-36" :class="isAuctionAbort ? 'bottom-n-02 right-0' : 'top-0 left-n-12'"></image>
              <image v-if="isAuctionAbort" :src="ossIcon('/auction/crown_84_58.png')" class="p-abso top-n-44 w-84 h-58" />
            </view>
            <view class="flex-c-c mt-18 text-e80404">
              <text class="font-20 l-h-26"><text class="font-18">NO.</text>{{ firstRecord.code }}</text>
              <view class="ml-06 w-max-410 font-24 l-h-34 text-hidden">{{ firstRecord.nickname }}</view>
            </view>
            <view class="mt-08 font-28 text-9 l-h-28">（地区：{{ firstRecord.province_name || '未知' }}）</view>
            <view class="mt-14 font-32 text-e80404 l-h-44">¥{{ firstRecord.bid_price }}</view>
            <view class="font-24 text-9 l-h-34">{{ firstRecord.create_time | date('mm.dd hh:MM:ss') }}</view>
          </view>
        </view>
        <view v-if="!isAuctionAbort" class="w-706 h-08 bg-ffffff">
          <view class="records__devide"></view>
        </view>
      </view>
      <view class="records__content">
        <AuctionBidList :list="otherRecords" :uid="loginInfo.uid" :onsaleStatus="onsale_status" />
      </view>
    </template>
  </view>
</template>

<script>
import { MAuctionGoodsStatus } from '@/common/js/utils/mapperModel'

export default {
  name: 'auctionBidRecords', // 拍品出价记录
  data: () => ({
    MAuctionGoodsStatus,
    loading: true,
    id: '',
    onsale_status: '',
    loginInfo: {},
    list: []
  }),
  computed: {
    firstRecord ({ list }) {
      return list[0]
    },
    otherRecords ({ list }) {
      return list.slice(1)
    },
    isAuctionAbort ({ onsale_status }) {
      return MAuctionGoodsStatus.AuctionAbort === onsale_status
    }
  },
  methods: {
    async load () {
      const res = await this.$u.api.getAuctionBidRecords({ id: this.id })
      const list = res?.data?.list || []
      this.list = Object.freeze(list)
    }
  },
  onLoad (options) {
    this.loginInfo = uni.getStorageSync('loginInfo') || {}
    this.id = options.id
    this.onsale_status = +options.onsale_status
    this.load().finally(() => {
      this.loading = false
    })
  }
}
</script>

<style>
  page {
    height: 100%;
  }
</style>

<style lang="scss" scoped>
  .records {
    height: 100%;
    display: flex;
    flex-direction: column;

    &__header {
      flex-shrink: 0;
      @include flex-col(flex-end, center);
      margin-top: -46px;
      background: linear-gradient(139deg, #D4B053 0%, #D03935 100%);
    }

    &__devide {
      padding: 0 26rpx;
      height: 100%;
      background: linear-gradient(270deg, rgba(232,4,4,0) 0%, rgba(232,4,4,0.6) 33%, rgba(232,4,4,0.76) 52%, rgba(232,4,4,0.6) 67%, rgba(232,4,4,0) 100%);
      opacity: 0.5;
    }

    &__content {
      flex: 1;
      @include flex-row(flex-start, flex-start);
      overflow: auto;

      &::before,
      &::after {
        content: '';
        display: block;
        flex: 1;
        height: 100%;
      }

      &::before {
        background: linear-gradient(180deg, #D28848 0%, #EFE3D8 83%, rgba(255,255,255,0) 100%);
      }

      &::after {
        background: linear-gradient(180deg, #D13935 0%, #F4D0CF 65%, #FFFFFF 100%);
      }

      > view {
        padding: 0 26rpx;
        @include size(706rpx, 100%);
        overflow: auto;
      }
    }
  }
</style>
