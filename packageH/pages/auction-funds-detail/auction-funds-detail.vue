<template>
  <view class="h-min-vh-100 bg-f5f5f5">
    <vh-navbar title="收支详情" height="46" :background="{ background: '#e80404' }" back-icon-color="#FFF" title-color="#FFF"></vh-navbar>
    <view class="ptb-20-plr-24">
      <view class="flex-c-c flex-column h-538 bg-ffffff b-rad-10">
        <vh-image :loading-type="4" :src="fundsDetail.product_img" :width="300" :height="300" :border-radius="6" />
        <view class="mt-20 w-514 font-wei-500 font-24 text-3 l-h-34 text-center text-hidden">{{ fundsDetail.title || fundsDetail.goods_title }}</view>
        <view class="flex-c-c mt-16">
          <text class="font-28 text-6">
            <template v-if="fundsDetail.$isFromFundsList">{{ fundsDetail.type | toText('MAuctionFundsOrderTypeText2') }}</template>
            <template v-else>保证金</template>
          </text>
          <text class="ml-20 font-wei-600 font-36 text-3">{{ fundsDetail.source_type === MAuctionFundsType.Income ? '+' : '-' }}{{ fundsDetail.payment_amount }}</text>
        </view>
      </view>
      <view class="mt-20 ptb-00-plr-24 font-28 text-6 bg-ffffff b-rad-10">
        <template v-if="fundsDetail.type === MAuctionFundsOrderType.Earnest">
          <view class="flex-sb-c h-104 bb-s-02-eeeeee">
            <text>当前状态</text>
            <text class="font-wei-500 font-24 text-e80404">{{ fundsDetail.status_cn }}
              <!-- <template v-if="fundsDetail.$isFromFundsList">{{ fundsDetail.status | toText('MAuctionFundsStatusText') }}</template>
              <template v-else>{{ fundsDetail.status | toText('MAuctionEarnestStatusText') }}</template> -->
            </text>
          </view>
          <view v-if="fundsDetail.source_type === MAuctionFundsType.Income" class="flex-sb-c h-104 bb-s-02-eeeeee">
            <text>退款时间</text>
            <text class="font-wei-500 font-24 text-e80404">{{ fundsDetail.created_time }}</text>
          </view>
        </template>
        <template v-if="fundsDetail.type === MAuctionFundsOrderType.Earnest || fundsDetail.source_type === MAuctionFundsType.Expend">
          <view class="flex-sb-c h-104 bb-s-02-eeeeee">
            <text>支付状态</text>
            <text class="font-wei-500 font-24" :class="fundsDetail.type === MAuctionFundsOrderType.AuctionOrder ? 'text-e80404' : 'text-3'">支付成功</text>
          </view>
          <view class="flex-sb-c h-104 bb-s-02-eeeeee">
            <text>支付时间</text>
            <text class="font-wei-500 font-24 text-3">{{ fundsDetail.created_time }}</text>
          </view>
          <view class="flex-sb-c h-104 bb-s-02-eeeeee">
            <text>支付方式</text>
            <text class="font-wei-500 font-24 text-3">{{ fundsDetail.payment_method | toText('MPaymentMethodText') }}</text>
          </view>
        </template>
        <template v-else>
          <view class="flex-sb-c h-104 bb-s-02-eeeeee">
            <text>收款状态</text>
            <text class="font-wei-500 font-24 text-e80404"></text>
          </view>
          <view class="flex-sb-c h-104 bb-s-02-eeeeee">
            <text>收款时间</text>
            <text class="font-wei-500 font-24 text-3">{{ fundsDetail.created_time }}</text>
          </view>
          <view class="flex-sb-c h-104 bb-s-02-eeeeee">
            <text>收款账户</text>
            <text class="font-wei-500 font-24 text-e"></text>
          </view>
        </template>
        <view class="flex-sb-c h-104">
          <text>交易编号</text>
          <view class="flex-c-c" @click.stop="copy.copyText(fundsDetail.tradeno)">
            <text class="font-wei-500 font-24 text-3">{{ fundsDetail.tradeno }}</text>
            <button class="vh-btn flex-c-c ml-10 w-56 h-30 font-wei-500 font-18 text-3 l-h-26 bg-eeeeee b-rad-15">复制</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionFundsOrderType, MAuctionFundsType } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'

export default {
  name: 'auctionIncomeList', // 拍卖收支详情
  data: () => ({
    MAuctionFundsOrderType,
    MAuctionFundsType,
  }),
  computed: {
    ...mapState(['routeTable']),
    ...mapState('auctionFunds', ['fundsDetail'])
  },
  onLoad () {
    if (!this.fundsDetail) {
      this.jump.redirectTo(this.routeTable.pHAuctionFundsList)
      return
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
