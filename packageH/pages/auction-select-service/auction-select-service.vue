<template>
	<view class="content pb-124">
		<!-- 导航栏 -->
		<vh-navbar title='选择服务' />
		
		<!-- 选择服务类型 -->
		<view class="bg-ffffff b-rad-10 mt-20 mr-24 ml-24 ptb-00-plr-24">
			<view class="pt-32 font-32 font-wei text-6">选择服务类型</view>
			<!-- 仅退款 -->
			<view class="flex-sb-c" v-for="( item, index ) in serviceTypeList" :key="index" @click="params.service_type = item.type">
				<image class="w-48 h-48" :src="ossIcon(`/auction_select_service/ser_type${index}.png`)" mode="aspectFill" />
				<view class="flex-1 flex-sb-c bb-s-01-eeeeee ml-24 ptb-32-plr-00">
					<view class="font-28 font-wei text-3">{{ item.name }}</view>
					<vh-select :checked="params.service_type === item.type"/>
				</view>
			</view>
		</view>
		
		<!-- 退款信息 -->
		<view class="bg-ffffff b-rad-10 mt-20 mr-24 ml-24 ptb-32-plr-24">
			<view class="flex-sb-c">
				<view class="font-32 font-wei text-6">退款金额</view>
				<view class="font-32 font-wei text-e80404">
					<text class="font-24">¥</text>
					<text class="font-32">{{ orderDetailInfo.payment_amount }}</text>
				</view>
			</view>
			<view class="flex-sb-c mt-28">
				<view class="font-32 font-wei text-6">退款方式</view>
				<view class="font-32 font-wei text-3">支持原路返回</view>
			</view>
		</view>
		
		<!-- 退款原因 -->
		<view class="bg-ffffff b-rad-10 mt-20 mr-24 ml-24 ptb-00-plr-24">
			<view class="pt-32 font-32 font-wei text-6">退款原因</view>
			<view class="flex-sb-c bb-d-01-eeeeee ptb-32-plr-00" v-for="( item, index ) in refundReasonList" :key="index" @click="params.refund_reason = item.content"> 
				<view class="font-28 font-wei text-3">{{ item.content }}</view>
				<vh-select :checked="params.refund_reason === item.content" />
			</view>
		</view>
		
		<!-- 补充描述和凭证 -->
		<view v-if="orderDetailInfo.status === 2" class="bg-ffffff b-rad-10 mt-20 mr-24 ml-24 ptb-32-plr-24">
			<view class="">
				<text class="font-32 font-wei text-3">补充描述和凭证</text>
				<text class="font-28 text-9">（选填）</text>
			</view>
			
			<view class="bg-f7f7f7 b-rad-04 mt-32 p-20">
				<view class="">
					<textarea v-model="params.describe" class="w-p100 h-150 font-28 text-3" placeholder="请输入补充描述" placeholder-style="color:#999;font-size:28rpx;"/>
				</view>
				
				<view class="mt-20">
					<AuctionUpload
						ref="uUpload" 
						:plate="1"
						:directory="'vinehoo/client/auctionSelectService/'" 
						:auto-upload="false" 
						:max-count="3" 
						@on-list-change="onListChange"
						@on-uploaded="onUploaded" />
				</view>
			</view>
		</view>
		
		<!-- 弹框 -->
		<view class="">
			<!-- 提交成功弹框 -->
			<u-modal v-model="showSubmitSuccessMod" :show-title="false" content="" :width="490"
			 confirm-text="知道了" :confirm-style="{fontSize:'28rpx', color:'#999'}" @confirm="jumpBuyerAfterSaleDetail">
				<view class="pt-86 pb-64">
					<view class="d-flex j-center a-center">
						<image class="w-264 h-184" :src="ossIcon(`/comm/succ_red.png`)" mode="aspectFill" />
					</view>
					
					<view class="d-flex flex-column j-center a-center mt-30 l-h-44">
						<text class="font-28 text-3">提交成功</text>
						<text class="font-28 text-3">我们将在48小时内处理</text>
					</view>
				</view>
			</u-modal>
		</view>
		
		<!-- 底部按钮 -->
		<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff flex-c-c b-sh-00021200-022">
			<!-- 提交 -->
			<u-button 
			:disabled="!canSubmit"
			shape="circle" 
			:hair-line="false" 
			:ripple="true" ripple-bg-color="#FFF"
			:custom-style="{width:'646rpx', height:'64rpx', backgroundColor: !canSubmit ? '#FCE4E3' : '#E80404', fontSize:'28rpx', fontWeight:'bold', color:'#FFF', border:'none'}" 
			@click="submit">提交</u-button>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		data() {
			return {
				serviceTypeList: [{ name: '仅退款', type: 0 }, { name: '退货退款', type: 1 }], //服务类型列表
				refundReasonList: [{ content: '商品破损' }, { content: '商品与描述不符' }, { content: '其他' }], //退款原因列表
				params: {
					order_no: '', //订单号
					service_type: '', //服务类型：0-仅退款 1-退货退款
					refund_reason: '', //退款原因
					describe: '', //描述
					voucher: '', //凭证
				}, //参数
				uploadFileList: [], //上传完成的文件列表
				showSubmitSuccessMod: false,//是否提交成功弹框
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
			...mapState('auction', ['orderDetailInfo']),
			
			// 是否可以提交
			canSubmit() {
				const { order_no, service_type, refund_reason, describe, voucher } = this.params
				if( order_no && service_type !== '' && refund_reason ) return true
				return false
			}
		},
		
		onLoad() {
			this.login.isLoginV3(this.$vhFrom).then(isLogin => {
				if (isLogin) this.init()
			})
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations('auction', ['muOrderDetailInfo']),
			
			// 初始化板块
			init() {
				const { order_no, status } = this.orderDetailInfo
				if( status === 1 ) {
					this.serviceTypeList = this.serviceTypeList.slice(0,1)
					this.refundReasonList = [{ content: '卖家长时间不发货' }] //退款原因列表
					this.params.service_type = 0
					this.params.refund_reason = this.refundReasonList[0].content
				}else if( status === 2 ) {
					
				} 
				this.params.order_no = order_no
			},
			
			// 上传列表发生改变
			onListChange(list) {
				console.log('-----------------------上传列表发生改变')
				this.uploadFileList = list
				this.params.voucher = list.map(({ response }) => response).join()
			},
			
			// 所有上传成功
			onUploaded(list, index) {
				console.log('-------上传所有文件成功')
				console.log(list)
				this.feedback.toast({ title: '所有图片上传成功~'})
				this.params.voucher = list.map(({ response }) => response).join()
				this.afterSaleApply()
			},
			
			// 提交
			submit() {
				const uploadFileListLength = this.uploadFileList.length
				if (uploadFileListLength) {
					this.$refs.uUpload.upload()
				} else {
					this.afterSaleApply()
				}
			},
			
			// 申请售后
			async afterSaleApply() {
				this.feedback.loading({title:'提交中...'})
				try {
					await this.$u.api.auctionAfterSaleApply(this.params)
					let res = await this.$u.api.auctionOrderDetail({ order_no: this.orderDetailInfo.order_no })
					this.muOrderDetailInfo(res.data)
					this.showSubmitSuccessMod = true
				}catch(e) {
					console.log('------------------------我到了catch分支')
					console.log(e)
				}
			},
			
			// 跳转买家售后详情
			jumpBuyerAfterSaleDetail() {
				if(!this.orderDetailInfo.refund_order_no) {
					this.feedback.toast({ title: '未获取到退款单号，请重新提交~' })
					setTimeout(()=> { this.jump.navigateBack() }, 1500)
				}
				this.jump.redirectTo(`${this.routeTable.pHAuctionBuyerAfterSaleDetail}?refundOrderNo=${this.orderDetailInfo.refund_order_no}`)
			}
			
		}
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>