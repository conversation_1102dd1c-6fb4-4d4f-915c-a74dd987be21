<template>
  <view>
    <vh-navbar title="收款账户" height="46" showBorder />
    <view v-if="!loading">
      <view v-if="isWxProcess" class="ptb-00-plr-32">
        <view class="flex-sb-c h-108 bb-s-02-eeeeee" @click="onWxBind">
          <view class="flex-c-c">
            <image :src="ossIcon('/auction/wx_54.png')" class="w-54 h-54" />
            <text class="ml-24 font-wei-500 font-32 text-3 l-h-44">微信账户</text>
          </view>
          <view class="flex-c-c">
            <text class="font-32 text-6 l-h-44">{{ bindStatus ? '换绑' : '未绑定' }}</text>
            <image :src="ossIcon('/about/arrow_r_12_20.png')" class="ml-10 w-12 h-20" />
          </view>
        </view>
      </view>
      <view v-else class="p-24 font-32 text-6 l-h-44 text-center">请在微信客户端打开链接{{ href }}</view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { WX_APPID_PROD } from '@/common/js/fun/constant'
import wx from 'weixin-js-sdk'

export default {
  name: 'auctionPayeeAccount', // 收款账户
  data: () => ({
    loading: true,
    isWxProcess: false,
    bindStatus: 0,
    code: '',
  }),
  computed: {
    ...mapState(['routeTable']),
    href () {
      return window?.location?.href
    },
  },
  methods: {
    async load () {
      const res = await this.$u.api.getWxOABindStatus()
      this.bindStatus = res?.data?.is_follow
    },
    async onWxBind () {
      await this.$u.api.bindWxOA({ code: this.code })
      this.feedback.toast()
      this.bindStatus = 1
    },
  },
  onLoad (options) {
    const ua = window.navigator.userAgent.toLowerCase()
		this.isWxProcess = /micromessenger/.test(ua)
    if (this.isWxProcess) {
      this.login.isLoginV3(this.$vhFrom).then(isLogin => {
        if (!isLogin) return
        const { code } = options
        if (this.isDev) {
          if (!code) {
            location.href = `https://activity.vinehoo.com/activities-v3/RedirectFetchWxCode?appid=${WX_APPID_PROD}&redirectUrl=${window.location.href.split("#")[0]}`
            return
          } else {
            this.code = code
          }
        } else {
          if (!code) {
            location.href =
              'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
              WX_APPID_PROD +
              '&redirect_uri=' +
              encodeURIComponent(window.location.href) +
              `&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect`
            return
          } else {
            this.code = code
          }
        }
        this.load().finally(() => {
          this.loading = false
        })
      })
    } else {
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
