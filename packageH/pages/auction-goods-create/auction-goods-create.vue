<template>
  <view>
    <vh-navbar title="发布拍品" height="46">
      <view slot="right" class="d-flex">
        <button class="vh-btn flex-c-c mr-10 w-108 h-44 font-wei-500 font-20 text-9 bg-ffffff b-s-01-979797-r b-rad-23" @click="onSaveDraft">存草稿</button>
        <!-- <button class="vh-btn flex-c-c mr-24 w-88 h-44 font-wei-500 font-20 text-ffffff b-rad-23" :class="publishBtnDisabled ? 'bg-666666' : 'bg-e80404'" @click="onPublish">发布</button> -->
      </view>
    </vh-navbar>

    <view class="pb-200">
      <view class="ptb-20-plr-24 bg-f5f5f5">
        <view class="ptb-28-plr-24 bg-ffffff b-rad-10">
          <textarea v-model="goods.title" placeholder="请在此输入您的产品名称" placeholder-style="text-d8d8d8" :maxlength="88" auto-height class="w-p100 h-min-110 font-28 text-3 l-h-40" />
          <view class="mt-10 font-24 text-9 l-h-34"><text :class="goods.title ? 'text-e80404' : ''">{{ goods.title.length }}</text>/88</view>
        </view>
        
        <view class="mt-20 ptb-28-plr-24 bg-ffffff b-rad-10">
          <textarea v-model="goods.detail" placeholder="请描述您的拍品，买家关心的购买渠道来源" placeholder-style="text-d8d8d8" :maxlength="1000" auto-height class="w-p100 h-min-160 font-28 text-3 l-h-40" />
          <view class="mt-10 font-24 text-9 l-h-34"><text :class="goods.detail ? 'text-e80404' : ''">{{ goods.detail.length }}</text>/1000</view>
        </view>

        <view class="mt-04">
          <AuctionUpload
            :plate="93"
            directory="vinehoo/client/auctionGoodsCreate/"
            :max-count="9"
            :fileList="productImgFileList"
            @on-list-change="onListChange($event, 'product_img')"
            @on-uploaded="onUploaded($event, 'product_img')"
          />
        </view>
      </view>

      <view class="ptb-00-plr-24">
        <view class="ptb-40-plr-00 bb-s-02-ececec">
          <view class="font-wei-500 font-32 text-6 l-h-44">选择分类</view>
          <view class="flex-s-c mt-20">
            <button
              v-for="(item, index) in MAuctionGoodsCategoryText"
              :key="index"
              class="vh-btn flex-c-c mr-10 w-144 h-46 font-wei-500 font-24 b-rad-23"
              :class="category.category_id === item.value ? 'text-ffffff bg-e80404' : 'text-6 bg-ffffff b-s-01-666666'"
              @click="onCategoryClick(item)"
            >{{ item.text }}</button>
          </view>
          <view class="pt-08">
            <AuctionCGCategoryProperty required name="名称" :value="category.product_name" placeholder="请输入您的产品名称吧～" @click="initcgcpEditPopupParams('产品名称', 'product_name')" />
            <AuctionCGCategoryProperty v-if="isWhiteSpiritsCategory" required name="品牌" :value="category.brand" placeholder="请选择您的产品品牌" @click="initcgcpEditPopupParams('品牌', 'brand')" />
            <AuctionCGCategoryProperty v-if="!isWhiteSpiritsCategory" required name="产国" :value="category.country" placeholder="请选择您的原产国" @click="initcgcpEditPopupParams('原产国', 'country')" />
            <AuctionCGCategoryProperty v-if="isWhiteSpiritsCategory" required name="香型" :value="category.odor_type" placeholder="请选择香型" @click="initcgcpEditPopupParams('香型', 'odor_type')" />
            <AuctionCGCategoryProperty required name="容量" :value="category.net_content ? `${category.net_content}ml` : ''" placeholder="您的产品容量是？" @click="initcgcpEditPopupParams('容量', 'net_content')" />
            <AuctionCGCategoryProperty required name="度数" :value="category.alcoholic_strength ? `${category.alcoholic_strength}%vol` : ''" placeholder="酒精度数是？" @click="initcgcpEditPopupParams('度数', 'alcoholic_strength')" />
            <AuctionCGCategoryProperty required name="年份" :value="category.years" placeholder="2008年？" @click="initcgcpEditPopupParams('年份', 'years')" />
            <AuctionCGCategoryProperty v-if="isWhiteSpiritsCategory" required name="生产年份" :value="category.production_year ? `${category.production_year}年` : ''" placeholder="2008年？" @click="initcgcpEditPopupParams('生产年份', 'production_year')" />
          </view>
          <view class="mt-32">
            <view class="flex-s-c">
              <view class="flex-c-c" @click="isShowMore = !isShowMore">
                <text class="mr-12 font-wei-500 font-24 text-6 l-h-34">更多参数</text>
                <image :src="ossIcon('/auction/arrow_d_20_12.png')" class="w-20 h-12" :class="isShowMore ? 't-ro-n-180 tran-2' : 'tran-2'" />
              </view>
              <text class="ml-20 font-24 text-9">更多详细参数能使拍品出价更高</text>
            </view>
            <view v-if="isShowMore" class="pt-08">
              <AuctionCGCategoryProperty name="产地" :value="category.place" placeholder="藏品产地～" @click="initcgcpEditPopupParams('产地', 'place')" />
              <AuctionCGCategoryProperty v-if="!isWhiteSpiritsCategory" name="日期" :value="category.filling_time" placeholder="罐装日期" @click="initcgcpEditPopupParams('罐装日期', 'filling_time')" />
              <AuctionCGCategoryProperty name="包装" :value="category.packaging" placeholder="包装方式" @click="initcgcpEditPopupParams('包装方式', 'packaging')" />
              <AuctionCGCategoryProperty v-if="isWineCategory" name="品种" :value="category.grape_variety" placeholder="请选择葡萄品种？" @click="initcgcpEditPopupParams('葡萄品种', 'grape_variety')" />
              <AuctionCGCategoryProperty name="储存" :value="category.saving_mode" placeholder="储存方式是？" @click="initcgcpEditPopupParams('储存方式', 'saving_mode')" />
              <AuctionCGCategoryProperty v-if="!isWhiteSpiritsCategory" name="评分" :value="category.grade" placeholder="获奖评分" @click="initcgcpEditPopupParams('获奖评分', 'grade')" />
            </view>
          </view>
        </view>
        <view class="ptb-32-plr-00">
          <view class="flex-sb-c" @click="pricePopupVisible = true">
            <text class="font-wei-500 font-32 text-6 l-h-44">¥ 卖家报价</text>
            <view class="flex-c-c">
              <text v-if="priceParams.quote" class="font-wei-500 font-36 text-e80404 l-h-34"><text class="font-24">¥{{ ' ' }}</text>{{ priceParams.quote }}</text>
              <text v-else class="font-24 text-9 l-h-34">请填写价格</text>
              <image :src="ossIcon('/about/arrow_r_12_20.png')" class="ml-10 w-12 h-20" />
            </view>
          </view>
          <view class="mt-10 font-24 text-9 l-h-34">将影响之后的拍卖设置，请合理填写</view>
          <view v-if="priceParams.quote">
            <view class="flex-sb-e pr-48 pb-12 h-84 bb-s-02-eeeeee">
              <text class="font-28 text-3 l-h-40">起拍价</text>
              <text class="font-wei-500 font-28 text-3 l-h-34"><text class="font-24">¥</text>{{ ' ' }}{{ priceParams.price }}</text>
            </view>
            <view class="flex-sb-e pr-48 pb-12 h-84 bb-s-02-eeeeee">
              <text class="font-28 text-3 l-h-40">加价幅度</text>
              <text class="font-wei-500 font-28 text-3 l-h-34"><text class="font-24">¥</text>{{ ' ' }}{{ priceParams.markup }}</text>
            </view>
            <view class="flex-sb-e pr-48 pb-12 h-84 bb-s-02-eeeeee">
              <text class="font-28 text-3 l-h-40">参拍保证金</text>
              <text class="font-wei-500 font-28 text-3 l-h-34"><text class="font-24">¥</text>{{ ' ' }}{{ priceParams.joinAuctionEarnest }}</text>
            </view>
            <view class="flex-sb-e pr-48 pb-12 h-84 bb-s-02-eeeeee">
              <text class="font-28 text-3 l-h-40">委托保证金</text>
              <text class="font-wei-500 font-28 text-3 l-h-34"><text class="font-24">¥</text>{{ ' ' }}{{ priceParams.earnest }}</text>
            </view>
            <!-- <view class="flex-s-c mt-32">
              <image v-if="goods.is_surcer_ship" :src="ossIcon('/auction/checkbox_h_28.png')" class="w-28 h-28" @click="goods.is_surcer_ship = 0" />
              <image v-else :src="ossIcon('/auction/checkbox_28.png')" class="w-28 h-28" @click="goods.is_surcer_ship = 1" />
              <text class="ml-22 font-28 text-3 l-h-40">支持鉴定后发货</text>
              <image :src="ossIcon('/auction/ask_30_32.png')" class="ml-10 w-30 h-32" @click="surveryPopupVisible = true" />
            </view> -->
            <view class="mt-20">
              <view class="font-24 l-h-34">
                <text class="text-3">其他资料</text>
                <text class="ml-20 text-9">(资料齐全可提高审核通过率)</text>
              </view>
              <view class="flex-sb-c mt-20">
                <AuctionUpload
                  :plate="92"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="surcerImgFileList"
                  @on-list-change="onListChange($event, 'is_surcer_img')"
                  @on-uploaded="onUploaded($event, 'is_surcer_img')"
                />
                <AuctionUpload
                  :plate="91"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="cusCleImgFileList"
                  @on-list-change="onListChange($event, 'cus_cle_img')"
                  @on-uploaded="onUploaded($event, 'cus_cle_img')"
                />
              </view>
              <view class="flex-sb-c mt-20">
                <AuctionUpload
                  :plate="90"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="sicImgFileList"
                  @on-list-change="onListChange($event, 'is_sic_img')"
                  @on-uploaded="onUploaded($event, 'is_sic_img')"
                />
                <AuctionUpload
                  :plate="89"
                  directory="vinehoo/client/auctionGoodsCreate/"
                  :max-count="1"
                  :fileList="propurImgFileList"
                  @on-list-change="onListChange($event, 'is_propur_img')"
                  @on-uploaded="onUploaded($event, 'is_propur_img')"
                />
              </view>
            </view>
            <view class="mt-40">
              <view class="font-wei-500 font-32 text-3 l-h-44">发货地址</view>
              <view class="mt-20 h-188" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=9`)">
                <view v-if="addressInfo.address" class="flex-sb-c ptb-00-plr-24 h-p100 bg-f9f9f9 b-rad-10">
                  <view>
                    <view>
                      <text class="font-wei-500 font-32 text-3">{{ addressInfo.consignee }}</text>
                      <text class="ml-20 font-28 text-9">{{ addressInfo.consignee_phone }}</text>
                    </view>
                    <view class="flex-s-c">
                      <view v-if="addressInfo.is_default" class="flex-c-c mr-08 w-50 h-24 font-wei-500 font-18 text-ffffff bg-e80404 b-rad-04">默认</view>
                      <view v-if="addressInfo.label" class="flex-c-c mr-08 w-50 h-24 font-wei-500 font-18 text-ffffff bg-2e7bff b-rad-04">{{ addressInfo.label }}</view>
                      <text class="font-24 text-3 l-h-34">{{ addressInfo.province_name }} {{ addressInfo.city_name }} {{ addressInfo.town_name }}</text>
                    </view>
                    <view class="font-24 text-3 l-h-34">{{ addressInfo.address }}</view>
                  </view>
                  <image :src="ossIcon('/after_sale_detail/arrow_right_12x20.png')" class="w-12 h-20">
                </view>
                <view v-else class="h-p100">
                  <image :src="ossIcon('/order-confirm/add_bg.png')" class="p-abso w-702 h-188" />
                  <view class="p-rela flex-c-c flex-column h-p100">
                    <image :src="ossIcon('/order-confirm/add_ico.png')" class="w-84 h-84" />
                    <text class="mt-14 font-28 text-3 l-h-40">新建收货地址</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="p-fixed left-0 bottom-0 w-p100 pt-28 h-174 bg-ffffff b-sh-00021200-022 z-9999">
        <button class="vh-btn flex-c-c mtb-00-mlr-auto w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="publishBtnDisabled ? 'bg-fce4e3' : 'bg-e80404'" @click="onPublish">发布</button>
        <view class="flex-c-c mt-20">
          <vh-check :checked="publishChecked" :width="26" :height="26" @click="publishChecked = !publishChecked" />
          <view class="ml-10 font-24 text-9">
            <text @click="publishChecked = !publishChecked">阅读并接受</text>
            <text class="text-83a6cc" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/auctionRules`, $vhFrom)">《拍卖规则》</text>
            <text class="text-83a6cc" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/auctionEntrustContract`, $vhFrom)">《委托拍卖协议》</text>
          </view>
        </view>
      </view>
    </view>

    <AuctionCGCPEditPopup ref="auctioncgcpEditPopupRef" :params="cgcpEditPopupParams" :category="category" />

    <AuctionCGPricePopup v-model="pricePopupVisible" :priceParams="priceParams" />

    <u-popup v-model="surveryPopupVisible" mode="center" width="552rpx" height="414rpx" border-radius="20">
      <view class="p-rela w-552 h-414">
        <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-552 h-414" />
        <view class="p-rela pt-52">
          <view class="font-wei-600 font-32 text-3 l-h-44 text-center">鉴定后发货</view>
          <view class="mt-24 ptb-00-plr-38 font-wei-500 font-26 text-6 l-h-48 text-center">商品将发货至平台指定第三方权威检测机构，鉴别通过后再由检测机构发往买家，力求消费者到手商品100%正品</view>
          <view class="flex-c-c mt-48">
            <button class="vh-btn flex-c-c w-200 h-60 font-wei-500 font-28 text-e80404 bg-ffffff" @click="surveryPopupVisible = false">知道了</button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { MAuctionGoodsCategory, MAuctionRNType } from '@/common/js/utils/mapperModel'
import { MAuctionGoodsCategoryText } from '@/common/js/utils/mapper'
import { FILL_TIME_YEAR_LIST, WINE_GRADE_LIST } from '@/common/js/utils/auctionGoodsConfig'
import { mapState, mapMutations, mapActions } from 'vuex'
import computedNumber from '@/common/js/utils/computedNumber'
import getJoinAuctionEarnest from '@/common/js/utils/getJoinAuctionEarnest'

export default {
  name: 'auctionGoodsCreate', // 创建拍品
  data: () => ({
    MAuctionGoodsCategory,
    MAuctionGoodsCategoryText,
    goods: {
      title: '',
      detail: '',
      product_img: '',
      is_surcer_ship: 0,
      is_surcer_img: '',
      cus_cle_img: '',
      is_sic_img: '',
      is_propur_img: '',
    },
    category: {
      category_id: '',
      category_name: '',
      product_name: '',
      brand: '',
      country: '',
      odor_type: '',
      net_content: '',
      alcoholic_strength: '',
      years: '',
      production_year: '',
      place: '',
      filling_time: '',
      packaging: '',
      grape_variety: '',
      saving_mode: '',
      grade: '',
      product_name__input: '',
      brand__input: '',
      country__input: '',
      odor_type__input: '',
      net_content__input: '',
      alcoholic_strength__input: '',
      years__input: '',
      production_year__input: '',
      place__input: '',
      filling_time__input: [FILL_TIME_YEAR_LIST[0], 1],
      packaging__input: '',
      grape_variety__input: '',
      saving_mode__input: '',
      grade__input: [0, WINE_GRADE_LIST[0]],
    },
    priceParams: {
      quote: '', // 卖家报价
      price: '', // 起拍价
      markup: '', // 加价幅度
      joinAuctionEarnest: '', // 参拍保证金
      earnest: '', // 平台保证金
      serviceCharge: 0, // 服务费
      quoteInput: '',
      priceInput: '',
      markupInput: '',
      earnestInput: '',
      serviceChargeInput: 0,
    },
    cgcpEditPopupParams: {
      visible: false,
      title: '',
      keyValue: '',
      keyValueInput: '',
      searchValue: '',
      searchValueInput: '',
    },
    pricePopupVisible: false,
    surveryPopupVisible: false,
    isShowMore: false,
    addressInfo: {},
    productImgFileList: [],
    surcerImgFileList: [],
    cusCleImgFileList: [],
    sicImgFileList: [],
    propurImgFileList: [],
    goodsId: '',
    draftGoodsId: '',
    publishChecked: false,
    isForbidden: false,
    isFromDrafts: '',
  }),
  computed: {
    ...mapState(['routeTable', 'ossPrefix', 'addressInfoState', 'userInfo', 'agreementPrefix']),
    ...mapState('auctionGoods', ['auctionGoods', 'entrustEarnestPayStatus']),
    publishBtnDisabled ({ goods, category, priceParams, addressInfo, publishChecked }) {
      const { title, detail, product_img } = goods
      const { category_id, product_name, brand, country, odor_type, net_content, alcoholic_strength, years, production_year } = category
      const { quote, price, markup } = priceParams 
      return !(title && detail && product_img && category_id !== '' && product_name && (category_id === MAuctionGoodsCategory.WhiteSpirits ? brand : true) && (category_id !== MAuctionGoodsCategory.WhiteSpirits ? country : odor_type && production_year) && net_content && alcoholic_strength && years && quote && price && markup && addressInfo.address && publishChecked)
    },
    isWineCategory ({ category }) {
      return category.category_id === MAuctionGoodsCategory.Wine
    },
    isWhiteSpiritsCategory ({ category }) {
      return category.category_id === MAuctionGoodsCategory.WhiteSpirits
    },
  },
  methods: {
    ...mapMutations(['muPayInfo', 'muAddressInfoState']),
    ...mapMutations('auctionGoods', ['SET_AUCTION_GOODS', 'SET_ENTRUST_EARNEST_PAYSTATUS']),
    ...mapActions(['getUserInfo']),
    async init () {
      const isLogin = await this.login.isLoginV3(this.$vhFrom)
      if (!isLogin) return
      await this.loadAuctionUserInfo()
      this.initData()
      this.loadAddressInfo()
      this.getUserInfo()
    },
    async loadAuctionUserInfo () {
      const res = await this.$u.api.getAuctionUserInfo()
      const { ac_type = 0, status = 1 } = res?.data?.info || {}
      this.isForbidden = !status
      if (this.isForbidden) {
        this.feedback.toast({ title: '账号异常，请联系客服' })
        return
      }
      const isRealName = MAuctionRNType.NotRN !== ac_type
      if (!isRealName) {
        this.jump.redirectTo(this.routeTable.pHAuctionRealName)
        return
      }
    },
    initData () {
      if (!this.auctionGoods) return
      const { category_arr, quote, province_id, province_name, city_id, city_name, district_id, district_name, address, consignee, consignee_phone } = this.auctionGoods
      const goodsKeyValueList = ['title', 'detail', 'product_img', 'is_surcer_ship', 'is_surcer_img', 'cus_cle_img', 'is_sic_img', 'is_propur_img']
      this.draftGoodsId = this.auctionGoods.id
      goodsKeyValueList.forEach(keyValue => {
        this.goods[keyValue] = this.auctionGoods[keyValue]
      })
      const categoryKeyValueList = ['category_id', 'category_name', 'product_name', 'brand', 'country', 'odor_type', 'net_content', 'alcoholic_strength', 'years', 'production_year', 'place', 'filling_time', 'packaging', 'grape_variety', 'saving_mode', 'grade']
      categoryKeyValueList.forEach(keyValue => {
        let value = category_arr[keyValue]
        if ('category_id' !== keyValue) value = value || '' // category_id有0值
        this.category[keyValue] = value
        if (!['category_id', 'category_name'].includes(keyValue)) {
          const keyValueInput = `${keyValue}__input`
          if (['filling_time', 'grade'].includes(keyValue)) {
            this.category[keyValueInput] = value.includes('-') ? value.split('-') : this.$options.data().category[keyValueInput]
          } else {
            this.category[keyValueInput] = value
          }
        }
      })
      const priceParamsKeyValueList = ['quote', 'price', 'markup']
      priceParamsKeyValueList.forEach(keyValue => {
        this.priceParams[keyValue] = this.auctionGoods[keyValue]
        this.priceParams[`${keyValue}Input`] = this.auctionGoods[keyValue]
      })
      if (quote) {
        const joinAuctionEarnest = getJoinAuctionEarnest(quote)
        const earnest = computedNumber(+quote, '*', 0.1).result
        Object.assign(this.priceParams, { earnest, earnestInput: earnest, joinAuctionEarnest })
      }
      this.addressInfo = { province_id, province_name, city_id, city_name, town_id: district_id, town_name: district_name, address, consignee, consignee_phone }
      const getFileList = (str) => {
        if (!str) return []
        let list = []
        if (Array.isArray(str)) {
          list = str
        } else {
          list = str.split(',')
        }
        return list.map(item => ({
          fileType:'image',
          url: item.includes('http') ? item : `${this.ossPrefix}${item}`,
          response: item.includes('http') ? item : `${this.ossPrefix}${item}`,
          progress: 100,
          error: false,
          file: {}
        }))
      }
      const { product_img, is_surcer_img, cus_cle_img, is_sic_img, is_propur_img } = this.auctionGoods
      this.productImgFileList = getFileList(product_img)
      this.surcerImgFileList = getFileList(is_surcer_img)
      this.cusCleImgFileList = getFileList(cus_cle_img)
      this.sicImgFileList = getFileList(is_sic_img)
      this.propurImgFileList = getFileList(is_propur_img)
      this.SET_AUCTION_GOODS(null)
    },
    async loadAddressInfo () {
      if (Object.keys(this.addressInfoState).length) this.addressInfo = this.addressInfoState
      if (this.addressInfo.address) return
      const res = await this.$u.api.addressList()
      const list = res?.data?.list || []
      if (list.length) {
        const findDefaultItem = list.find(item => item.is_default)
        if (findDefaultItem) {
          this.addressInfo = findDefaultItem
        } else {
          this.addressInfo = list[0]
        }
      }
    },
    onCategoryClick (item) {
      const { value, text } = item
      this.category = this.$options.data().category
      this.category.category_id = value
      this.category.category_name = text
    },
    initcgcpEditPopupParams (title, keyValue) {
      if (this.category.category_id === '') {
        this.feedback.toast({ title: '请先选择分类' })
        return
      }
      const keyValueInput = `${keyValue}__input`
      if (keyValue === 'grade' && !this.category[keyValueInput][0]) {
        this.category[keyValueInput].splice(0, 1, this.$refs.auctioncgcpEditPopupRef.wineGradeOrgList[0])
      }
      this.cgcpEditPopupParams = { visible: true, title, keyValue, keyValueInput: `${keyValue}__input`, searchValue: '', searchValueInput: '' }
    },
    onListChange (list, keyValue) {
      this.goods[keyValue] = list.map(({ response, url }) => response || url.replace(this.ossPrefix, '')).join()
    },
    onUploaded (list, keyValue) {
      this.goods[keyValue] = list.map(({ response }) => response).join()
      uni.hideLoading()
    },
    initParams () {
      console.log('goods', this.goods)
      const { title, detail, product_img, is_surcer_ship, is_surcer_img, cus_cle_img, is_sic_img, is_propur_img } = this.goods
      const { category_id, category_name, product_name, brand, country, odor_type, net_content, alcoholic_strength, years, production_year, place, filling_time, packaging, grape_variety, saving_mode, grade } = this.category
      const category_arr = { category_id, category_name, product_name, brand, country, odor_type, net_content, alcoholic_strength, years, production_year, place, filling_time, packaging, grape_variety, saving_mode, grade }
      const { quote, price, markup } = this.priceParams
      const { province_id, province_name, city_id, city_name, town_id, town_name, address, consignee, consignee_phone } = this.addressInfo
      const params = {
        source: 1,
        title,
        detail,
        product_img,
        category: category_id,
        is_surcer_ship, is_surcer_img, cus_cle_img, is_sic_img, is_propur_img,
        category_arr,
        // quote: 0.01, price: 0.01, markup: 0.01,
        quote, price, markup,
        province_id,
        province_name,
        city_id,
        city_name,
        district_id: town_id,
        district_name: town_name,
        address,
        consignee,
        consignee_phone,
        uid: this.userInfo.uid,
        uname: this.userInfo.nickname
      }
      console.log('params', params)
      return params
    },
    onSaveDraft () {
      if (!this.goods.title) {
        this.feedback.toast({ title: '请填写产品名称' })
        return
      }
      const auctionGoodsDrafts = uni.getStorageSync('auctionGoodsDrafts') || []
      const params = this.initParams()
      const findIndex = auctionGoodsDrafts.findIndex(item => item.id === this.draftGoodsId)
      if (findIndex === -1) {
        if (auctionGoodsDrafts.length >= 50) {
          auctionGoodsDrafts.pop()
        }
        params.id = Date.now()
        auctionGoodsDrafts.unshift(params)
      } else {
        auctionGoodsDrafts.splice(findIndex, 1, params)
      }
      uni.setStorageSync('auctionGoodsDrafts', auctionGoodsDrafts)
      this.feedback.toast({ title: '保存成功' })
      this.jumpDrafts()
    },
    async onPublish () {
      if (this.publishBtnDisabled) return
      if (this.isForbidden) {
        this.feedback.toast({ title: '账号异常，请联系客服' })
        return
      }
      const params = this.initParams()
      const res = await this.$u.api.createAuctionGoods(params)
      this.goodsId = res?.data || 0
      if (!this.goodsId) {
        this.feedback.toast({ title: '创建拍品失败，请稍候再试' })
        return
      }
      this.jump.navigateTo(`${this.routeTable.pHAuctionGoodsCreateStep}?goodsId=${this.goodsId}&draftGoodsId=${this.draftGoodsId}&earnest=${this.priceParams.earnest}`)
    },
    jumpDrafts () {
      this.isFromDrafts ? this.jump.navigateBack() : this.jump.redirectTo(this.routeTable.pHAuctionGoodsDrafts)
    },
  },
  onLoad (options) {
    this.isFromDrafts = options.isFromDrafts
    this.muAddressInfoState({}) // 进页面清空，防止持久化导致错乱
    this.SET_ENTRUST_EARNEST_PAYSTATUS(false)
    this.init()
  },
  onShow () {
    this.loadAddressInfo()
    if (this.goodsId) {
      if (this.entrustEarnestPayStatus) {
        this.jump.redirectTo(this.routeTable.pHAuctionMyGoodsList)
      } else {
        this.onSaveDraft()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
