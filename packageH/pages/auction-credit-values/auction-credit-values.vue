<template>
  <view>
    <vh-navbar title="信用值" height="46" showBorder>
      <view slot="right" class="flex-c-c p-24 font-30 text-6" @click="jump.navigateTo(routeTable.pHAuctionCreditValuesIntro)">说明</view>
    </vh-navbar>
    <view v-if="!loading">
      <view class="p-rela mtb-24-mlr-auto w-686 h-240">
        <image :src="ossIcon('/auction/cv_card_686_240.png')" class="p-abso top-0 w-p100 h-p100" />
        <view class="p-rela pt-48 h-p100">
          <view class="font-wei-600 font-84 text-ffffff l-h-84 text-center">{{ creditValues }}</view>
          <view class="p-abso bottom-46 w-p100 flex-c-c">
            <text class="font-24 text-ffffff l-h-40">我的信用分</text>
            <image :src="ossIcon('/auction/credit_values_h_18_24.png')" class="ml-06 w-18 h-24" />
          </view>
        </view>
      </view>
      <view class="auction-cv__tabs flex-c-c mt-16">
        <u-tabs :list="tabsList" :current="currentTabIndex" height="64" font-size="28" active-color="#333" inactive-color="#666" bar-width="36" bar-height="8" gutter="62" @change="onTabChange" />
      </view>
      <view class="h-02 bg-eeeeee"></view>
      <view v-if="list.length" class="mt-28 ptb-00-plr-48">
        <view v-for="(item, index) in list" :key="index" class="flex-sb-c ptb-32-plr-00 bb-s-02-eeeeee">
          <view>
            <view class="font-30 text-3 l-h-40">{{ item.reason }}</view>
            <view class="mt-10 font-24 text-9 l-h-40">{{ item.created_time }}</view>
          </view>
          <view class="flex-c-c">
            <text class="font-32" :class="item.operation === MAuctionCreditValuesOperation.Add ? 'text-e80404' : 'text-3'">{{ item.operation === MAuctionCreditValuesOperation.Add ? '+' : '-' }}{{ item.score }}</text>
            <image :src="ossIcon(`/auction/credit_values${item.operation === MAuctionCreditValuesOperation.Add ? '_h' : ''}_18_24.png`)" class="ml-06 w-18 h-24" />
          </view>
        </view>
      </view>
      <AuctionNone v-else class="pt-140" img="/auction/none_442_400.png" imgClazz="w-442 h-400" desc="暂无记录～" descClazz="" />
    </view>
  </view>
</template>

<script>
import { MAuctionCreditValuesTab, MAuctionCreditValuesOperation } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'

export default {
  name: 'auctionCreditValues', // 信用值
  data: () => ({
    MAuctionCreditValuesOperation,
    loading: true,
    tabsList: [
      { name: '全部', type: MAuctionCreditValuesTab.All },
      { name: '增加记录', type: MAuctionCreditValuesTab.Add },
      { name: '扣分记录', type: MAuctionCreditValuesTab.Sub }
    ],
    currentTabIndex: 0,
    loginInfo: {},
    creditValues: 0,
    query: { page: 1, limit: 10 },
    totalPage: 0,
    list: []
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    load () {
      Promise.all([
        this.loadCreditValues(),
        this.loadList()
      ]).finally(() => {
        this.loading = false
      })
    },
    async loadCreditValues () {
      const res = await this.$u.api.userSpecifiedData({field: 'auction_credit_score'})
      this.creditValues = res?.data?.auction_credit_score || 0
    },
    async loadList () {
      const { type } = this.tabsList[this.currentTabIndex]
      const res = await this.$u.api.searchAuctionCreditValuesList({
        ...this.query,
        uid: this.loginInfo.uid,
        type
      })
      const { list = [], total = 0 } = res?.data || {}
      this.list = this.query.page === 1 ? list : this.list.concat(list)
      this.totalPage = Math.ceil(total / this.query.limit)
    },
    onTabChange (index) {
      this.currentTabIndex = index
      this.query = this.$options.data().query
      this.loadList()
    }
  },
  onLoad () {
    this.login.isLoginV3(this.$vhFrom).then(isLogin => {
      if (!isLogin) return
      this.loginInfo = uni.getStorageSync('loginInfo') || {}
      this.load()
    })
  },
  onReachBottom () {
    if (this.query.page === this.totalPage || !this.totalPage) return
    this.query.page++
    this.loadList()
  }
}
</script>

<style lang="scss" scoped>
  .auction-cv {
    &__tabs {
      ::v-deep {
        .u-tab {
          &-item {
            min-width: 112rpx;
            box-sizing: content-box;
            font-weight: 600 !important;
            vertical-align: top;
          }

          &-bar {
            bottom: auto;
            background: linear-gradient(214deg, #FF8383 0%, #E70000 100%);
          }
        }
      }
    }
  }
</style>
