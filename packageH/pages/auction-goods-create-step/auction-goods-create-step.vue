<template>
  <view>
    <vh-navbar title="保证金规则" height="46" showBorder></vh-navbar>
    <view class="pt-140">
      <view class="flex-c-c">
        <text class="font-36 text-0 l-h-50">保证金¥</text>
        <text class="ml-08 font-wei-600 font-56 text-3 l-h-80">{{ earnest }}</text>
      </view>
      <view class="pt-20 ptb-00-plr-34">
        <view v-for="(item, index) in entrustEarnestTerms" :key="index" class="mt-20 font-28 text-3 l-h-40">{{ index + 1 }}.<text v-html="item" /></view>
      </view>
      <view class="flex-c-c mt-100">
        <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="checked ? 'bg-e80404' : 'bg-fce4e3'" @click="onPay">支付</button>
      </view>
      <view class="flex-c-c mt-34">
        <vh-check :checked="checked" :width="32" :height="32" @click="checked = !checked" />
        <view class="ml-10 font-24 text-9 l-h-34">
          <text @click="checked = !checked">我同意并遵守本平台</text>
          <text class="text-83a6cc" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/auctionEarnestRules`, $vhFrom)">《保证金规则》</text>
          <text class="text-83a6cc" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/auctionGiveStatement`, $vhFrom)">《送拍声明书》</text>
        </view>
      </view>
    </view>

    <AuctionEarnestPaySelectPopup ref="auctionEarnestPaySelectPopupRef" v-model="earnestPaySelectPopupVisible" :orderInfo="earnestOrderInfo" @paySuccess="onEarnestOrderPaySuccess" @payFail="onEarnestOrderPayFail" />
    <AuctionEarnestPaySuccessPopup v-model="earnestPaySuccessPopupVisible" @confirm="jump.navigateBack()" />
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { MAuctionEarnestType } from '@/common/js/utils/mapperModel'

export default {
  name: 'auctionGoodsCreateStep', // 创建拍品下一步
  data: () => ({
    goodsId: 0,
    draftGoodsId: 0,
    earnest: '',
    entrustEarnestTerms: [
      '委托保证金为委托人拍品报价的10%；',
      '若拍卖标的流拍或订单已完成（前提为不存在交易纠纷），拍卖结束后<text class="text-e80404">72小时内</text>，平台将<text class="text-e80404">退还保证金</text>至原支付账户；',
      '如委托人交易<text class="text-e80404">违规</text>（包含延期发货、成交不卖、虚假发货、交易争议等违规情形），<text class="text-e80404">保证金将扣除</text>。'
    ],
    checked: false,
    earnestPaySelectPopupVisible: false,
    earnestPaySuccessPopupVisible: false,
    earnestOrderInfo: null,
    earnestPayStatus: false,
  }),
  computed: {
    ...mapState(['routeTable', 'agreementPrefix']),
  },
  methods: {
    ...mapMutations('auctionGoods', ['SET_ENTRUST_EARNEST_PAYSTATUS']),
    async onPay () {
      if (!this.checked) return
      let orderFrom = 2
      const { $android, $ios } = this
      if ($android) orderFrom = 1
      else if ($ios) orderFrom = 0
      const res = await this.$u.api.createAuctionEarnestOrder({
        order_from: orderFrom,
        type: MAuctionEarnestType.Entrust,
        goods_id: this.goodsId
      })
      const data = res?.data || {}
      data.$paySuccessReturnUrl = `${this.routeTable.pHAuctionMyGoodsList}?draftGoodsId=${this.draftGoodsId}`
      this.earnestOrderInfo = data
      this.earnestPaySelectPopupVisible = true
    },
    onEarnestOrderPaySuccess () {
      this.earnestPaySelectPopupVisible = false
      this.earnestPaySuccessPopupVisible = true
      const auctionGoodsDrafts = uni.getStorageSync('auctionGoodsDrafts') || []
      const findIndex = auctionGoodsDrafts.findIndex(item => item.id === +this.draftGoodsId)
      if (findIndex !== -1) {
        auctionGoodsDrafts.splice(findIndex, 1)
        uni.setStorageSync('auctionGoodsDrafts', auctionGoodsDrafts)
      }
      this.SET_ENTRUST_EARNEST_PAYSTATUS(true)
    },
    onEarnestOrderPayFail () {
      this.earnestPaySelectPopupVisible = false
      this.jump.navigateBack()
    },
  },
  onLoad (options) {
    const { goodsId, draftGoodsId, earnest } = options
    this.goodsId = goodsId
    this.draftGoodsId = draftGoodsId
    this.earnest = earnest
    if (!this.goodsId || !this.earnest) {
      this.jump.redirectTo(this.routeTable.pHAuctionGoodsCreate)
      return
    }
  },
  onShow () {
    if (this.earnestOrderInfo) {
      this.$refs.auctionEarnestPaySelectPopupRef.queryPayStatus()
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
