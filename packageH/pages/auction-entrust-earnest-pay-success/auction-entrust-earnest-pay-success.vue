<template>
  <view>
    <vh-navbar height="46" backIconColor="#fff" :background="{ background: '#d30808' }"></vh-navbar>
    <view class="mt-n-20">
      <view class="flex-c-c">
        <image :src="ossIcon('/auction/pay_success_264_184.png')" class="w-264 h-184"></image>
      </view>
      <view class="mt-20 font-wei-500 font-36 text-ffffff l-h-50 text-center">支付成功</view>
      <view class="mt-10 ptb-00-plr-76 font-28 text-ffffff l-h-40 text-center">
        您的拍品已提交审核，审核结果将会以短信方式通知，请耐心等待！
      </view>
      <view class="flex-sb-c mt-100 ptb-00-plr-72">
        <button
          class="vh-btn flex-c-c w-278 h-64 font-wei-500 font-28 text-ffffff bg-transp b-s-02-ffffff b-rad-32"
          @click="jump.redirectTo($routeTable.pHAuctionMyCreateList)"
        >我发布的</button>
        <button
          class="vh-btn flex-c-c w-278 h-64 font-wei-500 font-28 text-d60d0d bg-ffffff b-rad-32"
          @click="jump.redirectTo('/packageH/pages/auction-index-new/auction-index-new')"
        >返回首页</button>
      </view>
    </view>
  </view>
</template>

<script>
import auctionMyCreateDraftsUtil from '@/common/js/utils/auctionMyCreateDrafts'

export default {
  onLoad (options) {
    const { draftGoodsId = '' } = options
    auctionMyCreateDraftsUtil.removeGoodsByDraftId(+draftGoodsId)
  }
}
</script>

<style>
  page {
    background: #d30808;
  }
</style>

<style lang="scss" scoped>
</style>
