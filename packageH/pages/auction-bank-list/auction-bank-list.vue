<template>
  <view>
    <vh-navbar title="收款账户" height="46" />
    <view v-if="!loading">
      <view v-if="list.length" class="auction-bl__list ptb-00-plr-32 pt-60 pb-128">
        <u-swipe-action
          v-for="(item, index) in list"
          :key="index"
          :index="index"
          btn-width="140"
          :options="[{ text: '删除', style: { backgroundColor: '#FF9127' } }]"
          @click="onDelete(item)"
        >
          <view class="p-rela h-250 b-rad-10" style="background: #1963B8;">
            <image :src="ossIcon('/auction/bank_bg_686_250.png')" class="p-abso w-686 h-250" />
            <view class="p-rela flex-s-c pt-70 pl-32">
              <image :src="ossIcon('/auction/bank_icon_64.png')" class="w-64 h-64" />
              <view class="ml-20">
                <view class="font-36 text-ffffff l-h-50">{{ item.account_holder }}</view>
                <view class="font-28 text-ffffff l-h-40">{{ item.card_no }}</view>
              </view>
            </view>
            <image :src="ossIcon('/auction/bank_default_110_70.png')" style="top: 40rpx; right: -8rpx;" class="p-abso w-110 h-70" />
          </view>
        </u-swipe-action>
      </view>
      <view v-else class="pt-200">
        <view class="flex-c-c">
          <image :src="ossIcon('/auction/bank_none_440_390.png')" class="w-440 h-390" />
        </view>
        <view class="mt-20 font-28 text-9 l-h-40 text-center">您还没有添加银行卡</view>
      </view>
      <view class="p-fixed left-0 bottom-0 flex-c-c w-p100 h-128 bg-ffffff z-9999">
        <view style="background: rgba(246,246,246,0.87);" class="flex-c-c w-702 h-84 b-rad-10" @click="jump.navigateTo(routeTable.pHAuctionBankAdd)">
          <view class="p-rela">
            <view class="w-24 h-04 bg-979797"></view>
            <view class="p-abso top-0 w-24 h-04 bg-979797 t-ro-90"></view>
          </view>
          <text class="ml-18 font-wei-500 font-36 text-6 l-h-50">增加银行卡</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'auctionBankList', // 收款账户
  data: () => ({
    loading: true,
    query: {
      page: 1,
      limit: 100
    },
    list: [],
    totalPage: 0
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    async load () {
      const res = await this.$u.api.searchAuctionBankList(this.query)
      const { list = [], total = 0 } = res?.data || {}
      this.list = this.query.page === 1 ? list : this.list.concat(list)
      this.totalPage = Math.ceil(total / this.query.limit)
    },
    onDelete (item) {
      console.log('item', item)
      this.$u.api.removeAuctionBank({ id: item.id }).then(() => {
        this.feedback.toast({ title: '删除成功' })
        this.load()
      })
    }
  },
  onShow () {
    this.load().finally(() => {
      this.loading = false
    })
  }
}
</script>

<style lang="scss" scoped>
  .auction-bl {
    &__list {
      > uni-view {
        margin-bottom: 20rpx;
      }
    }
  }
</style>
