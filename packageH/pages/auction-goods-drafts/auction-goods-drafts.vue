<template>
  <view class="h-min-vh-100 bg-f5f5f5">
    <vh-navbar title="草稿箱" height="46" />
    <view class="ptb-00-plr-24">
      <view class="flex-sb-c ptb-00-plr-20 h-80">
        <text class="font-wei-500 font-28 text-3 l-h-40">{{ list.length }}个草稿待发布</text>
        <text class="font-28 text-9 l-h-40">保存最近50个</text>
      </view>
      <view class="bg-ffffff">
        <view v-for="(item, index) in list" :key="index">
          <view v-if="index" class="mtb-00-mlr-20 h-02 bg-eeeeee"></view>
          <view class="p-20">
            <view class="flex-s-c">
              <vh-image v-if="item.product_img" :loading-type="4" :src="item.product_img && item.product_img.split(',')[0]" :width="120" :height="120" :border-radius="6" />
              <image v-else :src="ossIcon('/auction/none_img_120.png')" class="w-120 h-120" />
              <view class="ml-20 w-522">
                <view class="font-wei-500 font-28 text-3 l-h-40">{{ item.title }}</view>
                <view class="mt-10 font-24 text-6 l-h-34">完善信息，立即发布啦～</view>
              </view>
            </view>
            <view class="flex-e-c mt-24">
              <button class="vh-btn flex-c-c w-106 h-64 font-wei-500 font-28 text-3 bg-ffffff b-s-02-d8d8d8 b-rad-32" @click="onDelete(index)">删除</button>
              <button class="vh-btn flex-c-c ml-16 w-106 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="onEdit(item)">编辑</button>
            </view>
          </view>
        </view>
      </view>
      <view class="mt-80 mb-20 font-wei-500 font-24 text-6 l-h-34 text-center">哎呀，没有啦～</view>
    </view>
    <u-popup v-model="popupVisible" mode="center" width="552rpx" height="414rpx" border-radius="20">
      <view class="p-rela w-552 h-414">
        <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-552 h-414" />
        <view class="p-rela pt-86">
          <view class="font-wei-500 font-32 text-3 l-h-44 text-center">确认删除这个草稿？</view>
          <view class="mt-20 font-28 text-3 l-h-34 text-center">这可是兔子君辛苦编辑的呢</view>
          <view class="flex-c-c mt-80">
            <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32" @click="onConfirm">确认</button>
            <button class="vh-btn flex-c-c ml-60 w-180 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="popupVisible = false">取消</button>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'auctionGoodsDrafts', // 拍品草稿箱
  data: () => ({
    list: [],
    currentIndex: 0,
    popupVisible: false
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    ...mapMutations('auctionGoods', ['SET_AUCTION_GOODS']),
    onDelete (index) {
      this.currentIndex = index
      this.popupVisible = true
    },
    onEdit (item) {
      this.SET_AUCTION_GOODS(item)
      this.jump.navigateTo(`${this.routeTable.pHAuctionGoodsCreate}?isFromDrafts=1`)
    },
    onConfirm () {
      this.list.splice(this.currentIndex, 1)
      uni.setStorageSync('auctionGoodsDrafts', this.list)
      this.popupVisible = false
    }
  },
  onShow () {
    this.list = uni.getStorageSync('auctionGoodsDrafts') || []
  }
}
</script>

<style lang="scss" scoped>
</style>
