<template>
	<view class="content">
		<!-- 导航栏 -->
		<vh-navbar title="物流详情" :title-bold="true" />
		
		<!-- 物流轨迹 -->
		<view class="">
			<!-- 拥有物流轨迹信息 -->
			<view v-if="logisticData.traces && logisticData.traces.length" class="">
				<!-- 订单状态 -->
				<view class="pt-24">
					<view class="bg-ffffff ml-24 mr-24 b-rad-10">
						<view class="d-flex a-center pt-24 pb-24 ml-24 mr-24 bb-s-01-f8f8f8">
							<vh-image :loading-type="2" :src="logisticsInfo.image" :width="50" :height="50" :border-radius="4" />
							<text class="ml-20 font-28 text-3 font-wei l-h-40">{{ logisticData.state_text }}</text>
						</view>
						
						<view class="d-flex a-center pt-32 pb-32 ml-24 mr-24 bb-s-01-f8f8f8" @click="copy.copyText(logisticData.logistic_code)">
							<text class="font-28 text-3 font-wei l-h-40">物流单号</text>
							<text class="ml-40 font-24 text-3 l-h-36">{{logisticData.logistic_code}}</text>
							<text class="bg-f5f5f5 ml-12 ptb-02-plr-12 b-rad-14 font-16 text-3">复制</text>
						</view>
						
						<view class="d-flex a-center pt-32 pb-32 ml-24 mr-24">
							<text class="font-28 text-3 font-wei l-h-40">预计送达</text>
							<text class="ml-40 font-24 text-3 l-h-36">{{logisticData.arrival_time ? logisticData.arrival_time : '暂无信息'}}</text>
						</view>
					</view>
				</view>
				
				<!-- 时间轴 -->
				<view class="pb-72">
					<view class="bg-ffffff mt-20 ml-24 mr-24 b-rad-10 ptb-32-plr-24 d-flex a-center">
						<view class="text-ffffff">-</view>
						<view class="">
							<u-time-line>
							    <view class="" v-for="(item, index) in logisticData.traces" :key="index">
							    	<u-time-line-item nodeTop="10">
							    		<template v-slot:node>
											<view v-if="index == 0" class="w-26 h-26 bg-fce4e3 d-flex j-center a-center b-rad-p50">
												<view class="w-14 h-14 bg-e80404 b-rad-p50" />
											</view>
											
											<view v-else class="w-14 h-14 bg-cccccc b-rad-p50" />
							    		</template>
							    		<template v-slot:content>
							    			<view class="ml-12">
							    				<view :class="index == 0 ? 'text-e80404' : 'text-9'">
							    					<!-- <text class="font-28 font-wei l-h-40">已签收</text> -->
							    					<text class="font-24 l-h-40">{{item.ftime}}</text>
							    				</view>
							    				<view class="mt-10 font-24 l-h-40" :class="index == 0 ? 'text-3' : 'text-9'">{{item.context}}</view>
							    			</view>
							    		</template>
							    	</u-time-line-item>
							    </view>
							
							</u-time-line>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 暂无物流轨迹信息 -->
			<vh-empty v-else :padding-top="260" :padding-bottom="820" :image-src="ossIcon(`/empty/emp_log.png`)" :text-bottom="0" text="暂无物流信息~" />
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	
	export default{
		name:"logistics-detail",
		
		data(){
			return{
				logisticData: {}, //物流数据
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState('auction', ['logisticsInfo']),

			hasLogisticsInfo({ logisticsInfo }) {
				return !!(logisticsInfo && Object.keys(logisticsInfo).length)
			}
		},
		
		onLoad() {
			if (!this.hasLogisticsInfo) return
			this.getLogisticsDetails()
		},

		onShow() {
			if (!this.hasLogisticsInfo) {
				this.jump.reLaunch('/')
				return
			}
		},
		
		methods: {
			// 获取物流详情
			async getLogisticsDetails() {
				const { image, logisticCode, expressType, phone, address } = this.logisticsInfo
				let res = await this.$u.api.logisticsDetails({ logisticCode, expressType, phone, address })
				this.logisticData = res.data
			}
		}
	}
</script>

<style>
	@import "@/common/css/page.css";
</style>
