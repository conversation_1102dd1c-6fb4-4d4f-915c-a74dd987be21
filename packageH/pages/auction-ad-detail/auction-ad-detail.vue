<template>
  <view>
    <view v-if="!loading">
      <view class="p-rela">
        <vh-image :src="picture" :height="492" />
        <image :src="backIcon" class="p-abso top-40 left-24 w-56 h-56" @click="onBack"></image>
      </view>
      <view class="mt-n-28">
        <view class="auction-ad-detail__tabs">
          <view class="h-22"></view>
          <view class="flex-c-c">
            <u-tabs :list="tabsList" :current="currentTabIndex" height="84" font-size="32" active-color="#333" inactive-color="#999" bar-width="36" bar-height="8" gutter="57" @change="onTabChange" />
          </view>
        </view>
        <view v-if="list.length" class="ptb-20-plr-24">
          <view
            v-for="(item, index) in list"
            :key="index"
            class="p-rela ptb-00-plr-20 pb-28 bg-ffffff b-rad-10"
            :class="[index ? 'mt-20' : '', item.$isAuctionAbort ? 'pt-28' : 'pt-70']"
            @click="onJump(item)"
          >
            <image v-if="item.$isAuctionAbort" :src="ossIcon('/auction/success_108_132.png')" class="p-abso bottom-0 right-0 w-108 h-132"></image>
            <view v-else class="p-abso top-0 right-0 w-176 h-46">
              <image :src="ossIcon(item.$erStatusBg)" class="p-abso w-p100 h-p100" />
              <view class="p-rela flex-c-c w-p100 h-p100">
                <image :src="ossIcon('/auction/hammer_36_34.png')" class="w-36 h-34" />
                <text class="ml-10 font-24 l-h-34" :class="item.$erStatusTextClazz">{{ item.$erStatusText }}</text>
              </view>
            </view>
            <view class="d-flex j-sb">
              <vh-image :loading-type="4" :src="item.product_img" :width="152" :height="152" :border-radius="6" />
              <view class="flex-1 pl-20">
                <view class="h-68 font-wei-500 font-24 l-h-34 text-hidden-2" :class="item.$adDetailTitleTextClazz">{{ item.title }}</view>
                <text class="mt-04 ptb-00-plr-08 font-22 l-h-32 b-rad-04" :class="item.$adDetailTimeTextClazz">
                  <template v-if="item[item.$eTimeKeyValue]">{{ item[item.$eTimeKeyValue] }} {{ item.$erTimeSuffixText }}</template>
                  <template v-else>{{ item.$erTimeSuffixText }}</template>
                </text>
                <view class="flex-sb-c">
                  <view class="flex-c-c l-h-34">
                    <text class="font-24" :class="item.$adDetailPricePrefixClazz">{{ item.pricePrefixText }}</text>
                    <text class="ml-06 font-32" :class="item.$adDetailPriceClazz"><text class="font-24">¥</text>{{ item.final_auction_price === '0.00' ? item.price : item.final_auction_price }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <vh-empty v-else bgColor="transparent" :padding-top="100" :padding-bottom="100" :image-src="ossIcon('/empty/emp_goods.png')" text="暂无数据" :text-bottom="0" />
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionGoodsStatus } from '@/common/js/utils/mapperModel'
import { MAuctionGoodsStatusObjMapper } from '@/common/js/utils/mapper'
const MAuctionAdDetailTab = {
  Bidding: 0,
  Waiting: 1,
  Success: 2,
}
export default {
  name: 'auctionAdDetail', // 拍卖广告详情页面
  data: () => ({
    loading: true,
    id: '',
    picture: '',
    tabsList: [
      { name: '竞拍中', type: MAuctionAdDetailTab.Bidding },
      { name: '待开拍', type: MAuctionAdDetailTab.Waiting },
      { name: '最近成交', type: MAuctionAdDetailTab.Success }
    ],
    currentTabIndex: 0,
    biddingList: [],
    waitingList: [],
    successList: [],
  }),
  computed: {
    pageLength () {
      return this.pages.getPageLength()
    },
    backIcon ({ $app }) {
      if ($app) {
        return this.ossIcon('/auction/cancel_56.png')
      }
      return this.ossIcon(this.pageLength <= 1 ? '/goods_detail/nav_home_whi.png' : '/auction/cancel_56.png')
    },
    currentTabType ({ tabsList, currentTabIndex }) {
      return tabsList[currentTabIndex].type
    },
    list ({ currentTabType, biddingList, waitingList, successList }) {
      switch (currentTabType) {
        case MAuctionAdDetailTab.Bidding:
          return biddingList
        case MAuctionAdDetailTab.Waiting:
          return waitingList
        case MAuctionAdDetailTab.Success:
          return successList
        default:
          return []
      }
    }
  },
  methods: {
    async load () {
      if (!this.id) return
      const res = await this.$u.api.getAuctionAdDetail({ id: this.id })
      const data = res?.data || {}
      const { picture = '', bidding_list = [], waiting_list = [], success_list = [] } = data
      this.picture = picture
      const getMapList = (list) => {
        return list.map(item => Object.assign({}, MAuctionGoodsStatusObjMapper[item.onsale_status], item, { $isAuctionAbort: item.onsale_status === MAuctionGoodsStatus.AuctionAbort }))
      }
      this.biddingList = getMapList(bidding_list)
      this.waitingList = getMapList(waiting_list)
      this.successList = getMapList(success_list)
      this.loading = false
    },
    onTabChange (index) {
      this.currentTabIndex = index
    },
    onJump (item) {
      this.jump.navigateTo(`${this.$routeTable.pHAuctionGoodsDetail}?id=${item.id}`)
    },
    onBack () {
      if (this.$app) {
        this.$customBack()
      } else {
        if (this.pageLength <= 1) {
          this.jump.reLaunch(this.$routeTable.pgIndex)
        } else {
          this.jump.navigateBack()
        }
      }
    },
  },
  onLoad (options) {
    this.id = options.id
    this.load()
  },
  onPullDownRefresh() {
    this.load().finally(() => {
      uni.stopPullDownRefresh()
    })
  },
}
</script>

<style>
  page {
    background: #f5f5f5;
  }
</style>

<style lang="scss" scoped>
  .auction-ad-detail {
    &__tabs {
      background: #FEFEFE;
      border-radius: 24rpx 24rpx 16rpx 16rpx;
      overflow: hidden;

      ::v-deep {
        .u-tabs {
          background: #FEFEFE !important;
        }

        .u-tab {
          &-item {
            min-width: 112rpx;
            box-sizing: content-box;
            font-weight: 600 !important;
            vertical-align: top;
          }

          &-bar {
            bottom: auto;
            background: #e80404 !important;
          }
        }
      }
    }
  }
</style>
