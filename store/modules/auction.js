const auction = {
	namespaced: true,
	state: () => ({
		logisticsInfo:{ 
			image:'https://images.wineyun.com/vinehoo/client/commodity/video/cover/il58_1652752586157.jpg?w=1280&h=630',
			expressType:5,
			logisticCode:'JDVD02388623231'
		}, //经纬度信息
		orderDetailInfo: {
		   "id": 1,
		   "uid": 1,
		   "nickname": "zrc",
		   "seller_uid": 2,
		   "seller_nickname": "test",
		   "order_no": "VHA221208008549701509",
		   "order_status": 1,
		   "auction_type": 1,
		   "order_qty": 1,
		   "goods_id": 1,
		   "goods_img": "https://images.wineyun.com/vinehoo/goods-images/203408/1670491652182KAJnyhXx2_mf3WHWDmf.png?w=750&h=464",
		   "product_code": "{\"nums\": 1, \"short_code\": \"010046701091\"}",
		   "goods_name": "测试商品",
		   "refund_status": 0,
		   "push_t_status": 0,
		   "push_wms_status": 0,
		   "payment_method": 1,
		   "payment_subject": 2,
		   "payment_amount": "100.00",
		   "express_type": 2,
		   "express_number": "",
		   "province_id": 23,
		   "province_name": "重庆",
		   "city_id": 271,
		   "city_name": "重庆市",
		   "district_id": 2499,
		   "district_name": "九龙坡区",
		   "address": "龙泉路24号",
		   "consignee": "熊俊杰",
		   "consignee_phone": "18223970406",
		   "return_number": null,
		   "warehouse_code": "034",
		   "is_delete": 0,
		   "is_evaluate": 0,
		   "tradeno": "SN23242343242",
		   "payment_time": "2022-12-09 16:08:42",
		   "delivery_time": "1970-01-01",
		   "goods_receipt_time": "1970-01-01 08:00:00",
		   "update_time": 0,
		   "created_time": "2022-12-09 16:08:42",
		   "countdown": 1780,
		   "earnest_money": '200',
		   'status': 2
		}
	}),
	getters: {},
	mutations: {
		// 变更物流信息 state = 状态， data = 物流信息
		muLogisticsInfo(state, data) {
			state.logisticsInfo = data
		},
		// 变更订单详情信息 state = 状态， data = 订单详情信息
		muOrderDetailInfo(state, data) {
			state.orderDetailInfo = data
		},
	},
	actions: {}
}

export default auction
