import { ossIcon } from '@/common/js/utils/oss'
import Vue from 'vue'

const newcomerCoupon = {
	namespaced: true,
	state: () => ({
		newcomerCouponInfo: {
			unclaimed_popup_image: ossIcon(`/comm/new_peo_cou_money.png`),
			claimed_popup_image: ossIcon(`/comm/new_peo_cou_rec.png`)
		}
	}),
	getters: {},
	mutations: {
		SET_NEWCOMER_COUPON_INFO (state, info) {
			const filterInfo = {}
			Object.keys(info).forEach(key => {
				if (info[key]) {
					filterInfo[key] = info[key]
				}
			})
			state.newcomerCouponInfo = Object.assign({}, state.newcomerCouponInfo, filterInfo)
		}
	},
	actions: {
		initNewcomerCouponInfo ({ commit }) {
			Vue.prototype.$u.api.getVersionInfo().then(res => {
				const { unclaimed_popup_image = '', claimed_popup_image = '' } = res?.data || {}
				commit('SET_NEWCOMER_COUPON_INFO', {
					unclaimed_popup_image,
					claimed_popup_image
				})
			})
		}
	}
}

export default newcomerCoupon
