const topic = {
	namespaced: true,
	state: () => ({
		checkedTopicList: [],
		defaultWineCommentTopicIds: [640],
		wineCommentTopicList: [],
		wineCommentTopicIdList: []
	}),
	getters: {},
	mutations: {
		UPDATE_CHECKED_TOPIC_LIST (state, topicList) {
			state.checkedTopicList = topicList
		},
		UPDATE_WINE_COMMENT_TOPIC_LIST (state, wineCommentTopicList) {
			state.wineCommentTopicList = wineCommentTopicList
			state.wineCommentTopicIdList = wineCommentTopicList.map(({ id }) => id)
		}
	},
	actions: {}
}

export default topic
