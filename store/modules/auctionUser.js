import Vue from 'vue'

const auctionUser = {
	namespaced: true,
	state: () => ({
		auctionUserInfo: {},
	}),
	getters: {
		isDisabledAuctionUser (state) {
			const { status = 1 } = state.auctionUserInfo
			return !status
		}
	},
	mutations: {
		SET_AUCTION_USER_INFO (state, data) {
			state.auctionUserInfo = data
		}
	},
	actions: {
		async getAuctionUserInfo ({ commit, state }) {
			if (Object.keys(state.auctionUserInfo).length) return
			const res = await Vue.prototype.$u.api.getAuctionUserInfo()
			commit('SET_AUCTION_USER_INFO', res?.data?.info || {})
		}
	}
}

export default auctionUser
