const startupPageOptions = {
	namespaced: true,
	state: () => ({
		prefixTabbarPage: '',
		indexStartupPageCount: 0,
		miaofaStartupPageCount: 0,
	}),
	getters: {},
	mutations: {
		UPDATE_PREFIX_TABBAR_PAGE (state, page) {
			state.prefixTabbarPage = page
		},
		UPDATE_INDEX_STARTUP_PAGE_COUNT (state, count) {
			state.indexStartupPageCount = count
		},
		UPDATE_MIAOFA_STARTUP_PAGE_COUNT (state, count) {
			state.miaofaStartupPageCount = count
		},
	},
	actions: {}
}

export default startupPageOptions
