import Vue from 'vue'

const miaofa = {
	namespaced: true,
	state: () => ({
		goldAreaList: [],
	}),
	mutations: {
		SET_GOLD_AREA_LIST (state, data) {
			state.goldAreaList = data
		}
	},
	actions: {
		async getGoldAreaList ({ commit, state }) {
			if (state.goldAreaList.length) return
			const res = await Vue.prototype.$u.api.getSecondCombine({ client: Vue.prototype.$client })
			const { home_screen = [] } = res?.data || {}
			commit('SET_GOLD_AREA_LIST', home_screen)
		}
	}
}

export default miaofa
