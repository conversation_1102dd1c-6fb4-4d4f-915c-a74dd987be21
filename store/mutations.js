// 更改Vuex store中的state状态（唯一方法）
const mutations = {
	// 变更from状态 state = 状态， data = 来自哪个端
	muFrom(state, data) {
		state.from = data
	},
	// 变更版本号 state = 状态， data = 版本号
	muVersion(state, data) {
		state.version = data
	},
	// 变更用户信息 state = 状态， data = 用户信息
	muUserInfo(state, data) {
		state.userInfo = data
	},
	// 变更到期提醒 state = 状态， data = 到期提醒
	muCouponExpirationReminder( state, data ) {
		state.couponExpirationReminder = data
	},
	// 是否取消过我的签到遮罩层 0 = 未取消过、1 = 取消过 state = 状态， data = 是否取消过
	muIsCancelMineSignMask(state, data) {
		state.isCancelMineSignMask = data
	},
	// 变更地区信息 state = 状态， data = 地区信息
	muRegionInfo(state, data) {
		state.regionInfo = data
	},
	// 变更收货地址信息 state = 状态， data = 收货地址信息
	muAddressInfoState(state, data) {
		state.addressInfoState = data
	},
	// 变更地址标签 state = 状态， data = 标签列表
	muLabelList(state, data) {
		state.labelList = data
	},
	// 变更发票信息 state = 状态， data = 发票信息
	muInvoiceInfo(state, data) {
		state.invoiceInfo = data
	},
	// 变更服务器当前时间 state = 状态， data = 服务器当前时间
	muServercurrentTime(state, data) {
		state.servercurrentTime = data
	},
	// 变更订单确认页订单信息 state = 状态， data = 订单确认页订单商品信息
	muOrderInfo(state, data) {
		state.orderGoodsInfo = data
	},
	// 变更收银台支付信息 state = 状态， data = 收银台支付信息
	muPayInfo(state, data) {
		state.payInfo = data
	},
	// 变更收银台支付信息 state = 状态， data = 售后商品信息
	muAfterSaleGoodsInfo(state, data) {
		state.afterSaleGoodsInfo = data
	},
	// 变更城市id state = 状态， data = 精品酒会城市信息
	muWinePartyCityInfo(state, data) {
		state.winePartyCityInfo = data
	},
	// 变更酒会订单信息 state = 状态， data = 酒会订单信息
	muWinePartyOrderInfo(state, data) {
		state.winePartyOrderInfo = data
	},
	// 变更兔头订单信息 state = 状态， data = 兔头订单信息
	muRabbitOrderInfo(state, data) {
		state.rabbitOrderInfo = data
	},
	// 变更兔头数量 state = 状态， data = 兔头数量
	muRabbitNum(state, data) {
		state.rabbitNum = data
	},
	// 变更兔头优惠券信息 state = 状态， data = 兔头优惠券信息
	// muRabbitCoupon(state, data) {
	// 	state.rabbitCoupon = data
	// },
	// 变更物流信息 state = 状态， data = 物流信息
	muLogisticsInfo(state, data) {
		state.logisticsInfo = data
	},
	// 变更门店桌子（信息） state = 状态， data = 门店桌子信息
	muStoreTableInfo(state, data) {
		state.storeTableInfo = data
	},
	// 变更门店详情（信息） state = 状态， data = 门店信息
	muStoreInfo(state, data) {
		state.storeInfo = data
	},
	// 变更门店订单信息 state = 状态， data = 门店订单信息
	muStoreOrderInfo(state, data) {
		state.storeOrderInfo = data
	},
	// 变更门店订单详情 state = 状态， data = 门店订单详情
	muStoreOrderDetail(state, data) {
		state.storeOrderDetail = data
	},
}

export default mutations