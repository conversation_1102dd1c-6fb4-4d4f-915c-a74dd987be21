import Vue from 'vue'
import Vuex from 'vuex'
import state from './state'
import getters from './getters'
import mutations from './mutations'
import actions from './actions'
import shoppingCart from './modules/shoppingCart' //购物车
import wineComment from './modules/wineComment'
import topic from './modules/topic'
import auction from './modules/auction'
import auctionFunds from './modules/auctionFunds'
import auctionGoods from './modules/auctionGoods'
import auctionPayeeAccount from './modules/auctionPayeeAccount'
import auctionUser from './modules/auctionUser'
import newPeopleArea from './modules/newPeopleArea'
import miaofa from './modules/miaofa'
import startupPageOptions from './modules/startupPageOptions'
import personAuctionGoods from './modules/personAuctionGoods'
import newcomerCoupon from './modules/newcomerCoupon'
Vue.use(Vuex)

const store = new Vuex.Store({
	state, //根目录state
	getters, //根目录getters
	mutations, //根目录mutations
	actions, //根目录actions
	modules:{
		shoppingCart,//购物车模块
		wineComment,
		topic,
		auction,
		auctionFunds,
		auctionGoods,
		auctionPayeeAccount,
		auctionUser,
		newPeopleArea,
		miaofa,
		startupPageOptions,
		personAuctionGoods,
		newcomerCoupon,
	}
})

export default store
