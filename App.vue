<script>
import Vue from 'vue'
import guid from '@/common/js/fun/guid.js' //全局唯一id
import { mapState, mapMutations } from 'vuex'

export default {
  onLaunch(options) {
    console.log('App Launc', options)
    this.clearCache()
    this.setUniqueId()
    this.getSource(options.query)
    // 初始化获取tabbar主题配置
    this.initTabbarThemeConfig()

    // console.log(guid())
    window.vhAppSendMessage = (obj = {}) => {
      console.log('obj', obj)
      if (typeof obj === 'string') obj = JSON.parse(obj)
      const { type = 1 } = obj
      if (type === 1) return { type, data: '酒云网' }
      if (type === 4) {
        const images = JSON.parse(obj.data).images
        console.log('uploadPictureInfo', images)
        uploadPictureInfo && uploadPictureInfo(images)
        window.uploadPictureInfo = null
      }
      if (type === 6) {
        // APP收银台支付成功
      }
      if (type === 7) {
        // APP收银台取消支付
      }
      if (type === 8) {
        // 拉起APP支付失败
        onPullAppPayFail && onPullAppPayFail()
      }
      if (type == 10) {
        indexTopRefresh && indexTopRefresh()
      }
      return ''
    }
    let { from = '', version = '', statusBarHeight = 0, safeBeautyBottom = 0 } = options?.query || {}
    if (from && version) {
      uni.setStorageSync('vhAppWebviewParams', options.query)
    } else {
      const params = uni.getStorageSync('vhAppWebviewParams')
      if (params) {
        from = params.from
        version = params.version
        statusBarHeight = params.statusBarHeight
        safeBeautyBottom = params.safeBeautyBottom || 0
      }
    }
    if (from) this.muFrom(from)
    if (version) this.muVersion(version)
    Vue.prototype.$vhFrom = from
    Vue.prototype.$android = from === '1'
    Vue.prototype.$ios = from === '2'
    Vue.prototype.$harmonyos = from === 'next'
    const $app = ['1', '2', 'next'].includes(from)
    Vue.prototype.$app = $app
    Vue.prototype.$vhVersion = version
    Vue.prototype.$appStatusBarHeight = +statusBarHeight
    Vue.prototype.$safeBeautyBottom = +safeBeautyBottom
    let $customBack = null
    if ($app) {
      $customBack = () => {
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'goBack', android_path: 'goBack' },
        })
      }
    }
    Vue.prototype.$customBack = $customBack
    Vue.prototype.$routeTable = this.routeTable
    let $client = 3
    if (Vue.prototype.$android) $client = 1
    else if (Vue.prototype.$ios) $client = 0
    else if (Vue.prototype.$harmonyos) $client = 5
    Vue.prototype.$client = $client
    Vue.prototype.$routeTable = this.routeTable
    if (!$app && `/${options.path}` === this.routeTable.pgIndex && !Object.keys(options.query).length) {
      if (this.login.isLogin('', 0)) {
        this.$u.api.userSpecifiedData({ field: 'home_select' }).then((res) => {
          const {
            data: { home_select = '1' },
          } = res
          if (home_select === '0') {
            this.jump.reLaunch(this.routeTable.pgMiaoFa)
          }
        })
      } else {
        this.jump.reLaunch(this.routeTable.pgMiaoFa)
      }
    }

    const checkWASMSupport = () => {
      if (typeof WebAssembly === 'object' && typeof WebAssembly.instantiate === 'function') {
        return true
      } else {
        this.$u.api.reportWasmSupport()
        return false
      }
    }
    console.log('checkWASMSupport', checkWASMSupport())
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable']),
  },
  mounted() {
    setInterval(() => {
      const visibleGoods = uni.getStorageSync('visibleGoods') || []
    const uniqueGoods = visibleGoods.filter((item, index, self) =>
      index === self.findIndex((t) => t.period === item.period)
    );
    if(uniqueGoods.length){
      this.$u.api.vmProductReport({data:uniqueGoods}).then(res=>{
                if(res.error_code===0){
                  uni.setStorageSync('visibleGoods', []);
                }
            })
    }
    
    }, 3000)
  },
  onShow() {
    // // #ifdef MP-WEIXIN
    // this.network.getNetworkType()
    // this.network.onNetworkStatusChange()
    // // #endif
   
    console.log('App Show')
  },
  onHide() {
    // // #ifdef MP-WEIXIN
    // this.network.offNetworkStatusChange()
    // // #endif
    console.log('App Hide')
  },

  methods: {
    ...mapMutations(['muFrom', 'muVersion']),

    // 初始化tabbar主题配置
    async initTabbarThemeConfig() {
      uni.removeStorageSync('tabbarThemeConfig')
      try {
        const res = await this.$u.api.secondConfig()
        if (res.data.isopen) {
          const config = {
            isopen: res.data.isopen,
            iconPath:
              'http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/tabbar-NewYear-sel.gif',
            selectedIconPath:
              'http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/tabbar-NewYear-sel.gif',
            text: '年货节',
          }
          uni.setStorageSync('tabbarThemeConfig', config)
        } else {
          uni.removeStorageSync('tabbarThemeConfig')
        }
      } catch (error) {
        console.error('获取tabbar主题配置失败：', error)
      }
    },

    // 获取活动标识 query = 查询参数 (如有参数)
    // query.source_platform 用户访问来源平台 （必定存在）
    // query.source_event 来源事件(活动)（必定存在）
    // query.source_user 来源事件(活动)用户标识（可能存在）
    // query.source_timeliness 来源时效性（以分钟为单位）（可能存在）
    getSource(query) {
      try {
        console.log(query)
        let { source_platform, source_event, source_user = '', source_timeliness = 120 } = query
        console.log('source_platform', source_platform)
        console.log('source_event', source_event)
        console.log('source_user', source_user)
        if (source_platform && source_event) {
          let data = {}
          data.timeStamp = Date.now() //当前时间戳
          const storageSource = uni.getStorageSync('source') || '' //storage来源标识
          if (storageSource.sourcePlatform && storageSource.sourceEvent) {
            const { sourcePlatform, sourceEvent, sourceUser, timeStamp } = storageSource
            if (`${sourcePlatform}${sourceEvent}${sourceUser}` === `${source_platform}${source_event}${source_user}`) {
              console.log('------------我需要取之前保存的时间戳')
              data.timeStamp = timeStamp //如果之前保存的标识跟重新访问页面拿到的标识一致（失效时间之内）则取之前保存的时间戳
            }
          }
          console.log('-----------------------------------我是storageSource')
          console.log(storageSource)
          data.sourcePlatform = source_platform //用户访问来源平台
          data.sourceEvent = source_event //来源事件(活动)
          data.sourceUser = source_user //来源事件(活动)用户标识
          data.sourceTimeliness = +source_timeliness //来源时效性（以分钟为单位）
          if (Number.isNaN(data.sourceTimeliness)) data.sourceTimeliness = 120 //如果传递进来的格式不对默认120
          uni.setStorageSync('source', data)
        } else {
          console.log('------------我不需要传活动标识')
        }
      } catch (e) {
        console.log(e)
        //TODO handle the exception
        console.log('------------获取source失败')
      }
    },
    // 处理v3根v2的缓存数据穿插（如果登录的是v3，清空v2的用户缓存数据）
    clearCache() {
      try {
        let aeskey = uni.getStorageSync('aeskey')
        let uuid = uni.getStorageSync('uuid')
        if (aeskey || uuid) {
          uni.clearStorageSync()
          console.log('----------------清除成功aeskey或者uuid成功')
          // uni.setStorageSync('aeskey', aeskey);
          // uni.setStorageSync('uuid', uuid);
        } else {
          console.log('----------------我没有aeskey跟uuid')
        }
        uni.setStorageSync('version', 'v3')
      } catch (e) {
        // error
        console.log('----------------清空v2缓存异常')
      }
    },

    // 存下唯一标识
    setUniqueId() {
      uni.getStorage({
        key: 'uniqueId',
        success: (res) => {
          console.log('--------------我有uniqueId')
        },
        fail() {
          console.log('--------------我没有uniqueId')
          uni.setStorageSync('uniqueId', guid())
        },
      })
    },
  },
}
</script>

<style lang="scss">
/*每个页面公共css */
@import 'uview-ui/index.scss'; //uView公共样式
@import '/common/css/global.css'; //全局样式
@import '/common/css/comm.css'; //公共样式
@import '/common/css/animate.css'; //公共动画

/* uView的一些需要修改默认样式的css */
/* 顶部导航栏去白线 */
::v-deep .nav-btn-back {
  display: none;
}
::v-deep .u-border-bottom:after {
  border-bottom-width: 0;
}

/* uni的一些需要修改默认样式的css */
// uni-modal .uni-modal__bd {
// 	white-space: pre-wrap;
// }
</style>
