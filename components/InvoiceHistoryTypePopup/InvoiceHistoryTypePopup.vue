<template>
	<u-popup 
		v-model="value"  
		:maskCloseAble="true" 
		mode="top" 
		:popup="false" 
		length="auto" 
		@close="$emit('input', false)" 
		:customStyle="{top: popupTop + 'px'}"
		:zIndex="979" 
		:borderRadius="20" 
	>
		<view class="p-rela h-162 flex-sb-c ptb-00-plr-24">
			<view class="bg-f6f6f6 b-rad-36 ptb-14-plr-24 text-3" 
			:class="{'bg-fce0e0 b-s-02-e80404 font-wei text-e80404' : invoiceType === item.value }"
			v-for="(item, index) in MInvoiceHistoryTypeList" :key="index"
			@click="$emit('click', item.value )"> {{ item.text }} </view>
		</view>
	</u-popup>
</template>

<script>
	import { MInvoiceHistoryTypeList } from '@/common/js/mapper/invoiceHistory/mapper'
	export default {
		props: {
			value: {
				type: Boolean,
				default: false
			},
			popupTop: {
				type: [Number, String],
				default: 0
			},
			invoiceType: {
				type: Number,
				default: 0
			}
		},
		data: () => ({
			MInvoiceHistoryTypeList,
		}),
	}
</script>