<template>
  <view class="w-340 bg-ffffff b-rad-10" @click="onJump">
    <view class="p-20 pb-10">
      <vh-image :loading-type="8" :src="goods.product_img && goods.product_img[0]" :width="300" :height="300" :border-radius="6" />
    </view>
    <view class="flex-c-c h-46 b-tl-tr-rad-10" :class="goods.onsale_status === MAuctionGoodsStatus.OnAuction ? 'bg-e80404' : 'bg-ff9127'">
      <image :src="ossIcon('/auction/hammer_36_34.png')" class="w-36 h-34" />
      <text class="ml-08 font-24 text-ffffff">{{ goods.onsale_status === MAuctionGoodsStatus.OnAuction ? `${goods.closing_auction_time_str} 结束` : `${goods.sell_time_str} 开始` }}</text>
    </view>
    <view class="p-20">
      <view class="font-24 text-3 l-h-34" :class="`text-hidden-${titleRows}`">{{ goods.title }}</view>
      <view v-if="goods.label_arr" class="d-flex mt-10">
        <view v-for="(label, labelIndex) in goods.label_arr.filter(item => item !== '正品保障')" :key="labelIndex" class="flex-c-c mr-22 w-86 h-26 font-18 text-e80404 text-hidden b-s-01-e80404-r b-rad-02">{{ label }}</view>
      </view>
      <view class="flex-sb-c mt-20">
        <text class="font-wei-500 font-32 text-e80404"><text class="font-18">¥</text>{{ goods.final_auction_price || goods.price }}</text>
        <view v-if="goods.$isShowPageviews" class="font-18 text-9 l-h-26">围观<text class="text-ff9127">{{ goods.pageviews || 0 }}</text></view>
        <view v-else class="flex-c-c" @click.stop="onEnjoy">
          <image :src="ossIcon(`/auction/enjoy${goods.is_like ? '_h' : ''}_24_26.png`)" class="mr-08 w-24 h-26" />
          <text class="font-wei-500 font-24 text-d8d8d8 l-h-34">{{ goods.like_count || 0 }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionGoodsStatus } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'

export default {
  props: {
    goods: {
      type: Object,
      default: () => ({})
    }
  },
  data: () => ({
    MAuctionGoodsStatus,
    titleRows: 1,
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    onJump () {
      this.jump.navigateTo(`${this.routeTable.pHAuctionGoodsDetail}?id=${this.goods.id}`)
    },
    async onEnjoy () {
      const isLogin = await this.login.isLoginV3(this.$vhFrom, 0)
      if (isLogin) {
        const { id, is_like, like_count } = this.goods
        if (is_like) {
          await this.$u.api.cancelEnjoyAuctionGoods({ goods_id: id })
          this.goods.is_like = 0
          this.goods.like_count = like_count - 1
        } else {
          await this.$u.api.addEnjoyAuctionGoods({ goods_id: id })
          this.goods.is_like = 1
          this.goods.like_count = like_count + 1
        }
      }
    }
  },
  created () {
    this.titleRows = Math.round(Math.random() + 1)
  }
}
</script>

<style lang="scss" scoped>
</style>
