<template>
  <u-popup :value="value" mode="bottom" width="100%" :height="height" border-radius="20" @input="onInput">
    <view class="p-rela pt-48">
      <image
        :src="ossIcon('/auction/close_54.png')"
        class="p-abso top-24 right-24 w-54 h-54"
        @click="onInput(false)"
      />
      <view class="font-wei-500 font-32 text-3 l-h-44 text-center">示例图</view>
      <view class="mt-32 ptb-00-plr-24">
        <image :src="sampleGraph" :class="sampleGraphClazz"></image>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    isWine: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    height ({ isWine }) {
      return isWine ? '784rpx' : '490rpx'
    },
    sampleGraph ({ isWine }) {
      return this.ossIcon(`/auction/${isWine ? '' : 'not_'}wine_sample_graph.png`)
    },
    sampleGraphClazz ({ isWine }) {
      return isWine ? 'w-p100 h-612' : 'w-p100 h-318'
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
  },
}
</script>