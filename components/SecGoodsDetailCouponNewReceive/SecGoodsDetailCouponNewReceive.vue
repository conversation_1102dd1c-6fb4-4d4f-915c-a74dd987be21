<template>
	<!-- <view class="bg-li-40 flex-sb-c b-rad-10 b-s-01-f8d6db ptb-10-plr-20" @click.stop="click">
		<view class="flex-s-c">
			<view class="font-32 font-wei text-fe3637 w-s-now">新人券</view>
			<view class="ml-20 w-430 h-p100 flex-s-c flex-wrap">
				<view class="h-34 flex-c-c bg-ffffff b-rad-08 mt-06 mr-12 mb-06 ptb-00-plr-08 font-18 text-e80404" v-for="(item, index) in list" :key="index">
					{{ item.label }} {{ item.quantity > 1 ? `x${item.quantity}` : `` }}
				</view>
			</view>
		</view>
		
		<view class="w-92 h-38 bg-fe3637 flex-c-c font-24 text-ffffff b-rad-20">领券</view>
	</view> -->
	<view class="w-702 h-70" @click.stop="click">
		<image class="wh-p100" :src="ossIcon(`/goods_detail/s_cou_new_bg.png`)" />
	</view>
</template>

<script>
	export default {
		props: {
			couponInfo: {
				type: Object,
				default: () => ({}),
			},
		},
		computed: {
			list({ couponInfo }) {
				const list = couponInfo?.list || []
				const sliceList = list.slice(0,3)
				return sliceList
			}
		},
		methods: {
			click() {
				if( this.login.isLogin(this.$vhFrom)) {
					this.$emit('receiveCoupon')
				}
			}
		}
	}
</script>
