<template>
	<view class="">
		<view class="bg-ffffff b-rad-10 mt-20 pt-40 pl-24 pb-06 pr-24" v-if="Array.isArray(info) && info.length">
			<view class="font-36 font-wei text-3">{{title}}</view>
			<vh-read-more :toggle="true" show-height="572" text-indent="0" color="#999" >
				<view class="" v-for="(item, index) in info" :key="index">
					<!-- <view v-if="index > 0" class="mt-32 h-01 bg-eeeeee"></view> -->
					<view v-if="index > 0" class="pt-48 pb-16">
						<u-line color="#DDD" border-style="dashed"></u-line>
					</view>
					<view class="mt-32">
						<vh-image :src="item.image" :height="290" :border-radius="8"/>
					</view>
					<view class="mt-32 font-30 text-3 l-h-50" v-html="item.introduce"></view>
				</view>
			</vh-read-more>
		</view>
		<view v-if="!Array.isArray(info) && info != null && info.image && info.introduce" class="bg-ffffff b-rad-10 mt-20 pt-40 pl-24 pb-06 pr-24">
			<view class="font-36 font-wei text-3">{{title}}</view>
			<view class="mt-32">
				<vh-image :src="info.image" :height="290" :border-radius="8"/>
			</view>
			<vh-read-more :toggle="true" show-height="250" text-indent="0" color="#999">
				<view class="mt-32 font-30 text-3 l-h-50" v-html="info.introduce"></view>
			</vh-read-more>
		</view>
	</view>
</template>

<script>
	/**
	 * goods-detail-introduce 商品详情介绍
	 * @description 此组件一般用于商品详情的介绍以标题、banner图片、介绍为主（商品详情商品介绍板块）
	 * @ title 名称
	 * @ info 信息
	 * @example <vh-goods-detail-introduce></vh-goods-detail-introduce>
	 */
	export default {
		name: 'vh-goods-detail-introduce',
		props: ['title', 'info'] 
	}
</script>

<style scoped></style>
