<template>
	<u-mask :show="show" :zoom="false">
		<view class="h-p100 flex-c-c-c">
			<image v-if="received === 0" :style="unclaimedImgStyle" class="fade-in" :src="unclaimedImg" mode="widthFix" @click.stop="receive"/>
			<image v-if="received === 1" :style="claimedImgStyle" class="fade-in" :src="claimedImg" mode="widthFix" @click.stop="$emit('jumpActivity')"/>
			<image class="w-54 h-54 mt-44" :src="ossIcon(`/comm/del_bla_trans.png`)" mode="widthFix" @click="$emit('close')"/>
		</view>
    </u-mask>
</template>

<script>
	import { mapState } from 'vuex'
	export default {
		props: {
			show: {
				type: Boolean,
				default: false
			},
			received: {
				type: [Number, String],
				default: ''
			}
		},
		computed: {
			...mapState('newcomerCoupon', ['newcomerCouponInfo']),
			unclaimedImg ({ newcomerCouponInfo }) {
				return newcomerCouponInfo.unclaimed_popup_image
			},
			unclaimedImgStyle ({ unclaimedImg }) {
				const wh = this.getImgWH(unclaimedImg)
				if (wh) return { width: `${wh.w * 2}rpx` }
				return { width: '670rpx' }
			},
			claimedImg ({ newcomerCouponInfo }) {
				return newcomerCouponInfo.claimed_popup_image
			},
			claimedImgStyle ({ claimedImg }) {
				const wh = this.getImgWH(claimedImg)
				if (wh) return { width: `${wh.w * 2}rpx` }
				return { width: '630rpx' }
			}
		},
		methods: {
			receive() {
				this.$emit('receive')
			},
			getImgWH (src) {
				const params = src.split('?')?.[1] || ''
				const vars = params.split('&')
				const searchParams = {}
				vars.forEach(varStr => {
					const [key, value] = varStr.split('=')
					searchParams[key] = value
				})
				const w = +(searchParams?.w || '')
				const h = +(searchParams?.h || '')
				if (!isNaN(w) && !isNaN(h) && w && h) return { w, h }
				return null
			}
		}
	}
</script>