<template>
  <u-popup v-if="text" :value="value" mode="center" width="582" border-radius="10" @input="onInput">
    <view class="pt-40 font-wei font-32 text-3 text-center">{{ title }}</view>
    <view class="mt-32 mr-40 ml-40">
      <scroll-view :style="{ maxHeight: `${maxHeight}rpx` }" scroll-y="true" :show-scrollbar="false" @scrolltolower="onScrolltolower">
        <view id="text" class="font-28 text-6 l-h-40">
          <u-parse :html="text" :show-with-animation="true" @linkpress="onLinkpress"/>
        </view>
      </scroll-view>
    </view>
    <view class="flex-c-c w-p100 h-104 mt-52 bg-ffffff b-sh-00021200-022">
      <view v-if="isReadFinish" class="fade-in">
        <u-button
          shape="circle"
          :hair-line="false"
          :ripple="true"
          ripple-bg-color="#FFF" 
          :custom-style="{ width:'440rpx', height:'64rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#E80404', border:'none' }"
          @click="onAgree"
        >同意并接受</u-button>
      </view>
      <view v-else class="fade-in flex-c-c w-440 h-64 font-28 text-ffffff bg-dddddd b-rad-32" @click="onInput(false)">请上滑看完本条款再同意</view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    isCross: {
      type: Number,
      default: 0
    }
  },
  data: () => ({
    maxHeight: 788,
    text: '',
    isReadFinish: false,
    title:''
  }),
  watch: {
    value () {
      if (this.value) {
        this.loadText().finally(() => {
          this.$nextTick(() => {
            uni.createSelectorQuery().in(this).select('#text').boundingClientRect(data => {
              this.isReadFinish = uni.upx2px(this.maxHeight) > data.height
            }).exec()
          })
        })
      }
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onAgree () {
      this.onInput(false)
      this.$emit('agree')
    },
    async loadText () {
      if (this.test) return
      const res = await this.$u.api.orderRelaText({ type: 16, is_cross: this.isCross })
			this.text = res.data[0].content
      this.title = res.data[0].title
    },
    onLinkpress (e) {
      e.ignore()
      const trimHref = this.$u.trim(e.href, 'all')
      this.jump.jumpH5Agreement(trimHref)
    },
    onScrolltolower () {
      this.isReadFinish = true
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
