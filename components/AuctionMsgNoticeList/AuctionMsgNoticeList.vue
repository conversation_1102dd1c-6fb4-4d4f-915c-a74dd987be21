<template>
  <view class="ptb-00-plr-24 pb-40">
    <view v-for="({ createTime, title, content, notice_data }, index) in list" :key="index" class="mt-40" @click="onJump(notice_data)">
      <view class="flex-c-c mtb-00-mlr-auto w-256 h-50 font-24 text-ffffff bg-d8d8d8 b-rad-10">{{ createTime }}</view>
      <view class="mt-30 ptb-28-plr-20 bg-ffffff b-rad-10">
        <view class="font-wei-500 font-32 text-3 l-h-44">{{ title }}</view>
        <view v-html="notice_data.content || content" class="mt-10 font-24 text-6 l-h-34 text-justify w-b-b-w"></view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import { MAuctionRNType } from '@/common/js/utils/mapperModel'

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    onJump (data) {
      const { goods_id, type } = data
      const { pHAuctionRNPersonal, pHAuctionRNCompany } = this.routeTable
      if (!goods_id) {
        switch (type) {
          case MAuctionRNType.PersonalRN:
            this.jump.navigateTo(pHAuctionRNPersonal)
            break
          case MAuctionRNType.CompanyRN:
            this.jump.navigateTo(pHAuctionRNCompany)
            break
        }
      }
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
