<template>
  <view class="ptb-00-plr-24 pb-40">
    <view v-for="({ createTime, notice_data, title }, index) in list" :key="index" class="mt-40">
      <view class="flex-c-c mtb-00-mlr-auto w-256 h-50 font-24 text-ffffff bg-d8d8d8 b-rad-10">{{ createTime }}</view>
      <view class="mt-30 ptb-28-plr-20 bg-ffffff b-rad-10">
        <view class="font-32 text-3 l-h-44">{{ title }}</view>
        <view class="d-flex j-sb mt-20" @click="onJump(notice_data)">
          <view v-html="notice_data.content" class="w-522 font-24 text-3 l-h-34 text-justify w-b-b-w"></view>
          <vh-image :loading-type="4" :src="getProductImg(notice_data)" :width="120" :height="120" :border-radius="6" />
        </view>
        <view v-if="notice_data.rule_title && notice_data.rule_content" class="mt-10">
          <view class="h-02 bg-eeeeee"></view>
          <view class="mt-28 font-28 text-3 l-h-40">{{ notice_data.rule_title }}</view>
          <view class="mt-20 font-24 text-6 l-h-34">
            <view v-for="(ruleItem, ruleIndex) in notice_data.rule_content" :key="ruleIndex" v-html="ruleItem" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    getProductImg (data) {
      const { product_img = '' } = data || {}
      return Array.isArray(product_img) ? product_img?.[0] || '' : product_img
    },
    onJump (data) {
      const { goods_id, type } = data
      const { pHAuctionGoodsDetail, pHAuctionBuyerOrderDetail, pHAuctionMyCreateList } = this.routeTable
      if (goods_id) {
        switch (type) {
          case 2:
            this.jump.navigateTo(`${pHAuctionBuyerOrderDetail}?goodsId=${goods_id}`)
            return
          case 100:
            if (this.$app) {
              wineYunJsBridge.openAppPage({
                client_path: { "ios_path": "MyPostedAuctionViewController", "android_path": "com.stg.rouge.activity.MySendAuctionActivity" },
                ad_path_param: [
                  { 
                    "ios_key":"login", "ios_val":"1",
                    "android_key":"login", "android_val":"1"
                  }
                ]
              })
            } else {
              this.jump.navigateTo(pHAuctionMyCreateList)
            }
            return
          default:
            this.jump.navigateTo(`${pHAuctionGoodsDetail}?id=${goods_id}`)
            return
        }
      }
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
