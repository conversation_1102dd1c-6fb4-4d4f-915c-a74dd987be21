<template>
  <view class="content">
    <!-- 有数据 -->
    <view v-if="list.length" class="list">
      <view class="list-item" v-for="(item, index) in list" :key="index" @click="jumpGoodsDetail(item)">
        <vh-image :src="item.banner_img" :loading-type="2" :width="246" :height="152" />

        <view class="list-item-right">
          <view class="">
            <view class="goods-name text-hidden-2">{{ item.title }}</view>
            <text class="package-name">{{ item.package_name }}</text>
          </view>

          <view class="list-item-right-bottom">
            <view class="price">
              <text class="price-symbol">¥</text>
              <text>{{ item.package_price }}</text>
            </view>

            <u-button
              shape="circle"
              :hair-line="false"
              :ripple="true"
              ripple-bg-color="#FFF"
              :custom-style="{
                width: '180rpx',
                height: '52rpx',
                fontSize: '26rpx',
                color: '#FFF',
                backgroundColor: '#E80404',
                border: 'none',
                margin: '0',
              }"
              @click="handleJump(item)"
              >{{ item.$type ? '查看详情' : '写酒评' }}</u-button
            >
          </view>
        </view>
      </view>
    </view>

    <!-- 酒评列表（无数据） -->
    <vh-empty
      v-else
      :padding-top="320"
      :padding-bottom="540"
      :image-src="`${osip}/empty/emp_goods.png`"
      text="亲，暂无酒评哦~"
    />
  </view>
</template>

<script>
import { OSIP } from '@/common/js/fun/constant.js'
import { mapState } from 'vuex'
/**
 * wine-comment-list 酒评列表
 * @description 该组件用于酒评列表显示，方便用户风格统一，减少工作量（ 通用组件 ）
 * @property Array list 酒评列表（默认[]）
 * @example <vh-wine-comment-list />
 */
export default {
  name: 'vh-wine-comment-list',

  props: {
    // 列表
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
  },

  data() {
    return {
      osip: OSIP, //oss静态图片前缀
    }
  },

  computed: {
    ...mapState(['routeTable']),
  },

  methods: {
    handleJump(item) {
      const { pCWineCommentSend, pCWineCommentDetail } = this.routeTable

      this.jump.appAndMiniJump(
        1,
        item.$type
          ? `${pCWineCommentDetail}?id=${item.wine_evaluation_id}&source=6`
          : `${pCWineCommentSend}?orderNo=${item.sub_order_no}`,
        this.$vhFrom
      )
    },
    jumpGoodsDetail(item) {
      this.jump.appAndMiniJump(1, `${this.routeTable.pgGoodsDetail}?id=${item.period}`, this.$vhFrom)
    },
  },
}
</script>

<style scoped lang="scss">
.list {
  margin: 0 24rpx;
  margin-top: 20rpx;
  &-item {
    background-color: #fff;
    display: flex;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    padding: 24rpx;
    &-right {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-left: 12rpx;
      &-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }

  & > view:nth-last-child(1) {
    margin-bottom: 0;
  }
}

.goods-name {
  font-size: 24rpx;
}

.text-hidden-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-word;
}

.package-name {
  background-color: #f5f5f5;
  border-radius: 4rpx;
  margin-top: 4rpx;
  padding: 2rpx 12rpx;
  font-size: 20rpx;
  color: #999;
}

.price {
  font-size: 32rpx;
  color: #333;
  &-symbol {
    font-size: 22rpx;
  }
}
</style>
