<template>
  <view>
    <u-popup :value="value" mode="bottom" width="100%" height="698rpx" border-radius="20" @input="onInput">
      <view class="flex-s-c ptb-00-plr-24 h-152">
        <image :src="ossIcon('/auction/close_44.png')" class="w-44 h-44" @click="onInput(false)" />
        <text class="ml-10 font-wei-500 font-32 text-6">常规出价</text>
      </view>
      <view class="h-08 bg-f7f7f7"></view>
      <view class="ptb-20-plr-00">
        <view class="flex-c-c h-40 font-28 text-3 l-h-40">
          <view class="w-158">¥{{ goods.price }}起拍</view>
          <view class="w-02 h-20 bg-d8d8d8"></view>
          <view class="w-304 text-center">
            <text>当前价格</text>
            <text class="ml-22 text-e80404">¥{{ goods.final_auction_price }}</text>
          </view>
          <view class="w-02 h-20 bg-d8d8d8"></view>
          <view class="w-236 text-right">
            <text>加价幅度</text>
            <text class="ml-22 text-e80404">¥{{ goods.markup }}</text>
          </view>
        </view>
        <view class="flex-c-c mt-60">
          <image :src="ossIcon(`/auction/icon_sub${subBtnStatus ? '_h' : ''}.png`)" class="w-96 h-96" @click="onCalcBidPrice('sub')" />
          <view class="w-382 font-wei-500 font-52 text-3 text-center"><text class="font-32">¥</text>{{ bidPrice }}</view>
          <image :src="ossIcon('/auction/icon_add_h.png')" class="w-96 h-96" @click="onCalcBidPrice('add')" />
        </view>
        <view class="mt-32 text-9 font-22 text-center">参与拍卖竞拍时，需要输入“当前价格+至少一个加价幅度”的金额。</view>
      </view>
      <view class="h-08 bg-f7f7f7"></view>
      <view class="mt-20 ptb-00-plr-48 flex-sb-c">
        <text class="font-wei-500 font-28 text-3">匿名购买</text>
        <image :src="ossIcon(`/auction/switch${isAnonymous ? '_h' : ''}_56_26.png`)" class="w-56 h-26" @click="isAnonymous = !isAnonymous" />
      </view>
      <button class="vh-btn flex-c-c mtb-00-mlr-auto mt-40 w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="onBid">提交</button>
    </u-popup>
  </view>
</template>

<script>
import computedNumber from '@/common/js/utils/computedNumber'
import { mapState } from 'vuex'

export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    goods: {
      type: Object,
      default: () => ({})
    },
    isBidPrice: {
      type: Boolean,
      default: false
    },
    anonymousName: {
      type: String,
      defualt: ''
    },
    provinceName: {
      type: String,
      default: ''
    },
  },
  data: () => ({
    isAnonymous: false,
    bidPrice: '',
    bidFailPopupVisible: false,
    bidFailMsg: '',
    bidBtnLoading: false
  }),
  computed: {
    ...mapState(['userInfo']),
    bidPriceSizeValid ({ bidPrice, goods, isBidPrice }) { // 出价大小校验
      const bidPriceNum = +bidPrice
      const { final_auction_price } = goods
      const finalAuctionPriceNum = +final_auction_price
      return (!isBidPrice && bidPriceNum >= finalAuctionPriceNum) || (isBidPrice && bidPriceNum > finalAuctionPriceNum)
    },
    subBtnStatus ({ bidPriceSizeValid, goods, bidPrice, isBidPrice }) {
      if (!bidPriceSizeValid) return false
      const { final_auction_price, markup } = goods
      const finalAuctionPriceNum = +final_auction_price
      const markupNum = +markup
      const bidPriceNum = +bidPrice
      return (!isBidPrice && bidPriceNum > finalAuctionPriceNum) || (isBidPrice && (bidPriceNum >= computedNumber(markupNum, '*', 2).next('+', finalAuctionPriceNum).result))
    }
  },
  watch: {
    value () {
      const { final_auction_price, markup } = this.goods
      if (this.isBidPrice) {
        this.bidPrice = this.formatPrice(computedNumber(+final_auction_price, '+', +markup).result)
      } else {
        this.bidPrice = final_auction_price
      }
    },
    'goods.final_auction_price' () {
      const { final_auction_price, markup } = this.goods
      if (!this.bidPriceSizeValid) {
        this.bidPrice = this.formatPrice(computedNumber(+final_auction_price, '+', +markup).result)
      }
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onCalcBidPrice (type = '') {
      if (!this.bidPriceSizeValid) return
      if (type === 'sub' && !this.subBtnStatus) return
      const bidPrice = +this.bidPrice
      const markup = +this.goods.markup
      if (type === 'sub' && bidPrice === +this.goods.final_auction_price) return
      let calcBidPrice = bidPrice
      switch (type) {
        case 'sub':
          calcBidPrice = computedNumber(bidPrice, '-', markup).result
          break
        case 'add':
          calcBidPrice = computedNumber(bidPrice, '+', markup).result
          break
      }
      this.bidPrice = this.formatPrice(calcBidPrice)
    },
    formatPrice (price) {
      const [firstNum = '', secondNum = ''] = `${price}`.split('.')
      const secondNumLength = secondNum.length
      switch (secondNumLength) {
        case 0:
          return `${firstNum}.00`
        case 1:
          return `${firstNum}.${secondNum}0`
        case 2:
          return price
      }
    },
    onBid () {
      if (this.bidBtnLoading) return
      this.bidBtnLoading = true
      const { nickname, avatar_image } = this.userInfo
      const params = {
        id: this.goods.id,
        bid_price: this.bidPrice,
        is_anonymous: +this.isAnonymous,
        nickname: this.isAnonymous ? this.anonymousName : nickname,
        avatar_image,
        province_name: this.provinceName
      }
      this.$u.api.auctionBid(params).catch((err) => {
        this.bidFailMsg = err?.data?.error_msg || ''
        this.bidFailPopupVisible = true
      }).finally(() => {
        this.bidBtnLoading = false
        this.onInput(false)
      })
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
