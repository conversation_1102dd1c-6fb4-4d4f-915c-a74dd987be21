<template>
	<view class="">
		<block v-for="(innItem, innIndex) in item.goodsInfo" :key="innIndex">
		   <view v-if="innIndex < 3" class="d-flex mt-20">
			<OrderGoodsImage :orderType="item.order_type" :goodsImg="innItem.goods_img" />
			<view class="flex-1 d-flex flex-column j-sb ml-12">
				<OrderGoodsTitle :goodsInfo="innItem" />
				<view class="d-flex j-sb a-center">
					<OrderGoodsTag :orderType="item.order_type" :auctionType="item.auction_type" :goodsInfo="innItem" />
					<text class="font-24 text-9">x{{innItem.order_qty}}</text>
				</view>
			</view>
		   </view>
		</block>
										
		<view v-if="item.goodsInfo.length >= 3" class="d-flex j-center a-center pt-20">
			<view class="w-120 h-44 d-flex j-center a-center b-rad-36 b-s-01-dddddd">
				<u-icon name="arrow-down" :size="24" color="#DDDDDD" />
			</view>
		</view>
									
		<view v-if="item.status === 1 && (item.unshipped_reason || item.predict_time)" class="bg-f7f7f7 b-rad-08 mt-30 ptb-10-plr-20">
			<view v-if="item.unshipped_reason" class="w-s-pw font-24 text-ff9127">{{ item.unshipped_reason }}</view>
			<view v-if="!item.unshipped_reason && item.predict_time" class="d-flex a-center">
				<view class="font-28 font-wei text-3">预计发货时间</view>
				<view class="ml-20 font-24 text-3">{{ item.predict_time.substr(0, 10).replace("-","年").replace("-","月") }}日 前发货</view>
			</view>
		</view>
		
		<view class="mt-28 d-flex j-end a-center">
			<view v-if="item.order_type == 11" class="text-3">
				<text class="font-18">成交价：</text>
				<text class="font-22 font-wei">¥</text>
				<text class="font-32 font-wei">{{ item.payment_amount }}</text>
			</view>
			<view v-else class="d-flex">
				<view class="text-3">
					<text class="font-18">共{{item.total_qty}}件</text>
				</view>
				<view v-if="item.order_type == 4" class="ml-20 text-3">
					<text class="font-18">实付款：</text>
					<text class="font-28 font-wei">{{item.payment_amount}}</text>
					<image class="w-28 h-28 ml-06" src="http://images.vinehoo.com/vinehoomini/v3/comm/rab_gold.png" mode="aspectFill" />
				</view>
				<view v-else class="ml-20 text-3">
					<text class="font-18">实付款：</text>
					<text class="font-22 font-wei">¥</text>
					<text class="font-28 font-wei">{{item.payment_amount}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name: "OrderListGoodsItem",
		
		props: {
			// 订单信息
			item : {
				type: Object,
				default: () => ({})
			},
		}
	}
</script>
