<template>
	<view class="bg-ffffff b-rad-16 mt-20 mr-24 ml-24 p-24 pt-04">
		<view class="d-flex mt-20">
			<vh-image :loading-type="2" :src="orderInfo.goods_img" :width="160" :height="160" :border-radius="10" />
			
			<view class="flex-1 d-flex flex-column j-sb ml-20">
				<view class="">
					<view class="mb-32 font-24 text-0 text-hidden-1">{{ orderInfo.goods_name }}</view>
					<AuctionOrderTypeName :auctionType="orderInfo.auction_type"/>
				</view>
				<view class="d-flex j-sb a-center">
					<text class="font-24 text-9">x{{ orderInfo.order_qty }}</text>
					<view class="text-3">
						<text class="font-18">成交价：</text>
						<text class="font-22 font-wei">¥</text>
						<text class="font-32 font-wei">{{ orderInfo.payment_amount }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
	export default {
		props: {
			// 订单信息
			orderInfo: {
				type: Object,
				default: function() {
					return {};
				}
			}
		}
	}
</script>

<style>
</style>