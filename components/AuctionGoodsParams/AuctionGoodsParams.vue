<template>
  <view>
    <view class="flex-s-c">
      <text class="font-wei-500 font-32 text-3 l-h-44">拍品参数</text>
      <view style="background: rgba(232, 4, 4, 0.11);" class="flex-c-c ml-20 ptb-00-plr-20 h-46 font-wei-500 font-24 text-e80404 l-h-34 b-rad-23">{{ category.category_name }}</view>
    </view>
    <template v-if="goods.label === 0 || (goods.label === 1 && goods.product_type === 1)">
      <view v-for="(rowItem, rowIndex) in rows" :key="rowIndex" class="flex-sb-c pt-20 bt-s-02-eeeeee" :class="rowIndex ? 'mt-20' : 'mt-28'">
        <view v-for="(colItem, colIndex) in rowItem" :key="colIndex" class="w-250">
          <view class="font-24 text-6 l-h-34">{{ colItem.title }}</view>
          <view class="mt-12 font-wei-500 font-28 text-3 l-h-40">{{ colItem.desc }}</view>
        </view>
      </view>
    </template>
  </view>
</template>

<script>
import { MAuctionGoodsCategory } from '@/common/js/utils/mapperModel'

export default {
  props: {
    goods: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    category ({ goods }) {
      return goods?.category_arr || {}
    },
    rows ({ category }) {
      const { category_id, brand, country, odor_type, net_content, alcoholic_strength, years, production_year } = category
      let list = [
        { title: '产国', desc: country || '未知' },
        { title: '度数(仅供参考)', desc: alcoholic_strength ? `${alcoholic_strength}%vol` : '未知' },
        { title: '容量', desc: net_content ? `${net_content}ml` : '未知' },
        { title: '年份', desc: years ? `${years}` : '未知' }
      ]
      if (category_id === MAuctionGoodsCategory.WhiteSpirits) {
        list = [
          { title: '品牌', desc: brand || '未知' },
          { title: '香型', desc: odor_type || '未知' },
          { title: '容量', desc: net_content ? `${net_content}ml` : '未知' },
          { title: '度数', desc: alcoholic_strength ? `${alcoholic_strength}%vol` : '未知' },
          { title: '年份', desc: years ? `${years}` : '未知' },
          { title: '生产年份', desc: production_year ? `${production_year}年` : '未知' },
        ]
      }
      return new Array(Math.ceil(list.length / 2)).fill('').map((item, index) => list.slice(2 * index, 2 * index + 2))
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
