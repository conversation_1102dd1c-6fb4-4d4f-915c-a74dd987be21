<template>
  <view class="">
    <!-- 评论 -->
    <view class="">
      <!-- 评论输入框 -->
      <view class="d-flex j-center a-center">
        <textarea
          class="w-654 h-120 bg-f7f7f7 mt-24 p-24 b-rad-10 font-28 text-3 l-h-40"
          v-model="commText"
          :cursor-spacing="110"
          :placeholder="placeholder"
          placeholder-style="font-size:28rpx;color:#999"
        />
      </view>

      <!-- 用户同意过协议 -->
      <view v-if="protocolList.includes('1')" class="d-flex j-end a-center mt-26 pr-24 mb-24 pl-24">
        <view class="d-flex">
          <image
            class="w-44 h-44"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/exp_gray.png"
            mode="widthFix"
            @click="showEmo = !showEmo"
          />
          <image
            class="w-44 h-44 ml-24"
            :src="`https://images.vinehoo.com/vinehoomini/v3/comm/send_${
              $u.trim(this.commText, 'all') == '' && img == '' ? 'gray' : 'red'
            }.png`"
            mode="widthFix"
            @click="commentAdd"
          />
        </view>
      </view>

      <!-- 用户没有同意过协议 -->
      <view v-else class="d-flex j-sb a-center mt-26 pr-24 mb-24 pl-24">
        <view class="d-flex a-center">
          <vh-check :checked="isAgree" :width="30" :height="30" @click="agreeCommAgreement()" />
          <view class="ml-10 font-22 text-6" @click="agreeCommAgreement()">
            <text class="">阅读并接受</text>
            <text class="text-2b8cf7" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/ContentPubRules`, $vhFrom)"
              >《酒云网内容发布规则》</text
            >
          </view>
        </view>

        <view class="d-flex">
          <image
            class="w-44 h-44"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/exp_gray.png"
            mode="widthFix"
            @click="showEmo = !showEmo"
          />
          <image
            class="w-44 h-44 ml-24"
            :src="`https://images.vinehoo.com/vinehoomini/v3/comm/send_${
              !isAgree || ($u.trim(this.commText, 'all') == '' && img == '') ? 'gray' : 'red'
            }.png`"
            mode="widthFix"
            @click="commentAdd"
          />
        </view>
      </view>
    </view>

    <!-- 表情包容器 -->
    <view v-if="showEmo" class="">
      <view v-if="img" class="p-rela w-120 h-120 bg-f5f5f5 b-rad-10 ml-24 mb-24">
        <image
          class="w-120 h-120 mb-46 mr-20"
          :src="`https://images.vinehoo.com/vinehoomini/v3/emoticon/${img}.gif`"
          mode="aspectFill"
        />
        <image
          class="p-abso top-06 right-06 w-32 h-32"
          src="https://images.vinehoo.com/vinehoomini/v3/comm/del_gray.png"
          mode="aspectFill"
          @click="delEmo"
        />
      </view>

      <view class="w-p100 h-350 bt-s-01-eeeeee bb-s-01-eeeeee">
        <scroll-view class="h-p100" scroll-y="true">
          <view v-if="emoTypeIndex == 0" class="h-p100 d-flex flex-wrap pt-32 pl-24 pr-24 o-scr-y">
            <view
              class="w-120 d-flex flex-column j-center a-center mb-20 mr-20 b-rad-10 o-hid"
              v-for="(item, index) in rabbitHeadEmojiList"
              :key="index"
              @click="selectEmo(item)"
            >
              <vh-image
                loading-type="2"
                :src="`https://images.vinehoo.com/vinehoomini/v3/emoticon/${item.img}.gif`"
                :width="120"
                :height="120"
              />
              <text class="mt-10 font-24 text-9">{{ item.name }}</text>
            </view>
          </view>

          <view v-if="emoTypeIndex == 1" class="h-p100 d-flex flex-wrap pt-32 pl-24 pr-24 o-scr-y">
            <view
              class="w-120 d-flex flex-column j-center a-center mb-20 mr-20 b-rad-10 o-hid"
              v-for="(item, index) in rabbitEmojiList"
              :key="index"
              @click="selectEmo(item)"
            >
              <vh-image
                loading-type="2"
                :src="`https://images.vinehoo.com/vinehoomini/v3/emoticon/${item.img}.gif`"
                :width="120"
                :height="120"
              />
              <text class="mt-10 font-24 text-9">{{ item.name }}</text>
            </view>
          </view>

          <view v-if="emoTypeIndex == 2" class="h-p100 d-flex flex-wrap pt-32 pl-24 pr-24 o-scr-y">
            <view
              class="font-40 ml-10 mr-10 mb-20"
              v-for="(item, index) in emoji"
              :key="index"
              @click="addEmoji(item)"
              >{{ item }}</view
            >
          </view>
        </scroll-view>
      </view>

      <view class="h-80 d-flex bg-f5f5f5">
        <view
          class="h-p100 d-flex j-center a-center ptb-00-plr-42 text-6 font-28"
          :class="emoTypeIndex == index ? 'bg-ffffff' : 'bg-f5f5f5'"
          v-for="(item, index) in emoTypeList"
          :key="index"
          @click="selectEmoType(index)"
          >{{ item }}</view
        >
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
/**
 * comment 评论
 * @description 该组件用于酒云网评论
 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
 * @property Number plate 上传的板块 0 = 售后、1 = 认证、2 = 个人中心上传头像
 * @property String placeholder 输入框默认占位图
 * @event {Function} on-comment-add 添加评论文本
 * @event {Function} on-img-send 发送兔头表情包
 * @example <vh-comment />
 */
export default {
  name: 'vh-comment',
  props: {
    // 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
    from: {
      type: [String, Number],
      default: '',
    },
    // 板块 0 = 商品详情、1 = 社区、2 = 酒闻详情
    plate: {
      type: Number,
      default: 0,
    },

    // placeholder
    placeholder: {
      type: String,
      default: '',
    },

    // 协议列表
    protocolList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      isAgree: false, //是否同意《酒云网-评论管理规则》和《社区运营规则》
      commText: '', //评论内容
      showEmo: false, //是否显示表情包
      img: '', //表亲包图片（非emoji）
      rabbitHeadEmojiList: [
        //兔头表情包
        { reg: '[rabbithead_吨吨吨]', img: 'rh1', name: '吨吨吨' },
        { reg: '[rabbithead_买买买]', img: 'rh2', name: '买买买' },
        { reg: '[rabbithead_剁手]', img: 'rh3', name: '剁手' },
        { reg: '[rabbithead_难喝]', img: 'rh4', name: '难喝' },
        { reg: '[rabbithead_喜欢]', img: 'rh5', name: '喜欢' },
        { reg: '[rabbithead_好]', img: 'rh6', name: '好' },
        { reg: '[rabbithead_嘿嘿嘿]', img: 'rh7', name: '嘿嘿嘿' },
        { reg: '[rabbithead_晕了]', img: 'rh8', name: '晕了' },
        { reg: '[rabbithead_哇]', img: 'rh9', name: '哇' },
        { reg: '[rabbithead_推荐]', img: 'rh10', name: '推荐' },
        { reg: '[rabbithead_悠闲]', img: 'rh11', name: '悠闲' },
        { reg: '[rabbithead_帅气]', img: 'rh12', name: '帅气' },
        { reg: '[rabbithead_思考]', img: 'rh13', name: '思考' },
        { reg: '[rabbithead_笑哭]', img: 'rh14', name: '笑哭' },
        { reg: '[rabbithead_愤怒]', img: 'rh15', name: '愤怒' },
        { reg: '[rabbithead_摸鱼]', img: 'rh16', name: '摸鱼' },
      ],
      rabbitEmojiList: [
        //兔子表情包
        { reg: '[rabbit_喝酒去]', img: 'rb1', name: '喝酒去' },
        { reg: '[rabbit_还不错]', img: 'rb2', name: '还不错' },
        { reg: '[rabbit_倒酒]', img: 'rb3', name: '倒酒' },
        { reg: '[rabbit_没酒喝]', img: 'rb4', name: '没酒喝' },
        { reg: '[rabbit_走一个]', img: 'rb5', name: '走一个' },
        { reg: '[rabbit_嘿嘿]', img: 'rb6', name: '嘿嘿' },
        { reg: '[rabbit_红酒杯]', img: 'rb7', name: '红酒杯' },
        { reg: '[rabbit_自罚一杯]', img: 'rb8', name: '自罚一杯' },
        { reg: '[rabbit_摇耳朵]', img: 'rb9', name: '摇耳朵' },
        { reg: '[rabbit_捂脸]', img: 'rb10', name: '捂脸' },
        { reg: '[rabbit_坏笑]', img: 'rb11', name: '坏笑' },
        { reg: '[rabbit_蹦迪]', img: 'rb12', name: '蹦迪' },
        { reg: '[rabbit_安排]', img: 'rb13', name: '安排' },
        { reg: '[rabbit_赞]', img: 'rb14', name: '赞' },
        { reg: '[rabbit_酒归你]', img: 'rb15', name: '酒归你' },
        { reg: '[rabbit_来啊]', img: 'rb16', name: '来啊' },
        { reg: '[rabbit_笔芯]', img: 'rb17', name: '笔芯' },
        { reg: '[rabbit_买买买]', img: 'rb18', name: '买买买' },
        { reg: '[rabbit_出来玩]', img: 'rb19', name: '出来玩' },
        { reg: '[rabbit_打脑壳]', img: 'rb20', name: '打脑壳' },
        { reg: '[rabbit_嗨]', img: 'rb21', name: '嗨' },
        { reg: '[rabbit_拜]', img: 'rb22', name: '拜' },
        { reg: '[rabbit_白眼]', img: 'rb23', name: '白眼' },
        { reg: '[rabbit_沉迷工作]', img: 'rb24', name: '沉迷工作' },
        { reg: '[rabbit_没看见]', img: 'rb25', name: '没看见' },
        { reg: '[rabbit_不约]', img: 'rb26', name: '不约' },
        { reg: '[rabbit_努力加油]', img: 'rb27', name: '努力加油' },
        { reg: '[rabbit_吃瓜]', img: 'rb28', name: '吃瓜' },
        { reg: '[rabbit_没错]', img: 'rb29', name: '没错' },
        { reg: '[rabbit_暗中观察]', img: 'rb30', name: '暗中观察' },
        { reg: '[rabbit_你继续]', img: 'rb31', name: '你继续' },
        { reg: '[rabbit_太难了]', img: 'rb32', name: '太难了' },
      ],
      emoji: [
        '😀',
        '😁',
        '😂',
        '🤣',
        '😃',
        '😄',
        '😅',
        '😆',
        '😉',
        '😊',
        '😋',
        '😎',
        '😍',
        '😘',
        '😗',
        '😙',
        '😚',
        '☺️',
        '🙂',
        '🤗',
        '🤩',
        '🤔',
        '🤨',
        '😐',
        '😑',
        '😶',
        '🙄',
        '😏',
        '😣',
        '😥',
        '😮',
        '🤐',
        '😯',
        '😪',
        '😫',
        '😴',
        '😌',
        '😛',
        '😜',
        '😝',
        '🤤',
        '😒',
        '😓',
        '😔',
        '😕',
        '🙃',
        '🤑',
        '😲',
        '☹️',
        '🙁',
        '😖',
        '😞',
        '😟',
        '😤',
        '😢',
        '😭',
        '😦',
        '😧',
        '😨',
        '😩',
        '🤯',
        '😬',
        '😰',
        '😱',
        '😳',
        '🤪',
        '😵',
        '😡',
        '😠',
        '🤬',
        '😷',
        '🤒',
        '🤕',
        '🤢',
        '🤮',
        '🤧',
        '😇',
        '🤠',
        '🤡',
        '🤥',
        '🤫',
        '🤭',
        '🧐',
        '🤓',
        '😈',
        '👿',
        '👹',
        '👺',
        '💀',
        '👻',
        '👽',
        '🤖',
        '💩',
        '😺',
        '😸',
        '😹',
        '😻',
        '😼',
        '😽',
        '🙀',
        '😿',
        '😾',
      ], //emoji表情包
      emoTypeList: ['兔头', '兔子', 'Emoji'], //表情类型列表
      emoTypeIndex: 0, //表情类型索引
    }
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['agreementPrefix']),
  },

  methods: {
    // 同意/取消同意协议
    async agreeCommAgreement() {
      this.isAgree = !this.isAgree
      try {
        await this.$u.api.agreeProtocol({ xid: 1 })
        uni.setStorageSync('protocolList', ['1'])
      } catch (e) {
        //TODO handle the exception
      }

      // uni.getStorage({
      // 	key:'protocolList',
      // 	success: res => {
      // 		uni.removeStorage({
      // 			key: 'protocolList',
      // 			success: () => {
      // 				console.log('removeStorage success');
      // 				this.isAgree = 0
      // 			}
      // 		});
      // 	},
      // 	fail: async () => {
      // 		try{
      // 			await this.$u.api.agreeProtocol({ xid: 1 })
      // 			this.isAgree = 1
      // 		}catch(e){
      // 			//TODO handle the exception
      // 		}
      // 	}
      // })
    },

    // 添加评论
    commentAdd() {
      console.log(this.protocolList)
      if (!this.isAgree && !this.protocolList.includes('1')) return this.feedback.toast({ title: '请勾选协议~' })
      if (this.$u.trim(this.commText, 'all') == '' && this.img == '')
        return this.feedback.toast({ title: '请输入评论内容~' })
      console.log('----------------我是子组件传递给父组件文本内容')
      this.$emit('on-comment-add', this.commText)
      this.img = ''
      this.commText = ''
    },

    // 添加emoji emo = 颜文字
    addEmoji(emo) {
      this.commText = this.commText + emo
    },

    // 删除表情包
    delEmo() {
      this.img = ''
      this.$emit('on-img-send', '')
    },

    // 选择表情包 item = 表情包列表某一项
    selectEmo(item) {
      this.img = item.img
      this.$emit('on-img-send', item.reg)
    },

    // 选择表情包类型 index = 列表索引
    selectEmoType(index) {
      this.emoTypeIndex = index
    },
  },
}
</script>

<style scoped></style>
