<template>
  <view class="vh-image-con" :class="[isMf ? 'mf-card' : '']" @tap="onClick" :style="[wrapStyle, backgroundStyle]">
    <image
      v-if="!isError"
      :src="getImage"
      :mode="mode"
      @error="onErrorHandler"
      @load="onLoadHandler"
      :lazy-load="lazyLoad"
      class="vh-image"
      :show-menu-by-longpress="showMenuByLongpress"
      :style="{
        backgroundColor: bgColor,
        borderRadius: shape == 'circle' ? '50%' : $u.addUnit(borderRadius),
      }"
    ></image>
    <view
      v-if="showLoading && loading"
      class="vh-image-loading"
      :style="{
        borderRadius: shape == 'circle' ? '50%' : $u.addUnit(borderRadius),
        backgroundColor: this.bgColor,
      }"
    >
      <image :style="[wrapStyle]" :src="getLoadingImage" :mode="mode" />
      <!-- <slot v-if="$slots.loading" name="loading" />
			<u-icon v-else :name="loadingIcon" :width="width" :height="height"></u-icon> -->
    </view>
    <view
      v-if="showError && isError && !loading"
      class="u-image-error"
      :style="{
        borderRadius: shape == 'circle' ? '50%' : $u.addUnit(borderRadius),
      }"
    >
      <!-- <slot v-if="$slots.error" name="error" />
			<u-icon v-else :name="errorIcon" :width="width" :height="height"></u-icon> -->
      <image :style="[wrapStyle]" :src="getLoadingImage" :mode="mode" />
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
/**
 * image 图片
 * @description 此组件为uni-app的image组件的加强版，在继承了原有功能外，还支持淡入动画、加载中、加载失败提示、圆角值和形状等。
 * @property {String | Number} loading-type 加载类型 1 = 白底灰字（长方形702*434）、2 = 灰底灰字（长方形702*434）、3 = 白底灰字（类似正方形276 * 262 ）、4 = 灰底灰字（正方形262 * 262 ）、5 = 灰底兔头图片
 * @property {String} src 图片地址
 * @property {String} mode 裁剪模式
 * @property {String | Number} width 宽度，单位任意，如果为数值，则为rpx单位（默认100%）
 * @property {String | Number} height 高度，单位任意，如果为数值，则为rpx单位（默认 auto）
 * @property {String} shape 图片形状，circle-圆形，square-方形（默认square）
 * @property {String | Number} border-radius 圆角值，单位任意，如果为数值，则为rpx单位（默认 0）
 * @property {Boolean} lazy-load 是否懒加载，仅微信小程序、App、百度小程序、字节跳动小程序有效（默认 true）
 * @property {Boolean} show-menu-by-longpress 是否开启长按图片显示识别小程序码菜单，仅微信小程序有效（默认 false）
 * @property {String} loading-icon 加载中的图标，或者小图片（默认 photo）
 * @property {String} error-icon 加载失败的图标，或者小图片（默认 error-circle）
 * @property {Boolean} show-loading 是否显示加载中的图标或者自定义的slot（默认 true）
 * @property {Boolean} show-error 是否显示加载错误的图标或者自定义的slot（默认 true）
 * @property {Boolean} fade 是否需要淡入效果（默认 true）
 * @property {String Number} width 传入图片路径时图片的宽度
 * @property {String Number} height 传入图片路径时图片的高度
 * @property {Boolean} webp 只支持网络资源，只对微信小程序有效（默认 false）
 * @property {String | Number} duration 搭配fade参数的过渡时间，单位ms（默认 500）
 * @property {String} bg-color 背景颜色，用于深色页面加载图片时，为了和背景色融合
 * @event {Function} click 点击图片时触发
 * @event {Function} error 图片加载失败时触发
 * @event {Function} load 图片加载成功时触发
 * @example <u-image width="100%" height="300rpx" :src="src"></u-image>
 */
export default {
  name: 'u-image',
  props: {
    // 加载类型 1 = 白底灰字（长方形702*434）、2 = 灰底灰字（长方形702*434）、3 = 白底灰字（类似正方形276 * 262 ）、4 = 灰底灰字（正方形262 * 262 ）、5 = 灰底兔头图片
    // 6 = 344 x 470
    // 7 = 344 x 512
    // 8 = 300 x 300
    // 9 = 702 x 206
    // 10 = 686 x 200 灰
    loadingType: {
      type: [String, Number],
      default: 1,
    },
    // 图片地址
    isMf: {
      type: Boolean,
      default: false,
    },
    src: {
      default: '',
    },
    // 裁剪模式
    mode: {
      type: String,
      default: 'aspectFill',
    },
    // 宽度，单位任意
    width: {
      type: [String, Number],
      default: '100%',
    },
    // 高度，单位任意
    height: {
      type: [String, Number],
      default: 'auto',
    },
    // 图片形状，circle-圆形，square-方形
    shape: {
      type: String,
      default: 'square',
    },
    // 圆角，单位任意
    borderRadius: {
      type: [String, Number],
      default: 0,
    },
    // 是否懒加载，微信小程序、App、百度小程序、字节跳动小程序
    lazyLoad: {
      type: Boolean,
      default: true,
    },
    // 开启长按图片显示识别微信小程序码菜单
    showMenuByLongpress: {
      type: Boolean,
      default: false,
    },
    // 加载中的图标，或者小图片
    loadingIcon: {
      type: String,
      default: 'photo',
    },
    // 加载失败的图标，或者小图片
    errorIcon: {
      type: String,
      default: 'error-circle',
    },
    // 是否显示加载中的图标或者自定义的slot
    showLoading: {
      type: Boolean,
      default: true,
    },
    // 是否显示加载错误的图标或者自定义的slot
    showError: {
      type: Boolean,
      default: true,
    },
    // 是否需要淡入效果
    fade: {
      type: Boolean,
      default: true,
    },
    // 只支持网络资源，只对微信小程序有效
    webp: {
      type: Boolean,
      default: false,
    },
    // 过渡时间，单位ms
    duration: {
      type: [String, Number],
      default: 100,
    },
    // 背景颜色，用于深色页面加载图片时，为了和背景色融合
    bgColor: {
      type: String,
      default: 'transparent',
    },
    isResize: {
      type: Boolean,
      default: false,
    },
    resizeRatio: {
      type: Object,
      default: () => ({ wratio: 16, hratio: 9 }),
    },
    resizeUsePx: {
      type: Boolean,
      default: false,
    },
    opacityProp: {
      type: Number,
      default: 1,
    },
    // 背景图片地址
    backgroundImage: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // 图片是否加载错误，如果是，则显示错误占位图
      isError: false,
      // 初始化组件时，默认为加载中状态
      loading: true,
      // 不透明度，为了实现淡入淡出的效果
      opacity: 1,
      // 过渡时间，因为props的值无法修改，故需要一个中间值
      durationTime: this.duration,
      // 图片加载完成时，去掉背景颜色，因为如果是png图片，就会显示灰色的背景
      backgroundStyle: {},
    }
  },
  watch: {
    src: {
      immediate: true,
      handler(n) {
        if (!n) {
          // 如果传入null或者''，或者false，或者undefined，标记为错误状态
          this.isError = true
          this.loading = false
        } else {
          this.isError = false
        }
      },
    },
  },
  computed: {
    //Vuex 辅助state函数
    ...mapState(['ossPrefix']),

    // 图片容器
    wrapStyle() {
      let style = {}
      // 通过调用addUnit()方法，如果有单位，如百分比，px单位等，直接返回，如果是纯粹的数值，则加上rpx单位
      style.width = this.$u.addUnit(this.width)
      style.height = this.$u.addUnit(this.height)
      // 添加背景图片样式
      if (this.backgroundImage) {
        style.backgroundImage = `url(${this.backgroundImage})`
        style.backgroundSize = '100% 100%'
        style.backgroundRepeat = 'no-repeat'
        style.backgroundPosition = 'center'
      }
      if (this.isResize) {
        const params = this?.src?.split('?')?.[1] || ''
        const vars = params.split('&')
        const searchParams = {}
        vars.forEach((varStr) => {
          const [key, value] = varStr.split('=')
          searchParams[key] = value
        })
        const w = +(searchParams?.w || '')
        const h = +(searchParams?.h || '')
        if (!isNaN(w) && !isNaN(h) && w && h) {
          const width = parseInt(this.width)
          let height = (width / w) * h
          const { wratio, hratio } = this.resizeRatio
          if (wratio !== 'auto' && hratio !== 'auto') {
            const maxHeight = (width * wratio) / hratio
            const minHeight = (width * hratio) / wratio
            if (height > maxHeight) {
              height = maxHeight
            } else if (height < minHeight) {
              height = minHeight
            }
          }
          if (this.resizeUsePx) {
            style.height = `${height}px`
          } else {
            style.height = this.$u.addUnit(height)
          }
        }
      }
      // 如果是配置了圆形，设置50%的圆角，否则按照默认的配置值
      style.borderRadius = this.shape == 'circle' ? '50%' : this.$u.addUnit(this.borderRadius)
      // 如果设置圆角，必须要有hidden，否则可能圆角无效
      style.overflow = this.borderRadius > 0 || this.shape == 'circle' ? 'hidden' : 'visible'
      if (this.fade) {
        style.opacity = this.opacity
        style.transition = `opacity ${Number(this.durationTime) / 1000}s ease-in-out`
      }
      return style
    },

    // 获取加载图片（默认加载中跟加载失败统一用酒云网默认图标，如有变化，再做扩展）
    getLoadingImage() {
      return `https://images.vinehoo.com/vinehoomini/v3/occupy_img/new_img${this.loadingType}.png`
    },

    // 获取图片（返回半路径就补上oss域名前缀、返回全路径就直接返回图片地址）
    getImage() {
      if (typeof this.src !== 'string') return ''
      if (this.src.indexOf('http') == -1) {
        return this.ossPrefix + this.src
      }
      return this.src
    },
  },
  methods: {
    // 点击图片
    onClick() {
      this.$emit('click')
    },
    // 图片加载失败
    onErrorHandler(err) {
      this.loading = false
      this.isError = true
      this.$emit('error', err)
    },
    // 图片加载完成，标记loading结束
    onLoadHandler() {
      this.loading = false
      this.isError = false
      this.$emit('load')
      // 如果不需要动画效果，就不执行下方代码，同时移除加载时的背景颜色
      // 否则无需fade效果时，png图片依然能看到下方的背景色
      if (!this.fade) return this.removeBgColor()
      // 原来opacity为1(不透明，是为了显示占位图)，改成0(透明，意味着该元素显示的是背景颜色，默认的灰色)，再改成1，是为了获得过渡效果
      this.opacity = 0
      // 这里设置为0，是为了图片展示到背景全透明这个过程时间为0，延时之后延时之后重新设置为duration，是为了获得背景透明(灰色)
      // 到图片展示的过程中的淡入效果
      this.durationTime = 0
      // 延时50ms，否则在浏览器H5，过渡效果无效
      setTimeout(() => {
        this.durationTime = this.duration
        this.opacity = this.opacityProp
        setTimeout(() => {
          this.removeBgColor()
        }, this.durationTime)
      }, 50)
    },
    // 移除图片的背景色
    removeBgColor() {
      // 淡入动画过渡完成后，将背景设置为透明色，否则png图片会看到灰色的背景
      this.backgroundStyle = {
        backgroundColor: 'transparent',
      }
    },
  },
}
</script>

<style scoped lang="scss">
.vh-image-con {
  position: relative;
  transition: opacity 0.5s ease-in;
}

.vh-image {
  width: 100%;
  height: 100%;
}

.vh-image-loading,
.u-image-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  color: $u-tips-color;
  font-size: 46rpx;
}
.mf-card {
  background-color: #f7f7f7 !important;
  padding: 5px;
}
</style>
