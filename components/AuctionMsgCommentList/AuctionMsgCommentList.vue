<template>
  <view class="ptb-00-plr-24 pb-20">
    <view v-for="({ isRead, createTime, content, notice_data }, index) in list" :key="index" class="mt-20 ptb-28-plr-20 bg-ffffff b-rad-10">
      <view class="flex-sb-c">
        <view class="flex-c-c">
          <vh-image :loading-type="4" :src="userInfo.avatar_image" :width="64" :height="64" shape="circle" />
          <text class="ml-10 font-wei-500 font-28 text-6">{{ userInfo.nickname }}</text>
          <view class="p-rela d-flex ml-06">
            <image :src="ossIcon('/auction/lv_bg_80_28.png')" class="w-80 h-28" />
            <view style="top: -2rpx; left: 30rpx;" class="p-abso font-wei-500 font-24 text-e79a31 l-h-32">Lv{{ userInfo.user_level }}</view>
          </view>
        </view>
        <view class="flex-c-c">
          <view v-if="!isRead" class="mr-10 w-16 h-16 bg-e80404 b-rad-14"></view>
          <text class="font-24 text-9">{{ createTime }}</text>
        </view>
      </view>
      <view class="mt-20 font-wei-500 font-28 text-3 l-h-40 text-justify w-b-b-w">
        <text v-html="notice_data.title" v-if="notice_data.type === '评论'"></text>
        <template v-else>
          <text class="text-ff9127">@{{ notice_data.user_nickname }}</text>回复了你：{{ content }}
        </template>
      </view>
      <view class="mt-20 h-02 bg-eeeeee"></view>
      <view class="flex-sb-c mt-20" @click="onJump(notice_data)">
        <vh-image :loading-type="4" :src="notice_data.goods_image" :width="120" :height="120" :border-radius="6" />
        <view class="w-522">
          <view class="font-24 text-6 l-h-34">{{ notice_data.goods_title }}</view>
          <view class="flex-s-c mt-10 font-24 text-6 l-h-34">
            <template v-if="notice_data.goods_brand">
              <text>{{ notice_data.goods_brand }}</text>
              <text class="ml-06 mr-06 font-28 text-d8d8d8 l-h-40">/</text>
            </template>
            <template v-if="notice_data.goods_net_content">
              <text>{{ notice_data.goods_net_content }}ml</text>
              <text class="ml-06 mr-06 font-28 text-d8d8d8 l-h-40">/</text>
            </template>
            <text>{{ notice_data.goods_status }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState(['routeTable', 'userInfo'])
  },
  methods: {
    onJump (data) {
      const { goods_id } = data
      const { pHAuctionGoodsDetail } = this.routeTable
      if (goods_id) {
        this.jump.navigateTo(`${pHAuctionGoodsDetail}?id=${goods_id}`)
      }
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
