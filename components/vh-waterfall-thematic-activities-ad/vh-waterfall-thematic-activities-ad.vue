<template>
	<view class="" @click="onClick">
		<vh-image :src="item.image" :height="212" />
		<view class="ptb-18-plr-16">
			<view class="">
				<text class="font-24 font-wei text-3 l-h-30">{{item.title}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import adBuryDotMixin from '@/common/js/mixins/adBuryDotMixin'
	/**
	 * waterfall-thematic-activities-ad 专题活动广告
	 * @description 该组件一般用于拥有专题活动广告位
	 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
	 * @property {Object} item 专题活动广告的每一项
	 * @event {Function} click 点击组件时触发
	 * @example <vh-activity-ad />
	 */
	export default {
		name:'vh-waterfall-thematic-activities-ad',
		mixins: [adBuryDotMixin],
		
		props: {
			// 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
			from: {
				type: [String, Number],
				default: ''
			},
			
			// 专题活动广告每一项
			item: {
				type: Object,
				default() {
					return {}
				}
			},
		},
		methods: {
			onClick () {
				if (this.buryDotParams) {
					const { channel, region_id } = this.buryDotParams
					const genre = 3
					const button_id = this.item.id
					this.jump.h5JumpBD(this.item.modul_data.activity_url, channel, genre, region_id, button_id, this.from)
				} else {
					this.jump.h5Jump(this.item.modul_data.activity_url, this.from)
				}
			}
		}
	}
	
</script>

<style scoped></style>
