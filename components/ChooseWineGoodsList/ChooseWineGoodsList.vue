<template>
  <view>
    <view v-for="(item, index) in list" :key="index" style="background: linear-gradient(339deg, #CEDFFF 0%, #CDD3F3 100%);" class="mt-20 h-692 b-rad-10" @click="handleJump(`${routeTable.pgGoodsDetail}?id=${item.id}`, $vhFrom)">
      <view class="p-rela p-10 pb-0">
        <!-- transform: rotate(0deg); 兼容ios圆角无效 -->
        <view class="h-570 bg-ffffff b-rad-10 o-hid" style="transform: rotate(0deg);">
          <view class="p-rela">
            <vh-image :src="item.banner_img" :height="422" />
            <!-- <image v-if="item.special_activity_data && item.special_activity_data.title_map" class="p-abso top-0 z-99 w-p100 h-422" :src="item.special_activity_data.title_map" mode="aspectFill"></image> -->
          </view>
          <view class="mt-28 ptb-00-plr-14">
            <view class="font-wei-500 font-30 text-3 l-h-44 text-hidden" @longpress.stop="handleLongpress(item.title, $vhFrom)" @touchend="handleTouched($vhFrom)">{{ item.title }}</view>
            <view v-if="!item.is_deposit" class="mt-16 ptb-00-plr-04 font-24 text-9 l-h-36 text-right">
              <text>已售<text class="text-e80404">{{ item.purchased + item.vest_purchased }}</text></text>
              <text>/限量<text class="text-e80404">{{ item.limit_number }}</text></text>
              <text v-if="item.quota_rule && item.quota_rule.quota_number != '9999'">/限购<text class="text-e80404">{{ item.quota_rule.quota_number }}</text></text>
            </view>
          </view>
        </view>
        <view style="bottom: 2rpx;" class="p-abso left-0 flex-c-c">
          <image :src="ossIcon('/index/choose_wine_flag_90_54.png')" class="w-90 h-54"></image>
        </view>
      </view>
      <view class="flex-sb-c mt-28 ptb-00-plr-24">
        <view>
          <view v-if="item.is_hidden_price === 1 || [3, 4].includes(item.onsale_status)" class="font-wei-500 font-32 text-e80404 l-h-44">价格保密</view>
          <view v-else class="flex-c-c">
            <view class="font-wei-500 font-36 text-e80404 l-h-44"><text class="font-24">¥</text>{{ item.price }}</view>
            <view class="ml-16 font-24 text-ffffff l-h-36">¥<text class="text-dec-l-t">{{ item.market_price }}</text></view>
          </view>
        </view>
        <view class="p-rela w-232 h-52">
          <image :src="ossIcon('/index/choose_wine_btn_232_52.png')" class="p-abso w-p100 h-p100" />
          <view class="p-rela flex-c-c h-p100">
            <view class="flex-c-c w-84 h-p100">
              <image :src="ossIcon('/index/shopping_40.png')" class="w-40 h-40"></image>
            </view>
            <view class="flex-c-c w-148 h-p100 font-wei-500 font-28 text-ffffff l-h-40">去抢购</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  mixins: [longpressCopyMixin],
  computed: {
    ...mapState(['routeTable'])
  },
}
</script>

<style lang="scss" scoped>
</style>
