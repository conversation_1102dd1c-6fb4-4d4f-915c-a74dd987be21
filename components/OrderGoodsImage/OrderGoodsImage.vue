<template>
	<vh-image :loading-type="2" :src="goodsImg" :width="getArea.width" :height="getArea.height" :border-radius="6" />
</template>

<script>
	export default {
		props: {
			// 订单类型
			orderType: {
				type: [String, Number],
				default: 0
			},
			// 图片
			goodsImg: {
				type: String,
				default: ''
			}
		},
		computed: {
			getArea({ orderType }) {
				let wh = { 
					width: 246,
					height: 152
				}
				if( orderType === 11 ) {
					wh.width = 160
					wh.height = 160
				}
				return wh
			}
		}
	}
</script>

<style>
</style>