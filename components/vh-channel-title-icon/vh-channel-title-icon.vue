<template>
  <view v-if="getChannel !== '' && plate == 'orderConfirm'" class="flex-s-c">
    <view v-if="showTitle" class="font-32 font-wei text-3 l-h-40">{{ iconName.get(getChannel).title }}商品</view>
    <view :style="[iconStyle]" class="mt-04">{{ iconName.get(getChannel).iconText }}</view>
  </view>
  <text v-else-if="getChannel !== ''" class="">
    <text v-if="showTitle" class="font-32 font-wei text-3 l-h-44">{{ iconName.get(getChannel).title }}商品</text>
    <text :style="[iconStyle]">{{ iconName.get(getChannel).iconText }}</text>
  </text>
</template>

<script>
import judgeAppNewVersion from '@/common/js/utils/judgeAppNewVersion'
/**
 * channel-title-icon 每个频道的标题 icon
 * @description 该组件一般用于标题频道的显示 一般默认为一个icon加频道标题。
 * @property {String Numbr} channel 商品频道
 * @property {String Numbr} marketing-attribute 营销属性
 * @property {String Numbr} warehouse-type 仓库类型
 * @property {Boolean} show-title 是否显示title
 * @property {String Numbr} border-radius 圆角
 * @property {String Numbr} margin-left 左外边距
 * @property {String} padding 内边距
 * @property {String Numbr} font-size 字体大小
 * @property {Boolean} font-bold 字体是否加粗
 * @property {String} text-color 字体颜色
 * @example <vh-channel-title-icon></vh-channel-title-icon>
 */
export default {
  name: 'vh-channel-title-icon',
  props: {
    is_seckill: {
      type: [Number],
      default: 0,
    }, // 商品频道
    channel: {
      type: [String, Number],
      default: 0,
    },
    // 营销属性
    marketingAttribute: {
      type: String,
      default: '0',
    },
    // 仓库类型
    warehouseType: {
      type: [String, Number],
      default: '0',
    },
    // 是否显示title
    showTitle: {
      type: Boolean,
      default: false,
    },
    // 圆角
    borderRadius: {
      type: [String, Number],
      default: 6,
    },
    // 左外边距
    marginLeft: {
      type: [String, Number],
      default: 0,
    },
    // 内边距
    padding: {
      type: String,
      default: '0 10rpx',
    },
    // 字体大小
    fontSize: {
      type: [String, Number],
      default: 24,
    },
    // 字体是否加粗
    fontBold: {
      type: Boolean,
      default: true,
    },
    isNewYearTheme: {
      type: Boolean,
      default: false,
    },
    // 字体颜色
    textColor: {
      type: String,
      default: '#FFFFFF',
    },
    plate: {
      type: String,
      default: '',
    },
  },
  computed: {
    // 获取频道
    getChannel() {
      if (this.marketingAttribute.includes('1')) {
        //拼团
        return 101
      } else if (this.channel == 9) {
        if (this.warehouseType == 1) return 102
        else if (this.warehouseType == 2) return 103
        else return 1
      }
      return this.channel
    },

    // icon名称样式
    iconName() {
      //icon名称
      let isNewVersion = true
      if (this.$android) {
        isNewVersion = judgeAppNewVersion('9.1.8')
      } else if (this.$ios) {
        isNewVersion = judgeAppNewVersion('9.24')
      }
      let map = new Map([
        [
          0,
          {
            title: this.is_seckill ? '秒杀' : '闪购',
            iconText: this.is_seckill ? '秒杀' : '闪购',
            bgColor: this.is_seckill ? '#FDE451' : '#E80404',
            textColor: this.is_seckill ? '#E80404' : '#FFF',
          },
        ], //闪购
        [
          1,
          {
            title: this.isNewYear ? '年货节' : '现货速发',
            iconText: this.isNewYear ? '年货节' : '现货速发',
            bgColor: '#FF9127',
            textColor: '#FFF',
          },
        ],
        [2, { title: '跨境', iconText: '跨境', bgColor: '#734cd2', textColor: '#FFF' }], //跨境
        [3, { title: '尾货', iconText: '尾货', bgColor: '#FF9127', textColor: '#FFF' }], //尾货
        [4, { title: '兔头', iconText: '兔头', bgColor: '#FF9127', textColor: '#FFF' }], //兔头
        [11, { title: '拍卖', iconText: '拍卖', bgColor: '#F6B869', textColor: '#FFF' }], //拍卖
        [
          9,
          {
            title: this.isNewYear ? '年货节' : '现货速发',
            iconText: this.isNewYear ? '年货节' : '现货速发',
            bgColor: '#FF9127',
            textColor: '#FFF',
          },
        ],
        [101, { title: '拼团', iconText: '拼团', bgColor: '#FF9127', textColor: '#FFF' }], //拼团（前端默认频道为101 = 拼团商品 等同于 营销活动包含拼团商品）
        [
          102,
          {
            title: this.channel === 9 ? (this.isNewYear ? '年货节' : '现货速发') : '闪购',
            iconText: '3小时达',
            bgColor: '#17E6A1',
            textColor: '#fff',
          },
        ], //3小时达（前端默认频道为102 = 等同于 仓库类型等于1）
        [
          103,
          {
            title: this.channel === 9 ? (this.isNewYear ? '年货节' : '现货速发') : '闪购',
            iconText: isNewVersion ? '次日达' : '本地仓',
            bgColor: '#FAB005',
            textColor: '#fff',
          },
        ], //本地仓（前端默认频道为103 = 等同于 仓库类型等于2）
      ])
      return map
    },

    // icon图标样式
    iconStyle() {
      // 图标样式
      let style = {}
      console.log('909988---',this.getChannel, this.iconName.get(this.getChannel));
      
      const { bgColor, textColor } = this.iconName.get(this.getChannel)
      style.backgroundColor = bgColor
      style.borderRadius = this.borderRadius + 'rpx'
      style.marginLeft = this.marginLeft + 'rpx'
      style.padding = this.padding
      style.fontSize = this.fontSize + 'rpx'
      if (this.fontBold) style.fontWeight = 'bold'
      style.color = textColor
      if (this.warehouseType == 1 && this.channel == 9) {
        //三小时达
        style.color = '#000'
        style.fontWeight = 'bold'
      }
      return style
    },
  },
  mounted() {
    if (!this.isNewYearTheme) {
      this.secondConfig()
    }
  },
  data() {
    return {
      isNewYear: false,
    }
  },
  methods: {
    async secondConfig() {
      const res = await this.$u.api.secondConfig()
      if (res.data.isopen) {
        this.isNewYear = true
      }
    },
  },
}
</script>

<style scoped></style>
