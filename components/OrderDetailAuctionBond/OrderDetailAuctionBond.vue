<template>
	<view v-if="bond" class="bg-ffffff flex-sb-c b-rad-16 mt-20 mr-24 ml-24 ptb-32-plr-20">
		<text class="font-28 text-6">保证金（{{bondMsg}}）</text>
		<text class="font-28 text-3"><text class="font-22">¥</text>{{ bond }}</text>
	</view>
</template>

<script>
	export default {
		props: {
			bond: {
				type: [Number, String],
				default: ''
			},
			bondMsg: {
				type: String,
				default: ''
			}
		}
	}
</script>
