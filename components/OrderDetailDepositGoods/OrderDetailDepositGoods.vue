<template>
	<view class="p-rela z-01 bg-ffffff b-rad-10 mt-20 mr-24 ml-24 pt-24">
		<view class="pr-20 pb-24 pl-28" v-for="(item, index) in orderInfo.goodsInfo" :key="index" @click.stop="jump.appAndMiniJump(1, `${$routeTable.pgGoodsDetail}?id=${item.period}`, $vhFrom, 1)">
			<view class="d-flex j-sb">
				<vh-image :loading-type="2" :src="item.goods_img" :width="246" :height="152" :border-radius="6" />
				<view class="flex-1 flex-sb-n-c ml-12">
					<view class="">
						<view class="text-hidden-2 font-24 text-0 l-h-34">{{item.goods_title}}</view>
						<view class="">
							<text class="bg-f5f5f5 ptb-04-plr-18 b-rad-08 mt-08 font-22 text-9">{{item.package_name}}</text>
						</view>
					</view>
					<view class="mt-04 flex-sb-e">
						<text class="font-22 text-6">x{{item.order_qty}}</text>
						
						<view class="">
							<view v-if="( !orderInfo.countdown && orderInfo.status === 1 ) || orderInfo.status === 3" class="flex-e-c mt-18">
								<view class="font-20 text-3">已膨胀至：</view>
								<view class="font-32 font-wei text-3">
									<text class="font-22">¥</text>
									<text>{{ orderInfo.deposit_coupon_value }}</text>
								</view>
							</view>
							<view v-else class="flex-e-c mt-18">
								<view class="font-20 text-3">订金：</view>
								<view class="font-32 font-wei text-3">
									<text class="font-22">¥</text>
									<text>{{ orderInfo.payment_amount }}</text>
								</view>
							</view>
						</view>
						
						
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			orderInfo: {
				type: Object,
				default: () => ({})
			}
		}
	}
</script>