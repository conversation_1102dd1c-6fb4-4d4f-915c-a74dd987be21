<template>
  <view class="p-rela vh-waterfall" :style="{ height: `${height}px` }">
    <view v-for="item in wflist" :key="item[idKey]" :style="[{ width: `${itemWidth}px` }, item.wfitemStyle]" :class="['wf-item', itemClazz]">
      <template v-if="type === MVhWaterfallType.Miaofa">
        <SecWaterFallFirstItem v-if="item.type === MSecondsWfitemType.AdSwiper" :item="item"></SecWaterFallFirstItem>
        <SecWaterFallProGoods v-else-if="item.type === MSecondsWfitemType.Goods" :item="item" @changePendInsertId="onChangePendInsertId" @changeShowSecWfitemMaskId="onChangeShowSecWfitemMaskId"></SecWaterFallProGoods>
        <SecWaterFallProAuctionGoods v-else-if="item.type === MSecondsWfitemType.Auction" :item="item"></SecWaterFallProAuctionGoods>
        <SecWaterFallProWineParty v-else-if="item.type === MSecondsWfitemType.Party" :item="item"></SecWaterFallProWineParty>
        <SecWaterFallContWineNews v-else-if="item.type === MSecondsWfitemType.News" :item="item" @changeShowSecWfitemMaskId="onChangeShowSecWfitemMaskId"></SecWaterFallContWineNews>
        <SecWaterFallContWineComment v-else-if="item.type === MSecondsWfitemType.Wine" :item="item"></SecWaterFallContWineComment>
        <SecWaterFallAdInterested v-else-if="item.type === MSecondsWfitemType.Interested" :item="item" @remove="remove"></SecWaterFallAdInterested>
        <SecWaterFallAdGuessLike v-else-if="item.type === MSecondsWfitemType.GuessLike" :item="item"></SecWaterFallAdGuessLike>
        <SecWaterfallInvestigates v-else-if="item.type === MSecondsWfitemType.Investigates" :item="item" @remove="remove"></SecWaterfallInvestigates>
        <SecWaterFallMask v-if="item[idKey] === secWfitemMaskId" :item="item" @remove="remove" @changeShowSecWfitemMaskId="onChangeShowSecWfitemMaskId"></SecWaterFallMask>
      </template>
    </view>
  </view>
</template>

<script>
import { MVhWaterfallType, MSecondsWfitemType } from '@/common/js/utils/mapperModel'
export default {
  props: {
    type: {
      type: String,
      default: MVhWaterfallType.Miaofa
    },
    value: {
      type: Array,
      default: () => []
    },
    addTime: {
      type: Number,
      default: 10
    },
    idKey: {
      type: String,
      default: 'id'
    },
    itemStyle: {
      type: Object,
      default: () => ({
        width: 346,
        marginRight: 10,
        marginBottom: 10
      })
    }
  },
  data: () => ({
    MVhWaterfallType,
    MSecondsWfitemType,
    isCanRender: true,
    isStopRender: false,
    wflist: [],
    index: 0,
    timer: null,
    lheight: 0,
    rheight: 0,
    height: 0,
    selector: '.vh-waterfall .wf-item',
    transition: 'all .2s',
    secWfitemMaskId: ''
  }),
  computed: {
    leftSideLeft () {
      return 0
    },
    rightSideLeft ({ itemStyle }) {
      const { width, marginRight } = itemStyle
      if (`${marginRight}`.includes('px')) {
        return uni.upx2px(width) + (+marginRight.replace('px', ''))
      }
      return uni.upx2px(width + marginRight)
    },
    itemWidth ({ itemStyle }) {
      return uni.upx2px(itemStyle.width)
    },
    itemMarginBottom ({ itemStyle }) {
      const { marginBottom } = itemStyle
      return uni.upx2px(marginBottom)
    },
    itemClazz ({ type }) {
      switch (type) {
        case MVhWaterfallType.Miaofa:
          return 'bg-ffffff b-rad-10 o-hid t-trans-3d-1'
        default:
          return ''
      }
    }
  },
  watch: {
    'value.length': {
      handler (newLength, oldLength = 0) {
        if (!newLength) {
          this.isCanRender = true
          this.$emit('renderEnd')
          const { wflist, index, lheight, rheight, height } = this.$options.data()
          this.wflist = wflist
          this.index = index
          this.lheight = lheight
          this.rheight = rheight
          this.height = height
          return
        }
        if (newLength > oldLength && this.isCanRender) {
          this.$emit('renderStart')
          this.renderWfitem()
        }
      },
      immediate: true
    }
  },
  methods: {
    renderWfitem () {
      this.isCanRender = false
      const item = this.value[this.index]
      if (!item || this.isStopRender) {
        this.isCanRender = true
        this.$emit('renderEnd')
        return
      }
      this.wflist.push(Object.assign({}, item, { wfitemStyle: { visibility: 'hidden' } }))
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this)
        query.selectAll(this.selector).boundingClientRect(data => {
          const { height: wfitemHeight } = data[this.index]
          const wfitemIndex = this.index
          let wfitemLeft, wfitemTop
          if (this.lheight <= this.rheight) {
            wfitemLeft = this.leftSideLeft
            wfitemTop = this.lheight
            this.lheight += wfitemHeight
            this.lheight += this.itemMarginBottom
          } else {
            wfitemLeft = this.rightSideLeft
            wfitemTop = this.rheight
            this.rheight += wfitemHeight
            this.rheight += this.itemMarginBottom
          }
          const wfitemStyle = {
            position: 'absolute',
            transition: this.transition,
            left: `${wfitemLeft}px`,
            top: `${wfitemTop}px`,
            visibility: 'visible'
          }
          this.wflist.splice(this.index, 1, Object.assign({}, item, { wfitemIndex, wfitemHeight, wfitemLeft, wfitemTop, wfitemStyle }))
          this.height = Math.max(this.lheight, this.rheight)
          
          this.timer && clearTimeout(this.timer)
          this.timer = setTimeout(() => {
            this.index++
            this.renderWfitem()
          }, this.addTime)
        }).exec()
      })
    },
    remove (id) {
      const index = this.wflist.map(item => item[this.idKey]).indexOf(id)
      const { wfitemLeft, wfitemTop } = this.wflist[index]
      // 计算lheight、rheight
      // 右侧向后查找第一个左侧的item，找不到则给默认的wfitemLeft、wfitemTop
      // 左侧向后查找第一个右侧的item，找不到则给默认的wfitemLeft、wfitemTop
      if (wfitemLeft) {
        this.rheight = wfitemTop
        let i = index + 1
        while (true) {
          const { wfitemLeft, wfitemTop } = this.wflist[i] || { wfitemLeft: this.leftSideLeft, wfitemTop: this.lheight }
          if (!wfitemLeft) {
            this.lheight = wfitemTop
            break
          }
          i++
        }
      } else {
        this.lheight = wfitemTop
        let i = index
        while (true) {
          const { wfitemLeft, wfitemTop } = this.wflist[i] || { wfitemLeft: this.rightSideLeft, wfitemTop: this.rheight }
          if (wfitemLeft) {
            this.rheight = wfitemTop
            break
          }
          i++
        }
      }
      this.wflist.splice(index, 1)
      this.wflist.forEach(item => {
        item.wfitemStyle.transition = ''
      })
      this.index--
      this.$emit('input', this.wflist)
      for (let i = index; i < this.index; i++) {
        const item = this.wflist[i]
        const { wfitemHeight } = item
        if (this.lheight <= this.rheight) {
          item.wfitemLeft = this.leftSideLeft
          item.wfitemTop = this.lheight
          this.lheight += wfitemHeight
          this.lheight += this.itemMarginBottom
        } else {
          item.wfitemLeft = this.rightSideLeft
          item.wfitemTop = this.rheight
          this.rheight += wfitemHeight
          this.rheight += this.itemMarginBottom
        }
        Object.assign(item.wfitemStyle, { transition: this.transition ,left: `${item.wfitemLeft}px`, top: `${item.wfitemTop}px` })
      }
      this.height = Math.max(this.lheight, this.rheight)
    },
    async insert (id, insertItem) {
      // 插入测试
      // const res = await this.$u.api.flashGoodsList({ page: 2, limit: 10, sort_type: 'onsale_time', order: 'asc' })
      // const findItem = res.data.list.find(item => !this.value.map(item => item.id).includes(item.id))
      // if (!findItem) return
      // insertItem = findItem
      const index = this.wflist.map(item => item[this.idKey]).indexOf(id)
      const { wfitemLeft, wfitemTop } = this.wflist[index]
      // 计算插入的位置
      // 右侧向后查找第一个右侧的item，找不到则给默认的wfitemLeft
      // 左侧向后查找第一个左侧的item，找不到则给默认的wfitemLeft
      let insertIndex
      if (wfitemLeft) {
        let i = index + 1
        while (true) {
          const { wfitemLeft } = this.wflist[i] || { wfitemLeft: this.rightSideLeft }
          if (wfitemLeft) {
            insertIndex = i
            break
          }
          i++
        }
      } else {
        let i = index + 1
        while (true) {
          const { wfitemLeft } = this.wflist[i] || { wfitemLeft: this.leftSideLeft }
          if (!wfitemLeft) {
            insertIndex = i
            break
          }
          i++
        }
      }
      // 计算lheight、rheight
      // 插入位置在最后则无需计算
      // 插入位置在右侧，向后查找第一个左侧的item
      // 插入位置在左侧，向后查找第一个右侧的item
      if (insertIndex < this.index) {
        const { wfitemLeft: originWfitemLeft, wfitemTop: originWfitemTop } = this.wflist[insertIndex]
        if (originWfitemLeft) {
          this.rheight = originWfitemTop
          let i = insertIndex + 1
          while (true) {
            const { wfitemLeft, wfitemTop } = this.wflist[i]
            if (!wfitemLeft) {
              this.lheight = wfitemTop
              break
            }
            i++
          }
        } else {
          this.lheight = originWfitemTop
          let i = insertIndex + 1
          while (true) {
            const { wfitemLeft, wfitemTop } = this.wflist[i]
            if (wfitemLeft) {
              this.rheight = wfitemTop
              break
            }
            i++
          }
        }
      }
      const wfitemStyle = {
        position: 'absolute',
        transition: this.transition,
        left: `${wfitemLeft}px`,
        top: `${wfitemTop}px`
      }
      const assignInsertItem = Object.assign({}, insertItem, { wfitemIndex: -1, wfitemHeight: 0, wfitemLeft, wfitemTop, wfitemStyle  })
      this.wflist.splice(insertIndex, 0, assignInsertItem)
      this.wflist.forEach(item => {
        item.wfitemStyle.transition = ''
      })
      this.index++
      this.$emit('input', this.wflist)
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this)
        query.selectAll(this.selector).boundingClientRect(data => {
          for (let i = insertIndex; i < this.index; i++) {
            const { height: wfitemHeight } = data[i]
            const item = this.wflist[i]
            const wfitemIndex = i
            let wfitemLeft, wfitemTop
            if (this.lheight <= this.rheight) {
              wfitemLeft = this.leftSideLeft
              wfitemTop = this.lheight
              this.lheight += wfitemHeight
              this.lheight += this.itemMarginBottom
            } else {
              wfitemLeft = this.rightSideLeft
              wfitemTop = this.rheight
              this.rheight += wfitemHeight
              this.rheight += this.itemMarginBottom
            }
            const wfitemStyle = {
              position: 'absolute',
              transition: this.transition,
              left: `${wfitemLeft}px`,
              top: `${wfitemTop}px`
            }
            Object.assign(item, { wfitemIndex, wfitemHeight, wfitemLeft, wfitemTop, wfitemStyle })
          }
          this.height = Math.max(this.lheight, this.rheight)
        }).exec()
      })
    },
    onChangePendInsertId (id) {
      this.$emit('changePendInsertId', id)
    },
    onChangeShowSecWfitemMaskId (id) {
      this.secWfitemMaskId = id
      console.log('secWfitemMaskId', id)
    },
    startRender () {
      this.isStopRender = false
      this.renderWfitem()
      return this.isCanRender
    },
    stopRender () {
      this.isStopRender = true
    },
  },
}
</script>