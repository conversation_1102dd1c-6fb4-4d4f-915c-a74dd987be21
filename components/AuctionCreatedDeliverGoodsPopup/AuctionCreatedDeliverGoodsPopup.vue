<template>
  <u-popup :value="value" :mode="mode" :mask-close-able="maskCloseAble" :width="width" :height="height" border-radius="20" @input="onInput">
    <view v-if="step === 1">
      <view class="pt-48 h-148 font-wei-600 font-32 text-3 l-h-44 text-center">请选择上门取件地址</view>
      <view class="ptb-00-plr-24">
        <view class="h-180 bg-f5f5f5 b-rad-10">
          <view class="flex-sb-c ptb-00-plr-24 h-p100" @click="jump.navigateTo(`${$routeTable.pEAddressManagement}?comeFrom=99`)">
            <view>
              <template v-if="addressInfo.id">
                <view class="flex-s-c">
                  <view v-if="addressInfo.is_default" class="flex-c-c mr-16 w-60 h-30 font-wei-500 font-20 text-ffffff bg-e80404 b-rad-04">默认</view>
                  <view v-if="addressInfo.label" class="flex-c-c mr-20 w-60 h-30 font-wei-500 font-20 text-ffffff bg-2e7bff b-rad-04">{{ addressInfo.label }}</view>
                  <view class="font-wei-500 font-28 text-3 l-h-40">{{ addressInfo.consignee }}</view>
                  <view class="ml-16 font-28 text-9 l-h-40">{{ addressInfo.consignee_phone }}</view>
                </view>
                <view class="mt-08 font-24 text-6 l-h-34">{{ addressInfo.province_name }} {{ addressInfo.city_name }} {{ addressInfo.town_name }} {{ addressInfo.address }}</view>
              </template>
            </view>
            <image :src="ossIcon('/after_sale_detail/arrow_right_12x20.png')" class="w-12 h-20">
          </view>
        </view>
      </view>
      <view class="p-rela h-188">
        <view class="p-abso left-0 bottom-0 flex-sb-c ptb-00-plr-36 w-p100 h-104 bg-ffffff b-sh-00021200-022">
          <button class="vh-btn flex-c-c w-308 h-64 font-28 text-9 bg-eeeeee b-rad-32" @click="onInput(false)">取消</button>
          <button class="vh-btn flex-c-c w-308 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="[disabled ? 'bg-fce0e0' : 'bg-e80404']" @click="onConfirmSenderAddress">下一步</button>
        </view>
      </view>
    </view>
    <view v-else-if="step === 2">
      <view class="pt-48 h-116">
        <view class="p-rela flex-c-c">
          <view class="font-wei-600 font-32 text-3 l-h-44">请选择上门取件地址</view>
          <view class="p-abso right-48 font-28 text-3 l-h-40" @click="onConfirmPickUpTimeIndex">确定</view>
        </view>
      </view>
      <view class="d-flex h-598">
        <view class="w-244 h-p100 bg-f5f5f5 o-scr-y">
          <view class="bg-ffffff">
            <view
              v-for="(item, index) in pickUpList"
              :key="index"
              class="flex-c-c h-90 font-28 text-3 l-h-40"
              :class="[index ? 'bt-s-02-transp' : '', index === pikcUpDayIndex ? 'bg-ffffff' : 'bg-f5f5f5', index === pikcUpDayIndex + 1 ? 'b-tr-rad-10' : '', index === pikcUpDayIndex - 1 ? 'b-br-rad-10' : '']"
              @click="onPickUpDayIndexChange(index)"
            >{{ item.day }}</view>
            <view class="h-90 bg-f5f5f5" :class="[pickUpList.length === pikcUpDayIndex + 1 ? 'b-tr-rad-10' : '']"></view>
          </view>
        </view>
        <view class="flex-1 ptb-00-plr-48 h-p100 o-scr-y">
          <view
            v-for="(item, index) in pickUpTimeList"
            :key="index"
            class="p-rela flex-c-c h-90"
            :class="[index ? 'bt-s-02-eeeeee' : '']"
            @click="pickUpTimeIndex = index"
          >
            <view class="font-28 l-h-40" :class="pickUpTimeIndex === index ? 'text-e80404' : 'text-3'">{{ item.timeRange }}</view>
            <image v-if="pickUpTimeIndex === index" :src="ossIcon('/auction/radio_h_32.png')" class="p-abso right-0 w-32 h-32"></image>
          </view>
        </view>
      </view>
    </view>
    <view v-else-if="step === 3">
      <view class="pt-48 h-104 font-wei-600 font-32 text-3 l-h-44 text-center">是否需要保价</view>
      <view class="ptb-00-plr-48 h-240">
        <view class="flex-sb-c h-108" @click="insured = 0">
          <view class="font-28 text-3 l-h-40">不保价</view>
          <image :src="ossIcon(`/auction/radio${insured === 0 ? '_h' : '' }_32.png`)" class="w-32 h-32"></image>
        </view>
        <view class="h-02 bg-eeeeee"></view>
        <view class="flex-sb-c h-108" @click="insured = 1">
          <view class="flex-c-c">
            <view class="font-28 text-3 l-h-40">要保价</view>
            <view class="ml-10 font-20 text-9 l-h-28">具体保价费用请与快递人员沟通确认。</view>
          </view>
          <image :src="ossIcon(`/auction/radio${insured === 1 ? '_h' : ''}_32.png`)" class="w-32 h-32"></image>
        </view>
      </view>
      <view class="p-rela h-128">
        <view class="p-abso left-0 bottom-0 flex-sb-c ptb-00-plr-36 w-p100 h-104 bg-ffffff b-sh-00021200-022">
          <button class="vh-btn flex-c-c w-308 h-64 font-28 text-9 bg-eeeeee b-rad-32" @click="step--">取消</button>
          <button class="vh-btn flex-c-c w-308 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="[disabled ? 'bg-fce0e0' : 'bg-e80404']" @click="onConfirmInsured">确认</button>
        </view>
      </view>
    </view>
    <view v-else-if="step === 4" class="p-rela wh-p100">
      <image :src="ossIcon('/auction/popup_bg_552_392.png')" class="p-abso wh-p100" />
      <view class="p-rela pt-84">
        <view class="font-wei-500 font-32 text-3 l-h-44 text-center">已完成</view>
        <view class="mt-24 font-26 text-6 l-h-36 text-center">您已完成预约，请注意接听电话！</view>
        <view class="mt-78 h-02 bg-dedede"></view>
        <view class="mt-32 font-wei-500 font-28 text-e80404 l-h-40 text-center" @click="onKnow">知道了</view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    auctionGoods: {
      type: Object,
      default: () => ({})
    }
  },
  data: () => ({
    step: 1,
    defaultAddressInfo: {},
    addressInfo: {},
    pickUpList: [],
    pikcUpDayIndex: 0,
    pickUpTimeIndex: '',
    insured: ''
  }),
  computed: {
    ...mapState(['addressInfoState']),
    mode ({ step }) {
      return step === 4 ? 'center' : 'bottom'
    },
    maskCloseAble ({ step }) {
      return step !== 4
    },
    width ({ step }) {
      return step === 4 ? '552rpx' : '100%'
    },
    height ({ step }) {
      return step === 4 ? '392rpx' : 'auto'
    },
    disabled ({ step, addressInfo, pickUpTimeIndex, insured }) {
      switch (step) {
        case 1:
          return !addressInfo.id
        case 2:
          return pickUpTimeIndex === this.$options.data().pickUpTimeIndex
        case 3:
          return insured === this.$options.data().insured
        default:
          return false
      }
    },
    pickUpTimeList ({ pickUpList, pikcUpDayIndex }) {
      return pickUpList[pikcUpDayIndex]?.timeList || []
    }
  },
  watch: {
    value (newVal) {
      if (newVal) {
        this.step = 1
        const { status, seller_province_id, seller_province_name, seller_city_id, seller_city_name, seller_district_id, seller_district_name, seller_address, seller_consignee, seller_consignee_phone } = this.auctionGoods
        if (status === 9999) {
          this.addressInfo = {
            id: -1,
            province_id: seller_province_id,
            province_name: seller_province_name,
            city_id: seller_city_id,
            city_name: seller_city_name,
            town_id: seller_district_id,
            town_name: seller_district_name,
            address: seller_address,
            consignee: seller_consignee,
            consignee_phone: seller_consignee_phone,
          }
        } else {
          this.addressInfo = this.defaultAddressInfo
        }
      }
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    initAddressInfo () {
      if (Object.keys(this.addressInfoState).length) {
        this.addressInfo = this.addressInfoState
      } else if (!this.addressInfo.id) {
        this.loadAddressInfo()
      }
    },
    async loadAddressInfo () {
      const res = await this.$u.api.addressList()
      const list = res?.data?.list || []
      if (list.length) {
        const findDefaultItem = list.find(item => item.is_default)
        if (findDefaultItem) {
          this.addressInfo = findDefaultItem
        } else {
          this.addressInfo = list[0]
        }
        this.defaultAddressInfo = this.addressInfo
      }
    },
    onConfirmSenderAddress () {
      if (this.disabled) return
      this.feedback.loading({ title: '' })
      const { province_name: senderProvince, city_name: senderCity, town_name: senderDistrict, address: senderDetailAddress } = this.addressInfo
      const { province_name: receiverProvince, city_name: receiverCity, district_name: receiverDistrict, address: receiverDetailAddress } = this.auctionGoods
      const params = { senderProvince, senderCity, senderDistrict, senderDetailAddress, receiverProvince, receiverCity, receiverDistrict, receiverDetailAddress }
      this.$u.api.getAuctionPickUpList(params).then(res => {
        const { pikcUpDayIndex, pickUpTimeIndex } = this.$options.data()
        this.pikcUpDayIndex = pikcUpDayIndex
        this.pickUpTimeIndex = pickUpTimeIndex
        this.pickUpList = res?.data || []
        this.step++
      })
    },
    onPickUpDayIndexChange (index) {
      this.pickUpTimeIndex = this.$options.data().pickUpTimeIndex
      this.pikcUpDayIndex = index
    },
    onConfirmPickUpTimeIndex () {
      if (this.disabled) return
      this.insured = this.$options.data().insured
      this.step++
    },
    onConfirmInsured () {
      if (this.disabled) return
      const { order_id } = this.auctionGoods
      const { province_id, province_name, city_id, city_name, town_id: district_id, town_name: district_name, address, consignee, consignee_phone } = this.addressInfo
      const pickUpItem = this.pickUpList[this.pikcUpDayIndex]
      const pickUpTimeItem = pickUpItem.timeList[this.pickUpTimeIndex]
      const pickupStartTime = `${pickUpItem.day} ${pickUpTimeItem.startTime}`
      const pickupEndTime = `${pickUpItem.day} ${pickUpTimeItem.endTime}`
      const params = {
        order_id,
        province_id,
        province_name,
        city_id,
        city_name,
        district_id,
        district_name,
        address,
        consignee,
        consignee_phone,
        pickupStartTime,
        pickupEndTime,
        remark: '个人拍品发货',
        desp: '个人拍品发货',
        isGuaranteeValue: this.insured
      }
      this.$u.api.deliverPersonGoods(params).then(() => {
        this.step++
      })
    },
    onKnow () {
      this.$emit('reload')
      this.onInput(false)
    }
  },
}
</script>