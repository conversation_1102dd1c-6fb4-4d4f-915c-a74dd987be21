<template>
  <view v-if="styleOneCard || styleTwoCard" class="mfcard ptb-00-plr-24">
    <view class="mfcard__inner p-20">
      <view v-if="styleOneCard">
        <view class="flex-sb-c">
          <text class="font-wei-600 font-42 text-ffe9cd l-h-58">{{ styleOneCard.card_name }}</text>
          <image :src="ossIcon('/miaofa/more_108_40.png')" class="w-108 h-40" @click="jump.appAndMiniJumpBD(2, `${routeTable.pAMoreThanWine}?id=${styleOneCard.id}`, 4, 3, 303000, styleOneCard.id, $vhFrom)"></image>
        </view>
        <view class="mfcard__one-list d-flex a-center mt-20 h-302 bg-ffffff b-rad-10 o-scr-x">
          <view v-for="(item, index) in styleOneCard.card_extend_detail" :key="item.id" class="p-rela w-184 h-270 ml-10 flex-shrink" @click="jump.appAndMiniJumpBD(1, `${routeTable.pgGoodsDetail}?id=${item.id}`, 4, 2, 304000, item.id, $vhFrom)">
            <image :src="ossIcon('/miaofa/card_bg_184_270.png')" class="p-abso w-p100 h-p100"></image>
            <image v-if="noIconList[index]" :src="ossIcon(noIconList[index])" class="p-abso w-60 h-38 top-0 left-0 z-01"></image>
            <view class="p-rela pt-10">
              <view class="w-164 mtb-00-mlr-auto">
                <vh-image :loading-type="2" :src="item.product_img" :width="164" :height="164" :border-radius="10" />
                <view class="mt-16 font-20 text-ffffff l-h-30 text-hidden-1">{{ item.title }}</view>
                <view class="flex-sb-c mt-04">
                  <text v-if="item.is_hidden_price == 1 || [3, 4].includes(item.onsale_status)" class="font-wei-500 font-28 text-ffffff l-h-34">价格保密</text>
                  <text class="text-ffffff l-h-34">
                    <text class="font-18">¥</text>
                    <text class="font-wei-500 font-28">{{ item.price }}</text>
                  </text>
                  <image :src="ossIcon('/miaofa/shop_34.png')" class="w-34 h-34" @click.stop="$emit('openPackPop', item)"></image>
                </view>
              </view>
            </view>
          </view>
          <view class="w-16 h-270 flex-shrink"></view>
        </view>
      </view>
      <view v-if="styleTwoCard" :class="{ 'mt-18': styleOneCard }">
        <view>
          <text class="font-wei-600 font-42 text-ffe9cd l-h-58">{{ styleTwoCard.card_name }}</text>
        </view>
        <view class="mt-18 pt-16 h-176 bg-ffffff b-rad-10">
          <swiper autoplay :interval="3 * 1000" circular indicator-dots class="h-p100">
            <swiper-item v-for="(list, index) in swiperList" :key="index" class="h-132">
              <div class="flex-sb-c ptb-00-plr-16">
                <div v-for="item in list" :key="item.id" class="mfcard__swiper-item p-rela w-310 h-132 b-rad-10" @click="jump.appAndMiniJumpBD(1, `${routeTable.pgGoodsDetail}?id=${item.id}`, 4, 2, 304000, item.id, $vhFrom)">
                  <view class="p-rela flex-sb-c p-10 h-p100">
                    <vh-image :loading-type="2" :src="item.product_img" :width="112" :height="112" :border-radius="10" />
                    <view class="flex-1 d-flex flex-column j-sb ml-10 h-p100">
                      <view class="font-20 text-ffffff l-h-30 text-hidden-2">{{ item.title }}</view>
                      <text v-if="item.is_hidden_price == 1 || [3, 4].includes(item.onsale_status)" class="font-wei-500 font-28 text-ffffff l-h-30">价格保密</text>
                      <view v-else class="l-h-30">
                        <text class="text-ffffff">
                          <text class="font-18">¥</text>
                          <text class="font-wei-500 font-28">{{ item.price }}</text>
                        </text>
                        <text class="ml-10 font-20 text-ffe0d0 text-dec-l-t">¥{{ item.market_price }}</text>
                      </view>
                    </view>
                  </view>
                </div>
              </div>
            </swiper-item>
          </swiper>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    noIconList: ['/miaofa/no1_60_38.png', '/miaofa/no2_60_38.png', '/miaofa/no3_60_38.png']
  }),
  computed: {
    ...mapState(['routeTable']),
    styleOneCard ({ list }) {
      return list.find(item => item.style === 0 && item.card_extend_detail && item.card_extend_detail.length)
    },
    styleTwoCard ({ list }) {
      return list.find(item => item.style === 1 && item.card_extend_detail && item.card_extend_detail.length)
    },
    swiperList ({ styleTwoCard }) {
      if (!styleTwoCard) return []
      const list = styleTwoCard.card_extend_detail
      const num = Math.ceil(list.length / 2)
      return Array(num).fill('').map((_, index) => list.slice(index * 2, (index + 1) * 2))
    }
  }
}
</script>

<style lang="scss" scoped>
  .mfcard {
    &__inner {
      @include iconBg('/miaofa/card_bg_702_688.png', 100%);
    }

    &__one-list {
      > view {
        &:first-of-type {
          margin-left: 16rpx;
        }
      }
    }

    &__swiper-item {
      @include iconBg('/miaofa/card_310_132.png', 310rpx 132rpx);
    }

    ::v-deep {
      .uni-swiper {
        &-dots {
          bottom: 10rpx;
        }

        &-dot {
          @include size(8rpx);
          background: #E0E0E0;
          margin-right: 12rpx;

          &-active {
            background: #FF9127;
          }
        }
      }
    }
  }
</style>
