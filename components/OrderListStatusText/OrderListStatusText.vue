<template>
	<text v-if="Object.keys(this.getStatus).includes(this.item.status + '')" class="font-28" :class="getStatus[item.status].textClass">{{ getStatus[item.status].text }}</text>
	<!-- <text v-if="item.status == 0" class="font-28 text-e80404">待支付</text>
	<text v-if="item.status == 1" class="font-28 text-e80404">待发货</text>
	<text v-if="item.status == 2" class="font-28 text-3">待收货</text>
	<text v-if="item.status == 3" class="font-28 text-3">已完成</text>
	<text v-if="item.status == 4" class="font-28 text-6">交易关闭</text>
	<text v-if="item.status == 5" class="font-28 text-e80404">拼团中</text>
	<text v-if="item.status == 6" class="font-28 text-ff9127">已暂存</text>
	<text v-if="item.status == 7" class="font-28 text-e80404">处理中</text>
	<text v-if="item.status == 8" class="font-28 text-6">退款成功</text> -->
</template>

<script>
	export default{
		name: "OrderListStatusText",
		
		props: {
			// 订单信息
			item : {
				type: Object,
				default: () => ({})
			},
		},
		
		computed: {
			getStatus() {
				return {
					0 : { textClass: 'text-e80404', text: '待支付'},
					1 : { textClass: 'text-e80404', text: '待发货'},
					2 : { textClass: 'text-e80404', text: '待收货'},
					3 : { textClass: 'text-3', text: '已完成'},
					4 : { textClass: 'text-6', text: '交易关闭'},
					5 : { textClass: 'text-e80404', text: '拼团中'},
					6 : { textClass: 'text-ff9127', text: '已暂存'},
					7 : { textClass: 'text-e80404', text: '处理中'},
					8 : { textClass: 'text-6', text: '退款成功'}
				}
			}
		}
	}
</script>

<style>
</style>