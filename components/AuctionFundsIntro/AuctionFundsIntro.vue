<template>
  <u-popup :value="value" mode="center" width="624rpx" height="700rpx" border-radius="20" @input="onInput">
    <view class="p-rela w-624 h-700">
      <image :src="ossIcon('/auction/popup_bg_624_700.png')" class="p-abso w-624 h-700" />
      <view class="p-rela d-flex flex-column h-p100">
        <view class="flex-1 ptb-00-plr-74 pt-60">
          <template v-if="isNew">
            <template v-if="isSeller">
              <view class="font-wei-600 font-32 text-3 l-h-44">什么是总消费？</view>
              <view class="font-28 text-6 l-h-48">在酒云拍卖所购买的全部拍品消费总和</view>
              <view class="mt-10 font-wei-600 font-32 text-3 l-h-44">什么是保证金？</view>
              <view class="font-28 text-6 l-h-48">卖家缴纳委托保证金之后，才可以委托平台进行拍卖</view>
              <view class="font-28 text-6 l-h-48">若竞拍不成功，并且放弃拍卖，保证金将原路退回</view>
              <view class="font-28 text-6 l-h-48">若竞拍成功，并且买家确认收货，保证金也原路退回</view>
              <view class="font-28 text-6 l-h-48">若竞拍成功，卖家存在违规行为，保证金将扣除</view>
            </template>
            <template v-else>
              <view class="font-wei-600 font-32 text-3 l-h-44">什么是总消费？</view>
              <view class="mt-12 font-28 text-6 l-h-48">在酒云拍卖所购买的全部拍品消费总和</view>
              <view class="mt-32 font-wei-600 font-32 text-3 l-h-44">什么是保证金？</view>
              <view class="mt-12 font-28 text-6 l-h-48">买家缴纳保证金后才可以参与竞拍，</view>
              <view class="mt-12 font-28 text-6 l-h-48">-若竞拍不成功，保证金将原路返还；</view>
              <view class="mt-12 font-28 text-6 l-h-48">-若竞拍成功，且买家在48小时内支付货款，保证金将原路返还；</view>
              <view class="mt-12 font-28 text-6 l-h-48">-若竞拍成功，但买家未在48小时内支付货款，保证金将被扣除。</view>
            </template>
          </template>
          <template v-else>
            <view class="font-wei-600 font-32 text-3 l-h-44">什么是总消费？</view>
            <view class="mt-12 font-28 text-6 l-h-48">在酒云拍卖所购买的全部拍品消费总和</view>
            <view class="mt-32 font-wei-600 font-32 text-3 l-h-44">什么是待收益？</view>
            <view class="mt-12 font-28 text-6 l-h-48">您卖出的拍卖待收益总和，等待买家收货后自动到账（到账后不计入待收益）</view>
            <view class="mt-32 font-wei-600 font-32 text-3 l-h-44">什么是总收益？</view>
            <view class="mt-12 font-28 text-6 l-h-48">您卖出的所有拍品，已完成已到账的全部收益总和</view>
          </template>
        </view>
        <view class="ptb-00-plr-36">
          <view class="pt-28 font-wei-500 font-28 text-e80404 text-center bt-s-01-dedede-r" :class="isNew ? 'h-100' : 'h-121'" @click="onInput(false)">知道了</view>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    isNew: {
      type: Boolean,
      default: false
    },
    isSeller: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
