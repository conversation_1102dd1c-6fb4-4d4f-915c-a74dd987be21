<template>
	<view class="d-inline-block text-center" :style="[purchasedContainerStyle]">
		<text class="p-rela" :style="[purchasedStyle]">已购</text>
	</view>
</template>

<script>
	export default{
		props: {
			width: {
				type: [String, Number],
				default: 72
			},
			bgColor: {
				type: String,
				default: '#E80404'
			},
			borderRadius: {
				type: [String, Number],
				default: 100
			},
			border: {
				type: String,
				default: 'none'
			},
			marginLeft: {
				type: [String, Number],
				default: 10
			},
			padding: {
				type: String,
				default: '0 16rpx'
			},
			fontSize: {
				type: [String, Number],
				default: 20
			},
			fontBold: {
				type: Boolean,
				default: false
			},
			color: {
				type: String,
				default: '#FFFFFF'
			},
			lineHeight: {
				type: [String, Number],
				default: 32
			},
			grade: {
				type: [String, Number],
				default: 0
			}
		},
		computed: {
			purchasedContainerStyle() {
				return {
					width: `${this.width}rpx`,
					background: this.bgColor,
					borderRadius: this.borderRadius + 'rpx',
					border: this.border,
					marginLeft: this.marginLeft + 'rpx',
					lineHeight: this.lineHeight + 'rpx',
					padding: this.padding,
				}
			},
			purchasedStyle() {
				return {
					bottom: '1rpx',
					fontSize: this.fontSize + 'rpx',
					fontWeight: this.fontBold ? 'bold' : 'normal',
					color: this.color,
					whiteSpace: 'nowrap'
				};
			}
		}
	}
</script>