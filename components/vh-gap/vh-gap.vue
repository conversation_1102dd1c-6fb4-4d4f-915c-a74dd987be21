<template>
	<view :style="[gapStyle]"></view>
</template>

<script>
	/**
	 * gap 间隔槽
	 * @description 该组件一般用于内容块之间的用一个灰色块隔开的场景，方便用户风格统一，减少工作量
	 * @property {String} bg-color 背景颜色（默认透明）
	 * @property {String Number} height 分割槽高度，单位rpx（默认30）
	 * @example <vh-gap height="80" bg-color="#bbb"></vh-gap>
	 */
	export default{
		name:"vh-gap",
		props: {
			// 背景
			bgColor: {
				type: String,
				default: 'transparent'
			},
			// 高度
			height: {
				type: [String, Number],
				default: 30
			},
		},
		computed: {
			gapStyle() {
				return {
					backgroundColor: this.bgColor,
					height: this.height + 'rpx',
				};
			}
		}
	}
</script>

<style scoped>
</style>
