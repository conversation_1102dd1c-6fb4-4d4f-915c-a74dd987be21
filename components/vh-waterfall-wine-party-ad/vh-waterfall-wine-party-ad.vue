<template>
	<view @click="onClick">
		<vh-image :src="item.image" :height="220" />
		<view class="ptb-18-plr-16">
			<view class="font-24 font-wei text-3 l-h-30">{{item.title}}</view>
			<view class="mt-08 font-20 text-9 l-h-28 text-hidden-1">{{item.modul_data.activity_time}}</view>
			<view class="mt-04 font-20 text-9 l-h-34 text-hidden-1">{{item.modul_data.province_name}}{{item.modul_data.city_name}}{{item.modul_data.district_name}}{{item.modul_data.address}}</view>
			<view class="d-flex j-sb a-center mt-12">
				<view class="">
					<text class="font-24 font-wei text-e80404"><text class="mr-06 font-18">¥</text>{{item.modul_data.money}}</text>
				</view>
				
				<view class="">
					<text class="font-18 text-9">剩余名额</text>
					<text class="ml-06 font-18 text-e80404">{{item.modul_data.last_num}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import adBuryDotMixin from '@/common/js/mixins/adBuryDotMixin'
	import { mapState } from 'vuex'
	/**
	 * waterfall-wine-party-ad 酒会广告
	 * @description 该组件一般用于拥有酒会广告位的列表
	 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
	 * @property {Object} item 酒会广告的每一项
	 * @event {Function} click 点击组件时触发
	 * @example <vh-waterfall-wine-party-ad />
	 */
	export default {
		name:'vh-waterfall-wine-party-ad',
		mixins: [adBuryDotMixin],
		
		props: {
			// 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
			from: {
				type: [String, Number],
				default: ''
			},
			
			// 酒会广告每一项
			item: {
				type: Object,
				default() {
					return {}
				}
			},
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},

		methods: {
			onClick () {
				if (this.buryDotParams) {
					const { channel, region_id } = this.buryDotParams
					const genre = 3
					const button_id = this.item.id
					this.jump.appAndMiniJumpBD(1, `${this.routeTable.pDWinePartyDetail}?id=${this.item.modul_data.id}`, channel, genre, region_id, button_id, this.from)
				} else {
					this.jump.appAndMiniJump(1, `${this.routeTable.pDWinePartyDetail}?id=${this.item.modul_data.id}`, this.from)
				}
			}
		}
	}
</script>

<style scoped></style>
