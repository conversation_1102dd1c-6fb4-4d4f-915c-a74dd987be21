<template>
	<u-popup
	v-model="value"  
	:maskCloseAble="false" 
	mode="center" 
	:popup="false" 
	length="auto" 
	@close="close" 
	:border-radius="10"
	>
	<view class="w-546 h-274 bg-ffffff">
		<view class="w-p100 h-190 flex-c-c bb-s-01-eeeeee">
			<view class="ptb-00-plr-46 text-center font-28 font-wei text-3">
				已加入<text class="text-e80404">“我的-心愿清单”</text>，上架后会第一时间提醒您哦！
			</view>
		</view>
		<view class="w-p100 h-84 d-flex">
			<view class="w-p50 h-p100 flex-c-c font-28 text-9" @click="know">我知道了</view>
			<view class="w-p50 h-p100 flex-c-c bl-s-01-eeeeee font-28 text-e80404" @click="lookWish">查看清单</view>
		</view>
	</view>
	</u-popup>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		props: {
			value: {
				type: Boolean,
				default: false
			}
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
		
		methods: {
			// 关闭
			close() {
				this.$emit('input', false);
			},
			// 我知道了
			know() {
				if( this.$app ) {
					if (this.$android) {
					    wineYunJsBridge.getDataFromApp(5)
					} else if (this.$ios) {
						wineYunJsBridge.openAppPage({
							client_path: 'opentNotificationAlert'
						})
					}
				}
				console.log('---我知道了')
				this.close()
			},
			// 查看清单
			lookWish() {
				console.log('---查看清单')
				if( this.$app ) {
					wineYunJsBridge.openAppPage({
						client_path: { "ios_path":"WishListViewController", "android_path":"com.stg.rouge.activity.WishListActivity" },
						ad_path_param: [
							{ 
								"ios_key":"login", "ios_val": "1",  
							    "android_key":"login", "android_val": "1" 
							},
						]
					})
				}
				else {
					this.jump.navigateTo(this.routeTable.pEWishList)
				}
				this.close()
			}
		}
	}
</script>

<style>
</style>