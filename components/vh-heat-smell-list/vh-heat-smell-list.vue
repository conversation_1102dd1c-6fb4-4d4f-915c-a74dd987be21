<template>
	<view class="pb-24">
		<view class="bg-ffffff ml-24 mr-24 p-24 b-rad-10">
			<view class="mt-24 d-flex j-sb" v-for="(item, index) in wineSmellList" :key="item.id" @tap.stop="jump.navigateTo(`${routeTable.pDWineSmellDetail}?id=${item.id}`)">
				<vh-image :loading-type="4" :src="item.img" :width="188" :height="188" :border-radius="6"/>
				<view class="ml-20 flex-1 d-flex flex-column j-sb">
					<view class="">
						<view class="font-28 text-3 l-h-40 text-hidden-2">{{item.title}}</view>
						<view class="mt-12 font-24 text-9 l-h-34 text-hidden-1">{{item.abst}}</view>
					</view>
					
					<view class="mt-28 d-flex j-end">
						<view class="d-flex a-center mr-52">
							<image class="w-26 h-26" src="https://images.vinehoo.com/vinehoomini/v3/comm/view.png" mode="aspectFill"></image>
							<text class="ml-06 font-24 text-9 l-h-34">{{item.viewnums | numToThousands}}</text>
						</view>
						<view class="d-flex a-center">
							<image class="w-26 h-26" src="https://images.vinehoo.com/vinehoomini/v3/comm/comm.png" mode="aspectFill"></image>
							<text class="ml-06 font-24 text-9 l-h-34">{{item.commentnums | numToThousands}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	/**
	 * heat-smell-list 热闻推荐列表
	 * @description 该组件一般用于显示热闻推荐列表。
	 * @event {Function} click 点击组件时触发
	 * @example <vh-heat-smell-list></vh-heat-smell-list>
	 */
	export default{
		name:"vh-heat-smell-list",
		
		props: {},
		
		data() {
			return {
				wineSmellList: [], //推荐列表
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
		
		created() {
			this.getWineSmellList()
		},
		
		methods:{
			// 获取酒闻列表
			async getWineSmellList() {
				try{
					let res = await this.$u.api.wineSmellList({ page: 1, limit: 4 })
					this.wineSmellList = res.data.list
				}catch(e){
					//TODO handle the exception
				}
			},
			
			// 点击内容
			click(index) {
				this.$emit('click', index);
			},
		}
	}
</script>

<style scoped></style>
