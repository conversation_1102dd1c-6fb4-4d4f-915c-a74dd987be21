<template>
	<view class="d-flex">
		<image class="fade-in" :style="[checkStyle]" :src="checked ? checkedImg : unCheckedImg" :mode="mode" @tap.stop="click()"></image>
	</view>
</template>

<script>
	/**
	 * check 是否选中
	 * @description 该组件一般用于选中项 类似于radio。
	 * @property {String Number} width 选择框宽度
	 * @property {String Number} height 选择框高度
	 * @property {Boolean} checked 是否选中
	 * @property {String} checked-img 选中的图片显示
	 * @property {String} un-checked-img 未选中的图片显示
	 * @property {String} mode 图片裁剪模式
	 * @property {String Number} margin-top 上外边距
	 * @event {Function} click 点击组件时触发
	 * @example <vh-check></vh-check>
	 */
	export default{
		name: 'vh-check',
		props: {
			// 宽
			width: {
				type: [String, Number],
				default: 32
			},
			// 高
			height: {
				type: [String, Number],
				default: 32
			},
			// 是否选中
			checked: {
				type: Boolean,
				default: false
			},
			// 选中的图片显示
			checkedImg: {
				type: String,
				default:'https://images.vinehoo.com/vinehoomini/v3/comm/cir_sel.png',
			},
			// 未选中的图片显示
			unCheckedImg: {
				type: String,
				default:'https://images.vinehoo.com/vinehoomini/v3/comm/cir_unsel.png',
			},
			// 图片裁剪模式
			mode: {
				type: String,
				default:'aspectFill'
			},
			marginTop: {
				type: [String, Number],
				default: 0
			}
		},
		
		computed: {
			checkStyle() { //选择框样式
				let style = {}
				style.marginTop = this.marginTop + 'rpx'
				style.width = this.width + 'rpx'
				style.height = this.height + 'rpx'
				return style
			}
		},
		
		methods:{
			// 点击内容
			click() {
				this.$emit('click');
			},
		}
		
	}
</script>

<style scoped>
</style>
