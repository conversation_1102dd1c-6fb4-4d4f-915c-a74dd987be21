<template>
	<u-popup 
	v-model="value"  
	:maskCloseAble="true" 
	mode="bottom" 
	:popup="false" 
	length="auto" 
	@close="close" 
	:border-radius="20"
	>
		<view class="p-rela">
			<view class="p-stic top-0 z-04 w-p100 bg-ffffff flex-c-c pt-40 pb-24 font-32 text-3 l-h-84">
				<text class="font-60 font-wei text-f44530">{{ couponInfo.total_face_value }}</text>
				<text class="ml-06 font-40 text-f44530">元</text>
				<text class="font-40 text-3">全场通用</text>
			</view>
			<view class="">
				<scroll-view style="max-height: 632rpx;" scroll-y="true">
					<view class="flex-c-c-c">
						<view class="p-rela w-638 h-140" :class="index ? 'mt-24' : ''" v-for="(item, index) in couponInfo.list" :key="index">
							<view class="p-abso top-0 left-0 z-04 w-130 h-30 bg-f56f68 flex-c-c b-tr-16-bl-16 font-18 text-ffffff">新人专享</view>
							<image class="p-abso w-638 h-140" :src="ossIcon(`/goods_detail/s_cou_new_bg2.png`)" mode="aspectFill" />
							<image v-if="couponInfo.is_draw" class="p-abso top-0 right-0 z-04 w-96 h-72" :src="ossIcon(`/goods_detail/s_cou_new_bg3.png`)" mode="aspectFill" />
							<view class="p-rela z-02 w-638 h-140 d-flex">
								<view class="p-rela w-210 h-140 flex-s-c">
									<view class="ml-44 mb-24 font-32 text-f44530">¥</view>
									<view class="mt-16 ml-08 font-72 font-wei text-f44530">{{ item.coupon_face_value }}</view>
								</view>
								<view class="w-428 h-140 flex-c-s-c">
									<view class="flex-s-e pl-74">
										<view class="w-max-210 font-32 text-3 text-hidden-2 l-h-44">{{ item.explain }}</view>
										<view v-if="item.quantity > 1" class="ml-20 font-26 text-f44530">x{{ item.quantity }}张</view>
									</view>
									<view v-if="couponInfo.is_draw" class="pl-74 mt-04 font-22 text-f44530 l-h-32">
										{{ item.created_time.replace('-', '月').replace(' ', '日 ') }}-{{ item.expire_time.replace('-', '月').replace(' ', '日 ') }}
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="ptb-00-plr-14 pt-30 pb-08 flex-c-c">
				<view class="p-rela w-480 h-198" @click="newPeopleCouponReceive">
					<image class="p-abso w-480 h-198" :src="ossIcon(`/goods_detail/s_cou_new_btn.png`)" mode="aspectFill" />
					<view class="p-rela z-02 w-480 h-198 flex-c-c font-40 font-wei text-ffffff">
						<text class="mb-06">{{ couponInfo.is_draw ? `去使用` : `一键领取` }}</text>
					</view>
				</view>
			</view>
		</view>
	</u-popup>
</template>

<script>
	export default {
		props: {
			value: {
				type: Boolean,
				default: false
			},
			couponInfo: {
				type: Object,
				default: () => ({})
			},
		},
		methods: {
			newPeopleCouponReceive() {
				if( this.couponInfo?.is_draw ) return this.close()
				this.feedback.loading({ title: '领取中...' })
				this.$u.api.newPeopleReceiveBenefits({ }).then(res => {
					this.feedback.toast({ title: '恭喜您，领取成功~' })
					this.$emit('receiveCoupon')
					 if (this.$android) {
						wineYunJsBridge.setDataFromApp({ fromAppType: "7" });
					} else if (this.$ios) {
						wineYunJsBridge.openAppPage({
							client_path: "updateBottomNewUserActivity",
							name: "更新APP数据",
						});
					}
				}).finally(() => {
					this.close()
				})
			},
			close() {
				this.$emit('input', false)
			}
		}
	}
</script>