<template>
	<view class="" @click="onClick">
		<vh-image :src="item.image" :height="220" />
		<view class="ptb-18-plr-16">
		   <view class="font-24 font-wei text-3">{{item.title}}</view>
		   <view class="font-20 text-9 l-h-34">{{item.modul_data.start_time}}</view>
		</view>
	</view>
</template>

<script>
	import adBuryDotMixin from '@/common/js/mixins/adBuryDotMixin'
	/**
	 * live-ad 直播广告
	 * @description 该组件一般用于拥有直播广告位的列表
	 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
	 * @property {Object} item 直播广告的每一项
	 * @event {Function} click 点击组件时触发
	 * @example <vh-waterfall-live-ad />
	 */
	export default {
		name:'vh-waterfall-live-ad',
		mixins: [adBuryDotMixin],
		
		props: {
			// 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
			from: {
				type: [String, Number],
				default: ''
			},
			
			// 直播广告每一项
			item: {
				type: Object,
				default() {
					return {}
				}
			},
		},
		methods: {
			onClick () {
				if (this.buryDotParams) {
					const { channel, region_id } = this.buryDotParams
					const genre = 3
					const button_id = this.item.id
					this.jump.jumpAppLiveBD(this.item.modul_data, channel, genre, region_id, button_id, this.from)
				} else {
					this.jump.jumpAppLive(this.item.modul_data, this.from)
				}
			}
		}
	}
</script>

<style scoped></style>
