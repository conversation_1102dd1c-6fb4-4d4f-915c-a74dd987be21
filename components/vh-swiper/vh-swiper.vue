<template>
	<view class="vh-swiper-wrap" :style="{ borderRadius: `${borderRadius}rpx` }">
		<swiper :current="elCurrent" @change="change" @animationfinish="animationfinish" :interval="interval" :circular="circular" :duration="duration" :autoplay="autoplay"
		 :previous-margin="effect3d ? effect3dPreviousMargin + 'rpx' : '0'" :next-margin="effect3d ? effect3dPreviousMargin + 'rpx' : '0'"
		 :style="{ height: height + 'rpx', backgroundColor: bgColor}">
			<swiper-item class="vh-swiper-item" v-for="(item, index) in list" :key="index">
				<view class="vh-list-image-wrap" @tap.stop.prevent="listClick(item)" :class="[uCurrent != index ? 'vh-list-scale' : '']" :style="{
						borderRadius: `${borderRadius}rpx`,
						transform: effect3d && uCurrent != index ? 'scaleY(0.9)' : 'scaleY(1)',
						margin: effect3d && uCurrent != index ? '0 20rpx' : 0,
					}">
					<!-- <image class="vh-swiper-image" :src="item[name] || item" :mode="imgMode" /> -->
					<vh-image :loading-type="loadingType" :src="item[name] || item" :height="height" :mode="imgMode" />
					<image v-if="borderImage && index == 0" class="vh-swiper-border" :src="borderImage" :mode="imgMode" />
					<view v-if="title && item.title" class="vh-swiper-title" :style="[{
							'padding-bottom': titlePaddingBottom
						}, titleStyle]">
						{{ item.title }}
					</view>
				</view>
			</swiper-item>
		</swiper>
		<view v-if="isShowIndicator" class="vh-swiper-indicator" :style="{
				top: indicatorPos == 'topLeft' || indicatorPos == 'topCenter' || indicatorPos == 'topRight' ? '12rpx' : 'auto',
				bottom: indicatorPos == 'bottomLeft' || indicatorPos == 'bottomCenter' || indicatorPos == 'bottomRight' ? `${indicatorBottom}rpx` : 'auto',
				justifyContent: justifyContent,
				padding: `0 ${effect3d ? '74rpx' : '24rpx'}`
			}">
			<!-- 矩形 -->
			<block v-if="mode == 'rect'">
				<view class="vh-indicator-item-rect" :class="{ 'vh-indicator-item-rect-active': index == uCurrent }" v-for="(item, index) in list"
				 :key="index"></view>
			</block>
			
			<!-- 圆点 -->
			<block v-if="mode == 'dot'">
				<view v-if="customDot" :style="[ index == uCurrent ? customDotStyle.active : customDotStyle.default ]" v-for="(item, index) in list"
				 :key="index"/>
				<view v-else class="vh-indicator-item-dot" :class="{ 'vh-indicator-item-dot-active': index == uCurrent }" v-for="(item, index) in list"
				 :key="index"></view>
			</block>
			
			<!-- 圆角矩形 -->
			<block v-if="mode == 'round'">
				<view class="vh-indicator-item-round" :class="{ 'vh-indicator-item-round-active': index == uCurrent }" v-for="(item, index) in list"
				 :key="index"></view>
			</block>
			
			<!-- 数字 -->
			<block v-if="mode == 'number'">
				<view class="vh-indicator-item-number">{{ uCurrent + 1 }}/{{ list.length }}</view>
			</block>
		</view>
	</view>
</template>

<script>
	/**
	 * swiper 轮播图
	 * @description 该组件一般用于导航轮播，广告展示等场景，可以自定义边框内容
	 * @property {String | Number} loading-type 加载类型 1 = 白底灰字（长方形702*434）、2 = 灰底灰字（长方形702*434）、3 = 白底灰字（类似正方形276 * 262 ）、4 = 灰底灰字（正方形262 * 262 ）
	 * @property {Array} list 轮播图数据，见官网"基本使用"说明
	 * @property {String} border-image 边框图片
	 * @property {Boolean} title 是否显示标题文字，需要配合list参数（默认false）
	 * @property {Object} indicator 用户自定义的指示器的样式
	 * @property {String Number} border-radius 轮播图圆角值，单位rpx（默认8）
	 * @property {String Number} interval 自动轮播时间间隔，单位ms（默认2500）
	 * @property {String} mode 指示器模式（默认round）
	 * @property {String Number} height 轮播图组件高度，单位rpx（默认250）
	 * @property {String} indicator-pos 指示器的位置（默认bottomCenter）
	 * @property {Boolean} effect3d 是否开启3D效果（默认false）
	 * @property {String Number} effect3d-previous-margin mode = true模式的情况下，激活项与前后项之间的距离，单位rpx（默认50）
	 * @property {Boolean} autoplay 是否自动播放（默认true）
	 * @property {String Number} duration 自动轮播时间间隔，单位ms（默认500）
	 * @property {Boolean} circular 是否衔接播放（默认true）
	 * @property {String} img-mode 图片的裁剪模式，详见image组件裁剪模式（默认aspectFill）
	 * @property {String} name 从list数组中读取的图片的属性名（默认image）
	 * @property {String Number} current 初始化时，默认显示第几项
	 * @property {String} bg-color 背景颜色（默认#ffffff）
	 * @property {Object} title-style 自定义标题样式
	 * @event {Function} click 点击轮播图时触发
	 * @example <vh-swiper :list="list" mode="dot" indicator-pos="bottomRight" />
	 */
	export default {
		name: "vh-swiper",
		props: {
			// 加载类型 1 = 白底灰字（长方形702*434）、2 = 灰底灰字（长方形702*434）、3 = 白底灰字（类似正方形276 * 262 ）、4 = 灰底灰字（正方形262 * 262 ）
			loadingType: {
				type: [String, Number],
				default: 1
			},
			// 轮播图的数据,格式如：[{image: 'xxxx', title: 'xxxx'}，{image: 'yyyy', title: 'yyyy'}]，其中title字段可选
			list: {
				type: Array,
				default () {
					return [];
				}
			},
			// 需要显示的边框图片
			borderImage: {
				type: String,
				default: ''
			},
			// 是否显示title标题
			title: {
				type: Boolean,
				default: false
			},
			// 用户自定义的指示器的样式
			indicator: {
				type: Object,
				default () {
					return {};
				}
			},
			// 圆角值
			borderRadius: {
				type: [Number, String],
				default: 8
			},
			// 隔多久自动切换
			interval: {
				type: [String, Number],
				default: 3000
			},
			// 指示器的模式，rect|dot|number|round
			mode: {
				type: String,
				default: 'round'
			},
			customDot: {
				type: Boolean,
				default: false
			},
			customDotStyle: {
				type: Object,
				default: () => ({
					default: {
						width: '10rpx',
						height: '10rpx',
						borderRadius: '10rpx',
						margin: '0 6rpx',
						transition: 'all 0.5s',
						backgroundColor: '#DDDDDD',
					},
					active: {
						width: '10rpx',
						height: '10rpx',
						borderRadius: '10rpx',
						margin: '0 6rpx',
						transition: 'all 0.5s',
						backgroundColor: '#E80404',
					}
				})
			},
			// list的高度，单位rpx
			height: {
				type: [Number, String],
				default: 250
			},
			// 指示器的位置，topLeft|topCenter|topRight|bottomLeft|bottomCenter|bottomRight
			indicatorPos: {
				type: String,
				default: 'bottomCenter'
			},
			indicatorBottom: {
				type: [String, Number ],
				default: 16
			},
			// 是否开启缩放效果
			effect3d: {
				type: Boolean,
				default: false
			},
			// 3D模式的情况下，激活item与前后item之间的距离，单位rpx
			effect3dPreviousMargin: {
				type: [Number, String],
				default: 50
			},
			// 是否自动播放
			autoplay: {
				type: Boolean,
				default: true
			},
			// 自动轮播时间间隔，单位ms
			duration: {
				type: [Number, String],
				default: 500
			},
			// 是否衔接滑动，即到最后一张时接着滑动，是否自动切换到第一张
			circular: {
				type: Boolean,
				default: true
			},
			// 图片的裁剪模式 
			imgMode: {
				type: String,
				default: 'aspectFill'
			},
			// 从list数组中读取的图片的属性名
			name: {
				type: String,
				default: 'image'
			},
			// 初始化时，默认显示第几项
			current: {
				type: [Number, String],
				default: 0
			},
			// 背景颜色
			bgColor: {
				type: String,
				default: '#FFFFFF'
			},
			// 标题的样式，对象形式
			titleStyle: {
				type: Object,
				default() {
					return {}
				}
			},
			isShowIndicator: {
				type: Boolean,
				default: true
			}
		},
		watch: {
			// 如果外部的list发生变化，判断长度是否被修改，如果前后长度不一致，重置uCurrent值，避免溢出
			list(nVal, oVal) {
				if(nVal.length !== oVal.length) this.uCurrent = 0;
			},
			// 监听外部current的变化，实时修改内部依赖于此测uCurrent值，如果更新了current，而不是更新uCurrent，
			// 就会错乱，因为指示器是依赖于uCurrent的
			current(n) {
				this.uCurrent = n;
			}
		},
		data() {
			return {
				uCurrent: this.current // 当前活跃的swiper-item的index
			};
		},
		computed: {
			justifyContent() {
				if (this.indicatorPos == 'topLeft' || this.indicatorPos == 'bottomLeft') return 'flex-start';
				if (this.indicatorPos == 'topCenter' || this.indicatorPos == 'bottomCenter') return 'center';
				if (this.indicatorPos == 'topRight' || this.indicatorPos == 'bottomRight') return 'flex-end';
			},
			titlePaddingBottom() {
				let tmp = 0;
				if (this.mode == 'none') return '12rpx';
				if (['bottomLeft', 'bottomCenter', 'bottomRight'].indexOf(this.indicatorPos) >= 0 && this.mode == 'number') {
					tmp = '60rpx';
				} else if (['bottomLeft', 'bottomCenter', 'bottomRight'].indexOf(this.indicatorPos) >= 0 && this.mode != 'number') {
					tmp = '40rpx';
				} else {
					tmp = '12rpx';
				}
				return tmp;
			},
			// 因为uni的swiper组件的current参数只接受Number类型，这里做一个转换
			elCurrent() {
				return Number(this.current);
			}
		},
		methods: {
			// item = 列表每一项
			listClick(item) {
				this.$emit('click', item);
			},
			change(e) {
				let current = e.detail.current;
				this.uCurrent = current;
				// 发出change事件，表示当前自动切换的index，从0开始
				this.$emit('change', current);
			},
			// 头条小程序不支持animationfinish事件，改由change事件
			// 暂不监听此事件，因为不再给swiper绑定uCurrent属性
			animationfinish(e) {
				// #ifndef MP-TOUTIAO
				// this.uCurrent = e.detail.current;
				// #endif
			}
		}
	};
</script>

<style lang="scss" scoped>
	
	.vh-swiper-wrap {
		position: relative;
		overflow: hidden;
		transform: translateY(0);
	}
	
	.vh-swiper-item {
		display: flex;
		overflow: hidden;
		align-items: center;
	}
    
	.vh-list-image-wrap {
		width: 100%;
		height: 100%;
		flex: 1;
		transition: all 0.5s;
		overflow: hidden;
		box-sizing: content-box;
		position: relative;
	}
	
	.vh-list-scale {
		transform-origin: center center;
	}
	
	.vh-swiper-image {
		width: 100%;
		will-change: transform;
		height: 100%;
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		/* #ifdef H5 */
		pointer-events: none;
		/* #endif */
	}
	
	.vh-swiper-border {
		position: absolute;
		z-index: 10;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
	}

	.vh-swiper-title {
		position: absolute;
		background-color: rgba(0, 0, 0, 0.3);
		bottom: 0;
		left: 0;
		width: 100%;
		font-size: 28rpx;
		padding: 12rpx 24rpx;
		color: rgba(255, 255, 255, 0.9);
	}
	
	.vh-swiper-indicator {
		padding: 0 24rpx;
		position: absolute;
		display: flex;
		width: 100%;
		z-index: 1;
	}

	.vh-indicator-item-rect {
		width: 26rpx;
		height: 8rpx;
		margin: 0 6rpx;
		transition: all 0.5s;
		background-color: rgba(0, 0, 0, 0.3);
	}

	.vh-indicator-item-rect-active {
		background-color: rgba(255, 255, 255, 0.8);
	}

	.vh-indicator-item-dot {
		width: 14rpx;
		height: 14rpx;
		margin: 0 6rpx;
		border-radius: 20rpx;
		transition: all 0.5s;
		background-color: rgba(0, 0, 0, 0.3);
	}

	.vh-indicator-item-dot-active {
		background-color: rgba(255, 255, 255, 0.8);
	}

	.vh-indicator-item-round {
		width: 14rpx;
		height: 14rpx;
		margin: 0 6rpx;
		border-radius: 20rpx;
		transition: all 0.5s;
		background-color: rgba(0, 0, 0, 0.3);
	}

	.vh-indicator-item-round-active {
		width: 34rpx;
		background-color: rgba(255, 255, 255, 0.8);
	}

	.vh-indicator-item-number {
		padding: 6rpx 16rpx;
		line-height: 1;
		background-color: rgba(0, 0, 0, 0.3);
		border-radius: 100rpx;
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
	}

</style>
