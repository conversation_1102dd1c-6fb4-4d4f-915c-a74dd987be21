<template>
	<u-mask :show="value" :zoom="false" :mask-click-able="false">
		<view class="h-p100 flex-c-c">
			<view class="p-rela z-01 w-542 h-672 bg-ffffff b-rad-10 ptb-00-plr-34">
				<view class="mt-48 font-36 font-wei-600 text-3 text-center l-h-50">附近有门店可以堂饮</view>
				<view class="p-rela mt-40 w-470 h-270 flex-c-c">
					<image class="p-abso top-0 z-02 right-0 w-76 h-78" :src="ossIcon(`/second_hair/in_${info.in_business ? 'business' : 'rest'}.png`)"></image>
					<image class="p-abso wh-p100" :src="ossIcon(`/second_hair/s_map_bg.png`)"></image>
					<view class="p-rela d-flex pr-36 pl-36">
						<image class="flex-shrink w-32 h-38" :src="ossIcon(`/second_hair/s_add.png`)"></image>
						<view class="ml-10 o-hid">
							<view class="flex-s-c">
								<text class="font-26 font-wei-600 text-3 l-h-36 text-hidden-2">{{ info.store_name }}</text>
							</view>
							<view class="mt-10 font-20 text-9 l-h-28">{{ info.address }}</view>
						</view>
					</view>
				</view>
				<view class="mt-24 font-wei-500 font-24 text-3 l-h-34 text-center text-hidden">门店距离您{{ info.distance | metreToKilometredistanceFilter }}，营业时间{{ info.start_time }}-{{ info.end_time }}</view>
				<view class="flex-sb-c mt-52">
					<button class="vh-btn flex-c-c w-210 h-64 font-28 text-6 bg-ffffff b-s-02-d8d8d8 b-rad-32" @click="cancel">取消</button>
					<button class="vh-btn flex-c-c w-210 h-64 font-28 text-ffffff bg-e80404 b-rad-32" @click="onJumpStore">前往门店</button>
				</view>
				<view class="flex-c-c mt-36" @click="sevenDaysDisplay">
					<image :src="ossIcon(`/second_hair/radio${checked ? '_h' : ''}_30.png`)" class="w-30 h-30"></image>
					<text class="ml-10 font-24 text-9">7天之内不再弹出</text>
				</view>
			</view>
		</view>
  </u-mask>
</template>

<script>
	import { STORE_URL } from '@/common/js/fun/constant'
	export default {
		props: {
			value: {
				type: Boolean,
				default: false
			},
			info: {
				type: Object,
				default: () => ({
					// address: "重庆市渝北区两江春城",
					// delivery_radius: 7,
					// end_time: "23:00",
					// id: 17,
					// in_business: 1,
					// latitude: "29.60000000",
					// longitude: "106.49000000",
					// pay_method: 0,
					// radius: 3000,
					// storeDistance: 100,
					// start_time: "00:00",
					// store_name: "店铺测试",
					// store_phone: "***********",
				})
			}
		},
		data: () => ({
			checked: false
		}),
		methods: {
			onJumpStore () {
				window.open(STORE_URL)
			},
			cancel() {
				this.$emit('cancel')
				uni.setStorageSync('storeMaskTomorrowDate', this.$u.timeFormat(Date.now(), 'yyyy-mm-dd'))
			},
			sevenDaysDisplay() {
				this.checked = !this.checked
				this.checked ? uni.setStorageSync('storeMaskVisibleCountDown', Date.now()) : uni.removeStorageSync('storeMaskVisibleCountDown')
			}
		}
	}
</script>