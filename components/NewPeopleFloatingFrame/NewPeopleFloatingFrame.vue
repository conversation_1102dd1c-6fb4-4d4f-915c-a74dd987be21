<template>
  <view class="fade-in">
    <!-- <view class="u-fixed-b">
			<view v-if="isLogin" class="p-rela w-p100 h-90">
				<image class="p-abso top-0 left-0 w-p100 h-p100" :src="ossIcon(`/comm/new_peo_sus_${received ? '' : 'un'}rec.png`)"  />
				<view class="p-abso top-0 right-52 w-142 h-p100" @click="$emit('jumpActivity')" />
				<view class="p-abso top-0 right-0 w-52 h-p100 flex-c-c" @click="close">
					<image class="w-40 h-40" :src="ossIcon(`/comm/new_peo_sus_del.png`)" />
				</view>
			</view>
			
			<view v-else class="p-rela w-p100 h-86 flex-sb-c pr-24" :class="isLiquorActivity ? 'bg-255-255-255-090' : 'bg-000-000-000-080'">
				<view class="flex-c-c">
					<view class="w-70 h-86 flex-c-c" @click="close">
						<image :class="isLiquorActivity ? 'w-32 h-32' : 'w-40 h-40'" :src="ossIcon(isLiquorActivity ? '/comm/new_peo_sus_del2.png' : '/comm/new_peo_sus_del.png')" />
					</view>
					<view class="font-28" :class="isLiquorActivity ? 'text-3' : 'text-ffffff'">登录酒云网，领取您的168福利礼包～</view>
				</view>
				<button class="vh-btn flex-c-c w-146 h-52 bg-e80404 b-rad-60 text-ffffff font-24" @click="login.isLogin($vhFrom)">立即登录</button>
			</view>
		</view>
		<view class="vh-fixed-placeholder" :style="{ height: `90rpx`}"></view> -->
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
export default {
  props: {
    isLogin: {
      type: Boolean,
      default: true,
    },
    tabbarHeight: {
      type: [Number, String],
      default: 0,
    },
    received: {
      type: [Number, String],
      default: 0,
    },
    isLiquorActivity: {
      type: Boolean,
      default: false,
    },
  },
  data: () => ({}),
  methods: {
    ...mapMutations('newPeopleArea', ['UPDATE_SHOW_NEW_PEOPLE_FLOATING_FRAME']),

    close() {
      this.UPDATE_SHOW_NEW_PEOPLE_FLOATING_FRAME(false)
    },
  },
}
</script>

<style lang="scss" scoped>
.u-fixed-b {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding-bottom: 0;
  padding-bottom: calc(116rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(116rpx + env(safe-area-inset-bottom));
  z-index: 100;
}
</style>
