<template>
  <view class="auction-bid-list" :class="{ 'is-from-detail': isFromDetail }">
    <view v-for="(item, index) in list" :key="index">
      <view class="flex-c-c h-132 text-9">
        <view class="p-rela flex-c-c w-60 h-60">
          <vh-image :loading-type="4" :src="!(+item.is_anonymous) ? item.avatar_image : 'https://images.vinehoo.com/avatars/rabbit.png'" :width="60" :height="60" shape="circle" />
          <image v-if="!index && isFromDetail" :src="ossIcon('/auction/circle_60.png')" class="p-abso w-60 h-60"></image>
        </view>
        <view class="ml-08">
          <view class="ml-12 w-382 text-hidden" :class="{ 'text-e80404': !index && isFromDetail }">
            <text class="font-20 l-h-26"><text class="font-18">NO.</text>{{ item.code }}</text>
            <text class="ml-06 font-24 l-h-34">{{ item.nickname }}</text>
          </view>
          <view class="mt-10 font-20 text-9 l-h-28">（地区：{{ item.province_name || '未知' }}）</view>
        </view>
        <view class="flex-1 text-right">
          <view class="flex-e-c">
            <image :class="getStatusClazz(index)" :src="ossIcon(getStatusIcon(index))"></image>
            <text class="ml-06 font-32 l-h-44" :class="{ 'text-e80404': !index && isFromDetail }">¥{{ item.bid_price }}</text>
          </view>
          <view class="font-24 l-h-34">{{ item.create_time | date('mm.dd hh:MM:ss') }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { MAuctionGoodsStatus } from '@/common/js/utils/mapperModel'

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    uid: {
      required: true
    },
    onsaleStatus: {
      required: true
    },
    isFromDetail: {
      type: Boolean,
      defualt: false
    }
  },
  data: () => ({
    MAuctionGoodsStatus,
  }),
  methods: {
    getStatusClazz (index) {
      if (!this.isFromDetail) return 'w-58 h-34'
      return index ? 'w-58 h-34' : 'w-60 h-36'
    },
    getStatusIcon (index) {
      if (!this.isFromDetail) return '/auction/abl_out_58_34.png'
      if (index) return '/auction/abl_out_58_34.png'
      return MAuctionGoodsStatus.AuctionAbort === this.onsaleStatus ? '/auction/abl_success_60_36.png' : '/auction/abl_lead_60_36.png'
    },
  },
}
</script>

<style lang="scss" scoped>
  .auction-bid-list {
    > view::after {
      content: '';
      display: block;
      height: 2rpx;
      background: linear-gradient(270deg, rgba(232,4,4,0.07) 0%, rgba(232,4,4,0.6) 33%, rgba(232,4,4,0.76) 52%, rgba(232,4,4,0.6) 67%, rgba(232,4,4,0.07) 100%);
      opacity: 0.5;
    }

    &.is-from-detail {
      > view:last-of-type::after {
        display: none;
      }
    }
  }
</style>
