<template>
	<view class="bb-s-01-eeeeee pt-08 pb-28">
	   <view class="d-flex mt-20" v-for="(innItem, innIndex) in item.goodsInfo" :key="innIndex">
			<vh-image :loading-type="2" :src="innItem.goods_img" :width="246" :height="152" :border-radius="6" />
			<view class="flex-1 d-flex flex-column j-sb ml-12">
				<view class="text-hidden-2 font-24 text-0 l-h-34">{{innItem.goods_title}}</view>
				<view class="d-flex j-sb a-center">
					<text class="bg-f5f5f5 ptb-04-plr-18 b-rad-08 mt-08 font-22 text-9">{{innItem.package_name}}</text>
					<text class="font-24 text-9">x{{innItem.order_qty}}</text>
				</view>
			</view>
	   </view>
	
		<view class="">
			<view v-if="( !item.countdown && item.status === 1 ) || item.status === 3" class="flex-e-c mt-18">
				<view class="font-20 text-3">已膨胀至：</view>
				<view class="font-32 font-wei text-3">
					<text class="font-22">¥</text>
					<text>{{ item.deposit_coupon_value }}</text>
				</view>
			</view>
			<view v-else class="flex-e-c mt-18">
				<view class="font-20 text-3">订金：</view>
				<view class="font-32 font-wei text-3">
					<text class="font-22">¥</text>
					<text>{{ item.payment_amount }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name: "OrderListGoodsItem",
		
		props: {
			// 订单信息
			item : {
				type: Object,
				default: () => ({})
			},
		}
	}
</script>
