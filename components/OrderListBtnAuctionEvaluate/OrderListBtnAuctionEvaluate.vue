<template>
	<view v-if="!item.is_comment" class="ml-20">
		<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
		:custom-style="{width:'148rpx', height:'52rpx', fontSize:'24rpx', color:'#E80404', border:'1rpx solid #E80404'}" 
		@click="click">评价</u-button>
	</view>
</template>

<script>
	export default {
		name: 'OrderListBtnAuctionEvaluate',
		props: {
			// 订单信息
			item : {
				type: Object,
				default: () => ({})
			},
		},
	
		methods: {
			click() {
				this.$emit('click')
			}
		}
	}
</script>

<style>
</style>