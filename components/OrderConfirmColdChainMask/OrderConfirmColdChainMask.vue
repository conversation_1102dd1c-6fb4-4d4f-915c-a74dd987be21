<template>
	<view class="tran-2 p-fixed top-0 right-0 bottom-0 left-0 op-000" :class="{ 'op-100': show }" hover-stop-propagation :style="[maskStyle]" 
	@click="$emit('agree', 0)">
		<view class="h-p100 d-flex j-center a-center">
			<view class="p-rela w-582 bg-ffffff b-rad-10 o-hid" @click.stop>
				<view class="d-flex j-center mt-40 font-32 font-wei text-3">{{ title }}</view>
				<view class="mt-32 mr-40 ml-46 pb-156">
					<scroll-view id="cold-scroll-con" style="max-height: 788rpx;" scroll-y="true" :show-scrollbar="false" @scrolltolower="$emit('scrolltolower')">
						<view id="cold-text-con" class="font-28 text-6 l-h-40">
							<view v-html="text"></view>
						</view>
					</scroll-view>
				</view>
				<view v-if="text" class="p-abso bottom-0 z-100 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-center a-center">
					<view v-if="readComplete" class="fade-in">
						<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF" 
						:custom-style="{width:'440rpx', height:'64rpx', fontSize:'28rpx', color:'#FFF', backgroundColor: '#E80404', border:'none'}"
						@click.stop="$emit('agree', 1)">同意并接受</u-button>
					</view>
					<view v-else class="fade-in w-440 h-64 bg-dddddd b-rad-32 d-flex j-center a-center font-28 text-ffffff" 
					@click.stop="$emit('agree', 0)">请上滑看完本条款再同意</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			show: {
				type: Boolean,
				default: false
			},
			text: {
				type: String,
				default: '',
			},
			title: {
				type: String,
				default: '',
			},
			readComplete: {
				type: Boolean,
				default: false
			}
		},
		computed: {
			maskStyle() {
				let style = {}
				style.backgroundColor = "rgba(0, 0, 0, 0.6)"
				if( this.show ) style.zIndex = this.$u.zIndex.mask
				else style.zIndex = -1
				style.transition = `all 0.3s ease-in-out`
				return style
			},
		},
		
		mounted() {
			// this.$nextTick(() => {
			// 	uni.createSelectorQuery().in(this).select('#cold-scroll-con').boundingClientRect( res1 => {
			// 		uni.createSelectorQuery().in(this).select('#cold-text-con').boundingClientRect( res2 => {
			// 			res1.height < res2.height ? this.$emit('readStatus', false) : this.$emit('readStatus', true)
			// 		}).exec()
			// 	}).exec()
			// })
		}
	}
</script>