<template>
  <u-popup v-model="params.visible" mode="bottom" width="100%" height="600rpx" border-radius="40">
    <view class="p-rela">
      <view style="left: 26rpx; top: 50%; transform: translateY(-50%);" class="p-abso flex-c-c w-88 h-88" @click="params.visible = false">
        <image :src="ossIcon('/auction/close_44.png')" class="w-44 h-44" />
      </view>
      <view class="flex-c-c h-120 font-wei-500 font-28 text-6 l-h-40">{{ params.title }}</view>
      <button style="top: 50%; transform: translateY(-50%);" class="vh-btn p-abso right-24 flex-c-c w-96 h-68 font-24 text-e80404 bg-ffffff" @click="onCategoryPropertyConfirm">确认</button>
    </view>
    <view v-if="params.keyValue === 'product_name'" class="ptb-00-plr-24">
      <textarea v-model="category[params.keyValueInput]" placeholder="请在此输入您的产品名称" placeholder-style="color: #999;" class="bs-bb ptb-28-plr-24 w-p100 h-182 font-28 text-3 l-h-40 bg-f5f5f5 b-rad-10" />
    </view>
    <view v-if="params.keyValue === 'brand'">
      <view class="flex-c-c mtb-00-mlr-auto ptb-00-plr-32 w-520 h-60 bg-f7f7f7 b-rad-40">
        <input v-model="category[params.keyValueInput]" placeholder="搜索产品品牌" placeholder-style="color: #999;" class="w-410 font-28 text-3" />
        <image :src="ossIcon('/auction/search_38_36.png')" class="ml-10 w-38 h-36" />
      </view>
      <scroll-view scroll-y="true" class="mt-12" style="max-height: 408rpx;">
        <view
          v-for="(item, index) in BRAND_LIST.filter(item => !category[params.keyValueInput] ? true : item.includes(category[params.keyValueInput]))"
          :key="index"
          class="mt-28 font-24 l-h-34 text-center"
          :class="category[params.keyValueInput] === item ? 'text-e80404' : 'text-3'"
          @click="category[params.keyValueInput] = item"
        >{{ item }}</view>
      </scroll-view>
    </view>
    <view v-if="params.keyValue === 'country'">
      <view class="flex-c-c mtb-00-mlr-auto ptb-00-plr-32 w-520 h-60 bg-f7f7f7 b-rad-40">
        <input v-model="category[params.keyValueInput]" placeholder="搜索原产国" placeholder-style="color: #999;" class="w-410 font-28 text-3" />
        <image :src="ossIcon('/auction/search_38_36.png')" class="ml-10 w-38 h-36" />
      </view>
      <scroll-view scroll-y="true" class="mt-12" style="max-height: 408rpx;">
        <view
          v-for="(item, index) in WINE_COUNTRY_LIST.filter(item => !category[params.keyValueInput] ? true : item.includes(category[params.keyValueInput]))"
          :key="index"
          class="mt-28 font-24 text-3 l-h-34 text-center"
          :class="category[params.keyValueInput] === item ? 'text-e80404' : 'text-3'"
          @click="category[params.keyValueInput] = item"
        >{{ item }}</view>
      </scroll-view>
    </view>
    <view v-if="params.keyValue === 'net_content'">
      <view class="flex-c-c">
        <view class="flex-c-c w-264 h-72 bg-f5f5f5 b-rad-10">
          <input v-model="category[params.keyValueInput]" placeholder="请输入产品容量" placeholder-style="color: #999;" class="w-198 h-40 font-28 text-3 l-h-40" />
        </view>
        <text class="ml-20 font-28 text-6 l-h-40">ml</text>
      </view>
    </view>
    <view v-if="params.keyValue === 'alcoholic_strength'">
      <view class="flex-c-c">
        <view class="flex-c-c w-264 h-72 bg-f5f5f5 b-rad-10">
          <input v-model="category[params.keyValueInput]" placeholder="请输入酒精度数" placeholder-style="color: #999;" class="w-198 h-40 font-28 text-3 l-h-40" />
        </view>
        <text class="ml-20 font-28 text-6 l-h-40">%vol</text>
      </view>
    </view>
    <view v-if="listSelectParams.visible">
      <scroll-view scroll-y="true" style="max-height: 480rpx;">
        <view
          v-for="(item, index) in listSelectParams.list"
          :key="index"
          class="mt-28 font-24 text-3 l-h-34 text-center"
          :class="category[params.keyValueInput] === item ? 'text-e80404' : 'text-3'"
          @click="category[params.keyValueInput] = item"
        >{{ item }}{{ listSelectParams.suffixStr }}</view>
      </scroll-view>
    </view>
    <view v-if="params.keyValue === 'place'">
      <view class="flex-c-c mtb-00-mlr-auto ptb-00-plr-32 w-520 h-60 bg-f7f7f7 b-rad-40">
        <input v-model="category[params.keyValueInput]" placeholder="搜索产品产地" placeholder-style="color: #999;" class="w-410 font-28 text-3" />
        <image :src="ossIcon('/auction/search_38_36.png')" class="ml-10 w-38 h-36" />
      </view>
      <scroll-view scroll-y="true" class="mt-12" style="max-height: 408rpx;">
        <view
          v-for="(item, index) in PLACE_LIST.filter(item => !category[params.keyValueInput] ? true : item.includes(category[params.keyValueInput]))"
          :key="index"
          class="mt-28 font-24 l-h-34 text-center"
          :class="category[params.keyValueInput] === item ? 'text-e80404' : 'text-3'"
          @click="category[params.keyValueInput] = item"
        >{{ item }}</view>
      </scroll-view>
    </view>
    <view v-if="params.keyValue === 'filling_time'">
      <view class="mtb-00-mlr-auto w-654">
        <view class="flex-sb-c h-62 font-24 text-e80404 l-h-34 bb-s-02-ececec">
          <view class="pl-68 w-168">{{ category[params.keyValueInput][0] }}年</view>
          <view class="pr-76 w-176 text-right">{{ category[params.keyValueInput][1] }}月</view>
        </view>
        <view class="flex-sb-c mt-22">
          <scroll-view scroll-y="true" style="max-height: 396rpx;" class="w-168">
            <view
              v-for="(item, index) in FILL_TIME_YEAR_LIST"
              :key="index"
              class="mb-28 pl-68 font-24 text-6 l-h-34"
              @click="category[params.keyValueInput].splice(0, 1, item)"
            >{{ item }}年</view>
          </scroll-view>
          <scroll-view scroll-y="true" style="max-height: 396rpx;" class="w-176">
            <view
              v-for="(item, index) in FILL_TIME_MONTH_LIST"
              :key="index"
              class="mb-28 pr-76 font-24 text-6 l-h-34 text-right"
              @click="category[params.keyValueInput].splice(1, 1, item)"
            >{{ item }}月</view>
          </scroll-view>
        </view>
      </view>
    </view>
    <view v-if="params.keyValue === 'grape_variety'">
      <view class="mtb-00-mlr-auto w-520">
        <view class="flex-c-c ptb-00-plr-32 h-60 bg-f7f7f7 b-rad-40">
          <input v-model="params.searchValueInput" placeholder="搜索葡萄品种" placeholder-style="color: #999;" class="w-410 font-28 text-3" @confirm="params.searchValue = params.searchValueInput" />
          <image :src="ossIcon('/auction/search_38_36.png')" class="ml-10 w-38 h-36" @click="params.searchValue = params.searchValueInput" />
        </view>
        <view class="flex-e-c mt-40">
          <view v-if="category[params.keyValueInput] === '或我也不知道'" class="flex-c-c" @click="category[params.keyValueInput] = ''">
            <image :src="ossIcon('/auction/checkbox_h_28.png')" class="w-28 h-28" />
            <text class="ml-14 font-24 text-9 l-h-34">或我也不知道</text>
          </view>
          <view v-else class="flex-c-c" @click="category[params.keyValueInput] = '或我也不知道'">
            <image :src="ossIcon('/auction/checkbox_28.png')" class="w-28 h-28" />
            <text class="ml-14 font-24 text-9 l-h-34">或我也不知道</text>
          </view>
        </view>
      </view>
      <scroll-view scroll-y="true" class="mt-20" style="max-height: 280rpx;">
        <view
          v-for="(item, index) in grapeVarietyList.filter(item => !params.searchValue ? true : item.includes(params.searchValue))"
          :key="index"
          class="mt-28 font-24 l-h-34 text-center"
          :class="category[params.keyValueInput] === item ? 'text-e80404' : 'text-3'"
          @click="category[params.keyValueInput] = item"
        >{{ item }}</view>
      </scroll-view>
    </view>
    <view v-if="radioBtnGroupParams.visible">
      <view class="d-flex flex-wrap ptb-00-plr-48">
        <view
          v-for="(item, index) in radioBtnGroupParams.list"
          :key="index"
          class="flex-c-c mb-28 w-306 h-60 font-24 text-6 bg-f7f7f7 b-rad-10"
          :class="[index % 2 ? 'mr-0' : 'mr-42', category[params.keyValueInput] === item ? 'text-e80404' : 'text-6']"
          @click="category[params.keyValueInput] = item"
        >{{ item }}</view>
      </view>
    </view>
    <view v-if="params.keyValue === 'grade'">
      <view class="mtb-00-mlr-auto w-654">
        <view class="flex-sb-c h-62 font-24 text-e80404 l-h-34 bb-s-02-ececec">
          <view class="pl-68 w-470 text-hidden">{{ category[params.keyValueInput][0] }}</view>
          <view class="pr-76 w-176 text-right">{{ category[params.keyValueInput][1] }}</view>
        </view>
        <view class="flex-sb-c mt-22">
          <scroll-view scroll-y="true" style="max-height: 396rpx;" class="w-470">
            <view
              v-for="(item, index) in wineGradeOrgList"
              :key="index"
              class="mb-28 pl-68 font-24 text-6 l-h-34"
              @click="category[params.keyValueInput].splice(0, 1, item)"
            >{{ item }}</view>
          </scroll-view>
          <scroll-view scroll-y="true" style="max-height: 396rpx;" class="w-176">
            <view
              v-for="(item, index) in WINE_GRADE_LIST"
              :key="index"
              class="mb-28 pr-76 font-24 text-6 l-h-34 text-right"
              @click="category[params.keyValueInput].splice(1, 1, item)"
            >{{ item }}</view>
          </scroll-view>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { alcoholicStrengthPattern } from '@/common/js/utils/pattern'
import { WINE_BRAND_LIST, WHITE_SPIRITS_BRAND_LIST, WINE_COUNTRY_LIST, SCENT_LIST, WINE_PLACE_LIST, WINE_PACKING_LIST, WHITE_SPIRITS_PACKING_LIST, STORAGE_MODE_LIST, FILL_TIME_YEAR_LIST, getFillTimeMonthList, WINE_GRADE_LIST, YEAR_LIST, WHITE_SPIRITS_YEAR_LIST } from '@/common/js/utils/auctionGoodsConfig'
import { MAuctionGoodsCategory } from '@/common/js/utils/mapperModel'

export default {
  props: ['params', 'category'],
  data: () => ({
    WINE_COUNTRY_LIST,
    whiteSpiritsPlaceList: [],
    FILL_TIME_YEAR_LIST,
    FILL_TIME_MONTH_LIST: [],
    YEAR_LIST,
    grapeVarietyList: [],
    wineGradeOrgList: [],
    WINE_GRADE_LIST,
  }),
  computed: {
    BRAND_LIST ({ category }) {
      switch (category.category_id) {
        case MAuctionGoodsCategory.Wine:
          return WINE_BRAND_LIST
        case MAuctionGoodsCategory.WhiteSpirits:
          return WHITE_SPIRITS_BRAND_LIST
        default:
          return []
      }
    },
    currentYearList ({ category }) {
      switch (category.category_id) {
        case MAuctionGoodsCategory.Wine:
        case MAuctionGoodsCategory.Liqueur:
          return YEAR_LIST.map(item => `${item}年`)
        case MAuctionGoodsCategory.WhiteSpirits:
          return WHITE_SPIRITS_YEAR_LIST
        default:
          return []
      }
    },
    PLACE_LIST ({ category, whiteSpiritsPlaceList }) {
      switch (category.category_id) {
        case MAuctionGoodsCategory.Wine:
        case MAuctionGoodsCategory.Liqueur: 
          return WINE_PLACE_LIST
        case MAuctionGoodsCategory.WhiteSpirits:
          return whiteSpiritsPlaceList
        default:
          return []
      }
    },
    PACKING_LIST ({ category }) {
      switch (category.category_id) {
        case MAuctionGoodsCategory.Wine:
        case MAuctionGoodsCategory.Liqueur:
          return WINE_PACKING_LIST
        case MAuctionGoodsCategory.WhiteSpirits:
          return WHITE_SPIRITS_PACKING_LIST
        default:
          return []
      }
    },
    fillTimeInputYear ({ category }) {
      return category.filling_time__input[0]
    },
    radioBtnGroupParams ({ params, PACKING_LIST }) {
      const { keyValue } = params
      const visible = ['odor_type', 'packaging', 'saving_mode'].includes(keyValue)
      let list = []
      switch (keyValue) {
        case 'odor_type':
          list = SCENT_LIST
          break
        case 'packaging':
          list = PACKING_LIST
          break
        case 'saving_mode':
          list = STORAGE_MODE_LIST
          break
      }
      return { visible, list }
    },
    listSelectParams ({ params, currentYearList }) {
      const { keyValue } = params
      const visible = ['years', 'production_year'].includes(keyValue)
      let list = []
      switch (keyValue) {
        case 'years':
          list = currentYearList
          break
        case 'production_year':
          list = YEAR_LIST
          break
      }
      const suffixStr = keyValue === 'production_year' ? '年' : ''
      return { visible, list, suffixStr }
    },
  },
  watch: {
    fillTimeInputYear: {
      handler (newVal) {
        this.FILL_TIME_MONTH_LIST = getFillTimeMonthList(newVal)
        if (!this.FILL_TIME_MONTH_LIST.includes(this.category.filling_time__input[1])) {
          this.category.filling_time__input[1] = 1
        }
      },
      immediate: true
    }
  },
  methods: {
    onCategoryPropertyConfirm () {
      const { keyValue, keyValueInput } = this.params
      const valueInput = this.category[keyValueInput]
      if (!valueInput) return
      if (keyValue === 'net_content' && !alcoholicStrengthPattern.test(valueInput)) {
        this.feedback.toast({ title: '请输入正确的容量' })
        return
      }
      if (keyValue === 'alcoholic_strength' && !alcoholicStrengthPattern.test(valueInput)) {
        this.feedback.toast({ title: '请输入正确的度数' })
        return
      }
      if (Array.isArray(valueInput)) {
        this.category[keyValue] = valueInput.join('-')
      } else {
        this.category[keyValue] = valueInput
      }
      this.params.visible = false
    },
    async loadWhiteSpiritsPlaceList () {
      if (this.whiteSpiritsPlaceList.length) return
      const res = await this.$u.api.regionList({ isJson:true })
      const list = res?.data?.list || []
			this.whiteSpiritsPlaceList = list.map(item => item.name)
    },
    async loadGrapeVarietyList () {
      if (this.grapeVarietyList.length) return
      const res = await this.$u.api.searchGrapeVarietyList()
      const list = res?.data?.list || []
      this.grapeVarietyList = list.map(({ gname_cn, gname_en }) => gname_cn || gname_en)
    },
    async loadWineGradeOrgList () {
      if (this.wineGradeOrgList.length) return
      const res = await this.$u.api.searchWineGradeOrgList()
      const list = res?.data?.list || []
      this.wineGradeOrgList = list
    },
  },
  created () {
    this.loadWhiteSpiritsPlaceList()
    this.loadGrapeVarietyList()
    this.loadWineGradeOrgList()
  }
}
</script>

<style lang="scss" scoped>
</style>
