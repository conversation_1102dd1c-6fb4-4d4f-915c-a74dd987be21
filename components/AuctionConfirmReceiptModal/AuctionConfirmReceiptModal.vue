<template>
	<u-mask :show="show" :zoom="false" @click="close">
		<view class="h-p100 flex-column flex-c-c">
			<view class="flex-column flex-c-c">
				<view class="p-rela w-552 h-414 bg-ffffff b-rad-30 o-hid" @click.stop>
					<image class="p-abso w-p100 h-p100 b-rad-30" :src="ossIcon(`/auction_seller_after_sale_detail/reason_bg.png`)" />
					<view class="p-rela z-02 w-p100 h-p100 p-84">
						<view class="text-center font-32 font-wei text-6">
							确认收货后不可发起售后，请确认商品无异常
						</view>
						
						<view class="flex-sb-c mt-86">
							<view class="">
								<u-button
									shape="circle" 
									:hair-line="false" 
									:ripple="true" 
									ripple-bg-color="#FFF"
									:custom-style="{ width:'160rpx', height:'64rpx', backgroundColor: '#FFF', fontSize:'28rpx' ,fontWeight:'bold', color:'#666', border:'1px solid #666666' }"
									@click="close"
								>取消</u-button>
							</view>
							
							<view class="">
								<u-button
									shape="circle" 
									:hair-line="false" 
									:ripple="true" 
									ripple-bg-color="#FFF"
									:custom-style="{ width:'160rpx', height:'64rpx', backgroundColor: '#E80404', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', border:'none' }"
									@click="confirm"
								>确认</u-button>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</u-mask>
</template>

<script>
	export default {
		props: {
			show: {
			  type: Boolean,
			  default: false
			},
		},
		
		methods: {
			close() {
				this.$emit('close', false)
			},
			confirm() {
				this.$emit('confirm')
			}
		}
	}
</script>

<style>
</style>