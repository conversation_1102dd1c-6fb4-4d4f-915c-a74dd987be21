<template>
  <view>
    <view :class="isHiddenText ? 'h-max-80 o-hid' : ''">
      <view :id="id" class="font-28 text-3 l-h-40" :class="isHiddenText ? 'text-hidden-2' : ''">{{ item.content }}</view>
    </view>
    <view v-if="isHiddenText" class="flex-e-c mt-10">
      <view class="flex-c-c" @click="onExpand">
        <view class="w-42 h-02 bg-cccccc"></view>
        <view class="ml-06 font-28 text-2e7bff l-h-40">展开</view>
      </view>
    </view>
    <view v-if="item.emoji_image" class="mt-10">
      <vh-image loading-type="2" :src="item.emoji_image" :width="120" :height="120" />
    </view>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data: () => ({
    isHiddenText: false,
  }),
  computed: {
    id ({ item }) {
      return `comment-content-${item.id}`
    }
  },
  methods: {
    onExpand () {
      this.isHiddenText = false
    }
  },
  mounted() {
    uni.createSelectorQuery().in(this).select(`#${this.id}`).boundingClientRect(res => {
      const height = res?.height || 0
      if (height > uni.upx2px(80)) {
        this.isHiddenText = true
      }
    }).exec()
  },
}
</script>

<style lang="scss" scoped>
</style>
