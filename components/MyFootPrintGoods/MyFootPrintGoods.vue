<template>
	<view class="bg-ffffff d-flex p-24" @click="handleJump(`${$routeTable.pgGoodsDetail}?id=${item.period}`, $vhFrom)">
		<vh-image :src="item.banner_img && item.banner_img[0]" :loading-type="2" :width="290" :height="180" :border-radius="6" />
		<view class="flex-1 flex-sb-n-c ml-16">
			<view class="">
				<view class="text-hidden-2">
					<vh-channel-title-icon :channel="item.periods_type" :font-bold="false" />
					<text class="ml-08 font-24 font-wei text-0 l-h-34">{{item.title}}</text>
				</view>
				<view class="mt-08 text-hidden-1 font-22 l-h-32">{{item.brief}}</view>
			</view>
			
			<view class="d-flex j-sb">
				<text v-if="item.is_hidden_price == 1 || [3, 4].includes(item.onsale_status)" class="font-24 text-ff0013 l-h-28">价格保密</text>
				<text v-else class="font-28 text-ff0013 l-h-26"><text class="font-18">¥</text>{{item.price}}</text>
				<text class="font-24 text-9 l-h-34">已售{{item.purchased + item.vest_purchased}}{{item.periods_type !== 1 ? `/限量${item.limit_number}` : ''}}{{item.quota_rule && item.quota_rule.quota_number == '9999' ? '' : `/限购${item.quota_rule && item.quota_rule.quota_number}`}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'
	export default {
		mixins: [longpressCopyMixin],
		props: {
			item: {
				type: Object,
				default: () => ({})
			},
		}
	}
</script>