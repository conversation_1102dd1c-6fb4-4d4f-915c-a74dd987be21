<template>
	<view class="" @click="onClick">
		<vh-image :src="item.image" :height="434" />
		<view class="ptb-20-plr-24">
			<view class="">
				<text class="font-30 font-wei text-3 l-h-44">{{item.title}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import adBuryDotMixin from '@/common/js/mixins/adBuryDotMixin'
	import { mapState } from 'vuex'
	/**
	 * normal-thematic-activities-ad 普通专题活动广告位
	 * @description 该组件一般用于普通专题活动广告位
	 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
	 * @property {Object} item 商品列表的每一项
	 * @example <vh-normal-thematic-activities-ad />
	 */
	export default {
		name:'vh-normal-thematic-activities-ad',
		mixins: [adBuryDotMixin],

		props: {
			// 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
			from: {
				type: [String, Number],
				default: ''
			},
			
			// 商品列表每一项
			item: {
				type: Object,
				default() {
					return {}
				}
			},
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
		methods: {
			onClick () {
				if (this.buryDotParams) {
					const { channel, region_id } = this.buryDotParams
					const genre = 3
					const button_id = this.item.id
					this.jump.h5JumpBD(this.item.modul_data.activity_url, channel, genre, region_id, button_id, this.from)
				} else {
					this.jump.h5Jump(this.item.modul_data.activity_url, this.from)
				}
			}
		}
	}
</script>

<style>
</style>