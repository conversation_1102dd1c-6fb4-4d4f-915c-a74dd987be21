<template>
	<view class="ih-form-item d-flex ptb-32-plr-00">
    <view v-if="label" class="flex-shrink w-148 font-wei-500 font-28 text-3 l-h-40">{{ label }}</view>
    <view class="flex-1">
      <slot>
        <view class="d-flex j-sb">
          <!-- <input v-model="model[prop]" :placeholder="currPlaceholder" placeholder-style="font-size: 28rpx; color: #999;" class="flex-1 h-40 font-28 text-3" /> -->
          <textarea v-model="model[prop]" @focus="onFocus" @blur="onBlur" @keydown.enter.prevent auto-height :placeholder="currPlaceholder" placeholder-style="font-size: 28rpx; color: #999;" class="flex-1 w-aut font-28 text-3 l-h-40" />
          <!-- <view v-if="model[prop] && isFocus" class="flex-e-c w-40 h-40" @click="onClear">
            <image class="mr-04 w-22 h-22" :src="ossIcon('/comm/del_gray.png')"></image>
          </view> -->
        </view>
      </slot>
    </view>
	</view>
</template>

<script>
export default{
  props: {
    label: {
      type: String,
      default: ''
    },
    model: {
      type: Object,
      default: () => ({})
    },
    prop: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    isFocus: false
  }),
  computed: {
    currPlaceholder ({ label, placeholder }) {
      return placeholder || `请输入${label}`
    }
  },
  methods: {
    onFocus () {
      this.isFocus = true
    },
    onBlur () {
      const timer = setTimeout(() => {
        this.isFocus = false
        timer && clearTimeout(timer)
      }, 100)
    },
    onClear () {
      this.model[this.prop] = ''
    },
  },
}
</script>
