<template>
  <u-popup :value="value" mode="center" :mask-close-able="false" width="552" height="414" border-radius="20" @input="onInput">
    <view class="p-rela w-552 h-414">
      <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-552 h-414" />
      <view class="p-rela pt-84">
        <view class="ptb-00-plr-52 font-wei-500 font-32 text-6 l-h-44 text-center">鉴于拍卖的特殊性，非质量问题，<text class="text-3">不支持七天无理由退货</text></view>
        <view class="flex-c-c mt-94">
          <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32" @click="onInput(false)">不同意</button>
          <button class="vh-btn flex-c-c ml-60 w-180 h-64 font-wei-500 font-28 text-ffffff l-h-40 b-rad-32" :class="time ? 'bg-d8d8d8' : 'bg-e80404'" @click="onAgree">同意<text class="font-24 text-e80404 l-h-34">{{ time ? `${time}s` : '' }}</text></button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    }
  },
  data: () => ({
    time: 3,
    interval: null,
  }),
  watch: {
    value () {
      if (this.value) {
        this.countDown()
      } else {
        this.interval && clearInterval(this.interval)
        this.time = this.$options.data().time
      }
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onAgree () {
      if (this.time) return
      this.$emit('agree')
    },
    countDown () {
      this.interval = setInterval(() => {
        this.time--
        if (this.time <= 0) {
          this.interval && clearInterval(this.interval)
        }
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
