<template>
	<u-popup 
	:maskCloseAble="maskCloseAble" 
	mode="bottom" 
	:popup="false" 
	v-model="value" 
	length="auto" 
	:safeAreaInsetBottom="safeAreaInsetBottom" 
	@close="close" 
	:z-index="uZIndex"
	:border-radius="40"
	>
		<view class="content">
			<!-- 选择器头部 -->
			<view class="vh-picker-header">{{ title }}</view>
			
			<!-- 选择器主体 -->
			<view class="vh-picker-body">
				<!-- 年份（以年为单位） -->
				<picker-view v-if="mode == 'year'" indicator-class="vh-picker-selected-view" :value="valueArr" @change="change" class="vh-picker-view" @pickstart="pickstart" @pickend="pickend">
					<picker-view-column v-if="!reset">
						<view class="vh-column-item" :class="valueArr[0] === index ? 'vh-column-select-item' : ''" v-for="(item, index) in range" :key="index">
							{{ getItemValue(item, 'year') }}年
						</view>
					</picker-view-column>
				</picker-view>
				
				<!-- 酒款颜色（左边为酒款类型、右边为酒款颜色+颜色值） -->
				<picker-view v-else-if="mode == 'wineColor'" indicator-class="vh-picker-selected-view" :value="valueArr" @change="change" class="vh-picker-view" @pickstart="pickstart" @pickend="pickend">
					<picker-view-column v-if="!reset">
						<view class="vh-column-item" :class="valueArr[0] === index ? 'vh-column-select-item' : ''" v-for="(item, index) in wineTypeList" :key="index">
							{{ item.name }}
						</view>
					</picker-view-column>
					<picker-view-column v-if="!reset">
						<view class="vh-column-item-wine-color" :class="valueArr[1] === index ? 'vh-column-select-item' : ''" v-for="(item, index) in wineColorList" :key="index">
							<view class="">
								{{ item.cate_name }}
							</view>
							
							<image class="wine-color" :src="item.cate_image"></image>
						</view>
					</picker-view-column>
				</picker-view>
				
				<!-- 适饮期（左右为年、中间有分割符） -->
				<picker-view v-else-if="mode == 'drinkingPeriod'" indicator-class="vh-picker-selected-view" :value="valueArr" @change="change" class="vh-picker-view" @pickstart="pickstart" @pickend="pickend">
					<template v-if="!reset">
						<view class="u-picker-selected-division-text">至</view>
						<picker-view-column v-for="(item, index) in range" :key="index">
							<view class="vh-column-item" v-for="(item1, index1) in item" :class="valueArr[index] === index1 ? 'vh-column-select-item' : ''" :key="index1">{{ getItemValue(item1, 'drinkingPeriod') }}年</view>
						</picker-view-column>
					</template>
				</picker-view>
			    
				<!-- 专家 -->
				<picker-view v-else-if="mode == 'expert'" indicator-class="vh-picker-selected-view" :value="valueArr" @change="change" class="vh-picker-view" @pickstart="pickstart" @pickend="pickend">
					<picker-view-column v-if="!reset">
						<view class="vh-column-item" :class="valueArr[0] === index ? 'vh-column-select-item' : ''" v-for="(item, index) in range" :key="index">
							{{ getItemValue(item, 'expert') }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
			
			<!-- 选择器底部 -->
			<view class="vh-picker-footer">
				<u-button
				:disabled="buttonIsDisabled"
				shape="circle" 
				:hair-line="false" 
				:ripple="true" 
				ripple-bg-color="#FFF"
				:custom-style="{ width:'646rpx', height:'72rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: buttonIsDisabled ? '#FCE4E3' : '#E80404', border:'none', borderRadius: '32rpx' }"
				@click="getResult('confirm')">确认</u-button>
			</view>
		</view>
	</u-popup>
</template>

<script>
	/**
	 * wine-comment-picker 弹出选择器
	 * @description 此选择器有两种弹出模式：年份、酒款颜色、多列（通用组件）
	 * @property {String} title 选择器的标题
	 * @property {String} mode 模式选择，year=年份，wineColor=酒款颜色、drinkingPeriod = 适饮期（默认year年份）
	 * @property {Array} range 自定义选择的数据（默认0）
	 * @property {String} range-key 当range参数的元素为对象时，指定Object中的哪个key的值作为选择器显示内容
	 * @property {Array} default-selector 数组形式，其中每一项表示选择了range对应项中的第几个
	 * @property {Boolean} safe-area-inset-bottom 是否开启底部安全区适配（默认true）
	 * @property {Boolean} mask-close-able 是否允许通过点击遮罩关闭Picker（默认true）
	 * @property {String Number} z-index 弹出时的z-index值（默认1075）
	 * @property {Boolean} show-sub-text 是否展示辅助内容（默认false）
	 * @example <vh-wine-comment-picker v-model="show" />
	 */
	export default {
		name: 'vh-wine-comment-picker',
		
		props: {
			// 顶部标题
			title: {
				type: String,
				default: ''
			},
			
			// 模式选择，year=年份，wineColor=酒款颜色、drinkingPeriod = 适饮期（默认year年份）
			mode: {
				type: String,
				default: 'year'
			},
			
			// 自定义选择的数据
			range: {
				type: Array,
				default() {
					return [];
				}
			},
			
			// 当 range 是一个 Array＜Object＞ 时，通过 range-key 来指定 Object 中 key 的值作为选择器显示内容
			rangeKey: {
				type: String,
				default: ''
			},
			
			// 提供的默认选中的下标
			defaultSelector: {
				type: Array,
				default() {
					return [0];
				}
			},
			
			// 是否开启底部安全区适配（默认true）
			safeAreaInsetBottom: {
				type: Boolean,
				default: false
			},
			
			// 是否允许通过点击遮罩关闭Picker
			maskCloseAble: {
				type: Boolean,
				default: true
			},
			
			// 通过双向绑定控制组件的弹出与收起
			value: {
				type: Boolean,
				default: false
			},
			
			// 弹出的z-index值
			zIndex: {
				type: [String, Number],
				default: 0
			},
			
		
		},
		
		data() {
			return {
				reset: false, //是否重置
				valueArr: [], //当前选中的数组
				// 酒款颜色板块
				wineTypeList:[], //酒类列表
				wineColorList:[], //酒体颜色列表
				wineTypeIndex: 0, //酒类索引
				wineColorIndex: 0, //酒体颜色索引
				moving: false // 列是否还在滑动中，微信小程序如果在滑动中就点确定，结果可能不准确
			};
		},
		
		mounted() {
			// console.log('===================我是子组件的range')
			// console.log( this.range ) 
			this.init();
		},
		
		computed: {
			// z-index
			uZIndex() {
				// 如果用户有传递z-index值，优先使用
				return this.zIndex ? this.zIndex : this.$u.zIndex.popup;
			},
			
			// 酒款类型发生变化
			wineTypeChange() {
				return `${this.wineTypeIndex}`;
			},
			
			// 按钮是否被禁用
			buttonIsDisabled() {
				if( this.moving ) return true
				if( this.mode === 'drinkingPeriod' ) {
					let val1 = this.range[0][this.valueArr[0]]
					let val2 = this.range[1][this.valueArr[1]]
					if( val1 >= val2 ) {
						return true
					}
				}
				return false
			}
		},
		
		watch: {
			propsChange() {
				this.reset = true;
				setTimeout(() => this.init(), 10);
			},
			
			// 如果酒款颜色发生变化，为了让picker联动起来，必须重置this.wineTypeList和this.wineColorList
			wineTypeChange(val) {
				console.log('--------------watch wineTypeList')
				this.wineColorList = this.range[1][this.wineTypeIndex]
			},
			
			// wineTypeList(val) {
			// 	console.log('--------------watch wineTypeList')
			// 	this.wineColorList = this.range[1][this.wineTypeIndex]
			// },
			
			// 如果地区发生变化，为了让picker联动起来，必须重置this.citys和this.areas
			// regionChange(val) {
			// 	this.citys = citys[this.province];
			// 	this.areas = areas[this.province][this.city];
			// },
			
			// 微信和QQ小程序由于一些奇怪的原因(故同时对所有平台均初始化一遍)，需要重新初始化才能显示正确的值
			value(n) {
				if (n) {
					this.reset = true;
					setTimeout(() => this.init(), 10);
				}
			}
		},
		
		methods: {
			// 初始化
			init() {
				this.valueArr = [];
				this.reset = false;
				if (this.mode === 'year') { //年份
					this.valueArr = this.defaultSelector;
				} else if ( this.mode == 'wineColor' ) { //酒款颜色
				    this.wineTypeList = this.range[0] //酒款列表
					this.wineColorList = this.range[1][0] //酒款颜色列表
					this.valueArr = [0, 0];
					
					// console.log('============== wineTypeList && wineColorList ')
					// console.log( this.wineTypeList )
					// console.log( this.wineColorList )
					
					// this.multiSelectorValue = this.defaultSelector;
					// if (this.params.province) {
					// 	this.valueArr.push(0);
					// 	this.setProvinces();
					// }
					// if (this.params.city) {
					// 	this.valueArr.push(0);
					// 	this.setCitys();
					// }
					// if (this.params.area) {
					// 	this.valueArr.push(0);
					// 	this.setAreas();
					// }
				}else if ( this.mode == 'drinkingPeriod' ) { //适饮期
					this.valueArr = this.defaultSelector;
					this.multiSelectorValue = this.defaultSelector;
				}else if( this.mode == 'expert' ) { //专家
					this.valueArr = this.defaultSelector;
				}
				this.$forceUpdate();
			},
			
			// 用户更改picker的列选项
			change(e) {
				this.valueArr = e.detail.value;
				console.log( this.valueArr )
				if( this.mode == 'wineColor') {
					let i = 0
					this.wineTypeIndex = this.valueArr[i++];
					this.wineColorIndex = this.valueArr[i++];
					 
					console.log( this.wineTypeIndex )
					console.log( this.wineColorIndex )
					
				}else if (this.mode == 'drinkingPeriod') { //适饮期
					let index = null;
					// 对比前后两个数组，寻找变更的是哪一列，如果某一个元素不同，即可判定该列发生了变化
					this.defaultSelector.map((val, idx) => {
						if (val != e.detail.value[idx]) index = idx;
					});
					// 为了让用户对多列变化时，对动态设置其他列的变更
					if (index != null) {
						this.$emit('columnchange', {
							column: index,
							index: e.detail.value[index]
						});
					}
				}
			},
			
			// 对单列和多列形式的判断是否有传入变量的情况
			getItemValue(item, mode) {
				// 目前(2020-05-25)uni-app对微信小程序编译有错误，导致v-if为false中的内容也执行，错误导致
				// 单列模式或者多列模式中的getItemValue同时被执行，故在这里再加一层判断
				if (this.mode == mode) {
					return typeof item == 'object' ? item[this.rangeKey] : item;
				}
			},
			
			// 标识滑动开始，只有微信小程序才有这样的事件
			pickstart() {
				// #ifdef MP-WEIXIN
				this.moving = true;
				// #endif
			},
			
			// 标识滑动结束
			pickend() {
				// #ifdef MP-WEIXIN
				this.moving = false;
				// #endif
			},
			
			// 用户点击确定按钮
			getResult(event = null) {
				// #ifdef MP-WEIXIN
				if (this.moving) return;
				// #endif
				let result = {};
				console.log( this.valueArr )
				if( this.mode == 'year' ) { //年份
					result = `${this.range[this.valueArr]}` 
				}else if( this.mode === 'wineColor' ) { //酒款颜色
					result.wineType = this.wineTypeList[this.wineTypeIndex]
					result.wineColor = this.wineColorList[this.wineColorIndex]
				}else if( this.mode == 'drinkingPeriod' ) { //适饮期
					let val1 = this.range[0][this.valueArr[0]]
					let val2 = this.range[1][this.valueArr[1]]
					result = `${val1}-${val2}`
				}else if( this.mode == 'expert' ) { //专家
					result = `${this.range[this.valueArr]}` 
				}
				if (event) this.$emit(event, result);
				this.close();
			},
			
			// 关闭
			close() {
				this.$emit('input', false);
			}
		}
	};
</script>

<style lang="scss" scoped>

	.content {
		position: relative;
		z-index: 999;
	}

	.vh-picker-header {
		width: 100%;
		height: 88rpx;
		display: flex;
		justify-content: center;
		align-items: flex-end;
		box-sizing: border-box;
		font-size: 32rpx;
		font-weight: 500;
		color: #000;
		line-height: 40rpx;
		background: #fff;
		position: relative;
	}

	.vh-picker-body {
		width: 100%;
		height: 470rpx;
		overflow: hidden;
		background-color: #fff;
	}

	.vh-picker-view {
		position: relative;
		height: 100%;
		box-sizing: border-box;
		padding: 0 32rpx;
	}

	::v-deep .vh-picker-selected-view { 
		padding: 18rpx 0;
	}

	.u-picker-selected-division-text {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		font-size: 24rpx;
		font-weight: bold;
		color: #333;
	}

	.vh-column-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.vh-column-item-wine-color {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.wine-color {
		width: 40rpx;
		height: 20rpx;
		border-radius: 10rpx;
		margin-left: 10rpx;
	}
		

	.vh-column-select-item {
		font-weight: bold;
		color: #333;
		transition: .1s all linear;
	}

	.vh-picker-footer { 
		height: 104rpx;
		background-color: #FFF;
		display: flex; 
		justify-content: center;
		align-items: center;
		box-shadow: 0 2rpx 12rpx 0 rgba(0,0,0,0.22);
	}
</style>
