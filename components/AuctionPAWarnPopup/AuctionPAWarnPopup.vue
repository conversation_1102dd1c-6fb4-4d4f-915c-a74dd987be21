<template>
  <u-popup :value="value" mode="center" width="592rpx" height="414rpx" border-radius="20" @input="onInput">
    <view class="p-rela w-592 h-414">
      <image :src="ossIcon('/auction/popup_bg_592_414.png')" class="p-abso w-592 h-414" />
      <view class="p-rela pt-84">
        <view class="font-wei-500 font-28 text-6 text-center l-h-48">
          <view>您还未绑定收款账户，请先绑定收款账户</view>
          <view>绑定后若{{ isBuyer ? '卖家' : '买家' }}违约您可获得赔付</view>
        </view>
        <view class="flex-sb-c ptb-00-plr-76 mt-86">
          <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32" @click="onInput(false)">再想想</button>
          <button class="vh-btn flex-c-c w-180 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="jumpPayeeAccount">确定</button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    isBuyer: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    jumpPayeeAccount () {
      this.onInput(false)
      this.jump.navigateTo(this.routeTable.pHAuctionPayeeAccount)
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
