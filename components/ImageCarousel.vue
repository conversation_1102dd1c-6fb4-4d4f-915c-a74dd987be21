<template>
  <!-- <view class="d-flex j-sb a-center mt-24">
    <view v-for="(inItem, inIndex) in swiperItem.list" :key="inIndex">
      <view class="w-140 h-140 d-flex j-center a-center b-rad-06 o-hid" :class="inIndex == 0 ? 'bg-li-23' : 'bg-li-24'">
        <vh-image :loading-type="4" :src="inItem.product_img" :width="120" :height="120" :border-radius="6" />
      </view>
      <view class="w-140 font-18 text-9 text-hidden-1">{{ inItem.title }}</view>
     
    </view>
  </view> -->
  <div
    class="carousel-container"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
    @mousedown="handleMouseDown"
    @mousemove="handleMouseMove"
    @mouseup="handleMouseUp"
    @mouseleave="handleMouseUp"
  >
    <div class="carousel" ref="carousel">
      <div
        v-for="(inItem, inIndex) in images"
        :key="inIndex"
        class="d-flex carousel-item flex-column j-center"
        @click="
          jump.appAndMiniJumpBD(1, `${routeTable.pgGoodsDetail}?id=${inItem.id}`, 2, 2, 105000, inItem.id, $vhFrom)
        "
      >
        <view
          class="w-140 h-140 d-flex j-center a-center b-rad-06 o-hid"
          :class="inIndex == 0 ? 'bg-li-23' : 'bg-li-24'"
        >
          <vh-image :loading-type="4" :src="inItem.product_img" :width="120" :height="120" :border-radius="6" />
        </view>
        <view class="w-140 font-18 text-9 text-hidden-1">{{ inItem.title }}</view>
        <view class="l-h-40">
          <text
            v-if="inItem.is_hidden_price || [3, 4].includes(inItem.onsale_status)"
            class="font-32 font-wei text-e80404"
            >价格保密</text
          >
          <template v-else>
            <text class="font-32 font-wei text-e80404"
              ><text class="font-18">¥</text>{{ inItem.group_price ? inItem.group_price : inItem.price }}</text
            >
          </template>
        </view>
      </div>
      <!-- 克隆第一个item用于无缝循环 -->
      <div
        v-if="images.length > 0"
        class="d-flex carousel-item flex-column j-center"
        @click="
          jump.appAndMiniJumpBD(
            1,
            `${routeTable.pgGoodsDetail}?id=${images[0].id}`,
            2,
            2,
            105000,
            images[0].id,
            $vhFrom
          )
        "
      >
        <view class="w-140 h-140 d-flex j-center a-center b-rad-06 o-hid" :class="0 == 0 ? 'bg-li-23' : 'bg-li-24'">
          <vh-image :loading-type="4" :src="images[0].product_img" :width="120" :height="120" :border-radius="6" />
        </view>
        <view class="w-140 font-18 text-9 text-hidden-1">{{ images[0].title }}</view>
        <view class="l-h-40">
          <text
            v-if="images[0].is_hidden_price || [3, 4].includes(images[0].onsale_status)"
            class="font-32 font-wei text-e80404"
            >价格保密</text
          >
          <template v-else>
            <text class="font-32 font-wei text-e80404"
              ><text class="font-18">¥</text>{{ images[0].group_price ? images[0].group_price : images[0].price }}</text
            >
          </template>
        </view>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
export default {
  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable', 'agreementPrefix', 'dev']),
  },
  name: 'ImageCarousel',
  props: {
    // 图片数组，每个元素包含 url 和可选的 alt 属性
    images: {
      type: Array,
      required: true,
      default: () => [],
    },
    // 轮播间隔时间（毫秒）
    interval: {
      type: Number,
      default: 3000,
    },
  },
  data() {
    return {
      currentPosition: 0,
      timer: null,
      isDragging: false,
      startX: 0,
      currentX: 0,
      initialPosition: 0,
    }
  },
  mounted() {
    this.startCarousel()
  },
  beforeDestroy() {
    this.clearTimer()
  },
  methods: {
    startCarousel() {
      this.clearTimer()
      this.timer = setInterval(() => {
        this.moveNext()
      }, this.interval)
    },
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    moveNext() {
      this.currentPosition++
      this.updateCarousel()

      if (this.currentPosition >= this.images.length) {
        setTimeout(() => {
          this.$refs.carousel.style.transition = 'none'
          this.currentPosition = 0
          this.updateTransform()

          setTimeout(() => {
            this.$refs.carousel.style.transition = 'transform 0.5s ease-in-out'
          }, 50)
        }, 500)
      }
    },
    updateCarousel() {
      this.updateTransform()
    },
    updateTransform() {
      if (this.$refs.carousel) {
        this.$refs.carousel.style.transform = `translateX(-${this.currentPosition * 50}%)`
      }
    },
    goToSlide(index) {
      this.currentPosition = index
      this.updateCarousel()
      this.startCarousel()
    },
    handleTouchStart(e) {
      this.clearTimer()
      this.isDragging = true
      this.startX = e.touches[0].clientX
      this.initialPosition = this.currentPosition
      this.$refs.carousel.style.transition = 'none'
    },

    handleTouchMove(e) {
      if (!this.isDragging) return

      const x = e.touches[0].clientX
      const diff = (this.startX - x) / this.$refs.carousel.offsetWidth
      this.currentPosition = this.initialPosition + diff
      this.updateTransform()
    },

    handleTouchEnd() {
      if (!this.isDragging) return

      this.isDragging = false
      this.$refs.carousel.style.transition = 'transform 0.5s ease-in-out'

      // 四舍五入到最近的整数位置
      this.currentPosition = Math.round(this.currentPosition)

      // 确保不会滑出边界
      if (this.currentPosition < 0) this.currentPosition = 0
      if (this.currentPosition >= this.images.length) this.currentPosition = this.images.length - 1

      this.updateCarousel()
      this.startCarousel()
    },

    handleMouseDown(e) {
      this.clearTimer()
      this.isDragging = true
      this.startX = e.clientX
      this.initialPosition = this.currentPosition
      this.$refs.carousel.style.transition = 'none'
    },

    handleMouseMove(e) {
      if (!this.isDragging) return

      const x = e.clientX
      const diff = (this.startX - x) / this.$refs.carousel.offsetWidth
      this.currentPosition = this.initialPosition + diff
      this.updateTransform()
    },

    handleMouseUp() {
      if (!this.isDragging) return

      this.isDragging = false
      this.$refs.carousel.style.transition = 'transform 0.5s ease-in-out'

      // 四舍五入到最近的整数位置
      this.currentPosition = Math.round(this.currentPosition)

      // 确保不会滑出边界
      if (this.currentPosition < 0) this.currentPosition = 0
      if (this.currentPosition >= this.images.length) this.currentPosition = this.images.length - 1

      this.updateCarousel()
      this.startCarousel()
    },
  },
}
</script>

<style scoped>
.carousel-container {
  /* width: 800px; */
  /* height: 400px; */
  position: relative;
  overflow: hidden;
  /* border-radius: 10px; */
  /* box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1); */
  touch-action: pan-y pinch-zoom;
  user-select: none;
}

.carousel {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease-in-out;
  transform: translateX(0);
  cursor: grab;
}

.carousel:active {
  cursor: grabbing;
}

.carousel-item {
  min-width: 50%;
  height: 100%;
  overflow: hidden;
}

.carousel-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-dots {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background-color 0.3s;
}

.dot.active {
  background-color: white;
}
</style>
