<template>
  <u-popup :value="value" mode="center" width="552rpx" height="414rpx" border-radius="20" @input="onInput">
    <view class="p-rela w-552 h-414">
      <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-552 h-414" />
      <view class="p-rela pt-84">
        <view class="ptb-00-plr-98 font-wei-500 font-32 text-3 text-center l-h-48">请先进行实名认证，认证通过后可发布拍品。</view>
        <view class="flex-sb-c ptb-00-plr-76 mt-86">
          <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32" @click="onInput(false)">取消</button>
          <button class="vh-btn flex-c-c w-180 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="jumpRealName">确定</button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
  },
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    jumpRealName () {
      this.onInput(false)
      this.feedback.toast({ title: '请前往APP认证~'})
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
