<template>
  <view>
    <view v-for="(item, index) in list" :key="item.id">
      <view v-if="index" class="mt-32 mb-32 h-02 bg-eeeeee"></view>
      <view class="flex-s-c" @click="jump.jumpUserHome(item.uid, $vhFrom)">
        <view class="p-rela w-60 h-60">
          <vh-image :loading-type="4" :src="item.avatar_image" :width="60" :height="60" shape="circle" />
          <image
            v-if="item.certified_info"
            :src="ossIcon('/comm/certified_24_26.png')"
            class="p-abso bottom-n-02 right-0 w-24 h-26"
          />
        </view>
        <view class="flex-s-c ml-12 o-hid">
          <text class="font-wei-600 font-26 text-3 l-h-36 text-hidden">{{ item.nickname }}</text>
          <view
            v-if="item.user_type == 2"
            class="flex-shrink flex-c-c ml-12 w-56 h-28 font-wei-500 font-22 text-ffffff bg-e80404 b-rad-04 w-s-now"
            >官方</view
          >
          <template v-else>
            <view class="flex-shrink flex-c-c ml-12 w-80 h-32 font-wei-600 font-22 text-ffffff bg-ff9127 b-rad-18">
              <text class="lv-text">LV.{{ item.user_level }}</text>
            </view>
            <view
              v-if="item.is_buy || item.is_deposit"
              class="flex-shrink flex-c-c ml-06 w-72 h-32 font-wei-500 font-22 text-ffffff bg-e80404 b-rad-18 w-s-now"
            >
              <text class="lv-text">{{ item.is_buy ? '已购' : '已订' }}</text>
            </view>
          </template>
        </view>
      </view>
      <view class="mt-12">
        <CommentContent :item="item"></CommentContent>
        <view class="mt-20">
          <CommentTime :item="item" @enjoy="onEnjoy" @reply="onReply"></CommentTime>
        </view>
      </view>
      <view v-if="item.$replysLen" class="mt-20 ptb-28-plr-24 bg-f9f9f9 b-rad-08">
        <view
          v-for="(replyItem, replyIndex) in item.replys.slice(0, item.$isShowAllReply ? item.$replysLen : showReplyLen)"
          :key="`reply-${replyItem.id}`"
          :class="replyIndex ? 'mt-32' : ''"
        >
          <view class="d-flex">
            <view class="p-rela w-42 h-42">
              <vh-image :loading-type="4" :src="replyItem.avatar_image" :width="42" :height="42" shape="circle" />
              <image
                v-if="replyItem.certified_info"
                :src="ossIcon('/comm/certified_24_26.png')"
                class="p-abso bottom-0 right-n-10 w-24 h-26"
              />
            </view>
            <view class="flex-c-c ml-12">
              <view
                class="w-max-194 font-wei-500 font-22 text-9 l-h-34 text-hidden"
                @click.stop="jump.jumpUserHome(replyItem.uid, $vhFrom)"
                >{{ replyItem.nickname }}</view
              >
              <view
                v-if="replyItem.user_type == 2"
                class="p-rela flex-c-c ml-10 w-40 h-22 font-wei-600 font-16 text-ffffff bg-e80404 b-rad-04 w-s-now"
                >官方</view
              >
              <template v-else>
                <view class="p-rela flex-c-c ml-10 w-54 h-22 font-wei-500 font-16 text-fca72e w-s-now">
                  LV.{{ replyItem.user_level }}
                  <view
                    style="
                      position: absolute;
                      width: 200%;
                      height: 200%;
                      border-radius: 8rpx;
                      border: 1px solid #ff9127;
                      transform: scale(0.5);
                    "
                  ></view>
                </view>
                <view v-if="replyItem.is_buy" class="p-rela flex-c-c ml-06 w-44 h-22 font-14 text-e80404 w-s-now">
                  已购
                  <view
                    style="
                      position: absolute;
                      width: 200%;
                      height: 200%;
                      border-radius: 8rpx;
                      border: 1px solid #ff8585;
                      transform: scale(0.5);
                    "
                  ></view>
                </view>
              </template>
              <view v-if="item.id !== replyItem.pid && replyItem.p_nickname" class="flex-c-c ml-12">
                <image :src="ossIcon('/comm/triangle_r_14_18.png')" class="mr-12 w-14 h-18"></image>
                <view
                  class="w-max-194 font-wei-500 font-22 text-9 l-h-34 text-hidden"
                  @click.stop="jump.jumpUserHome(replyItem.p_uid, $vhFrom)"
                  >{{ replyItem.p_nickname }}</view
                >
              </view>
            </view>
          </view>
          <view class="ml-54">
            <CommentContent :item="replyItem"></CommentContent>
            <view class="mt-20">
              <CommentTime :item="replyItem" @enjoy="onEnjoy" @reply="onReply"></CommentTime>
            </view>
          </view>
        </view>
        <view v-if="item.$replysLen > showReplyLen" class="flex-s-c mt-20 ml-54" @click="onToggleReply(item)">
          <text class="mr-08 font-28 text-9 l-h-40">{{ item.$isShowAllReply ? '收起' : '展开全部回复' }}</text>
          <image
            :src="ossIcon('/comm/arrow_b_20_12.png')"
            class="w-20 h-12 tran-2"
            :class="item.$isShowAllReply ? 't-ro-n-180' : ''"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data: () => ({
    showReplyLen: 2,
  }),
  computed: {
    sysPlatformAndroid() {
      return this.system.sysPlatformAndroid()
    },
  },
  methods: {
    onEnjoy(item) {
      this.$emit('enjoy', item)
    },
    onReply(item) {
      this.$emit('reply', item)
    },
    onToggleReply(item) {
      this.$emit('toggleReply', item)
    },
  },
}
</script>

<style lang="scss" scoped>
.lv-text {
  line-height: 32rpx;
  display: inline-block;
}
</style>
