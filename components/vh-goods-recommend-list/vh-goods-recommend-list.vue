<template>
	<view :style="[outerRecommendListConStyle]">
		<view class="bg-ffffff p-24 b-rad-10" :style="[innerRecommendListConStyle]">
			<view class="d-flex j-sb" :class="index == 0 ? '' : 'mt-24'" v-for="(item, index) in recommendList" :key="index" 
			@tap.stop="click(item)">
				<vh-image :loading-type="2" :src="item.banner_img[0]" :width="288" :height="180" :border-radius="6"/>
				
				<view class="flex-1 d-flex flex-column j-sb ml-20">
					<view class="text-hidden-3">
						<text class="ml-06 font-24 text-3 l-h-36">{{item.title}}</text>
					</view>
					
					<view class="mt-22 d-flex j-sb">
						<text v-if="item.is_hidden_price == 1 || [3, 4].includes(item.onsale_status)" class="font-32 text-ff0013 l-h-28">价格保密</text>
						<text v-else class="font-32 text-ff0013 l-h-28"><text class="font-20">¥</text>{{item.price}}</text>
						<!-- <text class="font-22 text-9 l-h-34">已售{{item.purchased + item.vest_purchased}}/限量{{item.limit_number}}份</text> -->
						<text class="font-22 text-9 l-h-34">已售{{item.purchased + item.vest_purchased}}份</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { mapState } from 'vuex'
	/**
	 * goods-recommend-list 猜你喜欢列表、热门推荐列表
	 * @description 该组件一般用于显示猜你喜欢列表、热门推荐列表。
	 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
	 * @property {String Number} outer-padding-bottom 推荐列表最外层容器跟底部的内边距，单位rpx（默认24）
	 * @property {String Number} inn-margin-left 推荐列表内层容器左外边距
	 * @property {String Number} inn-margin-right 推荐列表内层容器右外边距
	 * @property {Boolean} custom-click 是否自定义点击事件
	 * @event {Function} click 点击组件时触发
	 * @example <vh-goods-recommend-list />
	 */
	export default{
		name:"vh-goods-recommend-list",
		
		props: {
			// 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
			from: {
				type: [String, Number],
				default: ''
			},
			// 推荐列表最外层容器跟底部的内边距
			outerPaddingBottom: {
				type: [String, Number],
				default: 24
			},
			// 左边距
			innMarginLeft: {
				type: [String, Number],
				default: 24
			},
			// 右边距
			innMarginRight: {
				type: [String, Number],
				default: 24
			},
			// 是否自定义点击事件
			customClick:{
				type: Boolean,
				default: false
			},
			jumpType: {
				type: Number,
				default: 0
			},
			isInit: {
				type: Boolean,
				default: true
			}
		},
		
		data() {
			return {
				recommendList: [], //推荐列表
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
			
			// 推荐列表最外层容器样式
			outerRecommendListConStyle(){
				return{
					paddingBottom: this.outerPaddingBottom + 'rpx'
				}
			},
			
			// 推荐列表内层容器样式
			innerRecommendListConStyle() {
				let style = {}
				style.marginLeft = this.innMarginLeft + 'rpx'
				style.marginRight = this.innMarginRight + 'rpx'
				return style
			}
		},
		
		created() {
			if (this.isInit) this.getRecommendList()
		},
		
		methods:{
			// 获取推荐列表地
			async getRecommendList() {
				let res = await this.$u.api.recommendList()
				this.recommendList = res.data
			},
			
			// 点击 item = 商品列表某一项
			click( item ) {
				if( this.customClick ) this.$emit('click', item )
				if ( this.jumpType === 1 ) this.jump.appAndMiniJump(0, `${this.$routeTable.pgGoodsDetail}?id=${item.id}`, this.$vhFrom, 1)
				else this.jump.appAndMiniJump(0, `${this.routeTable.pgGoodsDetail}?id=${item.id}`, this.$vhFrom, 0 )
			}
		}
	}
</script>

<style scoped></style>
