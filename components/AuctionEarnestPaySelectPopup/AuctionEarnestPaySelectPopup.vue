<template>
  <u-popup :value="value" mode="bottom" :mask-close-able="false" width="100%" border-radius="30" @input="onInput">
    <view class="p-rela flex-c-c h-100">
      <view style="left: 26rpx; top: 50%; transform: translateY(-50%);" class="p-abso flex-c-c w-88 h-88" @click="onInput(false)">
        <image :src="ossIcon('/auction/close_44.png')" class="w-44 h-44" />
      </view>
      <text class="font-wei-600 font-32 text-6">请选择支付方式</text>
    </view>
    <view class="mt-52 ptb-00-plr-56 pb-70">
      <view v-if="isShowWxSelect" class="flex-sb-c h-150" @click="onPay(PAY_TYPE.WX)">
        <view class="flex-c-c">
          <image :src="ossIcon('/auction/wx_54.png')" class="w-54 h-54" />
          <text class="ml-34 font-32 text-3">微信支付</text>
        </view>
        <image v-if="type === PAY_TYPE.WX" :src="ossIcon('/auction/radio_h_32.png')" class="w-32 h-32" />
        <view v-else class="w-36 h-36 bg-e5e6e7 b-rad-p50"></view>
      </view>
      <view v-if="isShowWxSelect && isShowAliSelect" class="h-02 bg-eeeeee"></view>
      <view v-if="isShowAliSelect" class="flex-sb-c h-150" @click="onPay(PAY_TYPE.ALI)">
        <view class="flex-c-c">
          <image :src="ossIcon('/auction/ali_54.png')" class="w-54 h-54" />
          <text class="ml-34 font-32 text-3">支付宝支付</text>
        </view>
        <image v-if="type === PAY_TYPE.ALI" :src="ossIcon('/auction/radio_h_32.png')" class="w-32 h-32" />
        <view v-else class="w-36 h-36 bg-e5e6e7 b-rad-p50"></view>
      </view>
    </view>
    <view v-html="ailPayForm"></view>
  </u-popup>
</template>

<script>
import { mapState } from 'vuex'
import { MAppPaymentSource, MPaymentMethod } from '@/common/js/utils/mapperModel'
import { WX_APPID_PROD } from '@/common/js/fun/constant'
import wx from 'weixin-js-sdk'

const PAY_TYPE = {
  WX: 1,
  ALI: 2
}

export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    orderInfo: {
      type: Object,
      default: () => ({})
    },
  },
  data: () => ({
    PAY_TYPE,
    isWxProcess: false,
    type: '',
    ailPayForm: '',
    code: '',
    wxPayOpenId: '',
  }),
  computed: {
    ...mapState(['routeTable']),
    mainOrderNo ({ orderInfo }) {
      const { main_order_no = '', order_no = '' } = orderInfo || {}
      return main_order_no || order_no
    },
    pageFullPath () {
      return this.pages.getPageFullPath()
    },
    isShowWxSelect ({ $app, isWxProcess }) {
      if ($app) return true
      return isWxProcess
    },
    isShowAliSelect ({ $app, isWxProcess }) {
      if ($app) return true
      return !isWxProcess
    },
  },
  watch: {
    value () {
      this.type = this.$options.data().type
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    async onPay (type = PAY_TYPE.WX) {
      try {
        this.feedback.loading({ title: '支付中...' })
        this.type = type
        if (this.$app) {
          window.onPullAppPayFail = () => {
            this.feedback.hideLoading()
          }
          this.jump.pullAppPay(this.$vhFrom, Object.assign({}, this.orderInfo, { $payment_method: this.type, $from: 3 }))
          return
        }
        switch (type){
          case PAY_TYPE.WX:
            await this.wxPay()
            break
          case PAY_TYPE.ALI:
            await this.aliPay()
            break
        }
      } finally {
        if (!this.$app) this.type = this.$options.data().type
      }
    },
    async wxH5WxPay () {
      const queryParams = { pageFullPath: this.pageFullPath, vhType: 3 }
      const queryStr = Object.keys(queryParams).map(key => `${key}=${queryParams[key]}`).join('&')
      const return_url = `${location.origin}${this.routeTable.pPaySuccessJump}?${queryStr}`
      const params = {
        main_order_no: this.mainOrderNo,
        payment_method: 2,
        order_type: 2,
        is_cross: 0,
        return_url
      }
      const res = await this.$u.api.payMethod(params)
      location.href = res.data.h5_pay_info
    },
    h5WxPay() {
      const { token, uid } = uni.getStorageSync('loginInfo') || {}
      const query = {
        main_order_no: this.mainOrderNo,
        payment_method: 4,
        order_type: 1,
        is_cross: 0,
        token,
        uid,
        from: 5
      }
      const queryStr = Object.keys(query).map(key => `${key}=${query[key]}`).join('&')
      const href = `${this.h5WxPayOrigin}?${queryStr}`
      const newTab = window.open('/')
      if (newTab) {
        newTab.location = href
      } else {
        location.href = href
      }
    },
    async h5AliPay () {
      const newTab = window.open('/')
      const return_url = `${location.origin}${this.pageFullPath}`
      const params = {
        main_order_no: this.mainOrderNo,
        payment_method: 1,
        order_type: 2,
        is_cross: 0,
        return_url
      }
      const res = await this.$u.api.payMethod(params)
      const h5_pay_info = res.data.h5_pay_info
      if (newTab) {
        newTab.location = h5_pay_info
      } else {
        location.href = h5_pay_info
      }
    },
    async aliPay () {
      const { $paySuccessReturnUrl = '' } = this.orderInfo
      const returnUrl = $paySuccessReturnUrl || `${this.pageFullPath}${this.pageFullPath.includes('?') ? '&' : '?'}aePaySuccess=1`
      const params = {
        source: MAppPaymentSource.AuctionEarnest,
        main_order_no: this.mainOrderNo,
        payment_method: MPaymentMethod.AliH5,
        return_url: `${location.origin}${returnUrl}`
      }
      try {
        const res = await this.$u.api.appPayment(params)
        this.feedback.loading({ title: '支付中...' })
        this.ailPayForm = res?.data?.pay_info || ''
        this.$nextTick(() => {
          document?.forms['alipay_submit']?.submit()
          this.feedback.hideLoading()
        })
      } catch (e) {
        this.$emit('pullPayFail')
      }
    },
    async wxPay () {
      await this.loadWxPayOpenId()
      const params = {
        source: MAppPaymentSource.AuctionEarnest,
        main_order_no: this.mainOrderNo,
        payment_method: MPaymentMethod.WxJSAPI,
        open_id: this.wxPayOpenId,
      }
      try {
        const res = await this.$u.api.appPayment(params)
        const data = res?.data?.pay_info || {}
        const { timeStamp, nonceStr, signType, paySign } = data
        wx.chooseWXPay({
          timestamp: timeStamp,
          nonceStr,
          package: data.package,
          signType,
          paySign,
        })
      } catch (e) {
        this.$emit('pullPayFail')
      }
    },
    async queryPayStatus () {
      this.feedback.hideLoading()
      const res = await this.$u.api.getAuctionEarnestOrderDetail({ main_order_no: this.mainOrderNo })
      const { status = 0 } = res?.data || {}
      if ([1, 2, 3, 5].includes(status)) {
        this.$emit('paySuccess')
      } else {
        this.$emit('payFail')
      }
      return res
    },
    async loadWxPayOpenId () {
      try {
        if (this.wxPayOpenId) return
        const res = await this.$u.api.getWxPayOpenIdByCode({ code: this.code, genre: 1 })
        this.wxPayOpenId = res?.data?.openid || ''
      } catch (err) {
        const query = this.pages.getCurrenPage().$page.options
        const queryStr = Object.keys(query)
          .filter((key) => key !== 'code')
          .map((key) => `${key}=${query[key]}`)
          .join('&')
        const href = `${location.pathname}${queryStr ? `?${queryStr}` : ''}`
        location.href = href
      }
    },
    wxConfig () {
      let url = window.location.href.split("#")[0]
      if (this.$isDev) {
        url = 'https://activity.vinehoo.com/activities-v3/WechatCode'
      }
      this.$u.api.getJsapiSign({ url, appid: WX_APPID_PROD }).then(res => {
        const { appid, noncestr, sign, timestamp } = res || {}
        const configData = {
          debug: this.$isDev,
          appId: appid,
          nonceStr: noncestr,
          signature: sign,
          timestamp,
          jsApiList: ['chooseWXPay'],
        }
        console.log('configData', configData)
        wx.config(configData)
      })
    },
  },
  created () {
    const ua = window.navigator.userAgent.toLowerCase()
		this.isWxProcess = /micromessenger/.test(ua)
    if (this.isWxProcess) {
      const { code } = this.pages.getCurrenPage().$page.options
      if (this.$isDev) {
        if (!code) {
          location.href = `https://activity.vinehoo.com/activities-v3/RedirectFetchWxCode?appid=${WX_APPID_PROD}&redirectUrl=${encodeURIComponent(window.location.href.split("#")[0])}`
          return
        } else {
          this.code = code
        }
      } else {
        if (!code) {
          location.href =
            'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
            WX_APPID_PROD +
            '&redirect_uri=' +
            encodeURIComponent(window.location.href) +
            `&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect`
          return
        } else {
          this.code = code
        }
      }
      this.wxConfig()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
