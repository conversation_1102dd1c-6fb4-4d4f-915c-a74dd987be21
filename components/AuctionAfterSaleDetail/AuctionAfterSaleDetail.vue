<template>
	<!-- 售后明细 -->
	<view class="bg-ffffff b-rad-10 o-hid mt-20 ml-24 mr-24 ptb-00-plr-20">
		<!-- 发起时间 -->
		<view class="flex-sb-c bt-s-01-eeeeee ptb-32-plr-00">
			<text class="font-28 font-wei text-6">发起时间</text>
			<text class="font-28 text-3">{{ afterSaleInfo.created_time }}</text>
		</view>
		
		<!-- 售后类型 -->
		<view class="flex-sb-c bt-s-01-eeeeee ptb-32-plr-00">
			<text class="font-28 font-wei text-6">售后类型</text>
			<text class="font-28 font-wei text-3">{{ afterSaleInfo.service_type === 0 ? '仅退款' : '退货退款' }}</text>
		</view>
		
		<!-- 退款时间 -->
		<view v-if="afterSaleInfo.refund_time" class="flex-sb-c bt-s-01-eeeeee ptb-32-plr-00">
			<text class="font-28 font-wei text-6">退款时间</text>
			<text class="font-28 text-3">{{ afterSaleInfo.refund_time }}</text>
		</view>
		
		<!-- 退回方向 -->
		<view class="flex-sb-c bt-s-01-eeeeee ptb-32-plr-00">
			<text class="font-28 font-wei text-6">退回方向</text>
			<text class="font-28 text-3">原路退回</text>
		</view>
		
		<!-- 退货原因 -->
		<view class="flex-sb-c bt-s-01-eeeeee ptb-32-plr-00">
			<view class="font-28 font-wei text-6">退货原因</view>
			<view class="w-480 flex-e-c font-28 text-3">{{ afterSaleInfo.refund_reason }}</view>
		</view>
		
		<!-- 退款描述 -->
		<view v-if="afterSaleInfo.describe" class="bt-s-01-eeeeee ptb-32-plr-00">
			<view class="font-28 font-wei text-6">退款描述</view>
			<view class="bg-f6f6f6 b-rad-10 mt-20 p-24">{{ afterSaleInfo.describe }}</view>
		</view>
		
		<!-- 图片 -->
		<view v-if="afterSaleInfo.voucher.length" class="pb-32">
			<view class="font-28 font-wei text-3">图片</view>
			<view class="d-flex flex-nowrap a-center">
				<view class="w-210 h-210 b-rad-10 mt-20 mr-16" v-for="(item, index) in afterSaleInfo.voucher" :key="index" @click="image.previewImageList(afterSaleInfo.voucher, index)">
					<vh-image :loading-type="2" :src="item" :width="210" :height="210" :border-radius="10" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name: "AuctionAfterSaleDetail",
		
		props: {
			// 类型 0 = 买家、1 = 卖家 
			type: {
				type: [Number, String],
				default: 0
			},
			
			// 售后信息
			afterSaleInfo : {
				type: Object,
				default: function() {
					return {};
				}
			},
		}
	}
</script>

<style>
</style>