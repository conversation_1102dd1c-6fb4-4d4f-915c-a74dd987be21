<template>
	<view class="p-rela" @click="handleJump(`${routeTable.pHAuctionGoodsDetail}?id=${item.id}`)">
		<view class="p-abso top-16 left-16 z-04 flex-c-c w-56 h-30 font-18 text-e80404 bg-fde8eb b-rad-15">拍品</view>
		<!-- view class="p-abso top-12 left-12 z-04 bg-eeeeee b-rad-30 ptb-02-plr-10 font-18 text-6">次日达</view> -->
		<view class="flex-c-c pt-16">
			<vh-image :src="item.banner_img" :loadingType="1" :width="imgWidth" :height="imgHeight" :isResize="item.$imgIsResize" resizeUsePx />
		</view>
		<view class="h-46 bg-li-41 flex-c-c font-20 text-e80404"><text class="p-rela top-04">{{ item.sell_time }}</text></view>
		<view :class="pdClazz">
			<view :class="titleClazz">{{ item.title }}</view>
			<view class="flex-sb-e" :class="mtClazz">
				<view class="flex-s-e">
					<view
						:style="[item.$isBigScreen ? { height: '17px', fontSize: '16px', lineHeight: '17px' } : { height: '15px', fontSize: '14px', lineHeight: '15px' }]"
						class="h-30 font-28 font-wei-500 text-e80404 l-h-30"
					><text :style="[item.$isBigScreen ? { fontSize: '10px' } : { fontSize: '9px' }]" class="font-18">¥</text>{{ item.price }}</view>
					<view
						:style="[item.$isBigScreen ? { fontSize: '10px', lineHeight: '14px' } : { fontSize: '9px', lineHeight: '13px' }]"
						class="ml-06 font-18 text-9 l-h-26"
					>起拍</view>
				</view>
				<view
					:style="[item.$isBigScreen ? { fontSize: '10px', lineHeight: '14px' } : { fontSize: '9px', lineHeight: '13px' }]"
					class="ml-06 font-18 text-9 l-h-26"
				>围观{{ item.pageviews }}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import secondWfitemMixin from '@/common/js/mixins/secondWfitemMixin'
	export default {
		mixins: [secondWfitemMixin],
		computed: {
			imgWidth () {
				return `${uni.upx2px(this.item.$wfitemWidth) - uni.upx2px(16) * 2}px`
			},
			imgHeight () {
				return `${uni.upx2px(this.item.$wfitemWidth) - uni.upx2px(16) * 2}px`
			}
		}
		// props: {
		// 	item: {
		// 		type: Object,
		// 		default: () => ({
		// 			"id": 1,
		// 			"title": "猛男做的自然酒Bibbianad",
		// 			"banner_img": "/vine/a.jpg",
		// 			"price": 1,
		// 			"sell_time": "2023-05-17 13:44:59",
		// 			"pageviews": 152401,//围观量
		// 			"libels": [
		// 				{
		// 					"type": 1,
		// 					"title": "新人价"
		// 				}
		// 			]
		// 		})
		// 	}
		// }
	}
</script>
