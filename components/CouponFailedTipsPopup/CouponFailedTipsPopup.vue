<template>
  <u-popup :value="value" mode="center" width="552rpx" height="414rpx" border-radius="20" @input="onInput">
    <view class="p-rela wh-p100">
      <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso wh-p100" />
      <view class="p-rela pt-84">
        <view class="font-wei-500 font-32 text-3 l-h-44 text-center">您有{{ count }}张优惠券因商品下架已失效</view>
        <view class="mt-20 font-24 text-3 l-h-34 text-center">为您补偿同价值或更高价值优惠券</view>
        <view class="flex-c-c mt-100">
          <button class="vh-btn flex-c-c w-232 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="onInput(false)">去查看</button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    count: {
      type: Number,
      default: 0
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
