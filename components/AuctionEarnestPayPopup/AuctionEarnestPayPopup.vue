<template>
  <u-popup :value="value" mode="bottom" width="100%" height="1068rpx" border-radius="20" @input="onInput">
    <view class="flex-s-c ptb-00-plr-24 h-148">
      <text class="font-wei-500 font-32 text-3">支付保证金</text>
    </view>
    <view class="h-08 bg-f7f7f7"></view>
    <view class="ptb-32-plr-24">
      <view class="d-flex a-baseline">
        <text class="font-wei-500 font-32 text-6">金额</text>
        <text class="ml-40 font-wei-500 font-52 text-3"><text class="font-28">¥</text>{{ money }}</text>
        <view v-if="earnestCouponInfo && earnestCouponInfo.id" class="ml-10 flex-c-c ptb-00-plr-10 h-40 font-20 text-e80404 bg-fff2f2 b-rad-20">
          <text>已减</text>
          <text class="ml-06">¥<text class="font-20">{{ parseFloat(earnestCouponInfo.coupon_face_value) }}</text></text>
        </view>
      </view>
      <view class="mt-20">
        <view v-for="(item, index) in list" :key="index" class="mb-10 font-26 text-6 l-h-36">{{ index + 1 }}.<text v-html="item" /></view>
      </view>
    </view>
    <view class="h-08 bg-f7f7f7"></view>
    <view class="mt-40 ptb-00-plr-24 h-188" @click="jump.navigateTo(`${routeTable.pEAddressManagement}?comeFrom=8`)">
      <view v-if="addressInfo.id" class="flex-sb-c ptb-00-plr-24 h-p100 bg-f9f9f9 b-rad-10">
        <view>
          <view>
            <text class="font-wei-500 font-32 text-3">{{ addressInfo.consignee }}</text>
            <text class="ml-20 font-28 text-9">{{ addressInfo.consignee_phone }}</text>
          </view>
          <view class="flex-s-c">
            <view v-if="addressInfo.is_default" class="flex-c-c mr-08 w-50 h-24 font-wei-500 font-18 text-ffffff bg-e80404 b-rad-04">默认</view>
            <view v-if="addressInfo.label" class="flex-c-c mr-08 w-50 h-24 font-wei-500 font-18 text-ffffff bg-2e7bff b-rad-04">{{ addressInfo.label }}</view>
            <text class="font-24 text-3 l-h-34">{{ addressInfo.province_name }} {{ addressInfo.city_name }} {{ addressInfo.town_name }}</text>
          </view>
          <view class="font-24 text-3 l-h-34">{{ addressInfo.address }}</view>
        </view>
        <image :src="ossIcon('/after_sale_detail/arrow_right_12x20.png')" class="w-12 h-20">
      </view>
      <view v-else class="h-p100">
        <image :src="ossIcon('/order-confirm/add_bg.png')" class="p-abso w-702 h-188" />
				<view class="p-rela flex-c-c flex-column h-p100">
					<image :src="ossIcon('/order-confirm/add_ico.png')" class="w-84 h-84" />
					<text class="mt-14 font-28 text-3 l-h-40">新建收货地址</text>
				</view>
      </view>
    </view>
    <view class="flex-c-c mt-40">
      <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="addressInfo.id && checked ? 'bg-e80404' : 'bg-fce4e3'" @click="onSubmit">提交</button>
    </view>
    <view class="flex-c-c mt-24">
      <vh-check :checked="checked" :width="26" :height="26" @click="checked = !checked" />
      <view class="ml-10 font-24 text-9">
        <text @click="checked = !checked">阅读并接受</text>
        <text class="text-83a6cc" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/auctionEarnestRules`, $vhFrom)">《保证金规则》</text>
        <text class="text-83a6cc" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/auctionRules`, $vhFrom)">《拍卖规则》</text>
        <text class="text-83a6cc" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/auctionBiddingServiceProtocol`, $vhFrom)">《竞买协议》</text>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import { MAuctionEarnestType } from '@/common/js/utils/mapperModel'

export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    goods: {
      type: Object,
      default: () => ({})
    },
    earnestCouponInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data: () => ({
    list: [
      '保证金由委托方确认，只有缴纳保证金后方可获得竞拍资格；',
      '竞拍结束后，如果您未出价或者被淘汰，平台将于72小时内退还保证金至原支付账户；',
      '如竞拍成功，保证金将会在您<text class="text-e80404">【支付货款后72小时内退至原支付账户】</text>',
      '如竞拍成功后48小时内未付款则被视为违约，<text class="text-e80404">保证金将被全额扣除，同时扣除30个信用分，并禁止在未来的一个月内参与拍卖</text>。'
    ],
    addressInfo: {},
    checked: false
  }),
  computed: {
    ...mapState(['routeTable', 'addressInfoState', 'agreementPrefix']),
    money () {
      const { id, coupon_face_value } = this.earnestCouponInfo
      if (id) {
        return this.goods.margin > coupon_face_value ? this.goods.margin - coupon_face_value : 0.01
      }
      return this.goods.margin
    }
  },
  methods: {
    ...mapMutations(['muPayInfo']),
    onInput (value) {
      this.$emit('input', value)
    },
    initAddressInfo () {
      if (Object.keys(this.addressInfoState).length) {
        this.addressInfo = this.addressInfoState
      } else {
        this.loadAddressInfo()
      }
    },
    async loadAddressInfo () {
      const res = await this.$u.api.addressList()
      const list = res?.data?.list || []
      if (list.length) {
        const findDefaultItem = list.find(item => item.is_default)
        if (findDefaultItem) {
          this.addressInfo = findDefaultItem
        } else {
          this.addressInfo = list[0]
        }
      }
    },
    async onSubmit () {
      console.log('onSubmit', this.addressInfo.id, this.checked)
      if (!this.addressInfo.id || !this.checked) return
      this.feedback.loading({ title: '提交中...' })
      let orderFrom = 2
      const { $android, $ios } = this
      if ($android) orderFrom = 1
      else if ($ios) orderFrom = 0
      const params = {
        order_from: orderFrom,
        type: MAuctionEarnestType.Bidding,
        goods_id: this.goods.id,
        address_id: this.addressInfo.id
      }
      if (this.earnestCouponInfo.id) {
        params.coupon_issue_id = this.earnestCouponInfo.id
      }
      try {
        const res = await this.$u.api.createAuctionEarnestOrder(params)
        const data = res?.data || {}
        this.$emit('orderCreateSuccess', data)
      } catch (e) {
        this.$emit('orderCreateError')
      }
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
