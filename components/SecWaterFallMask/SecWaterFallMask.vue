<template>
	<view class="swf-mask p-abso left-0 top-0 flex-c-c-c wh-p100 bg-000-000-000-069 z-100" @click="close">
    <button class="vh-btn flex-c-c w-190 h-44 font-wei-500 font-20 text-3 bg-ffffff b-rad-22" @click.stop="report(MUserPortraitFeedbackType.LoseInterest)">
      <image :src="ossIcon('/second_hair/heart_broken_24_20.png')" class="mr-06 w-24 h-20" />
      <text>不感兴趣</text>
    </button>
    <button v-if="item.type === 'news'" class="vh-btn flex-c-c w-190 h-44 font-wei-500 font-20 text-3 bg-ffffff b-rad-22" @click.stop="report(MUserPortraitFeedbackType.ContentRepetition)">内容重复</button>
    <button v-else-if="item.type === 'goods'" class="vh-btn flex-c-c w-190 h-44 font-wei-500 font-20 text-3 bg-ffffff b-rad-22" @click.stop="report(MUserPortraitFeedbackType.HaveBought)">已经买了</button>
  </view>
</template>

<script>
  import { MSecondsWfitemType, MUserPortraitFeedbackType } from '@/common/js/utils/mapperModel'

	export default {
    props: {
      item: {
        type: Object,
        default: () => ({})
      }
    },
    data: () => ({
      MUserPortraitFeedbackType
    }),
    computed: {
      $wfitemIdKey () {
        return this.item.$wfitemIdKey
      }
    },
    methods: {
      close () {
        this.$emit('changeShowSecWfitemMaskId', '')
      },
      async report (feedback_type) {
        const isLogin = await this.login.isLoginV3(this.$vhFrom)
        if (!isLogin) return
        const { type, id } = this.item
        let genre = type
        if (type === MSecondsWfitemType.Goods) genre = 'second_goods'
        const params = {
          genre,
          feedback_type,
          item_id: id
        }
        await this.$u.api.reportUserPortrait(params)
        this.$emit('remove', this.item[this.$wfitemIdKey])
        this.close()
      }
    }
	}
</script>

<style lang="scss" scoped>
  .swf-mask {
    .vh-btn+.vh-btn {
      margin-top: 48rpx;
    } 
  }
</style>
