<template>
  <u-popup :value="value" mode="center" width="488" height="550" border-radius="20" @input="onInput" class="bg-transparent-popup">
    <view class="p-rela w-p100 h-p100">
      <image :src="ossIcon('/auction/kefu_bg_488_500.png')" class="p-abso w-p100 h-p100" />
      <view class="p-rela pt-50">
        <view class="font-wei-500 font-28 text-3 text-center l-h-40">
          <view>请扫码添加</view>
          <view>您的拍卖管家</view>
        </view>
        <view class="flex-c-c mt-70"  @longpress="onLongpress">
          <image :src="ossIcon('/auction/kefu_code_292_290.png')" class="w-292 h-290" />
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onLongpress () {
      if (this.$app) {
        const url = this.ossIcon('/auction/kefu_code_292_290.png')
        wineYunJsBridge.openAppPage({
          client_path: { ios_path: 'downloadFile', android_path: 'downloadFile' }, 
          ad_path_param: [
            { ios_key: 'url', ios_val: url, android_key: 'url', android_val: url },
            { android_key: 'suffix', android_val: 'png' }
          ]
        })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
