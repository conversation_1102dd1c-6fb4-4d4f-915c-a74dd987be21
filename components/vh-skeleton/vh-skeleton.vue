<template>
	<view class="p-rela o-hid" :style="[skyletonContainerStyle]">
		<!-- 默认骨架屏 -->
		<view v-if="type == 0" class="w-p100 h-p100 d-flex flex-column j-center a-center">
			<view class="p-rela font-48 font-wei text-e5e5e5">VINEHOO</view>
			<view class="font-28 text-e5e5e5">加载中...</view>
		</view>
		
		<!-- 商品详情 -->
		<view v-if="type == 1">
			<!-- 状态栏高度 -->
			<view class="bg-ffffff" :style="{ height: statusBarHeight + 'px' }"></view>
			
			<!-- 轮播图 -->
			<view class="h-466">
				<vh-image :loading-type="2" :height="466" />
			</view>
			
			<!-- 底图（非秒发表现为底图，秒发不显示底图） -->
			<view class="d-flex h-98 bg-e5e5e5 op-060"></view>
			
			<!-- 商品信息 -->
			<view class="bg-ffffff pt-32 pr-24 pb-44 pl-24">
				<view class="h-40 bg-f5f5f5 b-rad-10"></view>
				<view class="h-40 bg-f5f5f5 b-rad-10 mt-10"></view>
				<view class="w-408 h-40 bg-f5f5f5 b-rad-10 mt-10"></view>
				<view class="h-36 bg-f5f5f5 b-rad-10 mt-20"></view>
				<view class="w-200 h-36 bg-f5f5f5 b-rad-10 mt-10"></view>
				<view class="d-flex j-sb a-center mt-36">
					<view class="w-522 h-28 bg-f5f5f5 b-rad-14"></view>
					<view class="w-160 h-28 bg-f5f5f5 b-rad-14"></view>
				</view>
			</view>
			
			<!-- 预计发货时间 -->
			<view class="bg-ffffff b-rad-10 d-flex a-center mt-20 mr-24 ml-24 pt-40 pb-40 pl-24 pr-24">
				<view class="w-166 h-40 bg-f5f5f5 b-rad-10"></view>
				<view class="w-432 h-40 bg-f5f5f5 b-rad-10 ml-24"></view>
			</view>
			
			<!-- 产品金刚区 -->
			<view class="bg-ffffff b-rad-10 d-flex j-sb a-center mt-20 mr-24 ml-24 pt-40 pb-40 pl-24 pr-24">
				<view class="w-140 h-40 bg-f5f5f5 b-rad-10"></view>
				<view class="w-140 h-40 bg-f5f5f5 b-rad-10"></view>
				<view class="w-140 h-40 bg-f5f5f5 b-rad-10"></view>
				<view class="w-140 h-40 bg-f5f5f5 b-rad-10"></view>
			</view>
			
			<!-- 商品详情 -->
			<view class="bg-ffffff b-rad-10 mt-20 pt-40 pl-24 pb-40 pr-24">
				<view class="w-144 h-50 bg-f5f5f5 b-rad-10"></view>
				<view class="h-34 mt-32 bg-f5f5f5 b-rad-10"></view>
			</view>
			
			<!-- 底部操作按钮 -->
			<view class="p-fixed z-999 bottom-0 w-p100 h-104 bg-ffffff d-flex j-sb a-center b-sh-00021200-022 pl-70 pr-24">
				<!-- 评论、收藏 -->
				<view class="d-flex j-sb a-center">
					<view class="d-flex flex-column a-center">
						<view class="w-32 h-32 bg-f5f5f5 b-rad-06"></view>
						<view class="w-44 h-32 bg-f5f5f5 b-rad-06 mt-06"></view>
					</view>
					<view class="d-flex flex-column a-center ml-78">
						<view class="w-32 h-32 bg-f5f5f5 b-rad-06"></view>
						<view class="w-44 h-32 bg-f5f5f5 b-rad-06 mt-06"></view>
					</view>
				</view>
				
				<!-- 闪购、秒发、跨境、尾货 -->
				<view class="d-flex a-center">
					<view class="w-208 h-64 bg-ff9127 b-rad-32"></view>
					<view class="w-208 h-64 bg-e80404 b-rad-32 ml-24"></view>
				</view>
				
			</view>
		</view>
		
		<!-- 普通商品确认订单页，兔头商品确认订单页 -->
		<view v-if="type == 2" class="pt-20">
			<!-- 收货地址 -->
			<view class="bg-ffffff b-rad-10 d-flex j-sb a-center ml-24 mr-24 ptb-32-plr-24">
				<view class="">
					<view class="d-flex a-center">
						<view class="w-118 h-44 bg-e5e5e5"></view>
						<view class="w-178 h-40 bg-e5e5e5 ml-14"></view>
					</view>
					<view class="w-500 h-68 bg-e5e5e5 mt-12"></view>
				</view>
				<view class="w-12 h-20 bg-e5e5e5"></view>
			</view>
			
			<!-- 订单商品信息 -->
			<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24 ptb-32-plr-24">
				<view class="d-flex a-center">
					<view class="w-132 h-44 bg-e5e5e5"></view>
					<view class="w-72 h-30 bg-e5e5e5 ml-12"></view>
				</view>
				<view class="mt-28 d-flex">
					<view class="w-246 h-152 bg-e5e5e5 b-rad-06"></view>
					<view class="flex-1 d-flex flex-column j-sb ml-12">
						<view class="">
							<view class="w-390 h-68 bg-e5e5e5"></view>
							<view class="w-216 h-28 bg-e5e5e5 mt-08"></view>
						</view>
						<view class="d-flex j-sb a-center">
							<view class="w-64 h-40 bg-e5e5e5"></view>
							<view class="w-22 h-36 bg-e5e5e5"></view>
						</view>
					</view>
				</view>
			    <view class="mt-32 d-flex j-sb a-center">
			    	<view class="w-56 h-40 bg-e5e5e5"></view>
					<view class="w-200 h-36 bg-e5e5e5"></view>
			    </view>
			</view>
			
			<!-- 商品金额明细 -->
			<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
				<view class="ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-408 h-40 bg-e5e5e5"></view>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-112 h-40 bg-e5e5e5"></view>
					<view class="w-68 h-40 bg-e5e5e5"></view>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-112 h-40 bg-e5e5e5"></view>
					<view class="w-68 h-40 bg-e5e5e5"></view>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-112 h-40 bg-e5e5e5"></view>
					<view class="w-68 h-40 bg-e5e5e5"></view>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-112 h-40 bg-e5e5e5"></view>
					<view class="w-68 h-40 bg-e5e5e5"></view>
				</view>
				
				<view class="d-flex j-end a-center ml-24 mr-24 pt-32 pb-32">
					<view class="w-84 h-40 bg-e5e5e5"></view>
					<view class="w-104 h-40 bg-e5e5e5 ml-10"></view>
				</view>
			</view>
			
			<!-- 结算按钮 -->
			<view class="p-fixed bottom-0 z-999 w-p100 h-104 bg-ffffff b-sh-00021200-022 d-flex j-sb a-center pl-32 pr-24">
				<view class="d-flex a-center">
					<view class="w-84 h-40 bg-e5e5e5"></view>
					<view class="w-118 h-56 bg-e5e5e5 ml-10"></view>
				</view>
				
				<view class="w-208 h-64 bg-e80404 b-rad-32"></view>
			</view>
		</view>
		
		<!-- 普通商品订单详情、兔头订单商品详情、售后详情 -->
		<view v-if="type == 3">
			<!-- banner -->
			<view class="p-abso z-0 top-0 w-p100 h-270 bg-e80404"></view>
			
			<!-- 待付款 -->
			<view class="p-rela z-01 d-flex j-sb a-center ml-48 mr-48">
				<view class="">
					<view class="w-108 h-50 bg-ffffff"></view>
					<view class="w-450 h-40 bg-ffffff mt-08"></view>
				</view>
				<view class="w-128 h-128 bg-ffffff b-rad-10"></view>
			</view>
			
			<!-- 收货地址 -->
			<view class="p-rela z-01 bg-ffffff b-rad-10 d-flex j-sb a-center mt-32 ml-24 mr-24 pt-32 pr-24 pb-32 pl-24">
				<view class="d-flex">
					<view class="w-44 h-44 bg-e5e5e5 b-rad-10"></view>
					<view class="w-480 ml-16">
						<view class="d-flex a-center">
							<view class="w-114 h-44 bg-e5e5e5"></view>
							<view class="w-178 h-40 bg-e5e5e5 ml-36"></view>
						</view>
						<view class="w-480 h-36 bg-e5e5e5 mt-12"></view>
						<view class="w-220 h-36 bg-e5e5e5 mt-08"></view>
					</view>
				</view>
			</view>
			
			<!-- 订单列表 -->
			<view class="p-rela z-01">
				<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24 pt-32 pr-20 pb-32 pl-28">
					<view class="d-flex j-sb">
						<view class="w-246 h-152 bg-e5e5e5 b-rad-06"></view>
						<view class="flex-1 d-flex flex-column j-sb ml-12">
							<view class="w-390 h-68 bg-e5e5e5"></view>
							<view class="w-98 h-32 bg-e5e5e5 b-rad-04 mt-08"></view>
							<view class="mt-04 d-flex j-sb a-end">
								<view class="w-52 h-32 bg-e5e5e5"></view>
								<view class="w-68 h-40 bg-e5e5e5"></view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 订单明细 -->
			<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-112 h-40 bg-e5e5e5"></view>
					<view class="w-274 h-40 bg-e5e5e5"></view>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-112 h-40 bg-e5e5e5"></view>
					<view class="w-274 h-40 bg-e5e5e5"></view>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-112 h-40 bg-e5e5e5"></view>
					<view class="w-274 h-40 bg-e5e5e5"></view>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-112 h-40 bg-e5e5e5"></view>
					<view class="w-274 h-40 bg-e5e5e5"></view>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-112 h-40 bg-e5e5e5"></view>
					<view class="w-274 h-40 bg-e5e5e5"></view>
				</view>
				
				<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-112 h-40 bg-e5e5e5"></view>
					<view class="w-274 h-40 bg-e5e5e5"></view>
				</view>
				
				<view class="ml-24 mr-24 pt-32 pb-32">
					<view class="w-112 h-40 bg-e5e5e5"></view>
					<view class="h-40 bg-e5e5e5 b-rad-10 mt-20"></view>
				</view>
			</view>
			
		</view>
		
		<!-- 闪购首页 -->
		<view v-if="type == 4">
			<view class="h-60 bg-f5f5f5 b-rad-10 mt-20 ml-24 mr-24" />
			<view class="h-354 bg-f5f5f5 b-rad-10 o-hid mt-20 ml-24 mr-24">
				<vh-image :loading-type="2" :height="354"/>
			</view>
			<view class="h-48 bg-f5f5f5 b-rad-10 mt-20 ml-24 mr-24" />
			<view class="h-434 bg-f5f5f5 b-rad-10 mt-20 ml-24 mr-24 o-hid">
				<vh-image :loading-type="2" :height="434"/>
			</view>
			<view class="mt-20 ml-24 mr-24 pr-24 pb-28 pl-24">
				<view class="h-44 bg-f5f5f5 mt-24" />
				<view class="w-300 h-44 bg-f5f5f5 mt-08" />
				<view class="h-36 bg-f5f5f5 mt-12" />
				<view class="d-flex j-sb a-center mt-28">
					<view class="d-flex a-center">
						<view class="w-112 h-44 bg-f5f5f5" />
						<view class="w-80 h-36 bg-f5f5f5 ml-16" />
					</view>
					<view class="w-234 h-36 bg-f5f5f5" />
				</view>
			</view>
		</view>
		
		<!-- 首页 -->
		<view v-if="type == 5">
			<view class="h-344 bg-f5f5f5 b-rad-10 o-hid mt-20 ml-24 mr-24">
				<vh-image :loading-type="2" :height="344" />
			</view>
			<view class="h-26 bg-f5f5f5 mt-20 ml-48 mr-48" />
			<view class="b-rad-10 mt-20 ml-24 mr-24 p-24">
				<view class="d-flex j-sb a-center">
					<view class="d-flex flex-column j-center a-center" v-for="(item, index) in 5" :key="index">
						<!-- <view class="w-84 h-84 bg-f5f5f5 b-rad-p50 o-hid" >
							<vh-image :loading-type="2" :height="84" shape="circle"/>
						</view> -->
						<vh-image :loading-type="2" :width="84" :height="84" :border-radius="8"/>
						<view class="w-60 h-34 bg-f5f5f5 b-rad-10 mt-12" />
					</view>
				</view>
				<view class="h-62 bg-f5f5f5 mt-20" />
			</view>
			<view class="h-406 bg-f5f5f5 b-rad-10 o-hid mt-20 ml-24 mr-24">
				<vh-image :loading-type="2" :height="406" />
			</view>
			<view class="h-406 bg-f5f5f5 b-rad-10 o-hid mt-20 ml-24 mr-24">
				<vh-image :loading-type="2" :height="406" />
			</view>
		</view>
		
		<!-- 社区首页 -->
		<view v-if="type == 6">
			<!-- 话题 battle -->
			<view class="bg-ffffff b-rad-20 mt-20 ml-24 mr-24 pt-16 pr-24 pb-24 pl-24">
				<!-- 更多battle -->
				<view class="d-flex j-sb a-center">
					<view class="w-200 h-44 bg-ececec b-rad-08" />
					<view class="w-114 h-40 bg-ececec b-rad-08" />
				</view>
				
				<!-- battle -->
				<view class="w-p100 h-416 bg-li-3 b-rad-20 mt-20">
				</view>
			</view>
			
			<!-- 热门话题 -->
			<view class="bg-ffffff b-rad-20 mt-20 ml-24 mr-24 pt-16 pr-24 pb-10 pl-24">
				<view class="d-flex j-sb a-center">
					<view class="w-178 h-44 bg-ececec b-rad-08" />
					<view class="w-114 h-40 bg-ececec b-rad-08" />
				</view>
				
				<view class="top-list d-flex flex-wrap a-center mt-18">
					<view class="w-p50 d-flex a-center mb-20" v-for="(item, index) in 5" :key="index">
						<view class="w-300 h-40 bg-ececec b-rad-08" />
					</view>
					<view class="w-108 h-36 b-rad-20 bg-e2ebfa mb-20" />
				</view>
			</view>
			
			<!-- 帖子列表 -->
			<view class="bg-ffffff b-rad-20 mt-20 ml-24 mr-24 pt-28 pr-22 pb-28 pl-22">
				<view class="d-flex">
					<view class="w-88 h-88 bg-ececec b-rad-p50" />
					<view class="ml-10">
						<view class="w-200 h-40 bg-ececec b-rad-08" />
						<view class="w-98 h-34 bg-ececec b-rad-08 mt-06" />
					</view>
				</view>
				
				<view class="w-320 h-40 bg-e2ebfa b-rad-24 mt-24" />
				
				<view class="mt-22">
					<view class="w-p100 h-32 bg-ececec b-rad-06 mt-10" />
					<view class="w-p100 h-32 bg-ececec b-rad-06 mt-10" />
					<view class="w-p100 h-32 bg-ececec b-rad-06 mt-10" />
					<view class="w-300 h-32 bg-ececec b-rad-06 mt-10" />
				</view>
				
				<view class="d-flex flex-wrap j-sb a-center mt-20">
					<view class="w-210 h-210 bg-ececec b-rad-10" v-for="(item, index) in 3" :key="index"/>
				</view>
			</view>
		</view>
		
		<!-- 全局搜索 -->
		<view v-if="type == 7">
			<!-- 历史搜索 + 热门推荐 -->
			<view class="pl-32 pr-32">
				<view class="mt-52">
					<view class="w-132 h-44 bg-ececec b-rad-06 mb-32" />
					<view class="d-flex flex-wrap a-center">
						<view class="w-660 h-44 bg-ececec b-rad-06 mr-20 mb-24" />
						<view class="w-200 h-44 bg-ececec b-rad-06 mr-20 mb-24" />
					</view>
				</view>
				
				<view class="mt-36">
					<view class="w-132 h-44 bg-ececec b-rad-06 mb-32" />
					<view class="d-flex flex-wrap a-center">
						<view class="w-660 h-44 bg-ececec b-rad-06 mr-20 mb-24" />
						<view class="w-200 h-44 bg-ececec b-rad-06 mr-20 mb-24" />
					</view>
				</view>
			</view>
		</view>
		
		<!-- 支付抽奖 -->
		<view v-if="type == 8">
			
			<!--支付成功 -->
			<view class="p-rela z-02 d-flex flex-column j-center a-center mt-20">
				<view class="w-264 h-184 bg-ececec b-rad-04" />
				<view class="w-144 h-50 bg-ececec b-rad-04 mt-20" />
			</view>
			
			<!-- 操作按钮 -->
			<view class="p-rela z-02 mt-58 mr-72 ml-72">
				<view class="d-flex j-sb a-center">
					<view class="w-278 h-64 bg-ececec b-rad-32" />
					<view class="w-278 h-64 bg-ececec b-rad-32" />
				</view>
			</view>
		    
			<!-- 抽奖板块 -->
			<view class="p-rela z-02 bg-ffffff b-rad-20 mr-24 ml-24 mt-60 p-32">
				<view class="d-flex j-center a-center">
					<view class="w-160 h-44 bg-ececec b-rad-04" />
				</view>
				<view class="p-abso top-38 right-32 w-48 h-34 bg-ececec b-rad-04" />
				
				<view class="luck-draw p-rela d-flex j-sb a-center flex-wrap mt-10">
					<view class=" w-198 h-172 bg-fde8c7 d-flex flex-column j-center a-center b-rad-10 mt-22" v-for="(item, index) in 9" :key="index" />
				</view>
			</view>
			
			<!-- 猜你喜欢标题 -->
			<view class="d-flex j-center mt-52">
				<view class="w-190 h-50 bg-ffffff b-rad-04" />
			</view>
			
			<!-- 猜你喜欢列表 -->
			<view class="mt-32 ml-24 mr-24">
				<view class="bg-ffffff p-24 b-rad-10">
					<view class="d-flex j-sb" v-for="(item, index) in 3" :key="index">
						<view class="w-288 h-180 bg-ececec b-rad-06" />
						
						<view class="flex-1 d-flex flex-column j-sb ml-20">
							<view class="h-118 bg-ececec b-rad-06" />
							
							<view class="mt-22 d-flex j-sb">
								<view class="w-72 h-44 bg-ececec b-rad-06" />
							    <view class="w-148 h-32 bg-ececec b-rad-06" />
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 酒会详情 -->
		<view v-if="type == 9">
			<!-- banner -->
			<view class="pt-12 pr-40 pb-32 pl-40">
				<view class="d-flex flex-column j-center a-center">
					<view class="w-580 h-18 bg-dedede b-tl-tr-rad-06 op-015" />
					<view class="w-620 h-18 bg-dedede b-tl-tr-rad-06 op-040" />
					<view class="w-670 h-340 bg-f5f5f5 b-rad-10 o-hid">
						<vh-image :loading-type="2" :height="340" />
					</view>
				</view>
				
				<view class="w-670 h-40 bg-f5f5f5 mt-22" />
				<view class="w-200 h-40 bg-f5f5f5 mt-08" />
				
				<view class="d-flex j-sb a-center mt-28">
					<view class="w-100 h-60 bg-f5f5f5" />
					
					<view class="w-126 h-40 bg-f5f5f5" />
				</view>
			</view>
			
			<!-- 酒会详情 -->
			<view class="bg-f5f5f5 pt-20 pl-24 pb-124 pr-24">
				<!-- 活动地址 -->
				<view class="bg-ffffff d-flex j-sb a-center b-rad-10 ptb-30-plr-24">
					<view class="w-486 h-36 bg-f5f5f5" />
					<view class="d-flex a-center">
						<view class="w-02 h-40 bg-f5f5f5" />
						<view class="w-32 h-32 bg-f5f5f5 ml-32" />
					</view>
				</view>
				
				<!-- 活动详情 -->
				<view class="bg-ffffff b-rad-10 mt-20 ptb-40-plr-24">
					<view class="w-144 h-50 bg-f5f5f5" />
					
					<view class="d-flex j-sb pt-32 pb-32">
						<view class="w-128 h-50 bg-f5f5f5" />
						<view class="w-502 h-50 bg-f5f5f5" />
					</view>
					<u-line color="#eee" border-style="dashed" />
					
					<view class="d-flex j-sb pt-32 pb-32">
						<view class="w-128 h-50 bg-f5f5f5" />
						<view class="w-502 h-50 bg-f5f5f5" />
					</view>
					<u-line color="#eee" border-style="dashed" />
					
					<view class="d-flex j-sb pt-32 pb-32">
						<view class="w-128 h-50 bg-f5f5f5" />
						<view class="w-502 h-50 bg-f5f5f5" />
					</view>
					<u-line color="#eee" border-style="dashed" />
					
					<view class="d-flex j-sb pt-32 pb-32">
						<view class="w-128 h-50 bg-f5f5f5" />
						<view class="w-502 h-50 bg-f5f5f5" />
					</view>
					<u-line color="#eee" border-style="dashed" />
				</view>
				
			</view>
			
			<!-- 底部按钮 -->
			<view class="p-fixed bottom-0 z-100 bg-ffffff w-p100 h-104 d-flex j-center a-center b-sh-00021200-022">
				<view class="w-646 h-64 bg-f5f5f5 b-rad-36" />
			</view>
		</view>
		
		<!-- 酒闻详情 -->
		<view v-if="type == 10">
			<!-- 酒闻详情 -->
			<view class="pt-38 pr-40 pb-32 pl-40">
				<view class="w-670 h-48 bg-f5f5f5" />
				<view class="w-670 h-68 bg-f5f5f5 mt-12" />
				<view class="w-228 h-34 bg-f5f5f5 mt-12" />
				<view class="w-486 h-56 bg-eaf0fb b-rad-28 mt-24" />
				<view class="w-670 h-300 bg-f5f5f5 mt-28" />
				<view class="w-670 h-352 bg-f5f5f5 mt-16">
					<vh-image :loading-type='2' :height="352" />
				</view>
				<view class="w-670 h-300 bg-f5f5f5 mt-16" />
			</view>
			
			<!-- 底部按钮 -->
			<view class="p-fixed z-99 bottom-0 w-p100 h-104 bg-ffffff d-flex j-sb a-center b-sh-00021200-022 pl-66 pr-24">
				<view class="d-flex a-center">
					<view class="d-flex flex-column a-center">
						<view class="w-40 h-40 bg-f5f5f5" />
						<view class="w-40 h-22 bg-f5f5f5 mt-10" />
					</view>
					
					<view class="d-flex flex-column a-center ml-70">
						<view class="w-40 h-40 bg-f5f5f5" />
						<view class="w-40 h-22 bg-f5f5f5 mt-10" />
					</view>
				</view>
				
				<view class="w-438 h-64 bg-eeeeee b-rad-32" />
			</view>
		</view>
		
		<!-- 我的订单 -->
		<view v-if="type == 11">
			<!-- 我的订单列表 -->
			<view class="bg-ffffff b-rad-16 mt-20 mr-24 mb-20 ml-24 pl-24 pr-24" v-for="(item, index) in 4" :key="index">
				<!-- 订单号+订单状态 -->
				<view class="d-flex j-sb a-center pt-32 pb-32 bb-s-01-eeeeee">
					<view class="w-366 h-36 bg-f5f5f5" />
					<view class="w-84 h-40 bg-f5f5f5" />
				</view>
				
				<!-- 订单商品信息 -->
				<view class="bb-s-01-eeeeee pt-28 pb-28">
					<view class="d-flex mb-20">
						<vh-image :loading-type="2" :width="246" :height="152" :border-radius="6"/>
						
						<view class="flex-1 d-flex flex-column j-sb ml-12">
							<view class="w-396 h-30 bg-f5f5f5" />
							<view class="w-396 h-30 bg-f5f5f5 mt-08" />
							<view class="d-flex j-sb a-center mt-44">
								<view class="w-124 h-40 bg-f5f5f5" />
								<view class="w-28 h-36 bg-f5f5f5" />
							</view>
						</view>
					</view>
					
					<view class="mt-30 d-flex j-end a-center">
						<view class="w-138 h-26 bg-f5f5f5" />
						<view class="w-72 h-44 bg-f5f5f5 ml-20" />
					</view>
				</view>
				
				<!-- 底部按钮 -->
				<view class="d-flex j-sb a-center pt-28 pb-28">
					<view class="w-174 h-40 bg-f5f5f5" />
					<view class="w-180 h-52 bg-f5f5f5 b-rad-40" />
				</view>
			</view>
		</view>
		
		<!-- 秒发首页 -->
		<view v-if="type == 12">
			<!-- 轮播图 + 金刚区 -->
			<view class="p-rela bg-ffffff pt-20 pl-24 pr-24">
				<!-- 轮播图 -->
				<vh-image :loading-type="2" :width="702" :height="354" :border-radius="6"/>
				
				<!-- 金刚区 -->
				<view class="d-flex j-sb a-center ptb-32-plr-00">
					<view class="d-flex flex-column j-center a-center" v-for="(item, index) in 5" :key="index">
						<vh-image :loading-type="2" :width="86" :height="86" shape="circle"/>
						<view class="w-96 h-34 bg-f5f5f5 mt-10" />
					</view>
				</view>
			</view>
			
			<!-- 卡片（爆款好物更优惠）+ banner -->
			<view class="pt-24 pl-24 pr-24">
				<!-- banner -->
				<view class="d-flex j-center a-center">
					<view class="w-658 h-128 bg-f5f5f5 b-tl-tr-rad-80" />
				</view>
				
				<!-- 卡片 -->
				<view class="bg-ffffff b-sh-00061000-007 ptb-24-plr-32 b-rad-16">
					<!-- 热销前三商品 -->
					<view class="d-flex j-sb a-center bb-s-01-f0f0f0 pb-32">
						<view class="">
							<view class="w-134 h-44 bg-f5f5f5" />
							<view class="w-146 h-34 bg-f5f5f5 mt-10" />
						</view>
						
						<view class="d-flex">
							<view class="p-rela ml-32" v-for="(item, index) in 3" :key="index">
								<vh-image :loading-type="2" :width="128" :height="128" :border-radius="6" />
								<!-- <text class="p-abso right-0 bottom-n-08 bg-li-1 b-tr-10-bl-10 b-s-02-ffffff p-04 font-20 font-wei text-ffffff">NO.1</text> -->
							</view>
						</view>
					</view>
					
					<!-- 卡片 -->
					<view class="mt-22">
						<view class="d-flex j-sb">
							<view class="d-flex j-center a-center">
								<view class="w-146 h-44 bg-f5f5f5" />
								<view class="w-01 h-18 bg-d8d8d8 ml-12 mr-12"></view>
								<view class="w-194 h-36 bg-f5f5f5" />
							</view>
							
							<view class="d-flex a-center">
								<view class="w-48 h-36 bg-f5f5f5" />
								<view class="w-10 h-16 bg-f5f5f5 ml-04" />
							</view>
						</view>
						
						<view class="d-flex o-scr-x mt-32">
							<view class="w-202 d-flex flex-column j-center a-center mr-14" :class="index == 0 ? '' : 'ml-14'"  v-for="(item,index) in 5" :key="index">
								<vh-image :loading-type="2" :width="202" :height="202" :border-radius="6" />
								
								<view class="w-202 h-40 bg-f5f5f5 mt-20" />
								<view class="w-202 h-30 bg-f5f5f5 mt-06" />
								
								<view class="w-202 d-flex j-sb a-center">
									<view class="">
										<view class="w-70 h-30 bg-f5f5f5" />
										<view class="w-60 h-26 bg-f5f5f5" />
									</view>
									
									<view class="w-44 h-44 b-rad-p50 bg-f5f5f5 mt-06" />
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
		</view>
		
		<!-- 购物车 -->
		<view v-if="type == 13">
			<view class="pt-20">
				<view class="bg-ffffff ml-24 mr-24 mb-20 ptb-40-plr-24 b-rad-10" v-for="(outItem, outIndex) in 3" :key="outIndex">
					<view class="d-flex j-sb a-center mb-40">
						<view class="d-flex a-center">
							<view class="w-32 h-32 bg-f5f5f5 b-rad-p50" />
							<view class="w-128 h-44 bg-f5f5f5 ml-24" />
						</view>
					</view>
					
					<view class="list-item d-flex j-sb a-center mt-20" v-for="(item, index) in 2" :key="index">
						<view class="w-32 h-32 bg-f5f5f5 b-rad-p50" />
						<view class="ml-24">
							<vh-image :loading-type="2" :width="230" :height="144" :border-radius="8"/>
						</view>
						<view class="ml-16 flex-1">
							<view class="d-flex a-center">
								<view class="w-48 h-24 bg-f5f5f5 b-rad-04" />
								<view class="w-280 h-34 bg-f5f5f5 b-rad-04 ml-10" />
							</view>
							
							<view class="w-104 h-32 b-rad-06 mt-10" />
								
							
							<view class="mt-28 d-flex j-sb a-center">
								<view class="w-56 h-40 bg-f5f5f5 b-rad-04" />
								<view class="w-144 h-40 bg-f5f5f5 b-rad-04" />
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 是否展示加载框 -->
		<view v-if="showLoading" class="p-fixed top-0 z-600 w-p100 h-p100 d-flex j-center a-center">
			<vh-loading :mode="loadingMode" :loading-color="loadingColor" />
		</view>
		
		<!-- 白光扫过动画 -->
		<view v-if="showTwinkle" class="p-abso z-978 top-0 left-0 w-p100 h-p100 skeleton-twinkle" />
	</view>
</template>

<script>
	/**
	 * skeleton 骨架屏
	 * @property {String Number} // 类型 0 = 默认vinehoo加载、10000 = 只有加载框，其他类型根据页面具体骨架而定
	 * @property {String} bg-color 背景颜色（默认#F5F5F5）
	 * @property {Boolean} has-status-bar 是否有状态栏，默认为 true
	 * @property {Boolean} has-navigation-bar 是否有导航栏（标题栏），默认为 true
	 * @property {Boolean} has-tab-bar 是否有tabbar，默认为 false
	 * @property {Boolean} show-loading 是否显示加载提示，默认为 false
	 * @property {Boolean} show-twinkle 是否显示白光扫过的动画，默认为 false
	 * @property {String} loading-mode 加载图标模式 circle = 圆圈、flower = 花朵
	 * @property {String} loading-color 加载图标颜色（默认#E80404）
	 * @example <vh-skeleton></vh-skeleton>
	 */
	export default {
		name: 'vh-skeleton',
		
		props: {
			// 类型 0 = 默认vinehoo加载、10000 = 只有加载框，其他类型根据页面骨架而定
			type: {
				type: [Number, String], 
				default: 0
			},
			// 背景颜色
			bgColor: {
				type: String,
				default: '#F5F5F5'
			},
			// 是否有状态栏
			hasStatusBar: {
				type: Boolean,
				default: true
			},
			// 是否有导航栏（标题栏）
			hasNavigationBar: {
				type: Boolean,
				default: true
			},
			// 是否有tabbar
			hasTabBar: {
				type: Boolean,
				default: false
			},
			// 是否显示加载提示
			showLoading: {
				type: Boolean,
				default: true
			},
			// 是否显示白光扫过的动画
			showTwinkle: {
				type: Boolean,
				default: true
			},
			// 加载图标模式 circle = 圆圈、flower = 花朵
			loadingMode: { 
				type: String,
				default: 'circle'
			},
			// 加载图标颜色
			loadingColor: {
				type: String,
				default: '#E80404'
			},
		},
		
		computed: {
			// 获取状态栏高度
			statusBarHeight(){
				return this.system.getSysInfo().statusBarHeight
			},
						
			// 获取骨架屏容器高度
			skyletonContainerStyle() {
				let style = {} 
				style.backgroundColor = this.bgColor
				if(this.hasStatusBar && !this.hasNavigationBar && !this.hasTabBar){
					console.log('---------------我只有状态栏')
					style.height = `calc(100vh - ${this.statusBarHeight}px)`
				}
				if( !this.hasStatusBar && !this.hasNavigationBar && this.hasTabBar ){
					console.log('---------------我只tabbar')
					style.height = `calc(100vh - 57px)`
				}
				if(this.hasStatusBar && this.hasNavigationBar && !this.hasTabBar){
					console.log('---------------我只有状态栏和导航栏')
					style.height = `calc(100vh - ${this.statusBarHeight + 48}px)`
				}
				if(this.hasStatusBar && !this.hasNavigationBar && this.hasTabBar){
					console.log('---------------我只有状态栏和tabbar')
					style.height = `calc(100vh - ${this.statusBarHeight + 57}px)`
				}
				if(this.hasStatusBar && this.hasNavigationBar && this.hasTabBar){
					console.log('---------------我有状态栏和导航栏和tabbar')
					style.height = `calc(100vh - ${this.statusBarHeight + 48 + 57}px)`
				}
				return style
			},
		},

	}
</script>

<style scoped></style>
