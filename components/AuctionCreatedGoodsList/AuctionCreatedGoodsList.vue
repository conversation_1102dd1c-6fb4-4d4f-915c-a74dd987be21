<template>
  <view>
    <view v-if="list.length" class="ptb-20-plr-24">
      <view v-for="item in list" :key="item.id" class="mb-20 ptb-00-plr-20 bg-ffffff b-rad-10">
        <view class="p-rela d-flex j-sb pt-70 pb-28">
          <view class="p-abso top-16 flex-sb-c w-p100">
            <view class="font-wei-500 font-28 l-h-40" :class="item.$createdTimeClazz">{{ item.created_time_str }}</view>
            <view class="flex-c-c" @click="onAuditLook(item)">
              <image v-if="item.$isShowStatusTipsIcon" :src="ossIcon('/auction/red_tips_24.png')" class="mr-10 w-24 h-24"></image>
              <view class="font-28 l-h-40" :class="item.$statusClazz">{{ item.status_str }}</view>
            </view>
          </view>
          <vh-image :loading-type="4" :src="item.product_img" :width="152" :height="152" :border-radius="6" :opacityProp="item.status === 5 ? 0.6 : 1" />
          <view class="flex-1 d-flex flex-column j-sb ml-20">
            <view>
              <view class="h-68 font-wei-500 font-24 l-h-34 text-hidden-2" :class="item.$titleClazz">{{ item.title }}</view>
              <text v-if="item.$timeKey" class="mt-04 ptb-00-plr-08 font-22 l-h-32 b-rad-04" :class="item.$timeClazz">{{ item[item.$timeKey] }}{{ item.$timeSuffix }}</text>
            </view>
            <view class="flex-s-c">
              <text class="font-24 l-h-34" :class="item.$pricePrefixClazz">{{ item.$pricePrefix }}</text>
              <text class="ml-06 font-32 l-h-44" :class="item.$priceClazz"><text class="font-24 l-h-34">¥</text>{{ item[item.$priceKey] }}</text>
            </view>
          </view>
        </view>
        <view v-if="item.$btnList.length" class="flex-sb-c h-110 bt-s-02-eeeeee">
          <view>
            <view v-if="item.status === 9999" class="font-28 text-6 l-h-40">
              预约时间：{{ item.pickup_date }} <text class="text-e80404">{{ item.pickup_range }}</text>
            </view>
          </view>
          <view class="flex-c-c">
            <template v-for="btn in item.$btnList">
              <button v-if="btn === 1" :key="btn" class="vh-btn flex-c-c ml-20 w-148 h-52 font-26 text-6 bg-ffffff b-s-02-666666 b-rad-40" @click="onResubmit(item)">重新提交</button>
              <button v-else-if="btn === 2" :key="btn" class="vh-btn flex-c-c ml-20 w-148 h-52 font-26 text-6 bg-ffffff b-s-02-666666 b-rad-40" @click="onAbandon(item)">放弃上拍</button>
              <button v-else-if="btn === 3" :key="btn" class="vh-btn flex-c-c ml-20 w-148 h-52 font-26 text-ff9127 bg-ffffff b-s-02-ff9127 b-rad-40" @click="onDeliverGoods(item)">立即发货</button>
              <button v-else-if="btn === 4" :key="btn" class="vh-btn flex-c-c ml-20 w-148 h-52 font-26 text-6 bg-ffffff b-s-02-666666 b-rad-40" @click="onDeliverGoods(item)">修改发货</button>
            </template>
          </view>
        </view>
      </view>
    </view>
    <view v-else class="pt-120">
      <AuctionNone title="空空如也" desc="暂无发布拍品，快去发布吧" />
    </view>

    <AuctionCreatedAbandonPopup v-model="abandonPopupVisible" :auctionGoods="auctionGoods" @reload="reload"></AuctionCreatedAbandonPopup>
    <AuctionCreatedAuditLookPopup v-model="auditLookPopupVisible" :auctionGoods="auctionGoods"></AuctionCreatedAuditLookPopup>
    <AuctionCreatedDeliverGoodsPopup ref="auctionCreatedDeliverGoodsPopupRef" v-model="deliverGoodsPopupVisible" :auctionGoods="auctionGoods" @reload="reload"></AuctionCreatedDeliverGoodsPopup>
  </view>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    auctionGoods: null,
    abandonPopupVisible: false,
    auditLookPopupVisible: false,
    deliverGoodsPopupVisible: false
  }),
  methods: {
    onResubmit (item) {
      this.jump.navigateTo(`${this.$routeTable.pHAuctionGoodsCreateNew}?id=${item.id}`)
    },
    onAbandon (item) {
      this.auctionGoods = item
      this.abandonPopupVisible = true
    },
    onAuditLook (item) {
      if (item.status !== 7) return
      this.$u.api.getPersonAuctionGoodsRelease({ id: item.id }).then(res => {
        this.auctionGoods = res.data
        this.auditLookPopupVisible = true
      })
    },
    onDeliverGoods (item) {
      this.$u.api.getPersonAuctionGoodsRelease({ id: item.id }).then(res => {
        this.auctionGoods = { ...res.data, status: item.status }
        this.deliverGoodsPopupVisible = true
      })
    },
    reload () {
      this.$emit('reload')
    }
  },
}
</script>