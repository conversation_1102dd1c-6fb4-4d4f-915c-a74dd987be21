<template>
	<view class="ml-20">
		<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
		:custom-style="{width:'148rpx', height:'52rpx', fontSize:'24rpx', color:'#666', border:'1rpx solid #666'}" 
		@click="click">{{ item.after_sale_status == 2 ? '售后详情' : '申请售后' }}</u-button>
	</view>
</template>

<script>
	export default{
		name: "OrderListBtnAfterSale",
		
		props: {
			// 订单信息
			item : {
				type: Object,
				default: () => ({})
			},
		},
		
		methods: {
			click() {
				this.$emit('click')
			}
		}
	}
</script>

<style>
</style>