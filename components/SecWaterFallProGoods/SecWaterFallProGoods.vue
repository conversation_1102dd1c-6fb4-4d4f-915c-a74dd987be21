<template>
	<view class="p-rela" @click="handleJump(`${routeTable.pgGoodsDetail}?id=${item.id}`)" @longpress="handleLongpress">
		<view
			v-if="item.left_top_label && item.left_top_label.length"
			class="p-abso top-16 left-16 z-04 flex-c-c h-30 ptb-00-plr-10 font-18"
			:class="item.left_top_label[0].$clazz ? item.left_top_label[0].$clazz : 'text-6 bg-eeeeee b-rad-32'"
		>
			<image v-if="item.left_top_label[0].type === MProductLabelType.ColdChain" class="mr-04 w-16 h-16" :src="ossIcon(`/second_hair/s_ship_car.png`)" />
			<text>{{ item.left_top_label[0].title }}</text>
		</view>
		<!-- view class="p-abso top-12 left-12 z-04 bg-eeeeee b-rad-30 ptb-02-plr-10 font-18 text-6">次日达</view> -->
		<view class="flex-c-c pt-16">
			<vh-image :src="item.product_img" :loadingType="1" :width="imgWidth" :height="imgHeight" :isResize="item.$imgIsResize" resizeUsePx />
		</view>
		<view :class="[pdClazz, 'mt-12']">
			<view :class="titleClazz">{{ item.title }}</view>
			<!-- <view class="font-26 text-3 l-h-36 text-hidden-2" @longpress.stop="copy.copyText(item.title)">{{ item.title }}</view> -->
			<!-- <view class="mt-12 font-20 text-9">“精品葡萄酒推荐榜第一名”</view> -->
			<view :class="[isHiddenPrice ? 'd-flex a-baseline' : 'flex-s-e', mtClazz]">
				<view
					v-if="isHiddenPrice"
					:style="[item.$isBigScreen ? { fontSize: '13px', lineHeight: '19px' } : { fontSize: '12px', lineHeight: '17px' }]"
					class="font-24 text-6 l-h-34"
				>价格保密</view>
				<view
					v-else
					:style="[item.$isBigScreen ? { height: '17px', fontSize: '16px', lineHeight: '17px' } : { height: '15px', fontSize: '14px', lineHeight: '15px' }]"
					class="h-30 font-wei-500 text-e80404 font-28 l-h-30"
				><text :style="[item.$isBigScreen ? { fontSize: '10px' } : { fontSize: '9px' }]" class="font-18">¥</text>{{ item.price }}</view>
				<view
					:style="[item.$isBigScreen ? { fontSize: '10px', lineHeight: '14px' } : { fontSize: '9px', lineHeight: '13px' }]"
					class="ml-06 font-18 text-9 l-h-26"
				>已售{{ item.saled_count }}</view>
				<view
					v-if="item.periods_type !== 1"
					:style="[item.$isBigScreen ? { fontSize: '10px', lineHeight: '14px' } : { fontSize: '9px', lineHeight: '13px' }]"
					class="font-18 text-9 l-h-26"
				>/限量{{ item.limit_number }}</view>
				<!-- /限量{{ item.limit_number }}{{ quotaRule.quota_number == '9999' ? '' : `限购${quotaRule.quota_number}` }} -->
			</view>

			<view v-if="productLabelList.length" class="d-flex flex-wrap">
				<view v-for="(plitem, plindex) in productLabelList" :key="plindex" class="mr-16" :class="mtClazz">
					<view v-if="plitem.type === MProductLabelType.RedWhite" class="p-rela flex-c-c h-32">
						<view style="border-radius: 18px;" class="flex-c-c ptb-00-plr-12 h-p100 font-20 text-ffffff bg-e80404">{{ plitem.title }}</view>
						<!-- <view style="width: 200%; height: 200%; border-radius: 36px; border: 1px solid #F8D6DB; transform: scale(0.5);" class="p-abso"></view> -->
					</view>
					<view v-else-if="plitem.type === MProductLabelType.FullDiscount" style="border-radius: 18px;" class="p-rela flex-c-c pl-32 h-32 o-hid t-trans-3d-1">
						<image style="top: 50%; left: 0; transform: translateY(-50%);" class="p-abso w-32 h-p100" :src="ossIcon(`/second_hair/s_red_red_32_30.png`)" />
						<view class="flex-c-c ptb-00-plr-06 h-p100 font-wei-500 font-20 text-e80404">{{ plitem.title }}</view>
						<view style="left: -50%; width: 200%; height: 200%; border-radius: 36px; border: 1px solid #e80404; transform: scale(0.5);" class="p-abso"></view>
					</view>
					<view v-else-if="plitem.type === MProductLabelType.WeekNew" style="border-radius: 18px;" class="flex-c-c h-32 font-20 text-e80404 bg-fde8eb">
						<view class="flex-c-c w-34 h-p100">
							<image class="w-24 h-24" :src="ossIcon(`/second_hair/s_not_red.png`)" />
						</view>
						<view class="pr-10">{{ plitem.title }}</view>
					</view>
				</view>
			</view>
			<view v-if="topLabelList.length">
				<view v-for="(topItem, topIndex) in topLabelList" :key="topIndex" class="p-rela h-36 b-rad-06 o-hid" :class="mtClazz">
					<image class="p-abso w-p100 h-p100" :src="ossIcon(`/second_hair/${TopLabelToIcon[topItem.type]}.png`)" />
					<view class="p-rela z-02 w-p100 h-p100 flex-s-c pl-106 font-20 text-e80404"><text class="p-rela top-02">{{ topItem.title }}</text></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import secondWfitemMixin from '@/common/js/mixins/secondWfitemMixin'
	import { MProductLabelType, MGoodsStatus } from '@/common/js/utils/mapperModel'
	const TopLabelToIcon = {
		[MProductLabelType.Top1]: 's_top1_312_36',
		[MProductLabelType.Top2]: 's_top2_312_36',
		[MProductLabelType.Top3]: 's_top2_312_36',
	}
	export default {
		mixins: [secondWfitemMixin],
		data: () => ({
			MProductLabelType,
			MGoodsStatus,
			TopLabelToIcon
		}),
		computed: {
			imgWidth () {
				return `${uni.upx2px(this.item.$wfitemWidth) - uni.upx2px(16) * 2}px`
			},
			imgHeight () {
				return `${uni.upx2px(this.item.$wfitemWidth) - uni.upx2px(16) * 2}px`
			},
			productLabelList () {
				const list = this.item?.product_label || []
				return list.slice(0, 2)
			},
			topLabelList () {
				const list = this.item?.top_label || []
				return list.slice(0, 1)
			},
			isHiddenPrice () {
				return this.item.is_hidden_price || [MGoodsStatus.Disabled, MGoodsStatus.SaleOut].includes(this.item.onsale_status)
			},
			quotaRule () {
				return JSON.parse(this.item.quota_rule || '{}')
			},
		}
		// props: {
		// 	item: {
		// 		type: Object,
		// 		default: () => ({
		// 			"id": 1,//商品 id
		// 			"title": "珀帝夜丘红葡萄酒2017",//标题
		// 			"periods_type": 1,//商品频道
		// 			"banner_img": "/vine/a.jpg",//图片
		// 			"price": 98,//价格
		// 			"saled_count": 16,//已售数
		// 			"is_hidden_price": 0,//隐藏价格（0：隐藏，1：显示）
		// 			"onsale_status": 0,//上架状态（0：待上架，1：待售中，2：在售中，3：已下架）
		// 			"libels": [
		// 				{
		// 					"type": 1,
		// 					"title": "新人价"
		// 				}
		// 			]
		// 		})
		// 	}
		// }
	}
</script>
