<template>
	<u-popup 
	v-model="value"  
	:maskCloseAble="true" 
	mode="bottom" 
	:popup="false" 
	length="auto" 
	@close="$emit('input', false)" 
	:border-radius="20"
	>
		<view class="p-rela pb-40">
			<view class="p-stic top-0 z-04 bg-ffffff flex-c-c ptb-40-plr-00 font-32 text-3">可用优惠券</view>
			<view class="">
				<scroll-view style="max-height: 530rpx;" scroll-y="true">
					<view class="flex-c-c-c">
						<!-- 商品券 -->
						<view class="p-rela w-638 h-140 mb-20" v-for="(item, index) in list" :key="index">
							<image class="p-abso w-638 h-140" :src="ossIcon(`/goods_detail/s_cou_bg3.png`)" mode="aspectFill" />
							<view class="p-rela z-02 w-638 h-140 flex-c-c">
								<view class="p-rela w-264 h-140 flex-c-c w-s-now">
									<view class="mb-30 font-32 text-f44530">¥</view>
									<view class="ml-08 font-wei text-f44530" :class="Number(item.coupon_face_value).toFixed(0).length >= 4 ? 'font-54' : 'font-72'">{{ item.coupon_face_value }}</view>
									<view class="ml-06 font-24 text-6">{{ item.classify_id == 1 ? `无门槛` : `满减券` }}</view>
								</view>
								<view class="w-374 h-140 flex-s-c">
									<view class="ptb-00-plr-26 font-32 text-3 text-hidden-2">{{ item.explain }}</view>
								</view>
							</view>
						</view>
						
						<!-- 兔头券 -->
						<view v-if="rabbitCoupon.price" class="p-rela w-638 h-140 mb-20">
							<image class="p-abso w-638 h-140" :src="ossIcon(`/goods_detail/s_cou_bg4.png`)" mode="aspectFill" />
							<view class="p-rela z-02 w-638 h-140 flex-c-c">
								<view class="p-rela w-370 h-140 flex-s-c">
									<view class="ml-40 mb-30 font-32 text-f44530">¥</view>
									<view class="ml-08 font-72 font-wei text-f44530">{{ rabbitCoupon.price }}</view>
									<view class="ml-06 font-24 text-6">无门槛</view>
								</view>
								<view class="w-268 h-140 flex-c-c">
									<u-button
									shape="circle" 
									:hair-line="false" 
									:ripple="true" 
									ripple-bg-color="#FFF"
									:custom-style="{width:'220rpx', height:'72rpx', background: '#FFF', fontSize:'40rpx', color:'#FB7068', border:'none'}" 
									@click="$emit('receiveCoupon')">领券</u-button>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</u-popup>
</template>

<script>
	export default {
		props: {
			value: {
				type: Boolean,
				default: false
			},
			list: {
				type: Array,
				default: () => [],
			},
			rabbitCoupon: {
				type: Object,
				default: () => ({})
			}
		},
	}
</script>