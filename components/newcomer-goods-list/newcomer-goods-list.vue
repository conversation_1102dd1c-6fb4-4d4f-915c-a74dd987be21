<template>
  <view v-if="list && list.length" class="ngoods-list">
    <view class="ngoods-list__title">
      <image :src="ossIcon('/newborn_zone/new_zone_line.png')" />
      <text>{{ mapperItem.title }}</text>
      <image :src="ossIcon('/newborn_zone/new_zone_line.png')" />
    </view>
    <view class="ngoods-list__subtitle">{{ mapperItem.subtitle }}</view>
    <view class="ngoods-list__content">
      <view v-for="(item, index) in list" :key="index" class="ngoods-list__item" @click="handleJump(`${routeTable.pgGoodsDetail}?id=${item.period}`)">
        <image :src="item.custom_banner_img" class="ngoods-list__ibanner" />
				
        <view class="ngoods-list__icoupon">
          <image :src="ossIcon('/comm/new_zone_pri_bg.png')" />
          <view>
            <text>{{ mapperItem.couponTips }}</text>
            <text v-if="type === 1">下单立返¥{{ item.custom_newcomer_price }}元</text>
            <text v-else-if="type === 2">限购1份</text>
            <text v-else-if="type === 3">领券低至<text><text>¥</text>{{ item.custom_newcomer_price }}</text></text>
          </view>
        </view>
        <view class="ngoods-list__ititle">
          <vh-channel-title-icon :channel="item.periods_type" />
          <text>{{ item.title }}</text>
        </view>
        <view class="ngoods-list__ibrief">{{ item.brief }}</view>
        <view class="ngoods-list__ifooter">
          <view class="ngoods-list__iprice">
            <text v-if="type === 1" class="ngoods-list__ipcurr">0元购</text>
            <text v-else-if="type === 2" class="ngoods-list__ipcurr"><text>¥</text>{{ item.custom_newcomer_price }}</text>
            <text v-else-if="type === 3" class="ngoods-list__ipcurr"><text>¥</text>{{ item.price }}</text>
            <text class="ngoods-list__iporigin">{{ type === 3 ? `¥${item.market_price}` : `原价${item.market_price}` }}</text>
          </view>
          <button class="ngoods-list__ibtn">立即抢</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

const mapper = {
  1: { title: '新人0元购', subtitle: '所有商品下单全额立返', couponTips: '新人专享' },
  2: { title: '新人专享特价', subtitle: '爆款低价 超值精选', couponTips: '新人特价' },
  3: { title: '福利专区', subtitle: '新人专享底价 放心购', couponTips: '新人特惠' }
}

export default {
  name: 'newcomer-goods-list',
  props: {
    type: {
      type: Number,
      default: 1
    },
    list: {
      type: Array,
      default: () => []
    },
    from: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    title: '新人0元购',
    subtitle: '所有商品下单全额立返'
  }),
  computed: {
    ...mapState(['routeTable']),
    mapperItem ({ type }) {
      return mapper[type] || mapper[1]
    }
  },
  methods: {
    handleJump (url) {
      this.jump.appAndMiniJump(1, url, this.from )
    }
  }
}
</script>

<style lang="scss" scoped>
  .ngoods-list {
    &__title {
      @include flex-row;
      
      text {
        padding: 0 24rpx;
        @include font(600, 44rpx, #FFF5E6);
        line-height: 60rpx;
      }

      image {
        @include size(118rpx, 10rpx);

        &:first-of-type {
          transform: rotate(180deg);
        }
      }
    }

    &__subtitle {
      margin: 4rpx 0 0 0;
      @include font(400, 24rpx, #fff);
      line-height: 34rpx;
      text-align: center;
    }

    &__content {
      @include flex-row(flex-start);
      flex-wrap: wrap;
      margin: 18rpx 0 0 0;
      padding: 0 28rpx;
    }

    &__item {
      margin-top: 14rpx;
      padding: 12rpx;
      @include size(340rpx, 442rpx);
      background: #fff;
      border-radius: 10rpx;

      &:nth-child(even) {
        margin-left: 14rpx;
      }
    }

    &__ibanner {
      @include size(316rpx, 196rpx);
      border-radius: 10rpx 10rpx 0 0;
    }

    &__icoupon {
      position: relative;
      @include size(316rpx, 46rpx);

      image {
        @include size(100%);
      }

      view {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        @include flex-row;

        text {
          width: 122rpx;
          @include font(500, 24rpx, #fff);
          text-align: center;

          &:last-of-type {
            width: 202rpx;
            font-weight: 400;
            font-size: 18rpx;

            text {
              font-weight: 500;
              font-size: 32rpx;

              text {
                font-size: 18rpx;
              }
            }
          }
        }
      }
    }

    &__ititle {
      margin: 20rpx 0 0 0;
      height: 60rpx;
      @include font(500, 24rpx, #333);
      line-height: 30rpx;
      @include line(2);
    }

    &__ibrief {
      @include font(400, 20rpx, #999);
      line-height: 34rpx;
      @include line;
    }

    &__ifooter {
      margin-top: 10rpx;
      @include flex-row(space-between);
    }

    &__ipcurr {
      @include font(500, 32rpx, #E80404);

      text {
        font-size: 18rpx;
      }
    }

    &__iporigin {
      margin-left: 4rpx;
      @include font(400, 20rpx, #999);
      text-decoration: line-through;
    }

    &__ibtn {
      @include flex-row;
      margin: 0;
      padding: 0;
      @include size(92rpx, 32rpx);
      @include font(400, 20rpx, #fff);
      background: #E80404;
      border-radius: 16rpx;
    }
  }
</style>
