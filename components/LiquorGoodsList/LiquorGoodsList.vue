<template>
  <view style="height: 100vh; overflow: hidden">
    <scroll-view
      scroll-y
      @scroll="handleGoodsScroll"
      style="height: 100%"
      :scroll-top="0"
      @scrolltolower="handleScrollToLower"
    >
      <view
        v-for="(item, index) in list"
        :key="index"
        :id="'goods_' + index"
        class="mb-20 b-rad-10 o-hid goods-card"
        :class="[isWhiteTheme ? 'bg-ffffff' : 'bg-393228']"
        @click="handleJump(`${routeTable.pgGoodsDetail}?id=${item.id}`, $vhFrom)"
      >
        <view class="p-rela">
          <vh-image :src="item.banner_img" :height="434" />
          <image
            v-if="item.special_activity_data && item.special_activity_data.title_map"
            class="p-abso top-0 wh-p100"
            :src="item.special_activity_data.title_map"
            mode="aspectFill"
          ></image>
        </view>
        <view class="ptb-20-plr-24">
          <view
            style="max-height: 84rpx; overflow: hidden"
            @longpress.stop="handleLongpress(item.title, $vhFrom)"
            @touchend="handleTouched($vhFrom)"
          >
            <!-- <image :src="getIconSrc(item)" class="p-rela top-04 mr-10 w-88 h-30"></image> -->
            <view style="float: left" class="flex-c-c mr-08 h-42">
              <view
                v-if="item.$activityLabelText && item.$activityLableTextBg"
                :style="{ background: item.$activityLableTextBg }"
                style="padding-left: 7px; padding-right: 7px; height: 18px"
                class="flex-c-c font-wei-500 font-24 text-ffffff b-rad-06"
                >{{ item.$activityLabelText }}</view
              >
              <view v-for="(label, index) in item.activity_label" :key="index" class="p-rela flex-c-c ml-10">
                <view style="border-radius: 18px" class="flex-c-c pl-32 h-30 o-hid t-trans-3d-1">
                  <image
                    style="top: 50%; left: 0; transform: translateY(-50%)"
                    class="p-abso w-32 h-p100"
                    :src="ossIcon(`/second_hair/s_red_yellow_32_30.png`)"
                  />
                  <view class="flex-c-c pl-04 pr-10 h-p100 font-wei-500 font-20 text-ff9500">{{ label }}</view>
                </view>
                <!-- <view style="left: -50%; width: 200%; height: 200%; border: 1px solid #ff9500; transform: scale(0.5);" class="p-abso"></view> -->
                <view
                  style="width: 100%; height: 100%; border: 1px solid #ff9500; border-radius: 18px"
                  class="p-abso"
                ></view>
              </view>
            </view>
            <view class="font-wei-500 font-30 l-h-42" :class="[isWhiteTheme ? 'text-3' : 'text-ffffff']">{{
              item.title
            }}</view>
          </view>
          <view class="mt-10 font-28 text-9 l-h-40 text-hidden-2">{{ item.brief }}</view>
          <view class="flex-sb-c mt-20">
            <view
              v-if="item.is_hidden_price === 1 || [3, 4].includes(item.onsale_status)"
              class="font-wei-500 font-32 text-e80404 l-h-44"
              >价格保密</view
            >
            <view v-else class="flex-c-c">
              <view class="font-wei-500 font-36 text-ff9500 l-h-44"
                ><text class="font-24">¥</text>{{ item.price }}</view
              >
              <!-- <view class="ml-16 font-24 text-9 l-h-36"
              >¥<text class="text-dec-l-t">{{ item.market_price }}</text></view
            > -->
            </view>
            <view v-if="!item.is_deposit" class="font-24 l-h-36" :class="[isWhiteTheme ? 'text-9' : 'text-ffffff']">
              <text
                >已售<text class="text-ff9500">{{ item.purchased + item.vest_purchased }}</text></text
              >
              <text
                >/限量<text class="text-ff9500">{{ item.limit_number }}</text></text
              >
              <text v-if="item.quota_rule && item.quota_rule.quota_number != '9999'"
                >/限购<text class="text-ff9500">{{ item.quota_rule.quota_number }}</text></text
              >
            </view>
          </view>
        </view>
      </view>

      <!-- 底部加载状态 -->
      <view class="loading-more">
        <u-loadmore :status="loadStatus" />
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'

export default {
  data() {
    return {
      visibleGoods: [],
      loadStatus: 'loadmore', // 底部加载状态: loadmore-加载更多, loading-加载中, nomore-没有更多了
    }
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    isWhiteTheme: {
      type: Boolean,
      default: false,
    },
    page: {
      type: Number,
      default: 1,
    },
    totalPage: {
      type: Number,
      default: 1,
    },
  },
  mounted() {
    setInterval(() => {
      const existingData = uni.getStorageSync('visibleGoods') || []
      let newData = [...existingData, ...this.visibleGoods]
      uni.setStorageSync('visibleGoods', newData)
      this.visibleGoods = []
    }, 3000)
  },
  mixins: [longpressCopyMixin],
  computed: {
    ...mapState(['routeTable']),
  },
  methods: {
    handleScrollToLower() {
      console.log('触发了 scrolltolower 事件', this.page, this.totalPage)
      if (this.page < this.totalPage && this.loadStatus !== 'loading') {
        console.log('开始加载更多数据')
        this.loadStatus = 'loading'
        this.$emit('loadMore')
      } else {
        console.log('没有更多数据了 或 正在加载中')
        if (this.page >= this.totalPage) {
          this.loadStatus = 'nomore'
        }
      }
    },
    handleGoodsScroll(e) {
      // 处理导航栏背景色
      const scrollTop = e.detail.scrollTop
      if (scrollTop <= 100) {
        this.navBackgroundColor = `rgba(224, 20, 31, ${scrollTop / 100})`
      } else {
        this.navBackgroundColor = `rgba(224, 20, 31, 1)`
        // this.monitorScroll()
      }

      // 原有的曝光检测逻辑
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this)
        this.list.forEach((item, index) => {
          query
            .select(`#goods_${index}`)
            .boundingClientRect((data) => {
              if (data && data.top <= window.innerHeight && data.bottom >= 0) {
                let uid = ''
                let device = ''
                const userinfo = uni.getStorageSync('loginInfo') || '{}'
                if (userinfo && userinfo.uid) {
                  uid = userinfo.uid
                } else {
                  uid = uni.getStorageSync('uniqueId')
                }
                if (this.$vhFrom == 'next') {
                  device = 'hm'
                } else if (this.$vhFrom == '1') {
                  device = 'android'
                } else if (this.$vhFrom == '2') {
                  device = 'ios'
                } else {
                  device = 'h5'
                }
                const goodsItem = {
                  uid: String(uid),
                  created_time: new Date().getTime(),
                  metric_name: 'period_exposure',
                  device,
                  period: Number(item.id),
                  period_type: Number(item.periods_type),
                }

                // 基于ID去重
                const exists = this.visibleGoods.some((v) => v.period === item.id)
                if (!exists) {
                  this.visibleGoods.push(goodsItem)
                }
              }
            })
            .exec()
        })
      })
    },
    getIconSrc(item) {
      return (
        {
          威士忌: this.ossIcon('/flash_purchase/liquor/icon_whisky.png'),
          白兰地: this.ossIcon('/flash_purchase/liquor/icon_brandy.png'),
          朗姆酒: this.ossIcon('/flash_purchase/liquor/icon_rum.png'),
          白酒: this.ossIcon('/flash_purchase/liquor/icon_baijiu.png'),
        }[item?.product_category?.[0]] || this.ossIcon('/flash_purchase/liquor/icon_other.png')
      )
    },
    updateLoadStatus(status) {
      this.loadStatus = status
    },
  },
  watch: {
    list: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.loadStatus = this.page >= this.totalPage ? 'nomore' : 'loadmore'
        }
      },
      immediate: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.goods-card {
  font-family: 'PingFang SC', OpenSans, apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial,
    Roboto, 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}
.loading-more {
  padding: 20rpx 0;
}
</style>
