<template>
  <view :class="showUploadList ? 'vh-upload' : ''" v-if="!disabled">
    <!--上传的图片列表 -->
    <view
      v-if="showUploadList"
      class="vh-preview-wrap"
      :class="uploadConClass"
      v-for="(item, index) in lists"
      :key="index"
      :style="{ width: $u.addUnit(width), height: $u.addUnit(height) }"
    >
      <!-- 申请认证操作按钮 -->
      <view v-if="deletable && plate == 1" class="">
        <view class="vh-cer-delete-icon" @tap.stop="deleteItem(index)">
          <image src="https://images.vinehoo.com/vinehoomini/v3/certification_detail/del_img.png" mode="aspectFill" />
        </view>
        <view
          v-if="item.cerType == 'idCardImg'"
          class="vh-cer-re-upload-icon"
          @tap.stop="selectFile('image', 'idCardImg', index)"
        >
          <image
            src="https://images.vinehoo.com/vinehoomini/v3/certification_detail/re_upload_img.png"
            mode="aspectFill"
          />
        </view>
        <view
          v-if="item.cerType == 'cerQuaImg'"
          class="vh-cer-re-upload-icon"
          @tap.stop="selectFile('image', 'cerQuaImg', index)"
        >
          <image
            src="https://images.vinehoo.com/vinehoomini/v3/certification_detail/re_upload_img.png"
            mode="aspectFill"
          />
        </view>
      </view>

      <u-line-progress
        v-if="showProgress && item.progress > 0 && !item.error"
        :show-percent="false"
        height="6"
        active-color="#E80404"
        class="vh-progress"
        :percent="item.progress"
      />
      <view @tap.stop="retry(index)" v-if="item.error" class="vh-error-btn">点击重试</view>

      <!-- 售后 -->
      <template v-if="plate == 0" class="">
        <!-- 申请售后删除按钮 -->
        <image
          v-if="deletable"
          class="vh-rig-top-after-sale-delete-icon"
          src="https://images.vinehoo.com/vinehoomini/v3/comm/up_del.png"
          mode="aspectFill"
          @tap.stop="deleteItem(index)"
        />
        <!-- 预览处理 -->
        <image
          v-if="item.fileType == 'image'"
          class="vh-sale-preview"
          @tap.stop="doPreviewImage(item.url || item.path, index)"
          :src="item.url || item.path"
          :mode="imageMode"
        />
        <view v-if="item.fileType == 'video'" class="vh-sale-preview-video-con" @tap.stop="doPreviewVideo(item)">
          <image class="vh-sale-preview" :src="item.videoCoverImg" :mode="imageMode" />
          <view class="p-abso z-02 top-0 left-0 d-flex j-center a-center w-p100 h-p100">
            <image
              class="w-50 h-50"
              src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/video_play.png"
            ></image>
          </view>
        </view>
      </template>

      <!-- 认证 -->
      <template v-if="plate == 1" class="">
        <image
          v-if="item.response"
          @tap.stop="doPreviewImage(ossPrefix + item.response, index)"
          class="vh-cer-preview"
          :src="ossPrefix + item.response"
          :mode="imageMode"
        />
        <image
          v-else
          @tap.stop="doPreviewImage(item.url, index)"
          class="vh-cer-preview"
          :src="item.url"
          :mode="imageMode"
        />
      </template>
    </view>

    <slot name="file" :file="lists"></slot>

    <!-- 申请售后上传按钮 -->
    <template v-if="plate == 0">
      <view
        v-if="canUploadImage && afterSaleImageList.length < maxCount"
        style="display: inline-block"
        @tap="selectFile('image')"
      >
        <slot name="addImageBtn"></slot>
        <view
          v-if="!customImageBtn"
          class="vh-sale-list-item-border"
          :class="uploadConClass"
          hover-class="vh-add-wrap__hover"
          hover-stay-time="150"
          :style="{ width: $u.addUnit(width), height: $u.addUnit(height) }"
        >
          <image
            class="vh-sale-add-img"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/cam_red.png"
            mode="aspectFill"
          />
          <view class="vh-sale-add-tips-container">
            <text class="vh-sale-add-tips">上传凭证</text>
            <text class="vh-sale-add-tips">（最多3张）</text>
          </view>
        </view>
      </view>
      <view
        v-if="canUploadVideo && afterSaleVideoList.length == 0"
        style="display: inline-block"
        @tap="selectFile('video')"
      >
        <slot name="addVideoBtn"></slot>
        <view
          v-if="!customVideoBtn"
          class="vh-sale-list-item-border"
          :class="uploadConClass"
          hover-class="vh-add-wrap__hover"
          hover-stay-time="150"
          :style="{ width: $u.addUnit(width), height: $u.addUnit(height) }"
        >
          <image
            class="vh-sale-add-img"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/vie_red.png"
            mode="aspectFill"
          />
          <view class="vh-sale-add-tips-container">
            <text class="vh-sale-add-tips">上传视频</text>
            <text class="vh-sale-add-tips">（最多1个）</text>
          </view>
        </view>
      </view>
    </template>

    <!-- 申请认证上传按钮 -->
    <template v-if="plate == 1">
      <view v-if="canUploadImage && maxCount > lists.length && !hasIdCardImg" @tap="selectFile('image', 'idCardImg')">
        <view
          v-if="!customImageBtn"
          :class="uploadConClass"
          hover-class="vh-add-wrap__hover"
          hover-stay-time="150"
          :style="{ width: $u.addUnit(width), height: $u.addUnit(height) }"
        >
          <image
            class="vh-cer-add-img"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/cam_red.png"
            mode="aspectFill"
          />
          <view class="vh-cer-add-tips-container">
            <text class="vh-cer-add-tips">上传手持身份证照片</text>
          </view>
        </view>
      </view>

      <view v-if="canUploadImage && maxCount > lists.length && !hasCerQuaImg" @tap="selectFile('image', 'cerQuaImg')">
        <view
          v-if="!customImageBtn"
          :class="uploadConClass"
          hover-class="vh-add-wrap__hover"
          hover-stay-time="150"
          :style="{ width: $u.addUnit(width), height: $u.addUnit(height) }"
        >
          <image
            class="vh-cer-add-img"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/cam_red.png"
            mode="aspectFill"
          />
          <view class="vh-cer-add-tips-container">
            <text class="vh-cer-add-tips">上传认证信息资质图片</text>
          </view>
        </view>
      </view>
    </template>

    <!-- 个人中心上传按钮 -->
    <template v-if="plate == 2">
      <view class="mt-74 mb-74 d-flex j-sa a-center pl-68 pr-68">
        <image
          class="w-180 h-180"
          src="https://images.vinehoo.com/vinehoomini/v3/user_info/cam.png"
          mode="aspectFill"
          @click="handleCameraClick"
        />
        <image
          class="w-180 h-180"
          src="https://images.vinehoo.com/vinehoomini/v3/user_info/alb.png"
          mode="aspectFill"
          @click="selectFile('image', null, null, ['album'])"
        />
      </view>
    </template>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
/**
 * upload 上传
 * @description 该组件用于上传场景目前只支持（图片、视频）
 * @property Number plate 上传的板块 0 = 售后、1 = 认证、2 = 个人中心上传头像
 * @property {Boolean} show-upload-list 是否显示组件内部的图片预览（默认true）
 * @property {String} can-upload-image 是否支持上传图片 默认true、
 * @property {String} can-upload-video 是否支持上传视频 默认false
 * @property {String} directory oss文件目录
 * @property {String Number} max-count 最大选择图片的数量（默认99）
 * @property {Boolean} show-progress 是否显示进度条（默认true）
 * @property {Boolean} disabled 是否启用(显示/移仓)组件（默认false）
 * @property {String} image-mode 预览图片等显示模式，可选值为uni的image的mode属性值（默认aspectFill）
 * @property {Object} header 上传携带的头信息，对象形式
 * @property {String} name 上传文件的字段名，供后端获取使用（默认file）
 * @property {Array<String>} size-type original 原图，compressed 压缩图，默认二者都有（默认['original', 'compressed']）
 * @property {Array<String>} source-type 选择图片的来源，album-从相册选图，camera-使用相机，默认二者都有（默认['album', 'camera']）
 * @property {Boolean} preview-full-image	是否可以通过uni.previewImage预览已选择的图片（默认true）
 * @property {Boolean} multiple	是否开启图片多选，部分安卓机型不支持（默认true）
 * @property {Boolean} deletable 是否显示删除图片的按钮（默认true）
 * @property {String Number} max-size 选择单个文件的最大大小，单位B(byte)，默认不限制（默认Number.MAX_VALUE）
 * @property {Array<Object>} file-list 默认显示的图片列表，数组元素为对象，必须提供url属性
 * @property {Boolean} upload-text 选择图片按钮的提示文字（默认"选择图片"）
 * @property {Boolean} auto-upload 选择完图片是否自动上传，见上方说明（默认true）
 * @property {Boolean} show-tips 特殊情况下是否自动提示toast，见上方说明（默认true）
 * @property {Boolean} custom-image-btn 如果需要自定义选择图片的按钮，设置为true（默认false）
 * @property {Boolean} custom-video-btn 如果需要自定义选择视频的按钮，设置为true（默认false）
 * @property {String Number} width 内部预览图片区域和选择图片按钮的区域宽度
 * @property {String Number} height 内部预览图片区域和选择图片按钮的区域高度
 * @property {String} del-bg-color 右上角关闭按钮的背景颜色
 * @property {String} del-icon 右上角删除图标名称，只能为uView内置图标
 * @property {Object} form-data 上传额外携带的参数
 * @property {String | Number} index 在各个回调事件中的最后一个参数返回，用于区别是哪一个组件的事件
 * @event {Function} on-oversize 图片大小超出最大允许大小
 * @event {Function} on-preview 全屏预览图片时触发
 * @event {Function} on-remove 移除图片时触发
 * @event {Function} on-success 图片上传成功时触发
 * @event {Function} on-change 图片上传后，无论成功或者失败都会触发
 * @event {Function} on-error 图片上传失败时触发
 * @event {Function} on-progress 图片上传过程中的进度变化过程触发
 * @event {Function} on-uploaded 所有图片上传完毕触发
 * @event {Function} on-choose-complete 每次选择图片后触发，只是让外部可以得知每次选择后，内部的文件列表
 * @example <vh-upload :directory="directory" :file-list="fileList" />
 */
export default {
  name: 'vh-upload',

  props: {
    // 上传的板块 0 = 售后、1 = 认证、2 = 个人中心上传头像
    plate: {
      type: Number,
      default: 0,
    },
    //是否显示组件自带的图片预览功能
    showUploadList: {
      type: Boolean,
      default: true,
    },
    // 是否支持上传图片
    canUploadImage: {
      type: Boolean,
      default: true,
    },
    // 是否支持上传视频
    canUploadVideo: {
      type: Boolean,
      default: false,
    },
    // oss文件目录
    directory: {
      type: String,
      default: '',
    },
    // 最大上传数量
    maxCount: {
      type: [String, Number],
      default: 52,
    },
    //  是否显示进度条
    showProgress: {
      type: Boolean,
      default: true,
    },
    // 是否启用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 预览上传的图片时的裁剪模式，和image组件mode属性一致
    imageMode: {
      type: String,
      default: 'aspectFill',
    },
    // 头部信息
    header: {
      type: Object,
      default() {
        return {}
      },
    },
    // 上传的文件字段名
    name: {
      type: String,
      default: 'file',
    },
    // 所选的图片的尺寸, 可选值为original compressed
    sizeType: {
      type: Array,
      default() {
        return ['original', 'compressed']
      },
    },
    // 选择图片的来源
    sourceType: {
      type: Array,
      default() {
        return ['album', 'camera']
      },
    },
    // 是否在点击预览图后展示全屏图片预览
    previewFullImage: {
      type: Boolean,
      default: true,
    },
    // 是否开启图片多选，部分安卓机型不支持
    multiple: {
      type: Boolean,
      default: true,
    },
    // 是否展示删除按钮
    deletable: {
      type: Boolean,
      default: true,
    },
    // 文件大小限制，单位为byte
    maxSize: {
      type: [String, Number],
      default: Number.MAX_VALUE,
    },
    // 显示已上传的文件列表
    fileList: {
      type: Array,
      default() {
        return []
      },
    },
    // 上传区域的提示文字
    uploadText: {
      type: String,
      default: '选择图片',
    },
    // 是否自动上传
    autoUpload: {
      type: Boolean,
      default: true,
    },
    // 是否显示toast消息提示
    showTips: {
      type: Boolean,
      default: true,
    },
    // 是否通过slot自定义传入选择图片的按钮
    customImageBtn: {
      type: Boolean,
      default: false,
    },
    // 是否通过slot自定义传入选择视频的按钮
    customVideoBtn: {
      type: Boolean,
      default: false,
    },
    // 内部预览图片区域和选择图片按钮的区域宽度
    width: {
      type: [String, Number],
      default: 190,
    },
    // 内部预览图片区域和选择图片按钮的区域高度
    height: {
      type: [String, Number],
      default: 190,
    },
    // 右上角关闭按钮的叉号图标的颜色
    delColor: {
      type: String,
      default: '#ffffff',
    },
    // 右上角删除图标名称，只能为uView内置图标
    delIcon: {
      type: String,
      default: 'close',
    },
    // 如果上传后的返回值为json字符串，是否自动转json
    toJson: {
      type: Boolean,
      default: true,
    },
    // 上传前的钩子，每个文件上传前都会执行
    beforeUpload: {
      type: Function,
      default: null,
    },
    // 移除文件前的钩子
    beforeRemove: {
      type: Function,
      default: null,
    },
    // 允许上传的图片后缀
    limitType: {
      type: Array,
      default() {
        // 支付宝小程序真机选择图片的后缀为"image"
        // https://opendocs.alipay.com/mini/api/media-image
        return ['png', 'jpg', 'jpeg', 'webp', 'gif', 'image']
      },
    },
    // 在各个回调事件中的最后一个参数返回，用于区别是哪一个组件的事件
    index: {
      type: [Number, String],
      default: '',
    },
  },

  data() {
    return {
      // 售后相关
      afterSaleImageList: [], //申请售后图片列表
      afterSaleVideoList: [], //申请售后视频列表

      // 认证相关
      hasIdCardImg: true, //是否含有身份证图片（用户认证）
      hasCerQuaImg: true, //是否含有认证资质图片（用户认证）

      // 上传信息
      lists: [], //上传的图片列表
      uploadInfo: {}, //oss上传的信息 key、policy、
      uploading: false, //是否正在上传中
    }
  },

  computed: {
    //Vuex 辅助state函数
    ...mapState(['ossPrefix']),

    // 上传图片容器样式
    uploadConClass() {
      let classArr = [
        'vh-sale-list-item', //申请售后
        'vh-cer-list-item', //申请认证
      ]
      return classArr[this.plate]
    },
  },

  watch: {
    fileList: {
      immediate: true,
      handler(val) {
        this.lists = val
        // val.map(value => {
        // 	// 首先检查内部是否已经添加过这张图片，因为外部绑定了一个对象给fileList的话(对象引用)，进行修改外部fileList
        // 	// 时，会触发watch，导致重新把原来的图片再次添加到this.lists
        // 	// 数组的some方法意思是，只要数组元素有任意一个元素条件符合，就返回true，而另一个数组的every方法的意思是数组所有元素都符合条件才返回true
        // 	let tmp = this.lists.some(val => {
        // 		return val.url == value.url;
        // 	})
        // 	// 如果内部没有这个图片(tmp为false)，则添加到内部
        // 	!tmp && this.lists.push({ url: value.url, error: false, progress: 100 });
        // });
      },
    },
    // 监听lists的变化，发出事件
    // lists(n) {
    // 	this.$emit('on-list-change', n, this.index);
    // 	this.hasChoosedCerImg(n)
    // }

    // 深度监听lists的变化，发出事件
    lists: {
      handler(n) {
        console.log('--------------------------这是深度监听数据变化')
        console.log(n)
        this.$emit('on-list-change', n, this.index)
        this.hasChoosedCerImg(n)
        this.getAfterSaleUploadInfo(n)
      },
    },
  },

  created() {
    console.log('----------------------------我是子组件的created')
    this.hasChoosedCerImg(this.lists)
  },

  methods: {
    // 获取上传信息
    async getOssUploadInfo() {
      uni.showLoading({ title: '获取上传信息中...', mask: true })
      let res = await this.$u.api.ossUpload({ dir: this.directory })
      this.uploadInfo = res.data
    },

    // 清除列表
    clear() {
      this.lists = []
    },

    // 重新上传队列中上传失败的所有文件
    reUpload() {
      this.uploadFile()
    },

    // 选择图片 fileType = 文件类型（image = 图片、video = 视频）
    // cerType = 申请认证类型（idCardImg = 身份证图片、cerImg = 认证图片（目前只有认证在用））、fileListIndex = 文件列表索引（目前只有认证在用））
    // headerSourceType = 个人中心上传头像来源
    selectFile(fileType, cerType = null, fileListIndex = null, headerSourceType = null) {
      console.log('------------------我是上传来源')
      console.log(headerSourceType)
      if (this.disabled) return
      let {
        plate,
        name = '',
        maxCount,
        multiple,
        maxSize,
        sizeType,
        lists,
        camera,
        compressed,
        maxDuration,
        sourceType,
      } = this
      console.log(plate)
      let chooseFile = null
      let newMaxCount = maxCount - lists.length
      // 设置为只选择图片的时候使用 chooseImage 来实现
      chooseFile = new Promise((resolve, reject) => {
        if (fileType == 'image') {
          //上传图片
          uni.chooseImage({
            count: multiple ? (newMaxCount > 9 ? 9 : newMaxCount) : 1,
            sourceType: plate == 2 ? headerSourceType : sourceType,
            sizeType,
            success: resolve,
            fail: reject,
          })
        } else if (fileType == 'video') {
          //上传视频
          uni.chooseVideo({
            sourceType: sourceType,
            success: resolve,
            fail: reject,
          })
        }
        // switch(fileType) {
        // 	case 'image': //上传图片
        // 	uni.chooseImage({
        // 		count: multiple ? (newMaxCount > 9 ? 9 : newMaxCount) : 1,
        // 		sourceType: plate == 2 ? headerSourceType : sourceType,
        // 		sizeType,
        // 		success: resolve,
        // 		fail: reject
        // 	});
        // 	break;
        // 	case 'video': // 上传视频
        // 	uni.chooseVideo({
        // 		sourceType: sourceType,
        // 		success: resolve,
        // 		fail: reject
        // 	});
        // 	break;
        // }
      })
      chooseFile
        .then((res) => {
          let file = null
          let listOldLength = this.lists.length
          if (fileType == 'image') {
            //图片
            res.tempFiles.map((val, index) => {
              // 检查文件后缀是否允许，如果不在this.limitType内，就会返回false
              if (!this.checkFileExt(val)) return

              // 如果是非多选，index大于等于1或者超出最大限制数量时，不处理
              if (!multiple && index >= 1) return

              if (val.size > maxSize) {
                this.$emit('on-oversize', val, this.lists, this.index)
                this.showToast('超出允许的文件大小')
              } else {
                // 售后上传文件判断逻辑（ 判断售后板块 && maxCount < 售后图片列表 ）
                if (this.plate == 0 && maxCount <= this.afterSaleImageList.length) {
                  this.$emit('on-exceed', val, this.lists, this.index)
                  this.showToast('超出最大允许的文件个数')
                  return
                }
                // 其他板块上传文件判断逻辑（ 判断非售后板块 && maxCount < 上传图片长度 ）
                if (this.plate !== 0 && maxCount <= lists.length) {
                  this.$emit('on-exceed', val, this.lists, this.index)
                  this.showToast('超出最大允许的文件个数')
                  return
                }

                if (fileListIndex != null) {
                  //列表替换
                  console.log('------------------进入了列表替换')
                  // lists.splice(fileListIndex, 1)
                  // lists.splice(fileListIndex, 0, {
                  // 	fileType:'image', // 文件类型
                  // 	cerType, // 认证类型，默认为null
                  // 	videoCoverImg: '', //视频封面
                  // 	url: val.path, //图片地址
                  // 	progress: 0, //上传进度
                  // 	error: false, //是否报错
                  // 	file: val ,//文件信息
                  // 	aaa:1, //测试
                  // })

                  let item = {
                    fileType: 'image', // 文件类型
                    cerType, // 认证类型，默认为null
                    videoCoverImg: '', //视频封面
                    url: val.path, //图片地址
                    progress: 0, //上传进度
                    error: false, //是否报错
                    file: val, //文件信息
                  }
                  this.$set(lists, fileListIndex, item)
                  console.log(lists)
                } else {
                  //列表追加
                  console.log('------------------进入了列表追加')
                  lists.push({
                    fileType: 'image', // 文件类型
                    cerType, // 认证类型，默认为null
                    videoCoverImg: '', //视频封面
                    url: val.path, //图片地址
                    progress: 0, //上传进度
                    error: false, //是否报错
                    file: val, //文件信息
                  })
                }
              }
            })
          } else if (fileType == 'video') {
            //视频
            lists.push({
              fileType: 'video', // 文件类型
              cerType, //认证类型，默认为null
              videoCoverImg: res.thumbTempFilePath, //视频封面地址
              url: res.tempFilePath, //视频临时地址
              progress: 0, //进度
              error: false, //是否报错
              file: { path: res.tempFilePath, size: res.size }, //文件信息
            })
          }
          // 每次图片选择完，抛出一个事件，并将当前内部选择的图片数组抛出去
          this.$emit('on-choose-complete', this.lists, this.index)
          if (this.autoUpload) this.uploadFile(listOldLength)
        })
        .catch((error) => {
          this.$emit('on-choose-fail', error)
        })
    },

    // 是否选择了认证图片 list = 上传的图片列表
    hasChoosedCerImg(lists) {
      if (this.plate == 1) {
        if (lists.length) {
          console.log('-----------我是过滤')
          this.hasIdCardImg = lists.some((v) => {
            return v.cerType == 'idCardImg'
          })
          this.hasCerQuaImg = lists.some((v) => {
            return v.cerType == 'cerQuaImg'
          })
        } else {
          this.hasIdCardImg = false
          this.hasCerQuaImg = false
        }
        console.log(this.hasIdCardImg)
        console.log(this.hasCerQuaImg)
      }
    },

    // 获取售后上传信息 list = 上传的列表
    getAfterSaleUploadInfo(lists) {
      if (this.plate == 0) {
        console.log('--------------------------我是申请认证')
        this.afterSaleImageList = lists.filter((v) => {
          return v.fileType == 'image'
        })
        this.afterSaleVideoList = lists.filter((v) => {
          return v.fileType == 'video'
        })

        console.log('申请售后图片列表', this.afterSaleImageList)
        console.log('申请售后视频列表', this.afterSaleVideoList)
      }
    },

    // 提示用户消息
    showToast(message, force = false) {
      if (this.showTips || force) {
        uni.showToast({
          title: message,
          icon: 'none',
        })
      }
    },

    // 该方法供用户通过ref调用，手动上传
    upload() {
      this.uploadFile()
    },

    // 对失败的图片重新上传
    retry(index) {
      this.lists[index].progress = 0
      this.lists[index].error = false
      this.lists[index].response = null
      uni.showLoading({ title: '重新上传' })
      this.uploadFile(index)
    },

    // 上传文件
    async uploadFile(index = 0) {
      // 获取oss上传信息
      await this.getOssUploadInfo()

      uni.showLoading({ title: '上传中...', mask: true })
      if (this.disabled) return
      if (this.uploading) return

      // 全部上传完成
      console.log(this.lists[0].file)
      if (index >= this.lists.length) {
        this.$emit('on-uploaded', this.lists, this.index)
        return
      }

      // 检查是否是已上传或者正在上传中
      if (this.lists[index].progress == 100) {
        if (this.autoUpload == false) this.uploadFile(index + 1)
        return
      }

      // 执行before-upload钩子
      if (this.beforeUpload && typeof this.beforeUpload === 'function') {
        // 执行回调，同时传入索引和文件列表当作参数
        // 在微信，支付宝等环境(H5正常)，会导致父组件定义的customBack()函数体中的this变成子组件的this
        // 通过bind()方法，绑定父组件的this，让this.customBack()的this为父组件的上下文
        // 因为upload组件可能会被嵌套在其他组件内，比如u-form，这时this.$parent其实为u-form的this，
        // 非页面的this，所以这里需要往上历遍，一直寻找到最顶端的$parent，这里用了this.$u.$parent.call(this)
        // 明白意思即可，无需纠结this.$u.$parent.call(this)的细节
        let beforeResponse = this.beforeUpload.bind(this.$u.$parent.call(this))(index, this.lists)
        // 判断是否返回了promise
        if (!!beforeResponse && typeof beforeResponse.then === 'function') {
          await beforeResponse
            .then((res) => {
              // promise返回成功，不进行动作，继续上传
            })
            .catch((err) => {
              // 进入catch回调的话，继续下一张
              return this.uploadFile(index + 1)
            })
        } else if (beforeResponse === false) {
          // 如果返回false，继续下一张图片的上传
          return this.uploadFile(index + 1)
        } else {
          // 此处为返回"true"的情形，这里不写代码，就跳过此处，继续执行当前的上传逻辑
        }
      }

      // 检查上传地址
      if (!this.directory && !this.uploadInfo.host) {
        this.showToast('请配置上传地址', true)
        return
      }
      this.lists[index].error = false
      this.uploading = true

      // 构建formData
      let imgName = '' //图片名称
      let imgSuffix = '' //图片后缀

      if (this.plate == 2) {
        //用户头像板块（使用uid作为该图片的文件名）
        try {
          let loginInfo = uni.getStorageSync('loginInfo')
          console.log(loginInfo)
          imgName = loginInfo.uid //用户id
          imgSuffix = 'png' //图片后缀
        } catch (e) {
          //TODO handle the exception
          this.feedback.toast({ title: '获取用户信息失败' })
        }
      } else {
        //其他板块
        imgName = Math.random().toString(36).substr(2, 4) + '_' + new Date().getTime()
        imgSuffix = this.lists[index].url.substring(this.lists[index].url.lastIndexOf('.') + 1) //文件后缀 .png、.jpg...
      }

      let formData = {
        key: this.uploadInfo.dir + imgName + '.' + imgSuffix, //key
        policy: this.uploadInfo.policy, //policy
        OSSAccessKeyId: this.uploadInfo.accessid, //accessKeyId
        signature: this.uploadInfo.signature, //签名
        success_action_status: 200, //成功后返回的操作码
      }

      console.warn(formData)
      // 创建上传对象
      const task = uni.uploadFile({
        url: this.uploadInfo.host,
        filePath: this.lists[index].url,
        name: this.name,
        formData,
        // header: this.header,
        success: (res) => {
          console.log(res)
          // 判断是否json字符串，将其转为json格式
          let data = this.toJson && this.$u.test.jsonString(res.data) ? JSON.parse(res.data) : res.data
          if (![200, 201, 204].includes(res.statusCode)) {
            this.uploadError(index, data)
          } else {
            console.log('----------------------------------我是获取上传列表信息')
            console.log(this.lists[index])
            // 上传成功(如果是图片需要追加图片路径)
            if (this.lists[index].fileType == 'image') {
              console.warn(this.lists[index])
              uni.getImageInfo({
                src: this.lists[index].url, //临时图片路径
                success: (res) => {
                  console.log('----------------------------我是获取图片信息')
                  console.log(res)
                  let { width, height } = res
                  this.lists[index].response = `/${this.uploadInfo.dir}${imgName}.${imgSuffix}?w=${width}&h=${height}`
                  this.lists[index].progress = 100
                  this.lists[index].error = false
                  this.$emit('on-success', data, index, this.lists, this.index)
                  console.log('-------------------上传图片成功')
                },
                fail: (err) => {
                  console.log(err)
                },
              })
            } else {
              this.lists[index].response = '/' + this.uploadInfo.dir + imgName + '.' + imgSuffix
              this.lists[index].progress = 100
              this.lists[index].error = false
              this.$emit('on-success', data, index, this.lists, this.index)
              console.log(res)
              console.log('-------------------上传非图片成功')
            }
            // this.lists[index].response = '/' + this.uploadInfo.dir + imgName + '.' + imgSuffix;
            // this.lists[index].progress = 100;
            // this.lists[index].error = false;
            // this.$emit('on-success', data, index, this.lists, this.index);
            // console.log(res)
            // console.log('-------------------上传成功')
          }
        },
        fail: (e) => {
          this.uploadError(index, e)
        },
        complete: (res) => {
          uni.hideLoading()
          this.uploading = false
          this.uploadFile(index + 1)
          this.$emit('on-change', res, index, this.lists, this.index)
        },
      })
      task.onProgressUpdate((res) => {
        if (res.progress > 0) {
          this.lists[index].progress = res.progress
          this.$emit('on-progress', res, index, this.lists, this.index)
        }
      })
    },

    // 上传失败
    uploadError(index, err) {
      this.lists[index].progress = 0
      this.lists[index].error = true
      this.lists[index].response = null
      this.$emit('on-error', err, index, this.lists, this.index)
      console.log(index, err)
      this.showToast('上传失败，请重试')
    },

    // 删除一个图片
    deleteItem(index) {
      uni.showModal({
        title: '提示',
        content: '您确定要删除此项吗？',
        success: async (res) => {
          if (res.confirm) {
            // 先检查是否有定义before-remove移除前钩子
            // 执行before-remove钩子
            if (this.beforeRemove && typeof this.beforeRemove === 'function') {
              // 此处钩子执行 原理同before-remove参数，见上方注释
              let beforeResponse = this.beforeRemove.bind(this.$u.$parent.call(this))(index, this.lists)
              // 判断是否返回了promise
              if (!!beforeResponse && typeof beforeResponse.then === 'function') {
                await beforeResponse
                  .then((res) => {
                    // promise返回成功，不进行动作，继续上传
                    this.handlerDeleteItem(index)
                  })
                  .catch((err) => {
                    // 如果进入promise的reject，终止删除操作
                    this.showToast('已终止移除')
                  })
              } else if (beforeResponse === false) {
                // 返回false，终止删除
                this.showToast('已终止移除')
              } else {
                // 如果返回true，执行删除操作
                this.handlerDeleteItem(index)
              }
            } else {
              // 如果不存在before-remove钩子，
              this.handlerDeleteItem(index)
            }
          }
        },
      })
    },

    // 执行移除图片的动作，上方代码只是判断是否可以移除
    handlerDeleteItem(index) {
      // 如果文件正在上传中，终止上传任务，进度在0 < progress < 100则意味着正在上传
      if (this.lists[index].process < 100 && this.lists[index].process > 0) {
        typeof this.lists[index].uploadTask != 'undefined' && this.lists[index].uploadTask.abort()
      }
      this.lists.splice(index, 1)
      this.$forceUpdate()
      this.$emit('on-remove', index, this.lists, this.index)
      this.showToast('移除成功')
    },

    // 用户通过ref手动的形式，移除一张图片
    remove(index) {
      // 判断索引的合法范围
      if (index >= 0 && index < this.lists.length) {
        this.lists.splice(index, 1)
        this.$emit('on-list-change', this.lists, this.index)
      }
    },

    // 预览图片
    doPreviewImage(url, index) {
      if (!this.previewFullImage) return
      const images = this.lists.map((item) => item.url || item.path)
      uni.previewImage({
        urls: images,
        current: url,
        success: () => {
          this.$emit('on-preview', url, this.lists, this.index)
        },
        fail: () => {
          uni.showToast({
            title: '预览图片失败',
            icon: 'none',
          })
        },
      })
    },

    // 预览视频 item = 列表某一项
    doPreviewVideo(item) {
      this.jump.navigateTo(`/packageF/pages/full-screen-video/full-screen-video?videoLink=${item.url}`)
    },

    // 判断文件后缀是否允许
    checkFileExt(file) {
      // 检查是否在允许的后缀中
      let noArrowExt = false
      // 获取后缀名
      let fileExt = ''
      const reg = /.+\./
      // 如果是H5，需要从name中判断
      // #ifdef H5
      fileExt = file.name.replace(reg, '').toLowerCase()
      // #endif
      // 非H5，需要从path中读取后缀
      // #ifndef H5
      fileExt = file.path.replace(reg, '').toLowerCase()
      // #endif
      // 使用数组的some方法，只要符合limitType中的一个，就返回true
      noArrowExt = this.limitType.some((ext) => {
        // 转为小写
        return ext.toLowerCase() === fileExt
      })
      if (!noArrowExt) this.showToast(`不允许选择${fileExt}格式的文件`)
      return noArrowExt
    },

    handleCameraClick() {
      console.log(this.$vhFrom)
      if (this.$vhFrom === 'next') {
        this.$emit('takePhoto')
      } else {
        // this.$emit('takePhoto')
        this.selectFile('image', null, null, ['camera'])
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.vh-upload {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.vh-preview-wrap {
  border: 1px solid rgb(235, 236, 238);
}

.vh-rig-top-after-sale-delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  width: 32rpx;
  height: 32rpx;
  padding-bottom: 24rpx;
  padding-left: 24rpx;
}

.vh-progress {
  position: absolute;
  bottom: 0;
  left: 8rpx;
  right: 8rpx;
  z-index: 9;
  width: auto;
}

.vh-error-btn {
  color: #ffffff;
  background-color: $u-type-error;
  font-size: 20rpx;
  padding: 4px 0;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
  line-height: 1;
}

.vh-add-wrap__hover {
  background-color: rgb(235, 236, 238);
}

// 申请售后
.vh-sale-preview-video-con {
  position: relative;
  width: 100%;
  height: 100%;
}
.vh-sale-preview {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.vh-sale-list-item {
  position: relative;
  width: 190rpx;
  height: 190rpx;
  border-radius: 6rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-top: 12rpx;
  margin-right: 12rpx;
}

.vh-sale-list-item-border {
  border: 0.5px dashed #999999;
}

.vh-sale-add-img {
  width: 76rpx;
  height: 60rpx;
}

.vh-sale-add-tips-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
}

.vh-sale-add-tips {
  font-size: 24rpx;
  color: #999;
  line-height: 34rpx;
}

// 申请认证上传
.vh-cer-delete-icon {
  position: absolute;
  top: 80rpx;
  left: 76rpx;
  z-index: 10;
  width: 50rpx;
  height: 50rpx;
  & image {
    width: 100%;
    height: 100%;
  }
}

.vh-cer-re-upload-icon {
  position: absolute;
  top: 80rpx;
  right: 76rpx;
  z-index: 10;
  width: 50rpx;
  height: 50rpx;
  & image {
    width: 100%;
    height: 100%;
  }
}

.vh-cer-preview {
  width: 308rpx;
  height: 196rpx;
  border-radius: 6rpx;
}

.vh-cer-list-item {
  position: relative;
  width: 328rpx;
  height: 228rpx;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 10rpx;
  margin-left: 30rpx;
}

.vh-cer-add-img {
  width: 72rpx;
  height: 60rpx;
}

.vh-cer-add-tips-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 24rpx;
}

.vh-cer-add-tips {
  font-size: 24rpx;
  color: #666;
  line-height: 34rpx;
}
</style>
