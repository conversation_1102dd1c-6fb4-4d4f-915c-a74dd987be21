<template>
	<view class="p-rela" @click="handleJump(`${routeTable.pCWineCommentDetail}?id=${item.id}`)">
		<view :class="leftTopTagClazz">
			<view :style="leftTopTagStyle">酒评</view>
		</view>
		<vh-image v-if="item.banner_img" :src="item.banner_img" :loadingType="1" :width="item.$wfitemWidth" :height="item.$wfitemWidth" :isResize="item.$imgIsResize" />
		<view :class="item.banner_img ? pdClazz : 'ptb-16-plr-12 pt-48 pb-14'">
			<view :class="titleClazz">{{ item.title }}</view>
			<view class="flex-sb-c" :class="mtClazz">
				<view class="flex-c-c o-hid">
					<view class="flex-shrink">
						<vh-image :src="item.user_img" :loadingType="5" shape="circle" :width="32" :height="32" />
					</view>
					<view class="w-max-120 ml-06 font-20 text-6 text-hidden">{{ item.user_name }}</view>
					<view class="flex-shrink flex-c-c ml-04 w-48 h-22 font-14 text-ffffff b-rad-18 bg-ff9127">
						<text :style="levelStyle">LV.{{ item.user_level }}</text>
					</view>
				</view>
				<!-- <view v-if="item.scoring" class="flex-c-c ml-06">
					<image class="wh-14 ml-02" :src="ossIcon(`/second_hair/s_star_${ (5 - item.scoring) > index ? 'gray' : 'red' }.png`)" v-for="(_, index ) in 5" :key="index"/>
				</view> -->
				<view v-if="item.scoring" class="flex-c-c ml-06">
					<image class="w-18 h-18 ml-02" :src="ossIcon(`/second_hair/s_star_red_18.png`)" v-for="(_, index ) in item.scoring" :key="index"/>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import secondWfitemMixin from '@/common/js/mixins/secondWfitemMixin'
	export default {
		mixins: [secondWfitemMixin],
		data: () => ({
			levelStyle: {
				display: 'flex',
				justifyContent: 'center',
				alignItems: 'center',
				width: '200%',
				height: '200%',
				whiteSpace: 'nowrap',
				fontSize: '28rpx',
				transform: 'scale(0.5)',
			},
		})
		// props: {
		// 	item: {
		// 		type: Object,
		// 		default: () => ({
		// 		    "id": 1,
		// 			"title": "酒评人在举手投足之间的一 次打分，往往就能…",
		// 			"banner_img": "/vine/a.jpg",
		// 			"user_img": "/vine/a.jpg",
		// 			"user_name": "团团种草",
		// 			"user_level": 12,
		// 			"scoring": 3
		// 		})
		// 	}
		// },
	}
</script>