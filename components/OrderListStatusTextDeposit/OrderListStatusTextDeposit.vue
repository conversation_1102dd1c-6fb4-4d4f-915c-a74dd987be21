<template>
	<view class="flex-sb-c ptb-32-plr-00 bb-s-01-eeeeee">
		<text class="font-24 text-3" @click.stop="copy.copyText( item.order_no )">订单号：{{item.order_no}}</text>
		<text v-if="Object.keys(this.getStatus).includes(this.item.status + '')" class="font-28 text-3">{{ getStatus[item.status].text }}</text>
	</view>
</template>

<script>
	export default {
		props: {
			item : {
				type: Object,
				default: () => ({})
			},
		},
		
		computed: {
			getStatus() {
				return {
					1 : { text: '已支付'},
					3 : { text: '已结清'},
					4 : { text: '交易关闭' },
					8 : { text: '已退款' }
				}
			}
		}
	}
</script>