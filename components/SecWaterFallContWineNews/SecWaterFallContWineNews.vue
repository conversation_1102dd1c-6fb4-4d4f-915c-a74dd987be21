<template>
	<view class="p-rela" @click="handleJump(`${routeTable.pDWineSmellDetail}?id=${item.id}`)" @longpress="handleLongpress">
		<view :class="leftTopTagClazz">
			<view :style="leftTopTagStyle">文章</view>
		</view>
		<vh-image :src="item.banner_img" :loadingType="1" :width="item.$wfitemWidth" :height="item.$wfitemWidth" :isResize="item.$imgIsResize" />
		<view :class="pdClazz">
			<view :class="titleClazz">{{ item.title }}</view>
			<view class="flex-sb-c" :class="mtClazz">
				<view class="flex-c-c o-hid">
					<view class="flex-shrink">
						<vh-image :src="item.user_img" :loadingType="5" shape="circle" :width="32" :height="32" />
					</view>
					<view class="w-max-120 ml-06 font-20 text-6 text-hidden">{{ item.user_name }}</view>
				</view>
				<view class="flex-c-c ml-06">
					<image class="w-26 h-18" :src="ossIcon(`/second_hair/s_view_26_18.png`)" />
					<view class="ml-04 font-22 text-6 l-h-32">{{ (item.pageviews || 0) | numToThousands }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import secondWfitemMixin from '@/common/js/mixins/secondWfitemMixin'
	export default {
		mixins: [secondWfitemMixin],
		// props: {
		// 	item: {
		// 		type: Object,
		// 		default: () => ({
		// 		    "id": 1,
		// 			"title": "巴罗洛单一园这样红，离不 开当地气候。",
		// 			"banner_img": "/vine/a.jpg",
		// 			"user_img": "/vine/a.jpg",
		// 			"user_name": "小编-乐乐",
		// 			"pageviews": 12000
		// 		})
		// 	}
		// },
	}
</script>
