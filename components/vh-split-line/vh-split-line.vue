<template>
	<view class="d-flex j-center a-center" :style="[upDownStyle]">
		<template v-if="!showImage">
			<view :style="[lineStyle]"></view>
			<view :style="[textStyle]">{{text}}</view>
			<view :style="[lineStyle]"></view>
		</template>
		<template v-else>
			<image :src="imageSrc" mode="aspectFill" :style="[imageStyle]"></image>
			<view :style="[textStyle]">{{text}}</view>
		</template>
	</view>
</template>

<script>
	/**
	 * splitLine 分割线
	 * @description 此组件一般用于页面分割标识区。
	 * @property {String Number} padding-top 到上一个相邻元素的距离
	 * @property {String Number} padding-bottom 到下一个相邻元素的距离
	 * @property {String Number} margin-left 中间文字跟左边横线的距离
	 * @property {String Number} margin-right 中间文字跟右边横线的距离
	 * @property {String} text 中间文字内容
	 * @property {String Number} font-size 中间文字大小
	 * @property {Boolean} font-bold 中间文字是否加粗
	 * @property {String} text-color 中间文字颜色
	 * @property {Boolean} is-tran 是否缩放线条宽度
	 * @property {String Number} line-width 线条宽度
	 * @property {String Number} line-height 线条长度
	 * @property {String} line-color 线条颜色
	 * @property {Boolean} show-image 是否展示图片（一定要和图片宽高配合使用）
	 * @property {String} image-src 图片路径（一定是在允许插入图片时使用）
	 * @property {String Number} image-width 图片宽度
	 * @property {String Number} image-height 图片高度
	 * @example <vh-split-line :padding-top="20" :paddingBottom="20"></vh-split-line>
	 */
	export default{
		name: "vh-split-line",
		props: {
			// 上边距
			paddingTop: {
				type: [String, Number],
				default: 40
			},
			// 下边距
			paddingBottom: {
				type: [String, Number],
				default: 40
			},
			// 左边距
			marginLeft: {
				type: [String, Number],
				default: 40
			},
			// 右边距
			marginRight: {
				type: [String, Number],
				default: 40
			},
			// 文字内容
			text: {
				type: String, 
				default: '已浏览'
			},
			// 文字大小
			fontSize: {
				type: [String, Number],
				default: 28
			},
			// 文字是否加粗
			fontBold: {
				type: Boolean,
				default: false
			},
			// 文字颜色
			textColor: {
				type: String,
				default: '#666666'
			},
			// 是否缩放线条
			isTran:{
				type:Boolean,
				default:false
			},
			// 线条宽度
			lineWidth: {
				type: [String,Number],
				default: 200
			},
			// 线条长度
			lineHeight: {
				type: [String,Number],
				default: 10
			},
			// 线颜色
			lineColor: {
				type: String,
				default: '#E0E0E0'
			},
			// 是否展示图片
			showImage: {
				type: Boolean,
				default: false
			},
			// 图片地址
			imageSrc: {
				type: String,
				default:'',
			},
			// 图片宽度
			imageWidth: {
				type: [String, Number],
				default: 36
			},
			// 图片高度
			imageHeight: {
				type: [String, Number],
				default: 38
			}
		},
		data(){
			return{
				
			}
		},
		computed:{
			// 分割线样式
			upDownStyle(){
				return{
					paddingTop: this.paddingTop + 'rpx',
					paddingBottom: this.paddingBottom + 'rpx'
				}
			},
			
			// 文字左右两边线的样式
			lineStyle(){
				let style = {}
				style.width = this.lineWidth + 'rpx'
				style.height = this.lineHeight + 'rpx'
				if(this.isTran) style.transform = 'scaleY(0.5)'
				style.backgroundColor = this.lineColor
				return style
			},
			
			// 图片样式
			imageStyle(){
				return{
					width: this.imageWidth + 'rpx',
					height: this.imageHeight + 'rpx'
				}
			},
			
			// 文字样式
			textStyle(){
				let style = {}
				style.marginLeft = this.marginLeft + 'rpx'
				style.marginRight = this.marginRight + 'rpx'
				if(this.fontBold) style.fontWeight = 'bold'
				style.fontSize = this.fontSize + 'rpx'
				style.color = this.textColor
				return style
			}
		}
	}
</script>

<style scoped>
</style>
