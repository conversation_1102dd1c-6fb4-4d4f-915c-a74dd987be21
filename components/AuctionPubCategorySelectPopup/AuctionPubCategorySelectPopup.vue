<template>
  <u-mask :show="value" :zoom="false" :mask-click-able="false" @click="onInput(false)">
    <div class="h-p100 flex-c-c">
      <view class="p-rela w-552 h-584">
        <image :src="ossIcon('/auction/popup_bg_552_584.png')" class="p-abso wh-p100" />
        <view class="p-rela pt-48">
          <view class="font-wei-500 font-32 text-3 text-center l-h-44">请选择拍品类型</view>
          <view class="flex-c-c mt-14">
            <view class="p-rela w-168 h-44" @click="onJump('pHAuctionMyCreateDrafts')">
              <image :src="ossIcon('/auction/border_bg_168_44.png')" class="p-abso wh-p100" />
              <view class="flex-c-c wh-p100">
                <text class="font-22 text-6 l-h-32">草稿箱：</text>
                <text class="font-24 text-e80404 l-h-34">{{ draftCount }}</text>
              </view>
            </view>
          </view>
          <view class="flex-sb-c mt-40 ptb-00-plr-20">
            <image :src="ossIcon('/auction/wine_244_350_1.png')" class="w-244 h-350" @click="onJump('pHAuctionMyBuyingList')"></image>
            <image :src="ossIcon('/auction/not_wine_244_350_1.png')" class="w-244 h-350" @click="onJump('pHAuctionGoodsCreateNew')"></image>
          </view>
        </view>
        <image :src="ossIcon('/auction/close_54.png')" class="p-abso left-p-50 bottom-n-110 t-trans-x-m50 w-54 h-54" @click="onInput(false)"></image>
      </view>
    </div>
	</u-mask>
</template>

<script>
import { mapState } from 'vuex'
import auctionMyCreateDraftsUtil from '@/common/js/utils/auctionMyCreateDrafts'
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
  },
  computed: {
    ...mapState(['routeTable'])
  },
  data: () => ({
    draftCount: 0
  }),
  watch: {
    value (newVal) {
      if (newVal) {
        this.draftCount = auctionMyCreateDraftsUtil.getDraftCount()
      }
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onJump (key) {
      this.onInput(false)
      this.jump.navigateTo(this.$routeTable[key])
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
