<template>
	<view class="p-rela w-702 h-310 b-rad-16 mt-24 mr-24 ml-24 p-16">
		<image class="p-abso top-0 left-0 w-p100 h-p100" :src="ossIcon(`/second_hair/new_peo_bg_702_310.png`)" />
		<view class="p-rela z-02 w-p100 h-p100 d-flex j-sb flex-column">
			<view class="flex-1" @click="$emit('jumpActivity')"></view>
			
			<view class="flex-sb-c">
				<view v-if="info.collect_status === 1" class="p-rela w-160 h-220 b-rad-10 p-16">
				    <image class="p-abso top-0 left-0 w-p100 h-p100" :src="ossIcon(`/second_hair/new_peo_rec.png`)" />
					<view class="p-rela z-02 w-p100 h-p100 flex-e-n-c">
						<view class="flex-c-c-c">
							<view class="mt-04">
								<vh-count-down 
							    	:plateName="'miaofaPeoplePlate'"
									:timestamp="info.remainder" 
									:bg-color="'#FFF6E4'"
									:separatorColonPadding="'0 4rpx 4rpx 4rpx'"
									:separatorSize="20"
									:separatorColor="'#FFF'" 
									:fontSize="16"
									:color="'#333'"
									@end="$emit('countDownEnd')"
								/>
							</view>
						</view>
					</view>
				</view>
				<image v-else class="w-160 h-220" :src="ossIcon(`/second_hair/new_peo_unclaimed.png`)" @click="$emit('jumpActivity')"/>
				
				<view class="w-500 h-220 bg-ffffff d-flex b-rad-10 ml-nth-child1-00 ptb-00-plr-14">
					<view class="flex-sb-c-c w-148 ml-14" v-for="( item, index ) in list" :key="index"
					@click="handleJump(`${$routeTable.pgGoodsDetail}?id=${item.periods}`, $vhFrom, true)">
						<view class="p-16">
							<vh-image :src="item.images[0]" loading-type="2" width="116" height="116" />
						</view>
						
						<view class="flex-c-c-c p-12">
							<view class="font-16 text-ff4444 text-hidden-1">{{ item.goods_name }}</view>
							<view class="font-wei-500 text-3 l-h-22 text-hidden-1">
								<text class="font-18">新人价</text>
								<text class="ml-04 font-24"><text class="font-16">¥</text>{{ item.newcomer_price }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'
	export default {
		mixins: [longpressCopyMixin],
		props: {
			list: {
				type: Array,
				default: () => []
			},
			info: {
				type: Object,
				default: () => ({})
			}
		}
	}
</script>