<template>
	<view class="" @click="handleJump(`${routeTable.pgGoodsDetail}?id=${item.id}`, from)">
		<view class="p-rela">
			<vh-image :src="item.banner_img" :height="214" />
			<image v-if="item.special_activity_data && item.special_activity_data.title_map" class="p-abso top-0 z-99 w-p100 h-214" :src="item.special_activity_data.title_map" mode="aspectFill"></image>
		</view>
	
		<view class="ptb-18-plr-16">
			<view class="" @longpress.stop="handleLongpress(item.title, from)" @touchend="handleTouched(from)">
				<vh-channel-title-icon v-if="item.marketing_attribute" :channel="item.periods_type" :marketing-attribute="item.marketing_attribute" :font-size="18" :font-bold="false" />
				<text class="ml-08 font-26 font-wei text-3 l-h-36">{{item.title}}</text>
			</view>
			
			<view class="font-22 text-9 l-h-34 text-hidden-2">{{item.brief}}</view>
			
			<view class="mt-14 d-flex j-sb a-center">
				<view class="">
					<text v-if="item.is_hidden_price == 1 || [3, 4].includes(item.onsale_status)" class="font-28 font-wei text-e80404 l-h-44">价格保密</text>
					<text v-else class="font-28 font-wei text-e80404 l-h-44"><text class="font-18">¥</text>{{item.price}}</text>
				</view>
				
				<!-- 2022-07-19 秒发列表只显示已售 需求方：杨文科 -->
				<view v-if="!item.is_deposit" class="">
					<text v-if="item.periods_type == 1" class="font-18 text-9 l-h-36">已售<text class="text-e80404">{{item.purchased + item.vest_purchased}}</text></text>
					<text v-else class="font-18 text-9 l-h-36">已售<text class="text-e80404">{{item.purchased + item.vest_purchased}}
					</text>/限量<text class="text-e80404">{{item.limit_number}}</text>{{ item.quota_rule.quota_number == '9999'?'':'/限购'}}
					<text class="text-e80404">{{item.quota_rule.quota_number == '9999' ? '' : item.quota_rule.quota_number}}</text></text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'
	import { mapState } from 'vuex'
	/**
	 * waterfall-goods-list 瀑布流商品列表
	 * @description 该组件一般用于拥有瀑布流的商品列表
	 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
	 * @property {Object} item 商品列表的每一项
	 * @event {Function} click 点击组件时触发
	 * @example <vh-waterfall-goods-list />
	 */
	export default {
		name:'vh-waterfall-goods-list',
		
		props: {
			// 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
			from: {
				type: [String, Number],
				default: ''
			},
			
			// 商品列表每一项
			item: {
				type: Object,
				default() {
					return {}
				}
			},
		},

		mixins: [longpressCopyMixin],
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
	}
	
</script>

<style scoped></style>
