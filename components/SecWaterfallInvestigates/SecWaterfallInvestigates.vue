<template>
  <view class="p-rela h-226">
    <image :src="ossIcon('/second_hair/investigates_bg_344_226.png')" class="p-abso wh-p100"></image>
    <view class="p-rela pt-40">
      <view class="font-wei-600 font-30 text-3 l-h-42 text-center">{{ item.title }}</view>
      <view class="flex-c-c mt-50">
        <button
          class="vh-btn w-132 h-42 font-24 l-h-42 bg-ffffff b-rad-26"
          :class="currType === MInvestigatesFeedbackType.Satisfaction ? 'text-e80404 b-s-02-e80404' : 'text-3 b-s-02-d8d8d8'"
          @click="submit(MInvestigatesFeedbackType.Satisfaction)"
        >不满意</button>
        <button
          class="vh-btn ml-32 w-132 h-42 font-24 l-h-42 bg-ffffff b-rad-26"
          :class="currType === MInvestigatesFeedbackType.Dissatisfaction ? 'text-e80404 b-s-02-e80404' : 'text-3 b-s-02-d8d8d8'"
          @click="submit(MInvestigatesFeedbackType.Dissatisfaction)"
        >满意</button>
      </view>
    </view>
  </view>
</template>
<script>
  import secondWfitemMixin from '@/common/js/mixins/secondWfitemMixin'
  import { MInvestigatesFeedbackType } from '@/common/js/utils/mapperModel'

	export default {
		mixins: [secondWfitemMixin],
    data: () => ({
      MInvestigatesFeedbackType,
      currType: '',
    }),
    methods: {
      async submit (type) {
        const isLogin = await this.login.isLoginV3(this.$vhFrom)
				if (!isLogin) return
        this.feedback.loading()
        this.currType = type
        const params = {
          satisfaction_survey_id: this.item.id,
          result: this.currType,
        }
        await this.$u.api.addInvestigatesFeedback(params)
        this.emitRemove()
        this.currType = ''
      }
    },
  }
</script>

<style lang="scss" scoped>
</style>
