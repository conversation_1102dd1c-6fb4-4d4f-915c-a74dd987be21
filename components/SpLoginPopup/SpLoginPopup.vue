<template>
	<u-popup 
	v-model="value"  
	:maskCloseAble="true" 
	mode="bottom" 
	:popup="false" 
	length="auto" 
	@close="close" 
	:border-radius="20"
	>
		<view class="p-rela ptb-40-plr-24">
			<view class="d-flex j-sb a-center">
				<view class="d-flex a-center">
					<image class="w-50 h-50 b-rad-p50" :src="ossIcon(`/comm/mini_logo.png`)" mode="widthFix"></image>
					<view class="ml-20 font-28 font-wei text-3">
						<text>欢迎来到酒云拍卖，一键注册/登录，斩获名庄酒款！</text>
					</view>
				</view>
				
				<view class="p-20" @click.stop="close">
					<u-icon name="close" :size="28" color="#333" />
				</view>
			</view>
			
			<view class="d-flex j-center a-center mt-50">
				<u-button 
				:hair-line="false" 
				:ripple="true" 
				ripple-bg-color="#F8F8F8"
				:custom-style="{width:'702rpx', backgroundColor: '#1AAD19', fontSize:'28rpx', color:'#FFF', border:'none', borderRadius:'10rpx' }"
				@click="jump.navigateTo(routeTable.pgLogin)"
				>登录</u-button>
			</view>
		</view>
	</u-popup>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		name: 'SpLoginPopup',
		
		props: {
			// 通过双向绑定控制组件的弹出与收起
			value: {
				type: Boolean,
				default: false
			},
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
		
		methods: {
			close() {
				this.$emit('input', false);
			}
		}
	}
</script>