<template>
	<view class="d-flex flex-column a-center" :style="[outerEmpConStyle]">
		<image :style="{width: width + 'rpx', height: height + 'rpx'}" :src="imageSrc" mode="aspectFill"></image>
		<view class="mt-60 font-36 font-wei text-3">{{text}}</view>
		<view v-if="subText" class="mt-20 font-28 text-6">{{subText}}</view>
	</view>
</template>

<script>
	/**
	 * empty 缺省页
	 * @description 该组件一般用于无数据展示。
	 * @property {String} bg-color 缺省背景颜色（默认白色）
	 * @property {String Number} padding-top 缺省页容器上方内边距，单位rpx（默认0）
	 * @property {String Number} padding-bottom 缺省页容器下方内边距，单位rpx（默认0）
	 * @property {String Number} width 缺省页容器宽度，单位rpx（默认440）
	 * @property {String Number} height 缺省页容器高度，单位rpx（默认360）
	 * @property {String} image-src 图片路径 (默认'')
	 * @property {String} text 缺省页文字描述（默认''）
	 * @property {String} subText 缺省页辅助文字描述（默认''）
	 * @example <AuctionEmpty width="440" height="360"></AuctionEmpty>
	 */
	export default{
		name:"AuctionEmpty",
		
		props: {
			// 缺省背景颜色
			bgColor: {
				type: String,
				default: '#FFF'
			},
			// 缺省页容器上方内边距
			paddingTop: {
				type: [String, Number],
				default: 0
			},
			// 缺省页容器下方内边距
			paddingBottom: {
				type: [String, Number],
				default: 0
			},
			// 缺省页容器宽度
			width: {
				type: [String, Number],
				default: 440
			},
			// 缺省页容器高度
			height: {
				type: [String, Number],
				default: 400
			},
			// 图片路径
			imageSrc: {
				type: String,
				default: ''
			},
			// 缺省页文字描述
			text: {
				type: String, 
				default: ''
			},
			// 缺省页辅助文字描述
			subText: {
				type: String, 
				default: ''
			},
		},
		computed: {
			// 外层缺省页容器样式
			outerEmpConStyle(){
				return{
					backgroundColor: this.bgColor,
					paddingTop: this.paddingTop + 'rpx',
					paddingBottom: this.paddingBottom + 'rpx'
				}
			},
		}
	}
</script>

<style scoped>
</style>
