<template>
	<view class="" @click="onClick">
		<vh-image :src="item.image" :height="422" />
		<view class="ptb-20-plr-24">
			<view class="font-30 font-wei text-3">{{item.title}}</view>
			<view class="mt-12 font-28 text-9 l-h-40 text-hidden-1">{{item.modul_data.activity_time}}</view>
			<view class="mt-04 font-28 text-9 l-h-40 text-hidden-1">{{item.modul_data.province_name}}{{item.modul_data.city_name}}{{item.modul_data.district_name}}{{item.modul_data.address}}</view>
			<view class="d-flex j-sb a-center mt-24">
				<text class="font-36 font-wei text-e80404"><text class="mr-06 font-24">¥</text>{{item.modul_data.money}}</text>
				<view class="">
					<text class="font-24 text-9 ">剩余名额</text>
					<text class="ml-06 font-24 text-e80404">{{item.modul_data.last_num}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import adBuryDotMixin from '@/common/js/mixins/adBuryDotMixin'
	import { mapState } from 'vuex'
	/**
	 * normal-wine-party-ad 普通酒会广告位
	 * @description 该组件一般用于普通列表的广告位
	 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
	 * @property {Object} item 广告位列表的每一项
	 * @example <vh-normal-wine-party-ad />
	 */
	export default {
		name:'vh-normal-wine-party-ad',
		mixins: [adBuryDotMixin],

		props: {
			// 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
			from: {
				type: [String, Number],
				default: ''
			},
			
			// 商品列表每一项
			item: {
				type: Object,
				default() {
					return {}
				}
			},
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},

		methods: {
			onClick () {
				if (this.buryDotParams) {
					const { channel, region_id } = this.buryDotParams
					const genre = 3
					const button_id = this.item.id
					this.jump.appAndMiniJumpBD(1, `${this.routeTable.pDWinePartyDetail}?id=${this.item.modul_data.id}`, channel, genre, region_id, button_id, this.from)
				} else {
					this.jump.appAndMiniJump(1, `${this.routeTable.pDWinePartyDetail}?id=${this.item.modul_data.id}`, this.from)
				}
			}
		}
	}
</script>

<style>
</style>