<template>
	<view v-if="list.length && list.length >= 2" class="bb-s-01-eeeeee mtb-00-mlr-24 ptb-32-plr-00">
		<view class="d-flex j-sb a-center">
			<view class="font-28 font-wei text-3">开票公司</view>
			<view class="d-flex a-center" @click="$emit('openInvoiceDetail')">
				<view class="font-24 text-3">明细</view>
				<view class="ml-10">
					<u-icon name="arrow-right" :size="24" color="#666" />
				</view>
			</view>
		</view>
		
		<view class="bg-f6f6f6 b-rad-10 mt-20 mt-nth-child1-00 p-24">
			<block v-for="( item, index ) in list" :key="index">
				<view v-if="item.goods_info.length" class="d-flex j-sb mt-10 font-24">
					<view class="text-6">{{ item.invoice_company }}</view>
					<view class="font-wei text-3">¥{{ item.total_money }}</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	/**
	 * invoice-company 开票公司
	 * @description 该组件一般用于开票公司显示（酒云网专用组件）
	 * @property Array list 商品列表（默认[]）
	 * @example <sp-invoice-company />
	 */
	export default {
		name: 'sp-invoice-company',
		
		props: {
			// 列表
			list: {
				type: Array,
				default: function() {
					return [];
				}
			},
		},
	}
</script>

<style>
</style>