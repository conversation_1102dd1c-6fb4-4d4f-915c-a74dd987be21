<template>
	<u-popup
	v-model="value"  
	:maskCloseAble="false" 
	mode="center" 
	:popup="false" 
	length="auto" 
	@close="close" 
	:border-radius="10"
	>
	<view class="w-546 h-222 bg-ffffff">
		<view class="w-p100 h-138 flex-c-c bb-s-01-eeeeee">
			<view class="font-28 font-wei text-3">打开消息通知，及时获取最新消息！</view>
		</view>
		<view class="w-p100 h-82 d-flex">
			<view class="w-p50 h-p100 flex-c-c font-28 text-9" @click="giveUp">放弃</view>
			<view class="w-p50 h-p100 flex-c-c bl-s-01-eeeeee font-28 text-e80404" @click="open">去开启</view>
		</view>
	</view>
	</u-popup>
</template>

<script>
	export default {
		props: {
			value: {
				type: Boolean,
				default: false
			}
		},
		
		methods: {
			// 关闭
			close() {
				this.$emit('input', false);
			},
			// 放弃
			giveUp() {
				console.log('---放弃')
				this.close()
			},
			// 去开启
			open() {
				console.log('---去开启')
				this.close()
			}
		}
	}
</script>

<style>
</style>