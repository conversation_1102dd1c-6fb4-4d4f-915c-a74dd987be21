<template>
	<view class="p-rela h-394">
		<image class="p-abso wh-p100" :src="ossIcon(`/second_hair/s_ad_int_bg.png`)" />
		<view class="p-rela z-02 wh-p100 ptb-00-plr-16">
			<view class="pt-22 font-30 font-wei-500 text-3 l-h-40">{{ item.name }}</view>
			<view class="font-22 text-9 l-h-40">{{ item.subhead }}</view>
			<view class="mt-20">
				<view class="p-rela h-52 bg-ffffff flex-c-c b-rad-26 font-26 text-3" :class="[currIndex === index ? 'text-e80404 b-s-02-e80404' : 'b-s-02-f0f0f0', index ? 'mt-20' : '']" v-for="(item, index) in categories" :key="index" @click="onSubmit(index)">
					{{ item.name }}
					<view style="top: 50%; transform: translateY(-50%);" class="p-abso right-0 w-52 h-52 flex-c-c">
						<image :src="ossIcon(`/second_hair/radio${currIndex === index ? '_h' : ''}_32.png`)" class="w-32 h-32"></image>
					</view>
				</view>
			</view>
			<view class="flex-c-c mt-20" @click="onRandom">
				<image class="wh-26" :src="ossIcon(`/second_hair/s_change.png`)" />
				<view class="ml-06 font-20 text-e80404 l-h-40">换一换</view>
			</view>
		</view>
	</view>
</template>

<script>
	import secondWfitemMixin from '@/common/js/mixins/secondWfitemMixin'
	export default {
		mixins: [secondWfitemMixin],
		data: () => ({
			index: 0,
			categories: [],
			currIndex: -1,
		}),
		methods: {
			initCategories () {
				const categories = []
				while (categories.length < 3) {
					categories.push({ ...this.item.labels[this.index], checked: false })
					if (this.index < this.item.labels.length - 1) {
						this.index++
					} else {
						this.index = 0
					}
				}
				this.categories = categories
			},
			onRandom () {
				this.initCategories()
			},
			async onSubmit (index) {
				const isLogin = await this.login.isLoginV3(this.$vhFrom)
				if (!isLogin) return
				this.currIndex = index
				this.feedback.loading()
				const [{ user_portrait_id }] = this.categories
				const user_portrait_labels_id = `${this.categories[index].id}`
				const params = { user_portrait_id, user_portrait_labels_id }
				await this.$u.api.reportUserPortraitLabel(params)
				this.emitRemove()
				this.currIndex = -1
			}
		},
		created () {
			this.initCategories()
		},
	}
</script>