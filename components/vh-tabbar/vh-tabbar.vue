<template>
  <view v-if="show" @touchmove.stop.prevent="() => {}">
    <!-- tabbar背景 -->
    <!-- <image class="p-fixed z-997 left-0 bottom-safe-area w-p100 h-168" src="https://images.vinehoo.com/vinehoomini/v3/comm/tabbar_bg.png" mode="aspectFill"></image> -->

    <!-- tabbar主体 -->

    <view
      class="vh-tabbar__content p-b-safe-area"
      :class="[tabbarClass, { 'miaofa-bg': isMiaofaPage && isNewYear }]"
      :style="{ height: $u.addUnit(height) }"
    >
      <view
        class="vh-tabbar__content__item"
        v-for="(item, index) in displayList"
        :key="index"
        :class="{ 'vh-tabbar__content__circle': midButton && item.midButton }"
        @tap.stop.prevent="clickHandler(index)"
      >
        <view
          :class="[
            midButton && item.midButton ? 'vh-tabbar__content__circle__button' : 'vh-tabbar__content__item__button',
          ]"
        >
          <u-icon
            :size="item.text ? (midButton && item.midButton ? midButtonSize : iconSize) : 84"
            :name="elIconPath(index)"
            img-mode="scaleToFill"
            :color="elColor(index)"
            :custom-prefix="item.customIcon ? 'custom-icon' : 'uicon'"
          />
          <u-badge
            :count="item.count"
            :is-dot="item.isDot"
            v-if="item.count || item.isDot"
            :offset="[-2, getOffsetRight(item.count, item.isDot)]"
          />
        </view>
        <view class="vh-tabbar__content__item__text" :style="{ color: elColor(index) }">{{ item.text }}</view>
      </view>
      <!-- <view v-if="midButton" class="vh-tabbar__content__circle__border" :style="{ left: midButtonLeft }"></view> -->
    </view>

    <!-- 这里加上一个48rpx的高度,是为了增高有凸起按钮时的防塌陷高度(也即按钮凸出来部分的高度) -->
    <!-- :style="{ height: `calc( ${$u.addUnit(height)} + ${midButton ? 48 : 0}rpx )` }" -->
    <view
      class="vh-fixed-placeholder p-b-safe-area"
      :style="{ height: `calc( ${$u.addUnit(height)} + ${midButton ? 48 : 0}rpx )` }"
    ></view>
    <view class="popup-mask" v-if="showPopup" @click="closePopup"></view>
    <view class="popup-content" :class="{ 'popup-show': showPopup }">
      <view class="popup-inner">
        <view class="popup-item" @tap="handleWineReviewClick">
          <image
            class="popup-image"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/post-wine-talk-banner-x2.png"
            mode="aspectFill"
          />
          <!-- <view class="popup-text-wrapper">
              <view class="popup-title-wrapper">
                <text class="popup-title">写酒评</text>
                <image
                  class="title-icon ml-20"
                  src="https://images.vinehoo.com/vinehoomini/v3/comm/post-wine-talk-icon.png"
                  mode="aspectFit"
                />
              </view>
              <text class="popup-desc">品鉴体验，要表达</text>
            </view> -->
        </view>
        <view class="popup-item" @tap="handlePostClick">
          <image
            class="popup-image"
            src="https://images.vinehoo.com/vinehoomini/v3/comm/post-list-banner-x2.png"
            mode="aspectFill"
          />
        </view>
      </view>
      <!-- 修改底部按钮，添加点击事件 -->
      <view class="popup-button" @tap="closePopup">
        <image
          class="button-image"
          src="https://images.vinehoo.com/vinehoomini/v3/comm/com-close.png"
          mode="aspectFit"
        />
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
/**
 * tabbar 底部菜单栏
 * @description 此组件一般用于页面底部菜单栏。
 * @property {Boolean} show 是否显示此组件
 * @property {String Number} value 通过v-model绑定current值
 * @property {String Number} height tabbar的高度，默认50px，单位任意，如果为数值，则为rpx单位
 * @property {String Number} icon-size 非��起图标的大小，单位任意，数值默认rpx
 * @property {String Number} mid-button-size 凸起的图标的大小，单位任意，数值默认rpx
 * @property {String} active-color 激活时的演示，包括字体图标，提示文字等的演示
 * @property {String} inactive-color 未激活时的颜色
 * @property {Boolean} mid-button 是否显示中部的凸起按钮
 * @property {Array} list 底部菜单栏列表
 * @property {Function} beforeSwitch 切换前的回调
 * @property {Boolean} hideTabBar 是否隐藏原生tabbar
 * @example <vh-tabbar />
 */

export default {
  name: 'vh-tabbar',

  props: {
    loading: {
      type: Boolean,
      default: true,
    },
    // 是否显示此组件
    show: {
      type: Boolean,
      default: true,
    },
    // 通过v-model绑定current值
    value: {
      type: [String, Number],
      default: 0,
    },

    // tabbar的高度，默认50px，单位任意，如果为数值，则为rpx单位
    height: {
      type: [String, Number],
      default: 116,
    },
    // height: {
    // 	type: [String, Number],
    // 	default: 60
    // },
    // 非凸起图标的大小，单位任意，数值默认rpx
    iconSize: {
      type: [String, Number],
      default: 52,
    },
    // 凸起的图标的大小，单位任意，数值默认rpx
    midButtonSize: {
      type: [String, Number],
      default: 110,
    },

    // tabbar的高度，默认50px，单位任意，如果为数值，则为rpx单位
    // height: {
    // 	type: [String, Number],
    // 	default: '50px'
    // },
    // // 非凸起图标的大小，单位任意，数值默认rpx
    // iconSize: {
    // 	type: [String, Number],
    // 	default: 40
    // },
    // // 凸起的图标的大小，单位任意，数值默认rpx
    // midButtonSize: {
    // 	type: [String, Number],
    // 	default: 90
    // },

    // 激活时的演示，包括字体图标，提示文字等的演示
    activeColor: {
      type: String,
      default: '#E80404',
    },
    // 未激活时的颜色
    inactiveColor: {
      type: String,
      default: '#333',
    },
    // 是否显示中部的凸起按钮
    midButton: {
      type: Boolean,
      default: true,
    },
    // 底部菜单栏列表
    list: {
      type: Array,
      default() {
        return [
          {
            iconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/index_black.png',
            selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/index_red.png',
            text: '首页',
            customIcon: false,
            pagePath: '/pages/index/index',
          },
          {
            iconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/fla_pur_black.png',
            selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/fla_pur_red.png',
            text: '闪购',
            customIcon: false,
            pagePath: '/pages/flash-purchase/flash-purchase',
          },
          //   {
          //     iconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/community-unclick.png',
          //     selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/community-click.png',
          //     midButton: true,
          //     customIcon: false,
          //     pagePath: '/pages/community/community',
          //   },
          {
            iconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/new_sec_hair_black.png',
            selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/new_sec_hair_red.png',
            text: '现货速发',
            customIcon: false,
            pagePath: '/pages/miaofa/miaofa',
          },
          {
            iconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/mine_black.png',
            selectedIconPath: 'https://images.vinehoo.com/vinehoomini/v3/tab_bar/mine_red.png',
            text: '我的',
            customIcon: false,
            pagePath: '/pages/mine/mine',
          },
        ]
      },
    },

    // 切换前的回调
    beforeSwitch: {
      type: Function,
      default: null,
    },
    // 是否隐藏原生tabbar
    hideTabBar: {
      type: Boolean,
      default: true,
    },

    tabbarClass: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      midButtonLeft: '50%',
      pageUrl: '', // 当前页面URL
      showPopup: false, // 控制弹窗显示
      lastClickTime: 0, //最后一次点击事件
      topRefreshTimer: null,
      tabbarType: 1,
      showPopup: false, // 控制弹窗显示
      isNewYear: false, // 新年主题标志
    }
  },

  created() {
    // 是否隐藏原生tabbar
    if (this.hideTabBar) uni.hideTabBar()
    // 获取引入了u-tabbar页面的路由地址，该地址没有路径前面的"/"
    let pages = getCurrentPages()
    // 页面栈中的最后一个即为项为当前页面，route属性为页面路径
    this.pageUrl = pages[pages.length - 1].route
    // 初始化配置
  },

  computed: {
    ...mapState('startupPageOptions', ['indexStartupPageCount', 'miaofaStartupPageCount']),

    isMiaofaPage() {
      return this.pageUrl === 'pages/miaofa/miaofa'
    },

    // 处理实际显示的列表
    displayList() {
      if (!this.list || !Array.isArray(this.list)) return []

      const tempList = JSON.parse(JSON.stringify(this.list))
      if (this.isNewYear && tempList[3]) {
        const config = uni.getStorageSync('tabbarThemeConfig')
        if (config && config.isopen) {
          tempList[3] = {
            ...tempList[3],
            iconPath: config.iconPath,
            selectedIconPath: config.selectedIconPath,
            text: config.text,
          }
        }
      }
      return tempList
    },

    elIconPath() {
      return (index) => {
        let pagePath = this.displayList[index].pagePath
        if (pagePath) {
          if (pagePath == this.pageUrl || pagePath == '/' + this.pageUrl) {
            return this.displayList[index].selectedIconPath
          } else {
            return this.displayList[index].iconPath
          }
        } else {
          return index == this.value ? this.displayList[index].selectedIconPath : this.displayList[index].iconPath
        }
      }
    },

    elColor() {
      return (index) => {
        let pagePath = this.displayList[index].pagePath
        if (pagePath) {
          if (pagePath == this.pageUrl || pagePath == '/' + this.pageUrl) return this.activeColor
          else return this.inactiveColor
        } else {
          return index == this.value ? this.activeColor : this.inactiveColor
        }
      }
    },
  },

  mounted() {
    console.log(this.list)
    this.midButton && this.getMidButtonLeft()
    const localConfig = uni.getStorageSync('tabbarThemeConfig')
    if (localConfig && localConfig.isopen) {
      this.isNewYear = true
    } else {
      setTimeout(() => {
        this.initTabbarConfig()
      }, 500)
    }
  },

  methods: {
    ...mapMutations('startupPageOptions', [
      'UPDATE_PREFIX_TABBAR_PAGE',
      'UPDATE_INDEX_STARTUP_PAGE_COUNT',
      'UPDATE_MIAOFA_STARTUP_PAGE_COUNT',
    ]),

    // 初始化 tabbar 配置
    initTabbarConfig() {
      try {
        const localConfig = uni.getStorageSync('tabbarThemeConfig')
        if (localConfig && localConfig.isopen) {
          this.isNewYear = true
        }
      } catch (error) {
        console.error('初始化 tabbar 配置失败:', error)
      }
    },
    openPopup() {
      console.log('打开弹窗')
      this.showPopup = true
    },

    // 处理发帖点击
    handlePostClick() {
      this.jump.loginNavigateTo(this.$routeTable.pCSendPost)
      this.closePopup() // 跳转后关闭弹窗
    },

    // 处理写酒评点击
    handleWineReviewClick() {
      this.jump.loginNavigateTo(this.$routeTable.pCWineComment)
      this.closePopup() // 跳转后关闭弹窗
    },

    // 关闭弹窗
    closePopup() {
      console.log('关闭弹窗')
      this.showPopup = false
    },
    // 切换tab
    switchTab(index) {
      this.$emit('change', index)
      if (this.displayList[index].pagePath) {
        if (this.displayList[index].pagePath === `/${this.pageUrl}`) {
          // 检查是否是凸起按钮且当前已激活

          if (this.displayList[index].midButton && this.midButton) {
            console.log(this.displayList[index].midButton, this.midButton)
            this.openPopup() // 先打开弹窗
          }
          this.handleDoubleClick()
        } else {
          uni.switchTab({
            url: this.displayList[index].pagePath,
          })
          this.recordTabbarPages(index)
        }
      } else {
        this.$emit('input', index)
      }
    },

    // 记录页面切换
    recordTabbarPages(index) {
      const currPagePath = `/${this.pageUrl}`
      const jumpPagePath = this.displayList[index].pagePath
      const { pgIndex, pgMiaoFa } = this.$routeTable
      if (currPagePath === pgIndex && jumpPagePath === pgMiaoFa) {
        this.UPDATE_MIAOFA_STARTUP_PAGE_COUNT(this.miaofaStartupPageCount + 1)
      } else if (currPagePath === pgMiaoFa && jumpPagePath === pgIndex) {
        this.UPDATE_INDEX_STARTUP_PAGE_COUNT(this.indexStartupPageCount + 1)
      } else {
        this.UPDATE_INDEX_STARTUP_PAGE_COUNT(0)
        this.UPDATE_MIAOFA_STARTUP_PAGE_COUNT(0)
      }
    },

    // 双击事件处理
    handleDoubleClick() {
      if (this.loading) return
      const currentTime = new Date().getTime()
      const lastClickTime = this.lastClickTime
      this.lastClickTime = currentTime
      if (currentTime - lastClickTime < 300) {
        this.topRefreshTimer && clearTimeout(this.topRefreshTimer)
        this.topRefreshTimer = setTimeout(() => {
          this.$emit('topRefresh')
        }, 300)
      }
    },

    // 计算角标的right值
    getOffsetRight(count, isDot) {
      if (isDot) return -20
      return count > 9 ? -40 : -30
    },

    // 获取凸起按钮外层元素的left值
    getMidButtonLeft() {
      let windowWidth = this.$u.sys().windowWidth
      this.midButtonLeft = windowWidth / 2 + 'px'
    },

    // 点击处理
    async clickHandler(index) {
      if (this.beforeSwitch && typeof this.beforeSwitch === 'function') {
        let beforeSwitch = this.beforeSwitch.bind(this.$u.$parent.call(this))(index)
        if (!!beforeSwitch && typeof beforeSwitch.then === 'function') {
          await beforeSwitch
            .then(() => {
              this.switchTab(index)
            })
            .catch(() => {})
        } else if (beforeSwitch === true) {
          this.switchTab(index)
        }
      } else {
        this.switchTab(index)
      }
    },

    // 弹窗相关方法
    openPopup() {
      this.showPopup = true
    },

    closePopup() {
      this.showPopup = false
    },

    handlePostClick() {
      this.jump.loginNavigateTo(this.$routeTable.pCSendPost)
      this.closePopup()
    },

    handleWineReviewClick() {
      this.jump.loginNavigateTo(this.$routeTable.pCWineComment)
      this.closePopup()
    },
  },
}
</script>

<style scoped lang="scss">
.vh-fixed-placeholder {
  /* #ifndef APP-NVUE */
  box-sizing: content-box;
  /* #endif */
}

.vh-tabbar {
  &__content {
    display: flex;
    align-items: center;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 998;
    background-color: #fff;
    box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.22);
    /* #ifndef APP-NVUE */
    box-sizing: content-box;
    /* #endif */

    &.miaofa-bg {
      background: linear-gradient(to bottom, #ffe4ba, #fffaf0);
    }

    &__circle__border {
      border-radius: 100%;
      width: 130rpx;
      height: 130rpx;
      top: -26rpx;
      position: absolute;
      z-index: 4;
      background-color: #ffffff;
      // 由于安卓的无能，导致只有3个tabbar item时，此css计算方式有误差
      // 故使用js计算的形式来定位，此处不注释，是因为js计算有延后，避免出现位置闪动
      left: 50%;
      transform: translateX(-50%);
    }
    &__item {
      flex: 1;
      display: flex;
      justify-content: center;
      height: 100%;
      padding: 12rpx 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      &__button {
        position: absolute;
        top: 14rpx;
        left: 50%;
        transform: translateX(-50%);
      }

      &__text {
        font-size: 24rpx;
        line-height: 40rpx;
        position: absolute;
        bottom: 14rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    &__circle {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      z-index: 10;
      /* #ifndef APP-NVUE */
      height: calc(100% - 1px);
      /* #endif */

      &__button {
        width: 130rpx;
        height: 130rpx;
        border-radius: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        background-color: #ffffff;
        top: -26rpx;
        left: 50%;
        z-index: 6;
        transform: translateX(-50%);
      }
    }
  }
}

// 修改弹窗相关样式
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998; // 提高层级
}

.popup-content {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  height: 600rpx; // 修改高度为600rpx
  z-index: 9999;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
  display: flex;
  flex-direction: column;

  &.popup-show {
    transform: translateY(0);
  }
}

.popup-inner {
  flex: 1;
  padding: 60rpx 52rpx 0 52rpx;
  overflow-y: auto;
}

.popup-button {
  height: 112rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 100rpx; // 给底部一些间距
}

.button-image {
  width: 112rpx;
  height: 112rpx;
}

.popup-item {
  width: 100%;
  height: 136rpx;
  margin-bottom: 24rpx;
  position: relative; // 添加相对定位
  z-index: 1; // 添加基础 z-index
}

.popup-image {
  width: 100%;
  height: 100%;
  position: relative; // 添加相对定位
  z-index: 1; // 添加 z-index，确保在文字层级之下
}

.popup-title-wrapper {
  display: flex;
  align-items: center; // 垂直居中对齐
  gap: 8rpx; // 标题和图标之间的间距
  position: relative; // 添加相对定位
  z-index: 3; // 添加更高的 z-index
}

.title-icon {
  width: 12rpx;
  height: 20rpx;
  position: relative; // 添加相对定位
  z-index: 3; // 与 title-wrapper 相同的 z-index
}

.popup-text-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 30rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 6rpx;
  z-index: 2; // 添加 z-index
}

.popup-title {
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
  text-align: left;
  position: relative; // 添加相对定位
  z-index: 31111; // 与 title-wrapper 相同的 z-index
}

.popup-desc {
  font-size: 20rpx;
  color: #666666;
  text-align: left;
  position: relative; // 添加相对定位
  z-index: 31111; // 与其他文字元素相同的 z-index
}
</style>
