<template>
  <view class="slider" :style="{ width: `${width}rpx` }" @touchmove.stop.prevent="onTouchMove" @click="onClick">
    <view class="slider__top" :style="{ height: `${height}rpx`, backgroundColor: inactiveColor }">
      <view class="slider__tpoints" :style="{ height: `${pointHeight}rpx` }">
        <view
          class="slider__tpoint"
          v-for="(item, index) in rangeList.length"
          :key="index"
          :style="[
            pointStyle,
            {
              left: `${optionWidthPercentArr[index]}%`,
              width: `${pointWidth}rpx`,
              height: `${pointHeight}rpx`,
              display: index <= activeIndex ? 'none' : 'block',
            },
          ]"
        />
      </view>
      <view class="slider__tbar" :style="[barStyle, { backgroundColor: activeColor }]"></view>
    </view>

    <view class="slider__bottom">
      <text
        class="slider__bitem"
        v-for="(item, index) in rangeList"
        :key="index"
        :style="{ left: `${optionWidthPercentArr[index]}%` }"
        >{{ item.cate_name || item }}</text
      >
    </view>
  </view>
</template>

<script>
export default {
  props: {
    value: {
      type: [Number, String],
      default: 0,
    },
    width: {
      type: [Number, String],
      default: 500,
    },
    height: {
      type: [Number, String],
      default: 26,
    },
    inactiveColor: {
      type: String,
      default: '#EEE',
    },
    activeColor: {
      type: String,
      default: '#E80404',
    },
    rangeList: {
      type: Array,
      default: () => [],
    },
    pointWidth: {
      type: Number,
      default: 26,
    },
    pointHeight: {
      type: Number,
      default: 26,
    },
    pointStyle: {
      type: Object,
      default: () => ({
        background: '#FFFFFF',
        borderRadius: '50%',
        border: '2rpx solid #CCC',
      }),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  data: () => ({
    sliderBoxLeft: 0,
    sliderBoxWidth: 0,
    optionWidthArr: [],
    optionWidthPercentArr: [],
    activeIndex: -1,
    barStyle: { width: 0 },
  }),

  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal !== undefined && newVal !== null) {
          // 移除百分号并转为数字

          const val = parseInt(newVal.toString().replace('%', ''))
          console.log(val)
          // 修改 barStyle 的计算逻辑
          this.barStyle = {
            width:
              val === 0 || val === 30
                ? `${this.height}rpx` // 当值为0时,宽度设为高度值
                : val === 100
                ? '100%'
                : `${(this.width * val) / 100 + this.pointWidth / 2}rpx`,
            transition: 'width 0.2s',
          }

          // 计算activeIndex - 当值为0时特殊处理
          const index =
            val === 0
              ? 0 // 值为0时,index也为0
              : Math.round((val / 100) * (this.rangeList.length - 1))
          this.activeIndex = index
        }
      },
    },
  },

  mounted() {
    this.$uGetRect('.slider').then(({ left, width }) => {
      this.sliderBoxLeft = left
      this.sliderBoxWidth = width
      const rangeListLen = this.rangeList.length
      const basicWidth = width / ((rangeListLen - 1) * 2)
      this.optionWidthArr = Array(rangeListLen)
        .fill()
        .map((item, index) => {
          if (!index) return [0, basicWidth]
          if (index + 1 === rangeListLen)
            return [((rangeListLen - 1) * 2 - 1) * basicWidth, (rangeListLen - 1) * 2 * basicWidth]
          return [(index * 2 - 1) * basicWidth, (index * 2 + 1) * basicWidth]
        })
      const basicWidthPercent = 100 / (rangeListLen - 1)
      this.optionWidthPercentArr = Array(rangeListLen)
        .fill()
        .map((item, index) => index * basicWidthPercent)

      // 初始化时如果有值，设置barStyle
      console.log(this.value)
      if (this.value) {
        const val = parseInt(this.value.toString().replace('%', ''))
        console.log(val)
        this.barStyle = {
          width:
            val === 0 || val === 30
              ? `${this.height}rpx`
              : val === 100
              ? '100%'
              : `${(this.width * val) / 100 + this.pointWidth / 2}rpx`,
          transition: 'width 0.2s',
        }
        // 计算activeIndex
        const index = Math.round((val / 100) * (this.rangeList.length - 1))
        this.activeIndex = index
      }
    })
  },

  methods: {
    onTouchMove(e) {
      if (!e.changedTouches[0]) {
        return
      }
      const movePageX = e.changedTouches[0].pageX
      this.computedBarStyle(movePageX)
    },
    onClick(e) {
      const { x } = e.detail
      this.computedBarStyle(x, true)
    },
    computedBarStyle(x, isTransition = false) {
      if (this.disabled) return
      let distance = x - this.sliderBoxLeft
      if (distance < 0) distance = 0
      if (distance > this.sliderBoxWidth) distance = this.sliderBoxWidth
      const findIndex = this.optionWidthArr.findIndex((item) => distance <= item[1])
      this.activeIndex = findIndex
      const optionWidthPercent = this.optionWidthPercentArr[findIndex]
      this.$emit('input', optionWidthPercent)
      const dotWidth = this.pointWidth / 2
      let barStyle = {}
      if (optionWidthPercent === 0) {
        barStyle = { width: `${this.height}rpx` }
      } else if (optionWidthPercent === 100) {
        barStyle = { width: `${optionWidthPercent}%` }
      } else {
        barStyle = { width: `${(this.width * optionWidthPercent) / 100 + dotWidth}rpx` }
      }
      this.barStyle = {
        ...barStyle,
        transition: isTransition ? 'width 0.2s' : 'none',
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.slider {
  &__top {
    position: relative;
    border-radius: 200rpx;
  }

  &__tpoints {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
  }

  &__tpoint {
    position: absolute;
    transform: translateX(-50%);

    &:first-of-type,
    &:last-of-type {
      transform: translateX(0);
    }

    &:last-of-type {
      right: 0;
      left: auto !important;
    }
  }

  &__tbar {
    position: absolute;
    top: 0;
    height: 100%;
    border-radius: 200rpx;
    transition: width 0.2s;
  }

  &__bottom {
    position: relative;
    margin-top: 10rpx;
    height: 28rpx;
  }

  &__bitem {
    font-size: 20rpx;
    color: #666;
    line-height: 28rpx;
    position: absolute;
    transform: translateX(-50%);

    &:first-of-type,
    &:last-of-type {
      transform: translateX(0);
    }

    &:last-of-type {
      right: 0;
      left: auto !important;
    }
  }
}
</style>
