<template>
  <u-popup :value="value" mode="center" width="552rpx" :height="status ? '390rpx' : '414rpx'" border-radius="20" :mask-close-able="false" @input="onInput">
    <view v-show="status === 0" class="p-rela wh-p100">
      <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso wh-p100" />
      <view class="p-rela pt-108">
        <view class="font-wei-500 font-32 text-6 l-h-48 text-center">您确定要放弃上拍吗？</view>
        <view class="flex-sb-c mt-110 ptb-00-plr-76">
          <button class="vh-btn flex-c-c w-160 h-60 font-28 text-6 bg-transp b-s-02-666666 b-rad-32" @click="onInput(false)">我再想想</button>
          <button class="vh-btn flex-c-c w-160 h-60 font-28 text-6 bg-transp b-s-02-666666 b-rad-32" @click="onConfirm">确认</button>
        </view>
      </view>
    </view>
    <view v-show="status === 1" class="p-rela wh-p100">
      <image :src="ossIcon('/auction/popup_bg_552_390.png')" class="p-abso wh-p100" />
      <view class="p-rela pt-84">
        <view class="font-wei-500 font-32 text-6 l-h-48 text-center">
          <view>您已放弃上拍，委托保证金</view>
          <view>将在72小时内退回！</view>
        </view>
        <view class="mt-84 h-02 bg-dedede"></view>
        <view class="mt-32 font-wei-500 font-28 text-e80404 text-center" @click="onKnow">知道了</view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    auctionGoods: {
      type: Object,
      default: () => ({})
    }
  },
  data: () => ({
    status: 0
  }),
  watch: {
    value (newVal) {
      if (newVal) this.status = 0
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onConfirm () {
      this.feedback.loading()
      this.$u.api.abandonPersonAuction({ id: this.auctionGoods.id }).then(() => {
        this.status = 1
      })
    },
    onKnow () {
      this.$emit('reload')
      this.onInput(false)
    }
  },
}
</script>