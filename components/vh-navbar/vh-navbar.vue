<template>
  <view class="">
    <view class="vh-navbar" :style="[navbarStyle]" :class="[{ 'vh-navbar-fixed': isFixed }, navbarClass]">
      <view
        class="vh-status-bar"
        :style="{ height: (appStatusBarHeight || customStatusBarHeight || statusBarHeight) + 'px' }"
      ></view>
      <view class="vh-navbar-inner" :style="[navbarInnerStyle]" :class="{ 'new-year-nav': newYearTheme }">
        <view class="vh-back-wrap" v-if="isBack" @tap="goBack">
          <u-icon v-if="vhFrom" name="nav-back" :color="backIconColor" :size="backIconSize"></u-icon>
          <template v-else>
            <u-icon
              :name="pageLength <= 1 ? 'home' : backIconName"
              :color="backIconColor"
              :size="backIconSize"
            ></u-icon>
            <view class="vh-back-text" v-if="backText" :style="[backTextStyle]">{{ backText }}</view>
          </template>
        </view>
        <view class="vh-navbar-content-title" v-if="title" :style="[titleStyle]">
          <view
            class="vh-title"
            :style="{
              color: titleColor,
              fontSize: titleSize + 'rpx',
              fontWeight: titleBold ? 'bold' : 'normal',
            }"
          >
            {{ title }}
          </view>
        </view>
        <view class="vh-slot-content">
          <slot></slot>
        </view>
        <view class="vh-slot-right">
          <slot name="right"></slot>
        </view>
      </view>
    </view>
    <!-- 解决fixed定位后导航栏塌陷的问题 -->
    <view
      class="vh-navbar-placeholder"
      v-if="isFixed && !immersive"
      :style="{
        width: '100%',
        height: Number(navbarHeight) + (appStatusBarHeight || customStatusBarHeight || statusBarHeight) + 'px',
      }"
    ></view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

// 获取系统状态栏的高度
let systemInfo = uni.getSystemInfoSync()
let menuButtonInfo = {}
// 如果是小程序，获取右上角胶囊的尺寸信息，避免导航栏右侧内容与胶囊重叠(支付宝小程序非本API，尚未兼容)
// #ifdef MP-WEIXIN || MP-BAIDU || MP-TOUTIAO || MP-QQ
menuButtonInfo = uni.getMenuButtonBoundingClientRect()
// #endif
/**
 * navbar 自定义导航栏
 * @description 此组件一般用于在特殊情况下，需要自定义导航栏的时候用到，一般建议使用uniapp自带的导航栏。
 * @property {Boolean} is-fixed 导航栏是否固定在顶部（默认true）
 * @property {String Number} z-index 固定在顶部时的z-index值（默认980）
 * @property {String Number} height 导航栏高度(不包括状态栏高度在内，内部自动加上)，注意这里的单位是px（默认44）
 * @property {Object} background 导航栏背景设置（默认{ background: '#ffffff' }）
 * @property {Boolean} is-back 是否显示导航栏左边返回图标和辅助文字（默认true）
 * @property {Boolean} show-border 导航栏底部是否显示下边框，如定义了较深的背景颜色，可取消此值（默认true）
 * @property {Object} border-style 边框样式（默认{ borderBottom: '0.5px solid #EEEEEE'; }）
 * @property {String} back-icon-name 左边返回图标的名称，只能为uView自带的图标（默认arrow-left）
 * @property {String} back-text 返回图标右边的辅助提示文字
 * @property {String Number} back-icon-size 左边返回图标的大小，单位rpx（默认30）
 * @property {String} back-icon-color 左边返回图标的颜色（默认#606266）
 * @property {Object} back-text-style 返回图标右边的辅助提示文字的样式，对象形式（默认{ color: '#606266' }）
 * @property {String} title 导航栏标题，如设置为空字符，将会隐藏标题占位区域
 * @property {String Number} title-width 导航栏标题的最大宽度，内容超出会以省略号隐藏，单位rpx（默认250）
 * @property {String Number} title-size 导航栏标题字体大小，单位rpx（默认32）
 * @property {Boolean} title-bold 标题字体是否加粗
 * @property {String} title-color 标题的颜色（默认#606266）
 * @property {Boolean} immersive 沉浸式，允许fixed定位后导航栏塌陷，仅fixed定位下生效（默认false）
 * @property {Function} custom-back 自定义返回逻辑方法
 * @example <vh-navbar back-text="返回" title="团团默认标题" />
 */
export default {
  name: 'vh-navbar',

  props: {
    newYearTheme: {
      type: Boolean,
      default: false,
    },
    // 导航栏是否固定在顶部
    isFixed: {
      type: Boolean,
      default: true,
    },
    //固定在顶部时的z-index值（默认980）
    zIndex: {
      type: [String, Number],
      default: '',
    },
    // 导航栏高度，单位px，非rpx
    height: {
      type: [String, Number],
      default: '',
    },
    // 对象形式，因为用户可能定义一个纯色，或者线性渐变的颜色
    background: {
      type: Object,
      default() {
        return {
          background: '#ffffff',
        }
      },
    },
    // 是否显示导航栏左边返回图标和辅助文字
    isBack: {
      type: [Boolean, String],
      default: true,
    },
    // 是否显示导航栏的边框
    showBorder: {
      type: Boolean,
      default: false,
    },
    // 边框样式
    borderStyle: {
      type: Object,
      default() {
        return {
          borderBottom: '0.5px solid #EEEEEE',
        }
      },
    },
    // 左边返回的图标
    backIconName: {
      type: String,
      default: 'nav-back',
    },
    // 返回的文字提示
    backText: {
      type: String,
      default: '',
    },
    // 左边返回图标的大小，rpx
    backIconSize: {
      type: [String, Number],
      default: '44',
    },
    // 返回箭头的颜色
    backIconColor: {
      type: String,
      default: '#333',
    },
    // 返回的文字的 样式
    backTextStyle: {
      type: Object,
      default() {
        return {
          color: '#333',
        }
      },
    },
    // 导航栏标题
    title: {
      type: String,
      default: '',
    },
    // 标题的宽度，如果需要自定义右侧内容，且右侧内容很多时，可能需要减少这个宽度，单位rpx
    titleWidth: {
      type: [String, Number],
      default: '250',
    },
    // 标题的字体大小
    titleSize: {
      type: [String, Number],
      default: 36,
    },
    // 标题字体是否加粗
    titleBold: {
      type: Boolean,
      default: true,
    },
    // 标题的颜色
    titleColor: {
      type: String,
      default: '#333',
    },
    // 是否沉浸式，允许fixed定位后导航栏塌陷，仅fixed定位下生效
    immersive: {
      type: Boolean,
      default: false,
    },
    // 自定义返回逻辑
    customBack: {
      type: Function,
      default: null,
    },
    navbarClass: {
      type: String,
      default: '',
    },
    customStatusBarHeight: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      menuButtonInfo: menuButtonInfo,
      statusBarHeight: systemInfo.statusBarHeight,
      appStatusBarHeight: 0,
    }
  },

  computed: {
    ...mapState(['routeTable']),
    // 页面栈长度
    pageLength() {
      return this.pages.getPageLength()
    },

    // 导航栏内部盒子的样式
    navbarInnerStyle() {
      let style = {}
      // 导航栏宽度，如果在小程序下，导航栏宽度为胶囊的左边到屏幕左边的距离
      style.height = this.navbarHeight + 'px'
      // // 如果是各家小程序，导航栏内部的宽度需要减少右边胶囊的宽度
      // #ifdef MP
      let rightButtonWidth = systemInfo.windowWidth - menuButtonInfo.left
      style.marginRight = rightButtonWidth + 'px'
      // #endif
      return style
    },

    // 整个导航栏的样式
    navbarStyle() {
      let style = {}
      style.zIndex = this.zIndex ? this.zIndex : this.$u.zIndex.navbar
      // 合并用户传递的背景色对象
      Object.assign(style, this.background)

      // 合并边框
      if (this.showBorder) {
        Object.assign(style, this.borderStyle)
      }
      return style
    },

    // 导航中间的标题的样式
    titleStyle() {
      let style = {}
      // #ifndef MP
      style.left = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px'
      style.right = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px'
      // #endif
      // #ifdef MP
      // 此处是为了让标题显示区域即使在小程序有右侧胶囊的情况下也能处于屏幕的中间，是通过绝对定位实现的
      let rightButtonWidth = systemInfo.windowWidth - menuButtonInfo.left
      style.left = (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + 'px'
      style.right =
        rightButtonWidth - (systemInfo.windowWidth - uni.upx2px(this.titleWidth)) / 2 + rightButtonWidth + 'px'
      // #endif
      style.width = uni.upx2px(this.titleWidth) + 'px'
      return style
    },

    // 转换字符数值为真正的数值
    navbarHeight() {
      // #ifdef APP-PLUS || H5
      return this.height ? this.height : 44
      // #endif
      // #ifdef MP
      // 小程序特别处理，让导航栏高度 = 胶囊高度 + 两倍胶囊顶部与状态栏底部的距离之差(相当于同时获得了导航栏底部与胶囊底部的距离)
      // 此方法有缺陷，暂不用(会导致少了几个px)，采用直接固定值的方式
      // return menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;//导航高度
      let height = systemInfo.platform == 'ios' ? 44 : 48
      return this.height ? this.height : height
      // #endif
    },

    vhFrom() {
      return this.$vhFrom
    },
  },

  created() {
    console.log('---------------我是子组件的页面栈长度')
    console.log(this.pages.getPageLength())
    console.log(this.pageLength)
    const pageFullPath = this.pages.getPageFullPath()
    const { pEAddressAdd, pEAddressManagement, pBOrderDepositDetail } = this.routeTable
    if (
      pageFullPath.includes('/packageH') ||
      pageFullPath.includes(pEAddressAdd) ||
      pageFullPath.includes(pEAddressManagement) ||
      pageFullPath.includes(pBOrderDepositDetail)
    ) {
      this.appStatusBarHeight = this.$appStatusBarHeight
    }
  },

  methods: {
    goBack() {
      // 如果自定义了点击返回按钮的函数，则执行，否则执行返回逻辑
      if (typeof this.customBack === 'function') {
        // 在微信，支付宝等环境(H5正常)，会导致父组件定义的customBack()函数体中的this变成子组件的this
        // 通过bind()方法，绑定父组件的this，让this.customBack()的this为父组件的上下文
        this.customBack.bind(this.$u.$parent.call(this))()
      } else {
        if (this.$app && this.pages.getPageLength() === 1) {
          this.$customBack()
          return
        }
        if (this.pageLength <= 1) {
          // if(!this.$app && this.pages.getPageFullPath().includes(this.routeTable.pHAuctionGoodsDetail) ) {
          // 	const hasRecordedFriendCircle = uni.getStorageSync('hasRecordedFriendCircle') || ''
          // 	let url = `${this.routeTable.pHAuctionIndex}${hasRecordedFriendCircle ? '?fromFriendCircle=1': '' }`
          // 	uni.reLaunch({url})
          // }else{
          // 	uni.reLaunch({
          // 		url:'/pages/index/index'
          // 	})
          // }
          uni.reLaunch({
            url: '/pages/index/index',
          })
        } else {
          uni.navigateBack()
        }
      }
    },
  },
}
</script>

<style scoped lang="scss">
.vh-navbar {
  width: 100%;
}

.vh-navbar-fixed {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 991;
}

.vh-status-bar {
  width: 100%;
}

.vh-navbar-inner {
  display: flex;
  justify-content: space-between;
  position: relative;
  align-items: center;
}

.vh-back-wrap {
  display: flex;
  align-items: center;
  flex: 1;
  flex-grow: 0;
  padding: 14rpx 14rpx 14rpx 24rpx;
}

.vh-back-text {
  padding-left: 4rpx;
  font-size: 30rpx;
}

.vh-navbar-content-title {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  position: absolute;
  left: 0;
  right: 0;
  height: 60rpx;
  text-align: center;
  flex-shrink: 0;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  word-break: break-all;
}

// .vh-navbar-centent-slot {
// 	flex: 1;
// }

.vh-title {
  line-height: 60rpx;
  font-size: 32rpx;
  flex: 1;
}

.vh-navbar-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.vh-slot-content {
  flex: 1;
  display: flex;
  align-items: center;
}

.new-year-nav {
  background-image: url('http://vinehoo.oss-cn-zhangjiakou.aliyuncs.com/vinehoomini/v3/theme/newyear/theme-NewYear-nav.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}
</style>
