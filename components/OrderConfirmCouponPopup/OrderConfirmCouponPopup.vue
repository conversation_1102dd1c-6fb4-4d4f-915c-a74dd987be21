<template>
  <u-popup :value="value" mode="bottom" width="100%" border-radius="20" @input="onInput">
    <view class="flex-c-c h-124">
      <view class="p-rela flex-c-c h-p100" @click="type = 0">
        <view class="font-wei-500 font-28 l-h-40" :class="[type === 0 ? 'text-3' : 'text-9']">可用优惠券({{ canUseCouList.length }})</view>
        <view v-if="type === 0" class="p-abso bottom-0 w-66 h-06 bg-333333 b-rad-04"></view>
      </view>
      <view class="p-rela flex-c-c ml-108 h-p100" @click="type = 1">
        <view class="font-wei-500 font-28 l-h-40" :class="[type === 1 ? 'text-3' : 'text-9']">不可用优惠券({{ canNotUseCouList.length }})</view>
        <view v-if="type === 1" class="p-abso bottom-0 w-66 h-06 bg-333333 b-rad-04"></view>
      </view>
      <image :src="ossIcon('/coupon/close_46.png')" class="p-abso right-24 w-46 h-46" @click="onInput(false)"></image>
    </view>
    <scroll-view v-if="list.length" class="h-max-1000 bg-f5f5f5 o-scr-y" scroll-y="true">
      <view class="p-24">
        <view v-for="(item, index) in list" :key="item.id" :class="[index ? 'mt-24' : '']">
          <CouponListItem :item="item" :isUnusable="type === 1" :isSelected="item.id === canUseSelCouId" @click.native="type === 0 && $emit('changeCouId', item)"></CouponListItem>
        </view>
      </view>
    </scroll-view>
    <view v-else class="ptb-24-plr-00">
      <view class="p-rela flex-c-c">
        <image :src="ossIcon('/empty/emp_cou.png')" class="w-440 h-360"></image>
        <view class="p-abso bottom-0 font-28 text-9 l-h-40">暂无优惠券</view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    canUseCouList: {
      type: Array,
      default: () => []
    },
    canNotUseCouList: {
      type: Array,
      default: () => []
    },
    canUseSelCouId: {
      type: Number,
      default: 0
    }
  },
  data: () => ({
    type: 0
  }),
  computed: {
    list ({ type, canUseCouList, canNotUseCouList }) {
      return type ? canNotUseCouList : canUseCouList
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
