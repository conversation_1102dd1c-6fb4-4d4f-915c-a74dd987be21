<template>
	<text class="p-rela z-04 w-88 h-30 flex-c-c bs-bb b-rad-02 mb-02 font-18 text-hidden"
	:class="auctionType === 0 ? 'b-s-01-e80404 text-e80404' : 'b-s-01-2e7bff text-2e7bff'">
	{{ auctionType === 0 ? '商家拍品' : '个人拍品'}}</text>
</template>

<script>
	export default {
		name: 'AuctionOrderTypeName',
		
		props: {
			// 拍品类型 0-商家拍品 1-个人拍品
			auctionType: {
				type: [Number, String],
				default: 0
			},
		}
	}
</script>

<style>
</style>