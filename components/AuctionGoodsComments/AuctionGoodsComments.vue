<template>
  <view>
    <view class="font-wei-500 font-36 text-3">评论</view>
    <view v-if="comments.length" class="mt-32">
      <VhCommentList :list="comments" @enjoy="onEnjoy" @reply="onReply" @toggleReply="onToggleReply" />
    </view>
    <view v-else class="flex-c-c flex-column mt-n-08">
      <image :src="ossIcon('/auction/comments_none_130_110.png')" class="w-130 h-110" />
      <view class="mt-10 font-24 text-9 l-h-34 text-center">快去夺首评!</view>
    </view>

    <u-popup v-model="popupVisible"  mode="bottom" width="100%" border-radius="40">
      <view>
        <view class="pt-24">
          <textarea class="bs-bb mtb-00-mlr-auto p-24 w-702 h-168 bg-f7f7f7 b-rad-10 font-28 text-3 l-h-40" v-model="commentParams.content" :placeholder="placeholder" placeholder-style="color:#999" />
        </view>
        <view class="flex-sb-c ptb-32-plr-24 pt-20">
          <view>
            <view v-if="!isAgreed" class="flex-c-c">
              <vh-check :checked="checked" :width="26" :height="26" @click="checked = !checked" />
              <view class="ml-10 font-24 text-9 l-h-34">
                <text @click="checked = !checked">阅读并接受</text>
                <text class="text-2b8cf7" @click.stop="jump.jumpH5Agreement(`${agreementPrefix}/ContentPubRules`, $vhFrom)">《酒云网内容发布规则》</text>
              </view>
            </view>
          </view>
          <view class="flex-c-c">
            <image :src="ossIcon('/auction/emoji_44.png')" class="w-44 h-44" @click="showEmo = !showEmo"></image>
            <image :src="ossIcon(`/auction/publish${disabled ? '' : '_h'}_44.png`)" class="ml-24 w-44 h-44" @click="onSubmit"></image>
          </view>
        </view>
        <view v-if="commentParams.emoji_image" class="p-rela w-120 h-120 bg-f5f5f5 b-rad-10 ml-24 mb-24">
          <image class="w-120 h-120 mb-46 mr-20" :src="commentParams.emoji_image" />
          <image class="p-abso top-06 right-06 w-32 h-32" :src="ossIcon('/comm/del_gray.png')" @click="commentParams.emoji_image = ''"/>
        </view>

        <view v-show="showEmo">
          <view class="w-p100 h-350 bt-s-01-eeeeee bb-s-01-eeeeee">
            <scroll-view class="h-p100" scroll-y="true">
              <view v-show="emoTypeIndex == 0" class="h-p100 d-flex flex-wrap pt-32 pl-24 pr-24">
                <view class="w-120 d-flex flex-column j-center a-center mb-20 mr-20 b-rad-10 o-hid" v-for="(item, index) in RabbitHeadEmojiList" :key="index" @click="commentParams.emoji_image = item.img">
                  <vh-image loading-type="2" :src="item.img" :width="120" :height="120" />
                  <text class="mt-10 font-24 text-9">{{ item.name }}</text>
                </view>
              </view>
              
              <view v-show="emoTypeIndex == 1" class="h-p100 d-flex flex-wrap pt-32 pl-24 pr-24">
                <view class="w-120 d-flex flex-column j-center a-center mb-20 mr-20 b-rad-10 o-hid" v-for="(item, index) in RabbitEmojiList" :key="index" @click="commentParams.emoji_image = item.img">
                  <vh-image loading-type="2" :src="item.img" :width="120" :height="120" />
                  <text class="mt-10 font-24 text-9">{{ item.name }}</text>
                </view>
              </view>
              
              <view v-show="emoTypeIndex == 2" class="h-p100 d-flex flex-wrap pt-32 pl-24 pr-24">
                <view class="font-40 ml-10 mr-10 mb-20" v-for="(item, index) in CommonEmojiList" :key="index" @click="addEmoji(item)">{{item}}</view>
              </view>
            </scroll-view>
          </view>
          
          <view class="h-80 d-flex bg-f5f5f5">
            <view
              v-for="(item, index) in emoTypeList"
              :key="index"
              class="h-p100 d-flex j-center a-center ptb-00-plr-42 text-6 font-28" 
              :class="emoTypeIndex == index ? 'bg-ffffff' : 'bg-f5f5f5'"
              @click="selectEmoType(index)"
            >{{item}}</view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { RabbitHeadEmojiList, RabbitEmojiList, CommonEmojiList } from '@/common/js/utils/emoji'
import { mapState } from 'vuex'

export default {
  props: {
    goodsId: {
      required: true
    }
  },
  data: () => ({
    RabbitHeadEmojiList,
    RabbitEmojiList,
    CommonEmojiList,
    comments: [],
    popupVisible: false,
    commentParams: {
      goods_id: '',
      content: '',
      emoji_image: '',
      pid: '',
    },
    showEmo: false,
    emoTypeList: ['兔头','兔子','Emoji'],
    emoTypeIndex: 0,
    placeholder: '说说你的观点…',
    checked: false,
    isAgreed: false,
  }),
  computed: {
    ...mapState(['agreementPrefix', 'userInfo']),
    disabled ({ commentParams, checked, isAgreed }) {
      return !(commentParams.content.length && (isAgreed ? true : checked))
    }
  },
  methods: {
    load () {
      this.$u.api.getAuctionGoodsComments({ goods_id: this.goodsId }).then(res => {
        const list = res?.data?.list || []
        list.forEach(item => {
          item.$replysLen = item?.replys.length || 0
          item.$isShowAllReply = false
        })
        this.comments = list
      })
    },
    open () {
      this.popupVisible = true
      const { commentParams, placeholder } = this.$options.data()
      this.commentParams = Object.assign({}, commentParams, { goods_id: this.goodsId })
      this.placeholder = placeholder
    },
    addEmoji(emo) {
      this.commText = this.commText + emo
    },
    selectEmoType(index) {
      this.emoTypeIndex = index
    },
    async onEnjoy (item) {
      const isLogin = await this.login.isLoginV3(this.$vhFrom)
      if (!isLogin) return
      const { id, top_pid, like_status } = item
      let currComment = null
      if (top_pid) {
        const { replys = [] } = this.comments.find(item => item.id === top_pid) || {}
        currComment = replys.find(item => item.id === id)
      } else {
        currComment = this.comments.find(item => item.id === id)
      }
      if (!currComment) return
      this.feedback.loading({ title: '' })
      try {
        if (like_status) {
          await this.$u.api.cancelEnjoyAuctionGoodsComment({ goods_id: this.goodsId, comment_id: id })
          currComment.like_status = 0
          currComment.like_nums--
        } else {
          await this.$u.api.addEnjoyAuctionGoodsComment({ goods_id: this.goodsId, comment_id: id })
          currComment.like_status = 1
          currComment.like_nums++
        }
      } finally {
        this.feedback.hideLoading()
      }
    },
    async onSubmit () {
      if (this.disabled) return
      const res = await this.$u.api.addAuctionGoodsComment(this.commentParams)
      const data = res?.data || null
      if (!data) return
      const { pid, topPid } = this.commentParams
      const comment_time = '刚刚'
      if (pid) {
        const findItem = this.comments.find(item => item.id == topPid)
        if (findItem) {
          Object.assign(data, { like_status: 0, like_nums: 0, comment_time })
          findItem.replys.unshift(data)
          findItem.$replysLen++
        }
      } else {
        Object.assign(data, { replys: [], $replysLen: 0, $isShowAllReply: false, like_status: 0, like_nums: 0, comment_time })
        this.comments.unshift(data)
      }
      this.popupVisible = false
      if (!this.isAgreed) {
        this.$u.api.agreeProtocol({ xid: 1 }).then(() => {
          uni.setStorageSync('protocolList', ['1'])
          this.isAgreed = true
        })
      }
    },
    async onReply (item) {
      const isLogin = await this.login.isLoginV3(this.$vhFrom)
      if (!isLogin) return
      const { id, top_pid, nickname } = item
      this.popupVisible = true
      this.commentParams = Object.assign({}, this.$options.data().commentParams, { goods_id: this.goodsId, pid: id, topPid: top_pid || id })
      this.placeholder = `@${nickname}:`
    },
    loadAgreementStatus () {
      uni.getStorage({
        key: 'protocolList',
        success: res => {
          this.isAgreed = res.data.includes('1')
        },
        fail: async () => {
          const protocol = this?.userInfo?.protocol || []
          this.isAgreed = protocol.includes('1')
          if (this.isAgreed) uni.setStorageSync('protocolList', protocol)
        }
      })
    },
    onToggleReply (item) {
      const { id } = item
      const currComment = this.comments.find(item => item.id === id)
      if (!currComment) return
      currComment.$isShowAllReply = !currComment.$isShowAllReply
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
