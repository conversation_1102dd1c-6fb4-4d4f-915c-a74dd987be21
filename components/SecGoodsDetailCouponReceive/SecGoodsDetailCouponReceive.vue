<template>
	<!-- <view v-if="list.length || Object.keys(rabbitCoupon).length" class="flex-sb-c"  @click.stop="$emit('receiveCoupon')">
		<view class="w-560 flex-s-c flex-wrap">
			<view v-if="showRabbitCoupon" class="p-rela w-146 h-46 flex-c-c mr-16 mt-08 mb-08 font-24 text-fe3637">
				<image class="p-abso w-p100 h-p100" :src="ossIcon(`/goods_detail/s_cou_bg1.png`)" mode="aspectFill"></image>
				<view class="p-rela z-02 w-p100 h-p100 flex-c-c">无门槛{{ rabbitCoupon.price }}元</view>
			</view>
			
			<view class="p-rela h-46 flex-c-c mr-16 mt-08 mb-08 ptb-00-plr-20 font-24 text-fe3637" v-for="( item, index ) in getCouponList" :key="index">
				<image class="p-abso w-p100 h-p100" :src="ossIcon(`/goods_detail/s_cou_bg1.png`)" mode="scaleToFill"></image>
				<view class="p-rela z-02 w-p100 h-p100 flex-c-c">{{ item.label }}</view>
			</view>
		</view>
		
		<view class="w-92 h-38 bg-fe3637 flex-c-c font-24 text-ffffff b-rad-20">兑换</view>
	</view> -->
	<view v-if="Object.keys(rabbitCoupon).length" class="flex-sb-c"  @click.stop="$emit('receiveCoupon')">
		<view class="w-560 flex-s-c flex-wrap">
			<view class="p-rela w-146 h-46 flex-c-c mr-16 mt-08 mb-08 font-24 text-fe3637">
				<image class="p-abso w-p100 h-p100" :src="ossIcon(`/goods_detail/s_cou_bg1.png`)" mode="aspectFill"></image>
				<view class="p-rela z-02 w-p100 h-p100 flex-c-c">无门槛{{ rabbitCoupon.price }}元</view>
			</view>
		</view>
		
		<view class="w-92 h-38 bg-fe3637 flex-c-c font-24 text-ffffff b-rad-20">兑换</view>
	</view>
</template>

<script>
	export default {
		props: {
			list: {
				type: Array,
				default: () => [],
			},
			rabbitCoupon: {
				type: Object,
				default: () => ({})
			}
		},
		computed: {
			// showRabbitCoupon({ list, rabbitCoupon }) {
			// 	return !!(rabbitCoupon.price && list.length <= 2)
			// },
			// getCouponList({ list, showRabbitCoupon }) {
			// 	const sliceLen = showRabbitCoupon ? 2 : 3
			// 	const sliceList = list.slice(0, sliceLen)
			// 	return sliceList
			// }
		}
	}
</script>