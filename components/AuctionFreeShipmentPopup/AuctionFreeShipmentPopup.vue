<template>
  <u-popup :value="value" mode="center" width="552rpx" height="410rpx" border-radius="20" @input="onInput">
    <view class="p-rela wh-p100">
      <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso wh-p100" />
      <view class="p-rela pt-84 pb-20 h-p100">
        <view class="font-wei-500 font-32 text-3 text-center">运费说明</view>
        <view class="mt-24 ptb-00-plr-84 font-26 text-3 l-h-36 text-center">此拍品为包邮拍品，运费将由卖家承担。</view>
        <view class="p-abso bottom-20 w-p100" @click="onInput(false)">
          <view class="w-p100 h-01 bg-dedede"></view>
          <view class="flex-c-c h-104 font-wei-500 font-28 text-e80404">知道了</view>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
