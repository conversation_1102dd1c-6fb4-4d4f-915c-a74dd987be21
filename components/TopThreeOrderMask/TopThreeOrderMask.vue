<template>
	<u-mask :show="show" :zoom="false" @click="$emit('close')">
		<view class="h-p100 flex-c-c-c">
			<view class="p-rela z-01 w-526 h-710" @click.stop="$emit('jump')">
				<image class="p-abso w-p100 h-p100" :src="ossIcon(`/pay_success/cou_bg4.png`)" />
				<view style="top: 84rpx; right: 226rpx; color: #CA101A; line-height: 280rpx; text-shadow: 6rpx 0 0 #000;" class="p-abso font-wei-600 font-200">
					{{ info.coupon_money }}
				</view>
				<view style="top: 460rpx; left: 50%; transform: translateX(-50%);" class="p-abso font-32 text-3">{{ stageText }}</view>
			</view>
			<image class="mt-60 w-54 h-54" :src="ossIcon(`/pay_success/del_gray.png`)" mode="widthFix" @click="$emit('close')"/>
		</view>
    </u-mask>
</template>

<script>
	export default {
		props: {
			show: {
				type: Boolean,
				default: false
			},
			info: {
				type: Object,
				default: () => ({})
			}
		},
		computed: {
			stageText ({ info }) {
				switch (info.stage) {
					case 2:
						return '成功解锁第二单'
					case 3:
						return '成功解锁第三单'
					default:
						return '新人首单礼'
				}
			}
		}
	}
</script>