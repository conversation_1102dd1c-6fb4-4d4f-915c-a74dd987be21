<template>
  <view class="fade-in-down">
    <!-- 秒发 -->
    <!-- <template v-if="channel === 1">
              <view v-if="channel === 1 && isSecondNewPeopleGoodsConfNewActivity" class="p-rela w-p100 h-200 mt-n-12">
                  <view v-if="!isHiddenPrice && priceDiscount" class="p-abso z-04 top-20 right-24 w-80 h-44 flex-c-c b-rad-10 b-s-01-fde8eb font-24 text-ffffff">{{ priceDiscount }}折</view>
                  <image class="p-abso w-p100 h-200" :src="ossIcon(`/goods_detail/s_new_bg_2.png`)" mode="aspectFill" />
                  <view class="p-abso bottom-0 w-p100 h-116 d-flex j-sb ptb-00-plr-24" :class="isHiddenPrice ? 'a-center' : 'a-start pt-20'">
                      <view v-if="isHiddenPrice" class="font-42 text-6 l-h-58">价格保密</view>
                      <view v-else class="d-flex a-start">
                          <view class="p-rela top-26 font-28 text-9 l-h-40">低至</view>
                          <view class="ml-10 h-74 font-52 font-wei-500 text-e80404 l-h-74"><text class="font-28">¥{{ ' ' }}</text>{{secNewPrice}}</view>
                          <view class="p-rela top-26 ml-20 font-28 text-9 l-h-40">原价<text class="text-dec-l-t">¥{{ marketPrice }}</text></view>
                      </view>
                      <text class="font-24 text-6 l-h-34" :class="isHiddenPrice ? '' : 'p-rela top-30'">已售{{soldNum}}</text>
                  </view>
              </view>
              <view v-else class="d-flex j-sb pr-24 pl-24" :class="isHiddenPrice ? 'a-center mt-40' : 'a-start mt-32'">
                  <view v-if="isHiddenPrice" class="font-42 text-6 l-h-58">价格保密</view>
                  <view v-else class="d-flex a-start">
                      <view class="h-74 font-52 font-wei-500 text-e80404 l-h-74"><text class="font-28">¥{{ ' ' }}</text>{{price}}</view>
                      <view class="p-rela top-26 ml-20 font-28 text-9 l-h-40">原价<text class="text-dec-l-t">¥{{ marketPrice }}</text></view>
                  </view>
                  <text class="font-24 text-6 l-h-34" :class="isHiddenPrice ? '' : 'p-rela top-30'">已售{{soldNum}}</text>
              </view>
          </template>
          <view v-else-if="channel === 9"  class="mt-32 d-flex j-sb a-center pr-24 pl-24">
              <view v-if="priceSecrecy == 1 || onSaleStatus == 3 || onSaleStatus == 4" class="font-52 font-wei text-e80404">价格保密</view>
              <view v-else class="">
                  <text class="font-52 font-wei text-e80404"><text class="mr-06 font-24">¥</text>{{price}}</text>
                  <text class="mt-16 ml-04 font-28 text-9 text-dec-l-t">¥{{marketPrice}}</text>
              </view>
              <text class="font-26 text-6">已售<text class="font-40 font-wei text-e80404">{{soldNum}}</text>份</text>
          </view> -->

    <template v-if="[1, 9].includes(channel)">
      <view v-if="isSecondNewPeopleGoodsConfNewActivity" class="p-rela w-p100 h-200 mt-n-12">
        <view
          v-if="!isHiddenPrice && priceDiscount"
          class="p-abso z-04 top-20 right-24 w-80 h-44 flex-c-c b-rad-10 b-s-01-fde8eb font-24 text-ffffff"
          >{{ priceDiscount }}折</view
        >
        <image class="p-abso w-p100 h-200" :src="ossIcon(`/goods_detail/s_new_bg_2.png`)" mode="aspectFill" />
        <view
          class="p-abso bottom-0 w-p100 h-116 d-flex j-sb ptb-00-plr-24"
          :class="isHiddenPrice ? 'a-center' : 'a-start pt-20'"
        >
          <view v-if="isHiddenPrice" class="font-42 text-6 l-h-58">价格保密</view>
          <view v-else class="d-flex a-start">
            <view class="p-rela top-26 font-28 text-9 l-h-40">低至</view>
            <view class="ml-10 h-74 font-52 font-wei-500 text-e80404 l-h-74"
              ><text class="font-28">¥{{ ' ' }}</text
              >{{ secNewPrice }}</view
            >
            <!-- <view class="p-rela top-26 ml-20 font-28 text-9 l-h-40"
              >原价<text class="text-dec-l-t">¥{{ marketPrice }}</text></view
            > -->
          </view>
          <text class="font-24 text-6 l-h-34" :class="isHiddenPrice ? '' : 'p-rela top-30'">已售{{ soldNum }}</text>
        </view>
      </view>
      <view v-else class="d-flex j-sb pr-24 pl-24" :class="isHiddenPrice ? 'a-center mt-40' : 'a-start mt-32'">
        <view v-if="isHiddenPrice" class="font-42 text-6 l-h-58">价格保密</view>
        <view v-else class="d-flex a-start">
          <view class="h-74 font-52 font-wei-500 text-e80404 l-h-74"
            ><text class="font-28">¥{{ ' ' }}</text
            >{{ price }}</view
          >
          <!-- <view class="p-rela top-26 ml-20 font-28 text-9 l-h-40"
            >原价<text class="text-dec-l-t">¥{{ marketPrice }}</text></view
          > -->
        </view>
        <text class="font-24 text-6 l-h-34" :class="isHiddenPrice ? '' : 'p-rela top-30'">已售{{ soldNum }}</text>
      </view>
    </template>
    <!-- 秒杀 -->
    <view v-else-if="is_seckill && !isDepositGoods" class="">
      <view class="p-rela w-p100 h-98 d-flex j-sb a-center pl-132 pr-26">
        <image
          class="p-abso left-0 z-n-01 w-p100 h-p100"
          src="https://images.vinehoo.com/vinehoomini/v3/goods_detail/seckill.png"
          mode="aspectFill"
        ></image>

        <view class="">
          <view class="font-24 font-wei text-ffffff l-h-34">限量秒杀</view>
          <view v-if="quota_number != '9999'" class="font-22 text-ffb7b7 l-h-32">每人限购{{ quota_number }}件</view>
        </view>

        <view v-if="saleStatus === 2" class="d-flex a-center">
          <view class="font-24 text-912b00">距结束还剩：</view>
          <vh-count-down
            :timestamp="timestamp"
            :bg-color="'#F6780E'"
            :day-color="'#912B00'"
            :separatorColor="'#ffffff'"
          ></vh-count-down>
        </view>

        <view v-else class="bg-231-136-000-040 d-flex b-rad-04">
          <view
            v-if="saleStatus === 0"
            class="w-230 h-52 bg-e78800 d-flex j-center a-center b-rad-04 font-28 text-ffffff"
            >{{ getSaleDate }}</view
          >
          <view v-if="saleStatus === 1" class="w-240 h-52 bg-e78800 d-flex j-center a-center b-rad-04">
            <text class="font-24 text-ffffff">倒计时</text>
            <vh-count-down
              :show-days="false"
              :timestamp="timestamp"
              :bg-color="'#ffffff'"
              :color="'#F6780E'"
              :day-color="'#F6780E'"
              :separatorColor="'#ffffff'"
            ></vh-count-down>
          </view>
          <view class="w-142 h-52 d-flex j-center a-center font-28 text-912b00">即将开抢</view>
        </view>
      </view>

      <view v-if="!isHiddenPrice" class="mt-32 d-flex a-center pl-24">
        <text class="font-52 font-wei text-e80404"><text class="mr-06 font-24">¥ </text>{{ price }}</text>
        <text class="mt-16 ml-04 font-28 text-9 text-dec-l-t">¥{{ marketPrice }}</text>
        <text class="mt-16 bg-fce4e3 b-rad-04 ml-12 ptb-02-plr-06 font-20 text-e80404 l-h-28">秒杀价</text>
      </view>

      <view v-if="isHiddenPrice" class="mt-32 pl-24 font-52 font-wei text-e80404">价格保密</view>
    </view>

    <!-- 闪购、跨境、尾货、拼团 -->
    <view v-else class="p-rela w-p100 h-98 d-flex j-sb a-center pl-130">
      <image class="p-abso left-0 z-n-01 w-p100 h-p100" :src="src" mode="aspectFill"></image>
      <view class="d-flex a-center">
        <!-- 2022-07-19 商品下架或者售罄时也显示价格保密  需求方：龙飞-->
        <view v-if="isHiddenPrice" class="mb-06 font-40 font-wei text-ffffff">价格保密</view>
        <view v-else class="">
          <view v-if="isDepositGoods" class="flex-c-c">
            <view class="flex-c-e">
              <view class="font-28 text-ffffff mb-10">订金¥</view>
              <view class="font-52 font-wei text-ffffff">{{ depositPackageList[0].deposit_price }}</view>
            </view>
            <view class="w-98 h-34 bg-ffffff flex-c-c b-rad-30 ml-20 font-22 text-e80404">可膨胀</view>
          </view>
          <view v-else class="d-flex a-start">
            <!-- <text class="font-52 font-wei text-ffffff l-h-40"><text class="font-28">¥</text>{{price}}</text>
                          <text class="ml-04 font-28 text-ffffff text-dec-l-t">¥{{marketPrice}}</text> -->
            <view class="h-74 font-52 font-wei text-ffffff l-h-74"
              ><text class="font-28">¥{{ ' ' }}</text
              >{{ price }}</view
            >
            <!-- <view class="p-rela top-26 ml-12 font-28 text-ffffff l-h-40 text-dec-l-t">¥{{ marketPrice }}</view> -->
          </view>
        </view>
      </view>
      <view v-if="saleStatus == 0" class="d-flex flex-column j-center a-center mr-10">
        <view class="font-24" :style="{ color: countDownStyle.get(channel).color }">即将开售</view>
        <view
          class="bg-e80404 b-rad-06 mt-04 ptb-00-plr-06 font-24 text-ffffff"
          :style="{ backgroundColor: countDownStyle.get(channel).bg }"
          >{{ getSaleDate }}</view
        >
      </view>
      <view v-if="saleStatus == 1" class="d-flex flex-column j-center a-center mr-38">
        <view class="font-24" :style="{ color: countDownStyle.get(channel).color }">即将开售</view>
        <view class="mt-04">
          <vh-count-down
            :show-days="false"
            :timestamp="timestamp"
            :bg-color="countDownStyle.get(channel).bg"
            :day-color="countDownStyle.get(channel).color"
            :separatorColor="countDownStyle.get(channel).color"
          ></vh-count-down>
        </view>
      </view>
      <view v-if="saleStatus == 2" class="d-flex flex-column j-center a-center mr-10">
        <view class="font-24" :style="{ color: countDownStyle.get(channel).color }">距结束还剩</view>
        <view class="mt-04">
          <vh-count-down
            :timestamp="timestamp"
            :bg-color="countDownStyle.get(channel).bg"
            :day-color="countDownStyle.get(channel).color"
            :separatorColor="countDownStyle.get(channel).color"
          ></vh-count-down>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
/*
       * vh-goods-detail-channel-bg 商品详情频道底图背景
       * @description 该组件一般用于商品详情显示 频道底图背景、商品价格、市场价格、限购、倒计时等信息
       * @property {String Numbr} channel 商品频道 0 = 闪购、1 = 秒发、2 = 跨境、3 = 尾货
       * @property {String Numbr} sale-status 售卖状态（前端判断日期显示样式） 0 = 即将售卖，且时间大于 24小时、1 = 即将售卖 且时间小于 24小时、2 = 售卖中、结束售卖
       * @property {String Numbr} on-sale-status 在售状态（后端返回） 0：待上架，1：待售中，2：在售中，3：已下架，4：已售罄
       * @property {String} saleDate 开售日期 格式为 '2022-01-12 10:38:50'
       * @property {String Number} timestamp 倒计时，单位为秒
       * @property {Numbr} price-secrecy 价格是否保密 0 = 不保密 1 = 保密
       * @property {String} price 价格
       * @property {String} market-price 市场价格
       * @property {String Numbr} sold-num 已售数量
       
       * @example <vh-goods-detail-channel-bg></vh-goods-detail-channel-bg>
       */
export default {
  name: 'vh-goods-detail-channel-bg',
  props: {
    is_seckill: {
      type: Number,
      default: 0,
    },
    isDepositGoods: {
      type: Boolean,
      default: false,
    },
    depositPackageList: {
      type: Array,
      default: () => [],
    },
    // 商品频道
    channel: {
      type: [String, Number],
      default: 0,
    },
    // 售卖状态 （前端判断日期显示样式）
    saleStatus: {
      type: [String, Number],
      default: 2,
    },
    // 在售状态（后端返回）
    onSaleStatus: {
      type: [String, Number],
      default: 2,
    },
    // 开售时期
    saleDate: {
      type: String,
      default: '2022-01-12 10:38:50',
    },
    // 倒计时的时间，秒为单位
    timestamp: {
      type: [Number, String],
      default: 0,
    },
    // 图片路径
    src: {
      type: String,
      default: '',
    },
    // 价格是否保密
    priceSecrecy: {
      type: Number,
      default: 1,
    },
    priceDiscount: {
      type: [String, Number],
      default: '0',
    },
    // 商品价格
    price: {
      type: [Number, String],
      default: '0',
    },
    secNewPrice: {
      type: [Number, String],
      default: '0',
    },
    // 市场价格
    marketPrice: {
      type: [Number, String],
      default: '0',
    },
    isSecondNewPeopleGoodsConfNewActivity: {
      type: Boolean,
      default: false,
    },
    userIsLogin: {
      type: Boolean,
      default: false,
    },
    quota_number: {
      type: [String, Number],
      default: '9999',
    },
    // 已售数量
    soldNum: {
      type: [String, Number],
      default: 0,
    },
  },
  computed: {
    // 是否隐藏价格
    isHiddenPrice({ priceSecrecy, onSaleStatus }) {
      if (priceSecrecy == 1 || onSaleStatus == 3 || onSaleStatus == 4) {
        return true
      }
      return false
    },

    // 倒计时样式
    countDownStyle() {
      let map = new Map([
        [0, { bg: '#E80404', color: '#E80404' }], //闪购
        [2, { bg: '#815DDA', color: '#815DDA' }], //跨境
        [3, { bg: '#F6780E', color: '#F6780E' }], //尾货
        [101, { bg: '#F6780E', color: '#F6780E' }], //拼团（前端默认频道为101 = 拼团商品 等同于 营销活动包含拼团商品）
      ])
      return map
    },

    // 获取开售日期
    getSaleDate() {
      let date = new Date(this.saleDate.replace(/-/g, '/'))
      let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
      let day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
      let hour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
      let min = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
      return `${month}月${day}日 ${hour}:${min}`
    },
  },
}
</script>

<style scoped></style>
