<template>
	<u-popup 
	v-model="value"  
	:maskCloseAble="true" 
	mode="bottom" 
	:height="676" 
	:popup="false" 
	length="auto" 
	@close="close" 
	:border-radius="40"
	>
		<view class="p-rela ptb-00-plr-40">
			<view class="p-stic z-03 top-0 bg-ffffff flex-c-c-c ptb-40-plr-00">
				<view class="text-center font-32 font-wei text-0">发票将由以下单位分别开具</view>
				<!-- <view class="mt-16 font-26 text-ff9127">纸质发票请联系客服开具</view> -->
			</view>
			
			<view class="pb-58 mt-nth-child1-00">
				<block v-for="( item, index ) in list" :key="index">
					<view v-if="item.goods_info.length" class="mt-32">
						<view class="flex-s-c">
							<view class="font-28 font-wei text-3">{{ item.invoice_company }}</view>
							<!-- <view v-if="item.invoice_company.includes('电子商务')" class="w-88 h-28 flex-c-c b-rad-06 b-s-01-feb36c ml-06 font-20 text-ff9127">纸质专票</view>
							<view v-if="item.invoice_company.includes('科技有限')" class="w-88 h-28 flex-c-c b-rad-06 b-s-01-c7c7c7 ml-06 font-20 text-6">电子专票</view> -->
						</view>
						
						<view class="mt-nth-child1-24">
							<view class="d-flex j-sb mt-10" v-for="(item1, index1) in item.goods_info" :key="index1">
								<view class="w-540 font-24 text-6 text-hidden-2">{{ item1.title }}</view>
								<view class="font-24 font-wei text-3">¥{{ item1.price }}</view>
							</view>
						</view>
					</view>
				</block>
			</view>
		</view>
	</u-popup>
</template>

<script>
	export default {
		props: {
			value: {
				type: Boolean,
				default: false
			},
			list: {
				type: Array,
				default: function() {
					return [];
				}
			}
		},
		methods: {
			close() {
				this.$emit('input', false);
			}
		}
	}
</script>