<template>
	<view class="p-rela" @click="handleJump(`${routeTable.pDWinePartyDetail}?id=${item.id}`)">
		<vh-image :src="item.banner_img" :loadingType="1" :width="item.$wfitemWidth" :height="item.$wfitemWidth" :isResize="item.$imgIsResize" :resizeRatio="{ wratio: 'auto', hratio: 'auto' }" />
		<view :class="pdClazz">
			<view :class="titleClazz">{{ item.title }}</view>
			<view :class="[isFree ? 'flex-sb-s' : 'flex-sb-e', mtClazz]">
				<view
					v-if="isFree"
					:style="[item.$isBigScreen ? { fontSize: '13px', lineHeight: '19px' } : { fontSize: '12px', lineHeight: '17px' }]"
					class="font-24 font-wei-500 text-e80404 l-h-34"
				>免费</view>
				<view v-else class="flex-s-e">
					<view
						:style="[item.$isBigScreen ? { height: '17px', fontSize: '16px', lineHeight: '17px' } : { height: '15px', fontSize: '14px', lineHeight: '15px' }]"
						class="h-30 font-28 font-wei-500 text-e80404 l-h-30"
					><text :style="[item.$isBigScreen ? { fontSize: '10px' } : { fontSize: '9px' }]" class="font-18">¥</text>{{ item.price }}</view>
					<view
						:style="[item.$isBigScreen ? { fontSize: '10px', lineHeight: '14px' } : { fontSize: '9px', lineHeight: '13px' }]"
						class="ml-06 font-18 text-9 l-h-26"
					>起</view>
				</view>
				<text
					:style="[
						item.$isBigScreen ? { fontSize: '10px', lineHeight: '14px', top: isFree ? '4px' : 0 } : { fontSize: '9px', lineHeight: '13px', top: isFree ? '3px' : 0 },
					]"
					class="p-rela font-18 text-9 l-h-26"
				>剩余名额 {{ item.remaining_quota || 0 }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import secondWfitemMixin from '@/common/js/mixins/secondWfitemMixin'
	export default {
		mixins: [secondWfitemMixin],
		computed: {
			isFree () {
				return this.item.price == '0.00'
			}
		}
		// props: {
		// 	item: {
		// 		type: Object,
		// 		default: () => ({
		// 			"id": 1,
		// 			"title": "甜渣节主会场-成都站",
		// 			"banner_img": "/vine/a.jpg",
		// 			"price": 188,
		// 			"remaining_quota": 3//剩余数
		// 		})
		// 	}
		// }
	}
</script>