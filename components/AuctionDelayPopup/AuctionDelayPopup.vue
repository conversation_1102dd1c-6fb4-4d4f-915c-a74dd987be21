<template>
  <u-popup :value="value" mode="center" width="552rpx" height="414rpx" border-radius="20" @input="onInput">
    <view class="p-rela w-552 h-414">
      <image :src="ossIcon('/auction/popup_bg_552_414.png')" class="p-abso w-552 h-414" />
      <view class="p-rela pt-52">
        <view class="font-wei-600 font-32 text-3 text-center">“延时周期”是什么？</view>
        <view class="mt-20 ptb-00-plr-38 font-28 text-6 l-h-48 text-center">竞拍结束前2分钟内，若有其他人出价，则竞拍时间将延长{{ delayTime }}分钟，直至最后2分钟内无人出价，竞拍结束。</view>
        <view class="mt-62 font-wei-500 font-28 text-e80404 text-center" @click="onInput(false)">知道了</view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    delayTime: {
      type: [Number, String],
      default: 5
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
