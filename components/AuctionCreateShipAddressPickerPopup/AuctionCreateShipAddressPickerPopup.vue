<template>
  <u-popup :value="value" mode="bottom" width="100%" height="618rpx" border-radius="20" @input="onInput">
    <view class="pt-48 pl-48 pr-48">
      <view class="p-rela flex-c-c">
        <text class="font-wei-500 font-32 text-3 l-h-44">选择地区</text>
        <text class="p-abso right-0 font-28 text-3 l-h-40" @click="onConfirm">确定</text>
      </view>

      <picker-view :value="pickerViewValue" @change="onPickerViewValueChange" indicator-style="height: 104rpx" class="mt-40 h-416">
        <picker-view-column>
            <view class="flex-c-c font-wei-600 font-28 text-9" v-for="(item, index) in list" :key="index">{{ item }}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    list: [],
    pickerViewValue: [0]
  }),
  watch: {
    value (newVal) {
      if (newVal) {
        this.initList();
      }
    }
  },
  methods: {
    initList () {
      if (this.list.length) return
      this.$u.api.getShipAddressRegionList().then(res => {
        this.list = res.data.from_addr_list
      })
    },
    onInput (value) {
      this.$emit('input', value)
    },
    onConfirm () {
      const shipAddress = this.list[this.pickerViewValue[0]]
      this.$emit('confirm', shipAddress)
      this.onInput(false)
    },
    onPickerViewValueChange (e) {
      const value = e.detail.value
      this.pickerViewValue = value
    }
  },
}
</script>