<template>
	<view class="" @click="onClick">
		<vh-image :src="item.image" :height="422" />
		<view class="ptb-20-plr-24">
			<view class="font-30 font-wei text-3">{{item.title}}</view>
			<view class="d-flex j-sb a-center mt-24">
				<view class="font-28 text-9 l-h-40">{{item.modul_data.start_time}}</view>
				<view class="d-flex a-center">
					<image class="w-28 h-26" :src="`https://images.vinehoo.com/vinehoomini/v3/comm/view.png`" mode="widthFix" />
					<text class="ml-06 font-24 text-9 l-h-34">{{item.modul_data.num}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import adBuryDotMixin from '@/common/js/mixins/adBuryDotMixin'
	import { mapState } from 'vuex'
	/**
	 * normal-live-ad 普通直播广告位
	 * @description 该组件一般用于普通列表的直播广告位
	 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
	 * @property {Object} item 广告位列表的每一项
	 * @example <vh-normal-wine-party-ad />
	 */
	export default {
		name:'vh-normal-live-ad',
		mixins: [adBuryDotMixin],
		
		props: {
			// 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
			from: {
				type: [String, Number],
				default: ''
			},
			
			// 商品列表每一项
			item: {
				type: Object,
				default() {
					return {}
				}
			},
		},
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
		methods: {
			onClick () {
				if (this.buryDotParams) {
					const { channel, region_id } = this.buryDotParams
					const genre = 3
					const button_id = this.item.id
					this.jump.jumpAppLiveBD(this.item.modul_data, channel, genre, region_id, button_id, this.from)
				} else {
					this.jump.jumpAppLive(this.item.modul_data, this.from)
				}
			}
		}
	}
</script>

<style>
</style>