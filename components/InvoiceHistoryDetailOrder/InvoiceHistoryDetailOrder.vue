<template>
	<view class="">
		<!-- @click="handleJump(`${$routeTable.pgGoodsDetail}?id=${item.period}`, $vhFrom)" -->
		<view class="bg-ffffff b-rad-16 mtb-00-mlr-24 mt-20 ptb-28-plr-24" v-for="( item, index ) in list" :key="index">
			<view class="flex-sb-c bb-s-01-eeeeee pb-28 font-26 text-3">
				<view class="flex-s-c">
					<text class="font-28 text-6">订单编号</text>
					<text class="ml-20">{{ item.sub_order_no }}</text>
				</view>
				<text class="font-wei-500">已完成</text>
			</view>
			
			<view class="d-flex mt-28">
				<vh-image :src="item.banner_img" :loadingType="2" :width="246" :height="152" :borderRadius="6" />
				<view class="flex-1 flex-sb-n-c ml-20">
					<view class="">
						<view class="font-24 text-hidden-2 l-h-34">{{ item.title }}</view>
						<view class="mt-04">
							<text class="bg-f5f5f5 b-rad-08 ptb-04-plr-16 font-22 text-9">{{ item.package_name }}</text>
						</view>
					</view>
					<text class="font-24 text-9">x{{ item.order_qty }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'
	export default {
		// mixins: [longpressCopyMixin],
		props: {
			list: {
				type: Array,
				default: () => []
			}
		}
	}
</script>