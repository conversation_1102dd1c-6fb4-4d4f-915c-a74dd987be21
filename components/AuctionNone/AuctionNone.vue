<template>
  <view>
    <view class="flex-c-c">
      <image :src="ossIcon(img)" :class="imgClazz" />
    </view>
    <view v-if="btnText" class="flex-c-c" :class="btnBlockClazz">
      <button class="vh-btn flex-c-c w-208 h-64 font-wei-500 font-32 text-e80404 bg-ffffff b-rad-29 b-s-02-e80404" @click="btnFun">{{ btnText }}</button>
    </view>
    <view v-if="title" class="font-wei-500 font-36 text-3 l-h-50 text-center" :class="titleClazz">{{ title }}</view>
    <view v-if="desc" class="font-28 text-6 l-h-40 text-center" :class="descClazz">{{ desc }}</view>
  </view>
</template>

<script>
import Vue from 'vue'

export default {
  props: {
    img: {
      type: String,
      default: '/auction/none_440_400.png'
    },
    imgClazz: {
      type: String,
      default: 'w-440 h-400'
    },
    btnText: {
      type: String,
      default: '',
    },
    btnFun: {
      type: Function,
      default: () => {
        const { $app, $ios } = Vue.prototype
        if (!$app) return
        if ($ios) {
          wineYunJsBridge.openAppPage({
            client_path: 'jumpCommunityAuction'
          })
          return
        }
        wineYunJsBridge.openAppPage({
          client_path: { 'android_path': 'goMain' },
          ad_path_param: [
            { android_key: 'type', android_val: '2' },
            { android_key: 'str', android_val: '1' }
          ]
        })
      }
    },
    btnBlockClazz: {
      type: String,
      default: 'mt-68'
    },
    title: {
      type: String,
      default: '',
    },
    titleClazz: {
      type: String,
      default: 'mt-72'
    },
    desc: {
      type: String,
      default: '',
    },
    descClazz: {
      type: String,
      default: 'mt-20'
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
