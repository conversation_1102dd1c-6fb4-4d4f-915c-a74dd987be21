<template>
	<view class="">
		<block v-for="(innItem, innIndex) in item.goodsInfo" :key="innIndex">
			<view class="d-flex mt-20">
				<vh-image :loading-type="2" :src="innItem.goods_img" :width="160" :height="160" :border-radius="10" />
				<view class="flex-1 d-flex flex-column j-sb ml-12">
					<view class="">
						<view class="text-hidden-2">
							<vh-channel-title-icon :channel="innItem.periods_type" padding="2rpx 8rpx" :font-size="20"/>
							<text class="ml-12 font-24 text-0 l-h-34">{{innItem.goods_title}}</text>
						</view>
						<view class="flex-e-c">
							<text class="font-24 text-9">x{{ innItem.order_qty }}</text>
						</view>
					</view>
					<view class="d-flex j-sb a-center">
						<text class="p-rela z-04 w-98 h-30 flex-c-c bs-bb b-rad-02 b-s-01-e80404 mb-02 font-18 text-hidden text-e80404">商家拍品</text>
						<view class="text-3">
							<text class="font-18">成交价：</text>
							<text class="font-22 font-wei">¥</text>
							<text class="font-32 font-wei">{{ item.payment_amount }}</text>
						</view>
					</view>
				</view>
			</view>
		</block>
	</view>
</template>

<script>
	export default{
		name: "OrderListAuctionGoodsItem",
		
		props: {
			// 订单信息
			item : {
				type: Object,
				default: () => ({})
			},
		}
	}
</script>
