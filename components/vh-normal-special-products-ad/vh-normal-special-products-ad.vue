<template>
  <view class="" @click="onClick">
    <vh-image :src="item.image" :height="434" />
    <view class="ptb-20-plr-24">
      <view class="" @longpress.stop="handleLongpress(item.title, from)" @touchend="handleTouched(from)">
        <vh-channel-title-icon
          :channel="item.modul_data.periods_type"
          :marketing-attribute="item.modul_data.marketing_attribute"
          :font-bold="false"
        />
        <text class="ml-16 font-30 font-wei text-3 l-h-44">{{ item.title }}</text>
      </view>

      <view class="mt-12 font-28 text-9 l-h-40">{{ item.modul_data.brief }}</view>

      <view class="mt-28 d-flex j-sb a-center">
        <view
          v-if="item.modul_data.is_hidden_price == 1 || [3, 4].includes(item.modul_data.onsale_status)"
          class="font-36 font-wei text-e80404 l-h-44"
          >价格保密</view
        >
        <view v-else class="d-flex a-center">
          <text class="font-36 font-wei text-e80404 l-h-44"
            ><text class="font-24">¥</text>{{ item.modul_data.price }}</text
          >
          <!-- <text class="ml-16 font-24 text-9 text-dec-l-t l-h-36">¥{{item.modul_data.market_price}}</text> -->
        </view>
        <!-- 2022-07-19 秒发列表只显示已售 需求方：杨文科 -->
        <view class="">
          <text v-if="item.modul_data.periods_type == 1" class="font-24 text-9 l-h-36"
            >已售<text class="text-e80404">{{ item.modul_data.purchased + item.modul_data.vest_purchased }}</text></text
          >
          <text v-else class="font-24 text-9 l-h-36"
            >已售<text class="text-e80404">{{ item.modul_data.purchased + item.modul_data.vest_purchased }} </text
            >/限量<text class="text-e80404">{{ item.modul_data.limit_number }}</text
            >{{ item.modul_data.quota_rule.quota_number == '9999' ? '' : '/限购' }}
            <text class="text-e80404">{{
              item.modul_data.quota_rule.quota_number == '9999' ? '' : item.modul_data.quota_rule.quota_number
            }}</text></text
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'
import adBuryDotMixin from '@/common/js/mixins/adBuryDotMixin'
import { mapState } from 'vuex'
/**
 * normal-special-products-ad 普通专题产品广告位
 * @description 该组件一般用于普通专题商品广告位
 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
 * @property {Object} item 商品列表的每一项
 * @example <vh-normal-special-products-ad />
 */
export default {
  name: 'vh-normal-special-products-ad',

  props: {
    // 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
    from: {
      type: [String, Number],
      default: '',
    },

    // 商品列表每一项
    item: {
      type: Object,
      default() {
        return {}
      },
    },
  },

  mixins: [longpressCopyMixin, adBuryDotMixin],

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable']),
  },

  methods: {
    onClick() {
      if (this.jumpStatus) return
      if (this.buryDotParams) {
        const { channel, region_id } = this.buryDotParams
        const genre = 3
        const button_id = this.item.id
        this.jump.appAndMiniJumpBD(
          1,
          `${this.routeTable.pgGoodsDetail}?id=${this.item.modul_data.id}`,
          channel,
          genre,
          region_id,
          button_id,
          this.from
        )
      } else {
        this.jump.appAndMiniJump(1, `${this.routeTable.pgGoodsDetail}?id=${this.item.modul_data.id}`, this.from)
      }
    },
  },
}
</script>

<style></style>
