<template>
	<u-mask :show="show" :zoom="false">
		<view class="h-p100 flex-c-c-c">
			<view class="w-490 bg-ffffff b-rad-20">
				<view class="pt-86 pb-64">
					<view class="flex-c-c">
						<image class="w-264 h-184" :src="ossIcon(`/comm/succ_red.png`)" />
					</view>
					
					<view class="flex-c-c-c mt-30 l-h-44">
						<view class="font-28 text-3">中奖提示</view>
						<view class="pl-24 pr-24 text-center font-28 text-3">{{ info.msg }}</view>
					</view>
				</view>
				<view class="w-p100 h-104 bt-s-01-dedede">
					<view v-if="info.first_goods_id" class="wh-p100 flex-c-c">
						<view class="w-p50 h-p100 flex-c-c font-28 text-9" @click="$emit('know')">知道了</view>
						<view class="p-rela z-02 w-p50 h-p100 flex-c-c bl-s-01-eeeeee font-28 text-e80404"
						@click="$emit('use', info)">去使用</view>
					</view>
					<view v-else class="wh-p100 flex-c-c font-28 text-9" @click="$emit('know')">知道了</view>
				</view>
			</view>
		</view>
	</u-mask>
</template>

<script>
	export default {
		props: {
			show: {
				type: Boolean,
				default: false
			},
			info: {
				type: Object,
				default: () => ({})
			}
		},
		methods: {
			
		}
	}
</script>