<template>
	<text v-if="orderType === 11" class="p-rela z-04 w-98 h-30 flex-c-c bs-bb b-rad-02 b-s-01-e80404 mt-08 mb-02 font-18 text-hidden text-e80404">{{ auctionType ? '个人拍品' : '商家拍品' }}</text>
	<text v-else class="bg-f5f5f5 ptb-04-plr-18 b-rad-08 mt-08 font-22 text-9">{{goodsInfo.package_name}}</text>
</template>

<script>
	export default {
		props: {
			// 订单类型
			orderType: {
				type: [String, Number],
				default: 0
			},
			auctionType: {
			},
			// 商品信息
			goodsInfo: {
				type: Object,
				default: () => ({})
			}
		}
	}
</script>

<style>
</style>