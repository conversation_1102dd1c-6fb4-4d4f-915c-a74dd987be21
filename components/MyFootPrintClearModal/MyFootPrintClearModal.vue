<template>
	<u-popup
	v-model="value"  
	:maskCloseAble="false" 
	mode="center" 
	:popup="false" 
	length="auto" 
	@close="$emit('input', false)" 
	:border-radius="10"
	>
	<view class="w-546 h-222 bg-ffffff">
		<view class="w-p100 h-138 flex-c-c bb-s-01-eeeeee">
			<view class="font-28 font-wei text-3">确定清空浏览记录？</view>
		</view>
		<view class="w-p100 h-82 d-flex">
			<view class="w-p50 h-p100 flex-c-c font-28 text-9" @click="$emit('input', false)">取消</view>
			<view class="w-p50 h-p100 flex-c-c bl-s-01-eeeeee font-28 text-e80404" @click="$emit('confirm')">确定</view>
		</view>
	</view>
	</u-popup>
</template>

<script>
	export default {
		props: {
			value: {
				type: Boolean,
				default: false
			}
		},
	}
</script>