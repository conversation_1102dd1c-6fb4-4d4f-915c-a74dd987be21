<template>
  <view class="auction-wglist">
    <u-waterfall ref="uWaterFallRef" :value="list" :add-time="addTime" @input="$emit('input', $event)">
      <template v-slot:left="{ leftList }">
        <view v-for="(item, index) in leftList" :key="index" class="mb-20">
		  <AuctionGoodsListFirstItem v-if="item.$isFirstBanner" :item="item"/>
          <vh-waterfall-goods-list v-else-if="item.periods_type" :from="$vhFrom" :item="item" class="w-340 bg-ffffff b-rad-10 o-hid" style="transform: rotate(0deg);" />
          <AuctionGoodsCard v-else :goods="item" />
        </view>
      </template>
      <template v-slot:right="{ rightList }">
        <view v-for="(item, index) in rightList" :key="index" class="mb-20">
          <vh-waterfall-goods-list v-if="item.periods_type" :from="$vhFrom" :item="item" class="w-340 bg-ffffff b-rad-10 o-hid" style="transform: rotate(0deg);" />
          <AuctionGoodsCard v-else :goods="item" />
        </view>
      </template>
    </u-waterfall>
  </view>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    addTime: {
      type: Number,
      default: 200
    }
  },
  model: {
    prop: 'list',
    event: 'input',
  },
  methods: {
    clear () {
      this.$refs.uWaterFallRef.clear()
    },
    remove (id) {
      this.$refs.uWaterFallRef.remove(id)
    },
    modify (id, key, value) {
      this.$refs.uWaterFallRef.modify(id, key, value)
    }
  }
}
</script>

<style lang="scss" scoped>
  .auction-wglist {
    ::v-deep #u-right-column {
      align-items: flex-end;
    }
  }
</style>
