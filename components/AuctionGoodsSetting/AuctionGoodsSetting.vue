<template>
  <view>
    <view class="font-24 text-6 l-h-34">
      <view class="flex-s-c">
        <view class="flex-s-c w-328">
          <text>起拍价</text>
          <text class="ml-46 font-28 text-3 l-h-40">¥{{ goods.price }}</text>
        </view>
        <view class="ml-20 flex-c-c">
          <text>保证金</text>
          <text class="ml-20 font-28 text-3 l-h-40">¥{{ goods.margin }}</text>
          <image v-if="earnestStatus" :src="ossIcon('/auction/ask_red_28.png')" class="ml-06 w-28 h-28" @click="jumpNewFundsDetail" />
        </view>
      </view>
      <view class="flex-s-c mt-20">
        <text class="flex-shrink">加价幅度</text>
        <text class="ml-20 font-28 text-3 l-h-40">¥{{ goods.markup }}</text>
      </view>
      <view v-if="goods.sell_time && goods.closing_auction_time" class="d-flex mt-20">
        <text>竞拍周期</text>
        <text class="ml-20 font-26 text-3 l-h-34">{{ goods.sell_time | date('yyyy.mm.dd hh:MM:ss') }} - {{ goods.closing_auction_time | date('yyyy.mm.dd hh:MM:ss') }}</text>
      </view>
      <view class="flex-s-c mt-20">
        <text>延时周期</text>
        <text class="ml-20 font-28 text-3 l-h-40">5分钟/次</text>
        <image :src="ossIcon('/auction/icon_ask.png')" class="ml-06 w-28 h-28" @click="delayPopupVisible = true" />
      </view>
      <view v-if="isShowNonsupport" class="flex-s-c mt-20 pt-20 font-24 text-6 l-h-34 bt-s-02-eeeeee">
        <view class="flex-s-c w-328">
          <image :src="ossIcon('/auction/tips_28.png')" class="w-28 h-28"></image>
          <text class="ml-06">不支持七天无理由退货</text>
        </view>
        <view class="flex-s-c">
          <image :src="ossIcon('/auction/tips_28.png')" class="w-28 h-28"></image>
          <text class="ml-06">不支持发票</text>
        </view>
      </view>
    </view>

    <AuctionDelayPopup v-model="delayPopupVisible" />
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    goods: {
      type: Object,
      default: () => ({})
    },
    isShowNonsupport: {
      type: Boolean,
      default: false,
    },
    earnestStatus: {
      type: Number,
      default: 0
    },
    earnestOrderNo: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    delayPopupVisible: false
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    jumpNewFundsDetail () {
      if (!this.earnestOrderNo) return
      this.jump.navigateTo(`${this.routeTable.pHAuctionFundsDetailNew}?orderNo=${this.earnestOrderNo}&isFromAuctionGoods=1`)
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
