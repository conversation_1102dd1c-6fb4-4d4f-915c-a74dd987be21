<template>
  <view class="goods-card" @click="handleJump(`${routeTable.pgGoodsDetail}?id=${item.id}`, from)">
    <view class="p-rela">
      <vh-image :src="item.banner_img" :height="434" />
      <image
        v-if="item.special_activity_data && item.special_activity_data.title_map"
        class="p-abso top-0 z-99 w-p100 h-434"
        :src="item.special_activity_data.title_map"
        mode="aspectFill"
      ></image>
    </view>
    <view class="ptb-20-plr-24">
      <view
        style="overflow: hidden"
        @longpress.stop="handleLongpress(item.title, from)"
        @touchend="handleTouched(from)"
      >
        <view style="float: left" class="flex-c-c mr-08 h-42">
          <view
            v-if="item.is_seckill"
            style="background-color: #fde451"
            class="flex-c-c w-72 h-30 font-wei-500 font-24 text-e80404 b-rad-04"
            >秒杀</view
          >
          <view
            v-else-if="currPeriodsTypeObj"
            :style="{ background: currPeriodsTypeObj.bgColor }"
            style="padding-left: 7px; padding-right: 7px; height: 18px"
            class="flex-c-c font-wei-500 font-24 text-ffffff b-rad-06"
            >{{ currPeriodsTypeObj.title }}</view
          >
          <view v-for="(label, index) in item.activity_label" :key="index" class="p-rela flex-c-c ml-10">
            <view style="border-radius: 18px" class="flex-c-c pl-32 h-30 o-hid t-trans-3d-1">
              <image
                style="top: 50%; left: 0; transform: translateY(-50%)"
                class="p-abso w-32 h-p100"
                :src="ossIcon(`/second_hair/s_red_red_32_30.png`)"
              />
              <view class="flex-c-c pl-04 pr-10 h-p100 font-wei-500 font-20 text-e80404">{{ label }}</view>
            </view>
            <!-- <view style="left: -50%; width: 200%; height: 200%; border: 1px solid #e80404; transform: scale(0.5);" class="p-abso"></view> -->
            <view
              style="width: 100%; height: 100%; border: 1px solid #e80404; border-radius: 18px"
              class="p-abso"
            ></view>
          </view>
        </view>
        <view class="font-30 font-wei-500 text-3 l-h-42">{{ item.title }}</view>
      </view>

      <view class="mt-12 font-28 text-9 l-h-40">{{ item.brief }}</view>

      <view class="mt-28 d-flex j-sb a-center">
        <view
          v-if="item.is_hidden_price == 1 || [3, 4].includes(item.onsale_status)"
          class="font-36 font-wei text-e80404 l-h-44"
          >价格保密</view
        >
        <view v-else class="d-flex a-center">
          <text class="font-36 font-wei text-e80404 l-h-44"><text class="font-24">¥</text>{{ item.price }}</text>
          <!-- <text class="ml-16 font-24 text-9 text-dec-l-t l-h-36">¥{{ item.market_price }}</text> -->
        </view>
        <view v-if="item.is_seckill === 1 && item.onsale_status === 1" class="sell-time-box">
          <view class="sell-time">{{ sell_timeFormat(item.sell_time) }}</view>
          <view class="sell-text">即将开抢</view>
        </view>
        <!-- 2022-07-19 秒发列表只显示已售 需求方：杨文科 -->
        <view v-else-if="!item.is_deposit" class="">
          <text v-if="item.periods_type == 1" class="font-24 text-9 l-h-36"
            >已售<text class="text-e80404">{{ item.purchased + item.vest_purchased }}</text></text
          >
          <text v-else class="font-24 text-9 l-h-36"
            >已售<text class="text-e80404">{{ item.purchased + item.vest_purchased }} </text>/限量<text
              class="text-e80404"
              >{{ item.limit_number }}</text
            >{{ item.quota_rule.quota_number == '9999' ? '' : '/限购' }}
            <text class="text-e80404">{{
              item.quota_rule.quota_number == '9999' ? '' : item.quota_rule.quota_number
            }}</text></text
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'
import { mapState } from 'vuex'
/**
 * normal-goods-list 普通商品列表
 * @description 该组件一般用于普通商品列表
 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
 * @property {Object} item 商品列表的每一项
 * @example <vh-normal-goods-list />
 */
export default {
  name: 'vh-normal-goods-list',

  props: {
    newYearTheme: {
      type: Boolean,
      default: false,
    },
    // 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
    from: {
      type: [String, Number],
      default: '',
    },

    // 商品列表每一项
    item: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {}
  },
  mounted() {
    // this.secondConfig()
  },
  methods: {
    // async secondConfig() {
    //   const res = await this.$u.api.secondConfig()
    //   if (res.data.isopen) {
    //     this.isNewYear = true
    //   }
    // },
    sell_timeFormat(time) {
      const date = new Date(new Date(time).getTime())
      let month = date.getMonth() + 1
      let day = date.getDate()
      let hours = date.getHours()
      let minutes = date.getMinutes()
      month = month <= 9 ? '0' + month : month
      day = day <= 9 ? '0' + day : day
      hours = hours <= 9 ? '0' + hours : hours
      minutes = minutes <= 9 ? '0' + minutes : minutes
      return `${month}月${day}日 ${hours}:${minutes}`
    },
  },
  mixins: [longpressCopyMixin],

  computed: {
    //Vuex 辅助state函数
    ...mapState(['routeTable']),
    currPeriodsType({ item }) {
      if (item?.marketingAttribute?.includes('1') || item?.marketing_attribute?.includes('1')) {
        return 101
      }
      return item.periods_type
    },
    currPeriodsTypeObj({ currPeriodsType }) {
      return new Map([
        [0, { title: '闪购', bgColor: '#E80404' }],
        [1, { title: this.newYearTheme ? '年货节' : '现货速发', bgColor: '#FF9127' }],
        [2, { title: '跨境', bgColor: '#734cd2' }],
        [3, { title: '尾货', bgColor: '#FF9127' }],
        [4, { title: '兔头', bgColor: '#FF9127' }],
        [11, { title: '拍卖', bgColor: '#F6B869' }],
        [101, { title: '拼团', bgColor: '#FF9127' }],
      ]).get(currPeriodsType)
    },
  },
}
</script>

<style lang="scss" scoped>
.goods-card {
  font-family: 'PingFang SC', OpenSans, apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial,
    Roboto, 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}
.sell-time-box {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}
.sell-time {
  background-color: #e78800;
  padding: 6rpx 16rpx;
  border-top-left-radius: 4rpx;

  border-bottom-left-radius: 4rpx;
  color: #fff;
}
.sell-text {
  padding: 6rpx 16rpx;
  border-bottom-right-radius: 4rpx;
  border-top-right-radius: 4rpx;
  background-color: #f5cf99;
  color: #912b00;
}
</style>
