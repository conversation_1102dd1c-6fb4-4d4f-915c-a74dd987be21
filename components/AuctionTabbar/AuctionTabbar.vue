<template>
  <view class="d-flex p-fixed bottom-0 w-p100 h-116 bg-ffffff b-sh-00042800-026 z-999" v-safeBeautyBottom="$safeBeautyBottom">
    <view
      v-for="(item, index) in list"
      :key="index"
      :style="item.itemStyle"
      class="d-flex flex-column j-center a-center w-292 h-p100"
      @click="onClick(item)"
    >
      <image :src="ossIcon(`/auction${item.icon}`)" :style="item.iconStyle" class="w-52 h-52" />
      <text :style="item.textStyle" class="mt-06 font-24 text-9" :class="{ 'text-e80404': item.isActive }">{{ item.name }}</text>
    </view>
    <AuctionRNWarnPopup v-model="RNWarnPopupVisible" />
    <AuctionPAWarnPopup v-model="PAWarnPopupVisible" />
    <AuctionFunHiddenPopup v-model="funHiddenPopupVisible" />
  </view>
</template>

<script>
import { MAuctionRNType } from '@/common/js/utils/mapperModel'
import { mapState } from 'vuex'

export default {
  data: () => ({
    list: [],
    RNWarnPopupVisible: false,
    PAWarnPopupVisible: false,
    funHiddenPopupVisible: false,
  }),
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    async onClick (item) {
      const { isActive, path, isNavigate, isCheck, isCheckLogin } = item
      if (isActive) return
      if (isCheckLogin) {
        const isLogin = await this.login.isLoginV3(this.$vhFrom)
        if (!isLogin) return
      }
      if (isCheck) {
        // this.funHiddenPopupVisible = true
        // if (true) return
        const [userInfoRes, payeeAccountRes] = await Promise.all([
          this.$u.api.getAuctionUserInfo(),
          this.$u.api.searchAuctionPayeeAccounts(),
        ])
        const { ac_type = 0 } = userInfoRes?.data?.info || {}
        const isRealName = MAuctionRNType.NotRN !== ac_type
        if (!isRealName) {
          this.RNWarnPopupVisible = true
          return
        }
        // const list = payeeAccountRes?.data?.list || []
        // if (!list.length) {
        //   this.PAWarnPopupVisible = true
        //   return
        // }
      }
      if (isNavigate) {
        this.jump.navigateTo(path)
      } else {
        this.jump.redirectTo(path)
      }
    }
  },
  created () {
    const pageLength = this.pages.getPageLength()
    const currentPage = getCurrentPages()[pageLength - 1]
    const path = currentPage.$page.path
    const { pHAuctionIndex, pHAuctionGoodsCreate, pHAuctionMine } = this.routeTable
    this.list = [
      {
        name: '拍品',
        icon: pHAuctionIndex === path ? '/auction_index_h.png' : '/auction_index.png',
        isActive: pHAuctionIndex === path,
        path: pHAuctionIndex
      },
      {
        name: '发布',
        icon: '/publish_122.png',
        path: pHAuctionGoodsCreate,
        itemStyle: { width: '166rpx', justifyContent: 'flex-start', transform: 'translateY(-46rpx)' },
        iconStyle: { width: '122rpx', height: '122rpx', flexShrink: 0 },
        textStyle: { marginTop: 0, color: '#333' },
        isNavigate: true,
        isCheck: true,
        isCheckLogin: true,
      },
      {
        name: '我的',
        icon: pHAuctionMine === path ? '/auction_mine_h.png' : '/auction_mine.png',
        isActive: pHAuctionMine === path,
        path: pHAuctionMine,
        isCheckLogin: true,
      }
    ]
  }
}
</script>