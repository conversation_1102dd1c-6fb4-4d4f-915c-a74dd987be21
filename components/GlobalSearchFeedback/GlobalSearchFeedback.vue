<template>
  <view v-if="value" class="p-fixed bottom-80 ptb-00-plr-24 w-p100">
    <view class="p-rela flex-c-c h-196 bg-000-000-000-055 b-rad-20">
      <image :src="ossIcon('/global_search/close_54.png')" class="p-abso top-20 right-24 w-54 h-54" @click="add()" />
      <view class="w-p100">
        <view class="ptb-00-plr-48 font-wei-500 font-26 text-ffffff l-h-36">您对当前的搜索结果满意吗？</view>
        <view class="flex-c-c mt-32">
          <button :class="btnClazz" @click="add(MSearchFeedbackType.Satisfaction)">满意</button>
          <button class="ml-134" :class="btnClazz" @click="add(MSearchFeedbackType.Dissatisfaction)">不满意</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { MSearchFeedbackType } from '@/common/js/utils/mapperModel'

export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    keywords: {
      type: String,
      default: ''
    }
  },
  data: () => ({
    MSearchFeedbackType,
    btnClazz: 'vh-btn flex-c-c w-184 h-64 font-wei-500 font-28 text-ffffff bg-255-255-255-040 b-s-01-ffffff b-rad-34'
  }),
  methods: {
    add (type = MSearchFeedbackType.Unevaluated) {
      const params = {
        keywords: this.keywords,
        satisfaction: type,
        user_unique_code: uni.getStorageSync('uniqueId')
      }
      this.$u.api.addSeachFeedback(params)
      if (type !== MSearchFeedbackType.Unevaluated) {
        this.feedback.toast({ title: '谢谢您的反馈' })
      }
      this.$emit('input', false)
    }
  },
}
</script>