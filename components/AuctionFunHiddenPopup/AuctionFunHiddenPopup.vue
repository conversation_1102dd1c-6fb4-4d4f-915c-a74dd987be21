<template>
  <u-popup :value="value" mode="center" width="558rpx" height="698rpx" border-radius="20" @input="onInput" class="bg-transparent-popup">
    <view class="d-flex" @click="onInput(false)">
      <image :src="ossIcon('/auction/fun_hidden_558_698_2.png')" class="w-558 h-698" />
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
