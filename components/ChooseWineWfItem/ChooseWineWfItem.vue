<template>
  <view style="background: linear-gradient(339deg, #CEDFFF 0%, #CDD3F3 100%);" class="b-rad-10" @click="handleJump(`${routeTable.pgGoodsDetail}?id=${item.id}`, $vhFrom)">
    <view class="p-rela p-04 pb-0">
      <!-- transform: rotate(0deg); 兼容ios圆角无效 -->
      <view class="bg-ffffff b-rad-10 o-hid" style="transform: rotate(0deg);">
        <view class="p-rela">
          <vh-image :src="item.banner_img" :height="210" />
        </view>
        <view class="mt-16 ptb-00-plr-08">
          <view class="font-wei-500 font-26 text-3 l-h-36" @longpress.stop="handleLongpress(item.title, $vhFrom)" @touchend="handleTouched($vhFrom)">{{ item.title }}</view>
          <view v-if="!item.is_deposit" class="flex-e-c h-58 ptb-00-plr-08 font-18 text-9 l-h-26">
            <text>已售<text class="text-e80404">{{ item.purchased + item.vest_purchased }}</text></text>
            <text>/限量<text class="text-e80404">{{ item.limit_number }}</text></text>
            <text v-if="item.quota_rule && item.quota_rule.quota_number != '9999'">/限购<text class="text-e80404">{{ item.quota_rule.quota_number }}</text></text>
          </view>
        </view>
      </view>
      <view style="bottom: -2rpx;" class="p-abso left-0 flex-c-c">
        <image :src="ossIcon('/index/choose_wine_flag_72_48.png')" class="w-72 h-48"></image>
      </view>
    </view>
    <view class="flex-sb-c h-90 ptb-00-plr-12">
      <view>
        <view v-if="item.is_hidden_price === 1 || [3, 4].includes(item.onsale_status)" class="font-wei-500 font-28 text-e80404 l-h-44">价格保密</view>
        <view v-else>
          <view class="font-wei-500 font-28 text-e80404 l-h-44"><text class="font-18">¥</text>{{ item.price }}</view>
          <view style="margin-top: -14rpx;" class="font-18 text-ffffff l-h-36">¥<text class="text-dec-l-t">{{ item.market_price }}</text></view>
        </view>
      </view>
      <view class="flex-c-c">
        <image :src="ossIcon('/index/choose_wine_btn_154_40.png')" class="w-154 h-40" />
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'

export default {
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [longpressCopyMixin],
  computed: {
    ...mapState(['routeTable']),
  },
}
</script>

<style lang="scss" scoped>
</style>
