<template>
	<view class="ptb-32-plr-24">
		<!-- 订单编号 -->
		<view class="flex-sb-c">
			<text class="font-32 font-wei text-6">订单编号：</text>
			<view class="" @click="copy.copyText(afterSaleInfo.order_no)">
				<text class="font-24 text-6">{{ afterSaleInfo.order_no }}</text>
				<text class="bg-eeeeee b-rad-18 ml-10 ptb-02-plr-10 font-18 text-3">复制</text>
			</view>
		</view>
		
		<!-- 退款金额 -->
		<view class="flex-sb-c mt-20">
			<text class="font-32 font-wei text-6">退款金额</text>
			<text class="font-32 font-wei text-e80404"><text class="font-22">¥</text>{{ afterSaleInfo.refund_money }}</text>
		</view>
		
		<!-- 快递编号： -->
		<view v-if="afterSaleInfo.express_number" class="flex-sb-c mt-20">
			<text class="font-32 font-wei text-6">快递编号：</text>
			<view class="" @click="copy.copyText(afterSaleInfo.express_number)">
				<text class="font-24 text-6">{{ afterSaleInfo.express_number }}</text>
				<text class="bg-eeeeee b-rad-18 ml-10 ptb-02-plr-10 font-18 text-3">复制</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name: "ActionAfterSaleOrderDetail",
		
		props: {
			// 类型 0 = 买家、1 = 卖家 
			type: {
				type: [Number, String],
				default: 0
			},
			
			// 售后信息
			afterSaleInfo : {
				type: Object,
				default: function() {
					return {};
				}
			},
		}
	}
</script>

<style>
</style>