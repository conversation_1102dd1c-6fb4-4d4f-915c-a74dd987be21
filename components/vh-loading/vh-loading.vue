<template>
	<view class="d-flex flex-column j-center a-center" :style="[loadingContainerStyle]">
		<u-loading :mode="mode" :size="size" :color="loadingColor"></u-loading>
		<view v-if="showText" class="mt-10 font-24" :style="[loadingTextStyle]">{{loadingText}}</view>
	</view>
</template>

<script>
	/**
	 * loading 加载层
	 * @description 该组件一般用于首页的默认加载占位。
	 * @property {String} width 加载框所占宽度
	 * @property {String} height 加载框所占高度 
	 * @property {String} mode 加载图标模式 circle = 圆圈 flower = 花朵
	 * @property {Number} size 加载图标大小 
	 * @property {String} loading-color 加载图标以及文字颜色 
	 * @property {Boolean} show-text 是否显示加载文本 
	 * @property {String} loading-text 加载文本 
	 * @property {String, Number} text-size 加载文本字体大小
	 * @example <vh-loading></vh-loading>
	 */
	export default{
		name: 'vh-loading',
		props: {
			// 加载框所占宽度
			width: {
				type: String,
				default: '100%'
			},
			// 加载框所占高度
			height: {
				type: String,
				default: '100vh'
			},
			// 加载图标模式
			mode: {
				type: String,
				default: 'circle'
			},
			// 加载图标大小
			size: {
				type: Number,
				default: 50
			},
			// 加载图标以及文字颜色
			loadingColor: {
				type: String,
				default: '#E80404'
			},
			showText: {
				type: Boolean,
				default: false
			},
			// 加载文本
			loadingText: {
				type: String,
				default: '加载中...'
			},
			// 文字大小
			textSize: {
				type: [String, Number],
				default: 24
			}
		},
		computed: {
			// 加载容器样式
			loadingContainerStyle(){
				return {
					width: this.width,
					height: this.height
				}
			},
			// 加载文本样式
			loadingTextStyle(){
				return{
					color: this.loadingColor,
					fontSize: this.textSize
				}
			}
		}
	}
</script>

<style scoped></style>
