<template>
  <u-popup :value="value" mode="bottom" width="100%" height="684rpx" border-radius="40" @input="onInput">
    <view class="flex-c-c h-86 bg-fdf9e6">
      <text class="font-28 text-e80404 l-h-40">*</text>
      <text class="font-24 text-af7e1f l-h-34">卖家报价会影响起拍价、加价幅度、请合理填写。</text>
    </view>
    <view class="ptb-00-plr-48">
      <view class="flex-sb-e pb-12 h-84 bb-s-02-eeeeee">
        <text class="font-28 text-3 l-h-40">卖家报价</text>
        <input :value="priceParams.quoteInput" @input="onPriceInput($event, 'quoteInput')" placeholder="¥请输入价格" placeholder-style="font-size: 24rpx; color: #333;" class="w-410 font-28 text-3 l-h-34 text-right" />
      </view>
      <view class="flex-sb-e pb-12 h-84 bb-s-02-eeeeee">
        <text class="font-28 text-3 l-h-40">起拍价</text>
        <input :value="priceParams.priceInput" @input="onPriceInput($event, 'priceInput')" placeholder="¥请输入价格，不超过卖家报价50%" placeholder-style="font-size: 24rpx; color: #999;" class="w-410 font-28 text-3 l-h-34 text-right" />
      </view>
      <view class="flex-sb-e pb-12 h-84 bb-s-02-eeeeee">
        <text class="font-28 text-3 l-h-40">加价幅度</text>
        <input :value="priceParams.markupInput" @input="onPriceInput($event, 'markupInput')" placeholder="¥请输入价格，不超过卖家报价5%" placeholder-style="font-size: 24rpx; color: #999;" class="w-410 font-28 text-3 l-h-34 text-right" />
      </view>
      <view class="flex-sb-e pb-12 h-84 bb-s-02-eeeeee">
        <text class="font-28 text-3 l-h-40">委托保证金</text>
        <text v-if="priceParams.earnestInput" class="font-wei-500 font-28 text-3 l-h-34"><text class="font-24">¥</text>{{ ' ' }}{{ priceParams.earnestInput }}</text>
        <text v-else class="font-24 text-3 l-h-34">委托保证金为卖家报价的10%</text>
      </view>
      <view class="flex-sb-c mt-50">
        <text class="font-wei-500 font-28 text-3 l-h-40">平台服务费</text>
        <text class="font-24 text-af7e1f l-h-34">拍品成交价3%</text>
      </view>
      <view class="flex-c-c">
        <button class="vh-btn flex-c-c mt-60 w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="priceConfirmBtnDisabled ? 'bg-fce4e3' : 'bg-e80404'" @click="onPriceConfirm">确认</button>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { nzPricePattern } from '@/common/js/utils/pattern'
import computedNumber from '@/common/js/utils/computedNumber'
import getJoinAuctionEarnest from '@/common/js/utils/getJoinAuctionEarnest'

export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    priceParams: {
      type: Object,
      default: () => ({})
    },
  },
  computed: {
    priceConfirmBtnDisabled ({ priceParams }) {
      const { quoteInput, priceInput, markupInput } = priceParams
      return !(quoteInput && priceInput && markupInput)
    },
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onPriceInput (e, keyValue) {
      const value = e.detail.value
      this.priceParams[keyValue] = value
      if (keyValue === 'quoteInput' && nzPricePattern.test(value)) {
        this.priceParams.earnestInput = computedNumber(+value, '*', 0.1).result
      }
    },
    onPriceConfirm () {
      if (this.priceConfirmBtnDisabled) return
      const { quoteInput, priceInput, markupInput } = this.priceParams
      if (!nzPricePattern.test(quoteInput)) {
        this.feedback.toast({ title: '请输入正确的卖家报价' })
        return
      }
      if (!nzPricePattern.test(priceInput)) {
        this.feedback.toast({ title: '请输入正确的起拍价' })
        return
      } else if (+priceInput > computedNumber(+quoteInput, '*', 0.5).result) {
        this.feedback.toast({ title: '起拍价不超过卖家报价的50%' })
        return
      }
      if (!nzPricePattern.test(markupInput)) {
        this.feedback.toast({ title: '请输入正确的加价幅度' })
        return
      } else if (+markupInput > computedNumber(+quoteInput, '*', 0.05).result) {
        this.feedback.toast({ title: '加价幅度不超过卖家报价的5%' })
        return
      }
      const keyValueList = ['quote', 'price', 'markup', 'earnest', 'serviceCharge']
      keyValueList.forEach(keyValue => {
        this.priceParams[keyValue] = this.priceParams[`${keyValue}Input`]
      })
      this.priceParams.joinAuctionEarnest = getJoinAuctionEarnest(this.priceParams.quote)
      this.onInput(false)
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
