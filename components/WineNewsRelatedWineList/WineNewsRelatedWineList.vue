<template>
	<view v-if="list.length" class="pb-22">
		<view class="pl-40 flex-s-c">
			<image class="w-50 h-50 b-rad-p50" :src="ossIcon(`/wine_smell_detail/link_red.png`)" mode="widthFix" />
			<view class="ml-20 font-28 text-6">关联酒款</view>
		</view>
		<view class="p-rela w-p100 flex-s-c o-scr-x">
			<view class="h-162 flex-c-c mt-22 mr-last-nth-child1-40 ml-nth-child1-40">
				<view class="w-594 h-142 bg-ffffff d-flex a-center b-sh-00001002-007 b-rad-10 ml-20 p-20" 
				v-for="(item, index) in list" :key="index" @click="handleJump(`${$routeTable.pgGoodsDetail}?id=${item.id}`, $vhFrom)">
					 <vh-image :src="item.banner_img" :loading-type="2" :width="160" :height="102" :borderRadius="6" />
					 <view class="flex-1 ml-16">
						 <text class="font-24 text-3 text-hidden-3">{{ item.title }}</text>
					 </view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'
	export default {
		props: {
			list: {
				type: Array,
				default: () => []
			}
		},
		mixins: [longpressCopyMixin],
	}
</script>
