<template>
	<u-mask :show="show" :zoom="false" @click="click">
		<view class="h-p100 flex-c-c">
			<view class="p-rela z-01 w-546 h-466 bg-ffffff flex-s-c flex-column b-rad-20">
				<image class="w-290 h-214 mt-n-88" :src="ossIcon(`/comm/again_order_bell.png`)" mode="widthFix" />
				<view class="mt-44 font-32 font-wei text-3">温馨提示</view>
				<view class="mt-12 font-28 text-6 text-3 l-h-48">亲亲，这波Sale已经结束啦～</view>
				<view class="w-p100 flex-sb-c mt-60 ptb-00-plr-48">
					<u-button 
						shape="circle" 
						:hair-line="false" 
						:ripple="true" 
						ripple-bg-color="#FFF"
						:custom-style="{width:'202rpx', backgroundColor: '#efefef',fontSize:'28rpx', color:'#333', border:'none'}" 
						@click="reminder">
						上架提醒
					</u-button>
					
					<u-button 
						shape="circle" 
						:hair-line="false" 
						:ripple="true" 
						ripple-bg-color="#FFF"
						:custom-style="{width:'202rpx', backgroundColor: '#e80404',fontSize:'28rpx', color:'#fff', border:'none'}" 
						@click="nowWantTo">
						 现在想要
					</u-button>
				</view>
			</view>
		</view>
    </u-mask>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		props: {
			// 来源 0 = 订单列表、1 = 商品详情
			source: {
				type: [Number, String],
				default: 0
			},
			// 是否显示
			show: {
				type: Boolean,
				default: false
			},
			// 客服信息
			customerInfo: {
				type: Object,
				default: () => ({})
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},
		
		methods: {
			click() {
				this.$emit('click');
			},
			
			// 上架提醒
			reminder() {
				this.$emit('reminder');
			},
			
			// 现在想要
			nowWantTo() {
				console.log('-sasasa')
				if( this.$app ) {
					if(this.$vhFrom == 'next'){
						this.$emit('click');
					} else{
						let {id, periods_type, title, brief, price, banner_img} = this.customerInfo
					let obj = {
							title,
							des:brief,
							price,
							cover:banner_img[0],
							autoOnceMsg: '该商品显示售卖结束，请问还有货么？',
							url:`${this.routeTable.pgGoodsDetail}?id=${id}`
						}
					 wineYunJsBridge.openAppPage({
					 	client_path: { "ios_path":"ShopDetailkefu", "android_path":"shangpin.kefu"}, 
					 	ad_path_param: [{ "ios_key":"info", "ios_val": JSON.stringify(obj), "android_key":"info", "android_val": JSON.stringify(obj) }]
					 });
					}
					
				}else {
					this.feedback.toast({ title: '请前往APP或小程序查看此功能~'})
				}
			}
		}
	}
</script>

<style>
</style>