<template>
	<view class="content">
		<u-popup 
			mode="bottom" 
			:popup="false" 
			v-model="value" 
			length="auto" 
			@close="close" 
			:border-radius="20"
		>
			<view class="param-con">
				<!-- 标题 -->
				<view class="title">{{ title }}</view>
				
				<!-- 选择项 -->
				<scroll-view class="params" scroll-y="true">
					<view class="params-list">
						<view class="params-list-item" v-for="( item, index ) in paramsList" :key="item.id">
							<view class="params-list-item-title" :class="{ 'params-list-item-top-40': index > 0 }">{{ item.name }}</view>
							<view class="inn-params-list">
								<view class="inn-params-list-item" v-for="( item1, index1 ) in item.list" :key="item1.asid"
								@click="selectItem(item1)">
									<vh-check 
									:checked-img="`${osip}/comm/rect_sel.png`"
									:un-checked-img="`${osip}/comm/rect_usel.png`"
									:checked="selectList.findIndex( v => v.asid === item1.asid ) > -1" 
									@click="selectItem(item1)"
									/>
									<view class="inn-params-list-item-title">{{ item1.cate_name }}</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
				
				<!-- 底部按钮 -->
				<view class="btn-con">
					<u-button 
					shape="circle" 
					:hair-line="false" 
					:ripple="true" 
					ripple-bg-color="#FFF"
					:custom-style="{width:'308rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#999', backgroundColor: '#EEEEEE', border:'none'}"
					@click="reset">重置</u-button>
					
					<u-button 
					:disabled="!canConfirm"
					shape="circle" 
					:hair-line="false" 
					:ripple="true" 
					ripple-bg-color="#FFF" 
					:custom-style="{width:'308rpx', height:'64rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: !canConfirm ? '#FCE4E3' : '#E80404' , border:'none'}"
					@click="confirm">确定</u-button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import { OSIP } from '@/common/js/fun/constant.js'
	/**
	 * wine-comment-popup-multi-params 酒评多选参数弹框
	 * @description 该组件多选参数弹出层展示，方便用户风格统一，减少工作量（ 通用组件 ）
	 * @property {Boolean} value 通过双向绑定控制组件的弹出与收起（默认false）
	 * @property {String} title 标题（默认闻香）
	 * @property {String Number} type 类型（默认1）1 = 闻香、2 = 配餐
	 * @property {Array} params-list 参数列表，默认（[]）
	 * @property {String} defaultIdsStr 默认选中的id字符串（默认''）
	 * @example <vh-wine-comment-popup-multi-params />
	 */
	export default {
		name: 'vh-wine-comment-popup-multi-params',
		
		props: {
			// 通过双向绑定控制组件的弹出与收起
			value: {
				type: Boolean,
				default: false
			},
			
			// 类型
			type: {
				type: [String, Number],
				default: 1
			},
			
			// 标题
			title: {
				type: String,
				default: '香气特征'
			},
			
			// 参数列表
			paramsList: {
				type: Array,
				default() {
					return [];
				}
			},
			
			// 默认选中的id字符串
			defaultIdsStr: {
				type: String,
				default: ''
			}
		},
		
		data() {
			return {
				osip: OSIP, //oss静态图片前缀
				selectList: [], //选中的列表
			}
		},
		
		computed: {
			// 是否可以确认数据
			canConfirm() {
				return this.selectList.length
			}
		},
		
		mounted() {
			console.log('--------------------0000')
			this.getSelectList()
		},
		
		methods: {
			// 关闭
			close() {
				this.$emit('input', false);
			},
			
			// 获取选中列表
			getSelectList() {
				console.log( this.defaultIdsStr )
				if( this.defaultIdsStr ) {
					let idsList = this.defaultIdsStr.split(',')
					idsList = idsList.map( item => Number(item)) 
					let innList = this.paramsList.map( item => item.list ).flat()
					let selectList = []
					innList.forEach( v => { if( idsList.includes( v.asid )) { selectList.push(v) } })
					console.log( this.defaultIdsStr )
					console.log( this.paramsList )
					console.log( this.paramsList.flat() )
					console.log( innList )
					this.selectList = selectList
					console.log( this.selectList )
				}
			},
			
			// 选中某项 item = 列表某一项
			selectItem( item ) {
				let index = this.selectList.findIndex( v => v.asid === item.asid )
				if (index === -1) {
					if (this.selectList.filter(({ parent_id }) => item.parent_id === parent_id).length < 3) {
						this.selectList.push( item )
					} else {
						this.feedback.toast({ title: '每个单项最多选择三项' })
					}
				} else {
					this.selectList.splice( index, 1 )
				}
				
				// 选中的数组对象
				console.log( item )
				console.log( this.selectList )
			},
			
			// 重置
			reset() {
				this.selectList = []
			},
			
			// 确定
			confirm() {
				console.log('----------------我是确认')
				if( !this.selectList.length ) return
				let nameFormat = ''
				switch( this.type ) {
					case 1:
					nameFormat = ' '
					break
					case 2:
					nameFormat = '、'
					break
				}
				let ids = this.selectList.map( item => item.asid ).join(',')
				let names = this.selectList.map( item => item.cate_name ).join(nameFormat)
				let data = { ids, names }
				this.$emit( 'confirm', data )
				this.close()
			},
		}
	}
</script>

<style scoped lang="scss">
	.param-con {
		position: relative;
	}
	
	.title {
		// position: fixed;
		// z-index: 100;
		// top: 0;
		// width: 100%;
		// background-color: #FFF;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 32rpx 0;
		font-size: 32rpx;
		font-weight: bold;
	}
	
	.params {
		max-height: 896rpx;
		padding: 0 0 124rpx 0;
		&-list {
			padding: 0 40rpx;
			& > view:nth-last-child(1) {
				margin-bottom: 0; 
			}
			&-item {
				&-title {
					font-size: 30rpx;
					font-weight: bold;
					color: #333;
				}
				
				&-top-40 {
					margin-top: 40rpx;
				}
			}
		}
	}
	
	.inn-params-list {
		display: flex;
		flex-wrap: wrap;
		&-item {
			width: 33.33%;
			display: flex;
			align-items: center;
			margin-top: 24rpx;
			// background-color: rgba(0,0,0,0.22);
			&-title {
				width: 120rpx;
				display: flex;
				align-items: center;
				margin-left: 20rpx;
				font-size: 28rpx;
				color: #333;
				line-height: 40rpx;
			}
		}
	}
    
	.btn-con {
		position: fixed;
		bottom: 0;
		z-index: 999;
		background-color: #FFF;
		width: 100%;
		height: 104rpx;
		display: flex;
		justify-content: space-around;
		align-items: center;
		box-shadow: 0 2rpx 12rpx 0 rgba(0,0,0,0.22);
		padding: 0 32rpx;
	}
</style>