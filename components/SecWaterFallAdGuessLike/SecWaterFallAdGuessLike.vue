<template>
	<view class="p-rela h-496">
		<image class="p-abso w-p100 h-p100" :src="ossIcon(`/second_hair/s_ad_like_bg_344_496.png`)" />
		<view class="p-rela z-02 d-flex flex-column h-p100 ptb-00-plr-16">
			<view class="d-flex ml-n-02 pt-18">
				<image :src="ossIcon(`/second_hair/s_ad_like_icon_34_42.png`)" class="w-34 h-42"></image>
				<view class="ml-06 font-wei-600 font-30 text-3 l-h-40">你可能喜欢：</view>
			</view>
			<view class="flex-1 flex-c-c">
				<view class="flex-sb-c flex-wrap">
					<view class="w-148 flex-c-c-c" :class="index > 1 ? 'mt-20' : ''" v-for="(item, index) in item.list" :key="item.id" @click="jumpGoodsDetail(item)">
						<view class="w-p100 bg-ffffff b-rad-10 p-06">
							<vh-image :src="item.product_img" :loadingType="1" :width="136" :height="136" />
						</view>
						<view class="d-flex a-start mt-12">
							<view class="h-30 font-wei-500 font-28 text-e80404 l-h-30"><text class="font-18">¥</text>{{ item.price }}</view>
							<view class="p-rela top-06 ml-06 font-18 text-9 l-h-26 text-dec-l-t">¥{{ item.market_price }}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import secondWfitemMixin from '@/common/js/mixins/secondWfitemMixin'
	export default {
		mixins: [secondWfitemMixin],
		methods: {
			jumpGoodsDetail (item) {
				this.jump.appAndMiniJump(1, `${this.routeTable.pgGoodsDetail}?id=${item.id}`, this.$vhFrom)
			}
		},
	}
</script>
