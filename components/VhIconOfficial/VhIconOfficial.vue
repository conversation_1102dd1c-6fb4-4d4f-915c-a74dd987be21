<template>
	<view class="d-inline-block text-center" :style="[containerStyle]">
		<text class="p-rela" :style="[style]">官方</text>
	</view>
</template>

<script>
	export default{
		props: {
			bgColor: {
				type: String,
				default: '#E80404'
			},
			borderRadius: {
				type: [String, Number],
				default: 4
			},
			border: {
				type: String,
				default: 'none'
			},
			marginLeft: {
				type: [String, Number],
				default: 10
			},
			padding: {
				type: String,
				default: '0 6rpx'
			},
			fontSize: {
				type: [String, Number],
				default: 22
			},
			fontBold: {
				type: Boolean,
				default: false
			},
			color: {
				type: String,
				default: '#FFFFFF'
			},
			lineHeight: {
				type: [String, Number],
				default: 28
			},
		},
		computed: {
			containerStyle() {
				return {
					background: this.bgColor,
					borderRadius: this.borderRadius + 'rpx',
					border: this.border,
					marginLeft: this.marginLeft + 'rpx',
					padding: this.padding,
					lineHeight: this.lineHeight + 'rpx',
				}
			},
			style() {
				return {
					bottom: '1rpx',
					fontSize: this.fontSize + 'rpx',
					fontWeight: this.fontBold ? 'bold' : 'normal',
					color: this.color,
					whiteSpace: 'nowrap'
				};
			}
		}
	}
</script>
