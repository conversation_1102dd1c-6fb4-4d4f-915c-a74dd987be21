<template>
	<view class="">
		<!-- 订单状态 -->
		<view class="d-flex j-sb a-center pt-32 pb-32 bb-s-01-eeeeee">
			<text class="font-24 text-3">{{ item.created_time }}</text>
			<view class="d-flex a-center">
				<!-- 买家 -->
				<view v-if="type === 0" class="">
					<text v-if="item.status === 0" class="font-28 text-e80404">待卖家处理</text>
					<text v-if="item.status === 1" class="font-28 text-e80404">卖家同意退货</text>
					<text v-if="item.status === 2" class="font-28 text-e80404">待卖家收货</text>
					<text v-if="item.status === 3" class="font-28 text-3">退款成功</text>
					<text v-if="item.status === 5" class="font-28 text-3">卖家拒绝退款</text>
					<text v-if="item.status === 6" class="font-28 text-3">卖家拒绝收货</text>
				</view>
				<!-- 卖家 -->
				<view v-if="type === 1" class="">
					<text v-if="item.status === 0" class="font-28 text-e80404">待处理</text>
					<text v-if="item.status === 1" class="font-28 text-e80404">等待买家寄回</text>
					<text v-if="item.status === 2" class="font-28 text-e80404">等待收货</text>
					<text v-if="item.status === 3" class="font-28 text-3">退款成功</text>
					<text v-if="item.status === 5" class="font-28 text-3">拒绝退款</text>
					<text v-if="item.status === 6" class="font-28 text-3">拒绝收货</text>
				</view>
				<view class="ml-20">
					<u-icon name="arrow-right" :size="24" color="#999" />
				</view>
			</view>
		</view> 
		
		<!-- 订单商品信息 -->
		<view class="bb-s-01-eeeeee pt-08 pb-28">
			  <view class="d-flex mt-20">
				<vh-image :loading-type="2" :src="item.goods_img" :width="160" :height="160" :border-radius="10" />
				<view class="flex-1 d-flex flex-column j-sb ml-12">
					<view class="">
						<view class="font-24 text-0 text-hidden-1">{{ item.goods_name }}</view>
						<view class="flex-sb-c mt-32">
							<AuctionOrderTypeName :auctionType="item.auction_type"/>
							<text class="font-24 text-9">x{{ item.order_qty }}</text>
						</view>
					</view>
					<view class="flex-e-c mt-02">
						<view class="text-3">
							<text class="font-18">成交价：</text>
							<text class="font-22 font-wei">¥</text>
							<text class="font-32 font-wei">{{ item.payment_amount }}</text>
						</view>
					</view>
				</view>
			  </view>
		</view>
	</view>
</template>

<script>
	export default{
		name: "AuctionAfterSaleListItem",
		
		props: {
			// 类型 0 = 买家、1 = 卖家 
			type: {
				type: [Number, String],
				default: 0
			},
			
			// 商品信息
			item : {
				type: Object,
				default: function() {
					return {};
				}
			},
		}
	}
</script>

<style>
</style>