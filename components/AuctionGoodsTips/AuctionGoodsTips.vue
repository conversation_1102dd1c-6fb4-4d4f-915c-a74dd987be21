<template>
  <view>
    <view class="font-wei-500 font-32 text-3 l-h-44 text-center">温馨提示</view>
    <view class="mt-20">
      <view v-for="(item, index) in tips" :key="index" :class="index ? 'mt-28' : ''">
        <view class="font-wei-500 font-28 text-3 l-h-48">{{ item.title }}</view>
        <view v-for="(text, textIndex) in item.list" :key="textIndex" class="d-flex mt-10">
          <view class="pt-12 w-30">
            <view class="w-10 h-10 bg-ff9127 b-rad-p50"></view>
          </view>
          <view class="flex-1 font-24 text-6 l-h-34">{{ text }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    goods: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    tips () {
      return [
        {
          title: '关于拍品',
          list: [
            '我司就标的的瑕疵、品质、价值及真伪不承担担保责任。所有拍品均由委托人提供，拍卖标的均以拍卖时的状态出售，竟买人可在预展期间与拍卖人 (客服) 联系查看标的情况。',
            `所有拍品均经严格审核，但受保存条件、批次等因素影响，${this.goods.product_type === 1 ? '老酒均存在一定程度的品相瑕疵(如:附件缺失、破损、污渍、水位较低等)，': ''}瑕疵描述以图片为主，文字仅作参考。`,
            '因标的资料及版本众多，平台会尽力进行准确描述，但不对标的物的任何口头或书面描述，或当中任何错误或缺失负责，所有标的信息以展示图片为主，文字仅作参考。',
            '竞买人既参与竞拍，则视为对拍卖标的的信息内容、瑕疵、品质价值、真伪已有充分了解、认可、接受，并对自己的竞买行为负责，一经拍出，概不退换!',
          ]
        },
        {
          title: '关于验收',
          list: this.goods.product_type === 1 ? [
            '由于老酒具有一定的特殊性，运输过程中的轻微漏液、脱胶、封口破裂等属正常情况。收货请当面验收，如外在包装、标签、税条等轻微破损，且漏液在5%内，不予退赔。',
            '如有严重破损可拒签，因运输不当造成的破损且漏液超过5%，或酒瓶有较明显破损的，可予以退赔，签收即代表确认无误，如未验货而直接签收的，损失由客户自行承担，后续概不负责。',
          ] : [
            '因运输不当造成的损坏，或者外观破损严重的可拒签，签收即代表确认无误如未验货而直接签收的，损失由客户自行承担，后续概不负责。',
          ]
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
