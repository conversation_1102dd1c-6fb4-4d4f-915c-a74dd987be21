<template>
	<view class="">
		<view v-if="list.length" class="p-24">
			<view class="bg-ffffff mb-24 ptb-28-plr-24 b-rad-16" v-for="( item, index ) in list" :key="index">
				<view class="flex-sb-c bb-s-01-eeeeee pb-30">
					<text class="font-28 text-3">{{ MInvoiceHistoryTypeInfo[item.genre] }}</text>
					<text class="font-28" :class="item.$statusTextClazz">{{ item.$statusText }}</text>
				</view>
				
				<view class="d-flex mt-28">
					<vh-image :src="item.order.banner_img" :loadingType="2" :width="246" :height="152" :borderRadius="6" />
					<view class="flex-1 flex-sb-n-c ml-12">
						<view class="font-24 l-h-34 text-hidden-2">{{ item.order.title }}</view>
						<text class="font-32 text-3"><text class="font-22">¥</text>{{ item.order.payment_amount }}</text>
					</view>
				</view>
				
				<view class="bg-f7f7f7 flex-sb-c b-rad-08 mt-28 ptb-24-plr-20">
					<text class="font-24 text-6">{{ item.receipt_name }}</text>
					<text class="ml-24 font-32 text-3 w-s-now"><text class="font-22">¥</text>{{ item.invoice_price }}</text>
				</view>
				
				<view class="flex-sb-c mt-34">
					<text class="font-24 text-3">一张发票，含{{ item.count }}个订单</text>
					<view class="">
						<u-button
							shape="circle" 
							:hair-line="false" 
							:ripple="true" 
							ripple-bg-color="#FFF"
							:custom-style="{width:'152rpx', height:'52rpx', fontSize:'26rpx', color:'#666', border:'1rpx solid #666'}" 
							@click="jump.navigateTo(`${$routeTable.pBOrderInvoiceHistotyDetail}?invoice_code=${item.invoice_code}`)">查看详情
						</u-button>
					</view>
				</view>
			</view>
			<u-loadmore :status="reachBottomLoadStatus" />
		</view>
		<vh-empty v-else :paddingTop="300" :imageSrc="ossIcon(`/empty/emp_order.png`)" text="暂无开票历史信息" />
	</view>
</template>

<script>
	import { MInvoiceHistoryTypeInfo } from '@/common/js/mapper/invoiceHistory/mapper'
	export default {
		props: {
			list: {
				type: Array,
				default: () => []
			},
			reachBottomLoadStatus: {
				type: String,
				default: ''
			}
		},
		data: () => ({
			MInvoiceHistoryTypeInfo
		})
	}
</script>