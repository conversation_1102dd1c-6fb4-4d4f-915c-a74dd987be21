<template>
  <view class="flex-c-c-c pt-32 pb-24">
    <scroll-view class="w-680" scroll-x @scroll="scroll">
      <view class="d-flex w-p100 ml-nth-child1-00 mr-last-nth-child1-00">
        <view class="" v-for="(item, index) in list" :key="index" @click="jumpPage(item, index)">
          <view class="p-rela w-136 flex-c-c-c w-s-now" v-if="!(from === 'next' && (item.id === 32 || item.id === 13))">
            <vh-image :loading-type="2" :src="item.icon" :width="84" :height="84" :border-radius="8" />
            <text class="mt-12 font-24 text-3">{{ item.label_name }}</text>
            <image
              v-if="item.badge"
              class="p-abso z-100 top-0 right-06 w-44 h-20"
              :src="item.badge"
              mode="aspectFill"
            />
          </view>
        </view>
      </view>
    </scroll-view>

    <view v-if="list.length > 5" class="p-rela w-72 h-06 b-rad-10 o-hid bg-e0e0e0 mt-24">
      <view
        class="p-abso top-0 left-12 w-22 h-p100 bg-e80404 b-rad-10"
        :style="{ left: `${scrollLeftPercent}%` }"
      ></view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    from: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data: () => ({
    scrollLeftPercent: 0,
  }),
  methods: {
    jumpPage(item, index) {
      if (this.from === 'next' && (item.id === 30 || item.id === 56)) {
        if (item.id === 30) {
          this.jump.appAndMiniJumpBD(
            0,
            '/packageE/pages/daily-tasks/daily-tasks',
            7,
            3,
            401000,
            index + 1,
            this.from,
            0,
            true
          )
        }
        if (item.id === 56) {
          this.jump.appAndMiniJumpBD(
            0,
            '/packageE/pages/large-turntable/large-turntable',
            7,
            3,
            401000,
            index + 1,
            this.from,
            0,
            true
          )
        }
      } else {
        this.jump.pubConfJumpBD(item, 2, 3, 102000, item.id, this.$vhFrom)
      }
    },
    scroll(e) {
      console.log(this.list)
      const scrollWidth1 = uni.upx2px(680)
      const { scrollLeft, scrollWidth: scrollWidth2 } = e?.detail || {}
      this.scrollLeftPercent = (scrollLeft / (scrollWidth2 - scrollWidth1)) * 70
      console.log(this.scrollLeftPercent)
    },
  },
}
</script>
