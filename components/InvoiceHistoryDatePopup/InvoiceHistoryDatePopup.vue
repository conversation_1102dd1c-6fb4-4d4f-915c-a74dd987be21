<template>
	<u-popup 
	:maskCloseAble="true" 
	mode="top" 
	:popup="false" 
	v-model="value" 
	length="auto" 
	@close="close" 
	:zIndex="979"
	:customStyle="{top: popupTop + 'px'}"
	:border-radius="20"
	>
		<view class="content">
			<!-- 选择器主体 -->
			<view class="vh-picker-body">
				<picker-view indicator-class="vh-picker-selected-view" :value="valueArr" @change="change" class="vh-picker-view" @pickstart="pickstart" @pickend="pickend">
					<template v-if="!reset">
						<picker-view-column v-for="(item, index) in range" :key="index">
							<view class="vh-column-item" v-for="(item1, index1) in item" :class="valueArr[index] === index1 ? 'vh-column-select-item' : ''" :key="index1">
								{{ getItemValue(item1, 'date') }} {{ index === 0 ? '年' : index1 === 0 ? '' : '月' }}
							</view>
						</picker-view-column>
					</template>
				</picker-view>
			</view>
			
			<!-- 选择器底部 -->
			<view class="vh-picker-footer">
				<u-button
				shape="circle" 
				:hair-line="false" 
				:ripple="true" 
				ripple-bg-color="#FFF"
				:custom-style="{ width:'646rpx', height:'72rpx', fontSize:'28rpx' ,fontWeight:'bold', color:'#FFF', backgroundColor: '#E80404', border:'none' }"
				@click="getResult('confirm')">确认</u-button>
			</view>
		</view>
	</u-popup>
</template>

<script>
	export default {
		props: {
			popupTop: {
				type: [Number, String],
				default: 0
			},
			mode: {
				type: String,
				default: 'date'
			},
			value: {
				type: Boolean,
				default: false
			},
			// 自定义选择的数据
			range: {
				type: Array,
				default: () => []
			},
			// 当 range 是一个 Array＜Object＞ 时，通过 range-key 来指定 Object 中 key 的值作为选择器显示内容
			rangeKey: {
				type: String,
				default: ''
			},
			defaultSelector: {
				type: Array,
				default: () => []
			},
		},
		
		data() {
			return {
				reset: false, //是否重置
				valueArr: [], //当前选中的数组
				moving: false // 列是否还在滑动中，微信小程序如果在滑动中就点确定，结果可能不准确
			};
		},
		
		mounted() {
			this.init();
		},
		
		watch: {
			propsChange() {
				this.reset = true;
				setTimeout(() => this.init(), 10);
			},
			// 微信和QQ小程序由于一些奇怪的原因(故同时对所有平台均初始化一遍)，需要重新初始化才能显示正确的值
			value(n) {
				if (n) {
					this.reset = true;
					setTimeout(() => this.init(), 10);
				}
			}
		},
		
		methods: {
			// 初始化
			init() {
				this.valueArr = [];
				this.reset = false;
				this.valueArr = this.defaultSelector;
				this.multiSelectorValue = this.defaultSelector;
				this.$forceUpdate();
			},
			
			// 用户更改picker的列选项
			change(e) {
				this.valueArr = e.detail.value;
				console.log( this.valueArr )
				let index = null;
				// 对比前后两个数组，寻找变更的是哪一列，如果某一个元素不同，即可判定该列发生了变化
				this.defaultSelector.map((val, idx) => {
					if (val != e.detail.value[idx]) index = idx;
				});
				// 为了让用户对多列变化时，对动态设置其他列的变更
				if (index != null) {
					this.$emit('columnchange', {
						column: index,
						index: e.detail.value[index]
					});
				}
			},
			
			// 对单列和多列形式的判断是否有传入变量的情况
			getItemValue(item, mode) {
				// 目前(2020-05-25)uni-app对微信小程序编译有错误，导致v-if为false中的内容也执行，错误导致
				// 单列模式或者多列模式中的getItemValue同时被执行，故在这里再加一层判断
				if (this.mode == mode) {
					return typeof item == 'object' ? item[this.rangeKey] : item;
				}
			},
			
			// 标识滑动开始，只有微信小程序才有这样的事件
			pickstart() {
				// #ifdef MP-WEIXIN
				this.moving = true;
				// #endif
			},
			
			// 标识滑动结束
			pickend() {
				// #ifdef MP-WEIXIN
				this.moving = false;
				// #endif
			},
			
			// 用户点击确定按钮
			getResult(event = null) {
				// #ifdef MP-WEIXIN
				if (this.moving) return;
				// #endif
				console.log( this.valueArr )
				let val1 = this.range[0][this.valueArr[0]]
				let val2 = this.range[1][this.valueArr[1]]
				if (event) this.$emit(event, `${val1}`, val2 < 10 ? `0${val2}` : val2);
			},
			
			// 关闭
			close() {
				this.$emit('input', false);
			}
		}
	};
</script>

<style lang="scss" scoped>
	// .content {
	// 	position: relative;
	// 	z-index: 999;
	// }
	.vh-picker-body {
		width: 100%;
		height: 540rpx;
		overflow: hidden;
		background-color: #fff;
	}

	.vh-picker-view {
		// position: relative;
		height: 100%;
		box-sizing: border-box;
		// padding: 0 32rpx;
	}
	::v-deep .vh-picker-selected-view { 
		padding: 0;
	}
	.vh-column-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #A3A3A3;
	}
	.vh-column-select-item {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		transition: .1s all ease-in;
	}
	.vh-picker-footer { 
		position: fixed;
		bottom: 0;
		z-index: 10000;
		width: 100%;
		height: 104rpx;
		background-color: #FFF;
		display: flex; 
		justify-content: center;
		align-items: center;
		box-shadow: 0 2rpx 12rpx 0 rgba(0,0,0,0.22);
	}
</style>
