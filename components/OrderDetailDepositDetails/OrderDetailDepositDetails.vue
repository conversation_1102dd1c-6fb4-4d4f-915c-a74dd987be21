<template>
	<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
		<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
			<text class="font-28 font-wei text-3">订单编号</text>
			<text class="font-24 text-3" @click.stop="copy.copyText( orderInfo.order_no )">{{orderInfo.order_no}}</text>
		</view>
		
		<view class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
			<text class="font-28 font-wei text-3">下单时间</text>
			<text class="font-24 text-3">{{orderInfo.created_time}}</text>
		</view>
			
		<view v-if="![4, 8].includes(orderInfo.status)" class="d-flex j-sb a-center ml-24 mr-24 pt-32 pb-32 bb-s-01-eeeeee">
			<text class="font-28 font-wei text-3">支付方式</text>
			<text class="font-24 text-3">{{ +orderInfo.payment_method | toText('MPaymentMethodText') }}</text>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			orderInfo: {
				type: Object,
				default: () => ({})
			}
		}
	}
</script>