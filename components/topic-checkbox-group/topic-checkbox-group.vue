<template>
  <view class="d-flex flex-wrap">
    <view
      v-for="item in topicList"
      :key="item.id"
      class="d-flex a-center b-rad-28 mt-24 mr-24 h-50 ptb-00-plr-20 font-24 bg-eeeeee text-3"
      :class="value.includes(item.id) ? 'bg-e2ebfa text-2e7bff' : ''"
      @click="handleCheck(item)"
      >#{{ item.title }}#</view
    >

    <view v-if="showMore" class="d-flex a-center b-rad-28 b-s-01-e7e7e7 mt-24 h-50 ptb-00-plr-20" @click="jumpMore">
      <view class="mr-10 font-24 text-3">更多话题</view>
      <u-icon name="arrow-right" :size="20" color="#999" />
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'topicCheckboxGroup',
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    topicList: {
      type: Array,
      default: () => [],
    },
    showMore: {
      type: Boolean,
      default: false,
    },
    checkMax: {
      type: Number,
      default: 3,
    },
  },
  computed: {
    ...mapState(['routeTable']),
  },
  methods: {
    ...mapMutations('topic', ['UPDATE_CHECKED_TOPIC_LIST']),
    handleCheck(item) {
      const { id } = item
      const findIndex = this.value.findIndex((v) => v === id)
      if (findIndex === -1) {
        if (this.value.length === this.checkMax) {
          this.feedback.toast({ title: `话题最多只能选择${this.checkMax}个` })
          return
        }
        this.value.push(id)
        this.$emit('input', this.value)
      } else {
        this.value.splice(findIndex, 1)
      }
    },
    jumpMore() {
      const checkedTopicList = this.topicList.filter((item) => this.value.includes(item.id))
      this.UPDATE_CHECKED_TOPIC_LIST(checkedTopicList)
      this.jump.navigateTo(this.routeTable.PCTopicSelectMore)
    },
  },
}
</script>
