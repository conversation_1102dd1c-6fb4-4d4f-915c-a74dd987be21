<template>
	<view v-if="showProgress" class="fade-in mt-12 d-flex j-sb a-center">
		<view class="h-28 flex-1 d-flex a-center b-rad-14 mt-12 o-hid" :class="channelClassMap.get(channel).bg">
			<view class="h-p100 b-rad-14 d-flex j-end a-center tran-1" :class="channelClassMap.get(channel).bgLin" :style="{width: progress + '%'}">
				<text v-if="progress >= 12" class="font-18 text-ffffff mr-10">{{progress}}%</text>
			</view>
			<view v-if="progress < 12" class="font-18 ml-10" :class="channelClassMap.get(channel).alrBuyColor">{{progress}}%</view>
		</view>
		
		<view class="ml-20">
			<text class="font-22" :class="channelClassMap.get(channel).alrBuyColor">已购</text>
			<text class="ml-02 font-44 font-wei" :class="channelClassMap.get(channel).alrBuyNumColor">{{aleradyBuy}}</text>
			<text class="ml-02 mr-02 font-44 text-9">/</text>
			<text class="font-28 font-wei text-3">{{limitNumber}}</text>
			<text class="ml-02 font-22 text-9">限量</text>
			<text v-if="limitPurchase && limitPurchase!='9999'" class="font-22 text-9">/<text class="font-28 font-wei text-3">{{limitPurchase}}</text>限购</text>
		</view>
	</view>
</template>

<script>
	/*
	 * goods-detail-limit-progress 每个频道的商品限量百分比进度条
	 * @description 该组件一般用于商品详情 限量跟已购的比值
	 * @property {String Numbr} channel 商品频道
	 * @property {String Numbr} alerady-buy 已购
	 * @property {String Numbr} limit-number 限量
	 * @example <vh-goods-detail-limit-progress></vh-goods-detail-limit-progress>
	 */
	export default{
		name:"vh-goods-detail-limit-progress",
		
		props:{
			// 商品频道
			channel:{
				type: [String, Number],
				default: 0
			},
			// 已购
			aleradyBuy:{
				type: [String, Number],
				default: 0
			},
			// 限量
			limitNumber:{
				type: [String, Number],
				default: 0
			},
			// 限购
			limitPurchase: {
				type: [String, Number],
				default: 0
			}
		},
	
		computed:{
			// 是否展示百分比
			showProgress() {
				if( this.channel != 1 && this.channel != 9 ) { //频道不为秒发或者商家秒发时需要显示
					return true
				}
				return false
			},
			
			//频道样式map
			channelClassMap() {
				let map = new Map([
					[0, {bg:'bg-fce4e3', bgLin:'bg-li-2', alrBuyColor:'text-f35e56', alrBuyNumColor: 'text-e80404'}], //闪购
					[2, {bg:'bg-f1edfb', bgLin:'bg-li-8', alrBuyColor:'text-ab8cf4', alrBuyNumColor: 'text-825dda'}], //跨境
					[3, {bg:'bg-feedde', bgLin:'bg-li-9', alrBuyColor:'text-fb933b', alrBuyNumColor: 'text-f6780e'}], //尾货
					[101, {bg:'bg-feedde', bgLin:'bg-li-9', alrBuyColor:'text-fb933b', alrBuyNumColor: 'text-f6780e'}] //拼团（前端默认频道为101 = 拼团商品 等同于 营销活动 = 包含拼团商品）
					// ['y', {bg:'bg-fce4e3', bgLin:'bg-li-2', alrBuyColor:'text-f35e56', alrBuyNumColor: 'text-e80404'}], //秒杀
				])
				return map
			},
			
			// 百分比
			progress() {
				if(!(this.aleradyBuy / this.limitNumber)) {
					return 0
				}else{
					let progress = this.aleradyBuy / this.limitNumber * 100
					progress > 0 && progress <= 1 ? progress = 1 : progress = Math.floor(progress)
					return progress
				}
			}
		}
	}
</script>

<style scoped></style>
