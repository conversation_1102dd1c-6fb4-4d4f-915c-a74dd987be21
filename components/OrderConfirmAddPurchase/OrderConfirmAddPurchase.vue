<template>
	<view class="bg-ffffff b-rad-10 mt-20 ml-24 mr-24">
		<view v-if="list.length === 1" class="p-rela w-702 h-300 p-10">
			<image class="p-abso top-0 left-0 w-702 h-300" :src="ossIcon(`/order_confirm/add_bg1.png`)" mode="" />
			<view class="p-rela d-flex bg-ffffff b-rad-20 mt-74 p-16" v-for="(item, index) in list" :key="index"
			@click="$emit('click', item)">
				<vh-image :src="item.banner_img" :loadingType="2" :width="172" :height="172" :borderRadius="10" />
				<view class="d-flex j-sb flex-column flex-1 ml-20">
					<view class="">
						<view class="font-28 text-3 text-hidden-1">{{ item.title }}</view>
						<view class="mt-10">
							<text class="bg-f5f5f5 b-rad-08 ptb-04-plr-18 font-22 text-9">{{ item.package_name }}</text>
						</view>
					</view>
					<view class="flex-s-c">
						<text class="font-32 font-wei text-3 l-h-34"><text class="font-24">¥</text>{{ item.price }}</text>
						<text v-if="Math.floor(item.reduced_price)" class="bg-fff2f2 b-rad-60 ml-10 ptb-00-plr-10">
							<text class="font-20 text-e80404">已减</text>
							<text class="ml-06 font-28 font-wei text-e80404">
								<text class="font-20">¥</text>{{ Math.floor(item.reduced_price) }}
							</text>
						</text>
					</view>
				</view>
				<view class="p-abso right-30 top-84">
					<vh-select :checked="item.$checked"/>
				</view>
			</view>
		</view>
		<view v-if="list.length > 1" class="p-rela w-702 h-270 mt-30 p-10">
			<image class="p-abso top-0 left-0 w-702 h-270" :src="ossIcon(`/order_confirm/add_bg2.png`)" mode="" />
			<view class="p-rela w-p100 o-scr-x mt-64">
				<view class="w-max-cont h-182 bg-ffffff flex-s-c mr-last-nth-child1-24 ml-nth-child1-24">
					<view class="w-396 h-142 bg-fafafa d-flex b-rad-10 ml-16 p-12" 
					v-for="(item, index) in list" :key="index" 
					@click="$emit('click', item)">
						 <vh-image :src="item.banner_img" :loading-type="2" :width="118" :height="118" :borderRadius="4" />
						 <view class="d-flex j-sb flex-column flex-1 ml-16">
							<view class="">
								<view class="font-22 text-3 text-hidden-1">{{ item.title }}</view>
								<view class="mt-04 font-18 text-9 l-h-26">{{ item.package_name }}</view>
							</view>
							<view class="flex-sb-c">
								<view class="flex-s-c">
									<text class="font-28 font-wei text-3 l-h-34 w-s-now"><text class="font-18">¥</text>{{ item.price }}</text>
									<text v-if="Math.floor(item.reduced_price)" class="bg-fff2f2 b-rad-60 ml-04 ptb-00-plr-10 w-s-now">
										<text class="font-14 text-e80404 l-h-26">已减</text>
										<text class="font-28 font-wei text-e80404 l-h-34"><text class="font-18">¥</text>{{ Math.floor(item.reduced_price) }}</text>
									</text>
								</view>
								
								<vh-select :width="26" :height="26" :checked="item.$checked" />
							</view>
						 </view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			list: {
				type: Array,
				default: () => []
			}
		}
	}
</script>