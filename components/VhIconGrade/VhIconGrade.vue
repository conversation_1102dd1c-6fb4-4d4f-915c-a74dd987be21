<template>
	<view class="d-inline-block text-center" :style="[gradeContainerStyle]">
		<text class="p-rela" :style="[gradeStyle]">LV.{{grade ? grade : 0}}</text>
	</view>
</template>

<script>
	export default{
		props: {
			width: {
				type: [String, Number],
				default: 80
			},
			bgColor: {
				type: String,
				default: '#ff9127'
			},
			borderRadius: {
				type: [String, Number],
				default: 100
			},
			border: {
				type: String,
				default: 'none'
			},
			marginLeft: {
				type: [String, Number],
				default: 10
			},
			padding: {
				type: String,
				default: '0 16rpx'
			},
			fontSize: {
				type: [String, Number],
				default: 24
			},
			fontBold: {
				type: Boolean,
				default: false
			},
			color: {
				type: String,
				default: '#FFFFFF'
			},
			lineHeight: {
				type: [String, Number],
				default: 32
			},
			grade: {
				type: [String, Number],
				default: 0
			}
		},
		computed: {
			gradeContainerStyle() {
				return {
					width: `${this.width}rpx`,
					background: this.bgColor,
					borderRadius: this.borderRadius + 'rpx',
					border: this.border,
					marginLeft: this.marginLeft + 'rpx',
					lineHeight: this.lineHeight + 'rpx',
				}
			},
			gradeStyle() {
				return {
					bottom: '1rpx',
					fontFamily: 'PingFangSC-Semibold, PingFang SC',
					fontSize: this.fontSize + 'rpx',
					fontWeight: this.fontBold ? 'bold' : 'normal',
					color: this.color,
					whiteSpace: 'nowrap'
				};
			}
		}
	}
</script>
