<template>
  <u-popup :value="value" mode="center" width="552rpx" height="482rpx" border-radius="20" @input="onInput">
    <view class="p-rela wh-p100">
      <image :src="ossIcon('/auction/popup_bg_552_482.png')" class="p-abso wh-p100" />
      <view class="p-rela pt-84">
        <view v-if="tips.length" class="ptb-00-plr-50 font-wei-500 font-32 text-6 l-h-48 text-center">{{ tips[0] }}<text v-if="tips[1]">（<text class="text-3">{{ tips[1] }}</text>）</text>。</view>
        <view class="flex-sb-c mt-72 ptb-00-plr-72">
          <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32" @click="onInput(false)">取消</button>
          <button class="vh-btn flex-c-c w-180 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="$emit('confirm')">确定</button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    tips: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
  },
}
</script>