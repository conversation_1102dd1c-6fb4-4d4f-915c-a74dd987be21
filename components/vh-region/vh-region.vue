<template>
	<u-popup :maskCloseAble="maskCloseAble" mode="bottom" :popup="false" v-model="value" length="auto" :safeAreaInsetBottom="safeAreaInsetBottom" @close="close" :z-index="uZIndex">
		<view class="p-rela z-999">
			<view class="region-header" @touchmove.stop.prevent="">
				<view class="bs-bb p-16 text-center" 
					:style="{ color: cancelColor }" 
					hover-class="op-050" 
					:hover-stay-time="150" 
					@tap="getResult('cancel')"
				>{{cancelText}}</view>
				<view class="text-3">{{ title }}</view>
				<view
					class="bs-bb p-16 text-center"
					:style="{ color: moving ? cancelColor : confirmColor }"
					hover-class="op-050"
					:hover-stay-time="150"
					@touchmove.stop=""
					@tap.stop="getResult('confirm')"
				>
					{{confirmText}}
				</view>
			</view>
			<view class="w-p100 h-500 bg-ffffff o-hid">
				<picker-view :value="valueArr" @change="change" class="h-p100 bs-bb" @pickstart="pickstart" @pickend="pickend">
					<picker-view-column v-if="!reset && params.province">
						<view class="d-flex j-center a-center ptb-00-plr-08 font-32 text-3" v-for="(item, index) in provinces" :key="index">
							<view class="o-hid w-s-now t-o-ell">{{ item.label }}</view>
						</view>
					</picker-view-column>
					<picker-view-column v-if="!reset && params.city">
						<view class="d-flex j-center a-center ptb-00-plr-08 font-32 text-3" v-for="(item, index) in citys" :key="index">
							<view class="o-hid w-s-now t-o-ell">{{ item.label }}</view>
						</view>
					</picker-view-column>
					<picker-view-column v-if="!reset && params.area">
						<view class="d-flex j-center a-center ptb-00-plr-08 font-32 text-3" v-for="(item, index) in areas" :key="index">
							<view class="o-hid w-s-now t-o-ell">{{ item.label }}</view>
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</view>
	</u-popup>
</template>

<script>
import { mapState } from 'vuex'

/**
 * region 地区弹出选择器
 * @description 此选择器用于选选择地址
 * @property {Boolean} mask-close-able 是否允许通过点击遮罩关闭弹窗（默认true）
 * @property {Boolean} safe-area-inset-bottom 是否开启底部安全区适配（默认false）
 * @property {String} cancel-color 取消按钮的颜色（默认#606266）
 * @property {String} cancel-text 取消按钮的文字
 * @property {String} title 顶部标题
 * @property {String} confirm-color 确认按钮的颜色（默认#E80404）
 * @property {String} confirm-text 确认按钮的文字
 * @property {Object} params 需要显示的参数
 * @property {String Number} z-index 弹出时的z-index值（默认1075）
 * @event {Function} confirm 点击确定按钮，返回当前选择的值
 * @event {Function} cancel 点击取消按钮，返回当前选择的值
 * @example <vh-region v-model="show"></vh-region>
 */
export default {
	name: 'vh-region',
	props: {
		// 是否允许通过点击遮罩关闭弹窗
		maskCloseAble: {
			type: Boolean,
			default: true
		},
		// 是否开启底部安全区适配
		safeAreaInsetBottom: {
			type: Boolean,
			default: false
		},
		// 取消按钮的颜色
		cancelColor: {
			type: String,
			default: '#606266'
		},
		// 取消按钮的文字
		cancelText: {
			type: String,
			default: '取消'
		},
		// 顶部标题
		title: {
			type: String,
			default: ''
		},
		// 确认按钮的颜色
		confirmColor: {
			type: String,
			default: '#E80404'
		},
		// 确认按钮的文字
		confirmText: {
			type: String,
			default: '确认'
		},
		// region中需要显示的参数
		params: {
			type: Object,
			default() {
				return {
					province: true,
					city: true,
					area: true,
				};
			}
		},
		// 通过双向绑定控制组件的弹出与收起
		value: {
			type: Boolean,
			default: false
		},
		// 弹出的z-index值
		zIndex: {
			type: [String, Number],
			default: 0
		}
	},
	data() {
		return {
			reset: false, 
			valueArr: [],
			provinces: [], //省列表
			citys: '', //市列表
			areas: '', //区列表
			province: 0, //省索引
			city: 0, //市索引
			area: 0, //区索引
			moving: false // 列是否还在滑动中，微信小程序如果在滑动中就点确定，结果可能不准确
		};
	},
	
	created() {
		this.provinces = this.regionInfo.provinces
		this.citys = this.regionInfo.citys[0]
		this.areas = this.regionInfo.areas[0][0]
	},
	
	
	mounted() {
		this.init();
	},
	
	computed: {
		//Vuex 辅助state函数
		...mapState(['regionInfo']),
		
		// 引用这几个变量，是为了监听其变化
		regionChange() {
			return `${this.province}-${this.city}`;
		},
		
		// 如果用户有传递z-index值，优先使用
		uZIndex() {
			return this.zIndex ? this.zIndex : this.$u.zIndex.popup;
		}
	},
	
	watch: {
		// props发生变化
		propsChange() {
			this.reset = true;
			setTimeout(() => this.init(), 10);
		},
		
		// 如果地区发生变化，为了让picker联动起来，必须重置this.citys和this.areas
		regionChange(val) {
			this.citys = this.regionInfo.citys[this.province];
			this.areas = this.regionInfo.areas[this.province][this.city];
		},
		
		// 微信和QQ小程序由于一些奇怪的原因(故同时对所有平台均初始化一遍)，需要重新初始化才能显示正确的值
		value(n) {
			if (n) {
				this.reset = true;
				setTimeout(() => this.init(), 10);
			}
		}
	},
	
	methods: {
		// 标识滑动开始，只有微信小程序才有这样的事件
		pickstart() {
			// #ifdef MP-WEIXIN
			this.moving = true;
			// #endif
		},
		
		// 标识滑动结束
		pickend() {
			// #ifdef MP-WEIXIN
			this.moving = false;
			// #endif
		},
		
		// 初始化
		init() {
			this.valueArr = [];
			this.reset = false;
			if (this.params.province) {
				this.valueArr.push(0);
				this.setProvinces();
			}
			if (this.params.city) {
				this.valueArr.push(0);
				this.setCitys();
			}
			if (this.params.area) {
				this.valueArr.push(0);
				this.setAreas();
			}
			this.$forceUpdate();
		},
		
		// 设置省
		setProvinces() {
			// 判断是否需要province参数
			if (!this.params.province) return;
			let tmp = 0;
			// 历遍省份数组匹配
			this.regionInfo.provinces.map((v, k) => {
				if (v.label == tmp) {
					tmp = k;
				}
			});
			this.province = tmp;
			this.provinces = this.regionInfo.provinces;
			// 设置默认省份的值
			this.valueArr.splice(0, 1, this.province);
		},
		
		// 设置市
		setCitys() {
			if (!this.params.city) return;
			let tmp = 0;
			this.regionInfo.citys[this.province].map((v, k) => {
				if (v.label == tmp) {
					tmp = k;
				}
			});
			this.city = tmp;
			this.citys = this.regionInfo.citys[this.province];
			this.valueArr.splice(1, 1, this.city);
		},
		
		// 设置区
		setAreas() {
			if (!this.params.area) return;
			let tmp = 0;
			this.regionInfo.areas[this.province][this.city].map((v, k) => {
				if (v.label == tmp) {
					tmp = k;
				}
			});
			this.area = tmp;
			this.areas = this.regionInfo.areas[this.province][this.city];
			this.valueArr.splice(2, 1, this.area);
		},
		
		// 关闭组件
		close() {
			this.$emit('input', false);
		},
		
		// 用户更改picker的列选项
		change(e) {
			this.valueArr = e.detail.value;
			let i = 0;
			if (this.params.province) this.province = this.valueArr[i++];
			if (this.params.city) this.city = this.valueArr[i++];
			if (this.params.area) this.area = this.valueArr[i++];
		},
		
		// 用户点击确定按钮
		getResult(event = null) {
			// #ifdef MP-WEIXIN
			if (this.moving) return;
			// #endif
			let result = {};
			if (this.params.province) result.province = this.regionInfo.provinces[this.province];
			if (this.params.city) result.city = this.regionInfo.citys[this.province][this.city];
			if (this.params.area) result.area = this.regionInfo.areas[this.province][this.city][this.area];
			if (event) this.$emit(event, result);
			this.close();
		},
	}
};
</script>

<style lang="scss" scoped>

.region-header {
	width: 100%;
	height: 90rpx;
	padding: 0 40rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-sizing: border-box;
	font-size: 30rpx;
	background: #fff;
	position: relative;
}

.region-header::after {
	content: '';
	position: absolute;
	border-bottom: 1rpx solid #eaeef1;
	-webkit-transform: scaleY(0.5);
	transform: scaleY(0.5);
	bottom: 0;
	right: 0;
	left: 0;
}

</style>
