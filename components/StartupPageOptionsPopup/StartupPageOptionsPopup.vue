<template>
  <u-popup
    v-model="show"
    :maskCloseAble="true"
    mode="bottom"
    :popup="false"
    length="auto"
    :border-radius="20"
    @close="close"
  >
    <view class="p-rela pt-50 pb-48">
      <view class="ptb-00-plr-52">
        <view class="font-36 font-wei-500 text-3">设置默认主页</view>
        <view class="mt-08">
          <view class="flex-sb-c mt-68" v-for="(item, index) in options" :key="index" @click="currType = item.type">
            <view class="flex-c-c">
              <vh-image :src="ossIcon(item.icon)" :loading-type="2" :width="36" :height="36" />
              <view class="ml-20 font-32 font-wei-500 text-3">{{ item.name }}</view>
            </view>
            <image
              :src="ossIcon(`/miaofa/radio${item.type == currType ? '_h' : ''}_32_1.png`)"
              class="w-32 h-32"
            ></image>
          </view>
        </view>
      </view>

      <view class="flex-sb-c mt-100 ptb-00-plr-32">
        <button class="vh-btn flex-c-c w-308 h-64 bg-eeeeee b-rad-32 font-28 text-9" @click="close">取消</button>
        <button class="vh-btn flex-c-c w-308 h-64 bg-e80404 b-rad-32 font-28 text-ffffff" @click="confirm">确定</button>
      </view>
    </view>
  </u-popup>
</template>

<script>
const options = Object.freeze([
  { type: 0, name: '现货速发', icon: '/tab_bar/sec_hair_black.png' },
  { type: 1, name: '首页', icon: '/tab_bar/index_black.png' },
])
export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    type: {
      type: [Number, String],
      default: 1,
    },
  },
  mounted() {
    this.secondConfig()
  },
  data: () => ({
    options,
    show: !(uni.getStorageSync('startupOptionsRecord') || 0),
    currType: 1,
  }),
  methods: {
    async secondConfig() {
      const res = await this.$u.api.secondConfig()
      if (res.data.isopen) {
        this.options = [
          { type: 0, name: '年货节', icon: '/tab_bar/sec_hair_black.png' },
          { type: 1, name: '首页', icon: '/tab_bar/index_black.png' },
        ]
      }
    },
    close() {
      uni.setStorageSync('startupOptionsRecord', 1)
      this.show = false
    },
    confirm() {
      this.feedback.loading()
      this.$u.api.userChangePageConfig({ btntype: 2, btnstatus: this.currType }).then((res) => {
        this.feedback.toast({ title: '设置成功~ ' })
        this.close()
      })
    },
  },
  created() {
    this.currType = this.type
  },
}
</script>
