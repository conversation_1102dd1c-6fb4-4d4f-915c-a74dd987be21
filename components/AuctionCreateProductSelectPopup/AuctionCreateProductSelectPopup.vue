<template>
  <u-popup :value="value" mode="bottom" width="100%" height="662rpx" border-radius="20" @input="onInput">
    <view class="p-rela pt-48 h-p100">
      <view class="font-wei-600 text-3 l-h-44 text-center">选择商品</view>
      <view class="mt-28 ptb-00-plr-48 h-400 o-scr-y">
        <view
          v-for="(item, index) in list"
          :key="item.id"
          class="flex-sb-c ptb-32-plr-00"
          :class="index ? 'bt-s-02-eeeeee' : ''"
          @click="selectedId = item.id"
        >
          <view class="flex-1 font-28 text-3 l-h-40">{{ item.cn_product_name }}</view>
          <image :src="ossIcon(`/auction/radio${selectedId === item.id ? '_h' : ''}_32.png`)" class="ml-32 w-32 h-32"></image>
        </view>
      </view>
      <view class="p-abso left-0 bottom-0 flex-c-c w-p100 h-104 bg-ffffff b-sh-00021200-022 z-999">
        <button class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff b-rad-32" :class="selectedId ? 'bg-e80404' : 'bg-fce4e3'" @click="onConfirm">确定</button>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    list: {
      type: Array,
      default: () => []
    },
    productId: {
      type: Number,
      default: 0
    }
  },
  data: () => ({
    selectedId: 0
  }),
  watch: {
    value (newVal) {
      if (newVal) {
        this.selectedId = this.productId
      }
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onConfirm () {
      if (!this.selectedId) return
      this.$emit('confirm', this.selectedId)
      this.onInput(false)
    }
  },
}
</script>