<template>
  <u-popup :value="value" mode="bottom" width="100%" height="618rpx" border-radius="20" @input="onInput">
    <view class="pt-48 pl-48 pr-48">
      <view class="p-rela flex-c-c">
        <text class="font-wei-500 font-32 text-3 l-h-44">选择时长</text>
        <text class="p-abso right-0 font-28 text-3 l-h-40" @click="onConfirm">确定</text>
      </view>

      <picker-view :value="pickerViewValue" @change="onPickerViewValueChange" indicator-style="height: 104rpx" class="mt-40 h-416">
        <picker-view-column>
            <view class="flex-c-c font-wei-600 font-28 text-9" v-for="item in list" :key="item.id">{{ item.name }}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    pickerViewValue: [0]
  }),
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onConfirm () {
      const cycle = this.list[this.pickerViewValue[0]]
      this.$emit('confirm', cycle)
      this.onInput(false)
    },
    onPickerViewValueChange (e) {
      const value = e.detail.value
      this.pickerViewValue = value
    }
  },
}
</script>