<template>
	<view class="w-340 h-512">
		<vh-swiper 
			:list="item.list" 
			:name="'banner_img'" 
			:loadingType="7"
			:height="512" 
			:mode="'dot'" 
			:customDot="true" 
			:customDotStyle="customDotStyle"
			:borderRadius="10" 
			:isShowIndicator="item.list.length > 1"
			 @click="onJump" 
		 />
	</view>
</template>

<script>
	export default {
		props: {
		  item: {
		    type: Object,
		    default: () => ({})
		  }
		},
		data: () => ({
			customDotStyle: {
				default: {
					width: '14rpx',
					height: '14rpx',
					borderRadius: '14rpx',
					margin: '0 12rpx',
					transition: 'all 0.5s',
					backgroundColor: '#EEA47A',
				},
				active: {
					width: '14rpx',
					height: '14rpx',
					borderRadius: '14rpx',
					margin: '0 12rpx',
					transition: 'all 0.5s',
					backgroundColor: '#FFFFFF',
				}
			},
		}),
		methods: {
			onJump(item) {
				const { jump_type = 0, goods_id, activity_id } = item
				if (jump_type === 0) {
					if (goods_id) {
						this.jump.navigateTo(`${this.$routeTable.pHAuctionGoodsDetail}?id=${item.goods_id}`)
					}
				} else if (jump_type === 1) {
					if (activity_id) {
						this.jump.navigateTo(`${this.$routeTable.pHAuctionAdDetail}?id=${activity_id}`)
					}
				}
			}
		}
	}
</script>