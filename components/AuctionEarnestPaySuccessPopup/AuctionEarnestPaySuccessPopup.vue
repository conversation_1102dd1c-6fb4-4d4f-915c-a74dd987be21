<template>
  <u-popup :value="value" mode="bottom" width="100%" height="548" border-radius="20" @input="onInput">
    <view class="d-flex j-center pt-98">
      <image :src="ossIcon('/auction/icon_pay_success.png')" class="w-266 h-184"></image>
    </view>
    <view class="mt-16 font-32 text-3 text-center">支付成功</view>
    <button class="vh-btn flex-c-c mtb-00-mlr-auto mt-96 w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="onConfirm">确认</button>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onConfirm () {
      this.onInput(false)
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
