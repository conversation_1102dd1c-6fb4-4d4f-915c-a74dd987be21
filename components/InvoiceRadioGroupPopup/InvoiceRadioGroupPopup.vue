<template>
  <u-popup
    :value="value"
    mode="bottom"
    width="100%"
    height="674rpx"
    border-radius="40"
    :mask-custom-style="{ background: 'rgba(0, 0, 0, 0.3)' }"
    @input="onInput"
  >
    <view class="d-flex flex-column h-p100 bg-f5f5f5">
      <view class="pt-48 h-128 font-wei-500 font-32 text-0 text-center">发票抬头</view>
      <view class="flex-1 o-scr-y">
        <scroll-view v-if="list.length" scroll-y="true" class="h-p100">
          <view class="ptb-00-plr-24">
            <view
              v-for="(item, index) in list"
              :key="item.id"
              class="p-24 bg-ffffff b-rad-10"
              :class="index ? 'mt-20' : ''"
            >
              <view class="flex-sb-c pb-20 bb-s-02-eeeeee">
                <view class="font-wei-600 font-28 text-3 l-h-40"
                  >{{ item.invoice_type | toText('MInvoiceTypeText') }}抬头-{{
                    item.type_id | toText('MInvoiceFrontTypeText')
                  }}</view
                >
                <view class="flex-c-c w-34 h-34">
                  <image
                    :src="ossIcon(`/invoices/${invoiceId === item.id ? 'radio_h-34' : 'radio_34'}.png`)"
                    class="flex-shrink p-10 wh-p100"
                    @click.stop="onChange(item)"
                  ></image>
                </view>
              </view>
              <view class="flex-sb-c mt-20">
                <view>
                  <view class="font-24 text-3 l-h-34">{{ item.invoice_name }}</view>
                  <view v-if="item.type_id == MInvoiceFrontType.Company" class="mt-04 font-24 text-9 l-h-34">{{
                    item.taxpayer
                  }}</view>
                </view>
                <view class="flex-c-c w-38 h-38">
                  <image
                    :src="ossIcon('/invoices/edit_38.png')"
                    class="flex-shrink p-10 wh-p100"
                    @click.stop="onEdit(item)"
                  ></image>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
        <vh-empty
          v-else
          bgColor="transparent"
          :image-src="ossIcon('/empty/emp_order.png')"
          :text-bottom="0"
          text="您还没有添加发票抬头"
        ></vh-empty>
      </view>
      <view class="d-flex j-center pt-40 h-152">
        <button
          class="vh-btn flex-c-c w-646 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32"
          @click="jump.navigateTo($routeTable.pEInvoiceHeadAdd)"
        >
          添加发票抬头
        </button>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { MInvoiceFrontType } from '@/common/js/utils/mapperModel'
import { mapMutations } from 'vuex'

export default {
  props: {
    value: {
      type: Boolean,
      default: true,
    },
    invoiceId: {
      type: Number,
      default: 0,
    },
  },
  data: () => ({
    MInvoiceFrontType,
    list: [],
  }),
  computed: {},
  created() {
    // this.load()
  },
  methods: {
    ...mapMutations(['muInvoiceInfo']),
    onInput(value) {
      this.$emit('input', value)
    },
    load() {
      this.$u.api.invoiceList().then((res) => {
        this.list = res.data?.list || []
        const findItem = this.list.find((item) => item.id === this.invoiceId)
        this.$emit('find', findItem)
      })
    },
    onEdit(item) {
      this.muInvoiceInfo(item)
      uni.setStorageSync('InvoiceInfo',item);
      this.jump.appAndMiniJump(1, this.routeTable.pEInvoiceHeadAdd, this.$vhFrom)
    },
    onChange(item) {
      this.$emit('change', item)
    },
  },
}
</script>

<style lang="scss" scoped></style>
