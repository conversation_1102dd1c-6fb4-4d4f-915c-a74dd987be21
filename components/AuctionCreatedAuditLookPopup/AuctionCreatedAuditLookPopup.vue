<template>
  <u-popup :value="value" mode="center" width="552rpx" height="482rpx" border-radius="20" @input="onInput">
    <view class="p-rela wh-p100">
      <image :src="ossIcon('/auction/popup_bg_552_482.png')" class="p-abso wh-p100" />
      <view class="p-rela pt-84">
        <view class="font-wei-500 font-32 text-6 l-h-44 text-center">你提交的拍品审核未通过</view>
        <view class="mt-20 ptb-00-plr-50 h-min-176 font-24 text-6 l-h-34 text-center text-hidden-5">{{ auctionGoods && auctionGoods.reject_remark }}</view>
        <view class="flex-sb-c mt-10 ptb-00-plr-76">
          <button class="vh-btn flex-c-c w-160 h-60 font-28 text-6 bg-transp b-s-02-666666 b-rad-32" @click="onInput(false)">知道了</button>
          <button class="vh-btn flex-c-c w-180 h-60 font-28 text-ffffff bg-e80404 b-rad-32" @click="onToModify">去修改</button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    auctionGoods: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onToModify () {
      this.jump.navigateTo(`${this.$routeTable.pHAuctionGoodsCreateNew}?id=${this.auctionGoods.id}`)
    }
  },
}
</script>