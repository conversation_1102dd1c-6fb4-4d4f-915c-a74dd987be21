<template>
	<view class="bg-ffffff b-rad-16 mt-20 mr-24 ml-24 ptb-00-plr-20">
		<!-- 订单编号 -->
		<view class="flex-sb-c ptb-32-plr-00">
			<text class="font-28 text-6">订单编号</text>
			<view class="" @click="copy.copyText(orderInfo.order_no)">
				<text class="font-24 text-6">{{ orderInfo.order_no }}</text>
				<text class="bg-eeeeee b-rad-18 ml-10 ptb-02-plr-10 font-18 text-3">复制</text>
			</view>
		</view>
		
		<!-- 商户交易号 -->
		<view v-if="![0,4].includes(orderInfo.status)" class="flex-sb-c bt-s-01-eeeeee ptb-32-plr-00">
			<text class="font-28 text-6">商户交易号</text>
			<text class="font-28 text-6">{{ orderInfo.tradeno }}</text>
		</view>
		
		<!-- 创建时间 -->
		<view class="flex-sb-c bt-s-01-eeeeee ptb-32-plr-00">
			<text class="font-28 text-6">创建时间</text>
			<text class="font-28 text-6">{{ orderInfo.created_time }}</text>
		</view>
		
		<!-- 支付时间 -->
		<view v-if="![0,4].includes(orderInfo.status)" class="flex-sb-c bt-s-01-eeeeee ptb-32-plr-00">
			<text class="font-28 text-6">支付时间</text>
			<text class="font-28 text-6">{{ orderInfo.payment_time }}</text>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			// 订单信息
			orderInfo: {
				type: Object,
				default: function() {
					return {};
				}
			}
		}
	}
</script>

<style>
</style>