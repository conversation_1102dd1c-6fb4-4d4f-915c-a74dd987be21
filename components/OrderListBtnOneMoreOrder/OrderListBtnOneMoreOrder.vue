<template>
	<view v-if="![4, 11].includes(orderType)" class="ml-20">
		<u-button 
			shape="circle" 
			:hair-line="false" 
			:ripple="true" 
			ripple-bg-color="#FFF"
			:custom-style="{width:'148rpx', height:'52rpx', fontSize:'24rpx', color:'#E80404', border:'1rpx solid #E80404'}" 
			@click="click">再来一单</u-button>
	</view>
</template>

<script>
	export default {
		name: 'OrderListBtnOneMoreOrder',
		props: {
			orderType: {
				type: [String, Number],
				default: 0
			}
		},
		
		methods: {
			click() {
				this.$emit('click')
			}
		}
	}
</script>

<style>
</style>