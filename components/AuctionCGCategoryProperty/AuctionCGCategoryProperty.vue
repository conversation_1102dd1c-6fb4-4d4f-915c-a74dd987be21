<template>
  <view class="flex-c-c mt-24">
    <text class="w-72 font-wei-500 font-24 text-6 l-h-34"><text v-if="required" class="text-e80404">*</text>{{ name }}</text>
    <view class="flex-sb-c pl-16 pr-20 w-630 h-min-80 bg-f4f4f4 b-rad-10" @click="$emit('click')">
      <view class="w-518 font-24 l-h-34 text-hidden" :class="value ? 'text-3' : 'text-9'">{{ value || placeholder }}</view>
      <image :src="ossIcon('/about/arrow_r_12_20.png')" class="w-12 h-20" />
    </view>
  </view>
</template>

<script>
export default {
  props: {
    required: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      default: ''
    },
    value: {
    },
    placeholder: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
