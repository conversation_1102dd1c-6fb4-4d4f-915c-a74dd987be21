<template>
	<view class="mb-20 ptb-28-plr-24 bg-ffffff b-rad-10">
		<image :src="ossIcon('/goods_detail/deposit_flow.png')" class="w-p100 h-74"></image>
		<view class="mt-28 h-02 bg-eeeeee"></view>
		<view class="flex-s-c mt-26 font-26 l-h-36">
			<view class="w-248 text-3">订金支付截止时间</view>
			<view class="font-wei-500 text-e80404">{{ countDownStr }}</view>
		</view>
		<view class="flex-s-c mt-20 font-26 l-h-36">
			<view class="w-248 text-3">尾款支付开始时间</view>
			<view class="font-wei-500">{{ sellTimeList[0] }}</view>
			<view class="ml-24 font-wei-500 text-e80404">{{ sellTimeList[1] }}</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			estimatedDeliveryTime: {
				type: String,
				default: ''
			},
			timeInfo: {
				type: Object,
				default: () => ({})
			}
		},
		data: () => ({
			countDownStr: '',
			interval: null,
		}),
		computed: {
			sellTimeList ({ timeInfo }) {
				return timeInfo?.sell_time?.split(' ') || []
			}
		},
		methods: {
			formatTime(seconds) {
				const day = Math.floor(seconds / (60 * 60 * 24));
				const hour = Math.floor(seconds / (60 * 60)) - day * 24;
				const minute = Math.floor(seconds / 60) - hour * 60 - day * 24 * 60;
				const second = Math.floor(seconds) - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60;
				this.countDownStr = `${day ? `${day}天 ` : ''}${hour < 10 ? `0${hour}:` : `${hour}:`}${minute < 10 ? `0${minute}:` : `${minute}:`}${second < 10 ? `0${second}` : second}`
			},
			startCountDown () {
				this.endCountDown()
				let seconds = this.date.getSeconds(this.timeInfo.sell_time)
				this.formatTime(seconds);
				this.interval = setInterval(() => {
					if (seconds <= 0) {
						this.endCountDown()
						return
					}
					seconds--;
					this.formatTime(seconds);
				}, 1000);
			},
			endCountDown () {
				this.interval && clearInterval(this.interval)
			}
		},
		created() {
			this.startCountDown()
		},
	}
</script>