<template>
  <view class="p-rela flex-sb-c h-184 bg-ffffff b-rad-10 o-hid">
    <image v-if="expireIcon" :src="ossIcon(expireIcon)" class="p-abso top-0 left-0 w-70 h-80 z-01"></image>
    <view class="p-rela ml-16 mr-12 bg-f6f6f6 b-rad-06 o-hid">
      <view class="w-136 h-136" :class="[[1007, 1008].includes(item.coupon_type) ? 'p-10' : '']">
        <vh-image :src="item.image_url" width="100%" height="100%" />
      </view>
      <view v-if="[1007, 1008].includes(item.coupon_type) && (isUsed || isFailed)" style="background: rgba(238,238,238,0.6);" class="p-abso top-0 left-0 wh-p100"></view>
    </view>
    <view class="flex-1 d-flex flex-column j-sb ptb-20-plr-00 h-p100 o-hid">
      <view class="d-flex a-center">
        <image v-if="icon" :src="ossIcon(icon)" style="width: 38px; height: 16px;" class="mr-06"></image>
        <view class="flex-1 font-wei-600 l-h-40 text-hidden" :class="nameClazz">{{ item.coupon_name }}</view>
      </view>
      <view class="font-22 l-h-32 text-hidden-2" :class="descClazz">{{ item.coupon_type_describe ? item.coupon_type_describe.desc : item.desc }}</view>
      <view class="font-22 l-h-32" :class="expireClazz">有效期至{{ item.expire_time }}</view>
    </view>
    <view class="p-rela flex-c-c w-160 h-p100" :class="[rightSideClazz]">
      <view class="p-rela d-flex flex-column a-center w-p100 h-136" :class="[isFailed ? 'j-center' : 'j-end']">
        <view class="coupon-face-value d-flex a-baseline" :class="[isFailed ? '' : 'p-abso bottom-64', faceValueClazz, faceValueIsRight ? 'right-0 a-self-end' : '']">
          <view v-if="![2003, 2004].includes(item.coupon_type)" class="font-28 l-h-40 l-h-40">¥</view>
          <view class="font-wei-500 font-52 l-h-74 w-s-now">{{ couponFaceValue }}</view>
          <view v-if="[2003, 2004].includes(item.coupon_type)" class="font-28 l-h-40 l-h-40">折</view>
        </view>
        <view class="font-18 l-h-26" :class="[isFailed ? 'mt-n-16' : '', typeTextClazz]">{{ couponTypeText }}</view>
        <view v-if="btnStatus" class="flex-c-c mt-16 w-120 h-38 font-24 b-rad-20" :class="[btnClazz]" @click="toUse">{{ btnText }}</view>
      </view>
      <view style="transform: translate(-50%, -50%);" class="p-abso left-0 top-0 w-32 h-32 b-rad-p50 bg-f5f5f5 z-01" :class="[isSelected ? 'b-s-02-e80404' : 'b-s-02-f5f5f5']"></view>
      <view style="transform: translate(-50%, 50%);" class="p-abso left-0 bottom-0 w-32 h-32 b-rad-p50 bg-f5f5f5 z-01" :class="[isSelected ? 'b-s-02-e80404' : 'b-s-02-f5f5f5']"></view>
    </view>
    <view v-if="isSelected" class="p-abso wh-p100 b-s-02-e80404 b-rad-10">
      <image :src="ossIcon('/coupon/select_h_44_34.png')" class="p-abso top-n-02 right-n-02 w-44 h-34"></image>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    isUsed: { // 已使用
      type: Boolean,
      default: false
    },
    isFailed: { // 已失效
      type: Boolean,
      default: false
    },
    isUnusable: { // 不可用
      type: Boolean,
      default: false
    },
    isSelected: {
      type: Boolean,
      default: false
    },
    isJump: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    faceValueIsRight: false
  }),
  computed: {
    expireIcon ({ item }) {
      if (this.isUsed) return ''
      if (this.isFailed) return '/coupon/expire_d.png'
      if (this.isUnusable) return ''
      switch (item.use_notice_day) {
        case 1:
          return '/coupon/expire_1.png'
        case 2:
          return '/coupon/expire_4.png'
        case 3:
          return '/coupon/expire_2.png'
        case 4:
          return '/coupon/expire_3.png'
        default:
          return ''
      }
    },
    // 1立减 2满减 5指定 6折扣
    icon ({ item, isUsed, isFailed, isUnusable }) {
      if (isUnusable) {
        switch (item.cp_id) {
          case 1:
            return '/coupon/coupon_type1_n.png'
          case 2:
            return '/coupon/coupon_type2_n.png'
          case 5:
            return '/coupon/coupon_type5_n.png'
          case 6:
            return '/coupon/coupon_type6_n.png'
          default:
            return ''
        }
      }
      if (isUsed || isFailed) {
        switch (item.cp_id) {
          case 1:
            return '/coupon/coupon_type1_d.png'
          case 2:
            return '/coupon/coupon_type2_d.png'
          case 5:
            return '/coupon/coupon_type5_d.png'
          case 6:
            return '/coupon/coupon_type6_d.png'
          default:
            return ''
        }
      }
      switch (item.cp_id) {
        case 1:
          return '/coupon_type1.png'
        case 2:
          return '/coupon_type2.png'
        case 5:
          return '/coupon_type5.png'
        case 6:
          return '/coupon_type6.png'
        default:
          return ''
      }
    },
    nameClazz ({ isUsed, isFailed, isUnusable }) {
      if (isUsed || isFailed) return 'text-d8d8d8'
      if (isUnusable) return 'text-9'
      return 'text-3'
    },
    descClazz ({ isUsed, isFailed, isUnusable }) {
      if (isUsed || isFailed) return 'text-cecdcd'
      if (isUnusable) return 'text-d8d8d8'
      return 'text-3'
    },
    expireClazz ({ isUsed, isFailed, isUnusable }) {
      if (isUsed || isFailed) return 'text-cecdcd'
      if (isUnusable) return 'text-d8d8d8'
      return 'text-9'
    },
    rightSideClazz ({ isUsed, isFailed, isUnusable }) {
      if (isUsed || isFailed) return 'bg-eeeeee'
      if (isUnusable) return 'bg-eeeeee'
      return 'bg-fff8f5'
    },
    faceValueClazz ({ isUsed, isFailed, isUnusable }) {
      if (isUsed || isFailed) return 'text-9'
      if (isUnusable) return 'text-6'
      return 'text-f44530'
    },
    typeTextClazz ({ isUsed, isFailed, isUnusable }) {
      if (isUsed || isFailed) return 'text-9'
      if (isUnusable) return 'text-3'
      return 'text-3'
    },
    btnStatus ({ isFailed }) {
      if (isFailed) return false
      return true
    },
    btnText({ isUsed, isUnusable, isSelected }) {
      if (isUsed) return '已使用'
      if (isUnusable) return '不可用'
      if (isSelected) return '使用中'
      return '去使用'
    },
    btnClazz ({ isUsed, isUnusable, isSelected }) {
      if (isUsed) return 'text-6 b-s-02-999999'
      if (isUnusable) return 'text-ffffff bg-d8d8d8 b-s-02-d8d8d8'
      if (isSelected) return 'text-ffffff bg-f44530 b-s-02-f44530'
      return 'text-f44530 b-s-02-e80404'
    },
    couponFaceValue ({ item }) {
      return parseFloat(item.coupon_face_value)
    },
    couponTypeText ({ item }) {
      const couponType = item.coupon_type
      const thresholdPrice = parseFloat(item.threshold_price)
      const fullDiscountText = `满${thresholdPrice}元可用`
      if ([1007, 1009, 2002].includes(couponType)) return '立减'
      if ([2003, 2004].includes(couponType)) return '折扣'
      if ([1008, 1010].includes(couponType)) return fullDiscountText
      switch (item.cp_id) {
        case 1:
          return '立减'
        case 2:
          return fullDiscountText
        default:
          return ''
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      uni.createSelectorQuery().in(this).select('.coupon-face-value').boundingClientRect(data => {
        this.faceValueIsRight = (data?.width || 0) > uni.upx2px(160)
      }).exec()
    })
  },
  methods: {
		toUse() {
      if (!this.isJump) return
			/*优惠券类型 
				1001 全场商品立减券,1002闪购立减,1003秒发立减券,1004全场商品满减券,
				1005闪购满减券，1006秒发满减券,1007指定商品立减券,1008指定商品满减券,
				1009指定活动立减券,1010指定活动满减券,2001酒会立减券,2002指定酒会立减券,
				2003酒会折扣券,2004指定酒会折扣券,3001运费减免券
			*/
			switch(this.item.coupon_type) {
				case 1002:
				case 1005:
        if (this.$app)
							wineYunJsBridge.openAppPage({
							client_path: { ios_path: 'goMain', android_path: 'goMain' },
							ad_path_param: [
								{
								ios_key: 'type',
								ios_val: '1',
								android_key: 'type',
								android_val: '1',
								},
							],
							})
						else this.jump.switchTab(this.$routeTable.pgFlashPurchase) //跳转闪购
				
				break
				case 1003:
				case 1006:
        if (this.$app)
							wineYunJsBridge.openAppPage({
							client_path: { ios_path: 'goMain', android_path: 'goMain' },
							ad_path_param: [
								{
								ios_key: 'type',
								ios_val: '3',
								android_key: 'type',
								android_val: '3',
								},
							],
							})
						else this.jump.switchTab(this.$routeTable.pgMiaoFa) //跳转秒发
				
				break
				case 1007:
				case 1008:
        if (this.$app)
        this.jump.appAndMiniJump(1, `${this.$routeTable.pgGoodsDetail}?id=${this.item.relation_id}`, this.$vhFrom)
						else this.jump.navigateTo(`${this.$routeTable.pgGoodsDetail}?id=${this.item.relation_id}`) //跳转指定商品
				
				break
				case 1009:
				case 1010:
				this.jump.h5Jump(this.item.relation_url) //跳转指定活动
				break
				case 2001:
				case 2003:
        if (this.$app)
        this.jump.appAndMiniJump(1, this.$routeTable.pDWinePartyBoutique, this.$vhFrom)
						else this.jump.navigateTo( this.$routeTable.pDWinePartyBoutique ) //跳转酒会列表
				
				break
				case 2002:
				case 2004:
        if (this.$app)
        this.jump.appAndMiniJump(1, `${this.$routeTable.pDWinePartyDetail}?id=${this.item.relation_id}`, this.$vhFrom)
						else this.jump.navigateTo(`${this.$routeTable.pDWinePartyDetail}?id=${this.item.relation_id}`) //跳转指定酒会
				
				break
				default:
        if (this.$app)
        wineYunJsBridge.openAppPage({
                client_path: { ios_path: 'goMain', android_path: 'goMain' },
              })
						else this.jump.switchTab(this.$routeTable.pgIndex) //不确定的都跳转首页
				
			}
		}
  },
}
</script>