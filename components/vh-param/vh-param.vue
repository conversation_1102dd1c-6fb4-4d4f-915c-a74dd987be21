<template>
	<view v-if="introduce && introduce !== '0' && introduce !== '0.00' " class="">
		<u-line color="#DDD" border-style="dashed"></u-line>
		<view v-if="layoutMode == 'vertical'" class="ptb-16-plr-00">
			<view class="font-30 text-9 l-h-50">{{title}}</view>
			<text class="mt-10 font-30 text-3 l-h-50">{{getIntroduce}}</text>
		</view>
		<view v-else class="d-flex ptb-16-plr-00">
			<text class="w-122 font-30 text-9 w-s-now l-h-50">{{title}}</text>
			<text class="flex-1 ml-30 font-30 text-3 l-h-50">{{getIntroduce}}</text>
		</view>
	</view>
</template>

<script>
	/**
	 * param 参数
	 * @description 此组件一般用于左标题 右介绍或者上标题下介绍的标题（商品详情中的商品参数板块、酒会详情中的活动详情板块）
	 * @property {String} layoutMode 布局方式
	 * @property {String} title 名称
	 * @property {String} introduce 介绍
	 * @property {String} show-line 是否显示虚线
	 * @example <vh-param></vh-param>
	 */
	export default {
		name: 'vh-param',
		props: {
			// 布局方式 horizontal = 横向布局（默认）、vertical = 纵向布局
			layoutMode: {
				type: String,
				default: 'horizontal'
			},
			// 名称
			title: {
				type: String, 
				default: ''
			},
			// 介绍
			introduce: {
				type: String, 
				default: ''
			}
		},
		
		computed:{
			// 获取介绍
			getIntroduce() {
				if( this.title == '酒精度' ) return this.introduce + '%vol（仅供参考）'
				else if( this.title == '残糖' ) return this.introduce.replace('g/L', '') + 'g/L'
				else return this.introduce
			}
		}
	}
</script>

<style scoped></style>
