<template>
  <view>
    <view class="font-wei-500 font-32 text-3">拍品详情</view>
    <view class="mt-20 w-b-b-w font-28 o-hid" style="line-height: 1.4;">
      <u-parse :html="goods.detail" :show-with-animation="true" style="user-select: auto;" />
    </view>
  </view>
</template>

<script>
export default {
  props: {
    goods: {
      type: Object,
      default: () => ({})
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
