<template>
	<view class="">
		<!-- 订单状态 -->
		<view class="d-flex j-sb a-center pt-32 pb-32 bb-s-01-eeeeee">
			<text class="font-24 text-3">{{ item.created_time }}</text>
			<template v-if="type === 0">
				<text v-if="item.status === 0" class="font-28 text-e80404">待付款</text>
				<text v-if="item.status === 1" class="font-28 text-e80404">待发货</text>
				<text v-if="item.status === 2" class="font-28 text-e80404">待收货</text>
				<text v-if="item.status === 3" class="font-28 text-3">已完成</text>
				<text v-if="item.status === 4" class="font-28 text-3">订单关闭</text>
				<text v-if="item.status === 5" class="font-28 text-e80404">待评价</text>
				<text v-if="item.status === 7" class="font-28 text-3">已退款</text>
			</template>
			<template v-if="type === 1">
				<text v-if="item.status === 0" class="font-28 text-2e7bff">待付款</text>
				<text v-if="item.status === 1" class="font-28 text-2e7bff">待发货</text>
				<text v-if="item.status === 2" class="font-28 text-2e7bff">待收货</text>
				<text v-if="item.status === 3" class="font-28 text-3">已完成</text>
				<text v-if="item.status === 4" class="font-28 text-3">订单关闭</text>
				<text v-if="item.status === 7" class="font-28 text-3">已退款</text>
			</template>
		</view> 
		<!-- 订单商品信息 -->
		<view class="bb-s-01-eeeeee pt-08 pb-28">
			  <view class="d-flex mt-20">
				<vh-image :loading-type="2" :src="item.goods_img" :width="160" :height="160" :border-radius="10" />
				<view class="flex-1 d-flex flex-column j-sb ml-12">
					<view class="">
						<view class="font-24 text-0 text-hidden-2">{{ item.goods_name }}</view>
						<view class="flex-e-c">
							<text class="font-24 text-9">x{{ item.order_qty }}</text>
						</view>
					</view>
					<view class="d-flex j-sb a-center">
						<AuctionOrderTypeName :auctionType="item.auction_type"/>
						<view class="text-3">
							<text class="font-18">成交价：</text>
							<text class="font-22 font-wei">¥</text>
							<text class="font-32 font-wei">{{ item.payment_amount }}</text>
						</view>
					</view>
				</view>
			  </view>
		</view>
	</view>
</template>

<script>
	export default{
		name: "AuctionOrderListItem",
		
		props: {
			// 类型 0 = 我拍到的、1 = 我卖出的 
			type: {
				type: [Number, String],
				default: 0
			},
			
			// 商品信息
			item : {
				type: Object,
				default: function() {
					return {};
				}
			},
		}
	}
</script>

<style>
</style>