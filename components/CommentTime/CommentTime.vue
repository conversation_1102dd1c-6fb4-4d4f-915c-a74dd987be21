<template>
  <view class="flex-sb-c">
    <text class="font-24 text-9 l-h-34">{{ item.comment_time }}</text>
    <view class="flex-c-c">
      <view class="w-138">
        <view class="flex-s-c" @click.stop="$emit('enjoy', item)">
          <image :src="ossIcon(`/comm/zan${item.like_status ? '_h' : ''}_26.png`)" class="w-26 h-26" />
          <text class="ml-06 font-28 text-9 l-h-40">{{ item.like_nums }}</text>
        </view>
      </view>

      <view class="flex-c-c" @click.stop="$emit('reply', item)">
        <image :src="ossIcon(`/comm/reply_26.png`)" class="w-26 h-26" />
        <text class="ml-06 font-28 text-9 l-h-40">回复</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
