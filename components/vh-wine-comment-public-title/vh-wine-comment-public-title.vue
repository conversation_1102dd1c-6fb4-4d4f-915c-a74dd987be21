<template>
	<view class="content">
		<view class="title">
			<vh-image :src="src" :loading-type="2" :width="width" :height="height" />
			<view class="title-text" :style="[textStyle]">{{ title }}</view>
		</view>
	</view>
</template>

<script>
	/**
	 * wine-comment-public-title 酒评通用标题
	 * @description 该组件用于酒评通用标题展示，左边为图标 右边为标题，方便用户风格统一，减少工作量（ 通用组件 ）
	 * @property {String} src 图片地址（默认''）
	 * @property {String Number} width 图片宽度（默认50）
	 * @property {String Number} height 图片高度（默认50）
	 * @property {String} title 标题（默认''）
	 * @example <vh-wine-comment-public-title />
	 */
	export default{
		name: "vh-wine-comment-public-title",
		
		props: {
			// 图片地址
			src: {
				type: String,
				default: ''
			},
			
			// 宽度
			width: {
				type: [Number, String],
				default: 50
			},
			
			// 宽度
			height: {
				type: [Number, String],
				default: 50
			},
			
			// 标题
			title: {
				type: String,
				default: ''
			},
			
			// 左边距
			marginLeft: {
				type: [Number, String],
				default: 20
			},
			
			// 字体大小
			fontSize: {
				type: [Number, String],
				default: 28
			},
			
			// 字体颜色
			color: {
				type: String,
				default: '#666'
			}
		},
		
		computed: {
			// 文本样式
			textStyle() {
				return {
					marginLeft: this.marginLeft + 'rpx',
					fontSize: this.fontSize + 'rpx',
					color: this.color
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.content {
		display: flex;
		align-items: center;
	}
	
	.title {
		display: flex;
		align-items: center;
		&-text {
			margin-left: 20rpx;
			font-size: 28rpx;
			color: #666;
			white-space: nowrap;
		}
	}
</style>