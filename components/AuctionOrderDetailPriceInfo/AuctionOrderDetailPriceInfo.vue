<template>
	<view class="bg-ffffff b-rad-16 mt-20 mr-24 ml-24 ptb-00-plr-20">
		<!-- 不支持七天无理由退货 -->
		<view v-if="![0,4].includes(orderInfo.status) && type == 0" class="flex-s-c bb-d-01-eeeeee ptb-32-plr-00">
			<view v-if="[1,2].includes(orderInfo.status)" class="">
				<view v-if="!orderInfo.refund_order_no && ((orderInfo.status === 1 && showAfterSaleBtn) || orderInfo.status === 2)" class="mr-20">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'108rpx', height:'42rpx', fontSize:'24rpx', fontWeight:'bold', color:'#999', border:'1rpx solid #999'}" 
					@click="afterSale">售后</u-button>
				</view>
				<view v-if="orderInfo.refund_order_no" class="mr-20">
					<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
					:custom-style="{width:'142rpx', height:'42rpx', fontSize:'24rpx', fontWeight:'bold', color:'#999', border:'1rpx solid #999'}" 
					@click="jump.navigateTo(`${routeTable.pHAuctionBuyerAfterSaleDetail}?refundOrderNo=${orderInfo.refund_order_no}`)">售后详情</u-button>
				</view>
			</view>
			<vh-image :loading-type="2" :src="ossIcon(`/auction_buyer_order_detail/mark.png`)" :width="28" :height="28" />
			<view class="ml-10 font-28 text-6">不支持七天无理由退货</view>
		</view>
		
		<!-- 商品总价 -->
		<view class="flex-sb-c pt-32">
			<text class="font-28 text-6">商品总价</text>
			<text class="font-28 text-3"><text class="font-22">¥</text>{{ orderInfo.payment_amount }}</text>
		</view>
		<!-- 运费 -->
		<view class="flex-sb-c pt-24 pb-34">
			<text class="font-28 text-6">运费</text>
			<text class="font-28 text-3"><text class="font-22">¥</text>0</text>
		</view>
		<!-- 合计 -->
		<view class="flex-sb-c bt-s-01-eeeeee ptb-32-plr-00">
			<text class="font-28 text-6">合计（含邮费）</text>
			<text class="font-32 text-e80404"><text class="font-22">¥</text>{{ orderInfo.payment_amount }}</text>
		</view>
	</view>
</template>

<script>
	import { mapState, mapMutations } from 'vuex'
	export default {
		props: {
			// 类型 0 = 我拍到的、1 = 我卖出的
			type: {
				type: [Number, String],
				default: 0
			},
			
			// 订单信息
			orderInfo: {
				type: Object,
				default: function() {
					return {};
				}
			}
		},
		
		computed: {
			//Vuex 辅助state函数
			...mapState(['routeTable']),
			
			// 是否展示售后按钮（支付后48小时后显示按钮）
			showAfterSaleBtn() {
			  const { payment_time } = this.orderInfo
			  let dateBegin  = new Date(payment_time.replace(/-/g, '/')) //支付时间
			  let dateEnd = new Date() //当前时间
			  let seconds = Math.floor((dateEnd.getTime() - dateBegin.getTime()) / 1000 ) //得到两个日期之间的秒数
			  // 172800
			  if(seconds > 172800) 
				  return true
			  else 
				  return false
			}
		},
		
		methods: {
			// Vuex mapMutations辅助函数
			...mapMutations('auction', ['muOrderDetailInfo']),
			
			// 售后
			afterSale() {
				this.muOrderDetailInfo(this.orderInfo)
				this.jump.navigateTo(this.routeTable.pHAuctionSelectService)
			},
		}
	}
</script>

<style>
</style>