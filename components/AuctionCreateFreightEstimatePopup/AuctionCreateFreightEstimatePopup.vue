<template>
  <u-popup :value="value" mode="bottom" width="100%" height="618rpx" border-radius="20" @input="onInput">
    <view class="p-rela pt-48">
      <image
        :src="ossIcon('/auction/close_54.png')"
        class="p-abso top-24 right-24 w-54 h-54"
        @click="onInput(false)"
      />
      <view class="font-wei-500 font-32 text-3 l-h-44 text-center">运费预估</view>
      <view class="mt-84 ptb-00-plr-48">
        <view class="flex-sb-c">
          <text class="font-28 text-3 l-h-40">选择发货地</text>
          <view class="d-flex a-center pl-32 pr-14 h-50 b-s-01-d8d8d8 b-rad-06" @click="popupVisible = true">
            <text class="font-24 l-h-34" :class="shipAddress === $options.data().shipAddress ? 'text-9' : 'text-3'">{{ shipAddress }}</text>
            <image :src="ossIcon('/invoices/arrow_d_20_12.png')" class="ml-32 w-20 h-12"></image>
          </view>
        </view>
        <view class="mt-48 h-02 bg-eeeeee"></view>
        <view class="flex-sb-c mt-28">
          <text class="font-28 text-3 l-h-40">预估运费（元）</text>
          <text class="font-wei-500 font-28 l-h-40" :class="minPrice && maxPrice ? 'text-e80404' : 'text-3'">{{ minPrice && maxPrice ? `¥${minPrice} ~ ¥${maxPrice}` : '-¥--' }}</text>
        </view>
        <view class="mt-40 h-02 bg-eeeeee"></view>
        <view class="mt-60 font-24 text-9 l-h-34">预估运费按照<text class="text-6">750ml</text>标准瓶重量进行计算，新疆、西藏、内蒙等偏远地区除外。</view>
      </view>
    </view>

    <AuctionCreateShipAddressPickerPopup v-model="popupVisible" @confirm="shipAddress = $event" />
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    }
  },
  data: () => ({
    shipAddress: '请选择',
    minPrice: '',
    maxPrice: '',
    popupVisible: false,
  }),
  watch: {
    shipAddress () {
      this.getFreightEstimate()
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    getFreightEstimate () {
      this.$u.api.getFreightEstimate({ from_addr: this.shipAddress }).then(res => {
        const { min_price = '', max_price = '' } = res.data
        this.minPrice = min_price
        this.maxPrice = max_price
      })
    }
  },
}
</script>