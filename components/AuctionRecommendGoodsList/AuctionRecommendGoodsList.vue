<template>
  <view v-if="list.length">
    <view class="flex-c-c">
        <image :src="ossIcon('/auction/recommend_36_38.png')" class="w-36 h-38" />
        <text class="ml-12 font-wei-600 font-36 text-3">热门推荐</text>
      </view>
      <view class="mt-50">
        <AuctionWGoodsList :list="list" :addTime="50" />
      </view>
  </view>
</template>

<script>
export default {
  props: {
    isInit: {
      type: Boolean,
      default: false
    }
  },
  data: () => ({
    list: []
  }),
  methods: {
    load () {
      this.$u.api.getAuctionHotRecommendGoodsList().then(res => {
        const { auction = [], second_periods = [] } = res?.data || {}
        auction.forEach(item => {
          item.$isShowPageviews = true
        })
        second_periods.forEach(item => {
          item.banner_img = item.banner_img && item.banner_img[0]
        })
        this.list = [...auction, ...second_periods]
      })
      // this.$u.api.getAuctionRecommendGoodsList().then(res => {
      //   const list = res?.data || []
      //   list.forEach(item => {
      //     item.detail = ''
      //   })
      //   this.list = list
      // })
    }
  },
  created () {
    if (this.isInit) this.load()
  }
}
</script>

<style lang="scss" scoped>
</style>
