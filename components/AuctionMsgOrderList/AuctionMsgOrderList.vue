<template>
  <view class="ptb-00-plr-24 pb-20">
    <view v-for="({ isRead, createTime, notice_data }, index) in list" :key="index" class="mt-20 ptb-28-plr-20 bg-ffffff" @click="onJump(notice_data)">
      <view class="flex-sb-c">
        <view class="flex-c-c">
          <image :src="ossIcon('/auction/order_64.png')" class="w-64 h-64"></image>
          <text class="font-wei-500 font-28 text-6">订单</text>
        </view>
        <view class="flex-c-c">
          <view v-if="!isRead" class="mr-10 w-16 h-16 bg-e80404 b-rad-14"></view>
          <text class="font-24 text-9">{{ createTime }}</text>
        </view>
      </view>
      <view v-html="notice_data.content" class="mt-20 font-28 text-3 l-h-40 text-justify w-b-b-w"></view>
      <view class="mt-12 h-02 bg-eeeeee"></view>
      <view class="mt-20">
        <view class="flex-sb-c">
          <vh-image :loading-type="4" :src="notice_data.product_img" :width="120" :height="120" :border-radius="6" />
          <view class="w-522">
            <view class="font-24 text-6 l-h-34">{{ notice_data.title }}</view>
            <view class="flex-s-c mt-10 font-24 text-6 l-h-34">
              <template v-if="notice_data.category_name">
                <text>{{ notice_data.category_name }}</text>
                <text class="ml-06 mr-06 font-28 text-d8d8d8 l-h-40">/</text>
              </template>
              <template v-if="notice_data.net_content">
                <text>{{ notice_data.net_content }}ml</text>
                <text class="ml-06 mr-06 font-28 text-d8d8d8 l-h-40">/</text>
              </template>
              <text v-if="notice_data.payment_amount">¥{{ notice_data.payment_amount }}</text>
            </view>
          </view>
        </view>
        <view v-if="notice_data.order_no" class="mt-20 font-24 text-6">订单号：{{ notice_data.order_no }}</view>
        <!-- <view class="mt-10 font-24 text-6">订单状态：<text v-html="notice_data.status"></text></view> -->
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    onJump (data) {
      const { order_no, type } = data
      const { pHAuctionOrderDetail, pHAuctionMyCreateList } = this.routeTable
      if ([111, 112].includes(+type)) {
        if (this.$app) {
            wineYunJsBridge.openAppPage({
              client_path: { "ios_path": "MyPostedAuctionViewController", "android_path": "com.stg.rouge.activity.MySendAuctionActivity" },
              ad_path_param: [
                { 
                  "ios_key":"login", "ios_val":"1",
                  "android_key":"login", "android_val":"1"
                }
              ]
            })
        } else {
          this.jump.navigateTo(pHAuctionMyCreateList)
        }
        return
      }
      if ([110, 111, 112, 113, 114, 115, 116].includes(+type)) return
      if (order_no) {
        this.jump.navigateTo(`${pHAuctionOrderDetail}?orderNo=${order_no}`)
      }
    }
  },
}
</script>

<style lang="scss" scoped>
</style>
