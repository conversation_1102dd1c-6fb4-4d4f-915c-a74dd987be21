<template>
	<view class="d-flex j-center" :style="[outerEmpConStyle]">
		<view class="p-rela" :style="[innEmpConStyle]">
			<image class="w-p100 h-p100" :src="imageSrc" mode="aspectFill"></image>
			<view class="p-abso w-p100 d-flex j-center font-28 text-9" :style="[textStyle]">{{text}}</view>
		</view>
	</view>
</template>

<script>
	/**
	 * empty 缺省页
	 * @description 该组件一般用于无数据展示。
	 * @property {String} bg-color 缺省背景颜色（默认白色）
	 * @property {String Number} padding-top 缺省页容器上方内边距，单位rpx（默认0）
	 * @property {String Number} padding-bottom 缺省页容器下方内边距，单位rpx（默认0）
	 * @property {String Number} width 缺省页容器宽度，单位rpx（默认440）
	 * @property {String Number} height 缺省页容器高度，单位rpx（默认360）
	 * @property {String} image-src 图片路径 (默认'')
	 * @property {String Number} text-bottom 文字距离容器底部的距离，单位rpx（默认46）
	 * @property {String} text 缺省页文字描述（默认''）
	 * @example <vh-empty width="440" height="360"></vh-empty>
	 */
	export default{
		name:"vh-empty",
		
		props: {
			// 缺省背景颜色
			bgColor: {
				type: String,
				default: '#FFF'
			},
			// 缺省页容器上方内边距
			paddingTop: {
				type: [String, Number],
				default: 0
			},
			// 缺省页容器下方内边距
			paddingBottom: {
				type: [String, Number],
				default: 0
			},
			// 缺省页容器宽度
			width: {
				type: [String, Number],
				default: 440
			},
			// 缺省页容器高度
			height: {
				type: [String, Number],
				default: 360
			},
			// 图片路径
			imageSrc: {
				type: String,
				default: ''
			},
			// 文字距离容器底部的距离
			textBottom: {
				type: [String, Number],
				default: 46
			},
			// 缺省页文字描述
			text: {
				type: String, 
				default: ''
			},
		},
		computed: {
			// 外层缺省页容器样式
			outerEmpConStyle(){
				return{
					backgroundColor: this.bgColor,
					paddingTop: this.paddingTop + 'rpx',
					paddingBottom: this.paddingBottom + 'rpx'
				}
			},
			
			// 内层缺省页容器样式
			innEmpConStyle() {
				return {
					width: this.width + 'rpx',
					height: this.height + 'rpx',
				}
			},
			
			// 文字样式
			textStyle(){
				return {
					bottom: this.textBottom + 'rpx',
				}
			}
		}
	}
</script>

<style scoped>
</style>
