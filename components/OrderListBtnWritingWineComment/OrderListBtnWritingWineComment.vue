<template>
	<view v-if="!item.is_comment" class="p-rela ml-20">
		<u-button shape="circle" :hair-line="false" :ripple="true" ripple-bg-color="#FFF"
		:custom-style="{width:'148rpx', height:'52rpx', fontSize:'24rpx', color:'#E80404', border:'1rpx solid #E80404'}" 
		@click="click">写酒评</u-button>
		
		<view class="p-abso top-n-24 right-0 w-86 h-42"
		@click.stop="click">
			<view class="p-abso z-04 top-02 w-86 h-42 d-flex j-center font-18 text-ffffff">+兔头{{ rabbitHeadCount || '' }}</view>
			<image class="p-abso z-03 w-86 h-42" :src="ossIcon(`/my_order/wine-comm_bg.png`)" mode=""></image>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'OrderListBtnWritingWineComment',
		props: {
			// 订单信息
			item : {
				type: Object,
				default: () => ({})
			},
			
			// 兔头数量
			rabbitHeadCount: {
				type: [Number, String],
				default: 0
			}
		},
	
		methods: {
			click() {
				this.$emit('click')
			}
		}
	}
</script>

<style>
</style>