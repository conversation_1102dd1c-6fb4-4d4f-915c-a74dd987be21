<template>
  <u-popup :value="value" mode="center" :mask-close-able="false" width="552" height="452" border-radius="20" @input="onInput">
    <view class="p-rela wh-p100">
      <image :src="ossIcon('/auction/popup_bg_552_444.png')" class="p-abso wh-p100" />
      <view class="p-rela pt-84">
        <view class="font-wei-500 font-32 text-3 l-h-44 text-center">温馨提示</view>
        <view class="mt-24 ptb-00-plr-50 font-28 text-3 l-h-40 text-center">您有一张29元保证金低扣券，点击使用即刻参拍～</view>
        <view class="flex-sb-c ptb-00-plr-76 mt-72">
          <button class="vh-btn flex-c-c w-160 h-64 font-wei-500 font-28 text-6 bg-ffffff b-s-02-666666 b-rad-32" @click="$emit('cancel')">取消</button>
          <button class="vh-btn flex-c-c w-180 h-64 font-wei-500 font-28 text-ffffff bg-e80404 b-rad-32" @click="$emit('use')">立即使用</button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    }
  },
  data: () => ({
  }),
  computed: {
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
