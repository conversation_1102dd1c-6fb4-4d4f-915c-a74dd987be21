<template>
  <u-popup :value="value" mode="center" width="540rpx" height="336rpx" border-radius="20" @input="onInput">
    <view class="p-rela pt-48 wh-p100">
      <view class="font-wei-600 font-30 text-3 text-center l-h-42">温馨提示</view>
      <view class="mtb-00-mlr-auto mt-32 w-444 font-28 text-3 text-center l-h-40">
        收货地址仅可修改一次，您是否确认修改？
      </view>
      <view class="p-abso bottom-0 flex-c-c w-p100 h-86 bt-s-02-eeeeee">
        <view class="p-abso w-02 h-p100 bg-eeeeee"></view>
        <button class="vh-btn flex-c-c w-p50 h-p100 font-30 text-9 bg-ffffff" @click="onInput(false)">取消</button>
        <button class="vh-btn flex-c-c w-p50 h-p100 font-wei-500 font-30 text-e80404 bg-ffffff" @click="onConfirm">确认修改</button>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: true
    },
    orderDetail: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    onInput (value) {
      this.$emit('input', value)
    },
    onConfirm () {
      const { order_no, order_type } = this.orderDetail
      const comeFrom = order_type === 11 ? 12 : 11
      this.$u.api.updateOrderAddressValidation({ sub_order_no: order_no, order_type }).then(() => {
        this.jump.navigateTo(`${this.$routeTable.pEAddressManagement}?comeFrom=${comeFrom}&subOrderNo=${order_no}&orderType=${order_type}`)
      })
      this.onInput(false)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
