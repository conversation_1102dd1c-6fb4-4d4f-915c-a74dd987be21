<template>
  <view class="agoods-list">
    <view class="agoods-list__content">
      <view class="agoods-list__title">酒款列表</view>
      <view class="agoods-list__inner">
        <view v-for="item in list" :key="item.id" class="agoods-list__item" @click="onJump(item)">
          <view class="agoods-list__ileft">
            <vh-image :loading-type="2" :src="item.banner_img" :height="180" />
          </view>
          <view class="agoods-list__iright">
            <text class="agoods-list__ititle">{{ item.title }}</text>
            
            <view class="agoods-list__irbottom">
              <text class="agoods-list__iprice"><text>{{ item.is_hidden_price || [3, 4].includes(item.onsale_status) ? '' : '¥' }}</text>{{ item.is_hidden_price || [3, 4].includes(item.onsale_status) ? '价格保密' : item.price }}</text>
              
              <view class="agoods-list__iquota">
                <text v-if="item.periods_type === 1">已售<text>{{ item.purchased + item.vest_purchased }}</text></text>
                <text v-else>已售<text>{{ item.purchased + item.vest_purchased }}
                </text>/限量<text>{{ item.limit_number }}</text>{{ item.quota_rule.quota_number == '9999'? '' : '/限购' }}
                <text>{{ item.quota_rule.quota_number == '9999' ? '' : item.quota_rule.quota_number }}</text></text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
	import { MINI_GOODS_DETAIL_URL } from '@/common/js/fun/constant'

export default {
  name: 'ActivityGoodsList',
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    ...mapState(['routeTable'])
  },
  methods: {
    onJump (item) {
      const { path, options } = this.pages.getCurrenPage().$page
      if (['/pages/activity/activity'].includes(path)) {
        let sourceStr = Object.keys(options)
          .filter(key => key.includes('source_'))
          .map(key => `${key}=${options[key]}`)
          .join('&')
        sourceStr = sourceStr && `&${sourceStr}`
        window.open(`${MINI_GOODS_DETAIL_URL}&id=${item.goods_id}${sourceStr}`)
      } else {
        this.jump.navigateTo(`${this.routeTable.pgGoodsDetail}?id=${item.goods_id}`)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .agoods-list {
    background: #FFF;
    border-radius: 20rpx;

    &__content {
      padding: 32rpx 24rpx;
    }

    &__title {
      @include font(600, 36rpx, #333);
      line-height: 50rpx;
    }

    &__inner {
      margin: 32rpx 0 0 0;
    }

    &__item {
      @include flex-row(center, stretch);

      &:not(:first-of-type) {
        margin: 20rpx 0 0 0;
      }
    }

    &__ileft {
      flex-shrink: 0;
      @include size(288rpx, 180rpx);
      border-radius: 6rpx;
      overflow: hidden;
    }

    &__iright {
      flex: 1;
      @include flex-col(space-between, stretch);
      margin: 0 0 0 20rpx;
    }
    
    &__ititle {
      @include font(400, 24rpx, #333);
      line-height: 34rpx;
      @include line(3);
    }

    &__irbottom {
      @include flex-row(space-between);
    }

    &__iprice {
      @include font(400, 32rpx, #FF0013);
      line-height: 28rpx;

      text {
        font-size: 20rpx;
      }
    }

    &__iquota {
      @include font(400, 22rpx, #999);
      line-height: 32rpx;
    }
  }
</style>
