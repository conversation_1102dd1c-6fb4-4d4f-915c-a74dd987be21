<template>
	<!-- 拍卖 -->
	<view class="bg-ffffff d-flex p-24" @click="jump.navigateTo(`${$routeTable.pHAuctionGoodsDetail}?id=${item.period}`)">
		<vh-image :src="item.banner_img && item.banner_img[0]" :loading-type="2" :width="152" :height="152" :border-radius="6" />
		<view class="flex-1 flex-sb-n-c ml-20">
			<view class="text-hidden-2">
				<text class="bg-fde8eb b-rad-04 ptb-04-plr-08 font-18 text-e80404">拍品</text>
				<text class="ml-08 font-24 font-wei text-0 l-h-34">{{item.title}}</text>
			</view>
			<view class="flex-s-c">
				<text class="font-24 text-6">起拍价</text>
				<text class="ml-06 font-32 text-3"><text class="font-24">¥</text>{{item.price}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default: () => ({})
			},
		}
	}
</script>