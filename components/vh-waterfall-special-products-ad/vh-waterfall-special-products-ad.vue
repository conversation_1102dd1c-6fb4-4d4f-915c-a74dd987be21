<template>
	<view class="" @click="onClick">
		<vh-image :src="item.image" :height="214" />
		<view class="ptb-18-plr-16">
			<view class="" @longpress.stop="handleLongpress(item.modul_data.title, from)" @touchend="handleTouched(from)">
				<vh-channel-title-icon :channel="item.modul_data.periods_type" :font-size="18" :font-bold="false" />
				<text class="ml-08 font-24 font-wei text-3 l-h-30">{{item.title}}</text>
			</view>
			
			<view class="font-22 text-9 l-h-34 text-hidden-2">{{item.modul_data.brief}}</view>
			
			<view class="mt-14 d-flex j-sb a-center">
				<text v-if="item.modul_data.is_hidden_price == 1 || [3, 4].includes(item.modul_data.onsale_status)" class="font-28 font-wei text-e80404 l-h-44">价格保密</text>
				<text v-else class="font-28 font-wei text-e80404 l-h-44"><text class="font-18">¥</text>{{item.modul_data.price}}</text>
				<!-- 2022-07-19 秒发列表只显示已售 需求方：杨文科 -->
				<text v-if="item.modul_data.periods_type == 1" class="font-18 text-9 l-h-36">已售<text class="text-e80404">{{item.modul_data.purchased + item.modul_data.vest_purchased}}</text></text>
				<text v-else class="font-18 text-9 l-h-36">已售<text class="text-e80404">{{item.modul_data.purchased + item.modul_data.vest_purchased}}</text>/限量<text class="text-e80404">{{item.modul_data.limit_number}}</text></text>
			</view>
		</view>
	</view>
</template>

<script>
	import longpressCopyMixin from '@/common/js/mixins/longpressCopyMixin'
	import adBuryDotMixin from '@/common/js/mixins/adBuryDotMixin'
	import { mapState } from 'vuex'
	/**
	 * waterfall-special-products-ad 商品广告
	 * @description 该组件一般用于拥有商品广告位的列表
	 * @property {String Number} from 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
	 * @property {Object} item 商品广告的每一项
	 * @event {Function} click 点击组件时触发
	 * @example <vh-waterfall-special-products-ad />
	 */
	export default {
		name:'vh-waterfall-special-products-ad',
		
		props: {
			// 来自哪个端 '' = H5或者小程序 1 = 安卓、2 = ios
			from: {
				type: [String, Number],
				default: ''
			},
			
			// 商品广告每一项
			item: {
				type: Object,
				default() {
					return {}
				}
			},
		},

		mixins: [longpressCopyMixin, adBuryDotMixin],
		
		computed:{
			//Vuex 辅助state函数
			...mapState(['routeTable']),
		},

		methods: {
			onClick () {
				if (this.jumpStatus) return
				if (this.buryDotParams) {
					const { channel, region_id } = this.buryDotParams
					const genre = 3
					const button_id = this.item.id
					this.jump.appAndMiniJumpBD(1, `${this.routeTable.pgGoodsDetail}?id=${this.item.modul_data.id}`, channel, genre, region_id, button_id, this.from)
				} else {
					this.jump.appAndMiniJump(1, `${this.routeTable.pgGoodsDetail}?id=${this.item.modul_data.id}`, this.from)
				}
			}
		}
	}
	
</script>

<style scoped></style>
