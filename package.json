{"name": "", "version": "1.0.0", "description": "", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "uni-app": {"scripts": {"h5:test": {"BROWSER": "<PERSON>rm<PERSON>", "title": "build:test", "env": {"UNI_PLATFORM": "h5", "ENV_TYPE": "test"}}, "h5:dev": {"BROWSER": "<PERSON>rm<PERSON>", "title": "build:dev", "env": {"UNI_PLATFORM": "h5", "ENV_TYPE": "dev"}}}}, "dependencies": {"axios": "^1.7.1", "crypto-js": "^4.2.0", "sjcl": "^1.0.8", "twemoji": "^14.0.2", "uqrcodejs": "^4.0.6", "uview-ui": "^1.8.4", "vconsole": "^3.14.6", "vinehoo-v3-api-sign": "^1.3.2", "weixin-js-sdk": "^1.6.0"}}