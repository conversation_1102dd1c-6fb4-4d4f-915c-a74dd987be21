// vue全局过滤器
import Vue from 'vue'
import * as mappers from '../utils/mapper'

const toDate = (timestamp, fmt = 'yyyy-MM-dd HH:mm:ss') => {
	if (!timestamp) return ''
	const tempTimestamp = typeof timestamp === 'string' ? timestamp.replace(/-/g, '/') : timestamp
	return Vue.prototype.$u.timeFormat(tempTimestamp, fmt)
}

// 转换成千 num = 数量 （ 超过 1000保留单位k）
let numToThousands = ( num ) => {
	const limitNum = 1000
	if( num >= limitNum ) {
		return parseFloat((num / limitNum).toFixed(1)) + 'k+'
	}
	return num
}

const toText = (input, type) => {
  const mapper = mappers[type]
  if (!mapper) return ''
  
  const result = mapper.find(obj => obj.value === input)
  return result && result.text
}

let numMaxTwo = ( num ) => {
	if( num >= 99 ) {
		return 99
	}
	return num
}
//将时间戳转为年月日
let formatDate = (date) => {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	return `${year}-${month}-${day}`;
}

let dateToText = (date) => {
	const today = new Date();
	  const yesterday = new Date();
	  yesterday.setDate(yesterday.getDate() - 1);
	  const twoDaysAgo = new Date();
	  twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
	  const oneDay = 24 * 60 * 60 * 1000
	  const SevenDaysAgo = new Date(Date.now() - 7 * oneDay)
	  
	  const formattedDate = date;
	  const formattedToday = formatDate(today);
	  const formattedYesterday = formatDate(yesterday);
	  const formattedTwoDaysAgo = formatDate(twoDaysAgo);
	  const formattedSevenDaysAgo = formatDate(SevenDaysAgo);
	
	  if (formattedDate === formattedToday) {
	    return '今天';
	  } else if (formattedDate === formattedYesterday) {
	    return '昨天';
	  } else if (formattedDate === formattedTwoDaysAgo) {
	    return '前天';
	  } else if(new Date(formattedDate).getTime() < SevenDaysAgo.getTime()){
	    return '七天前';
	  }else{
		  return formattedDate
	  }
}

//将年月日转为月日
let monthDay = (date) => {
  if (!['今天', '昨天', '七天前'].includes(date)) {
    const dateNum = new Date(date);
    const month = dateNum.getMonth() + 1; // 月份从0开始，需要加1
    const day = dateNum.getDate();
    return `${month}月${day}日`;
  } else {
    return date;
  }
};

// 米转公里
const metreToKilometredistanceFilter = (value) => {
	if( value < 1000 ) {
		return value.toFixed(0) + '米'
	}else {
		return (value / 1000).toFixed(0) + '公里';
	}
}

let filters = { 
	numToThousands, 
	toDate, 
	toText, 
	numMaxTwo, 
	dateToText, 
	monthDay,
	metreToKilometredistanceFilter
}

// 导出方法
export default { ...filters } //过滤器

export function registerFilters(Vue) { //注册全局过滤器
  Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key])
  })
}