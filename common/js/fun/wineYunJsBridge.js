var wineYunJsBridge = new Object();
// 分享 data例如: {"link": "分享地址", "title": "分享标题", "desc": "分享描述内容", "image": "分享图片地址"} 
wineYunJsBridge.share = function(data) {
    this.postMessage(data, 'share')
}

// 跳转页面 data例如: {"client_path": "goodsDetail", "param": {"id": 123}}
wineYunJsBridge.openAppPage = function(data) {
    this.postMessage(data, 'openAppPage')
}

// 从App获取数据 data例如: {"client_path": "goodsDetail", "param": {"id": 123}}
 
wineYunJsBridge.getDataFromApp = function(data){
	 return this.postMessage(data, 'getDataFromApp')
}

wineYunJsBridge.setDataFromApp = function(data){
    return this.postMessage(data, 'setDataFromApp')
}

// 跟app做数据交互
wineYunJsBridge.postMessage = function (data, fn) {
    let jsonStr = JSON.stringify(data);
	let str = ''
    if(!fn){
        return str;
    }
    try {
        if(wineYunJs && typeof wineYunJs[fn] == 'function'){
           str = wineYunJs[fn](jsonStr);
        }
    } catch (error) {
        console.log(error)
    }
    try {
        window.webkit.messageHandlers[fn].postMessage(jsonStr)
    } catch(error) {
        console.log(error)
    }
	
	 return str;
}

export default wineYunJsBridge;