import Vue from 'vue'

// 常量工具
const BUSI_GET_ICON_API_URL = '/api/vmall_distance/v3/goods/distance' //商家标签API接口
const OSIP = 'https://images.vinehoo.com/vinehoomini/v3' //oss静态图片前缀
const AUCTION_SOCKET_URL = () =>
  Vue.prototype.$isDev ? 'wss://test-wine.wineyun.com/auction-socket' : 'wss://callback.vinehoo.com/auction-socket'
const WX_APPID_DEV = 'wx28fd6c2f63a885b1'
const WX_APPID_PROD = 'wxe9524fcb47c2e4c9'
const CHOOSE_WINE_ACTIVITY_ID = () => (Vue.prototype.$isDev ? 37 : 39)
const LIQUOR_ACTIVITY_ID = () => (Vue.prototype.$isDev ? -1 : -1)
const AUCTION_PATH_PREFIX = '/packageH'
const AUCTION_SOURCE = {
  platform: 'auction',
  event: 'newuser',
}
const STORE_URL = 'https://cloud1-3gln8vakfeab2b40-1318265780.tcloudbaseapp.com/index.html'
// https://test-activity.wineyun.com/activities-v3/newPeopleArea
const NEW_PEOPLE_ACTIVITY_URL = () =>
  Vue.prototype.$isDev
    ? 'https://test-activity.wineyun.com/activities-v3/newPeopleArea'
    : 'https://activity.vinehoo.com/activities-v3/newPeopleArea'
const USER_RETURNING_URL = () =>
  `${
    Vue.prototype.$isDev ? 'https://test-activity.wineyun.com' : 'https://activity.vinehoo.com'
  }/activities-v3/userReturning`
const MINI_GOODS_DETAIL_URL = `https://cloud1-9gkbx77e27bcf5b3-1310518333.tcloudbaseapp.com/common-path.html?cloudMiniPath=/pages/goods-detail/goods-detail`
// 导出方法
export {
  OSIP,
  BUSI_GET_ICON_API_URL,
  AUCTION_SOCKET_URL,
  WX_APPID_DEV,
  WX_APPID_PROD,
  CHOOSE_WINE_ACTIVITY_ID,
  LIQUOR_ACTIVITY_ID,
  AUCTION_PATH_PREFIX,
  AUCTION_SOURCE,
  STORE_URL,
  NEW_PEOPLE_ACTIVITY_URL,
  USER_RETURNING_URL,
  MINI_GOODS_DETAIL_URL,
}
