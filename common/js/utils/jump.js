// 处理一些公共跳转方法
import { urlPattern } from './pattern'

const install = (Vue, vm) => {
	// 返回 delta = 返回页面层级 默认为一层
	let navigateBack = ( delta ) => {
		uni.navigateBack({
			delta
		})
	}
	
	// 保留当前页面进行跳转 url = 跳转的路径（url可带参数）
	let navigateTo = ( url ) => {
		const isUrl = urlPattern.test(url)
		if (isUrl) {
			window.open(url)
			return
		}
		uni.navigateTo({ 
			url 
		})
	}
	
	// 关闭当前页面进行跳转 url = 跳转的路径（url可带参数）
	let redirectTo = ( url ) => {
		uni.redirectTo({ 
			url 
		})
	}
	
	// 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面 url = 跳转的路径（url不可带参数）
	let switchTab = ( url ) => {
		uni.switchTab({
			url
		})
	}
	
	// 关闭所有页面，打开到应用内的某个页面 url = 跳转的路径（url可带参数）
	let reLaunch = ( url ) => {
		uni.reLaunch({
			url
		})
	}
	
	// 保留当前页面登录后进行跳转,若未登录，则先去登录页面 url = 跳转的路径（url可带参数）
	let loginNavigateTo = ( url ) => {
		if(vm.login.isLogin()) {
			vm.jump.navigateTo( url )
		}
	}
	
	// app跳转网页&小程序跳转小程序跳转 type = 网页类型（ 0 = 全屏、1 = 状态栏app写、2 = 标题栏+状态栏app写 ）、
	// path = 路径、from = 来自哪个端、
	// jumpType = 跳转类型（ 0 = navigateTo、1 = redirectTo、2 = switchTab、3 = reLaunch）
	// checkLogin 是否需要验证登录 false = 不需要、 true = 需要
	let appAndMiniJump = ( type, path, from = '', jumpType = 0, checkLogin = false ) => {
		console.log(path)
		if(vm.comes.isFromApp(from)) {
			if(from == 'next'){
				if( jumpType > 0 ) { //app需要关闭当前页跳转
					wineYunJsBridge.openAppPage({
						client_path: { "ios_path":"finish", "android_path":"finish" }
					})
				}
			}
			if(type == 0) { // 全屏
			    let client_path = { "ios_path":"GeneralWebViewController", "android_path":"com.stg.rouge.webview.WebFullActivity" } //客户端路径
			    let ad_path_param = [] //客户端额外参数
				ad_path_param = [
					{
						"ios_key":"url", "ios_val": path,  
						"android_key":"url", "android_val": path 
					},
					{ 
						"ios_key":"isHideNav", "ios_val": "yes",
						"android_key":"NullKey", "android_val": ""
					},
					{
						"ios_key":"isStatusBar", "ios_val": "yes",
						"android_key":"NullKey", "android_val": ""
					}
				]
				if( checkLogin ) { //需要检查登录
					ad_path_param.unshift({
						"ios_key":"login", "ios_val":"1",
						"android_key":"login", "android_val":"1"
					})
				}
				console.log(client_path)
				console.log(ad_path_param)
				wineYunJsBridge.openAppPage({ client_path, ad_path_param }); //跟app做交互
			}else if( type == 1 ) { // 1 = 状态栏app写
				let client_path = { "ios_path":"GeneralWebViewController", "android_path":"com.stg.rouge.webview.WebActivity" } //客户端路径
			    let ad_path_param = [] //客户端额外参数
				ad_path_param = [
					{ 
						"ios_key":"url", "ios_val": path,  
						"android_key":"url", "android_val": path 
					},
					{
						"ios_key":"isHideNav", "ios_val": "yes", //是否交给网页端控制标题栏
						"android_key":"isHideTitle", "android_val": "1" 
					},
					{
						"ios_key":"isStatusBar", "ios_val": "no", //是否交给网页端控制状态栏
						"android_key":"NullKey", "android_val": ""
					}
				]
				if( checkLogin ) { //需要检查登录
					ad_path_param.unshift({
						"ios_key":"login", "ios_val":"1",
						"android_key":"login", "android_val":"1"
					})
				}
				console.log(client_path)
				console.log(ad_path_param)
				wineYunJsBridge.openAppPage({ client_path, ad_path_param }); //跟app做交互
				
			} else if( type == 2 ) { //2 = 标题栏+状态栏app写
				let client_path = { "ios_path":"GeneralWebViewController", "android_path":"com.stg.rouge.webview.WebActivity" } //客户端路径
			    let ad_path_param = [] //客户端额外参数
				ad_path_param = [
					{ 
						"ios_key":"url", "ios_val": path,  
						"android_key":"url", "android_val": path 
					},
					{
						"ios_key":"isHideNav", "ios_val": "no",//是否交给网页端控制标题栏
						"android_key":"NullKey", "android_val": ""
					},
					{
						"ios_key":"isStatusBar", "ios_val": "no",//是否交给网页端控制状态栏
						"android_key":"NullKey", "android_val": ""  
					}
				]
				if( checkLogin ) { //需要检查登录
					ad_path_param.unshift({
						"ios_key":"login", "ios_val":"1",
						"android_key":"login", "android_val":"1"
					})
				}
				console.log(client_path)
				console.log(ad_path_param)
				wineYunJsBridge.openAppPage({ client_path, ad_path_param }); //跟app做交互
				
			} 
			if(from != 'next'){
				if( jumpType > 0 ) { //app需要关闭当前页跳转
					wineYunJsBridge.openAppPage({
						client_path: { "ios_path":"finish", "android_path":"finish" }
					})
				}
			}
			
		}else{
			console.log('appAndMiniJump')
			// switch(jumpType) {
			// 	case 0:
			// 	vm.jump.navigateTo(path)
			// 	break
			// 	case 1:
			// 	vm.jump.redirectTo(path)
			// 	break
			// 	case 2:
			// 	vm.jump.switchTab(path)
			// 	break
			// 	case 3:
			// 	vm.jump.reLaunch(path)
			// 	break
			// }
			if(checkLogin){
             if(vm.login.isLogin()) {
                 switch(jumpType) {
                     case 0:
                     vm.jump.navigateTo(path)
                     break
                     case 1:
                     vm.jump.redirectTo(path)
                     break
                     case 2:
                     vm.jump.switchTab(path)
                     break
                     case 3:
                     vm.jump.reLaunch(path)
                     break
                 }
             }
            } else{
             switch(jumpType) {
                 case 0:
                 vm.jump.navigateTo(path)
                 break
                 case 1:
                 vm.jump.redirectTo(path)
                 break
                 case 2:
                 vm.jump.switchTab(path)
                 break
                 case 3:
                 vm.jump.reLaunch(path)
                 break
             }
            }
		}
	}
	
	// app跳转h5&小程序跳转h5 
	// h5Path = h5路径、
	// from = 来自哪个端
	// jumpType = 跳转类型（ 0 = navigateTo、1 = redirectTo、2 = switchTab、3 = reLaunch）
	let h5Jump = ( h5Path, from = '', jumpType = 0 ) => {
		console.log(h5Path)
		if(vm.comes.isFromApp( from )) {
			wineYunJsBridge.openAppPage({
				client_path: { "ios_path":"GeneralWebViewController", "android_path":"com.stg.rouge.webview.WebActivity" },
				ad_path_param: [
					{ 
						"ios_key":"url", "ios_val": h5Path,  
					    "android_key":"url", "android_val": h5Path 
					},
					{
						"ios_key":"isHideNav", "ios_val": "no",//是否交给网页端控制标题栏
						"android_key":"NullKey", "android_val": ""
					},
					{
						"ios_key":"isStatusBar", "ios_val": "no",//是否交给网页端控制状态栏
						"android_key":"NullKey", "android_val": ""  
					}
				]
			})
		}else{
			console.log('h5Jump')
			let path = `${vm.$store.state.routeTable.pFWebView}?url=${encodeURIComponent(h5Path)}`
			switch(jumpType) {
				case 0:
				vm.jump.navigateTo(path)
				break
				case 1:
				vm.jump.redirectTo(path)
				break
				case 2:
				vm.jump.switchTab(path)
				break
				case 3:
				vm.jump.reLaunch(path)
				break
				case 4:
					window.open(h5Path)
			}
		}
	}
	
	// 通用配置跳转 item = 列表某一项、from = 来自哪个端
	let pubConfJump = ( item, from = '' ) => {
		console.log('110-----', item)
		console.log(from)
		let { client_path, ad_path_param } = item //配置跳转参数（客户端路径、额外参数）
		if(vm.comes.isFromApp(from)) {
			wineYunJsBridge.openAppPage({ client_path, ad_path_param })
		}else{
			if(!client_path.h5_path) return vm.feedback.toast({ title:'请配置跳转路径~' })
			let path = client_path.h5_path //页面路径
			switch( path ) {
				 case 'noThatFunction': //app有但小程序或h5不支持的功能
				 vm.feedback.toast({ title: '请前往APP查看此功能~'})
				 break
				 case 'wechatCustomerService': //微信客服
				 vm.system.customerService()
				 break
				 default: //默认路径跳转
				 let paramList = ad_path_param //页面路径额外参数列表
				 let param = {} //额外参数对象
				 paramList.forEach( v => { if ( v.h5_key && v.h5_val && v.h5_key !== 'url' ) param[v.h5_key] = v.h5_val })
				 let fullUrl = path + vm.$u.queryParams(param)
				 const findParam = paramList.find(({ h5_key }) => h5_key === 'url')
				 if (findParam) {
					const queryParams = vm.$u.queryParams(param)
					fullUrl = `${findParam.h5_val}${queryParams ? `${findParam.h5_val.includes('?') ? `&` : '?'}${queryParams.slice(1)}` : ''}`
				 }
				//  if(path.indexOf('https') > -1) { //跳转h5
				// 	 vm.jump.navigateTo(`${vm.$store.state.routeTable.pFWebView}?url=${encodeURIComponent(fullUrl)}`)
				//  }else{ //跳转小程序或者h5
				//   let needLogin = paramList.find(item => item.h5_key === 'login' && item.h5_val === '1')
				//   if( needLogin ) { //需要登录
				// 	  vm.jump.loginNavigateTo(fullUrl)
				//   } else {
				// 	  vm.jump.navigateTo(fullUrl)
				//   }
				// }
				let needLogin = paramList.find(item => item.h5_key === 'login' && item.h5_val === '1')
				if( needLogin ) { //需要登录
					vm.jump.loginNavigateTo(fullUrl)
				} else {
					vm.jump.navigateTo(fullUrl)
				}
				console.log(path)
				console.log(paramList)
				console.log(param)
				console.log(fullUrl)
				console.log('------------------我是小程序或者h5跳转')
			}
		}
	}
	
	// app分享 type 支持的分享功能 0 全部 1只显示微信好友和微信朋友圈、dataType 分享数据的类型 1分享链接 2分享图片 3分享小程序、title 标题、des 简介、linkUrl 跳转链接、img 图片地址、userName 小程序路径、path 小程序路径
	// toType 是否要直接 唤起对应分享 1微信好友 2微信朋友圈 3新浪微博 4QQ好友 5QQ空间  注意分享小程序只支持好友 所有如果你只分享小程序 就直接唤起微信好友即可
	let appShare = ({ type = 1, dataType = 3, title = '酒云网', des = '与百万发烧友一起淘酒~', img = '', userName = 'gh_e64b2bfc0bc5', path='/pages/index/index', toType = '' }) => {
		
		const { protocol, host } = location
		const linkUrl = `${protocol}//${host}${path}`
		let shareObj = { type, dataType, title, des, img, userName, path, linkUrl }
		if (toType) {
			shareObj.toType = toType
		}
		wineYunJsBridge.openAppPage({
			client_path: {"ios_path":"share", "android_path":"share" }, 
			ad_path_param: [{"ios_key":"info", "ios_val": JSON.stringify(shareObj), "android_key":"info", "android_val": JSON.stringify(shareObj) }]
		});
	}
	
	// 发生错误时跳转
	let errBack = () => { }
	
	// 跳转上一个页面 from = 来自哪个端
	let jumpPrePage = ( from = '' ) => {
		console.log(from)
		console.log(vm.pages.getPageLength())
		
		if(vm.pages.getPageLength() <= 1 && from == '') { //当页面被暴露成首页（分享...）
		    console.log('-----------------------------------------------我的页面栈 <= 1')
			vm.jump.reLaunch('/pages/index/index')
		}else{ //当页面没有被暴露成首页
			if(vm.comes.isFromApp(from)) { //判断是否从App过来 1 = 安卓 2 = ios
				wineYunJsBridge.openAppPage({ client_path: { "ios_path":"goBack", "android_path":"goBack" } })
			}else //h5端、微信小程序
				vm.jump.navigateBack()
		}
		
	}
	
	// 跳转app直播 item = 直播列表某一项、from = 来自哪个端
	let jumpAppLive = ( item, from = '' ) => {
		console.log(item)
		if(vm.comes.isFromApp(from)) {
			let { id } = item
			wineYunJsBridge.openAppPage({
				client_path: { "ios_path":"LiveDetailsViewController", "android_path":"com.stg.rouge.activity.LiveActivity"},
				ad_path_param: [{ "ios_key":"live_id", "ios_val":id, "android_key":"id", "android_val":id }],
			})
		}else{
			vm.feedback.toast({ title:'请前往APP查看此功能~'})
		}
	}
	
	// 跳转h5协议 path = 跳转路径， from = 来自哪个端
	let jumpH5Agreement = ( path, from = '') => {
		if(vm.comes.isFromApp(from)){
			wineYunJsBridge.openAppPage({
				client_path: { "ios_path":"GeneralWebViewController", "android_path":"com.stg.rouge.webview.WebActivity" },
				ad_path_param: [
					{ 
						"ios_key":"url", "ios_val": path,  
					    "android_key":"url", "android_val": path 
					},
					{
						"ios_key":"isHideNav", "ios_val": "no",//是否交给网页端控制标题栏
						"android_key":"NullKey", "android_val": ""
					},
					{
						"ios_key":"isStatusBar", "ios_val": "no",//是否交给网页端控制状态栏
						"android_key":"NullKey", "android_val": ""  
					}
				]
			})
		}else{
			// #ifdef H5
				window.open(path)
			// #endif
			// #ifdef MP
				vm.jump.navigateTo(`${vm.$store.state.routeTable.pFWebView}?url=${encodeURIComponent(path)}`)
			// #endif
		}
	}

	const jumpUserHome = (uid, from = '') => {
		console.log('uid', uid)
		if(vm.comes.isFromApp(from)) {
			wineYunJsBridge.openAppPage({
				client_path: {
					"ios_path": "PostsAndVideoViewController",
					"android_path": "com.stg.rouge.activity.UserHomeActivity"
				},
				ad_path_param: [
					{ "ios_key": "uid", "ios_val": `${uid}`, "android_key": "uid", "android_val": `${uid}` },
					{ "ios_key": "type", "ios_val": "1", "android_key": "NullKey", "android_val": "1" }
				]
			})
		}
	}

	const jumpAppWelcome = (from = '') => {
		if(vm.comes.isFromApp(from)) {
			wineYunJsBridge.openAppPage({
				client_path: {
					"android_path": "com.stg.rouge.activity.WelcomeActivity"
				}
			})
		}
	}

	const checkAppUpdate = (from = '') => {
		if(vm.comes.isFromApp(from)) {
			let client_path = {
				"android_path": "checkUpdate"
			}
			if (vm.$ios) client_path = 'checkUpdate'
			wineYunJsBridge.openAppPage({
				client_path
			})
		}
	}

	const pullAppPay = (from, orderInfo) => {
		if(vm.comes.isFromApp(from)) {
			if(from == 'next'){
				const { main_order_no} = orderInfo
			const params = {
				client_path: {
					"android_path": "toPay"
				},
				ad_path_param: [
					{ "android_key": "main_order_no", "android_val": `${main_order_no}` },
				]
			}
			console.warn(params);
			
			wineYunJsBridge.openAppPage(params)
			} else {
				const { main_order_no, $payment_method, $from } = orderInfo
			const params = {
				client_path: {
					"ios_path": "toPay",
					"android_path": "toPay"
				},
				ad_path_param: [
					{ "ios_key": "info", "ios_val": JSON.stringify({ main_order_no, payment: $payment_method }), "android_key": "main_order_no", "android_val": `${main_order_no}` },
					{ "android_key": "payment", "android_val": `${$payment_method}` },
					{ "android_key": "from", "android_val": $from }
				]
			}
			wineYunJsBridge.openAppPage(params)
			}
			
		}
	}

	const jumpAppPayment = (from, data) => {
		
		if(vm.comes.isFromApp(from)) {
			if(from == 'next'){
				const { main_order_no, priceString, androidFrom} = data
			const params = {
				client_path: {
					"android_path": "toPay"
				},
				ad_path_param: [
					{ "android_key": "main_order_no", "android_val": `${main_order_no}` },
					{ "android_key": "from", "android_val": `${androidFrom}` },
					{ "android_key": "payment_amount", "android_val": `${priceString}` }
				]
			}
			console.log('礼品卡支付');
			
			
			wineYunJsBridge.openAppPage(params)
			}  else {
				wineYunJsBridge.openAppPage({
					client_path: {
						"ios_path": "GotoPayViewControllerConfig",
						"android_path": "com.stg.rouge.activity.CashierActivity"
					},
					ad_path_param: [
						{ "ios_key": "info", "ios_val": JSON.stringify(data), "android_key": "main_order_no", "android_val": data?.androidMainOrderNo },
						{ "android_key": "from", "android_val": data?.androidFrom },
						{ "android_key": "payment_amount", "android_val": data?.priceString }
					]
				})
			}
			
		}
	}
	
	// 移除App订单列表item
	const removeAppOrderListItem = () => {
		if (vm.$android) {
		    wineYunJsBridge.getDataFromApp(8)
		} else if (vm.$ios) {
			wineYunJsBridge.openAppPage({ client_path: 'updateAppData', name: '更新APP数据' })
		}
		vm.$customBack()
	}
	 
	// 通知App订单列表刷新 needGoBack 是否需要返回页面
	const noticeAppOrderListRefresh = (needGoBack = true) => {
		if (vm.$android) {
		    wineYunJsBridge.getDataFromApp(9)
		} else if (vm.$ios) {
			wineYunJsBridge.openAppPage({ client_path: 'updateAppData', name: '更新APP数据' })
		}
		if( needGoBack ) {
			vm.$customBack()
		}
	}
	

	const reportBuryDot = (channel, genre, region_id, button_id) => {
		const params = {
			data: [{
				channel,
				genre,
				region_id,
				button_id
			}]
		}
		Vue.prototype.$u.api.reportBuryDot(params)
	}

	const pubConfJumpBD = (item, channel, genre, region_id, button_id, from = '') => {
		pubConfJump(item, from)
		reportBuryDot(channel, genre, region_id, button_id)
	}

	const h5JumpBD = (h5Path, channel, genre, region_id, button_id, from = '', jumpType = 0) => {
		h5Jump(h5Path, from, jumpType)
		reportBuryDot(channel, genre, region_id, button_id)
	}

	const appAndMiniJumpBD = (type, path, channel, genre, region_id, button_id, from = '', jumpType = 0, checkLogin = false) => {
		appAndMiniJump(type, path, from, jumpType, checkLogin)
		reportBuryDot(channel, genre, region_id, button_id)
	}

	const jumpAppLiveBD = ( item, channel, genre, region_id, button_id, from = '' ) => {
		jumpAppLive(item, from)
		reportBuryDot(channel, genre, region_id, button_id)
	}

	const navigateToBD = (url, channel, genre, region_id, button_id) => {
		navigateTo(url)
		reportBuryDot(channel, genre, region_id, button_id)
	}
	
	// 保留当前页面进行跳转（埋点）
	const loginNavigateToBD = (url, channel, genre, region_id, button_id, mid = 0 ) => {
		loginNavigateTo(url)
		reportBuryDot(channel, genre, region_id, button_id, mid)
	}
	
	
	// 挂载到Vue.prototype
	Vue.prototype.jump = {
		navigateBack, // 返回
		navigateTo, // 保留当前页面进行跳转
		redirectTo, // 关闭当前页面进行跳转
		switchTab, // 跳转到 tabBar 页面、并关闭其他所有非 tabBar 页面
		reLaunch, // 关闭所有页面，打开到应用内的某个页面
		loginNavigateTo, //保留当前页面登录后进行跳转,若未登录，则先去登录页面
		appAndMiniJump,//app跳转网页&小程序跳转小程序跳转
		h5Jump, //app跳转h5&小程序跳转h5
		pubConfJump, //公共配置跳转
		appShare, //app分享
		jumpPrePage, //跳转上一个页面
		jumpAppLive, //跳转app直播
		jumpH5Agreement, //跳转h5协议
		jumpUserHome, // 跳转个人主页
		jumpAppWelcome,
		checkAppUpdate,
		pullAppPay,
		jumpAppPayment,
		removeAppOrderListItem, // 移除App订单列表item
		noticeAppOrderListRefresh, //通知app订单列表刷新
		pubConfJumpBD,
		h5JumpBD,
		appAndMiniJumpBD,
		jumpAppLiveBD,
		navigateToBD,
		loginNavigateToBD, //保留当前页面登录后进行跳转,若未登录，则先去登录页面（埋点）
	}
}

// 导出方法
export default { install }