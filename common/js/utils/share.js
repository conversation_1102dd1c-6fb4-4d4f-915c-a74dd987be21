// 获取系统的一些基本信息
import wx from 'weixin-js-sdk'
import { WX_APPID_PROD, WX_APPID_DEV } from '@/common/js/fun/constant'
const install = (Vue, vm) => {
	// h5分享微信
	const h5ShareWeixin = (shareFriendsData = {}, shareMomentsData = {}) => {
		  const { origin, pathname, search } = location
		  const url = `${origin}${pathname}${search}`
		  const data = { url, appid: vm.$isDev ? WX_APPID_DEV : WX_APPID_PROD }
		  vm.$u.api.getJsapiSign(data).then(res => {
		    const { appid = '', noncestr = '', sign = '', timestamp = '' } = res || {}
		    const configData = {
		      debug: vm.$isDev,
		      appId: appid,
		      nonceStr: noncestr,
		      signature: sign,
		      timestamp,
		      jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
		    }
		    wx.config(configData)
		    wx.ready(() => {
		      // const { share_image, main_title, sub_title, share_url } = shareData
		      // const data = {
		      //   title: main_title,
		      //   desc: sub_title,
		      //   link: share_url,
		      //   imgUrl: share_image,
		      // }
		      wx.updateAppMessageShareData(shareFriendsData)
		      wx.updateTimelineShareData(shareMomentsData)
		    })
		  })
	}
	// 挂载到Vue.prototype
	Vue.prototype.share = {
		h5ShareWeixin, // h5分享微信（只支持微信环境下分享给好友和朋友圈）
	}
}

// 导出方法
export default { install }