import urlParamsList from '@/common/js/data/urlParamsList.js' //腾讯埋点列表
// 参数处理工具函数
const install = ( Vue, vm ) => {
	// 获取上一个页面的完整路径 url = 连接、name = 参数名
	let getUrlParam = ( url, name ) => {
		let reg = new RegExp('(^|&|/?)' + name + '=([^&|/?]*)(&|/?|$)', 'i');
		let r = url.substr(1).match(reg);
		if(r!=null){
			return r[2]
		}
		return null
	}
	
	// 是否为朋友圈上报 sourcePlatform = 用户访问来源平台 （必定存在）、sourceEvent = 来源事件(活动)（必定存在）
	const isMomentsParam = () => {
		const { sourcePlatform, sourceEvent } = uni.getStorageSync('source') || {} //来源参数
		if( sourcePlatform && sourceEvent ) {
			const reportInfo = urlParamsList.find(e => e.sourcePlatform === sourcePlatform && e.sourceEvent === sourceEvent) || {}
			if( Object.keys(reportInfo).length ) {
				return reportInfo.source
			}else{
				return 0
			}
		}else {
			return 0
		}
	}
	
	
	// 挂载到Vue.prototype
	Vue.prototype.param = {
		getUrlParam, //获取上一个页面的完整路径
		isMomentsParam, //是否为朋友圈上报
	}
}

// 导出方法
export default { install }