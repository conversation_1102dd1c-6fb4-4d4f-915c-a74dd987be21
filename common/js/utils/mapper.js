import * as mapperModel from './mapperModel'
export const MAuctionIndexGoodsLabelText = Object.freeze([
  {
    value: mapperModel.MAuctionIndexGoodsLabel.Official,
    text: '官方拍品'
  },
  {
    value: mapperModel.MAuctionIndexGoodsLabel.Personal,
    text: '个人拍品'
  },
  {
    value: mapperModel.MAuctionIndexGoodsLabel.OneYuan,
    text: '一元起拍'
  }
])

export const MAuctionGoodsStatusText = Object.freeze([
  {
    value: mapperModel.MAuctionGoodsStatus.PendAuction,
    text: '待开始'
  },
  {
    value: mapperModel.MAuctionGoodsStatus.OnAuction,
    text: '竞拍中'
  },
  {
    value: mapperModel.MAuctionGoodsStatus.AuctionAbort,
    text: '已结束'
  },
  {
    value: mapperModel.MAuctionGoodsStatus.Disabled,
    text: '已结束'
  },
  {
    value: mapperModel.MAuctionGoodsStatus.Unsuccessful,
    text: '已结束'
  },
])

export const MAuctionFundsOrderTypeText = Object.freeze([
  {
    value: mapperModel.MAuctionFundsOrderType.Earnest,
    text: '保证金'
  },
  {
    value: mapperModel.MAuctionFundsOrderType.AuctionOrder,
    text: '拍卖'
  }
])

export const MAuctionFundsOrderTypeText2 = Object.freeze([
  {
    value: mapperModel.MAuctionFundsOrderType.Earnest,
    text: '保证金'
  },
  {
    value: mapperModel.MAuctionFundsOrderType.AuctionOrder,
    text: '成交价'
  }
])

export const MAuctionEarnestTypeText = Object.freeze([
  {
    value: mapperModel.MAuctionEarnestType.Bidding,
    text: '竞拍保证金'
  },
  {
    value: mapperModel.MAuctionEarnestType.Entrust,
    text: '委托保证金'
  }
])

export const MAuctionFundsStatusText = Object.freeze([
  {
    value: mapperModel.MAuctionFundsStatus.Prepaid,
    text: '已支付'
  },
  {
    value: mapperModel.MAuctionFundsStatus.Freeze,
    text: '冻结中'
  },
  {
    value: mapperModel.MAuctionFundsStatus.InAuction,
    text: '竞拍中'
  },
  {
    value: mapperModel.MAuctionFundsStatus.Returned,
    text: '已退还'
  },
  {
    value: mapperModel.MAuctionFundsStatus.Receive,
    text: '确认收货后返回'
  },
  {
    value: mapperModel.MAuctionFundsStatus.IllegalDeduction,
    text: '违规扣除'
  },
  {
    value: mapperModel.MAuctionFundsStatus.Canceled,
    text: '已取消'
  }
])

export const MAuctionEarnestStatusText = Object.freeze([
  {
    value: mapperModel.MAuctionEarnestStatus.PendAuction,
    text: '待拍中'
  },
  {
    value: mapperModel.MAuctionEarnestStatus.Freeze,
    text: '冻结中'
  },
  {
    value: mapperModel.MAuctionEarnestStatus.InAuction,
    text: '竞拍中'
  },
  {
    value: mapperModel.MAuctionEarnestStatus.Returned,
    text: '已退还'
  },
  {
    value: mapperModel.MAuctionEarnestStatus.Receive,
    text: '确认收货后返回'
  },
  {
    value: mapperModel.MAuctionEarnestStatus.IllegalDeduction,
    text: '违规扣除'
  },
  {
    value: mapperModel.MAuctionEarnestStatus.Canceled,
    text: '已取消'
  }
])

export const MAuctionGoodsCategoryText = Object.freeze([
  {
    value: mapperModel.MAuctionGoodsCategory.Wine,
    text: '葡萄酒'
  },
  {
    value: mapperModel.MAuctionGoodsCategory.WhiteSpirits,
    text: '白酒'
  },
  {
    value: mapperModel.MAuctionGoodsCategory.Liqueur,
    text: '烈酒'
  }
])

export const MAuctionGoodsStatusObjMapper = {
  [mapperModel.MAuctionGoodsStatus.PendPutaway]: {
    statusText: '待上架',
    statusBg: '/auction/goods_status_1.png',
    timeText: '开始时间',
    timeTextClazz: 'text-ff9127',
    timeTextBgClazz: 'bg-ff9127',
    timeKeyValue: 'sell_time',
    pricePrefixText: '起拍价',
  },
  [mapperModel.MAuctionGoodsStatus.PendAuction]: {
    statusText: '待开拍',
    statusBg: '/auction/goods_status_1.png',
    timeText: '开始时间',
    timeTextClazz: 'text-ff9127',
    timeTextBgClazz: 'bg-ff9127',
    timeKeyValue: 'sell_time',
    pricePrefixText: '起拍价',
    $erStatusText: '待开始',
    $erStatusTextClazz: 'text-ffffff',
    $erStatusBg: '/auction/goods_status_1_176_46.png',
    $erTitleTextClazz: 'text-3',
    $rTimeKeyValue: 'sell_time_text',
    $eTimeKeyValue: 'sell_time_text',
    $erTimeSuffixText: '开始',
    $erTimeTextClazz: 'text-d76a00 bg-ffe3b7',
    $erPricePrefixClazz: 'text-6',
    $erPriceClazz: 'text-3',
    $adDetailTitleTextClazz: 'text-3',
    $adDetailTimeTextClazz: 'text-d76a00 bg-ffe3b7',
    $adDetailPricePrefixClazz: 'text-e80404',
    $adDetailPriceClazz: 'text-e80404',
  },
  [mapperModel.MAuctionGoodsStatus.OnAuction]: {
    statusText: '正在拍卖',
    statusBg: '/auction/goods_status_2.png',
    timeText: '结束时间',
    timeTextClazz: 'text-e80404',
    timeTextBgClazz: 'bg-e80404',
    timeKeyValue: 'closing_auction_time',
    pricePrefixText: '当前价',
    $erStatusText: '竞拍中',
    $erStatusTextClazz: 'text-ffffff',
    $erStatusBg: '/auction/goods_status_2_176_46.png',
    $erTitleTextClazz: 'text-3',
    $rTimeKeyValue: 'closing_auction_time_text',
    $eTimeKeyValue: 'closing_auction_time_text',
    $erTimeSuffixText: '结束',
    $erTimeTextClazz: 'text-d76a00 bg-ffe3b7',
    $erPricePrefixClazz: 'text-e80404',
    $erPriceClazz: 'text-e80404',
    $adDetailTitleTextClazz: 'text-3',
    $adDetailTimeTextClazz: 'text-d76a00 bg-ffe3b7',
    $adDetailPricePrefixClazz: 'text-e80404',
    $adDetailPriceClazz: 'text-e80404',
  },
  [mapperModel.MAuctionGoodsStatus.Disabled]: {
    statusText: '已下架',
    statusBg: '/auction/goods_status_4.png',
    timeText: '结束时间',
    timeTextClazz: 'text-9',
    timeTextBgClazz: 'bg-999999',
    timeKeyValue: 'closing_auction_time',
    pricePrefixText: '起拍价',
    $erStatusText: '已结束',
    $erStatusTextClazz: 'text-6',
    $erStatusBg: '/auction/goods_status_4_176_46.png',
    $erTitleTextClazz: 'text-d8d8d8',
    $erTimeSuffixText: '已结束',
    $erTimeTextClazz: 'text-9 bg-f7f7f7',
    $erPricePrefixClazz: 'text-d8d8d8',
    $erPriceClazz: 'text-d8d8d8',
    $adDetailTitleTextClazz: 'text-9',
    $adDetailTimeTextClazz: 'text-9 bg-d8d8d8',
    $adDetailPricePrefixClazz: 'text-d8d8d8',
    $adDetailPriceClazz: 'text-d8d8d8',
  },
  [mapperModel.MAuctionGoodsStatus.AuctionAbort]: {
    statusText: '已结束',
    statusBg: '/auction/goods_status_4.png',
    timeText: '结束时间',
    timeTextClazz: 'text-9',
    timeTextBgClazz: 'bg-999999',
    timeKeyValue: 'closing_auction_time',
    pricePrefixText: '成交价',
    $erStatusText: '已结束',
    $erStatusTextClazz: 'text-6',
    $erStatusBg: '/auction/goods_status_4_176_46.png',
    $erTitleTextClazz: 'text-d8d8d8',
    $erTimeSuffixText: '已结束',
    $erTimeTextClazz: 'text-9 bg-f7f7f7',
    $erPricePrefixClazz: 'text-d8d8d8',
    $erPriceClazz: 'text-d8d8d8',
    $adDetailTitleTextClazz: 'text-9',
    $adDetailTimeTextClazz: 'text-9 bg-d8d8d8',
    $adDetailPricePrefixClazz: 'text-d8d8d8',
    $adDetailPriceClazz: 'text-d8d8d8',
  },
  [mapperModel.MAuctionGoodsStatus.Unsuccessful]: {
    statusText: '已流拍',
    statusBg: '/auction/goods_status_4.png',
    timeText: '结束时间',
    timeTextClazz: 'text-9',
    timeTextBgClazz: 'bg-999999',
    timeKeyValue: 'closing_auction_time',
    pricePrefixText: '起拍价',
    $erStatusText: '已结束',
    $erStatusTextClazz: 'text-6',
    $erStatusBg: '/auction/goods_status_4_176_46.png',
    $erTitleTextClazz: 'text-d8d8d8',
    $erTimeSuffixText: '已结束',
    $erTimeTextClazz: 'text-9 bg-f7f7f7',
    $erPricePrefixClazz: 'text-d8d8d8',
    $erPriceClazz: 'text-d8d8d8',
    $adDetailTitleTextClazz: 'text-9',
    $adDetailTimeTextClazz: 'text-9 bg-d8d8d8',
    $adDetailPricePrefixClazz: 'text-d8d8d8',
    $adDetailPriceClazz: 'text-d8d8d8',
  },
}


// 我的参拍信息
export const MAuctionMyParticipationMapper = Object.freeze({
  [mapperModel.MAuctionMyParticipationStatus.Bidding]: {
	 0: {
		 $statusBgIndex: '2',
		 $statusText: '被反超',
	 },
	 1: {
		 $statusBgIndex: '1',
		 $statusText: '领先',
	 },
	 $tagBgClazz: 'bg-ffe3b7',
	 $tagTextClazz: 'text-ff9127',
	 $priceName: '当前价',
	 $tagText: '结束'
  },
  [mapperModel.MAuctionMyParticipationStatus.Shot]: {
	$statusBgIndex: '3',
    $statusText: '已拍中',
	$priceName: '成交价',
	$tagBgClazz: 'bg-f7f7f7',
	$tagTextClazz: 'text-9',
	$tagText: '已结束'
  },
  [mapperModel.MAuctionMyParticipationStatus.NotTaken]: {
	$statusBgIndex: '4',
    $statusText: '未拍中',
	$priceName: '成交价',
	$tagBgClazz: 'bg-f7f7f7',
	$tagTextClazz: 'text-9',
	$tagText: '已结束'
  },
})


export const MAuctionNoticeTypeText = Object.freeze([
  {
    value: mapperModel.MAuctionNoticeType.Auction,
    text: '拍卖'
  },
  {
    value: mapperModel.MAuctionNoticeType.Notice,
    text: '通知'
  },
  {
    value: mapperModel.MAuctionNoticeType.Order,
    text: '订单'
  },
  {
    value: mapperModel.MAuctionNoticeType.Comment,
    text: '评论'
  },
])

export const MPaymentMethodText = Object.freeze([
  {
    value: mapperModel.MPaymentMethod.AliApp,
    text: '支付宝APP'
  },
  {
    value: mapperModel.MPaymentMethod.AliH5,
    text: '支付宝H5'
  },
  {
    value: mapperModel.MPaymentMethod.AliPC,
    text: '支付宝PC'
  },
  {
    value: mapperModel.MPaymentMethod.WxAPP,
    text: '微信APP'
  },
  {
    value: mapperModel.MPaymentMethod.WxMini,
    text: '微信小程序'
  },
  {
    value: mapperModel.MPaymentMethod.WxH5,
    text: '微信H5'
  },
  {
    value: mapperModel.MPaymentMethod.TikTokAli,
    text: '抖音支付宝'
  },
  {
    value: mapperModel.MPaymentMethod.WxJSAPI,
    text: '微信JSAPI(公众号支付)'
  },
  {
    value: mapperModel.MPaymentMethod.TikTokWx,
    text: '抖音微信'
  },
  {
    value: mapperModel.MPaymentMethod.RabbitHead,
    text: '兔头'
  },
  {
    value: mapperModel.MPaymentMethod.GiftCard,
    text: '礼品卡'
  },
])

export const MInvoiceTypeText = Object.freeze([
  {
    value: mapperModel.MInvoiceType.General,
    text: '普通发票'
  },
  {
    value: mapperModel.MInvoiceType.Special,
    text: '专用发票'
  },
])

export const MInvoiceFrontTypeText = Object.freeze([
  {
    value: mapperModel.MInvoiceFrontType.Person,
    text: '个人'
  },
  {
    value: mapperModel.MInvoiceFrontType.Company,
    text: '单位'
  },
])

export const MMiaofaSearchTypeText = Object.freeze([
  {
    value: mapperModel.MMiaofaSearchType.Goods,
    text: '商品'
  },
  // {
  //   value: mapperModel.MMiaofaSearchType.Auction,
  //   text: '拍卖'
  // },
  {
    value: mapperModel.MMiaofaSearchType.Content,
    text: '内容'
  },
  {
    value: mapperModel.MMiaofaSearchType.WineParty,
    text: '酒会'
  },
])

// 列表状态码:1=已拍出,2=待发货,999=待发货(已下单上门取件) 3=已发货,4=已完成,5=已取消,6=待审核,7=已驳回,8=待开拍,9=竞拍中,10=已流拍
// btn 1=重新提交 2=放弃上拍 3=立即发货 4=修改发货
export const MAuctionCreatedStatusMapper = {
  1: {
    $createdTimeClazz: 'text-6',
    $isShowStatusTipsIcon: false,
    $statusClazz: 'text-3',
    $titleClazz: 'text-3',
    $timeKey: '',
    $timeClazz: '',
    $priceKey: 'final_auction_price',
    $priceClazz: 'text-6',
    $pricePrefix: '成交价',
    $pricePrefixClazz: 'text-3',
    $btnList: [],
  },
  2: {
    $createdTimeClazz: 'text-6',
    $isShowStatusTipsIcon: false,
    $statusClazz: 'text-e80404',
    $titleClazz: 'text-3',
    $timeKey: '',
    $timeClazz: '',
    $priceKey: 'final_auction_price',
    $priceClazz: 'text-6',
    $pricePrefix: '成交价',
    $pricePrefixClazz: 'text-3',
    $btnList: [3],
  },
  9999: {
    $createdTimeClazz: 'text-6',
    $isShowStatusTipsIcon: false,
    $statusClazz: 'text-e80404',
    $titleClazz: 'text-3',
    $timeKey: '',
    $timeClazz: '',
    $priceKey: 'final_auction_price',
    $priceClazz: 'text-6',
    $pricePrefix: '成交价',
    $pricePrefixClazz: 'text-3',
    $btnList: [4],
  },
  3: {
    $createdTimeClazz: 'text-6',
    $isShowStatusTipsIcon: false,
    $statusClazz: 'text-3',
    $titleClazz: 'text-3',
    $timeKey: '',
    $timeClazz: '',
    $priceKey: 'final_auction_price',
    $priceClazz: 'text-6',
    $pricePrefix: '成交价',
    $pricePrefixClazz: 'text-3',
    $btnList: [],
  },
  4: {
    $createdTimeClazz: 'text-6',
    $isShowStatusTipsIcon: false,
    $statusClazz: 'text-3',
    $titleClazz: 'text-3',
    $timeKey: '',
    $timeClazz: '',
    $priceKey: 'final_auction_price',
    $priceClazz: 'text-6',
    $pricePrefix: '成交价',
    $pricePrefixClazz: 'text-3',
    $btnList: [],
  },
  5: {
    $createdTimeClazz: 'text-d8d8d8',
    $isShowStatusTipsIcon: false,
    $statusClazz: 'text-3',
    $titleClazz: 'text-d8d8d8',
    $timeKey: '',
    $timeClazz: '',
    $priceKey: 'price',
    $priceClazz: 'text-d8d8d8',
    $pricePrefix: '起拍价',
    $pricePrefixClazz: 'text-d8d8d8',
    $btnList: [],
  },
  6: {
    $createdTimeClazz: 'text-6',
    $isShowStatusTipsIcon: false,
    $statusClazz: 'text-e80404',
    $titleClazz: 'text-3',
    $timeKey: '',
    $timeClazz: '',
    $priceKey: 'price',
    $priceClazz: 'text-6',
    $pricePrefix: '起拍价',
    $pricePrefixClazz: 'text-3',
    $btnList: [2],
  },
  7: {
    $createdTimeClazz: 'text-6',
    $isShowStatusTipsIcon: true,
    $statusClazz: 'text-3',
    $titleClazz: 'text-3',
    $timeKey: '',
    $timeClazz: '',
    $priceKey: 'price',
    $priceClazz: 'text-6',
    $pricePrefix: '起拍价',
    $pricePrefixClazz: 'text-3',
    $btnList: [1, 2],
  },
  8: {
    $createdTimeClazz: 'text-6',
    $isShowStatusTipsIcon: false,
    $statusClazz: 'text-ff9127',
    $titleClazz: 'text-3',
    $timeKey: 'sell_time_str',
    $timeClazz: 'text-d76a00 bg-ffecd3',
    $timeSuffix: ' 开始',
    $priceKey: 'price',
    $priceClazz: 'text-6',
    $pricePrefix: '起拍价',
    $pricePrefixClazz: 'text-3',
    $btnList: [],
  },
  9: {
    $createdTimeClazz: 'text-6',
    $isShowStatusTipsIcon: false,
    $statusClazz: 'text-e80404',
    $titleClazz: 'text-3',
    $timeKey: 'closing_auction_time_str',
    $timeClazz: 'text-d76a00 bg-ffecd3',
    $timeSuffix: ' 结束',
    $priceKey: 'final_auction_price',
    $priceClazz: 'text-e80404',
    $pricePrefix: '当前价',
    $pricePrefixClazz: 'text-e80404',
    $btnList: [],
  },
  10: {
    $createdTimeClazz: 'text-d8d8d8',
    $isShowStatusTipsIcon: false,
    $statusClazz: 'text-3',
    $titleClazz: 'text-d8d8d8',
    $timeKey: '',
    $timeClazz: '',
    $priceKey: 'price',
    $priceClazz: 'text-d8d8d8',
    $pricePrefix: '起拍价',
    $pricePrefixClazz: 'text-d8d8d8',
    $btnList: [1, 2],
  },
}

