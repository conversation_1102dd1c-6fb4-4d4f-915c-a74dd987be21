// 获取系统的一些基本信息
const install = (Vue, vm) => {
	// 获取系统信息
	let getSysInfo = () => {
	  const res = uni.getSystemInfoSync()
	  console.log('uni.getSystemInfoSync()', res)
	  return res
	}
	
	// 获取导航栏高度
	let navigationBarHeight = () => {
		let systemInfo = uni.getSystemInfoSync()
		let statusBarHeight = systemInfo.statusBarHeight //状态栏高度
		let navbarHeight = systemInfo.platform == 'ios' ? 44 : 48 //导航栏高度
		let height = statusBarHeight + navbarHeight // 状态栏高度+导航栏高度
		return height
	}
	
	
	// 设置状态栏图标颜色为黑色
	let setNavigationBarBlack = () => {
		uni.setNavigationBarColor({ 
			frontColor: '#000000' ,
			backgroundColor: '#FFFFFF'
		})
	}
	
	// 设置状态栏图标颜色为白色
	let setNavigationBarWhite = () => {
		uni.setNavigationBarColor({ 
			frontColor: '#FFFFFF' ,
			backgroundColor: '#000000'
		})
	}
	
	// 拨打电话 phoneNumber = 电话号码
	let phoneCall = ( phoneNumber ) => {
		uni.makePhoneCall({
		    phoneNumber
		});
	}
	
	// 短振动
	let vibrateShort = () => {
		uni.vibrateShort({
			success: res => {
					console.log('success');
			}
		})
	}
	
	// 打开客服
	let customerService = (from = '') => {
		if(from == 'next'){
			wineYunJsBridge.openAppPage({
				client_path: { android_path: 'shangpin.kefu' },
			  })
		} else {
			// vm.feedback.toast({ title:'请打开小程序或app咨询' })
		//#ifdef H5
		vm.feedback.toast({ title:'请打开小程序或app咨询' })
		//#endif
		//#ifdef MP-WEIXIN
			wx.openCustomerServiceChat({
			  extInfo: { url: 'https://work.weixin.qq.com/kfid/kfc6fc492072ba086a5' },
			  corpId: 'ww9e957916d7d659d3',
			  success: res => {
				  console.log('跳转微信客服成功')
			  }
			})
			//#endif
		}
		
		
	}
	
	// 页面滚动到哪个位置 scrollTop = 距离顶部位置、duration = 持续时间
	let pageScrollTo = ( scrollTop = 0 , duration = 300 ) => {
		uni.pageScrollTo({
		    scrollTop,
		    duration
		});
	}
	const sysPlatform = () => {
		let systemInfo = uni.getSystemInfoSync()
		return systemInfo.platform || ''
	}
	const sysPlatformAndroid = () => sysPlatform() === 'android'
	const sysPlatformIos = () => sysPlatform() === 'ios'
	
	// 挂载到Vue.prototype
	Vue.prototype.system = {
		getSysInfo, // 获取系统信息
		navigationBarHeight, //获取导航栏高度
		setNavigationBarBlack, //设置页面导航条图标为黑色
		setNavigationBarWhite, //设置状态栏图标颜色为白色
		phoneCall, //拨打电话 phoneNumber = 电话号码
		vibrateShort, //短振动
		customerService, //打开客服
		pageScrollTo, //页面滚动到顶部
		sysPlatformAndroid,
		sysPlatformIos,
	}
}

// 导出方法
export default { install }