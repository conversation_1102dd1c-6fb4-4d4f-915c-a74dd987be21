// 复制工具函数
const install = (Vue, vm) => {
	// 复制文本 text = 文本
	let copyText = ( text ) => {
		uni.setClipboardData({
		    data: text,
			showToast: true,
		    success: () => {
		        // vm.feedback.toast({ title:'复制文本成功~' })
		    }
		});
	}

	const appCopy = (text) => {
		wineYunJsBridge.openAppPage({
			client_path: { "ios_path":"copyToClipboard", "android_path":"copyToClipboard" },
			ad_path_param: [{ 
				"ios_key": "info", "ios_val": text,  
				"android_key": "info", "android_val": text 
			}]
		})
	} 
	
	// 挂载到Vue.prototype
	Vue.prototype.copy = {
		copyText, //复制文本
		appCopy
	}
}

// 导出方法
export default { install }