// 反馈给用用户的提示信息
const install = (Vue, vm) => {
	// 显示toast弹框 title = 弹窗提示文字、icon = 图标、mask = 是否显示透明蒙层，防止触摸穿透，默认：true、duration = 提示的延迟时间
	let toast = ({title = '操作成功', icon = 'none', mask = true, duration = 1500} = {}) => {
		uni.showToast({
			title,
			icon,
			mask,
			duration
		})
	}
	
	// 显示loading弹框 title = 弹窗提示文字、mask = 是否显示透明蒙层、防止触摸穿透，默认：true
	let loading = ({title = '加载中...', mask = true} = {}) => {
		uni.showLoading({
			title,
			mask
		})
	}
	
	// 关闭loading弹窗
	let hideLoading = () => {
		uni.hideLoading()
	}
	
	// 显示模态弹窗 title = 弹窗提示文字、content = 模态框内容、confirm = 确认的回调函数、cancel = 取消的回调函数
	let showModal = ({title = '提示', content = '删除之后数据无法恢复，确认删除吗？', cancelText = '取消', confirmText = "确定", confirm = () => {}, cancel = () => {} } = {}) => {
		uni.showModal({
			title,
			content,
			cancelText,
			confirmText,
			success: res => {
				if (res.confirm) 
				    confirm()
				else if (res.cancel) 
				    cancel()
			}
		})
	}

	let showWithoutCancelModal = ({title = '提示', content = '错误内容', showCancel = false,  confirm = () => {} } = {}) => {
		uni.showModal({
			title,
			content,
			showCancel,
			success: res => {
				if (res.confirm) confirm()
			}
		})
	}
	
	// 挂载到Vue.prototype
	Vue.prototype.feedback = {
		toast, //显示toast弹框
		loading, //显示loading
		hideLoading, //关闭loading
		showModal, //显示模态弹窗
		showWithoutCancelModal
	}
}

// 导出方法
export default { install }