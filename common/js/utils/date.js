// 处理日期格式
const install = (Vue, vm) => {
	// 获取两个时间之间的时间差（秒级）date = 结束日期，例如（2022-01-11 16:41:36）
	let getSeconds = ( date ) => {
	  let dateBegin = new Date() //当前时间
	  let dateEnd  = new Date(date.replace(/-/g, '/')) //结束时间
	  let seconds = Math.floor((dateEnd.getTime() - dateBegin.getTime()) / 1000 ) //得到两个日期之间的秒数
	  if(seconds > 0) 
		  return seconds
	  else 
		  return 0
	}
	
	// 获取时间戳
	let getTimeStamp = () => {
		return new Date().getTime()
	}
	
	// 获取当前月份的最后一天
	const getLastDayOfMonth = (date) => {
		 const [year, month] = date.split('-')
		 const lastDay = new Date(year, month, 0).getDate()
		 const lastDate = `${date}-${lastDay} 23:59:59`
		 return lastDate
	}
	
	// 获取当前月份的第一天
	const getFirstDayOfMonth = (date) => {
		return `${date}-01 00:00:00`
	}
	
	// 挂载到Vue.prototype
	Vue.prototype.date = {
		getSeconds, // 获取两个时差之间的秒数
		getTimeStamp, //获取时间戳
		getLastDayOfMonth, //获取当前月份的最后一天
		getFirstDayOfMonth //获取当前月份的第一天
	}
}

// 导出方法
export default { install }

