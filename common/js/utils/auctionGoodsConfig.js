export const WINE_BRAND_LIST = Object.freeze([ // 葡萄酒品牌
  '拉菲', '玛歌', '木桐', '拉图', '侯伯王',
  '柏图斯', '白马', '欧颂', '罗曼尼•康帝', '唐培里',
  '酩悦', '巴黎之花', '路易王妃', '罗兰百悦', '凯歌',
  '玛姆', '玻玛利', '堡林爵', '泰廷爵', '库克',
  '西施佳雅', '太阳园', '欧纳拉雅', '嘉雅', '朱塞佩•昆达莱利酒庄',
  '戴福诺酒庄', '碧安仙帝', '平古斯', '橡树河畔', '天使之堤',
  '贝加西西里亚', '缇欧佩佩', '伊慕酒庄', '普朗酒庄', '奔富',
  '云雾之湾', '克莱格', '伊拉苏酒庄', '干露酒厂', '桑塔丽塔',
  '卡氏家族酒庄', '黄尾袋鼠', '菲斯奈特', '其他'
])

export const WHITE_SPIRITS_BRAND_LIST = Object.freeze([ // 白酒品牌
  '白云边', '北大仓', '白水杜康', '扳倒井', '宝丰',
  '丛台', '董酒', '杜康', '钓鱼台', '丹泉',
  '汾酒', '丰谷', '汾杏', '古井贡', '国台',
  '桂林三花', '贵酒', '红星', '怀庄', '衡水老白干',
  '黄鹤楼', '荷花', '衡昌烧坊', '剑南春', '酒鬼',
  '今世缘', '金六福', '金沙', '金门高粱', '江小白',
  '景芝', '口子窖', '孔府家酒', '泸州老窖', '郎酒',
  '赖茅', '李渡', '泸州老窖特曲', '刘伶醉', '浏阳河',
  '茅台', '牛栏山', '全兴', '荣和烧坊', '人民小酒',
  '舍得', '水井坊', '双沟', '四特', '十八酒坊',
  '宋河', '赊店', '诗仙太白', '潭酒', '天佑德',
  '汤沟', '五粮液', '文君', '武陵', '西凤',
  '习酒', '小糊涂仙', '杏花村', '叙府', '小角楼',
  '湘窖', '洋河', '永丰', '伊力特', '迎驾贡酒',
  '鸭溪窖', '仰韶', '玉蝉老酒', '珍酒', '竹叶青',
  '筑春', '张弓', '其他'
])

export const WINE_COUNTRY_LIST = Object.freeze([ // 葡萄酒原产国
  '中国', '法国', '英国', '德国', '西班牙',
  '葡萄牙','智利', '阿根廷', '南非', '澳大利亚',
  '新西兰', '意大利', '美国', '丹麦', '罗马尼亚',
  '其他'
])

export const WINE_PLACE_LIST = Object.freeze([ // 葡萄酒产地
  '普罗旺斯', '波尔多', '罗纳河谷', '勃艮第', '博若莱',
  '香槟区', '朗格多克', '卢瓦河谷', '皮埃蒙特', '伦巴第',
  '威尼托', '托斯卡纳', '温布里亚', '阿普里切纳', '瓦伦西亚',
  '里奥哈', '加利西亚', '纳瓦拉'
])

export const WINE_PACKING_LIST = Object.freeze(['裸瓶装', '箱装', '礼盒装', '盒装']) // 葡萄酒包装方式

export const WHITE_SPIRITS_PACKING_LIST = Object.freeze(['裸瓶装', '箱装', '礼盒装', '盒装', '坛装']) // 白酒包装方式

export const STORAGE_MODE_LIST = Object.freeze(['常温', '冷藏', '冷冻']) // 存储方式

export const SCENT_LIST = ['酱香型', '浓香型', '清香型', '其他'] // 香型

const FILL_TIME_START_YEAR = 1950
const FILL_TIME_END_YEAR = new Date().getFullYear()
const FILL_TIME_YEAR_LIST = []
for (let i = FILL_TIME_START_YEAR; i <= FILL_TIME_END_YEAR; i++) {
  FILL_TIME_YEAR_LIST.unshift(i)
}
const getFillTimeMonthList = (year) => {
  const startMonth = 1
  let endMonth = 12
  if (year === FILL_TIME_END_YEAR) {
    endMonth = new Date().getMonth() + 1
  }
  const list = []
  for (let i = startMonth; i <= endMonth; i++) {
    list.push(i)
  }
  return list
}
export { FILL_TIME_YEAR_LIST, getFillTimeMonthList }

const WINE_GRADE_START = 70
const WINE_GRADE_END = 100
const WINE_GRADE_LIST = []
for (let i = WINE_GRADE_START; i <= WINE_GRADE_END; i++) {
  WINE_GRADE_LIST.push(i)
}
export { WINE_GRADE_LIST }

const START_YEAR = 1950
const END_YEAR = new Date().getFullYear()
const YEAR_LIST = []
for (let i = START_YEAR; i <= END_YEAR; i++) {
  YEAR_LIST.unshift(i)
}
export { YEAR_LIST }

export const WHITE_SPIRITS_YEAR_LIST = Object.freeze(['12年(含)-18年(含)', '18年(含)-30年(含)', '1年(含)-3年(含)', '1年及以下', '30年(含)-50年(含)', '3年(含)-8年(含)', '50年以上', '8年(含)-12年(含)'])
