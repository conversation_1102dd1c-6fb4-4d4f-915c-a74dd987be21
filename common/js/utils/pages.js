// 页面相关工具函数
const install = (Vue, vm) => {
	// 获取上一个页面的完整路径
	let getPrvePageFullPath = () => {
		let pages = getCurrentPages() //当前页面栈
		let prevPage = pages[pages.length - 2] //上一页面
		let fullPath = prevPage.$page.fullPath //页面完整路径
		console.log('--------------我是页面完整路径')
		console.log(prevPage)
		console.log(fullPath)
		console.log(decodeURIComponent(fullPath))
		return fullPath
	}
	
	// 获取页面栈长度
	let getPageLength = () => {
		let pages = getCurrentPages() //当前页面栈
		return pages.length
	}

	const getPageFullPath = () => {
		const pageLength = getPageLength()
    const currentPage = getCurrentPages()[pageLength - 1]
    return currentPage.$page.fullPath
	}
	
	const getCurrenPage = () => {
		const pageLength = getPageLength()
		return getCurrentPages()[pageLength - 1]
	}
	
	// 挂载到Vue.prototype
	Vue.prototype.pages = {
		getPrvePageFullPath, //获取上一个页面的完整路径
		getPageLength, //获取页面栈长度
		getPageFullPath,
		getCurrenPage
	}
}

// 导出方法
export default { install }