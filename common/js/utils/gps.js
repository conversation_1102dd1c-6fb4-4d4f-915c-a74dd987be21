// gps定位工具函数
const install = (Vue, vm) => {
	// 打开gps导航 address = 目标详细地址、latitude = 纬度、longitude = 经度
	let openGps = ( latitude, longitude, address ) => {
		if( vm.$store.state.scene == 1154 ) return vm.feedback.toast({ title: '请前往小程序查看' })
		console.log( latitude )
		console.log( longitude )
		console.log( address )
		// 121.444862,31.200745
		uni.getLocation({
			type: 'gcj02', //返回可以用于uni.openLocation的经纬度
			success: res => {
				uni.openLocation({
					latitude: Number(latitude), //纬度
					longitude: Number(longitude), //经度
					address //地址
				});
			}
		});
	}
	
	// 通过经纬度计算距离 (通过两组经纬度 计算它们之间的距离) la1 = 第一个纬度，lo1 = 第一个经度，la2 = 第二个纬度，lo2 = 第二个经度
	let lonAndLatDis = ( la1, lo1, la2, lo2 ) => {
		let La1 = la1 * Math.PI / 180.0;
		let La2 = la2 * Math.PI / 180.0;
		let La3 = La1 - La2;
		let Lb3 = lo1 * Math.PI / 180.0 - lo2 * Math.PI / 180.0;
		let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(La3 / 2), 2) + Math.cos(La1) * Math.cos(La2) * Math.pow(Math.sin(Lb3 / 2), 2)));
		s = s * 6378.137;//地球半径
		s = parseInt((Math.round(s * 10000) / 10000 * 1000).toFixed(0))
		return s;
	}
	
	// 挂载到Vue.prototype
	Vue.prototype.gps = {
		openGps, //打开gps导航
		lonAndLatDis //通过经纬度计算距离
	}
}

// 导出方法
export default { install }