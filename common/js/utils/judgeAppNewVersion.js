import Vue from 'vue'

export default (oldVersion) => {
  const currVersion = Vue.prototype?.$vhVersion || ''
  const toVersionArr = (str) => str.split('.').map(item => +item)
  const currVersionArr = toVersionArr(currVersion)
  const currVersionArrLen = currVersionArr.length
  const oldVersionArr = toVersionArr(oldVersion)
  const oldVersionArrLen = oldVersionArr.length
  console.log(currVersionArr, oldVersionArr)
  if (currVersionArrLen > oldVersionArrLen) return true
  else if (currVersionArrLen < oldVersionArrLen) return false
  let i = 0
  while (i < currVersionArrLen) {
    if (currVersionArr[i] > oldVersionArr[i]) return true
    else if (currVersionArr[i] < oldVersionArr[i]) return false
    i++
  }
  return false
}