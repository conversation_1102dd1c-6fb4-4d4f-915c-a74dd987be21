import { ossIcon } from '@/common/js/utils/oss'

const getEmojiImg = (src) => {
  return ossIcon(`${src}.gif`, 0, 'https://images.vinehoo.com/vinehoomini/v3/emoticon/')
}

export const RabbitHeadEmojiList = Object.freeze([
  { reg:'[rabbithead_吨吨吨]', img:'rh1', name:'吨吨吨' },
  { reg:'[rabbithead_买买买]', img:'rh2', name:'买买买' },
  { reg:'[rabbithead_剁手]', img:'rh3', name:'剁手' },
  { reg:'[rabbithead_难喝]', img:'rh4', name:'难喝' },
  { reg:'[rabbithead_喜欢]', img:'rh5', name:'喜欢' },
  { reg:'[rabbithead_好]', img:'rh6', name:'好' },
  { reg:'[rabbithead_嘿嘿嘿]', img:'rh7', name:'嘿嘿嘿' },
  { reg:'[rabbithead_晕了]', img:'rh8', name:'晕了' },
  { reg:'[rabbithead_哇]', img:'rh9', name:'哇' },
  { reg:'[rabbithead_推荐]', img:'rh10', name:'推荐' },
  { reg:'[rabbithead_悠闲]', img:'rh11', name:'悠闲' },
  { reg:'[rabbithead_帅气]', img:'rh12', name:'帅气' },
  { reg:'[rabbithead_思考]', img:'rh13', name:'思考' },
  { reg:'[rabbithead_笑哭]', img:'rh14', name:'笑哭' },
  { reg:'[rabbithead_愤怒]', img:'rh15', name:'愤怒' },
  { reg:'[rabbithead_摸鱼]', img:'rh16', name:'摸鱼' },
].map(item => ({ ...item, img: getEmojiImg(item.img) })))

export const RabbitEmojiList = Object.freeze([
  { reg:'[rabbit_喝酒去]', img:'rb1', name:'喝酒去' },
  { reg:'[rabbit_还不错]', img:'rb2', name:'还不错' },
  { reg:'[rabbit_倒酒]', img:'rb3', name:'倒酒' },
  { reg:'[rabbit_没酒喝]', img:'rb4', name:'没酒喝' },
  { reg:'[rabbit_走一个]', img:'rb5', name:'走一个' },
  { reg:'[rabbit_嘿嘿]', img:'rb6', name:'嘿嘿' },
  { reg:'[rabbit_红酒杯]', img:'rb7', name:'红酒杯' },
  { reg:'[rabbit_自罚一杯]', img:'rb8', name:'自罚一杯' },
  { reg:'[rabbit_摇耳朵]', img:'rb9', name:'摇耳朵' },
  { reg:'[rabbit_捂脸]', img:'rb10', name:'捂脸' },
  { reg:'[rabbit_坏笑]', img:'rb11', name:'坏笑' },
  { reg:'[rabbit_蹦迪]', img:'rb12', name:'蹦迪' },
  { reg:'[rabbit_安排]', img:'rb13', name:'安排' },
  { reg:'[rabbit_赞]', img:'rb14', name:'赞' },
  { reg:'[rabbit_酒归你]', img:'rb15', name:'酒归你' },
  { reg:'[rabbit_来啊]', img:'rb16', name:'来啊' },
  { reg:'[rabbit_笔芯]', img:'rb17', name:'笔芯' },
  { reg:'[rabbit_买买买]', img:'rb18', name:'买买买' },
  { reg:'[rabbit_出来玩]', img:'rb19', name:'出来玩' },
  { reg:'[rabbit_打脑壳]', img:'rb20', name:'打脑壳' },
  { reg:'[rabbit_嗨]', img:'rb21', name:'嗨' },
  { reg:'[rabbit_拜]', img:'rb22', name:'拜' },
  { reg:'[rabbit_白眼]', img:'rb23', name:'白眼' },
  { reg:'[rabbit_沉迷工作]', img:'rb24', name:'沉迷工作' },
  { reg:'[rabbit_没看见]', img:'rb25', name:'没看见' },
  { reg:'[rabbit_不约]', img:'rb26', name:'不约' },
  { reg:'[rabbit_努力加油]', img:'rb27', name:'努力加油' },
  { reg:'[rabbit_吃瓜]', img:'rb28', name:'吃瓜' },
  { reg:'[rabbit_没错]', img:'rb29', name:'没错' },
  { reg:'[rabbit_暗中观察]', img:'rb30', name:'暗中观察' },
  { reg:'[rabbit_你继续]', img:'rb31', name:'你继续' },
  { reg:'[rabbit_太难了]', img:'rb32', name:'太难了' },
].map(item => ({ ...item, img: getEmojiImg(item.img) })))

export const CommonEmojiList = Object.freeze([
  "😀","😁","😂","🤣","😃","😄","😅","😆","😉","😊",
  "😋","😎","😍","😘","😗","😙","😚","☺️","🙂","🤗",
  "🤩","🤔","🤨","😐","😑","😶","🙄","😏","😣","😥",
  "😮","🤐","😯","😪","😫","😴","😌","😛","😜","😝",
  "🤤","😒","😓","😔","😕","🙃","🤑","😲","☹️","🙁",
  "😖","😞","😟","😤","😢","😭","😦","😧","😨","😩",
  "🤯","😬","😰","😱","😳","🤪","😵","😡","😠","🤬",
  "😷","🤒","🤕","🤢","🤮","🤧","😇","🤠","🤡","🤥",
  "🤫","🤭","🧐","🤓","😈","👿","👹","👺","💀","👻",
  "👽","🤖","💩","😺","😸","😹","😻","😼","😽","🙀",
  "😿","😾"
])
