const OSS_BASE_HOST = 'https://images.vinehoo.com'
const OSS_ICON_URL = `${OSS_BASE_HOST}/vinehoomini/v3`

export const ossIcon = (src = '', size = 0, OSS_HOST = OSS_ICON_URL) => {
  const isIncluHttp = src.includes('http')
  if (size === 0) {
    return isIncluHttp ? src : `${OSS_HOST}${src}`
  } else {
    return isIncluHttp ? `${src}-${size}x${size}` : `${OSS_HOST}${src}-${size}x${size}`
  }
}

export function registerOssPlugin(Vue) {
  Vue.prototype.ossIcon = ossIcon
}
