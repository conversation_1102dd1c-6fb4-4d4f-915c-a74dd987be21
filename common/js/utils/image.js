// 图片工具函数
const install = (Vue, vm) => {
	// 预览单个图片
	let previewImage = ( urls = [], index = 0 ) => {
		console.log(urls)
		console.log(index)
		uni.previewImage({
			urls,
			current: urls[index]
		})
	}
	
	let previewImageList = ( urls = [], current = 0, indicator = true ) => {
		uni.previewImage({
		  urls,
		  current,
		  indicator,
		})
	}
	
	const saveImageToPhotosAlbum = ( url ) => {
		// #ifdef H5
		if (vm.$app) {
			wineYunJsBridge.openAppPage({
				client_path: { ios_path: 'downloadFile', android_path: 'downloadFile' }, 
				ad_path_param: [
					{ ios_key: 'url', ios_val: url, android_key: 'url', android_val: url },
					{ android_key: 'suffix', android_val: 'png' }
				]
			})
		}else {
			vm.feedback.toast({ title: '亲，请长按图片保存~' })
		}
		// #endif
		// #ifdef MP-MP-WEIXIN
		uni.downloadFile({
			url,
			success: res => {
				console.log('res', res)
				if( res.tempFilePath ) {
					uni.saveImageToPhotosAlbum({
						filePath: res.tempFilePath,
						success: () => vm.feedback.toast({ title: '保存成功' }),
						fail: (err) => {
							console.log(err)
							vm.feedback.toast({ title: '保存失败' })
						}
					})
				}
			}
		})
		// #endif
	} 
	
	// 挂载到Vue.prototype
	Vue.prototype.image = {
		previewImage, //预览单个图片
		previewImageList, //预览多个图片
		saveImageToPhotosAlbum, //保存图片到相册
	}
}

// 导出方法
export default { install }