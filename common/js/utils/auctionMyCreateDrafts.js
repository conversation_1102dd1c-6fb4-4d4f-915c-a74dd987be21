const { uid } = uni.getStorageSync('loginInfo') || { uid: '' }
const key = `auctionMyCreateDrafts-${uid}`

const getDraftList = () => {
  return uni.getStorageSync(key) || []
}

const getDraftCount = () => {
  return getDraftList().length
}

const saveDraftList = (list) => {
  list = list.slice(0, 50)
  uni.setStorageSync(key, list)
}

const getGoodsByDraftId = (draftId) => {
  const list = getDraftList()
  return list.find(item => item.id === draftId)
}

const removeGoodsByDraftId = (draftId) => {
  const list = getDraftList()
  const index = list.findIndex(item => item.id === draftId)
  if (index === -1) return
  list.splice(index, 1)
  saveDraftList(list)
}

const updateGoods = (goods) => {
  const list = getDraftList()
  const index = list.findIndex(item => item.id === goods.id)
  if (index === -1) return
  list.splice(index, 1, goods)
  saveDraftList(list)
}

const unshiftGoods = (goods) => {
  const list = getDraftList()
  list.unshift(goods)
  saveDraftList(list)
}

const getDraftIdByOrderNo = (orderNo) => {
  const list = getDraftList()
  return list.find(item => item.other_parameter && item.other_parameter.main_order_no === orderNo)?.id || ''
}

export default {
  getDraftList,
  getDraftCount,
  saveDraftList,
  getGoodsByDraftId,
  removeGoodsByDraftId,
  updateGoods,
  unshiftGoods,
  getDraftIdByOrderNo
}