import { AUCTION_PATH_PREFIX, AUCTION_SOURCE } from '@/common/js/fun/constant'

// 公共方法，登录相关
const install = (Vue, vm) => {
	const getLoginEvent = () => {
		const isFromAuctionPage = vm.pages.getPageFullPath().includes(AUCTION_PATH_PREFIX)
		if (vm.$app) {
			const platformKey = 'source_platform'
			const eventKey = 'source_event'
			const { platform, event } = AUCTION_SOURCE
			if (isFromAuctionPage) {
				return [
					{ ios_key: platformKey, ios_val: platform, android_key: platformKey, android_val: platform },
					{ ios_key: eventKey, ios_val: event, android_key: eventKey, android_val: event },
				]
			} else {
				return null
			}
		}
		return null
	}

	const openAppLoginPage = () => {
		const params = {
			client_path: { ios_path: 'LoginViewController', android_path: 'login' }
		}
		const ad_path_param = getLoginEvent()
		if (ad_path_param) params.ad_path_param = ad_path_param
		wineYunJsBridge.openAppPage(params)
	}

	// 判断用户是否登录 from = 从哪个端进入 （'' = 微信小程序或h5、 1 = 安卓、2 = ios），isJump = 是否跳转，identification = 标识（0 = 默认登录、1 = 门店登录）
	let isLogin = ( from = '', isJump = 1, identification = 0 ) => {
		if(from == '1'){ //安卓端获取用户数据（同步返回）
			let appUserInfo = wineYunJsBridge.getDataFromApp(1);
			try{
				uni.setStorageSync('loginInfo', JSON.parse(appUserInfo))
			}catch(e){
				feedback.toast({title:'保存app用户信息失败'})
			}
		}
		if(from == 'next'){ //鸿蒙端获取用户数据（同步返回）
			let appUserInfo = wineYunJsBridge.getDataFromApp(1);
			try{
				uni.setStorageSync('loginInfo', JSON.parse(appUserInfo))
			}catch(e){
				feedback.toast({title:'保存app用户信息失败'})
			}
		}
		if(from == '2'){//ios端获取用户数据（异步返回返回）
			wineYunJsBridge.openAppPage({client_path: 'appUserInfo', name: '获取用户信息'});
		}
		
		try {
		    const loginInfo = uni.getStorageSync('loginInfo');
		    if (loginInfo.uid && loginInfo.token) {
				return true
		    }else{
				if( isJump == 1 ){
					if(from == '1' || from == '2' || from == 'next' ){ //App端获取用户信息
						openAppLoginPage()
					}else{
						if( identification == 1 ) { //门店登录
							vm.jump.navigateTo(`${vm.$store.state.routeTable.pgLogin}?isStore=1`)
						}else{ //普通登录
							vm.jump.navigateTo(vm.$store.state.routeTable.pgLogin)
						}
					}
				}
			}
		} catch (err) {
		    // error
			console.log(err)
			vm.feedback.toast({title: '获取用户信息失败'})
		}
	}
	
	// 清除登录数据
	let clearLoginData = () => {
		// 默认保留版本信息、用户唯一id
		let version = uni.getStorageSync('version'); //版本号
		let uniqueId = uni.getStorageSync('uniqueId'); // 全局唯一id
		uni.clearStorageSync(); //清空缓存数据
		if(version) uni.setStorageSync('version', version);
		if(uniqueId) uni.setStorageSync('uniqueId', uniqueId);
	}

	const isLoginV2 = (from = '', isJump = 1) => {
		const judgeIsLogin = () => {
			const loginInfo = uni.getStorageSync('loginInfo')
			if (loginInfo.uid && loginInfo.token) {
				return true
			} else {
				if (isJump == 1) {
					if (from == '1' || from == '2' || from == 'next') {
						openAppLoginPage()
					} else {
						vm.jump.navigateTo(vm.$store.state.routeTable.pgLogin)
					}
				}
				return false
			}
		}
	
		return new Promise((resolve, reject) => {
			if (from == '1') {
				const appUserInfo = wineYunJsBridge.getDataFromApp(1)
				uni.setStorageSync('loginInfo', JSON.parse(appUserInfo))
				judgeIsLogin() ? resolve() : reject()
			} else if (from == '2') {
				window.onAppUserInfoSuccess = () => {
					judgeIsLogin() ? resolve() : reject()
				}
				wineYunJsBridge.openAppPage({client_path: 'appUserInfo', name: '获取用户信息'})
			} else if (from == 'next') {
				const appUserInfo = wineYunJsBridge.getDataFromApp(1)
				uni.setStorageSync('loginInfo', JSON.parse(appUserInfo))
				judgeIsLogin() ? resolve() : reject()
			} else {
				judgeIsLogin() ? resolve() : reject()
			}
		})
	}

	const isLoginV3 = (from = '', isJump = 1) => {
		return new Promise((resolve) => {
			isLoginV2(from, isJump).then(() => {
				resolve(true)
			}).catch(() => {
				resolve(false)
			})
		})
	}
	
	// 挂载到Vue.prototype
	Vue.prototype.login = {
		isLogin, // 判断用户是否登录
		clearLoginData, //清除登录数据
		isLoginV2,
		isLoginV3
	}
}

// 导出方法
export default { install }