export const MAuctionIndexAreaType = { // 拍卖首页金刚区类型
  Fixed: 1, // 固定模式
  Activity: 2, // 活动
  MiaoFa: 3, // 秒发
}

export const MAuctionAreaFixedType = { // 拍卖金刚区固定类型
  Official: 1, // 官方拍卖
  Personal: 2, // 个人拍卖
  OneYuan: 3, // 一元起拍
  ImmediatelyAbort: 4, // 即将截拍
  Recommend: 1230, // 推荐
}

export const MAuctionIndexGoodsLabel = { // 拍卖首页拍品标签
  Official: 0, // 官方拍卖
  Personal: 1, // 个人拍卖
  OneYuan: 2, // 一元起拍
  Recommend: 3, // 推荐
  ImmediatelyAbort: 4, // 即将截拍
}

export const MAuctionTypeToLabel = {
  [MAuctionAreaFixedType.Official]: MAuctionIndexGoodsLabel.Official,
  [MAuctionAreaFixedType.Personal]: MAuctionIndexGoodsLabel.Personal,
  [MAuctionAreaFixedType.OneYuan]: MAuctionIndexGoodsLabel.OneYuan,
  [MAuctionAreaFixedType.ImmediatelyAbort]: MAuctionIndexGoodsLabel.ImmediatelyAbort,
  [MAuctionAreaFixedType.Recommend]: MAuctionIndexGoodsLabel.Recommend,
}

export const MAuctionGoodsStatus = { // 拍卖商品状态
  PendPutaway: 0, // 待上架
  PendAuction: 1, // 待开拍
  OnAuction: 2, // 竞拍中
  Disabled: 3, // 已下架(没用到)
  AuctionAbort: 4, // 已截拍
  Unsuccessful: 5 // 已流拍
}

export const MAuctionMyParticipationStatus = Object.freeze({ //我的参拍状态
    Bidding: 0, //竞拍中
	Shot: 1, //已拍中
	NotTaken: 2, //未拍中
})

export const MAuctionGoodsReviewStatus = { // 拍卖商品审核状态
  PendBind: 0, // 待绑定
  PendReview: 1, // 待审核
  OnReview: 2, // 审核中
  PassReview: 3, // 已审核
  Rejected: 4, // 已驳回
}

export const MAuctionEarnestType = { // 拍卖保证金类型
  Bidding: 1, // 竞拍保证金
  Entrust: 2 // 委托保证金
}

export const MAuctionRNType = { // 拍卖实名认证类型
  NotRN: 0, // 未认证
  PersonalRN: 1, // 个人认证
  CompanyRN: 2 // 商户认证
}

export const MAuctionRNStatus = { // 拍卖实名认证状态
  NotSubmit: 0, // 未提交
  OnCheck: 1, // 审核中(待审核)
  Passed: 2, // 已通过
  Rejected: 3 // 已驳回
}

export const MAuctionNoticeType = { // 拍卖通知类型
  Auction: 1, // 拍卖
  Notice: 2, // 通知
  Order: 3, // 订单
  Comment: 4 // 评论
}

export const MAuctionGoodsCategory = { // 拍品分类
  Wine: 1, // 葡萄酒
  WhiteSpirits: 0, // 白酒
  Liqueur: 2, // 烈酒
}

export const MAuctionFundsType = { // 拍卖资金类型
  Expend: 1, // 支出
  Income: 2 // 收入
}

export const MAuctionFundsOrderType = { // 拍卖资金订单类型
  Earnest: 1, // 保证金
  AuctionOrder: 2 // 拍卖订单
}

export const MAuctionRemindOperation = { // 拍卖提醒操作
  Add: 1, // 添加
  Delete: 2 // 删除
}

export const MAuctionCreditValuesTab = {
  All: 0, // 全部
  Add: 1, // 增加
  Sub: 2 // 减少
}

export const MAuctionCreditValuesOperation = { // 拍卖信用分操作
  Add: 0, // 增加
  Sub: 1 // 减少
}

export const MAuctionFundsStatus = { // 拍卖资金状态
  Prepaid: 1, // 已支付
  Freeze: 2, // 冻结中
  InAuction: 3, // 竞拍中
  Returned: 4, // 已退还
  Receive: 5, // 确认收货后返回
  IllegalDeduction: 6, // 违规扣除
  Canceled: 7 // 已取消
}

export const MAuctionEarnestStatus = { // 拍卖保证金状态
  PendAuction: 1, // 待拍中
  Freeze: 2, // 冻结中
  InAuction: 3, // 竞拍中
  Returned: 4, // 已退还
  Receive: 5, // 确认收货后返回
  IllegalDeduction: 6, // 违规扣除
  Canceled: 7 // 已取消
}

export const MAuctionCertificateType = { // 拍卖证书类型
  Buyer: 0, // 买方
  Seller: 1 // 卖方
}

export const MAuctionIndexBannerJumpType = { // 拍卖首页Banner跳转类型
  InnerLink: 1, // 内链
  OuterLink: 2, // 外链
}

export const MAuctionIndexBannerInnerLinkType = { // 拍卖首页Banner内链类型
  AuctionGoodsDetail: 1, // 拍品详情页
}

export const MPaymentMethod = { // 支付方式
  AliApp: 0, // 支付宝APP
  AliH5: 1, // 支付宝H5
  AliPC: 2, // 支付宝PC
  WxAPP: 3, // 微信APP
  WxMini: 4, // 微信小程序
  WxH5: 5, // 微信H5
  TikTokAli: 6, // 抖音支付宝
  WxJSAPI: 7, // 微信JSAPI(公众号支付)
  TikTokWx: 8, // 抖音微信
  RabbitHead: 201, // 兔头
  GiftCard: 202, // 礼品卡
}

export const MAppPaymentSource = { // app支付来源
  CrossBorder: 1, // 跨境
  AuctionOrder: 2, // 拍卖订单
  AuctionEarnest: 3, // 拍卖保证金
}

export const MInvoiceType = { // 发票类型
  General: 1, // 普通发票
  Special: 2, // 专用发票
}

export const MInvoiceFrontType = { // 发票抬头类型
  Person: 1, // 个人
  Company: 2, // 公司
}

export const MSecondsWfitemType = { // 秒发瀑布流item类型
  AdSwiper: 'ad_swiper', // 广告轮播
  Goods: 'goods', // 商品
  Auction: 'auction', // 拍品
  Party: 'party', // 酒会
  News: 'news', // 酒闻
  Wine: 'wine', // 酒评
  Interested: 'interested', // 感兴趣分类
  GuessLike: 'guess_like', // 你可能喜欢
  Investigates: 'questionnaire',
}

export const MSWfitemTypeToIncParamsKey = {
  [MSecondsWfitemType.Goods]: 'goods_params',
  [MSecondsWfitemType.Auction]: 'auction_params',
  [MSecondsWfitemType.Party]: 'winepart_params',
  [MSecondsWfitemType.News]: 'news_params',
}

export const MSWfitemIncRes = {
  good: {
    type: MSecondsWfitemType.Goods,
    id: 'period',
    purchased: 'saled_count',
  },
  auction: {
    type: MSecondsWfitemType.Auction,
    id: 'id',
    viewnums: 'pageviews',
  },
  winepart: {
    type: MSecondsWfitemType.Party,
    id: 'id',
    num: 'remaining_quota',
  },
  news: {
    type: MSecondsWfitemType.News,
    id: 'id',
    viewnums: 'pageviews',
  },
}

export const MProductLabelType = { // 产品标签类型
  RedWhite: 1, // 新人价、每日秒杀、拼团...样式
  FullDiscount: 2, // 满减立减样式
  WeekNew: 3, // 本周上新样式
  Top1: 4, // top1样式
  Top2: 5, // top2样式
  Top3: 6, // top3样式
  ColdChain: 7, // 冷链包邮
  Vmall: 8, // 3小时达、次日达
}
  
export const MPeriodsType = { // 商品类型
  FlashPurchase: 0, // 闪购
  Seconds: 1, // 秒发
  CrossBorder: 2, // 跨境
  Leftover: 3, // 尾货
}

export const MUserPortraitFeedbackType = { // 用户喜欢反馈类型
  LoseInterest: 'lose_interest', // 不感兴趣
  HaveBought: 'have_bought', // 已经买了
  ContentRepetition: 'content_repetition', // 内容重复
}

export const MVhWaterfallType = { // 瀑布流类型
  Miaofa: 'miaofa',
}

export const MMiaofaSearchType = { // 秒发搜索类型
  Goods: 0, // 商品
  Auction: 1, // 拍卖
  Content: 2, // 内容
  WineParty: 3, // 酒会
}

export const MGoodsStatus = { // 商品状态
  PendPutaway: 0, // 待上架
  PendSale: 1, // 待售中
  OnSale: 2, // 在售中
  Disabled: 3, // 已下架
  SaleOut: 4, // 已售罄
}

export const MSearchFeedbackType = { // 搜索反馈类型
  Unevaluated: 0, // 未评价
  Satisfaction: 1, // 满意
  Ordinary: 2, // 一般
  Dissatisfaction: 3, // 不满意
}

export const MInvestigatesFeedbackType = {
  Satisfaction: 0,
  Dissatisfaction: 1,
}