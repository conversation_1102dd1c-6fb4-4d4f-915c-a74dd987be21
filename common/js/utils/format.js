// 格式处理工具函数
const install = ( Vue, vm ) => {
	//数字格式 num = 数量 （ 过万保留单位万、过亿保留单位亿 ）
	let numberFormat = ( num = 0 ) => {
		console.log('-------------------------------------我是数量')
		console.log(num)
		let length = num.toString().length
		if( length >= 5 ){ //过万
			if( length >= 9 ) { //过亿
				let num2 = num/100000000
				let value = (Math.floor(num2 * 100) / 100).toFixed(0)
				return `${value}亿`
			}else{ //过万
				let num2 = num/10000
				// let value = parseFloat(num2).toFixed(2)
				let value = (Math.floor(num2 * 100) / 100).toFixed(0)
				return `${value}万`
			}
		}else{ //千
			return num
		}
	}
	
	
	// 挂载到Vue.prototype
	Vue.prototype.format = {
		numberFormat, //处理数字格式
	}
}

// 导出方法
export default { install }