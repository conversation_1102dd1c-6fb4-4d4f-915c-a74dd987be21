// 公共方法，网络相关
const install = (Vue, vm) => {
	// 获取当亲网络信息
	let getNetworkType = () => {
		uni.getNetworkType({
		    success: res => {
				if(res.networkType == 'none') {
					console.log('getNetworkType noNetwork')
					let currPage = getCurrentPages().pop()
					if(currPage.route == 'packageF/pages/no-network/no-network') return
					vm.$u.throttle(() => {
						vm.jump.navigateTo('/packageF/pages/no-network/no-network')
					}, 500)
				}else {
					console.log('getNetworkType onNetwork')
				}
			}
		});
	}
	
	// 监听网络变化的回调函数
	let networkCallBack = (res) => {
		if(!res.isConnected){
			vm.feedback.toast({title: '网络断开！', icon: 'error'})
		}
	}
	
	// 监听网络变化
	let onNetworkStatusChange = () => {
		uni.onNetworkStatusChange(networkCallBack)
	}
	
	// 取消监听网络变化
	let offNetworkStatusChange = () => {
		uni.offNetworkStatusChange(networkCallBack)
	}
	
	// 挂载到Vue.prototype
	Vue.prototype.network = {
		getNetworkType, //获取网络状态
		onNetworkStatusChange, //监听网络变化
		offNetworkStatusChange //取消监听网络变化
	}
}

// 导出方法
export default { install }