import { mapState } from 'vuex'
import { MSecondsWfitemType } from '@/common/js/utils/mapperModel'

export default {
  name: 'secondWfitemMixin',
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data: () => ({
    leftTopTagClazz: 'p-abso top-16 left-16 z-03 flex-c-c w-56 h-26 font-18 text-ffffff bg-000-000-000-038 b-rad-04',
    leftTopTagStyle: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      width: '200%',
      height: '200%',
      whiteSpace: 'nowrap',
      fontSize: '36rpx',
      transform: 'scale(0.5)',
    },
    // titleClazz: 'h-max-72 font-26 text-3 l-h-36 text-justify o-hid'
    titleClazz: 'h-max-72 font-26 text-3 l-h-36 text-hidden-2',
    pdClazz: 'ptb-20-plr-16',
    mtClazz: 'mt-16'
  }),
  computed: {
    ...mapState(['routeTable']),
    $wfitemIdKey () {
      return this.item.$wfitemIdKey
    }
  },
  methods: {
    handleJump (url) {
      const { type, id, $isRecommended } = this.item
      if ([MSecondsWfitemType.Goods].includes(type) && !$isRecommended) {
        this.item.$isRecommended = true
        this.$emit('changePendInsertId', { [this.$wfitemIdKey]: this.item[this.$wfitemIdKey], id })
      }
      this.jump.appAndMiniJump(1, url, this.$vhFrom)
    },
    handleLongpress () {
      if (this.item.$reportDisabled) return
      this.$emit('changeShowSecWfitemMaskId', this.item[this.$wfitemIdKey])
    },
    emitRemove () {
      this.$emit('remove', this.item[this.$wfitemIdKey])
    }
  }
}
