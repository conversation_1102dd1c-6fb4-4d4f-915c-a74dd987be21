export default {
  name: 'listMixin',
  data: () => ({
    loading: true,
    list: [],
    query: {
      page: 1,
      limit: 10,
    },
    totalPage: 0,
    currentTabIndex: 0,
    reachBottomLoadStatus: 'loading',
    isLastIdPattern: false,
    isListPattern: false,
  }),
  methods: {
    load () {
    },
    async reload () {
      const query = this.isLastIdPattern ? { ...this.query, last_id: 0 } : { ...this.query, page: 1 }
      await this.load(query)
    },
    async reachBottomLoad () {
      if (this.reachBottomLoadStatus === 'loading') return
      if (this.isLastIdPattern) {
        if (!this.query.last_id) return
      } else if (this.isListPattern) {
        if (this.reachBottomLoadStatus === 'nomore') return
      } else {
        if (this.query.page === this.totalPage || !this.totalPage) return
      }
      this.reachBottomLoadStatus = 'loading'
      const query = this.isLastIdPattern ? this.query : { ...this.query, page: this.query.page + 1 }
      await this.load(query).catch(() => {
        this.reachBottomLoadStatus = 'loadmore'
      })
    },
    async changeTabIndex (index) {
      const query = this.isLastIdPattern ? { ...this.query, last_id: 0 } : { ...this.query, page: 1 }
      await this.load(query, index)
      this.onChangeToScroll()
    },
    async pullDownRefresh () {
      const query = this.isLastIdPattern ? { ...this.query, last_id: 0 } : { ...this.query, page: 1 }
      await this.load(query)
    },
    onChangeToScroll () {
      this.$nextTick(() => {
        this.system.pageScrollTo(0, 0)
      })
    },
  },
  onLoad () {
    if (this.isLastIdPattern) {
      delete this.query.page
      this.query = { ...this.query, last_id: 0 }
    } else if (this.isListPattern) {
      delete this.query.limit
    }
    const load = this.load
    function loadFun (query = this.query, currentTabIndex = this.currentTabIndex) {
      this.feedback.loading()
      return load(query, currentTabIndex).then((res) => {
        const { $pendUpdateObj = {} } = res
        Object.keys($pendUpdateObj).forEach(key => {
          this[key] = $pendUpdateObj[key]
        })
        const { total = 0, last_id, list } = res?.data || {}
        const { page, limit } = query
        if (this.isLastIdPattern) {
          this.query = { ...this.query, last_id }
        } else if (this.isListPattern) {
          this.query = query
        } else {
          this.totalPage = Math.ceil(total / limit)
          this.query = query
        }
        this.currentTabIndex = currentTabIndex
        if (this.isLastIdPattern) {
          this.reachBottomLoadStatus = !last_id ? 'nomore' : 'loadmore'
        } else if (this.isListPattern) {
          this.reachBottomLoadStatus = list && list.length ? 'loadmore' : 'nomore'
        } else {
          this.reachBottomLoadStatus = page == this.totalPage || !this.totalPage ? 'nomore' : 'loadmore'
        }
        this.loading = false
        return res
      }).catch(() => {
        this.reachBottomLoadStatus = 'loadmore'
        return { data: { list: this.list } }
      }).finally(() => {
        this.feedback.hideLoading()
        uni.stopPullDownRefresh()
      })
    }
    this.load = loadFun.bind(this)
  }
}
