import { NEW_PEOPLE_ACTIVITY_URL } from '@/common/js/fun/constant'
import { mapState } from 'vuex'
export default {
  name: 'NewPeopleMixin',
  data: () => ({
    newPeopleCouponActivityInfo: {},
    newPeopleGoodsList: [],
    newPeopleCouponPackage: {},
    isNewUser: 0,
    showNewPeopleIndexMask: false,
    receivedBenefits: false,
    receiveBenefitsLoading: false,
    getNPCAILoading: false,
  }),
  computed: {
    ...mapState('newPeopleArea', ['showNewPeopleFloatingFrame']),

    newPeopleAreaVisible({ isNewUser, newPeopleCouponActivityInfo, newPeopleCouponPackage }) {
      return !!(
        isNewUser &&
        Object.keys(newPeopleCouponActivityInfo).length &&
        Object.keys(newPeopleCouponPackage).length
      )
    },

    newPeopleFloatingFrameVisible({ newPeopleAreaVisible, showNewPeopleFloatingFrame }) {
      return !!(newPeopleAreaVisible && showNewPeopleFloatingFrame)
    },

    newPeopleIndexMaskVisible({
      newPeopleAreaVisible,
      newPeopleCouponPackage,
      receivedBenefits,
      showNewPeopleIndexMask,
    }) {
      const { collect_status } = newPeopleCouponPackage
      return (
        !!(newPeopleAreaVisible && (collect_status === 0 || (collect_status === 1 && receivedBenefits))) &&
        showNewPeopleIndexMask
      )
    },
  },
  onShow() {
    // this.getIsNewUser(),
    this.getNewPeopleCouponActivityInfo()
  },
  methods: {
    async getIsNewUser() {
      if (this.login.isLogin(this.$vhFrom, 0)) {
        const {
          data: { is_new_user = 0 },
        } = await this.$u.api.userSpecifiedData({ field: 'is_new_user' })
        this.isNewUser = +is_new_user
      } else {
        this.isNewUser = 1
      }
    },
    async getNewPeopleCouponActivityInfo() {
      try {
        if (this.getNPCAILoading) return
        this.getNPCAILoading = true
        const res = await this.$u.api.newPeopleCouponActivityInfo({ bection: 1 })
        const { data } = res
        this.newPeopleCouponActivityInfo = data || {}
        this.newPeopleGoodsList = data.goods_list || []
        this.newPeopleCouponPackage = data.coupon_package || {}
        // this.showNewPeopleIndexMask = true
        uni.getStorage({
          key: 'newPeopleIndexMaskCountDown',
          success: (res) => {
            const { data: storageDate } = res
            const currentDate = this.$u.timeFormat(Date.now(), 'yyyy-mm-dd')
            // if( storageDate == currentDate ) this.showNewPeopleIndexMask = false
            this.showNewPeopleIndexMask = !(storageDate === currentDate)
            console.log(storageDate)
            console.log(currentDate)
          },
          fail: () => {
            this.showNewPeopleIndexMask = true
          },
        })
      } finally {
        this.getNPCAILoading = false
        this.receiveBenefitsLoading = false
      }
    },
    getNewPeopleReceiveBenefits() {
      if (this.login.isLogin(this.$vhFrom)) {
        if (this.getNPCAILoading || this.receiveBenefitsLoading) return
        this.feedback.loading({ title: '领取中' })
        this.receiveBenefitsLoading = true
        this.$u.api
          .newPeopleReceiveBenefits()
          .then((res) => {
            this.receivedBenefits = true
            this.getNewPeopleCouponActivityInfo()
          })
          .catch(() => {
            this.receiveBenefitsLoading = false
          })
      }
    },
    onJumpActivityPage() {
      this.jump.h5Jump(NEW_PEOPLE_ACTIVITY_URL(), this.$vhFrom, 4)
    },

    closeNewPeopleIndexMask() {
      uni.setStorageSync('newPeopleIndexMaskCountDown', this.$u.timeFormat(Date.now(), 'yyyy-mm-dd'))
      this.showNewPeopleIndexMask = false
    },
  },
}
