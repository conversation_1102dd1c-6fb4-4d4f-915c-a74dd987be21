export default {
	name: 'navMsgMixin',
	data:() => ({
		isGetChannelKeyword: true,
		isGetShoppingCartNum: true,
		isGetMessageUnreadNum: true,
		channelKeyword: '',
		shoppingCartNum: 0,
		unReadTotalNum: 0,
		channelSection: 2
	}),
	onShow() {
		this.getShoppingCartNum(), 
		this.getMessageUnreadNum()
	},
	onLoad() {
		this.getChannelKeyword()
	},
	methods: {
		async getChannelKeyword() {
			if (!this.isGetChannelKeyword) return
			let res = await this.$u.api.channelKeyword({ type: this.channelSection })
			this.channelKeyword = res?.data?.keyword || '大家都在搜'
		},
		async getShoppingCartNum() {
			if (!this.isGetShoppingCartNum) return
			if(this.login.isLogin(this.from, 0)){
				const res = await this.$u.api.shoppingCartNum()
				const num = res?.data || 0
				this.shoppingCartNum = num > 99 ? 99 : num
			}
		},
		async getMessageUnreadNum() {
			if (!this.isGetMessageUnreadNum) return
			if(this.login.isLogin(this.from, 0)){
				const res = await this.$u.api.messageUnreadNum()
				const num = res?.data?.total_num || 0
				this.unReadTotalNum = num > 99 ? 99 : num
			}
		},
	}
	
}