export default {
  name: 'longpressCopyMixin',
  data: () => ({
    jumpStatus: false,
    jumpTimeout: null
  }),
  methods: {
    handleJump(url, from, isReload = false) {
      if (this.jumpStatus) return
      if (isReload) {
        location.href = url
        return
      }
      this.jump.appAndMiniJump(1, url, from )
    },
    handleLongpress(title = '', from) {
      const isIos = from === '2'
      if (isIos) {
        this.jumpStatus = true
        this.copy.appCopy(title)
      } else {
        this.copy.appCopy(title)
      }
    },
    handleTouched(from) {
      const isIos = from === '2'
      if (isIos) {
        this.jumpTimeout = setTimeout(() => {
          this.jumpStatus = false
          this.jumpTimeout && clearTimeout(this.jumpTimeout)
        }, 200)
      }
    }
  }
}
