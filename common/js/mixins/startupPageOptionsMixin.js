import { mapState, mapMutations } from 'vuex'
export default {
	data: () => ({
		tabBarType: 1,
		startupOptionsVisible: false,
	}),
	methods: {
		recordPages({ type, count }) {
			console.log(this.userIsLogin)
			console.log(type)
			console.log(count)
			if( !this.userIsLogin ) return
			const startupOptionsRecord = uni.getStorageSync('startupOptionsRecord') || ''
			if( !startupOptionsRecord && count >= 2 ) {
				this.tabBarType = type
				this.startupOptionsVisible = true
				uni.setStorageSync('startupOptionsRecord', 1)
			}
		}
	},
}