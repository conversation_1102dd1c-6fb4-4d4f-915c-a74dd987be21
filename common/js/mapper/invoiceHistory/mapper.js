import * as mapperModel from './model'
const {
	MInvoiceHistoryFilterType, //发票历史过滤类型
	MInvoiceHistoryType, //发票历史类型
	MInvoiceHistoryInvoiceStatus, // 发票历史发票状态
	MInvoiceHistoryDetailReceipt //发票历史详情发票
} = mapperModel

export const MInvoiceHistoryTypeList = Object.freeze([ //发票历史过滤类型列表
  {
    value: MInvoiceHistoryType.All,
    text: '全部发票类型'
  },
  {
    value: MInvoiceHistoryType.General,
    text: '电子普通发票'
  },
  {
    value: MInvoiceHistoryType.Special,
    text: '电子专用发票'
  },
])

export const MInvoiceHistoryFilterTypeList = Object.freeze([ //发票历史过滤类型列表
  {
    value: MInvoiceHistoryFilterType.All,
    text: '全部发票类型'
  },
  {
    value: MInvoiceHistoryFilterType.InvoiceTime,
    text: '开票时间'
  },
])

export const MInvoiceHistoryTypeInfo = Object.freeze({ //发票历史类型信息
 [MInvoiceHistoryType.General] : '电子普通发票',
 [MInvoiceHistoryType.Special] : '电子专用发票'
})

export const MInvoiceHistoryInvoiceStatusInfo = Object.freeze({ // 发票历史发票状态信息
	[MInvoiceHistoryInvoiceStatus.Pending]: {
		$statusIcon: '/order_invoice_history_detail/wait.png',
		$statusText: '开票中',
		$statusTextClazz: 'text-e80404'
	},
	[MInvoiceHistoryInvoiceStatus.Success]: {
		$statusIcon: '/order_invoice_history_detail/succ.png',
		$statusText: '已开票',
		$statusTextClazz: 'text-6'
	},
	[MInvoiceHistoryInvoiceStatus.Fail]: {
		$statusIcon: '/order_invoice_history_detail/fail.png',
		$statusText: '开票失败',
		$statusTextClazz: 'text-6'
	},
	[MInvoiceHistoryInvoiceStatus.Cancel]: {
		$statusIcon: '/order_invoice_history_detail/fail.png',
		$statusText: '开票作废',
		$statusTextClazz: 'text-6'
	},
})

export const MInvoiceHistoryDetailReceiptList = Object.freeze([ //发票历史详情发票列表
	{
	  key: MInvoiceHistoryDetailReceipt.Name,
	  info: {
		  name: '抬头名称',
	  }
	},
	{
	  key: MInvoiceHistoryDetailReceipt.Taxpayer,
	  info: {
		  name: '单位税号',
	  }
	},
	{
	  key: MInvoiceHistoryDetailReceipt.CompanyAddress,
	  info: {
		  name: '单位地址',
	  }
	},
	{
	  key: MInvoiceHistoryDetailReceipt.CompanyTel,
	  info: {
		  name: '单位电话',
	  }
	},
	{
	  key: MInvoiceHistoryDetailReceipt.OpeningBank,
	  info: {
		  name: '开户银行',
	  }
	},
	{
	  key: MInvoiceHistoryDetailReceipt.BankAccount,
	  info: {
		  name: '银行账号',
	  }
	},
	{
	  key: MInvoiceHistoryDetailReceipt.Email,
	  info: {
		  name: '邮箱地址'
	  }
	},
	
])