/* 一些常用的公共动画效果 */
/* 公共动画 */
/* 淡入 */
.fade-in{ 
	animation: fadeIn .3s ease-in-out;
	-webkit-animation: fadeIn .3s ease-in-out;
}

@-webkit-keyframes fadeIn {
  from { opacity: 0 }
  to { opacity: 1 }
}

@keyframes fadeIn {
  from { opacity: 0 }
  to { opacity: 1 }
}

/* 淡出 */
.fade-out {
	 animation: fadeOut .3s ease-out forwards;
	 -webkit-animation: fadeOut .3s ease-out forwards;
}

@-webkit-keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}


/* 从上往下淡入（小幅度，带透明度） */
.fade-in-down { 
	animation: fadeInDown .3s linear forwards;
	-webkit-animation: fadeInDown .3s linear forwards;
}

@-webkit-keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -20%, 0);
    transform: translate3d(0, -20%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -20%, 0);
    transform: translate3d(0, -20%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

/* 从上往下淡入（大幅度，不带透明） */
.fade-in-down-big {
  animation: fadeInDownBig .6s ease-in;
  -webkit-animation: fadeInDownBig .6s ease-in;
}

@-webkit-keyframes fadeInDownBig {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInDownBig {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }

  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

/* 从下往上淡入（小幅度，带透明） */
.fade-in-up-small { 
	animation: fadeInUpSmall .2s ease-in forwards;
	-webkit-animation: fadeInUpSmall .2s ease-in forwards;
}

/* @-webkit-keyframes fadeInUpSmall {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 1%, 0);
    transform: translate3d(0, 1%, 0);
  }

  to {
	opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
} */

@keyframes fadeInUpSmall {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 1%, 0);
    transform: translate3d(0, 1%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

/* 从下往上淡入（中幅度，带透明） */
.fade-in-up-medium { 
	animation: fadeInUpMedium .2s linear forwards;
	-webkit-animation: fadeInUpMedium .2s linear forwards;
}

@-webkit-keyframes fadeInUpMedium {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 20%, 0);
    transform: translate3d(0, 20%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInUpMedium {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 20%, 0);
    transform: translate3d(0, 20%, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

/* 列表卡片旋转x轴10度平铺（带透明读） */
.fade-in-roa-x-90 {
	animation: fadeInRotateX90 .3s ease-in forwards;
	-webkit-animation: fadeInRotateX90 .3s ease-in forwards;
}

@-webkit-keyframes fadeInRotateX90 {
	from {
		 -webkit-transform: rotateX(90deg);
		transform: rotateX(90deg);
		opacity: 0;
	}
	to{
		opacity: 1;
	}
}

@keyframes fadeInRotateX90 {
	from {
		 -webkit-transform: rotateX(90deg);
		transform: rotateX(90deg);
		opacity: 0;
	}
	to{
		opacity: 1;
	}
}

/* 果冻动画 (摇晃上面) */
.skew-top {
  animation: skewTop 1s linear;
  -webkit-animation: skewTop 1s linear;
}

@-webkit-keyframes skewTop {
	  0% {
		  -webkit-transform: skew(-10deg, 0);
		  transform: skew(-10deg, 0);
		  -webkit-transform-origin: left bottom;
		  transform-origin: left bottom;
	  }
	  20% {
		  -webkit-transform: skew(8deg, 0);
		  transform: skew(8deg, 0);
		  -webkit-transform-origin: right bottom;
		  transform-origin: right bottom;
	  }
	  40% {
		  -webkit-transform: skew(-6deg, 0);
		  transform: skew(-6deg, 0);
		  -webkit-transform-origin: left bottom;
		  transform-origin: left bottom;
	  }
	  60% {
		  -webkit-transform: skew(4deg, 0);
		  transform: skew(4deg, 0);
		  -webkit-transform-origin: right bottom;
		  transform-origin: right bottom;
	  }
	  80% {
	   -webkit-transform: skew(-2deg, 0);
	   transform: skew(-2deg, 0);
	   -webkit-transform-origin: left bottom;
	   transform-origin: left bottom;
	  }
	  100% {
	   -webkit-transform: skew(0deg, 0);
	   transform: skew(0deg, 0);
	   -webkit-transform-origin: right bottom;
	   transform-origin: right bottom;
	  }
}

@keyframes skewTop {
	 0% {
		  -webkit-transform: skew(-10deg, 0);
		  transform: skew(-10deg, 0);
		  -webkit-transform-origin: left bottom;
		  transform-origin: left bottom;
	  }
	  20% {
		  -webkit-transform: skew(8deg, 0);
		  transform: skew(8deg, 0);
		  -webkit-transform-origin: right bottom;
		  transform-origin: right bottom;
	  }
	  40% {
		  -webkit-transform: skew(-6deg, 0);
		  transform: skew(-6deg, 0);
		  -webkit-transform-origin: left bottom;
		  transform-origin: left bottom;
	  }
	  60% {
		  -webkit-transform: skew(4deg, 0);
		  transform: skew(4deg, 0);
		  -webkit-transform-origin: right bottom;
		  transform-origin: right bottom;
	  }
	  80% {
	   -webkit-transform: skew(-2deg, 0);
	   transform: skew(-2deg, 0);
	   -webkit-transform-origin: left bottom;
	   transform-origin: left bottom;
	  }
	  100% {
	   -webkit-transform: skew(0deg, 0);
	   transform: skew(0deg, 0);
	   -webkit-transform-origin: right bottom;
	   transform-origin: right bottom;
	  }
}


/* 骨架屏白光闪烁动画 */
.skeleton-twinkle {
	background: linear-gradient( 100deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, .5) 50%, rgba(255, 255, 255, 0) 60% );
	background-size: 200% 100%;
	background-position-x: 180%;
	-webkit-background: linear-gradient( 100deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, .5) 50%, rgba(255, 255, 255, 0) 60% );
	-webkit-background-size: 200% 100%;
	-webkit-background-position-x: 180%;
	animation: skeletonTwinkle 1s ease-in-out infinite;
	-webkit-animation: skeletonTwinkle 1s ease-in-out infinite;
}

@-webkit-keyframes skeletonTwinkle{
	to{
		background-position-x: -20%;
		-webkit-background-position-x: -20%;
	}
}

@keyframes skeletonTwinkle{
	to{
		background-position-x: -20%;
		-webkit-background-position-x: -20%;
	}
}


