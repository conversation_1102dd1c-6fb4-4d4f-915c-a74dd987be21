/* 页面公共样式 */
.vh-btn {
	margin: 0;
	padding: 0;
}

/* 置灰 */
.fil-gray100-opa40 {
	 -webkit-filter: grayscale(100%) opacity(40%);
	filter: grayscale(100%) opacity(40%);
}

.fil-gray100-opa100 {
	 -webkit-filter: grayscale(100%) opacity(100%);
	filter: grayscale(100%) opacity(100%);
}

.grayscale-100 {
	/* filter: grayscale(100%); */
}

.fil-blur10 { filter: blur(5px); }

/* 去除滚动条 */
::-webkit-scrollbar{ display: none; }

/* 防止图片闪一下 */
image{ will-change: transform; }

/* 过渡 */
.tran-1 { transition: all .8s; }
.tran-2 { transition: all .3s; }


/* 去除按钮边框 */
button::after{ border: none; }

/* 按钮边框 */
button { border-radius: 0; }

/* 定位 */
.p-stic{position: sticky;}
.p-abso{position: absolute;}
.p-fixed{ position: fixed; }
.p-rela{ position: relative; }

/* 定位距离 */

/* rpx */
/* 上rpx */
.top-n-156{ top: -156rpx; }
.top-n-64{ top: -64rpx; }
.top-n-60{ top: -60rpx; }
.top-n-48{ top: -48rpx; }
.top-n-44{ top: -44rpx; }
.top-n-24{ top: -24rpx; }
.top-n-16{ top: -16rpx; }
.top-n-14{ top: -14rpx; }
.top-n-10{ top: -10rpx; }
.top-n-06{ top: -6rpx; }
.top-n-04{ top: -4rpx; }
.top-n-02{ top: -2rpx; }
.top-0{ top: 0; }
.top-01{ top: 0.5px; }
.top-02{ top: 2rpx; }
.top-06{ top: 6rpx; }
.top-04{ top: 4rpx; }
.top-10{ top: 10rpx; }
.top-12{ top: 12rpx; }
.top-14{ top: 14rpx; }
.top-16{ top: 16rpx; }
.top-20{ top: 20rpx; }
.top-24{ top: 24rpx; }
.top-26{ top: 26rpx; }
.top-30{ top: 30rpx; }
.top-38{ top: 38rpx; }
.top-40{ top: 40rpx; }
.top-42{ top: 42rpx; }
.top-50{ top: 50rpx; }
.top-60{ top: 80rpx; }
.top-70{ top: 70rpx; }
.top-76{ top: 76rpx; }
.top-80{ top: 80rpx; }
.top-84{ top: 84rpx; }
.top-108{ top: 108rpx; }
.top-110{ top: 110rpx; }
.top-112{ top: 112rpx; }
.top-120{ top: 120rpx; }
.top-130{ top: 130rpx; }
.top-148{ top: 148rpx; }
.top-200{ top: 200rpx; }
.top-208{ top: 208rpx; }
.top-296{ top: 296rpx; }
.top-304{ top: 304rpx; }
.top-400{ top: 400rpx; }
.top-410{ top: 410rpx; }
.top-430{ top: 430rpx; }
.top-1260{ top: 1260rpx; }

/* 右rpx */
.right-n-52{ right: -52rpx; }
.right-n-40{ right: -40rpx; }
.right-n-20{ right: -20rpx; }
.right-n-18{ right: -18rpx; }
.right-n-16{ right: -16rpx; }
.right-n-14{ right: -14rpx; }
.right-n-12{ right: -12rpx; }
.right-n-10{ right: -10rpx; }
.right-n-04{ right: -4rpx; }
.right-n-02{ right: -2rpx; }
.right-0{ right: 0; }
.right-04{ right: 4rpx; }
.right-06{ right: 6rpx; }
.right-10{ right: 10rpx; }
.right-15{ right: 15rpx; }
.right-18{ right: 18rpx; }
.right-22{ right: 22rpx; }
.right-24{ right: 24rpx; }
.right-30{ right: 30rpx; }
.right-32{ right: 32rpx; }
.right-36{ right: 36rpx; }
.right-48{ right: 48rpx; }
.right-52{ right: 52rpx; }
.right-130{ right: 130rpx; }
.right-108{ right: 108rpx; }
.right-140{ right: 140rpx; }

/* 下rpx */
.bottom-n-110{ bottom: -110rpx; }
.bottom-n-104{ bottom: -104rpx; }
.bottom-n-32{ bottom: -32rpx; }
.bottom-n-30{ bottom: -30rpx; }
.bottom-n-24{ bottom: -24rpx; }
.bottom-n-16{ bottom: -16rpx; }
.bottom-n-10{ bottom: -10rpx; }
.bottom-n-08{ bottom: -8rpx; }
.bottom-n-02{ bottom: -2rpx; }
.bottom-0{ bottom: 0; }
.bottom-02{ bottom: 2rpx; }
.bottom-04{ bottom: 4rpx; }
.bottom-06{ bottom: 6rpx; }
.bottom-14{ bottom: 14rpx; }
.bottom-20{ bottom: 20rpx; }
.bottom-28{ bottom: 28rpx; }
.bottom-40{ bottom: 40rpx; }
.bottom-46{ bottom: 46rpx; }
.bottom-58{ bottom: 58rpx; }
.bottom-60{ bottom: 60rpx; }
.bottom-64{ bottom: 64rpx; }
.bottom-66{ bottom: 66rpx; }
.bottom-80{ bottom: 80rpx; }
.bottom-82{ bottom: 82rpx; }
.bottom-86{ bottom: 86rpx; }
.bottom-100{ bottom: 100rpx; }
.bottom-104{ bottom: 104rpx; }
.bottom-110{ bottom: 110rpx; }
.bottom-116{ bottom: 116rpx; }
.bottom-120{ bottom: 120rpx; }
.bottom-140{ bottom: 140rpx; }
.bottom-144{ bottom: 144rpx; }
.bottom-148{ bottom: 148rpx; }
.bottom-160{ bottom: 160rpx; }
.bottom-222{ bottom: 222rpx; }
.bottom-232{ bottom: 232rpx; }
.bottom-240{ bottom: 240rpx; }
.bottom-296{ bottom: 296rpx; }
.bottom-320{ bottom: 320rpx; }

/* 下（安全区域） */
.bottom-safe-area {
	bottom: 0;
	bottom: constant(safe-area-inset-bottom);  
	bottom: env(safe-area-inset-bottom);  
}

/* 左百分比 */
.left-p-16{ left: 15%; }
.left-p-50{ left: 50%; }
.left-p-80{ left: 80%; }

/* 左rpx */
.left-n-10{ left: -10rpx; }
.left-n-12{ left: -12rpx; }
.left-0{ left: 0; }
.left-12{ left: 12rpx; }
.left-16{ left: 16rpx; }
.left-20{ left: 20rpx; }
.left-24{ left: 24rpx; }
.left-28{ left: 28rpx; }
.left-34{ left: 34rpx; }
.left-32{ left: 32rpx; }
.left-36{ left: 36rpx; }
.left-46{ left: 46rpx; }
.left-52{ left: 52rpx; }
.left-40{ left: 40rpx; }
.left-94{ left: 94rpx; }


/* 层级 */
.z-n-01{z-index: -1;}
.z-0{z-index: 0;}
.z-01{z-index: 1;}
.z-02{z-index: 2;}
.z-03{z-index: 3;}
.z-04{z-index: 4;}
.z-09{z-index: 9;}
.z-10{z-index: 10;}
.z-100{z-index: 100;}
.z-600{z-index: 600;}
.z-978{z-index: 978;}
.z-979{z-index: 979;}
.z-980{z-index: 980;}
.z-997{z-index: 997;}
.z-998{z-index: 998;}
.z-999{z-index: 999;}
.z-1000{z-index: 1000;}
.z-9999{z-index: 9999;}

/* 宽高px */
/* 宽 */
/* 高 */
.h-px-25{ height: 25px; }
.h-px-40{ height: 40px; }
.h-px-45{ height: 45px; }
.h-px-46{ height: 46px; }
.h-px-48{ height: 48px; }
.h-px-59{ height: 59px; }
.h-px-84{ height: 84px; }
/* 宽高(百分比) */
/* 宽 */
.w-p10{ width: 10%; }
.w-p20{ width: 20%; }
.w-p25{ width: 25%; }
.w-p30{ width: 30%; }
.w-p50{ width: 50%; }
.w-p70{ width: 70%; }
.w-p60{ width: 60%; }
.w-p80{ width: 80%; }
.w-p90{ width: 90%; }
.w-p100{ width: 100%; }

/* 高 */
.h-p100{ height: 100%; }

/* 宽高 */
.wh-p100 { width: 100%; height: 100%; }

/* 宽高(rpx) */
/* 宽 */
.w-01{ width: 0.5px; }
.w-02{ width: 2rpx; }
.w-04{ width: 4rpx; }
.w-06{ width: 6rpx; }
.w-08{ width: 8rpx; }
.w-10{ width: 10rpx; }
.w-12{ width: 12rpx; }
.w-14{ width: 14rpx; }
.w-16{ width: 16rpx; }
.w-18{ width: 18rpx; }
.w-20{ width: 20rpx; }
.w-22{ width: 22rpx; }
.w-24{ width: 24rpx; }
.w-26{ width: 26rpx; }
.w-28{ width: 28rpx; }
.w-30{ width: 30rpx; }
.w-32{ width: 32rpx; }
.w-34{ width: 34rpx; }
.w-36{ width: 36rpx; }
.w-38{ width: 38rpx; }
.w-40{ width: 40rpx; }
.w-42{ width: 42rpx; }
.w-44{ width: 44rpx; }
.w-46{ width: 46rpx; }
.w-48{ width: 48rpx; }
.w-50{ width: 50rpx; }
.w-52{ width: 52rpx; }
.w-54{ width: 54rpx; }
.w-56{ width: 56rpx; }
.w-58{ width: 58rpx; }
.w-60{ width: 60rpx; }
.w-62{ width: 62rpx; }
.w-64{ width: 64rpx; }
.w-65{ width: 65rpx; }
.w-66{ width: 66rpx; }
.w-68{ width: 68rpx; }
.w-70{ width: 70rpx; }
.w-72{ width: 72rpx; }
.w-74{ width: 74rpx; }
.w-76{ width: 76rpx; }
.w-78{ width: 78rpx; }
.w-80{ width: 80rpx; }
.w-82{ width: 82rpx; }
.w-84{ width: 84rpx; }
.w-86{ width: 86rpx; }
.w-88{ width: 88rpx; }
.w-90{ width: 90rpx; }
.w-92{ width: 92rpx; }
.w-94{ width: 94rpx; }
.w-96{ width: 96rpx; }
.w-98{ width: 98rpx; }
.w-100{ width: 100rpx; }
.w-104{ width: 104rpx; }
.w-106{ width: 106rpx; }
.w-108{ width: 108rpx; }
.w-110{ width: 110rpx; }
.w-112{ width: 112rpx; }
.w-114{ width: 114rpx; }
.w-116{ width: 116rpx; }
.w-118{ width: 118rpx; }
.w-120{ width: 120rpx; }
.w-122{ width: 122rpx; }
.w-124{ width: 124rpx; }
.w-126{ width: 126rpx; }
.w-128{ width: 128rpx; }
.w-130{ width: 130rpx; }
.w-132{ width: 132rpx; }
.w-134{ width: 134rpx; }
.w-136{ width: 136rpx; }
.w-138{ width: 138rpx; }
.w-140{ width: 140rpx; }
.w-142{ width: 142rpx; }
.w-144{ width: 144rpx; }
.w-146{ width: 146rpx; }
.w-148{ width: 148rpx; }
.w-150{ width: 150rpx; }
.w-152{ width: 152rpx; }
.w-154{ width: 154rpx; }
.w-156{ width: 156rpx; }
.w-158{ width: 158rpx; }
.w-160{ width: 160rpx; }
.w-164{ width: 164rpx; }
.w-166{ width: 166rpx; }
.w-168{ width: 168rpx; }
.w-170{ width: 170rpx; }
.w-174{ width: 174rpx; }
.w-176{ width: 176rpx; }
.w-178{ width: 178rpx; }
.w-180{ width: 180rpx; }
.w-184{ width: 184rpx; }
.w-186{ width: 186rpx; }
.w-188{ width: 188rpx; }
.w-190{ width: 190rpx; }
.w-192{ width: 192rpx; }
.w-194{ width: 194rpx; }
.w-196{ width: 196rpx; }
.w-198{ width: 198rpx; }
.w-200{ width: 200rpx; }
.w-202{ width: 202rpx; }
.w-204{ width: 204rpx; }
.w-208{ width: 208rpx; }
.w-210{ width: 210rpx; }
.w-214{ width: 214rpx; }
.w-216{ width: 216rpx; }
.w-218{ width: 218rpx; }
.w-220{ width: 220rpx; }
.w-222{ width: 222rpx; }
.w-224{ width: 224rpx; }
.w-226{ width: 226rpx; }
.w-228{ width: 228rpx; }
.w-230{ width: 230rpx; }
.w-232{ width: 232rpx; }
.w-234{ width: 234rpx; }
.w-236{ width: 236rpx; }
.w-240{ width: 240rpx; }
.w-244{ width: 244rpx; }
.w-246{ width: 246rpx; }
.w-248{ width: 248rpx; }
.w-250{ width: 250rpx; }
.w-252{ width: 252rpx; }
.w-256{ width: 256rpx; }
.w-260{ width: 260rpx; }
.w-262{ width: 262rpx; }
.w-264{ width: 264rpx; }
.w-266{ width: 266rpx; }
.w-268{ width: 268rpx; }
.w-270{ width: 270rpx; }
.w-272{ width: 272rpx; }
.w-274{ width: 274rpx; }
.w-278{ width: 278rpx; }
.w-280{ width: 280rpx; }
.w-282{ width: 282rpx; }
.w-284{ width: 284rpx; }
.w-286{ width: 286rpx; }
.w-288{ width: 288rpx; }
.w-290{ width: 290rpx; }
.w-292{ width: 292rpx; }
.w-294{ width: 294rpx; }
.w-296{ width: 296rpx; }
.w-298{ width: 298rpx; }
.w-300{ width: 300rpx; }
.w-302{ width: 302rpx; }
.w-304{ width: 304rpx; }
.w-306{ width: 306rpx; }
.w-308{ width: 308rpx; }
.w-310{ width: 310rpx; }
.w-312{ width: 312rpx; }
.w-314{ width: 314rpx; }
.w-316{ width: 316rpx; }
.w-318{ width: 318rpx; }
.w-320{ width: 320rpx; }
.w-324{ width: 324rpx; }
.w-326{ width: 326rpx; }
.w-328{ width: 328rpx; }
.w-330{ width: 330rpx; }
.w-334{ width: 334rpx; }
.w-336{ width: 336rpx; }
.w-340{ width: 340rpx; }
.w-342{ width: 342rpx; }
.w-344{ width: 344rpx; }
.w-346{ width: 346rpx; }
.w-348{ width: 348rpx; }
.w-350{ width: 350rpx; }
.w-352{ width: 352rpx; }
.w-356{ width: 356rpx; }
.w-358{ width: 358rpx; }
.w-366{ width: 366rpx; }
.w-370{ width: 370rpx; }
.w-374{ width: 374rpx; }
.w-382{ width: 382rpx; }
.w-390{ width: 390rpx; }
.w-396{ width: 396rpx; }
.w-400{ width: 400rpx; }
.w-408{ width: 408rpx; }
.w-410{ width: 410rpx; }
.w-414{ width: 414rpx; }
.w-418{ width: 418rpx; }
.w-428{ width: 428rpx; }
.w-430{ width: 430rpx; }
.w-432{ width: 432rpx; }
.w-438{ width: 438rpx; }
.w-440{ width: 440rpx; }
.w-442{ width: 442rpx; }
.w-444{ width: 444rpx; }
.w-448{ width: 448rpx; }
.w-450{ width: 450rpx; }
.w-460{ width: 460rpx; }
.w-462{ width: 462rpx; }
.w-466{ width: 466rpx; }
.w-470{ width: 470rpx; }
.w-472{ width: 472rpx; }
.w-474{ width: 474rpx; }
.w-476{ width: 476rpx; }
.w-478{ width: 478rpx; }
.w-480{ width: 480rpx; }
.w-486{ width: 486rpx; }
.w-490{ width: 490rpx; }
.w-492{ width: 492rpx; }
.w-496{ width: 496rpx; }
.w-500{ width: 500rpx; }
.w-502{ width: 502rpx; }
.w-506{ width: 506rpx; }
.w-510{ width: 510rpx; }
.w-514{ width: 514rpx; }
.w-518{ width: 518rpx; }
.w-520{ width: 520rpx; }
.w-522{ width: 522rpx; }
.w-524{ width: 524rpx; }
.w-526{ width: 526rpx; }
.w-528{ width: 528rpx; }
.w-530{ width: 530rpx; }
.w-538{ width: 538rpx; }
.w-540{ width: 540rpx; }
.w-542{ width: 542rpx; }
.w-544{ width: 544rpx; }
.w-546{ width: 546rpx; }
.w-550{ width: 550rpx; }
.w-552{ width: 552rpx; }
.w-558{ width: 558rpx; }
.w-560{ width: 560rpx; }
.w-562{ width: 562rpx; }
.w-566{ width: 566rpx; }
.w-570{ width: 570rpx; }
.w-576{ width: 576rpx; }
.w-580{ width: 580rpx; }
.w-582{ width: 582rpx; }
.w-584{ width: 584rpx; }
.w-588{ width: 588rpx; }
.w-590{ width: 590rpx; }
.w-594{ width: 594rpx; }
.w-598{ width: 598rpx; }
.w-600{ width: 600rpx; }
.w-606{ width: 606rpx; }
.w-610{ width: 610rpx; }
.w-612{ width: 612rpx; }
.w-614{ width: 614rpx; }
.w-616{ width: 616rpx; }
.w-618{ width: 618rpx; }
.w-620{ width: 620rpx; }
.w-622{ width: 622rpx; }
.w-624{ width: 624rpx; }
.w-630{ width: 630rpx; }
.w-634{ width: 634rpx; }
.w-636{ width: 636rpx; }
.w-638{ width: 638rpx; }
.w-640{ width: 640rpx; }
.w-646{ width: 646rpx; }
.w-648{ width: 648rpx; }
.w-654{ width: 654rpx; }
.w-658{ width: 658rpx; }
.w-660{ width: 660rpx; }
.w-662{ width: 662rpx; }
.w-670{ width: 670rpx; }
.w-680{ width: 680rpx; }
.w-684{ width: 684rpx; }
.w-686{ width: 686rpx; }
.w-688{ width: 688rpx; }
.w-694{ width: 694rpx; }
.w-700{ width: 700rpx; }
.w-702{ width: 702rpx; }
.w-706{ width: 706rpx; }
.w-718{ width: 718rpx; }
.w-726{ width: 726rpx; }
.w-730{ width: 730rpx; }
.w-748{ width: 748rpx; }
.w-750{ width: 750rpx; }

/* 高 */
.h-01{ height: 0.5px; }
.h-02 { height: 2rpx; }
.h-04{ height: 4rpx; }
.h-06{ height: 6rpx; }
.h-08{ height: 8rpx; }
.h-10{ height: 10rpx; }
.h-12{ height: 12rpx; }
.h-14{ height: 14rpx; }
.h-16{ height: 16rpx; }
.h-18{ height: 18rpx; }
.h-20{ height: 20rpx; }
.h-22{ height: 22rpx; }
.h-24{ height: 24rpx; }
.h-26{ height: 26rpx; }
.h-28{ height: 28rpx; }
.h-30{ height: 30rpx; }
.h-32{ height: 32rpx; }
.h-34{ height: 34rpx; }
.h-36{ height: 36rpx; }
.h-38{ height: 38rpx; }
.h-40{ height: 40rpx; }
.h-42{ height: 42rpx; }
.h-44{ height: 44rpx; }
.h-46{ height: 46rpx; }
.h-48{ height: 48rpx; }
.h-50{ height: 50rpx; }
.h-52{ height: 52rpx; }
.h-54{ height: 54rpx; }
.h-56{ height: 56rpx; }
.h-58{ height: 58rpx; }
.h-60{ height: 60rpx; }
.h-62{ height: 62rpx; }
.h-64{ height: 64rpx; }
.h-66{ height: 66rpx; }
.h-68{ height: 68rpx; }
.h-70{ height: 70rpx; }
.h-72{ height: 72rpx; }
.h-74{ height: 74rpx; }
.h-76{ height: 76rpx; }
.h-78{ height: 78rpx; }
.h-80{ height: 80rpx; }
.h-82{ height: 82rpx; }
.h-84{ height: 84rpx; }
.h-86{ height: 86rpx; }
.h-88{ height: 88rpx; }
.h-90{ height: 90rpx; }
.h-92{ height: 92rpx; }
.h-94{ height: 94rpx; }
.h-96{ height: 96rpx; }
.h-98{ height: 98rpx; }
.h-100{ height: 100rpx; }
.h-102{ height: 102rpx; }
.h-104{ height: 104rpx; }
.h-106{ height: 106rpx; }
.h-108{ height: 108rpx; }
.h-110{ height: 110rpx; }
.h-112{ height: 112rpx; }
.h-114{ height: 114rpx; }
.h-116{ height: 116rpx; }
.h-118{ height: 118rpx; }
.h-120{ height: 120rpx; }
.h-121{ height: 121rpx; }
.h-122{ height: 122rpx; }
.h-124{ height: 124rpx; }
.h-126{ height: 126rpx; }
.h-128{ height: 128rpx; }
.h-130{ height: 130rpx; }
.h-132{ height: 132rpx; }
.h-134{ height: 134rpx; }
.h-136{ height: 136rpx; }
.h-138{ height: 138rpx; }
.h-140{ height: 140rpx; }
.h-142{ height: 142rpx; }
.h-144{ height: 144rpx; }
.h-146{ height: 146rpx; }
.h-148{ height: 148rpx; }
.h-150{ height: 150rpx; }
.h-152{ height: 152rpx; }
.h-154{ height: 154rpx; }
.h-156{ height: 156rpx; }
.h-158{ height: 158rpx; }
.h-160{ height: 160rpx; }
.h-162{ height: 162rpx; }
.h-164{ height: 164rpx; }
.h-166{ height: 166rpx; }
.h-168{ height: 168rpx; }
.h-170{ height: 170rpx; }
.h-172{ height: 172rpx; }
.h-174{ height: 174rpx; }
.h-176{ height: 176rpx; }
.h-178{ height: 178rpx; }
.h-180{ height: 180rpx; }
.h-182{ height: 182rpx; }
.h-184{ height: 184rpx; }
.h-186{ height: 186rpx; }
.h-188{ height: 188rpx; }
.h-190{ height: 190rpx; }
.h-192{ height: 192rpx; }
.h-194{ height: 194rpx; }
.h-196{ height: 196rpx; }
.h-198{ height: 198rpx; }
.h-200{ height: 200rpx; }
.h-202{ height: 202rpx; }
.h-204{ height: 204rpx; }
.h-206{ height: 206rpx; }
.h-210{ height: 210rpx; }
.h-214{ height: 214rpx; }
.h-218{ height: 218rpx; }
.h-220{ height: 220rpx; }
.h-222{ height: 222rpx; }
.h-224{ height: 224rpx; }
.h-226{ height: 226rpx; }
.h-228{ height: 228rpx; }
.h-230{ height: 230rpx; }
.h-232{ height: 232rpx; }
.h-236{ height: 236rpx; }
.h-240{ height: 240rpx; }
.h-246{ height: 246rpx; }
.h-250{ height: 250rpx; }
.h-254{ height: 254rpx; }
.h-256{ height: 256rpx; }
.h-258{ height: 258rpx; }
.h-260{ height: 260rpx; }
.h-266{ height: 266rpx; }
.h-270{ height: 270rpx; }
.h-272{ height: 272rpx; }
.h-274{ height: 274rpx; }
.h-276{ height: 276rpx; }
.h-286{ height: 286rpx; }
.h-288{ height: 288rpx; }
.h-290{ height: 290rpx; }
.h-292{ height: 292rpx; }
.h-294{ height: 294rpx; }
.h-296{ height: 296rpx; }
.h-298{ height: 298rpx; }
.h-300{ height: 300rpx; }
.h-302{ height: 302rpx; }
.h-304{ height: 304rpx; }
.h-306{ height: 306rpx; }
.h-308{ height: 308rpx; }
.h-310{ height: 310rpx; }
.h-312{ height: 312rpx; }
.h-314{ height: 314rpx; }
.h-316{ height: 316rpx; }
.h-318{ height: 318rpx; }
.h-320{ height: 320rpx; }
.h-326{ height: 326rpx; }
.h-336{ height: 336rpx; }
.h-338{ height: 338rpx; }
.h-340{ height: 340rpx; }
.h-344{ height: 344rpx; }
.h-348{ height: 348rpx; }
.h-350{ height: 350rpx; }
.h-352{ height: 352rpx; }
.h-354{ height: 354rpx; }
.h-356{ height: 356rpx; }
.h-360{ height: 360rpx; }
.h-366{ height: 366rpx; }
.h-374{ height: 374rpx; }
.h-376{ height: 376rpx; }
.h-380{ height: 380rpx; }
.h-386{ height: 386rpx; }
.h-388{ height: 388rpx; }
.h-390{ height: 390rpx; }
.h-394{ height: 394rpx; }
.h-400{ height: 400rpx; }
.h-404{ height: 404rpx; }
.h-406{ height: 406rpx; }
.h-410{ height: 410rpx; }
.h-414{ height: 414rpx; }
.h-416{ height: 416rpx; }
.h-433{ height: 433rpx; }
.h-434{ height: 434rpx; }
.h-440{ height: 440rpx; }
.h-442{ height: 442rpx; }
.h-460{ height: 460rpx; }
.h-462{ height: 462rpx; }
.h-466{ height: 466rpx; }
.h-476{ height: 476rpx; }
.h-484{ height: 484rpx; }
.h-496{ height: 496rpx; }
.h-500{ height: 500rpx; }
.h-508{ height: 508rpx; }
.h-510{ height: 510rpx; }
.h-512{ height: 512rpx; }
.h-516{ height: 516rpx; }
.h-528{ height: 528rpx; }
.h-530{ height: 530rpx; }
.h-538{ height: 538rpx; }
.h-550{ height: 550rpx; }
.h-558{ height: 558rpx; }
.h-560{ height: 560rpx; }
.h-568{ height: 568rpx; }
.h-570{ height: 570rpx; }
.h-572{ height: 572rpx; }
.h-578{ height: 578rpx; }
.h-580{ height: 580rpx; }
.h-582{ height: 582rpx; }
.h-584{ height: 584rpx; }
.h-588{ height: 588rpx; }
.h-592{ height: 592rpx; }
.h-598{ height: 598rpx; }
.h-600{ height: 600rpx; }
.h-612{ height: 612rpx; }
.h-618{ height: 618rpx; }
.h-620{ height: 620rpx; }
.h-634{ height: 634rpx; }
.h-650{ height: 650rpx; }
.h-652{ height: 652rpx; }
.h-658{ height: 658rpx; }
.h-672{ height: 672rpx; }
.h-692{ height: 692rpx; }
.h-698{ height: 698rpx; }
.h-700{ height: 700rpx; }
.h-710{ height: 710rpx; }
.h-730{ height: 730rpx; }
.h-748{ height: 748rpx; }
.h-750{ height: 750rpx; }
.h-752{ height: 752rpx; }
.h-772{ height: 772rpx; }
.h-800{ height: 800rpx; }
.h-810{ height: 810rpx; }
.h-820{ height: 820rpx; }
.h-854{ height: 854rpx; }
.h-878{ height: 878rpx; }
.h-882{ height: 882rpx; }
.h-934{ height: 934rpx; }
.h-936{ height: 936rpx; }
.h-988{ height: 988rpx; }
.h-1050{ height: 1050rpx; }
.h-1080{ height: 1080rpx; }
.h-1146{ height: 1146rpx; }
.h-1214{ height: 1214rpx; }
.h-1300{ height: 1300rpx; }
.h-1462{ height: 1462rpx; }
.h-1600{ height: 1600rpx; }
.h-1830{ height: 1830rpx; }

/* 宽高 */
.wh-10 { width: 10rpx; height: 10rpx; }
.wh-14 { width: 14rpx; height: 14rpx; }
.wh-26 { width: 26rpx; height: 26rpx; }
.wh-36 { width: 36rpx; height: 36rpx; }

/* 宽高（auto） */
/* 宽auto */
.w-aut{ width: auto; }

/* 宽 max-content */
.w-max-cont { width: max-content; }

/* 宽高(vw vh */
/* 宽（vw） */

/* 高（vh） */
.h-vh-01{ height: 1vh; }
.h-vh-85{ height: 85vh; }
.h-vh-90{ height: 90vh; }
.h-vh-100{ height: 100vh; }

/* 最大宽度rpx */
.w-max-120{ max-width: 120rpx; }
.w-max-140{ max-width: 140rpx; }
.w-max-170{ max-width: 170rpx; }
.w-max-194{ max-width: 194rpx; }
.w-max-210{ max-width: 210rpx; }
.w-max-252{ max-width: 252rpx; }
.w-max-264{ max-width: 264rpx; }
.w-max-290{ max-width: 290rpx; }
.w-max-310{ max-width: 310rpx; }
.w-max-366{ max-width: 366rpx; }
.w-max-410{ max-width: 410rpx; }
.w-max-430{ max-width: 430rpx; }
.w-max-470{ max-width: 470rpx; }
.w-max-474{ max-width: 474rpx; }
.w-max-488{ max-width: 488rpx; }
.w-max-502{ max-width: 502rpx; }
.w-max-510{ max-width: 510rpx; }
.w-max-520{ max-width: 520rpx; }
.w-max-552{ max-width: 552rpx; }
.w-max-580{ max-width: 580rpx; }
.w-max-590{ max-width: 590rpx; }
.w-max-620{ max-width: 620rpx; }
.w-max-660{ max-width: 660rpx; }

/* 最大高度 */
.h-max-72{ max-height: 72rpx; }
.h-max-80{ max-height: 80rpx;}
.h-max-1000{ max-height: 1000rpx;}

/* 最小宽度rpx */
.w-min-48{ min-width: 48rpx; }
.w-min-88{ min-width: 88rpx; }
.w-min-96{ min-width: 96rpx; }
.w-min-100{ min-width: 100rpx; }
.w-min-136{ min-width: 136rpx; }
.w-min-152{ min-width: 152rpx;}
.w-min-688{ min-width: 688rpx; }
.w-min-700{ min-width: 700rpx; }

/* 最小高度rpx */
.h-min-64{min-height: 64rpx;}
.h-min-80{min-height: 80rpx;}
.h-min-110{min-height: 110rpx;}
.h-min-166{min-height: 166rpx;}
.h-min-160{min-height: 160rpx;}
.h-min-176{min-height: 176rpx;}
.h-min-400{min-height: 400rpx;}
.h-min-500{min-height: 500rpx;}
.h-min-1440{min-height: 1440rpx;}
.h-min-vh-100{min-height: 100vh;}
.h-min-p100{ min-height: 100%; }



/* 最大高度rpx */

/* 高安全区域 */
.h-safe-area {
	height: 0;
	height: constant(safe-area-inset-bottom);  
	height: env(safe-area-inset-bottom);  
}


/* 背景 */
/* 背景hex */
.bg-ffffff{ background-color: #ffffff; }
.bg-eeeeee{ background-color: #eeeeee; }
.bg-f2f2f2{ background-color: #F2F2F2; }
.bg-f5f5f5{ background-color: #F5F5F5; }
.bg-f6f6f6{ background-color: #F6F6F6; }
.bg-f7f7f7{ background-color: #F7F7F7; }
.bg-f8f8f8{ background-color: #F8F8F8; }
.bg-f9f9f9{ background-color: #f9f9f9; }
.bg-999999{ background-color: #999999; }
.bg-e2ebfa{ background-color: #E2EBFA; }
.bg-ededed{ background-color: #EDEDED; }
.bg-de1e2f{ background-color: #DE1E2F; }
.bg-e80404{ background-color: #E80404; }
.bg-ff9127{ background-color: #FF9127; }
.bg-f3ca00{ background-color: #F3CA00; }
.bg-2e7bff{ background-color: #2E7BFF; }
.bg-c31f1e{ background-color: #C31F1E; }
.bg-e72538{ background-color: #E72538; }
.bg-fff3e5{ background-color: #FFF3E5; }
.bg-f1f1f1{ background-color: #F1F1F1; }
.bg-ff8f09{ background-color: #FF8F09; }
.bg-f0efef{ background-color: #F0EFEF; }
.bg-333333{ background-color: #333333; }
.bg-feffff{ background-color: #FEFFFF; }
.bg-cda241{ background-color: #CDA241; }
.bg-ddba65{ background-color: #DDBA65; }
.bg-f67b0e{ background-color: #F67B0E; }
.bg-d8d8d8{ background-color: #D8D8D8; }
.bg-fce4e3{ background-color: #FCE4E3; }
.bg-d2d2d2{ background-color: #D2D2D2; }
.bg-734cd2{ background-color: #734CD2; }
.bg-ed2317{ background-color: #ED2317; }
.bg-ff0013{ background-color: #FF0013; }
.bg-eb0404{ background-color: #EB0404; }
.bg-fffdec{ background-color: #FFFDEC; }
.bg-dddddd{ background-color: #DDDDDD; }
.bg-279385{ background-color: #279385; }
.bg-fce0e0{ background-color: #FCE0E0; }
.bg-f7ec74{ background-color: #F7EC74; }
.bg-fde6c9{ background-color: #FDE6C9; }
.bg-fdc6c9{ background-color: #FDC9C9; }
.bg-e0e1e0{ background-color: #E0E1E0; }
.bg-dedede{ background-color: #DEDEDE; }
.bg-eaf0fb{ background-color: #EAF0FB; }
.bg-ffcdcd{ background-color: #FFCDCD; }
.bg-a9c9ff{ background-color: #A9C9FF; }
.bg-fe0000{ background-color: #FE0000; }
.bg-e0e0e0{ background-color: #E0E0E0; }
.bg-f1f6ff{ background-color: #F1F6FF; }
.bg-fff0f0{ background-color: #FFF0F0; }
.bg-ca101a{ background-color: #CA101A; }
.bg-ffa825{ background-color: #FFA825; }
.bg-fdf3ef{ background-color: #FDF3EF; }
.bg-f8a233{ background-color: #F8A233; }
.bg-d80e0e{ background-color: #D80E0E; }
.bg-e67a36{ background-color: #E67A36; }
.bg-fff1e5{ background-color: #FFF1E5; }
.bg-e78800{ background-color: #E78800; }
.bg-815dda{ background-color: #815DDA; }
.bg-f1edfb{ background-color: #F1EDFB; }
.bg-f67b0e{ background-color: #F6780E; }
.bg-feedde{ background-color: #FEEDDE; }
.bg-aaaaaa{ background-color: #AAAAAA; }
.bg-fcf8d9{ background-color: #FCF8D9; }
.bg-666666{ background-color: #666666; }
.bg-d80707{ background-color: #D80707; }
.bg-9f9f9f{ background-color: #9F9F9F; }
.bg-f9efee{ background-color: #F9EFEE; }
.bg-e5e5e5{ background-color: #E5E5E5; }
.bg-f9e7cd{ background-color: #FFE7CD; }
.bg-cccccc{ background-color: #CCCCCC; }
.bg-ececec{ background-color: #ECECEC; }
.bg-fde8c7{ background-color: #FDE8C7; }
.bg-f4f4f4{ background-color: #F4F4F4; }
.bg-fafafa{ background-color: #FAFAFA; }
.bg-ffd761{ background-color: #FFD761; }
.bg-ffb874{ background-color: #FFB874; }
.bg-e7e7e7{ background-color: #E7E7E7; }
.bg-ffc8c8{ background-color: #FFC8C8; }
.bg-e1e1e1{ background-color: #E1E1E1; }
.bg-b44b33{ background-color: #B44B33; }
.bg-ffeaea{ background-color: #FFEAEA; }
.bg-fbede1{ background-color: #FBEDE1; }
.bg-ff9300{ background-color: #FF9300; }
.bg-ffe7ec{ background-color: #FFE7CE; }
.bg-ffe5e5{ background-color: #FFE5E5; }
.bg-feefd7{ background-color: #FEEFD7; }
.bg-fdfaea{ background-color: #FDFAEA; }
.bg-f79101{ background-color: #F79101; }
.bg-e58e93{ background-color: #E58E93; }
.bg-e5e6e7{ background-color: #E5E6E7; }
.bg-f6f4ff{ background-color: #F6F4FF; }
.bg-f4f4f6{ background-color: #F4F4F6; }
.bg-fcfcfc{ background-color: #FCFCFC; }
.bg-fff0e2{ background-color: #FFF0E2; }
.bg-ffe3b7{ background-color: #FFE3B7; }
.bg-fffafa{ background-color: #FFFAFA; }
.bg-979797{ background-color: #979797; }
.bg-ffddcf{ background-color: #FFDDCF; }
.bg-fdf9e6{ background-color: #FDF9E6; }
.bg-c8101b{ background-color: #C8101B; }
.bg-db2523{ background-color: #db2523; }
.bg-f5f5f7{ background-color: #F5F5F7; }
.bg-f56f68{ background-color: #F56F68; }
.bg-fde8eb{ background-color: #FDE8EB; }
.bg-e4e4e4{ background-color: #e4e4e4; }
.bg-000000{ background-color: #000; }
.bg-fe3637{ background-color: #FE3637; }
.bg-fff2f2{ background-color: #FFF2F2; }
.bg-fff6e4{ background-color: #FFF6E4; }
.bg-ff7878{ background-color: #FF7878; }
.bg-fce4e3{ background-color: #FCE4E3; }
.bg-ffecd3{ background-color: #FFECD3; }
.bg-da9840{ background-color: #DA9840; }
.bg-da9840{ background-color: #DA9840; }
.bg-ffecd3{ background-color: #FFECD3; }
.bg-1c1c1c{ background-color: #1C1C1C; }
.bg-1e1a16{ background-color: #1E1A16 !important; }
.bg-403021{ background-color: #403021; }
.bg-be6e38{ background-color: #BE6E38; }
.bg-ffd7a9{ background-color: #FFD7A9; }
.bg-393328{ background-color: #393328; }
.bg-ff9500{ background-color: #FF9500; }
.bg-0c0001{ background-color: #0C0001; }
.bg-393228{ background-color: #393228; }
.bg-fff8f5{ background-color: #FFF8F5; }
.bg-bdbdbd{ background-color: #BDBDBD; }
.bg-f44530{ background-color: #F44530; }
.bg-8247e4{ background-color: #8247E4; }

/* 无背景样式 */
.bg-none{ background: none; }

/* 背景（透明） */
.bg-transp{ background-color: transparent; }

/* 背景rgba */
.bg-252-224-224-030{ background-color: rgba(252, 224, 224, .3); }
.bg-255-255-255-059{ background-color: rgba(255, 255, 255, .59); }
.bg-255-255-255-030{ background-color: rgba(255, 255, 255, .3); }
.bg-255-255-255-040{ background-color: rgba(255, 255, 255, .4); }
.bg-255-255-255-090{ background-color: rgba(255, 255, 255, .9); }
.bg-231-136-000-040{ background-color: rgba(231, 136, 0, .4); }
.bg-000-000-000-030{ background-color: rgba(0, 0, 0, .3); }
.bg-000-000-000-060{ background-color: rgba(0, 0, 0, .6); }
.bg-000-000-000-035{ background-color: rgba(0, 0, 0, .35); }
.bg-000-000-000-038{ background-color: rgba(0, 0, 0, .38); }
.bg-000-000-000-055{ background-color: rgba(0, 0, 0, .55); }
.bg-000-000-000-069{ background-color: rgba(0, 0, 0, .69); }
.bg-000-000-000-080{ background-color: rgba(0, 0, 0, 0.8); }

/* 背景渐变 */
.bg-li-1{ background-image: linear-gradient(214deg, #FF6161 0%, #E70000 100%); }
.bg-li-2{ background-image: linear-gradient(270deg, #FF4955 0%, #D80708 100%); }
.bg-li-3{ background-image: linear-gradient(360deg, #FFF6F6 0%, #FFEEEA 100%); }
.bg-li-4{ background-image: linear-gradient(180deg, #FF4C4C 0%, #CD2020 100%); }
.bg-li-5{ background-image: linear-gradient(180deg, #FFFFFF 0%, #FFCD90 100%); }
.bg-li-6{ background: linear-gradient(180deg, #FFE7D7 0%, #FFE1C3 100%); }
.bg-li-7{ background: linear-gradient(90deg, #FEF6F0 0%, #FFF2E5 100%); }
.bg-li-8{ background: linear-gradient(270deg, #C4ACFF 0%, #744DD3 100%); }
.bg-li-9{ background: linear-gradient(270deg, #F79F1F 0%, #F6780E 100%); }
.bg-li-10{ background: linear-gradient(180deg, #FFE8E8 0%, #FFFFFF 100%); }
.bg-li-11{ background: linear-gradient(180deg, #FFFAFA 0%, #FFF3F3 100%); }
.bg-li-12{ background: linear-gradient(214deg, #FF8383 0%, #E70000 100%); }
.bg-li-13{ background: linear-gradient(180deg, #F8D03F 0%, #FF8F00 100%); }
.bg-li-14{ background: linear-gradient(210deg, #C9C9C9 0%, #999999 100%); }
.bg-li-15{ background: linear-gradient(90deg, #FB9E20 0%, #FFC75C 100%); }
.bg-li-16{ background: linear-gradient(90deg, #A0A0A0 0%, #D6D6D6 100%); }
.bg-li-17{ background: linear-gradient(180deg, #FFFFFF 0%, #F7F7F7 100%); }
.bg-li-18{ background: linear-gradient(180deg, #FFFFFF 0%, #F8F8F8 100%); }
.bg-li-19{ background: linear-gradient(214deg, #FF9144 0%, #F35D00 100%); }
.bg-li-20{ background: linear-gradient(214deg, #5AC2FF 0%, #505CFB 100%); }
.bg-li-21{ background: linear-gradient(218deg, #FF8383 0%, #E70000 100%); }
.bg-li-22{ background: linear-gradient(1deg, rgba(255, 145, 39, .15) 0%, #FFFFFF 100%); }
.bg-li-23{ background: linear-gradient(180deg, #F4EFE1 0%, #FFFFFF 100%); }
.bg-li-24{ background: linear-gradient(180deg, #F7F2EF 0%, #FFFFFF 100%); }
.bg-li-25{ background: linear-gradient(180deg, #F8D03F 0%, #FF8F00 100%); }
.bg-li-27{ background: linear-gradient(90deg, #FCDEDE 0%, #E80404 100%); }
.bg-li-28{ background: linear-gradient(141deg, #F7FCFF 0%, #F5FAFF 100%); }
.bg-li-29{ background: linear-gradient(134deg, #FCF1F1 0%, #FCF1F1 100%); }
.bg-li-30{ background: linear-gradient(125deg, #2E7BFF 0%, #31A9FC 100%); }
.bg-li-31{ background: linear-gradient(125deg, #DE2929 0%, #DE2929 100%); }
.bg-li-32{ background: linear-gradient(214deg, #FF8133 0%, #FF9C57 100%); }
.bg-li-33{ background: linear-gradient(214deg, #E80404 0%, #F35D00 100%); }
.bg-li-34{ background: linear-gradient(180deg, #FDF9E6 0%, #FFFFFF 100%); }
.bg-li-35{ background: linear-gradient(180deg, #FDFAE9 0%, #FDFEF5 100%); }
.bg-li-36{ background: linear-gradient(180deg, #EEF4FF 0%, #FBFDFF 100%); }
.bg-li-37{ background: linear-gradient(180deg, #FFEDFD 0%, #FEFBFF 100%); }
.bg-li-38{ background: linear-gradient(115deg, rgba(255,212,116,0.39) 0%, rgba(255,145,39,0.38) 100%); }
.bg-li-39{ background: linear-gradient(90deg, #FFE3AA 0%, rgba(255,145,39,0.38) 100%); }
.bg-li-40{ background: linear-gradient(90deg, #FCD6E0 0%, #FFEEE4 100%); }
.bg-li-41{ background: linear-gradient(360deg, #FFEAED 0%, #FFFFFF 100%); }
.bg-li-42{ background: linear-gradient(157deg, #FFF1E1 0%, #FFA885 100%); }
.bg-li-43{ background: linear-gradient(90deg, #FFF1D4 0%, #FFF5E1 100%); }

/* flex布局 */
.d-flex{ display: flex; }
.d-block{ display: block; }
.d-inline-block{ display: inline-block; }
.flex-1{ flex: 1; }
.flex-column{ flex-direction: column; }
.flex-row{ flex-direction: row; }
.flex-wrap{ flex-wrap: wrap; }
.flex-nowrap{ flex-wrap: nowrap; }
.flex-shrink{flex-shrink: 0;}
.j-start{ justify-content: flex-start; }
.j-center{ justify-content: center; }
.j-end{ justify-content: flex-end; }
.j-sb{ justify-content: space-between; }
.j-sa{ justify-content: space-around; }
.a-center{ align-items:center; }
.a-start{ align-items: flex-start; }
.a-end{ align-items:flex-end; }
.a-stretch{ align-items: stretch; }
.a-baseline{ align-items: baseline; }
.a-self-start{ align-self: flex-start; }
.a-self-auto{ align-self: auto; }
.a-self-end{ align-self: flex-end; }
.a-self-stretch{ align-self:stretch; }
.a-self-baseline{ align-self:baseline; }

/* 盒模型 */
.bs-bb { box-sizing: border-box; }

/* 裁剪区域 */
.bg-cl-txt{ background-clip: text; -webkit-background-clip: text}

/* 隐藏 */
/* 超出自动 */
.o-aut { overflow: auto; }
/* xy轴超出隐藏 */
.o-hid{ overflow: hidden; }

/* x轴超出滚动 */
.o-scr-x{ overflow-x: scroll; }

/* y轴超出隐藏 */
.o-hid-y{ overflow-y: hidden; }

/* y轴超出滚动 */
.o-scr-y{ overflow-y: scroll;   -webkit-overflow-scrolling: touch; }


/* 圆角百分比 */
.b-rad-p50{ border-radius: 50%; }

/* 圆角rpx */
.b-rad-02{ border-radius: 2rpx; }
.b-rad-04{ border-radius: 4rpx; }
.b-rad-06{ border-radius: 6rpx; }
.b-rad-08{ border-radius: 8rpx; }
.b-rad-10{ border-radius: 10rpx; }
.b-rad-12{ border-radius: 12rpx; }
.b-rad-13{ border-radius: 13rpx; }
.b-rad-14{ border-radius: 14rpx; }
.b-rad-15{ border-radius: 15rpx; }
.b-rad-16{ border-radius: 16rpx; }
.b-rad-18{ border-radius: 18rpx; }
.b-rad-20{ border-radius: 20rpx; }
.b-rad-22{ border-radius: 22rpx; }
.b-rad-23{ border-radius: 23rpx; }
.b-rad-24{ border-radius: 24rpx; }
.b-rad-26{ border-radius: 26rpx; }
.b-rad-27{ border-radius: 27rpx; }
.b-rad-28{ border-radius: 28rpx; }
.b-rad-29{ border-radius: 29rpx; }
.b-rad-30{ border-radius: 30rpx; }
.b-rad-32{ border-radius: 32rpx; }
.b-rad-34{ border-radius: 34rpx; }
.b-rad-36{ border-radius: 36rpx; }
.b-rad-40{ border-radius: 40rpx; }
.b-rad-41{ border-radius: 41rpx; }
.b-rad-46{ border-radius: 46rpx; }
.b-rad-52{ border-radius: 52rpx; }
.b-rad-60{ border-radius: 60rpx; }
.b-rad-66{ border-radius: 66rpx; }
.b-rad-100{ border-radius: 100rpx; }

/* 上右圆角 */
.b-tr-rad-10{ border-radius: 0 10rpx 0 0;}
.b-tr-rad-20{ border-radius: 0 20rpx 0 0;}
.b-tr-rad-28{ border-radius: 0 28rpx 0 0;}

/* 下右圆角 */
.b-br-rad-10{ border-radius: 0 0 10rpx 0;}
.b-br-rad-28{ border-radius: 0 0 28rpx 0;}

/* 上右、下左圆角（两者圆角相同） */
.b-tr-bl-rad-20{ border-radius: 0 20rpx 0 20rpx;}

/* 上左、下右圆角（两者圆角相同） */
.b-tr-10-bl-10{ border-radius: 10rpx 0 10rpx 0;}
.b-tr-16-bl-16{ border-radius: 16rpx 0 16rpx 0;}

/* 上左、下右圆角（两者圆角不相同） */
.b-tr-20-bl-10{ border-radius: 20rpx 0 10rpx 0;}

/* 上左、上右圆角 */
.b-tl-tr-rad-06{ border-radius: 6rpx 6rpx 0 0;}
.b-tl-tr-rad-10{ border-radius: 10rpx 10rpx 0 0;}
.b-tl-tr-rad-20{ border-radius: 20rpx 20rpx 0 0;}
.b-tl-tr-rad-28{ border-radius: 28rpx 28rpx 0 0;}
.b-tl-tr-rad-80{ border-radius: 80rpx 80rpx 0 0;}

/* 下左、下右圆角 */
.b-bl-br-rad-20{ border-radius: 0 0 20rpx 20rpx;}
.b-bl-br-rad-32{ border-radius: 0 0 32rpx 32rpx;}

/* 上左、上右、下右圆角 */
.b-tl-tr-br-rad-18{ border-radius: 18rpx 18rpx 18rpx 0;}

/* 上右、下右圆角 */
.b-tr-br-rad-04{ border-radius: 0 4rpx 4rpx 0; }
.b-tr-br-rad-06{ border-radius: 0 6rpx 6rpx 0; }
.b-tr-br-rad-10{ border-radius: 0 10rpx 10rpx 0; }

/* 上左、下左圆角 */
.b-tl-bl-rad-10{ border-radius: 10rpx 0 0 10rpx; }
.b-tl-bl-rad-200{ border-radius: 200rpx 0 0 200rpx; }


/* 边框 */
/* 实心边框边框上下左右rpx */
.b-s-01-e7e7e7{ border: 0.5px solid #E7E7E7; }
.b-s-01-e2ebfa{ border: 0.5px solid #E2EBFA; }
.b-s-01-f5f4f4{ border: 0.5px solid #F5F4F4; }
.b-s-01-c31f1e{ border: 0.5px solid #C31F1E; }
.b-s-01-e80404{ border: 0.5px solid #E80404; }
.b-s-01-979797{ border: 0.5px solid #979797; }
.b-s-01-cda241{ border: 0.5px solid #CDA241; }
.b-s-01-ff6f6f{ border: 0.5px solid #FF6F6F; }
.b-s-01-f1a570{ border: 0.5px solid #F1A570; }
.b-s-01-c59053{ border: 0.5px solid #C59053; }
.b-s-01-dddddd{ border: 0.5px solid #DDDDDD; }
.b-s-01-f0f0f0{ border: 0.5px solid #F0F0F0; }
.b-s-01-2e7bff{ border: 0.5px solid #2E7BFF; }
.b-s-01-f8d6db{ border: 0.5px solid #F8D6DB; }
.b-s-01-fde8eb{ border: 0.5px solid #FDE8EB; }
.b-s-01-feb36c{ border: 0.5px solid #FEB36C; }
.b-s-01-c7c7c7{ border: 0.5px solid #C7C7C7; }
.b-s-01-e80404-r{ border: 1rpx solid #E80404; }
.b-s-01-979797-r{ border: 1rpx solid #979797; }
.b-s-01-666666{ border: 1rpx solid #666666; }
.b-s-01-d8d8d8{ border: 1rpx solid #d8d8d8; }
.b-s-01-f8d6db-r{ border: 1rpx solid #F8D6DB; }
.b-s-01-ffffff{ border: 1rpx solid #fff; }
.b-s-01-ff9127{ border: 1rpx solid #FF9127; }
.b-s-01-ff8585{ border: 1rpx solid #FF8585; }
.b-s-01-eeeeee{ border: 1rpx solid #eee; }
.b-s-02-666666{ border: 2rpx solid #666666; }
.b-s-02-999999{ border: 2rpx solid #999999; }
.b-s-02-ffffff{ border: 2rpx solid #FFFFFF; }
.b-s-02-ff9127{ border: 2rpx solid #FF9127; }
.b-s-02-ececec{ border: 2rpx solid #ECECEC; }
.b-s-02-e80404{ border: 2rpx solid #E80404; }
.b-s-02-f6f6f6{ border: 2rpx solid #F6F6F6; }
.b-s-02-d8d8d8{ border: 2rpx solid #d8d8d8; }
.b-s-02-efc1cd{ border: 2rpx solid #EFC1CD; }
.b-s-02-979797{ border: 2rpx solid #979797; }
.b-s-02-f0f0f0{	border: 2rpx solid #F0F0F0; }
.b-s-02-f8d6db{	border: 2rpx solid #F8D6DB; }
.b-s-02-fce4e3{ border: 2rpx solid #FCE4E3; }
.b-s-02-ffe7cf{ border: 2rpx solid #FFE7CF; }
.b-s-02-f5f5f5{ border: 2rpx solid #F5F5F5; }
.b-s-02-f44530{ border: 2rpx solid #F44530; }
.b-s-04-ffffff{ border: 4rpx solid #FFFFFF; }


/* 实心上边框rpx */
.bt-s-01-eeeeee{border-top: 0.5px solid #EEEEEE;}
.bt-s-01-dedede{border-top: 0.5px solid #DEDEDE;}
.bt-s-01-f8f8f8{border-top: 0.5px solid #F8F8F8;}
.bt-s-01-dedede-r{border-top: 1rpx solid #DEDEDE;}
.bt-s-02-f8f8f8{border-top: 2rpx solid #F8F8F8;}
.bt-s-02-eeeeee{border-top: 2rpx solid #EEEEEE;}
.bt-s-02-transp{ border-bottom: 2rpx solid transparent; }


/* 实心右边框 */
.br-s-01-dddddd{ border-right: 0.5px solid #DDDDDD; }
.br-s-20-ffffff{ border-right: 20rpx solid #ffffff; }
.br-s-30-ffffff{ border-right: 30rpx solid #ffffff; }

/* 实心下边框rpx */
.bb-s-01-eeeeee{ border-bottom: 0.5px solid #EEEEEE; }
.bb-s-01-dedede{ border-bottom: 0.5px solid #DEDEDE; }
.bb-s-01-f8f8f8{ border-bottom: 0.5px solid #F8F8F8; }
.bb-s-01-f7f7f7{ border-bottom: 0.5px solid #F7F7F7; }
.bb-s-01-f5f5f5{ border-bottom: 0.5px solid #F5F5F5; }
.bb-s-01-f0f0f0{ border-bottom: 0.5px solid #F0F0F0; }
.bb-s-01-d8d8d8{ border-bottom: 0.5px solid #d8d8d8; }
.bb-s-01-efefeef{ border-bottom: 1rpx solid #EFEFEF; }
.bb-s-06-f6f6f6{ border-bottom: 6rpx solid #F6F6F6; }
.bb-s-02-eeeeee{ border-bottom: 2rpx solid #EEEEEE; }
.bb-s-20-transp{ border-bottom: 20rpx solid transparent; }
.bb-s-30-transp{ border-bottom: 30rpx solid transparent; }
.bb-s-02-ececec{ border-bottom: 2rpx solid #ececec; }
.bb-s-02-d8d8d8{ border-bottom: 2rpx solid #d8d8d8; }

/* 实心左边框rpx */
.bl-s-01-cccccc{ border-left: 0.5px solid #CCCCCC; }
.bl-s-01-eeeeee{ border-left: 0.5px solid #EEEEEE;}
.bl-s-02-e7e7e7{ border-left: 2rpx solid #E7E7E7;}
.bl-s-01-eeeeee{ border-left: 0.5px solid #EEEEEE;}
.bl-s-02-eeeeee{ border-left: 2rpx solid #EEEEEE;}
.bl-s-30-transp{ border-left: 30rpx solid transparent;}

/* 虚线边框上右下左rpx */
.b-d-01-999999{ border: 0.5px dashed #999999; }
.b-d-01-c6c5c5{ border: 0.5px dashed #C6C5C5; }
.b-d-01-2e7bff{ border: 0.5px dashed #2E7BFF; }
.b-d-01-e80404{ border: 0.5px dashed #E80404; }
.b-d-02-dbdbdb{ border: 2rpx dashed #DBDBDB; }
.b-d-02-d8d8d8{ border: 2rpx dashed #D8D8D8; }

.bt-d-02-d8d8d8 { border-top: 2rpx dashed #d8d8d8; }

/* 虚线右边框rpx */
.br-d-01-e6853f{ border-right: 0.5px dashed #E6853F;}
.br-d-01-ffe5e8{ border-right: 0.5px dashed #FFE5E8;}
.br-d-01-e7e7e7{ border-right: 0.5px dashed #E7E7E7;}

/* 虚线下边框 */
.bb-d-01-eeeeee{ border-bottom: 0.5px dashed #EEEEEE; }
.bb-d-02-ff7878{ border-bottom: 2rpx dashed #FF7878; }

/* 点下边框 */
.bb-dot-02-ff7878{ border-bottom: 2rpx dotted #FF7878; }

/* 阴影 */
.b-sh-00021200-022{ box-shadow: 0 2rpx 12rpx 0 rgba(0,0,0,0.22); }
.b-sh-00001002-007{ box-shadow: 0 0 10rpx 2rpx rgba(0,0,0,0.07); }
.b-sh-00041600-010{ box-shadow: 0 4rpx 16rpx 0 rgba(0,0,0,0.1); }
.b-sh-00041200-013{ box-shadow: 0 4rpx 12rpx 0 rgba(0,0,0,0.13); }
.b-sh-00042604-007{ box-shadow: 0 4rpx 26rpx 4rpx rgba(0,0,0,0.07); }
.b-sh-00061000-007{ box-shadow: 0 6rpx 10rpx 0 rgba(0, 0, 0, 0.07); }
.b-sh-02021200-015{ box-shadow: 2rpx 2rpx 6rpx 0 rgba(0, 0, 0, 0.15); }
.b-sh-00001002-009{ box-shadow: 0 0 10rpx 2rpx rgba(0,0,0,0.09); }
.b-sh-00041400-012{ box-shadow: 0 4rpx 14rpx 0 rgba(0,0,0,0.12); }
.b-sh-00042800-012{ box-shadow: 0 4rpx 28rpx 0 rgba(0, 0, 0, 0.15); }
.b-sh-00042800-026{ box-shadow: 0 4rpx 28rpx 0 rgba(0,0,0,0.26); }
.b-sh-00101600-001{ box-shadow: 0 10rpx 16rpx 0 rgba(0,0,0,0.1); }
.b-sh-00042800-015{ box-shadow: 0rpx 4rpx 28rpx 0rpx rgba(0,0,0,0.15); }
.b-sh-00041800-009{ box-shadow: 0rpx 4rpx 18rpx 0rpx rgba(0,0,0,0.09); }


/* 旋转 */
/* 旋转逆时针 */
.t-ro-n-45 { transform: rotate(-45deg); }
.t-ro-n-180 { transform: rotate(-180deg); }

/* 旋转顺时针 */
.t-ro-45 { transform: rotate(45deg); }
.t-ro-90 { transform: rotate(90deg); }
.t-ro-180 { transform: rotate(180deg); }

/* 旋转x */
.t-ro-x-180{ transform: rotateX(180deg); }

/* 旋转y */
.t-ro-y-30{ transform: rotateY(30deg); }
.t-ro-y-180{ transform: rotateY(180deg); }

/* 缩放 */
/* 缩放x */
.t-sc-x-h-1{ transform: scaleX(.5); }

/* 缩放y */
.t-sc-y-h-1{ transform: scaleY(.5); }

/* 3d旋转 */
.t-trans-3d-1 { transform: translate3d(0, 0, 0); }

/* 倾斜 */
.t-sk-x-n-15{ transform: skewX(-15deg); }
.t-sk-x-n-10{ transform: skewX(-10deg); }
.t-sk-x-10{ transform: skewX(10deg); }
.t-sk-x-15{ transform: skewX(15deg); }

.t-trans-x-72 { transform: translateX(72rpx); }
.t-trans-x-m50 { transform: translateX(-50%); }
.t-trans-x-m100 { transform: translateX(-100%); }

/* 透明度 */
.op-000{ opacity: 0; }
.op-007{ opacity: .07; }
.op-015{ opacity: .15; }
.op-040{ opacity: .4; }
.op-050{ opacity: .5; }
.op-060{ opacity: .6; }
.op-100{ opacity: 1; }

/* 外边距 */
/* 上(百分比) */
.mt-p20{ margin-top: 20%; }
.mt-p30{ margin-top: 30%; }
.mt-p40{ margin-top: 40%; }
.mt-p50{ margin-top: 50%; }

/* 上下左右（都相同） */
.m-0{ margin: 0; }
.m-24{ margin: 24rpx; }

/* 上右下左（上下、左右相同） */
.mtb-00-mlr-12{ margin: 0 12rpx; }
.mtb-00-mlr-04{ margin: 0 4rpx; }
.mtb-00-mlr-20{ margin: 0 20rpx; }
.mtb-00-mlr-24{ margin: 0 24rpx; }
.mtb-00-mlr-28{ margin: 0 28rpx; }
.mtb-00-mlr-32{ margin: 0 32rpx; }
.mtb-00-mlr-44{ margin: 0 44rpx; }
.mtb-00-mlr-auto{ margin: 0 auto; }
.mtb-02-mlr-00{ margin: 2rpx 0; }
.mtb-16-mlr-12{ margin: 16rpx 12rpx; }
.mtb-20-mlr-00{ margin: 20rpx 0; }
.mtb-20-mlr-24{ margin: 20rpx 24rpx; }
.mtb-24-mlr-auto{ margin: 24rpx auto; }

/* 上(rpx) */
.mt-n-290{ margin-top: -290rpx; }
.mt-n-88{ margin-top: -88rpx; }
.mt-n-86{ margin-top: -86rpx; }
.mt-n-80{ margin-top: -80rpx; }
.mt-n-72{ margin-top: -72rpx; }
.mt-n-70{ margin-top: -70rpx; }
.mt-n-54{ margin-top: -54rpx; }
.mt-n-48{ margin-top: -48rpx; }
.mt-n-40{ margin-top: -40rpx; }
.mt-n-30{ margin-top: -30rpx; }
.mt-n-28{ margin-top: -28rpx; }
.mt-n-20{ margin-top: -20rpx; }
.mt-n-16{ margin-top: -16rpx; }
.mt-n-12{ margin-top: -12rpx; }
.mt-n-10{ margin-top: -10rpx; }
.mt-n-08{ margin-top: -8rpx; }
.mt-n-02{ margin-top: -2rpx; }
.mt-02{ margin-top: 2rpx; }
.mt-04{ margin-top: 4rpx; }
.mt-06{ margin-top: 6rpx; }
.mt-08{ margin-top: 8rpx; }
.mt-10{ margin-top: 10rpx; }
.mt-12{ margin-top: 12rpx; }
.mt-14{ margin-top: 14rpx; }
.mt-16{ margin-top: 16rpx; }
.mt-18{ margin-top: 18rpx; }
.mt-20{ margin-top: 20rpx; }
.mt-22{ margin-top: 22rpx; }
.mt-24{ margin-top: 24rpx; }
.mt-26{ margin-top: 26rpx; }
.mt-28{ margin-top: 28rpx; }
.mt-30{ margin-top: 30rpx; }
.mt-32{ margin-top: 32rpx; }
.mt-34{ margin-top: 34rpx; }
.mt-36{ margin-top: 36rpx; }
.mt-38{ margin-top: 38rpx; }
.mt-40{ margin-top: 40rpx; }
.mt-42{ margin-top: 42rpx; }
.mt-44{ margin-top: 44rpx; }
.mt-46{ margin-top: 46rpx; }
.mt-48{ margin-top: 48rpx; }
.mt-50{ margin-top: 50rpx; }
.mt-52{ margin-top: 52rpx; }
.mt-56{ margin-top: 56rpx; }
.mt-58{ margin-top: 58rpx; }
.mt-60{ margin-top: 60rpx; }
.mt-62{ margin-top: 62rpx; }
.mt-68{ margin-top: 68rpx; }
.mt-64{ margin-top: 64rpx; }
.mt-70{ margin-top: 70rpx; }
.mt-72{ margin-top: 72rpx; }
.mt-74{ margin-top: 74rpx; }
.mt-76{ margin-top: 76rpx; }
.mt-78{ margin-top: 78rpx; }
.mt-80{ margin-top: 80rpx; }
.mt-84{ margin-top: 84rpx; }
.mt-86{ margin-top: 86rpx; }
.mt-88{ margin-top: 88rpx; }
.mt-90{ margin-top: 90rpx; }
.mt-92{ margin-top: 92rpx; }
.mt-94{ margin-top: 94rpx; }
.mt-96{ margin-top: 96rpx; }
.mt-100{ margin-top: 100rpx; }
.mt-102{ margin-top: 102rpx; }
.mt-106{ margin-top: 106rpx; }
.mt-108{ margin-top: 108rpx; }
.mt-110{ margin-top: 110rpx; }
.mt-118{ margin-top: 118rpx; }
.mt-120{ margin-top: 120rpx; }
.mt-124{ margin-top: 124rpx; }
.mt-130{ margin-top: 130rpx; }
.mt-132{ margin-top: 132rpx; }
.mt-136{ margin-top: 136rpx; }
.mt-146{ margin-top: 146rpx; }
.mt-152{ margin-top: 152rpx; }
.mt-158{ margin-top: 158rpx; }
.mt-160{ margin-top: 160rpx; }
.mt-176{ margin-top: 176rpx; }
.mt-188{ margin-top: 188rpx; }
.mt-196{ margin-top: 196rpx; }
.mt-230{ margin-top: 230rpx; }
.mt-260{ margin-top: 260rpx; }
.mt-304{ margin-top: 304rpx; }
.mt-348{ margin-top: 348rpx; }
.mt-368{ margin-top: 368rpx; }

/* 右 */
.mr-n-20{ margin-right: -20rpx; }
.mr-0{ margin-right: 0; }
.mr-02{ margin-right: 2rpx; }
.mr-04{ margin-right: 4rpx; }
.mr-06{ margin-right: 6rpx; }
.mr-08{ margin-right: 8rpx; }
.mr-10{ margin-right: 10rpx; }
.mr-12{ margin-right: 12rpx; }
.mr-14{ margin-right: 14rpx; }
.mr-16{ margin-right: 16rpx; }
.mr-18{ margin-right: 18rpx; }
.mr-20{ margin-right: 20rpx; }
.mr-22{ margin-right: 22rpx; }
.mr-24{ margin-right: 24rpx; }
.mr-26{ margin-right: 26rpx; }
.mr-28{ margin-right: 28rpx; }
.mr-30{ margin-right: 30rpx; }
.mr-32{ margin-right: 32rpx; }
.mr-34{ margin-right: 34rpx; }
.mr-36{ margin-right: 36rpx; }
.mr-38{ margin-right: 38rpx; }
.mr-40{ margin-right: 40rpx; }
.mr-42{ margin-right: 42rpx; }
.mr-46{ margin-right: 46rpx; }
.mr-48{ margin-right: 48rpx; }
.mr-50{ margin-right: 50rpx; }
.mr-52{ margin-right: 52rpx; }
.mr-54{ margin-right: 54rpx; }
.mr-56{ margin-right: 56rpx; }
.mr-60{ margin-right: 60rpx; }
.mr-62{ margin-right: 62rpx; }
.mr-72{ margin-right: 72rpx; }
.mr-75{ margin-right: 75rpx; }
.mr-84{ margin-right: 84rpx; }
.mr-90{ margin-right: 90rpx; }
.mr-94{ margin-right: 94rpx; }
.mr-100{ margin-right: 100rpx; }
.mr-104{ margin-right: 104rpx; }

/* 下 */
.mb-n-16{ margin-bottom: -16rpx; }
.mb-02{ margin-bottom: 2rpx; }
.mb-04{ margin-bottom: 4rpx; }
.mb-06{ margin-bottom: 6rpx; }
.mb-08{ margin-bottom: 8rpx; }
.mb-10{ margin-bottom: 10rpx; }
.mb-12{ margin-bottom: 12rpx; }
.mb-14{ margin-bottom: 14rpx; }
.mb-16{ margin-bottom: 16rpx; }
.mb-18{ margin-bottom: 18rpx; }
.mb-20{ margin-bottom: 20rpx; }
.mb-22{ margin-bottom: 22rpx; }
.mb-24{ margin-bottom: 24rpx; }
.mb-26{ margin-bottom: 26rpx; }
.mb-28{ margin-bottom: 28rpx; }
.mb-30{ margin-bottom: 30rpx; }
.mb-32{ margin-bottom: 32rpx; }
.mb-34{ margin-bottom: 34rpx; }
.mb-36{ margin-bottom: 36rpx; }
.mb-40{ margin-bottom: 40rpx; }
.mb-46{ margin-bottom: 46rpx; }
.mb-50{ margin-bottom: 50rpx; }
.mb-52{ margin-bottom: 52rpx; }
.mb-62{ margin-bottom: 62rpx; }
.mb-66{ margin-bottom: 66rpx; }
.mb-74{ margin-bottom: 74rpx; }
.mb-76{ margin-bottom: 76rpx; }
.mb-90{ margin-bottom: 90rpx; }
.mb-96{ margin-bottom: 96rpx; }
.mb-100{ margin-bottom: 100rpx; }
.mb-108{ margin-bottom: 108rpx; }
.mb-120{ margin-bottom: 120rpx; }
.mb-124{ margin-bottom: 124rpx; }
.mb-175{ margin-bottom: 175rpx; }

/* 左 */
.ml-n-28{ margin-left: -28rpx;}
.ml-n-24{ margin-left: -24rpx;}
.ml-n-20{ margin-left: -20rpx;}
.ml-n-15{ margin-left: -15rpx;}
.ml-n-14{ margin-left: -14rpx;}
.ml-n-10{ margin-left: -10rpx;}
.ml-n-06{ margin-left: -6rpx;}
.ml-n-02{ margin-left: -2rpx;}
.ml-02{ margin-left: 2rpx;}
.ml-04{ margin-left: 4rpx;}
.ml-06{ margin-left: 6rpx;}
.ml-08{ margin-left: 8rpx;}
.ml-10{ margin-left: 10rpx; }
.ml-12{ margin-left: 12rpx; }
.ml-14{ margin-left: 14rpx; }
.ml-15{ margin-left: 15rpx; }
.ml-16{ margin-left: 16rpx; }
.ml-18{ margin-left: 18rpx; }
.ml-20{ margin-left: 20rpx; }
.ml-22{ margin-left: 22rpx; }
.ml-24{ margin-left: 24rpx; }
.ml-26{ margin-left: 26rpx; }
.ml-28{ margin-left: 28rpx; }
.ml-30{ margin-left: 30rpx; }
.ml-32{ margin-left: 32rpx; }
.ml-34{ margin-left: 34rpx; }
.ml-36{ margin-left: 36rpx; }
.ml-38{ margin-left: 38rpx; }
.ml-40{ margin-left: 40rpx; }
.ml-44{ margin-left: 44rpx; }
.ml-46{ margin-left: 46rpx; }
.ml-48{ margin-left: 48rpx; }
.ml-50{ margin-left: 50rpx; }
.ml-52{ margin-left: 52rpx; }
.ml-54{ margin-left: 54rpx; }
.ml-56{ margin-left: 56rpx; }
.ml-58{ margin-left: 58rpx; }
.ml-60{ margin-left: 60rpx; }
.ml-62{ margin-left: 62rpx; }
.ml-64{ margin-left: 64rpx; }
.ml-66{ margin-left: 66rpx; }
.ml-70{ margin-left: 70rpx; }
.ml-72{ margin-left: 72rpx; }
.ml-74{ margin-left: 74rpx; }
.ml-78{ margin-left: 78rpx; }
.ml-80{ margin-left: 80rpx; }
.ml-82{ margin-left: 82rpx; }
.ml-86{ margin-left: 86rpx; }
.ml-90{ margin-left: 90rpx; }
.ml-94{ margin-left: 94rpx; }
.ml-100{ margin-left: 100rpx; }
.ml-108{ margin-left: 108rpx; }
.ml-124{ margin-left: 124rpx; }
.ml-134{ margin-left: 134rpx; }
.ml-190{ margin-left: 190rpx; }
.ml-200{ margin-left: 200rpx; }

/* 列表第一个元素上外边距 */
.mt-nth-child1-00 > view:nth-child(1) { margin-top: 0; }
.mt-nth-child1-24 > view:nth-child(1) { margin-top: 24rpx; }

/* 列表第一个元素左外边距 */
.ml-nth-child1-00 > view:nth-child(1) { margin-left: 0; }
.ml-nth-child1-24 > view:nth-child(1) { margin-left: 24rpx; }
.ml-nth-child1-40 > view:nth-child(1) { margin-left: 40rpx; }

/* 列表最后一个view组件右外边距 */
.mr-last-nth-child1-00 > view:nth-last-child(1) { margin-right: 0; }
.mr-last-nth-child1-24 > view:nth-last-child(1) { margin-right: 24rpx; }
.mr-last-nth-child1-40 > view:nth-last-child(1) { margin-right: 40rpx; }

/* 内边距 */
/* 苹果安全适配区域 */
.p-b-safe-area {
	padding-bottom: 0;  
	padding-bottom: constant(safe-area-inset-bottom);  
	padding-bottom: env(safe-area-inset-bottom);  
} 

/* 上右下左 */
.p-0{ padding: 0; }
.p-02{ padding: 2rpx; }
.p-04{ padding: 4rpx; }
.p-06{ padding: 6rpx; }
.p-08{ padding: 8rpx; }
.p-10{ padding: 10rpx; }
.p-12{ padding: 12rpx; }
.p-14{ padding: 14rpx; }
.p-16{ padding: 16rpx; }
.p-20{ padding: 20rpx; }
.p-24{ padding: 24rpx; }
.p-26{ padding: 26rpx; }
.p-32{ padding: 32rpx; }
.p-40{ padding: 40rpx; }
.p-60{ padding: 60rpx; }
.p-84{ padding: 84rpx; }

/* 上右下左（上下、左右相同） */
.ptb-00-plr-02{ padding: 0 2rpx; }
.ptb-00-plr-04{ padding: 0 4rpx; }
.ptb-00-plr-06{ padding: 0 6rpx; }
.ptb-00-plr-08{ padding: 0 8rpx; }
.ptb-00-plr-10{ padding: 0 10rpx; }
.ptb-00-plr-12{ padding: 0 12rpx; }
.ptb-00-plr-14{ padding: 0 14rpx; }
.ptb-00-plr-16{ padding: 0 16rpx; }
.ptb-00-plr-20{ padding: 0 20rpx; }
.ptb-00-plr-24{ padding: 0 24rpx; }
.ptb-00-plr-26{ padding: 0 26rpx; }
.ptb-00-plr-28{ padding: 0 28rpx; }
.ptb-00-plr-30{ padding: 0 30rpx; }
.ptb-00-plr-32{ padding: 0 32rpx; }
.ptb-00-plr-34{ padding: 0 34rpx; }
.ptb-00-plr-36{ padding: 0 36rpx; }
.ptb-00-plr-38{ padding: 0 38rpx; }
.ptb-00-plr-40{ padding: 0 40rpx; }
.ptb-00-plr-42{ padding: 0 42rpx; }
.ptb-00-plr-44{ padding: 0 44rpx; }
.ptb-00-plr-46{ padding: 0 46rpx; }
.ptb-00-plr-48{ padding: 0 48rpx; }
.ptb-00-plr-50{ padding: 0 50rpx; }
.ptb-00-plr-52{ padding: 0 52rpx; }
.ptb-00-plr-54{ padding: 0 54rpx; }
.ptb-00-plr-56{ padding: 0 56rpx; }
.ptb-00-plr-60{ padding: 0 60rpx; }
.ptb-00-plr-62{ padding: 0 62rpx; }
.ptb-00-plr-70{ padding: 0 70rpx; }
.ptb-00-plr-72{ padding: 0 72rpx; }
.ptb-00-plr-74{ padding: 0 74rpx; }
.ptb-00-plr-76{ padding: 0 76rpx; }
.ptb-00-plr-84{ padding: 0 84rpx; }
.ptb-00-plr-92{ padding: 0 92rpx; }
.ptb-00-plr-98{ padding: 0 98rpx; }
.ptb-01-plr-12{ padding: 1rpx 12rpx; }
.ptb-01-plr-14{ padding: 1rpx 14rpx; }
.ptb-02-plr-04{ padding: 2rpx 4rpx; }
.ptb-02-plr-06{ padding: 2rpx 6rpx; }
.ptb-02-plr-08{ padding: 2rpx 8rpx; }
.ptb-02-plr-10{ padding: 2rpx 10rpx; }
.ptb-02-plr-12{ padding: 2rpx 12rpx; }
.ptb-02-plr-16{ padding: 2rpx 16rpx; }
.ptb-02-plr-20{ padding: 2rpx 20rpx; }
.ptb-02-plr-22{ padding: 2rpx 22rpx; }
.ptb-02-plr-24{ padding: 2rpx 24rpx; }
.ptb-04-plr-00{ padding: 4rpx 0; }
.ptb-04-plr-08{ padding: 4rpx 8rpx; }
.ptb-04-plr-12{ padding: 4rpx 12rpx; }
.ptb-04-plr-16{ padding: 4rpx 16rpx; }
.ptb-04-plr-18{ padding: 4rpx 18rpx; }
.ptb-04-plr-24{ padding: 4rpx 24rpx; }
.ptb-04-plr-32{ padding: 4rpx 32rpx; }
.ptb-04-plr-40{ padding: 4rpx 40rpx; }
.ptb-04-plr-42{ padding: 4rpx 42rpx; }
.ptb-06-plr-00{ padding: 6rpx 0; }
.ptb-06-plr-12{ padding: 6rpx 12rpx; }
.ptb-06-plr-16{ padding: 6rpx 16rpx; }
.ptb-06-plr-20{ padding: 6rpx 20rpx; }
.ptb-06-plr-22{ padding: 6rpx 22rpx; }
.ptb-06-plr-24{ padding: 6rpx 24rpx; }
.ptb-06-plr-34{ padding: 6rpx 34rpx; }
.ptb-06-plr-44{ padding: 6rpx 44rpx; }
.ptb-08-plr-00{ padding: 8rpx 0; }
.ptb-08-plr-20{ padding: 8rpx 20rpx; }
.ptb-10-plr-14{ padding: 10rpx 14rpx; }
.ptb-10-plr-20{ padding: 10rpx 20rpx; }
.ptb-10-plr-24{ padding: 10rpx 24rpx; }
.ptb-12-plr-24{ padding: 12rpx 24rpx; }
.ptb-14-plr-00{ padding: 14rpx 0; }
.ptb-14-plr-20{ padding: 14rpx 20rpx; }
.ptb-14-plr-24{ padding: 14rpx 24rpx; }
.ptb-16-plr-00{ padding: 16rpx 0; }
.ptb-16-plr-12{ padding: 16rpx 12rpx; }
.ptb-16-plr-24{ padding: 16rpx 24rpx; }
.ptb-16-plr-32{ padding: 16rpx 32rpx; }
.ptb-16-plr-40{ padding: 16rpx 40rpx; }
.ptb-18-plr-16{ padding: 18rpx 16rpx; }
.ptb-18-plr-24{ padding: 18rpx 24rpx; }
.ptb-20-plr-00{ padding: 20rpx 0; }
.ptb-20-plr-16{ padding: 20rpx 16rpx; }
.ptb-20-plr-24{ padding: 20rpx 24rpx; }
.ptb-20-plr-44{ padding: 20rpx 44rpx; }
.ptb-24-plr-00{ padding: 24rpx 0; }
.ptb-24-plr-16{ padding: 24rpx 16rpx; }
.ptb-24-plr-20{ padding: 24rpx 20rpx; }
.ptb-24-plr-32{ padding: 24rpx 32rpx; }
.ptb-24-plr-56{ padding: 24rpx 56rpx; }
.ptb-28-plr-00{ padding: 28rpx 0; }
.ptb-28-plr-20{ padding: 28rpx 20rpx; }
.ptb-28-plr-24{ padding: 28rpx 24rpx; }
.ptb-28-plr-32{ padding: 28rpx 32rpx; }
.ptb-30-plr-00{ padding: 30rpx 0; }
.ptb-30-plr-20{ padding: 30rpx 20rpx; }
.ptb-32-plr-00{ padding: 32rpx 0; }
.ptb-32-plr-20{ padding: 32rpx 20rpx; }
.ptb-30-plr-24{ padding: 30rpx 24rpx; }
.ptb-30-plr-32{ padding: 30rpx 32rpx; }
.ptb-32-plr-24{ padding: 32rpx 24rpx; }
.ptb-32-plr-30{ padding: 32rpx 30rpx; }
.ptb-32-plr-44{ padding: 32rpx 44rpx; }
.ptb-36-plr-00{ padding: 36rpx 0; }
.ptb-40-plr-00{ padding: 40rpx 0; }
.ptb-40-plr-24{ padding: 40rpx 24rpx; }
.ptb-40-plr-32{ padding: 40rpx 32rpx; }
.ptb-40-plr-60{ padding: 40rpx 60rpx; }
.ptb-44-plr-00{ padding: 44rpx 0; }
.ptb-48-plr-00{ padding: 48rpx 0; }
.ptb-48-plr-58{ padding: 48rpx 58rpx; }
.ptb-52-plr-00{ padding: 52rpx 0; }
.ptb-56-plr-32{ padding: 56rpx 32rpx; }
.ptb-60-plr-00{ padding: 60rpx 0; }
.ptb-66-plr-00{ padding: 66rpx 0; }

/* 上 */
.pt-00{ padding-top: 0; }
.pt-02{ padding-top: 2rpx; }
.pt-04{ padding-top: 4rpx; }
.pt-06{ padding-top: 6rpx; }
.pt-08{ padding-top: 8rpx; }
.pt-10{ padding-top: 10rpx; }
.pt-12{ padding-top: 12rpx; }
.pt-14{ padding-top: 14rpx; }
.pt-16{ padding-top: 16rpx; }
.pt-18{ padding-top: 18rpx; }
.pt-20{ padding-top: 20rpx; }
.pt-22{ padding-top: 22rpx; }
.pt-24{ padding-top: 24rpx; }
.pt-26{ padding-top: 26rpx; }
.pt-28{ padding-top: 28rpx; }
.pt-30{ padding-top: 30rpx; }
.pt-32{ padding-top: 32rpx; }
.pt-34{ padding-top: 34rpx; }
.pt-36{ padding-top: 36rpx; }
.pt-38{ padding-top: 38rpx; }
.pt-40{ padding-top: 40rpx; }
.pt-42{ padding-top: 42rpx; }
.pt-44{ padding-top: 44rpx; }
.pt-46{ padding-top: 46rpx; }
.pt-48{ padding-top: 48rpx; }
.pt-50{ padding-top: 50rpx; }
.pt-52{ padding-top: 52rpx; }
.pt-60{ padding-top: 60rpx; }
.pt-64{ padding-top: 64rpx; }
.pt-70{ padding-top: 70rpx; }
.pt-80{ padding-top: 80rpx; }
.pt-84{ padding-top: 84rpx; }
.pt-86{ padding-top: 86rpx; }
.pt-88{ padding-top: 88rpx; }
.pt-92{ padding-top: 92rpx; }
.pt-98{ padding-top: 98rpx; }
.pt-100{ padding-top: 100rpx; }
.pt-108{ padding-top: 108rpx; }
.pt-120{ padding-top: 120rpx; }
.pt-122{ padding-top: 122rpx; }
.pt-130{ padding-top: 130rpx; }
.pt-140{ padding-top: 140rpx; }
.pt-154{ padding-top: 154rpx; }
.pt-160{ padding-top: 160rpx; }
.pt-170{ padding-top: 170rpx; }
.pt-198{ padding-top: 198rpx; }
.pt-200{ padding-top: 200rpx; }
.pt-238{ padding-top: 238rpx; }
.pt-248{ padding-top: 248rpx; }
.pt-270{ padding-top: 270rpx; }
.pt-278{ padding-top: 278rpx; }
.pt-230{ padding-top: 230rpx; }
.pt-332{ padding-top: 332rpx; }
.pt-468{ padding-top: 468rpx; }
.pt-530{ padding-top: 530rpx; }

/* 右 */
.pr-0{ padding-right: 0; }
.pr-04{ padding-right: 4rpx; } 
.pr-06{ padding-right: 6rpx; }
.pr-08{ padding-right: 8rpx; }
.pr-10{ padding-right: 10rpx; }
.pr-12{ padding-right: 12rpx; }
.pr-14{ padding-right: 14rpx; }
.pr-16{ padding-right: 16rpx; }
.pr-18{ padding-right: 18rpx; }
.pr-20{ padding-right: 20rpx; }
.pr-22{ padding-right: 22rpx; }
.pr-24{ padding-right: 24rpx; }
.pr-26{ padding-right: 26rpx; }
.pr-28{ padding-right: 28rpx; }
.pr-30{ padding-right: 30rpx; }
.pr-32{ padding-right: 32rpx; }
.pr-34{ padding-right: 34rpx; }
.pr-36{ padding-right: 36rpx; }
.pr-40{ padding-right: 40rpx; }
.pr-44{ padding-right: 44rpx; }
.pr-46{ padding-right: 46rpx; }
.pr-48{ padding-right: 48rpx; }
.pr-52{ padding-right: 52rpx; }
.pr-68{ padding-right: 68rpx; }
.pr-72{ padding-right: 72rpx; }
.pr-76{ padding-right: 76rpx; }
.pr-80{ padding-right: 80rpx; }
.pr-84{ padding-right: 84rpx; }

/* 下 */
.pb-0{ padding-bottom: 0; }
.pb-04{ padding-bottom: 4rpx; }
.pb-06{ padding-bottom: 6rpx; }
.pb-08{ padding-bottom: 8rpx; }
.pb-10{ padding-bottom: 10rpx; }
.pb-12{ padding-bottom: 12rpx; }
.pb-14{ padding-bottom: 14rpx; }
.pb-16{ padding-bottom: 16rpx; }
.pb-18{ padding-bottom: 18rpx; }
.pb-20{ padding-bottom: 20rpx; }
.pb-22{ padding-bottom: 22rpx; }
.pb-24{ padding-bottom: 24rpx; }
.pb-26{ padding-bottom: 26rpx; }
.pb-28{ padding-bottom: 28rpx; }
.pb-30{ padding-bottom: 30rpx; }
.pb-32{ padding-bottom: 32rpx; }
.pb-34{ padding-bottom: 34rpx; }
.pb-36{ padding-bottom: 36rpx; }
.pb-38{ padding-bottom: 38rpx; }
.pb-40{ padding-bottom: 40rpx; }
.pb-42{ padding-bottom: 42rpx; }
.pb-44{ padding-bottom: 44rpx; }
.pb-46{ padding-bottom: 46rpx; }
.pb-48{ padding-bottom: 48rpx; }
.pb-58{ padding-bottom: 58rpx; }
.pb-60{ padding-bottom: 60rpx; }
.pb-62{ padding-bottom: 62rpx; }
.pb-64{ padding-bottom: 64rpx; }
.pb-70{ padding-bottom: 70rpx; }
.pb-72{ padding-bottom: 72rpx; }
.pb-80{ padding-bottom: 80rpx; }
.pb-100{ padding-bottom: 100rpx; }
.pb-102{ padding-bottom: 102rpx; }
.pb-104{ padding-bottom: 104rpx; }
.pb-110{ padding-bottom: 110rpx; }
.pb-116{ padding-bottom: 116rpx; }
.pb-120{ padding-bottom: 120rpx; }
.pb-124{ padding-bottom: 124rpx; }
.pb-128{ padding-bottom: 128rpx; }
.pb-130{ padding-bottom: 130rpx; }
.pb-134{ padding-bottom: 134rpx; }
.pb-138{ padding-bottom: 138rpx; }
.pb-140{ padding-bottom: 140rpx; }
.pb-148{ padding-bottom: 148rpx; }
.pb-154{ padding-bottom: 154rpx; }
.pb-156{ padding-bottom: 156rpx; }
.pb-180{ padding-bottom: 180rpx; }
.pb-188{ padding-bottom: 188rpx; }
.pb-200{ padding-bottom: 200rpx; }
.pb-208{ padding-bottom: 208rpx; }
.pb-228{ padding-bottom: 228rpx; }
.pb-230{ padding-bottom: 230rpx; }
.pb-256{ padding-bottom: 256rpx; }
.pb-270{ padding-bottom: 270rpx; }
.pb-276{ padding-bottom: 276rpx; }

/* 左 */
.pl-04{ padding-left: 4rpx; }
.pl-08{ padding-left: 8rpx; }
.pl-10{ padding-left: 10rpx; }
.pl-12{ padding-left: 12rpx; }
.pl-14{ padding-left: 14rpx; }
.pl-16{ padding-left: 16rpx; }
.pl-18{ padding-left: 18rpx; }
.pl-20{ padding-left: 20rpx; }
.pl-22{ padding-left: 22rpx; }
.pl-24{ padding-left: 24rpx; }
.pl-26{ padding-left: 26rpx; }
.pl-28{ padding-left: 28rpx; }
.pl-30{ padding-left: 30rpx; }
.pl-32{ padding-left: 32rpx; }
.pl-34{ padding-left: 34rpx; }
.pl-36{ padding-left: 36rpx; }
.pl-40{ padding-left: 40rpx; }
.pl-46{ padding-left: 46rpx; }
.pl-48{ padding-left: 48rpx; }
.pl-50{ padding-left: 50rpx; }
.pl-52{ padding-left: 52rpx; }
.pl-54{ padding-left: 54rpx; }
.pl-66{ padding-left: 66rpx; }
.pl-68{ padding-left: 68rpx; }
.pl-70{ padding-left: 70rpx; }
.pl-74{ padding-left: 74rpx; }
.pl-100{ padding-left: 100rpx; }
.pl-106{ padding-left: 106rpx; }
.pl-110{ padding-left: 110rpx; }
.pl-118{ padding-left: 118rpx; }
.pl-130{ padding-left: 130rpx; }
.pl-132{ padding-left: 132rpx; }


/* 字体排布位置 */
.text-center{ text-align: center;}
.text-left{ text-align: left; }
.text-right{ text-align: right; }
.text-justify { text-align: justify; }

/* 文字排列方向 */
.text-v-l{ writing-mode: vertical-lr; }

/* 文字中间划线 */
.text-dec-l-t{ text-decoration: line-through; }

/* 字体大小 */
.font-12{ font-size: 12rpx; }
.font-14{ font-size: 14rpx; }
.font-16{ font-size: 16rpx; }
.font-18{ font-size: 18rpx; }
.font-20{ font-size: 20rpx; }
.font-22{ font-size: 22rpx; }
.font-24{ font-size: 24rpx; }
.font-26{ font-size: 26rpx; }
.font-28{ font-size: 28rpx; }
.font-30{ font-size: 30rpx; }
.font-32{ font-size: 32rpx; }
.font-34{ font-size: 34rpx; }
.font-36{ font-size: 36rpx; }
.font-38{ font-size: 38rpx; }
.font-40{ font-size: 40rpx; }
.font-42{ font-size: 42rpx; }
.font-44{ font-size: 44rpx; }
.font-48{ font-size: 48rpx; }
.font-52{ font-size: 52rpx; }
.font-54{ font-size: 54rpx; }
.font-56{ font-size: 56rpx; }
.font-58{ font-size: 58rpx; }
.font-60{ font-size: 60rpx; }
.font-64{ font-size: 64rpx; }
.font-68{ font-size: 68rpx; }
.font-70{ font-size: 70rpx; }
.font-72{ font-size: 72rpx; }
.font-76{ font-size: 76rpx; }
.font-84{ font-size: 84rpx; }
.font-86{ font-size: 86rpx; }
.font-88{ font-size: 88rpx; }
.font-90{ font-size: 90rpx; }
.font-180{ font-size: 180rpx; }
.font-200{ font-size: 200rpx; }
.font-240{ font-size: 240rpx; }

/* 字体颜色 */
.text-0{ color: #000 }
.text-3{ color: #333; }
.text-4{ color: #444; }
.text-6{ color: #666; }
.text-9{ color: #999; }
.text-transp{ color: transparent; }
.text-ffffff{ color: #FFFFFF; }
.text-979797{ color: #979797; }
.text-2e7bff{ color: #2E7BFF; }
.text-e80404{ color: #E80404; }
.text-bbbbbb{ color: #BBBBBB; }
.text-c31f1e{ color: #C31F1E; }
.text-f24b4b{ color: #F24B4B; }
.text-ff8f09{ color: #FF8F09; }
.text-c6c5c5{ color: #C6C5C5; }
.text-8f4c2a{ color: #8F4C2A; }
.text-ba835e{ color: #BA835E; }
.text-f15d22{ color: #F15D22; }
.text-e81710{ color: #E81710; }
.text-cda241{ color: #CDA241; }
.text-ddba65{ color: #DDBA65; }
.text-ff0013{ color: #FF0013; }
.text-bb6000{ color: #BB6000; }
.text-ba4c00{ color: #BA4C00; }
.text-c55d16{ color: #C55D16; }
.text-bb6d1e{ color: #BB6D1E; }
.text-e7a163{ color: #E7A163; }
.text-cc7a03{ color: #CC7A03; }
.text-ffdeb1{ color: #FFDEB1; }
.text-b46f3a{ color: #B46F3A; }
.text-103d23{ color: #103D23; }
.text-824729{ color: #824729; }
.text-153e68{ color: #153E68; }
.text-422684{ color: #422684; }
.text-be0000{ color: #BE0000; }
.text-7a5400{ color: #7A5400; }
.text-2b8cf7{ color: #2B8CF7; }
.text-ff6f6f{ color: #FF6F6F; }
.text-e04040{ color: #E04040; }
.text-d79c36{ color: #D79C36; }
.text-d2d2d2{ color: #D2D2D2; }
.text-d8d8d8{ color: #D8D8D8; }
.text-f35e56{ color: #F35E56; }
.text-757575{ color: #757575; }
.text-ffb4b4{ color: #FFB4B4; }
.text-a9c9ff{ color: #A9C9FF; }
.text-fe0000{ color: #FE0000; }
.text-e0e0e0{ color: #E0E0E0; }
.text-2d2d2d{ color: #2D2D2D; }
.text-f8a233{ color: #F8A233; }
.text-f4cfd1{ color: #F4CFD1; }
.text-d22628{ color: #D22628; }
.text-a54504{ color: #A54504; }
.text-fbf1ea{ color: #FBF1EA; }
.text-ffd8ab{ color: #FFD8AB; }
.text-fff5e6{ color: #FFF5E6; }
.text-c87b47{ color: #C87B47; }
.text-c1721e{ color: #C1721E; }
.text-ff9127{ color: #FF9127; }
.text-ed2317{ color: #ED2317; }
.text-c9c9c9{ color: #C9C9C9; }
.text-912b00{ color: #912B00; }
.text-e77800{ color: #E77800; }
.text-ffb7b7{ color: #FFB7B7; }
.text-815dda{ color: #815DDA; }
.text-825dda{ color: #825DDA; }
.text-ab8cf4{ color: #AB8CF4; }
.text-f6780e{ color: #F6780E; }
.text-fb933b{ color: #FB933B; }
.text-aaaaaa{ color: #AAAAAA; }
.text-de6a1b{ color: #DE6A1B; }
.text-f48b31{ color: #F48B31; }
.text-d4d4d4{ color: #D4D4D4; }
.text-dddddd{ color: #DDDDDD; }
.text-cfcfcf{ color: #CFCFCF; }
.text-8e591f{ color: #8E591F; }
.text-c87924{ color: #C87924; }
.text-ffc900{ color: #FFC900; }
.text-ffbbbb{ color: #FFBBBB; }
.text-c27807{ color: #C27807; }
.text-ffd6d3{ color: #FFD6D3; }
.text-ffd23e{ color: #FFD23E; }
.text-d2773f{ color: #D2773F; }
.text-e5e5e5{ color: #E5E5E5; }
.text-fb9f22{ color: #FB9F22; }
.text-99845f{ color: #99845F; }
.text-c59053{ color: #C59053; }
.text-c3c3c3{ color: #C3C3C3; }
.text-ca101a{ color: #CA101A; }
.text-d87f19{ color: #D87F19; }
.text-ff253d{ color: #FF253D; }
.text-cb7700{ color: #CB7700; }
.text-734cd2{ color: #734CD2; }
.text-83a6cc{ color: #83A6CC; }
.text-a24700{ color: #a24700; }
.text-d76a00{ color: #D76A00; }
.text-e79a31{ color: #e79a31; }
.text-af7e1f{ color: #AF7E1F; }
.text-e00701{ color: #E00701; }
.text-ffe9cd{ color: #FFE9CD; }
.text-ffe0d0{ color: #ffe0d0; }
.text-f44530{ color: #F44530; }
.text-fe3637{ color: #FE3637; }
.text-954538{ color: #954538; }
.text-fff6e4{ color: #FFF6E4; }
.text-ff4444{ color: #FF4444; }
.text-ff4455{ color: #FF4455; }
.text-b33216{ color: #B33216; }
.text-cc3d2a{ color: #CC3D2A; }
.text-ca4b3d{ color: #CA4B3D; }
.text-9e632a{ color: #9E632A; }
.text-b68757{ color: #B68757; }
.text-d60d0d{ color: #D60D0D; }
.text-cecdcd{ color: #CECDCD; }
.text-f3a549{ color: #f3a549; }
.text-fca72e{ color: #FCA72E; }
.text-ffd7a9{ color: #FFD7A9; }
.text-f9d5ab{ color: #F9D5AB; }
.text-ff9500{ color: #FF9500; }
.text-a7a7a7{ color: #A7A7A7; }
.text-8247e4{ color: #8247E4; }

/* 字体样式 */
.font-wei{ font-weight: bold; }
.font-wei-500{ font-weight: 500; }
.font-wei-450{ font-weight: 450; }
.font-wei-600{ font-weight: 600; }
.font-wei-700{ font-weight: 700; }

/* 行高rpx */
.l-h-16{ line-height: 16rpx; }
.l-h-18{ line-height: 18rpx; }
.l-h-22{ line-height: 22rpx; }
.l-h-26{ line-height: 26rpx; }
.l-h-28{ line-height: 28rpx; }
.l-h-30{ line-height: 30rpx; }
.l-h-32{ line-height: 32rpx; }
.l-h-34{ line-height: 34rpx; }
.l-h-36{ line-height: 36rpx; }
.l-h-38{ line-height: 38rpx; }
.l-h-40{ line-height: 40rpx; }
.l-h-42{ line-height: 42rpx; }
.l-h-44{ line-height: 44rpx; }
.l-h-46{ line-height: 46rpx; }
.l-h-48{ line-height: 48rpx; }
.l-h-50{ line-height: 50rpx; }
.l-h-52{ line-height: 52rpx; }
.l-h-54{ line-height: 54rpx; }
.l-h-56{ line-height: 56rpx; }
.l-h-58{ line-height: 58rpx; }
.l-h-60{ line-height: 60rpx; }
.l-h-62{ line-height: 62rpx; }
.l-h-64{ line-height: 64rpx; }
.l-h-66{ line-height: 66rpx; }
.l-h-72{ line-height: 72rpx; }
.l-h-74{ line-height: 74rpx; }
.l-h-80{ line-height: 80rpx; }
.l-h-84{ line-height: 84rpx; }
.l-h-96{ line-height: 96rpx; }
.l-h-200{ line-height: 200rpx; }
.l-h-230{ line-height: 230rpx; }
.l-h-240{ line-height: 240rpx; }

/* 行高倍数 */
.l-h-mul-10{ line-height: 1; }
.l-h-mul-18{ line-height: 1.8; }

/* 文字溢出样式 */
.t-o-ell{ text-overflow: ellipsis; }

/* 文字是否换行 */
.w-s-now{ white-space: nowrap; }
.w-s-pw{ white-space: pre-wrap; }

/* 英文换行 */
.w-b-b-a{ word-break: break-all; }
.w-b-b-w{ word-break: break-word; }

/* 文字溢出省略行数 */
.text-hidden-1{display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1; overflow: hidden; word-break: break-all;}
.text-hidden-2{display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2; overflow: hidden; word-break: break-word;}
.text-hidden-3{display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 3; overflow: hidden; word-break: break-word;}
.text-hidden-4{display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 4; overflow: hidden; word-break: break-word;}
.text-hidden-5{display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 5; overflow: hidden; word-break: break-word;}

/* 文字溢出隐藏文字 */
/* .text-hidden-text-1 { display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1; overflow: hidden; word-break: break-word; } */

.text-hidden {
	overflow: hidden;
	text-overflow: ellipsis;
    white-space: nowrap;
}

/* 文字间距 */
.l-s-n-30 { letter-spacing: -30rpx; } 
.l-s-n-02 { letter-spacing: -2rpx; } 

/* 文字阴影 */
.t-sh-080000-b33216 { text-shadow: 8rpx 0 0 #B33216; }

/* 弹性盒子横向排列 */
.flex-sb-n { display: flex; justify-content: space-between; align-items: normal; }
.flex-sb-c { display: flex; justify-content: space-between; align-items: center; }
.flex-sb-e { display: flex; justify-content: space-between; align-items: flex-end; }
.flex-sb-s { display: flex; justify-content: space-between; align-items: flex-start; }
.flex-c-s { display: flex; justify-content: center; align-items: flex-start; }
.flex-c-c { display: flex; justify-content: center; align-items: center; }
.flex-s-c { display: flex; justify-content: flex-start; align-items: center; }
.flex-s-e { display: flex; justify-content: flex-start; align-items: flex-end; }
.flex-e-c { display: flex; justify-content: flex-end; align-items: center; }
.flex-c-e { display: flex; justify-content: center; align-items: flex-end; }
.flex-sb-e { display: flex; justify-content: space-between; align-items: flex-end; }

/* 弹性盒子纵向排列(命名方式1) */
.flex-col-sb-s { display: flex; flex-direction: column; justify-content: space-between; align-items: flex-start; }
.flex-col-sb-c { display: flex; flex-direction: column; justify-content: space-between; align-items: center; }
.flex-col-sb-e { display: flex; flex-direction: column; justify-content: space-between; align-items: flex-end; }
.flex-col-c-c { display: flex; flex-direction: column; justify-content: center; align-items: center; }
.flex-col-s-c { display: flex; flex-direction: column; justify-content: flex-start; align-items: center; }
.flex-col-e-c { display: flex; flex-direction: column; justify-content: flex-end; align-items: center; }
.flex-col-c-e { display: flex; flex-direction: column; justify-content: center; align-items: flex-end; }

/* 弹性盒子纵向排列(命名方式2) */
.flex-sb-c-c { display: flex; justify-content: space-between; align-items: center; flex-direction: column; }
.flex-sb-n-c { display: flex; justify-content: space-between; align-items: normal; flex-direction: column; }
.flex-sa-c-c { display: flex; justify-content: space-around; align-items: center; flex-direction: column; }
.flex-sb-e-c { display: flex; justify-content: space-between; align-items: flex-end; flex-direction: column; }
.flex-c-c-c { display: flex; justify-content: center; align-items: center; flex-direction: column; }
.flex-s-c-c { display: flex; justify-content: flex-start; align-items: center; flex-direction: column; }
.flex-c-s-c { display: flex; justify-content: center; align-items: flex-start; flex-direction: column; }
.flex-e-c-c { display: flex; justify-content: flex-end; align-items: center; flex-direction: column; }
.flex-e-n-c { display: flex; justify-content: flex-end; align-items: normal; flex-direction: column; }
.flex-c-e-c { display: flex; justify-content: center; align-items: flex-end; flex-direction: column; }




/* 弹性盒子纵向排列(命名方式1) */
.flex-col-sb-s { display: flex; flex-direction: column; justify-content: space-between; align-items: flex-start; }
.flex-col-sb-c { display: flex; flex-direction: column; justify-content: space-between; align-items: center; }
.flex-col-sb-e { display: flex; flex-direction: column; justify-content: space-between; align-items: flex-end; }
.flex-col-c-c { display: flex; flex-direction: column; justify-content: center; align-items: center; }
.flex-col-s-c { display: flex; flex-direction: column; justify-content: flex-start; align-items: center; }
.flex-col-e-c { display: flex; flex-direction: column; justify-content: flex-end; align-items: center; }
.flex-col-c-e { display: flex; flex-direction: column; justify-content: center; align-items: flex-end; }

/* 弹性盒子纵向排列(命名方式2) */
.flex-sb-c-c { display: flex; justify-content: space-between; align-items: center; flex-direction: column; }
.flex-sb-n-c { display: flex; justify-content: space-between; align-items: normal; flex-direction: column; }
.flex-sa-c-c { display: flex; justify-content: space-around; align-items: center; flex-direction: column; }
.flex-sb-e-c { display: flex; justify-content: space-between; align-items: flex-end; flex-direction: column; }
.flex-c-c-c { display: flex; justify-content: center; align-items: center; flex-direction: column; }
.flex-s-c-c { display: flex; justify-content: flex-start; align-items: center; flex-direction: column; }
.flex-c-s-c { display: flex; justify-content: center; align-items: flex-start; flex-direction: column; }
.flex-e-c-c { display: flex; justify-content: flex-end; align-items: center; flex-direction: column; }
.flex-e-n-c { display: flex; justify-content: flex-end; align-items: normal; flex-direction: column; }
.flex-c-e-c { display: flex; justify-content: center; align-items: flex-end; flex-direction: column; }


.bg-transparent-popup .u-mode-center-box {
	background-color: transparent !important;
}

.overflow-visible-popup .u-mode-center-box {
	overflow: visible !important;
}

.overflow-visible-popup .uni-scroll-view {
	overflow: visible !important;
}
/* 页面公共样式 */
.vh-btn {
	margin: 0;
	padding: 0;
}

/* 置灰 */
.fil-gray100-opa40 {
	 -webkit-filter: grayscale(100%) opacity(40%);
	filter: grayscale(100%) opacity(40%);
}

.fil-gray100-opa100 {
	 -webkit-filter: grayscale(100%) opacity(100%);
	filter: grayscale(100%) opacity(100%);
}

.fil-blur10 { filter: blur(5px); }

/* 去除滚动条 */
::-webkit-scrollbar{ display: none; }

/* 防止图片闪一下 */
image{ will-change: transform; }

/* 过渡 */
.tran-1 { transition: all .8s; }
.tran-2 { transition: all .3s; }


/* 去除按钮边框 */
button::after{ border: none; }

/* 按钮边框 */
button { border-radius: 0; }

/* 定位 */
.p-stic{position: sticky;}
.p-abso{position: absolute;}
.p-fixed{ position: fixed; }
.p-rela{ position: relative; }

/* 定位距离 */

/* rpx */
/* 上rpx */
.top-n-156{ top: -156rpx; }
.top-n-64{ top: -64rpx; }
.top-n-60{ top: -60rpx; }
.top-n-44{ top: -44rpx; }
.top-n-24{ top: -24rpx; }
.top-n-16{ top: -16rpx; }
.top-n-10{ top: -10rpx; }
.top-n-06{ top: -6rpx; }
.top-n-04{ top: -4rpx; }
.top-n-02{ top: -2rpx; }
.top-0{ top: 0; }
.top-01{ top: 0.5px; }
.top-02{ top: 2rpx; }
.top-06{ top: 6rpx; }
.top-08{ top: 8rpx; }
.top-10{ top: 10rpx; }
.top-12{ top: 12rpx; }
.top-14{ top: 14rpx; }
.top-16{ top: 16rpx; }
.top-18{ top: 18rpx; }
.top-20{ top: 20rpx; }
.top-24{ top: 24rpx; }
.top-26{ top: 26rpx; }
.top-30{ top: 30rpx; }
.top-38{ top: 38rpx; }
.top-40{ top: 40rpx; }
.top-42{ top: 42rpx; }
.top-50{ top: 50rpx; }
.top-70{ top: 70rpx; }
.top-76{ top: 76rpx; }
.top-84{ top: 84rpx; }
.top-100{ top: 100rpx; }
.top-110{ top: 110rpx; }
.top-120{ top: 120rpx; }
.top-130{ top: 130rpx; }
.top-200{ top: 200rpx; }
.top-220{ top: 220rpx; }
.top-208{ top: 208rpx; }
.top-296{ top: 296rpx; }
.top-304{ top: 304rpx; }
.top-430{ top: 430rpx; }
.top-1260{ top: 1260rpx; }

/* 右rpx */
.right-n-52{ right: -52rpx; }
.right-n-40{ right: -40rpx; }
.right-n-20{ right: -20rpx; }
.right-n-18{ right: -18rpx; }
.right-n-14{ right: -14rpx; }
.right-n-12{ right: -12rpx; }
.right-n-10{ right: -10rpx; }
.right-n-04{ right: -4rpx; }
.right-n-02{ right: -2rpx; }
.right-0{ right: 0; }
.right-04{ right: 4rpx; }
.right-06{ right: 6rpx; }
.right-10{ right: 10rpx; }
.right-15{ right: 15rpx; }
.right-20{ right: 20rpx; }
.right-22{ right: 22rpx; }
.right-24{ right: 24rpx; }
.right-30{ right: 30rpx; }
.right-32{ right: 32rpx; }
.right-36{ right: 36rpx; }
.right-48{ right: 48rpx; }
.right-52{ right: 52rpx; }
.right-108{ right: 108rpx;}
.right-130{ right: 130rpx; }
.right-140{ right: 140rpx; }

/* 下rpx */
.bottom-n-110{ bottom: -110rpx; }
.bottom-n-104{ bottom: -104rpx; }
.bottom-n-32{ bottom: -32rpx; }
.bottom-n-30{ bottom: -30rpx; }
.bottom-n-24{ bottom: -24rpx; }
.bottom-n-16{ bottom: -16rpx; }
.bottom-n-10{ bottom: -10rpx; }
.bottom-n-08{ bottom: -8rpx; }
.bottom-n-02{ bottom: -2rpx; }
.bottom-0{ bottom: 0; }
.bottom-02{ bottom: 2rpx; }
.bottom-04{ bottom: 4rpx; }
.bottom-06{ bottom: 6rpx; }
.bottom-14{ bottom: 14rpx; }
.bottom-20{ bottom: 20rpx; }
.bottom-28{ bottom: 28rpx; }
.bottom-40{ bottom: 40rpx; }
.bottom-46{ bottom: 46rpx; }
.bottom-54{ bottom: 54rpx; }
.bottom-58{ bottom: 58rpx; }
.bottom-60{ bottom: 60rpx; }
.bottom-64{ bottom: 64rpx; }
.bottom-66{ bottom: 66rpx; }
.bottom-80{ bottom: 80rpx; }
.bottom-82{ bottom: 82rpx; }
.bottom-86{ bottom: 86rpx; }
.bottom-100{ bottom: 100rpx; }
.bottom-104{ bottom: 104rpx; }
.bottom-110{ bottom: 110rpx; }
.bottom-116{ bottom: 116rpx; }
.bottom-120{ bottom: 120rpx; }
.bottom-140{ bottom: 140rpx; }
.bottom-144{ bottom: 144rpx; }
.bottom-148{ bottom: 148rpx; }
.bottom-160{ bottom: 160rpx; }
.bottom-176{ bottom: 176rpx; }
.bottom-222{ bottom: 222rpx; }
.bottom-232{ bottom: 232rpx; }
.bottom-240{ bottom: 240rpx; }
.bottom-296{ bottom: 296rpx; }
.bottom-320{ bottom: 320rpx; }

/* 下（安全区域） */
.bottom-safe-area {
	bottom: 0;
	bottom: constant(safe-area-inset-bottom);  
	bottom: env(safe-area-inset-bottom);  
}

/* 左百分比 */
.left-p-16{ left: 15%; }
.left-p-50{ left: 50%; }
.left-p-80{ left: 80%; }

/* 左rpx */
.left-n-10{ left: -10rpx; }
.left-0{ left: 0; }
.left-12{ left: 12rpx; }
.left-16{ left: 16rpx; }
.left-20{ left: 20rpx; }
.left-24{ left: 24rpx; }
.left-28{ left: 28rpx; }
.left-34{ left: 34rpx; }
.left-36{ left: 36rpx; }
.left-52{ left: 52rpx; }
.left-73{ left: 73rpx; }
.left-94{ left: 94rpx; }
.left-10000{ left: 10000rpx; }


/* 层级 */
.z-n-01{z-index: -1;}
.z-0{z-index: 0;}
.z-01{z-index: 1;}
.z-02{z-index: 2;}
.z-03{z-index: 3;}
.z-04{z-index: 4;}
.z-100{z-index: 100;}
.z-600{z-index: 600;}
.z-978{z-index: 978;}
.z-979{z-index: 979;}
.z-980{z-index: 980;}
.z-997{z-index: 997;}
.z-998{z-index: 998;}
.z-999{z-index: 999;}
.z-1000{z-index: 1000;}
.z-9999{z-index: 9999;}

/* 宽高px */
/* 宽 */
.w-px-300{ width: 300px; }
/* 高 */
.h-px-25{ height: 25px; }
.h-px-40{ height: 40px; }
.h-px-45{ height: 45px; }
.h-px-46{ height: 46px; }
.h-px-48{ height: 48px; }
.h-px-50{ height: 50px; }
.h-px-59{ height: 59px; }
.h-px-84{ height: 84px; }
.h-px-419{ height: 419px; }
.h-px-533{ height: 533px; }
/* 宽高(百分比) */
/* 宽 */
.w-p10{ width: 10%; }
.w-p20{ width: 20%; }
.w-p25{ width: 25%; }
.w-p30{ width: 30%; }
.w-p49{ width: 49%; }
.w-p48{ width: 48%; }
.w-p50{ width: 50%; }
.w-p70{ width: 70%; }
.w-p60{ width: 60%; }
.w-p80{ width: 80%; }
.w-p90{ width: 90%; }
.w-p100{ width: 100%; }

/* 高 */
.h-p100{ height: 100%; }

/* 宽高 */
.wh-p100 { width: 100%; height: 100%; }


/* 宽高(rpx) */
/* 宽 */
.w-01{ width: 0.5px; }
.w-02{ width: 2rpx; }
.w-04{ width: 4rpx; }
.w-06{ width: 6rpx; }
.w-08{ width: 8rpx; }
.w-10{ width: 10rpx; }
.w-12{ width: 12rpx; }
.w-14{ width: 14rpx; }
.w-16{ width: 16rpx; }
.w-18{ width: 18rpx; }
.w-20{ width: 20rpx; }
.w-22{ width: 22rpx; }
.w-24{ width: 24rpx; }
.w-26{ width: 26rpx; }
.w-28{ width: 28rpx; }
.w-30{ width: 30rpx; }
.w-32{ width: 32rpx; }
.w-34{ width: 34rpx; }
.w-36{ width: 36rpx; }
.w-38{ width: 38rpx; }
.w-40{ width: 40rpx; }
.w-42{ width: 42rpx; }
.w-44{ width: 44rpx; }
.w-46{ width: 46rpx; }
.w-48{ width: 48rpx; }
.w-50{ width: 50rpx; }
.w-52{ width: 52rpx; }
.w-54{ width: 54rpx; }
.w-56{ width: 56rpx; }
.w-58{ width: 58rpx; }
.w-60{ width: 60rpx; }
.w-62{ width: 62rpx; }
.w-64{ width: 64rpx; }
.w-65{ width: 65rpx; }
.w-66{ width: 66rpx; }
.w-68{ width: 68rpx; }
.w-70{ width: 70rpx; }
.w-72{ width: 72rpx; }
.w-74{ width: 74rpx; }
.w-76{ width: 76rpx; }
.w-78{ width: 78rpx; }
.w-80{ width: 80rpx; }
.w-82{ width: 82rpx; }
.w-84{ width: 84rpx; }
.w-86{ width: 86rpx; }
.w-88{ width: 88rpx; }
.w-90{ width: 90rpx; }
.w-92{ width: 92rpx; }
.w-94{ width: 94rpx; }
.w-96{ width: 96rpx; }
.w-98{ width: 98rpx; }
.w-100{ width: 100rpx; }
.w-104{ width: 104rpx; }
.w-106{ width: 106rpx; }
.w-108{ width: 108rpx; }
.w-110{ width: 110rpx; }
.w-112{ width: 112rpx; }
.w-114{ width: 114rpx; }
.w-116{ width: 116rpx; }
.w-118{ width: 118rpx; }
.w-120{ width: 120rpx; }
.w-122{ width: 122rpx; }
.w-124{ width: 124rpx; }
.w-126{ width: 126rpx; }
.w-128{ width: 128rpx; }
.w-130{ width: 130rpx; }
.w-132{ width: 132rpx; }
.w-134{ width: 134rpx; }
.w-136{ width: 136rpx; }
.w-138{ width: 138rpx; }
.w-140{ width: 140rpx; }
.w-142{ width: 142rpx; }
.w-144{ width: 144rpx; }
.w-146{ width: 146rpx; }
.w-148{ width: 148rpx; }
.w-150{ width: 150rpx; }
.w-152{ width: 152rpx; }
.w-154{ width: 154rpx; }
.w-156{ width: 156rpx; }
.w-158{ width: 158rpx; }
.w-160{ width: 160rpx; }
.w-164{ width: 164rpx; }
.w-166{ width: 166rpx; }
.w-168{ width: 168rpx; }
.w-170{ width: 170rpx; }
.w-174{ width: 174rpx; }
.w-176{ width: 176rpx; }
.w-178{ width: 178rpx; }
.w-180{ width: 180rpx; }
.w-184{ width: 184rpx; }
.w-186{ width: 186rpx; }
.w-188{ width: 188rpx; }
.w-190{ width: 190rpx; }
.w-192{ width: 192rpx; }
.w-194{ width: 194rpx; }
.w-196{ width: 196rpx; }
.w-198{ width: 198rpx; }
.w-200{ width: 200rpx; }
.w-202{ width: 202rpx; }
.w-204{ width: 204rpx; }
.w-208{ width: 208rpx; }
.w-210{ width: 210rpx; }
.w-214{ width: 214rpx; }
.w-216{ width: 216rpx; }
.w-218{ width: 218rpx; }
.w-220{ width: 220rpx; }
.w-222{ width: 222rpx; }
.w-224{ width: 224rpx; }
.w-228{ width: 228rpx; }
.w-230{ width: 230rpx; }
.w-232{ width: 232rpx; }
.w-234{ width: 234rpx; }
.w-236{ width: 236rpx; }
.w-240{ width: 240rpx; }
.w-244{ width: 244rpx; }
.w-246{ width: 246rpx; }
.w-248{ width: 248rpx; }
.w-250{ width: 250rpx; }
.w-252{ width: 252rpx; }
.w-256{ width: 256rpx; }
.w-260{ width: 260rpx; }
.w-262{ width: 262rpx; }
.w-264{ width: 264rpx; }
.w-266{ width: 266rpx; }
.w-268{ width: 268rpx; }
.w-270{ width: 270rpx; }
.w-274{ width: 274rpx; }
.w-278{ width: 278rpx; }
.w-280{ width: 280rpx; }
.w-282{ width: 282rpx; }
.w-284{ width: 284rpx; }
.w-286{ width: 286rpx; }
.w-288{ width: 288rpx; }
.w-290{ width: 290rpx; }
.w-292{ width: 292rpx; }
.w-294{ width: 294rpx; }
.w-296{ width: 296rpx; }
.w-298{ width: 298rpx; }
.w-300{ width: 300rpx; }
.w-304{ width: 304rpx; }
.w-308{ width: 308rpx; }
.w-310{ width: 310rpx; }
.w-312{ width: 312rpx; }
.w-314{ width: 314rpx; }
.w-316{ width: 316rpx; }
.w-318{ width: 318rpx; }
.w-320{ width: 320rpx; }
.w-324{ width: 324rpx; }
.w-326{ width: 326rpx; }
.w-328{ width: 328rpx; }
.w-330{ width: 330rpx; }
.w-334{ width: 334rpx; }
.w-336{ width: 336rpx; }
.w-340{ width: 340rpx; }
.w-342{ width: 342rpx; }
.w-344{ width: 344rpx; }
.w-346{ width: 346rpx; }
.w-348{ width: 348rpx; }
.w-350{ width: 350rpx; }
.w-352{ width: 352rpx; }
.w-356{ width: 356rpx; }
.w-358{ width: 358rpx; }
.w-366{ width: 366rpx; }
.w-370{ width: 370rpx; }
.w-374{ width: 374rpx; }
.w-382{ width: 382rpx; }
.w-390{ width: 390rpx; }
.w-396{ width: 396rpx; }
.w-400{ width: 400rpx; }
.w-408{ width: 408rpx; }
.w-414{ width: 414rpx; }
.w-426{ width: 426rpx; }
.w-428{ width: 428rpx; }
.w-430{ width: 430rpx; }
.w-432{ width: 432rpx; }
.w-434{ width: 434rpx; }
.w-438{ width: 438rpx; }
.w-440{ width: 440rpx; }
.w-442{ width: 442rpx; }
.w-444{ width: 444rpx; }
.w-450{ width: 450rpx; }
.w-460{ width: 460rpx; }
.w-462{ width: 462rpx; }
.w-466{ width: 466rpx; }
.w-470{ width: 470rpx; }
.w-472{ width: 472rpx; }
.w-476{ width: 476rpx; }
.w-478{ width: 478rpx; }
.w-480{ width: 480rpx; }
.w-486{ width: 486rpx; }
.w-490{ width: 490rpx; }
.w-492{ width: 492rpx; }
.w-500{ width: 500rpx; }
.w-502{ width: 502rpx; }
.w-506{ width: 506rpx; }
.w-510{ width: 510rpx; }
.w-514{ width: 514rpx; }
.w-518{ width: 518rpx; }
.w-520{ width: 520rpx; }
.w-522{ width: 522rpx; }
.w-524{ width: 524rpx; }
.w-526{ width: 526rpx; }
.w-528{ width: 528rpx; }
.w-530{ width: 530rpx; }
.w-540{ width: 540rpx; }
.w-542{ width: 542rpx; }
.w-546{ width: 546rpx; }
.w-550{ width: 550rpx; }
.w-552{ width: 552rpx; }
.w-558{ width: 558rpx; }
.w-560{ width: 560rpx; }
.w-562{ width: 562rpx; }
.w-566{ width: 566rpx; }
.w-570{ width: 570rpx; }
.w-580{ width: 580rpx; }
.w-582{ width: 582rpx; }
.w-584{ width: 584rpx; }
.w-590{ width: 590rpx; }
.w-594{ width: 594rpx; }
.w-598{ width: 598rpx; }
.w-600{ width: 600rpx; }
.w-606{ width: 606rpx; }
.w-610{ width: 610rpx; }
.w-612{ width: 612rpx; }
.w-614{ width: 614rpx; }
.w-616{ width: 616rpx; }
.w-618{ width: 618rpx; }
.w-620{ width: 620rpx; }
.w-622{ width: 622rpx; }
.w-624{ width: 624rpx; }
.w-630{ width: 630rpx; }
.w-634{ width: 634rpx; }
.w-636{ width: 636rpx; }
.w-638{ width: 638rpx; }
.w-640{ width: 640rpx; }
.w-646{ width: 646rpx; }
.w-648{ width: 648rpx; }
.w-654{ width: 654rpx; }
.w-658{ width: 658rpx; }
.w-660{ width: 660rpx; }
.w-662{ width: 662rpx; }
.w-670{ width: 670rpx; }
.w-680{ width: 680rpx; }
.w-684{ width: 684rpx; }
.w-686{ width: 686rpx; }
.w-688{ width: 688rpx; }
.w-694{ width: 694rpx; }
.w-700{ width: 700rpx; }
.w-702{ width: 702rpx; }
.w-706{ width: 706rpx; }
.w-710{ width: 710rpx; }
.w-718{ width: 718rpx; }
.w-726{ width: 726rpx; }
.w-730{ width: 730rpx; }
.w-748{ width: 748rpx; }
.w-750{ width: 750rpx; }

/* 高 */
.h-01{ height: 0.5px; }
.h-02 { height: 2rpx; }
.h-04{ height: 4rpx; }
.h-06{ height: 6rpx; }
.h-08{ height: 8rpx; }
.h-10{ height: 10rpx; }
.h-12{ height: 12rpx; }
.h-14{ height: 14rpx; }
.h-16{ height: 16rpx; }
.h-18{ height: 18rpx; }
.h-20{ height: 20rpx; }
.h-22{ height: 22rpx; }
.h-24{ height: 24rpx; }
.h-26{ height: 26rpx; }
.h-28{ height: 28rpx; }
.h-30{ height: 30rpx; }
.h-32{ height: 32rpx; }
.h-34{ height: 34rpx; }
.h-36{ height: 36rpx; }
.h-38{ height: 38rpx; }
.h-40{ height: 40rpx; }
.h-42{ height: 42rpx; }
.h-44{ height: 44rpx; }
.h-46{ height: 46rpx; }
.h-48{ height: 48rpx; }
.h-50{ height: 50rpx; }
.h-52{ height: 52rpx; }
.h-54{ height: 54rpx; }
.h-56{ height: 56rpx; }
.h-58{ height: 58rpx; }
.h-60{ height: 60rpx; }
.h-62{ height: 62rpx; }
.h-64{ height: 64rpx; }
.h-66{ height: 66rpx; }
.h-68{ height: 68rpx; }
.h-70{ height: 70rpx; }
.h-72{ height: 72rpx; }
.h-74{ height: 74rpx; }
.h-76{ height: 76rpx; }
.h-78{ height: 78rpx; }
.h-80{ height: 80rpx; }
.h-82{ height: 82rpx; }
.h-84{ height: 84rpx; }
.h-86{ height: 86rpx; }
.h-88{ height: 88rpx; }
.h-90{ height: 90rpx; }
.h-92{ height: 92rpx; }
.h-94{ height: 94rpx; }
.h-96{ height: 96rpx; }
.h-98{ height: 98rpx; }
.h-100{ height: 100rpx; }
.h-102{ height: 102rpx; }
.h-104{ height: 104rpx; }
.h-106{ height: 106rpx; }
.h-108{ height: 108rpx; }
.h-110{ height: 110rpx; }
.h-112{ height: 112rpx; }
.h-114{ height: 114rpx; }
.h-116{ height: 116rpx; }
.h-118{ height: 118rpx; }
.h-120{ height: 120rpx; }
.h-122{ height: 122rpx; }
.h-124{ height: 124rpx; }
.h-126{ height: 126rpx; }
.h-128{ height: 128rpx; }
.h-130{ height: 130rpx; }
.h-132{ height: 132rpx; }
.h-134{ height: 134rpx; }
.h-136{ height: 136rpx; }
.h-138{ height: 138rpx; }
.h-140{ height: 140rpx; }
.h-142{ height: 142rpx; }
.h-144{ height: 144rpx; }
.h-146{ height: 146rpx; }
.h-148{ height: 148rpx; }
.h-150{ height: 150rpx; }
.h-152{ height: 152rpx; }
.h-154{ height: 154rpx; }
.h-156{ height: 156rpx; }
.h-158{ height: 158rpx; }
.h-160{ height: 160rpx; }
.h-162{ height: 162rpx; }
.h-164{ height: 164rpx; }
.h-166{ height: 166rpx; }
.h-168{ height: 168rpx; }
.h-172{ height: 172rpx; }
.h-174{ height: 174rpx; }
.h-176{ height: 176rpx; }
.h-178{ height: 178rpx; }
.h-180{ height: 180rpx; }
.h-182{ height: 182rpx; }
.h-184{ height: 184rpx; }
.h-186{ height: 186rpx; }
.h-188{ height: 188rpx; }
.h-190{ height: 190rpx; }
.h-192{ height: 192rpx; }
.h-194{ height: 194rpx; }
.h-196{ height: 196rpx; }
.h-198{ height: 198rpx; }
.h-200{ height: 200rpx; }
.h-202{ height: 202rpx; }
.h-204{ height: 204rpx; }
.h-206{ height: 206rpx; }
.h-210{ height: 210rpx; }
.h-214{ height: 214rpx; }
.h-220{ height: 220rpx; }
.h-222{ height: 222rpx; }
.h-224{ height: 224rpx; }
.h-226{ height: 226rpx; }
.h-228{ height: 228rpx; }
.h-230{ height: 230rpx; }
.h-232{ height: 232rpx; }
.h-236{ height: 236rpx; }
.h-240{ height: 240rpx; }
.h-246{ height: 246rpx; }
.h-250{ height: 250rpx; }
.h-256{ height: 256rpx; }
.h-258{ height: 258rpx; }
.h-260{ height: 260rpx; }
.h-266{ height: 266rpx; }
.h-270{ height: 270rpx; }
.h-272{ height: 272rpx; }
.h-274{ height: 274rpx; }
.h-276{ height: 276rpx; }
.h-286{ height: 286rpx; }
.h-288{ height: 288rpx; }
.h-290{ height: 290rpx; }
.h-292{ height: 292rpx; }
.h-294{ height: 294rpx; }
.h-296{ height: 296rpx; }
.h-300{ height: 300rpx; }
.h-302{ height: 302rpx; }
.h-304{ height: 304rpx; }
.h-306{ height: 306rpx; }
.h-308{ height: 308rpx; }
.h-310{ height: 310rpx; }
.h-312{ height: 312rpx; }
.h-316{ height: 316rpx; }
.h-318{ height: 318rpx; }
.h-320{ height: 320rpx; }
.h-326{ height: 326rpx; }
.h-336{ height: 336rpx; }
.h-338{ height: 338rpx; }
.h-340{ height: 340rpx; }
.h-344{ height: 344rpx; }
.h-350{ height: 350rpx; }
.h-352{ height: 352rpx; }
.h-354{ height: 354rpx; }
.h-356{ height: 356rpx; }
.h-360{ height: 360rpx; }
.h-366{ height: 366rpx; }
.h-374{ height: 374rpx; }
.h-376{ height: 376rpx; }
.h-380{ height: 380rpx; }
.h-386{ height: 386rpx; }
.h-388{ height: 388rpx; }
.h-390{ height: 390rpx; }
.h-394{ height: 394rpx; }
.h-400{ height: 400rpx; }
.h-404{ height: 404rpx; }
.h-406{ height: 406rpx; }
.h-410{ height: 410rpx; }
.h-414{ height: 414rpx; }
.h-416{ height: 416rpx; }
.h-422{ height: 422rpx; }
.h-434{ height: 434rpx; }
.h-440{ height: 440rpx; }
.h-442{ height: 442rpx; }
.h-458{ height: 458rpx; }
.h-460{ height: 460rpx; }
.h-462{ height: 462rpx; }
.h-466{ height: 466rpx; }
.h-484{ height: 484rpx; }
.h-496{ height: 496rpx; }
.h-500{ height: 500rpx; }
.h-508{ height: 508rpx; }
.h-510{ height: 510rpx; }
.h-512{ height: 512rpx; }
.h-516{ height: 516rpx; }
.h-520{ height: 520rpx; }
.h-528{ height: 528rpx; }
.h-530{ height: 530rpx; }
.h-550{ height: 550rpx; }
.h-560{ height: 560rpx; }
.h-568{ height: 568rpx; }
.h-570{ height: 570rpx; }
.h-580{ height: 580rpx; }
.h-582{ height: 582rpx; }
.h-584{ height: 584rpx; }
.h-588{ height: 588rpx; }
.h-592{ height: 592rpx; }
.h-598{ height: 598rpx; }
.h-600{ height: 600rpx; }
.h-612{ height: 612rpx; }
.h-618{ height: 618rpx; }
.h-620{ height: 620rpx; }
.h-634{ height: 634rpx; }
.h-650{ height: 650rpx; }
.h-652{ height: 652rpx; }
.h-658{ height: 658rpx; }
.h-692{ height: 692rpx; }
.h-672{ height: 672rpx; }
.h-692{ height: 692rpx; }
.h-700{ height: 700rpx; }
.h-710{ height: 710rpx; }
.h-730{ height: 730rpx; }
.h-748{ height: 748rpx; }
.h-750{ height: 750rpx; }
.h-752{ height: 752rpx; }
.h-772{ height: 772rpx; }
.h-800{ height: 800rpx; }
.h-810{ height: 810rpx; }
.h-820{ height: 820rpx; }
.h-854{ height: 854rpx; }
.h-878{ height: 878rpx; }
.h-882{ height: 882rpx; }
.h-934{ height: 934rpx; }
.h-988{ height: 988rpx; }
.h-1050{ height: 1050rpx; }
.h-1066{ height: 1066rpx; }
.h-1080{ height: 1080rpx; }
.h-1146{ height: 1146rpx; }
.h-1214{ height: 1214rpx; }
.h-1300{ height: 1300rpx; }
.h-1440{ height: 1440rpx; }
.h-1462{ height: 1462rpx; }
.h-1600{ height: 1600rpx; }
.h-1830{ height: 1830rpx; }

/* 宽高 */
.wh-10 { width: 10rpx; height: 10rpx; }
.wh-14 { width: 14rpx; height: 14rpx; }
.wh-26 { width: 26rpx; height: 26rpx; }
.wh-36 { width: 36rpx; height: 36rpx; }

/* 宽高（auto） */
/* 宽auto */
.w-aut{ width: auto; }

/* 宽 max-content */
.w-max-cont { width: max-content; }

/* 宽高(vw vh */
/* 宽（vw） */

/* 高（vh） */
.h-vh-01{ height: 1vh; }
.h-vh-85{ height: 85vh; }
.h-vh-100{ height: 100vh; }

/* 最小宽高（vw vh） */

/* 最小高度vh */
.h-min-vh-100{ min-height: 100vh; }

/* 最大宽度rpx */
.w-max-120{ max-width: 120rpx;}
.w-max-140{ max-width: 140rpx; }
.w-max-150{ max-width: 150rpx; }
.w-max-170{ max-width: 170rpx; }
.w-max-180{ max-width: 180rpx; }
.w-max-194{ max-width: 194rpx; }
.w-max-210{ max-width: 210rpx; }
.w-max-252{ max-width: 252rpx; }
.w-max-264{ max-width: 264rpx; }
.w-max-290{ max-width: 290rpx; }
.w-max-310{ max-width: 310rpx; }
.w-max-366{ max-width: 366rpx; }
.w-max-430{ max-width: 430rpx; }
.w-max-474{ max-width: 474rpx; }
.w-max-470{ max-width: 470rpx; }
.w-max-488{ max-width: 488rpx; }
.w-max-502{ max-width: 502rpx; }
.w-max-510{ max-width: 510rpx; }
.w-max-520{ max-width: 520rpx; }
.w-max-552{ max-width: 552rpx; }
.w-max-580{ max-width: 580rpx; }
.w-max-590{ max-width: 590rpx; }
.w-max-620{ max-width: 620rpx; }
.w-max-660{ max-width: 660rpx; }

/* 最大高度 */
.h-max-72{ max-height: 72rpx; }
.h-max-80{ max-height: 80rpx; }
.h-max-600{ max-height: 600rpx;}
.h-max-988{ max-height: 988rpx;}
.h-max-1000{ max-height: 1000rpx;}

/* 最小宽度rpx */
.w-min-48{ min-width: 48rpx; }
.w-min-88{ min-width: 88rpx; }
.w-min-96{ min-width: 96rpx; }
.w-min-100{ min-width: 100rpx; }
.w-min-136{ min-width: 136rpx; }
.w-min-152{ min-width: 152rpx;}
.w-min-688{ min-width: 688rpx; }
.w-min-700{ min-width: 700rpx; }

/* 最小高度rpx */
.h-min-40{ min-height: 40rpx; }
.h-min-80{min-height: 80rpx;}
.h-min-64{min-height: 64rpx;}
.h-min-110{ min-height: 110rpx; }
.h-min-160{ min-height: 160rpx; }
.h-min-166{min-height: 166rpx;}
.h-min-176{min-height: 176rpx;}
.h-min-240{min-height: 240rpx;}
.h-min-400{min-height: 400rpx;}
.h-min-500{min-height: 500rpx;}
.h-min-1440{min-height: 1440rpx;}



/* 最大高度rpx */

/* 高安全区域 */
.h-safe-area {
	height: 0;
	height: constant(safe-area-inset-bottom);  
	height: env(safe-area-inset-bottom);  
}


/* 背景 */
/* 背景hex */
.bg-ffffff{ background-color: #ffffff; }
.bg-eeeeee{ background-color: #eeeeee; }
.bg-f2f2f2{ background-color: #F2F2F2; }
.bg-f5f5f5{ background-color: #F5F5F5; }
.bg-f6f6f6{ background-color: #F6F6F6; }
.bg-f7f7f7{ background-color: #F7F7F7; }
.bg-f8f8f8{ background-color: #F8F8F8; }
.bg-f9f9f9{ background-color: #F9F9F9; }
.bg-999999{ background-color: #999999; }
.bg-e2ebfa{ background-color: #E2EBFA; }
.bg-ededed{ background-color: #EDEDED; }
.bg-de1e2f{ background-color: #DE1E2F; }
.bg-e80404{ background-color: #E80404; }
.bg-ff9127{ background-color: #FF9127; }
.bg-f3ca00{ background-color: #F3CA00; }
.bg-f3ca00{ background-color: #47c055; }
.bg-2e7bff{ background-color: #2E7BFF; }
.bg-c31f1e{ background-color: #C31F1E; }
.bg-e72538{ background-color: #E72538; }
.bg-fff3e5{ background-color: #FFF3E5; }
.bg-f1f1f1{ background-color: #F1F1F1; }
.bg-ff8f09{ background-color: #FF8F09; }
.bg-f0efef{ background-color: #F0EFEF; }
.bg-333333{ background-color: #333333; }
.bg-feffff{ background-color: #FEFFFF; }
.bg-cda241{ background-color: #CDA241; }
.bg-ddba65{ background-color: #DDBA65; }
.bg-f67b0e{ background-color: #F67B0E; }
.bg-d8d8d8{ background-color: #D8D8D8; }
.bg-fce4e3{ background-color: #FCE4E3; }
.bg-d2d2d2{ background-color: #D2D2D2; }
.bg-734cd2{ background-color: #734CD2; }
.bg-ed2317{ background-color: #ED2317; }
.bg-ff0013{ background-color: #FF0013; }
.bg-eb0404{ background-color: #EB0404; }
.bg-fffdec{ background-color: #FFFDEC; }
.bg-dddddd{ background-color: #DDDDDD; }
.bg-279385{ background-color: #279385; }
.bg-fce0e0{ background-color: #FCE0E0; }
.bg-f7ec74{ background-color: #F7EC74; }
.bg-fde6c9{ background-color: #FDE6C9; }
.bg-fdc6c9{ background-color: #FDC9C9; }
.bg-e0e1e0{ background-color: #E0E1E0; }
.bg-dedede{ background-color: #DEDEDE; }
.bg-eaf0fb{ background-color: #EAF0FB; }
.bg-ffcdcd{ background-color: #FFCDCD; }
.bg-a9c9ff{ background-color: #A9C9FF; }
.bg-fe0000{ background-color: #FE0000; }
.bg-e0e0e0{ background-color: #E0E0E0; }
.bg-f1f6ff{ background-color: #F1F6FF; }
.bg-fff0f0{ background-color: #FFF0F0; }
.bg-ca101a{ background-color: #CA101A; }
.bg-ffa825{ background-color: #FFA825; }
.bg-fdf3ef{ background-color: #FDF3EF; }
.bg-f8a233{ background-color: #F8A233; }
.bg-d80e0e{ background-color: #D80E0E; }
.bg-e67a36{ background-color: #E67A36; }
.bg-fff1e5{ background-color: #FFF1E5; }
.bg-e78800{ background-color: #E78800; }
.bg-815dda{ background-color: #815DDA; }
.bg-f1edfb{ background-color: #F1EDFB; }
.bg-f67b0e{ background-color: #F6780E; }
.bg-feedde{ background-color: #FEEDDE; }
.bg-aaaaaa{ background-color: #AAAAAA; }
.bg-bfbfbf{background-color: #BFBFBF;}
.bg-fcf8d9{ background-color: #FCF8D9; }
.bg-666666{ background-color: #666666; }
.bg-d80707{ background-color: #D80707; }
.bg-9f9f9f{ background-color: #9F9F9F; }
.bg-f9efee{ background-color: #F9EFEE; }
.bg-e5e5e5{ background-color: #E5E5E5; }
.bg-f9e7cd{ background-color: #FFE7CD; }
.bg-cccccc{ background-color: #CCCCCC; }
.bg-ececec{ background-color: #ECECEC; }
.bg-fde8c7{ background-color: #FDE8C7; }
.bg-f4f4f4{ background-color: #F4F4F4; }
.bg-fafafa{ background-color: #FAFAFA; }
.bg-ffd761{ background-color: #FFD761; }
.bg-ffb874{ background-color: #FFB874; }
.bg-e7e7e7{ background-color: #E7E7E7; }
.bg-ffc8c8{ background-color: #FFC8C8; }
.bg-e1e1e1{ background-color: #E1E1E1; }
.bg-b44b33{ background-color: #B44B33; }
.bg-ffeaea{ background-color: #FFEAEA; }
.bg-fbede1{ background-color: #FBEDE1; }
.bg-ff9300{ background-color: #FF9300; }
.bg-ffe7ec{ background-color: #FFE7CE; }
.bg-ffe5e5{ background-color: #FFE5E5; }
.bg-feefd7{ background-color: #FEEFD7; }
.bg-fdfaea{ background-color: #FDFAEA; }
.bg-f79101{ background-color: #F79101; }
.bg-f6f4ff{ background-color: #F6F4FF; }
.bg-f4f4f6{ background-color: #F4F4F6; }
.bg-fff2f2{ background-color: #FFF2F2; }
.bg-fde8eb{ background-color: #FDE8EB; }
.bg-f56f68{ background-color: #F56F68; }
.bg-e2ebfa{ background-color: #E2EBFA; }

.bg-f5f5f7{ background-color: #F5F5F7; }
.bg-fe3637{ background-color: #FE3637; }
.bg-e5e6e7{ background-color: #E5E6E7; }
.bg-db2523{ background-color: #db2523; }
.bg-e4e4e4{ background-color: #E4E4E4; }
.bg-fff2f2{ background-color: #FFF2F2; }
.bg-ffe3b7{ background-color: #FFE3B7; }
.bg-fff6e4{ background-color: #FFF6E4; }
.bg-ff7878{ background-color: #FF7878; }
.bg-fce4e3{ background-color: #FCE4E3; }
.bg-ffb100{ background-color: #ffb100;}
.bg-da9840{ background-color: #DA9840; }

.bg-ffecd3{ background-color: #FFECD3; }
.bg-1c1c1c{ background-color: #1C1C1C; }
.bg-1e1a16{ background-color: #1E1A16 !important; }
.bg-403021{ background-color: #403021; }
.bg-be6e38{ background-color: #BE6E38; }
.bg-ffd7a9{ background-color: #FFD7A9; }
.bg-393328{ background-color: #393328; }
.bg-ff9500{ background-color: #FF9500; }
.bg-0c0001{ background-color: #0C0001; }
.bg-393228{ background-color: #393228; }
.bg-5a389f{ background-color: #5A389F; }
.bg-1e49e2{ background-color: #1E49E2; }
.bg-c32320{ background-color: #c32320; }
.bg-fff8f5{ background-color: #FFF8F5; }
.bg-bdbdbd{ background-color: #BDBDBD; }
.bg-f44530{ background-color: #F44530; }
.bg-8247e4{ background-color: #8247E4; }

/* 无背景样式 */
.bg-none{ background: none; }

/* 背景（透明） */
.bg-transp{ background-color: transparent; }

/* 背景rgba */
.bg-252-224-224-030{ background-color: rgba(252, 224, 224, .3); }
.bg-255-255-255-059{ background-color: rgba(255, 255, 255, .59); }
.bg-255-255-255-030{ background-color: rgba(255, 255, 255, .3); }
.bg-255-255-255-040{ background-color: rgba(255, 255, 255, .4); }
.bg-231-136-000-040{ background-color: rgba(231, 136, 0, .4); }
.bg-255-255-255-090{ background-color: rgba(255, 255, 255, .9); }
.bg-000-000-000-030{ background-color: rgba(0, 0, 0, .3); }
.bg-000-000-000-060{ background-color: rgba(0, 0, 0, .6); }
.bg-222-222-222-090{ background-color: rgba(222, 222, 222, .9); }
.bg-000-000-000-035{ background-color: rgba(0, 0, 0, .35); }
.bg-000-000-000-038{ background-color: rgba(0, 0, 0, .38); }
.bg-000-000-000-055{ background-color: rgba(0, 0, 0, .55); }
.bg-000-000-000-069{ background-color: rgba(0, 0, 0, .69); }
.bg-000-000-000-080{ background-color: rgba(0, 0, 0, 0.8); }

/* 背景渐变 */
.bg-li-1{ background-image: linear-gradient(214deg, #FF6161 0%, #E70000 100%); }
.bg-li-2{ background-image: linear-gradient(270deg, #FF4955 0%, #D80708 100%); }
.bg-li-3{ background-image: linear-gradient(360deg, #FFF6F6 0%, #FFEEEA 100%); }
.bg-li-4{ background-image: linear-gradient(180deg, #FF4C4C 0%, #CD2020 100%); }
.bg-li-5{ background-image: linear-gradient(180deg, #FFFFFF 0%, #FFCD90 100%); }
.bg-li-6{ background: linear-gradient(180deg, #FFE7D7 0%, #FFE1C3 100%); }
.bg-li-7{ background: linear-gradient(90deg, #FEF6F0 0%, #FFF2E5 100%); }
.bg-li-8{ background: linear-gradient(270deg, #C4ACFF 0%, #744DD3 100%); }
.bg-li-9{ background: linear-gradient(270deg, #F79F1F 0%, #F6780E 100%); }
.bg-li-10{ background: linear-gradient(180deg, #FFE8E8 0%, #FFFFFF 100%); }
.bg-li-11{ background: linear-gradient(180deg, #FFFAFA 0%, #FFF3F3 100%); }
.bg-li-12{ background: linear-gradient(214deg, #FF8383 0%, #E70000 100%); }
.bg-li-13{ background: linear-gradient(180deg, #F8D03F 0%, #FF8F00 100%); }
.bg-li-14{ background: linear-gradient(210deg, #C9C9C9 0%, #999999 100%); }
.bg-li-15{ background: linear-gradient(90deg, #FB9E20 0%, #FFC75C 100%); }
.bg-li-16{ background: linear-gradient(90deg, #A0A0A0 0%, #D6D6D6 100%); }
.bg-li-17{ background: linear-gradient(180deg, #FFFFFF 0%, #F7F7F7 100%); }
.bg-li-18{ background: linear-gradient(180deg, #FFFFFF 0%, #F8F8F8 100%); }
.bg-li-19{ background: linear-gradient(214deg, #FF9144 0%, #F35D00 100%); }
.bg-li-20{ background: linear-gradient(214deg, #5AC2FF 0%, #505CFB 100%); }
.bg-li-21{ background: linear-gradient(218deg, #FF8383 0%, #E70000 100%); }
.bg-li-22{ background: linear-gradient(1deg, rgba(255, 145, 39, .15) 0%, #FFFFFF 100%); }
.bg-li-23{ background: linear-gradient(180deg, #F4EFE1 0%, #FFFFFF 100%); }
.bg-li-24{ background: linear-gradient(180deg, #F7F2EF 0%, #FFFFFF 100%); }
.bg-li-25{ background: linear-gradient(180deg, #F8D03F 0%, #FF8F00 100%); }
.bg-li-26{ background: linear-gradient(224deg, #FF7777 0%, #E80404 100%); }
.bg-li-27{ background: linear-gradient(90deg, #FCDEDE 0%, #E80404 100%); }
.bg-li-28{ background: linear-gradient(180deg, #FDFAE9 0%, #FDFEF5 100%); }
.bg-li-29{ background: linear-gradient(180deg, #EEF4FF 0%, #FBFDFF 100%); }
.bg-li-30{ background: linear-gradient(180deg, #FFEDFD 0%, #FEFBFF 100%); }
.bg-li-31{ background: linear-gradient(90deg, #FCD6E0 0%, #FFEEE4 100%); }
.bg-li-32{ background: linear-gradient(167deg, #FDEAEA 0%, #FFFFFF 100%); }
.bg-li-41{ background: linear-gradient(360deg, #FFEAED 0%, #FFFFFF 100%); }
.bg-li-100{ background: linear-gradient(90deg, #FFE3AA 0%, rgba(255,145,39,0.38) 100%); }
.bg-li-101{ background: linear-gradient(134deg, #FCF1F1 0%, #FCF1F1 100%); }
.bg-li-102{ background: linear-gradient(90deg, #FFF1D4 0%, #FFF5E1 100%); }



/* flex布局 */
.d-flex{ display: flex; }
.d-block{ display: block; }
.d-inline-block{ display: inline-block; }
.flex-1{ flex: 1; }
.flex-column{ flex-direction: column; }
.flex-row{ flex-direction: row; }
.flex-wrap{ flex-wrap: wrap; }
.flex-nowrap{ flex-wrap: nowrap; }
.flex-shrink{flex-shrink: 0;}
.j-start{ justify-content: flex-start; }
.j-center{ justify-content: center; }
.j-end{ justify-content: flex-end; }
.j-sb{ justify-content: space-between; }
.j-sa{ justify-content: space-around; }
.a-center{ align-items:center; }
.a-start{ align-items: flex-start; }
.a-end{ align-items:flex-end; }
.a-stretch{ align-items: stretch; }
.a-baseline{ align-items: baseline; }
.a-self-start{ align-self: flex-start; }
.a-self-auto{ align-self: auto; }
.a-self-end{ align-self: flex-end; }
.a-self-stretch{ align-self:stretch; }
.a-self-baseline{ align-self:baseline; }
/* flex布局常用（横向排列） */
.flex-sb-n { display: flex; justify-content: space-between; align-items: normal; }
.flex-sb-c { display: flex; justify-content: space-between; align-items: center; }
.flex-sa-c { display: flex; justify-content: space-around; align-items: center; }
.flex-sb-e { display: flex; justify-content: space-between; align-items: flex-end; }
.flex-sb-s { display: flex; justify-content: space-between; align-items: flex-start; }
.flex-c-s { display: flex; justify-content: center; align-items: flex-start; }
.flex-c-c { display: flex; justify-content: center; align-items: center; }
.flex-s-c { display: flex; justify-content: flex-start; align-items: center; }
.flex-s-e { display: flex; justify-content: flex-start; align-items: flex-end; }
.flex-e-c { display: flex; justify-content: flex-end; align-items: center; }
.flex-c-e { display: flex; justify-content: center; align-items: flex-end; }
/* flex布局常用（竖排列） */
.flex-sb-c-c { display: flex; justify-content: space-between; align-items: center; flex-direction: column; }
.flex-sb-n-c { display: flex; justify-content: space-between; align-items: normal; flex-direction: column; }
.flex-sa-c-c { display: flex; justify-content: space-around; align-items: center; flex-direction: column; }
.flex-sb-e-c { display: flex; justify-content: space-between; align-items: flex-end; flex-direction: column; }
.flex-c-c-c { display: flex; justify-content: center; align-items: center; flex-direction: column; }
.flex-s-c-c { display: flex; justify-content: flex-start; align-items: center; flex-direction: column; }
.flex-c-s-c { display: flex; justify-content: center; align-items: flex-start; flex-direction: column; }
.flex-e-c-c { display: flex; justify-content: flex-end; align-items: center; flex-direction: column; }
.flex-e-n-c { display: flex; justify-content: flex-end; align-items: normal; flex-direction: column; }
.flex-c-e-c { display: flex; justify-content: center; align-items: flex-end; flex-direction: column; }

/* 弹性盒子纵向排列(命名方式1) */
.flex-col-sb-s { display: flex; flex-direction: column; justify-content: space-between; align-items: flex-start; }
.flex-col-sb-c { display: flex; flex-direction: column; justify-content: space-between; align-items: center; }
.flex-col-sb-e { display: flex; flex-direction: column; justify-content: space-between; align-items: flex-end; }
.flex-col-c-c { display: flex; flex-direction: column; justify-content: center; align-items: center; }
.flex-col-s-c { display: flex; flex-direction: column; justify-content: flex-start; align-items: center; }
.flex-col-e-c { display: flex; flex-direction: column; justify-content: flex-end; align-items: center; }
.flex-col-c-e { display: flex; flex-direction: column; justify-content: center; align-items: flex-end; }

/* 盒模型 */
.bs-bb { box-sizing: border-box; }

/* 裁剪区域 */
.bg-cl-txt{ background-clip: text; -webkit-background-clip: text}

/* 隐藏 */
/* 超出自动 */
.o-aut { overflow: auto; }

/* xy轴超出隐藏 */
.o-hid{ overflow: hidden; }

/* x轴超出滚动 */
.o-scr-x{ overflow-x: scroll; }

/* y轴超出隐藏 */
.o-hid-y{ overflow-y: hidden; }

/* y轴超出滚动 */
.o-scr-y{ overflow-y: scroll;   -webkit-overflow-scrolling: touch; }


/* 圆角百分比 */
.b-rad-p50{ border-radius: 50%; }

/* 圆角rpx */
.b-rad-02{ border-radius: 2rpx; }
.b-rad-04{ border-radius: 4rpx; }
.b-rad-06{ border-radius: 6rpx; }
.b-rad-08{ border-radius: 8rpx; }
.b-rad-10{ border-radius: 10rpx; }
.b-rad-12{ border-radius: 12rpx; }
.b-rad-14{ border-radius: 14rpx; }
.b-rad-15{ border-radius: 15rpx; }
.b-rad-16{ border-radius: 16rpx; }
.b-rad-18{ border-radius: 18rpx; }
.b-rad-20{ border-radius: 20rpx; }
.b-rad-22{ border-radius: 22rpx; }
.b-rad-23{ border-radius: 23rpx; }
.b-rad-24{ border-radius: 24rpx; }
.b-rad-26{ border-radius: 26rpx; }
.b-rad-28{ border-radius: 28rpx; }
.b-rad-30{ border-radius: 30rpx; }
.b-rad-32{ border-radius: 32rpx; }
.b-rad-34{ border-radius: 34rpx; }
.b-rad-36{ border-radius: 36rpx; }
.b-rad-40{ border-radius: 40rpx; }
.b-rad-46{ border-radius: 46rpx; }
.b-rad-60{ border-radius: 60rpx; }
.b-rad-66{ border-radius: 66rpx; }
.b-rad-100{ border-radius: 100rpx; }

/* 上右圆角 */
.b-tr-rad-10{ border-radius: 0 10rpx 0 0;}
.b-tr-rad-20{ border-radius: 0 20rpx 0 0;}
.b-tr-rad-28{ border-radius: 0 28rpx 0 0;}

/* 下右圆角 */
.b-br-rad-10{ border-radius: 0 0 10rpx 0;}
.b-br-rad-28{ border-radius: 0 0 28rpx 0;}

/* 上右、下左圆角（两者圆角相同） */
.b-tr-bl-rad-20{ border-radius: 0 20rpx 0 20rpx;}

/* 上左、下右圆角（两者圆角相同） */
.b-tr-08-bl-08{ border-radius: 8rpx 0 8rpx 0;}
.b-tr-10-bl-10{ border-radius: 10rpx 0 10rpx 0;}
.b-tr-16-bl-16{ border-radius: 16rpx 0 16rpx 0;}

/* 上左、下右圆角（两者圆角不相同） */
.b-tr-20-bl-10{ border-radius: 20rpx 0 10rpx 0;}

/* 上左、上右圆角 */
.b-tl-tr-rad-06{ border-radius: 6rpx 6rpx 0 0;}
.b-tl-tr-rad-10{ border-radius: 10rpx 10rpx 0 0;}
.b-tl-tr-rad-20{ border-radius: 20rpx 20rpx 0 0;}
.b-tl-tr-rad-28{ border-radius: 28rpx 28rpx 0 0;}
.b-tl-tr-rad-80{ border-radius: 80rpx 80rpx 0 0;}

/* 下左、下右圆角 */
.b-bl-br-rad-20{ border-radius: 0 0 20rpx 20rpx;}
.b-bl-br-rad-32{ border-radius: 0 0 32rpx 32rpx;}

/* 上左、上右、下右圆角 */
.b-tl-tr-br-rad-18{ border-radius: 18rpx 18rpx 18rpx 0;}

/* 上右、下右圆角 */
.b-tr-br-rad-04{ border-radius: 0 4rpx 4rpx 0; }
.b-tr-br-rad-06{ border-radius: 0 6rpx 6rpx 0; }
.b-tr-br-rad-10{ border-radius: 0 10rpx 10rpx 0; }

/* 上左、下左圆角 */
.b-tl-bl-rad-10{ border-radius: 10rpx 0 0 10rpx; }
.b-tl-bl-rad-200{ border-radius: 200rpx 0 0 200rpx; }


/* 边框 */
/* 实心边框边框上下左右rpx */
.b-s-01-e7e7e7{ border: 0.5px solid #E7E7E7; }
.b-s-01-e2ebfa{ border: 0.5px solid #E2EBFA; }
.b-s-01-f5f4f4{ border: 0.5px solid #F5F4F4; }
.b-s-01-c31f1e{ border: 0.5px solid #C31F1E; }
.b-s-01-e80404{ border: 0.5px solid #E80404; }
.b-s-01-979797{ border: 0.5px solid #979797; }
.b-s-01-cda241{ border: 0.5px solid #CDA241; }
.b-s-01-ff6f6f{ border: 0.5px solid #FF6F6F; }
.b-s-01-f1a570{ border: 0.5px solid #F1A570; }
.b-s-01-c59053{ border: 0.5px solid #C59053; }
.b-s-01-dddddd{ border: 0.5px solid #DDDDDD; }
.b-s-01-f0f0f0{ border: 0.5px solid #F0F0F0; }
.b-s-01-f8d6db{ border: 0.5px solid #F8D6DB; }
.b-s-01-fde8eb{ border: 0.5px solid #FDE8EB; }
.b-s-01-e80404-r{ border: 1rpx solid #E80404; }
.b-s-01-feb36c{ border: 0.5px solid #FEB36C; }
.b-s-01-c7c7c7{ border: 0.5px solid #C7C7C7; }
.b-s-01-2e7bff{ border: 0.5px solid #2E7BFF; }
.b-s-01-d8d8d8{ border: 1rpx solid #d8d8d8; }
.b-s-01-ffffff{ border: 1rpx solid #fff; }
.b-s-01-ff9127{ border: 1rpx solid #FF9127; }
.b-s-01-ff8585{ border: 1rpx solid #FF8585; }
.b-s-01-666666{ border: 1rpx solid #666; }
.b-s-02-ffffff{ border: 2rpx solid #FFFFFF; }
.b-s-02-ff9127{ border: 2rpx solid #FF9127; }
.b-s-02-666666{ border: 2rpx solid #666666; }
.b-s-02-ececec{ border: 2rpx solid #ECECEC; }
.b-s-02-e80404{ border: 2rpx solid #E80404; }
.b-s-02-999999{ border: 2rpx solid #999999; }
.b-s-02-d8d8d8{ border: 2rpx solid #D8D8D8; }
.b-s-02-f0f0f0{	border: 2rpx solid #F0F0F0; }
.b-s-02-999999{ border: 2rpx solid #999999; }
.b-s-02-979797{ border: 2rpx solid #979797; }
.b-s-02-d8d8d8{ border: 2rpx solid #d8d8d8; }
.b-s-02-f8d6db{	border: 2rpx solid #F8D6DB; }
.b-s-02-fce4e3{ border: 2rpx solid #FCE4E3; }
.b-s-02-ffe7cf{ border: 2rpx solid #FFE7CF; }
.b-s-02-f5f5f5{ border: 2rpx solid #F5F5F5; }
.b-s-02-f44530{ border: 2rpx solid #F44530; }
.b-s-04-ffffff{ border: 4rpx solid #FFFFFF; }
.b-s-01-f8d6db-r{ border: 1rpx solid #F8D6DB; }


/* 实心上边框rpx */
.bt-s-01-eeeeee{border-top: 0.5px solid #EEEEEE;}
.bt-s-01-dedede{border-top: 0.1px solid #DEDEDE;}
.bt-s-01-f8f8f8{border-top: 0.5px solid #F8F8F8;}
.bt-s-02-f8f8f8{border-top: 2rpx solid #F8F8F8;}
.bt-s-02-eeeeee{border-top: 2rpx solid #EEEEEE;}
.bt-s-02-transp{ border-bottom: 2rpx solid transparent; }

/* 实心右边框 */
.br-s-01-dddddd{ border-right: 0.5px solid #DDDDDD; }
.br-s-20-ffffff{ border-right: 20rpx solid #ffffff; }

/* 实心下边框rpx */
.bb-s-01-eeeeee{ border-bottom: 0.5px solid #EEEEEE; }
.bb-s-01-dedede{ border-bottom: 0.5px solid #DEDEDE; }
.bb-s-01-f8f8f8{ border-bottom: 0.5px solid #F8F8F8; }
.bb-s-01-f7f7f7{ border-bottom: 0.5px solid #F7F7F7; }
.bb-s-01-f5f5f5{ border-bottom: 0.5px solid #F5F5F5; }
.bb-s-01-f0f0f0{ border-bottom: 0.5px solid #F0F0F0; }
.bb-s-01-d8d8d8{ border-bottom: 0.5px solid #d8d8d8; }
.bb-s-01-efefeef{ border-bottom: 1rpx solid #EFEFEF; }
.bb-s-02-eeeeee{ border-bottom: 2rpx solid #EEEEEE; }
.bb-s-06-f6f6f6{ border-bottom: 6rpx solid #F6F6F6; }
.bb-s-20-transp{ border-bottom: 20rpx solid transparent; }

/* 实心左边框rpx */
.bl-s-01-eeeeee{ border-left: 0.5px solid #eeeeee; }
.bl-s-01-cccccc{ border-left: 0.5px solid #CCCCCC; }
.bl-s-02-e7e7e7{ border-left: 2rpx solid #E7E7E7;}
.bl-s-02-eeeeee{ border-left: 2rpx solid #EEEEEE;}
.bl-s-20-transp{ border-left: 20rpx solid transparent;}

/* 虚线边框上右下左rpx */
.b-d-01-999999{ border: 0.5px dashed #999999; }
.b-d-01-c6c5c5{ border: 0.5px dashed #C6C5C5; }
.b-d-01-2e7bff{ border: 0.5px dashed #2E7BFF; }
.b-d-01-e80404{ border: 0.5px dashed #E80404; }
.b-d-02-dbdbdb{ border: 2rpx dashed #DBDBDB; }
.b-d-02-d8d8d8{ border: 2rpx dashed #D8D8D8; }

.bt-d-02-d8d8d8{ border-top: 2rpx dashed #d8d8d8; }
/* 虚线右边框rpx */
.br-d-01-e6853f{ border-right: 0.5px dashed #E6853F;}
.br-d-01-ffe5e8{ border-right: 0.5px dashed #FFE5E8;}
.br-d-01-e7e7e7{ border-right: 0.5px dashed #E7E7E7;}

/* 虚线下边框 */
.bb-d-01-eeeeee{ border-bottom: 0.5px dashed #EEEEEE; }

/* 点下边框 */
.bb-dot-02-ff7878{ border-bottom: 2rpx dotted #FF7878; }

/* 阴影 */
.b-sh-00021200-022{ box-shadow: 0 2rpx 12rpx 0 rgba(0,0,0,0.22); }
.b-sh-00001002-007{ box-shadow: 0 0 10rpx 2rpx rgba(0,0,0,0.07); }
.b-sh-00041600-010{ box-shadow: 0 4rpx 16rpx 0 rgba(0,0,0,0.1); }
.b-sh-00041200-013{ box-shadow: 0 4rpx 12rpx 0 rgba(0,0,0,0.13); }
.b-sh-00042604-007{ box-shadow: 0 4rpx 26rpx 4rpx rgba(0,0,0,0.07); }
.b-sh-00061000-007{ box-shadow: 0 6rpx 10rpx 0 rgba(0, 0, 0, 0.07); }
.b-sh-02021200-015{ box-shadow: 2rpx 2rpx 6rpx 0 rgba(0, 0, 0, 0.15); }
.b-sh-00001002-009{ box-shadow: 0 0 10rpx 2rpx rgba(0,0,0,0.09); }
.b-sh-00041400-012{ box-shadow: 0 4rpx 14rpx 0 rgba(0,0,0,0.12); }
.b-sh-00042800-012{ box-shadow: 0 4rpx 28rpx 0 rgba(0, 0, 0, 0.15); }
.b-sh-00041800-009{ box-shadow: 0rpx 4rpx 18rpx 0rpx rgba(0,0,0,0.09); }

/* 旋转 */
/* 旋转逆时针 */
.t-ro-n-45 { transform: rotate(-45deg); }
.t-ro-n-180 { transform: rotate(-180deg); }

/* 旋转顺时针 */
.t-ro-45 { transform: rotate(45deg); }

/* 旋转x */
.t-ro-x-180{ transform: rotateX(180deg); }

/* 旋转y */
.t-ro-y-30{ transform: rotateY(30deg); }
.t-ro-y-180{ transform: rotateY(180deg); }

/* 缩放 */
/* 缩放x */
.t-sc-x-h-1{ transform: scaleX(.5); }

/* 缩放y */
.t-sc-y-h-1{ transform: scaleY(.5); }

/* 3d旋转 */
.t-trans-3d-1 { transform: translate3d(0, 0, 0); }

.t-trans-x-m50 { transform: translateX(-50%); }
.t-trans-x-m100 { transform: translateX(-100%); }
.t-trans-x-m50 { transform: translateX(-50%); }


/* 倾斜 */
.t-sk-x-n-30{ transform: skewX(-30deg); }
.t-sk-x-n-15{ transform: skewX(-15deg); }
.t-sk-x-n-10{ transform: skewX(-10deg); }
.t-sk-x-10{ transform: skewX(10deg); }
.t-sk-x-15{ transform: skewX(15deg); }
.t-sk-x-30{ transform: skewX(30deg); }

.t-trans-x-72 { transform: translateX(72rpx); }
.t-trans-x-m100 { transform: translateX(-100%); }

/* 透明度 */
.op-000{ opacity: 0; }
.op-007{ opacity: .07; }
.op-015{ opacity: .15; }
.op-040{ opacity: .4; }
.op-050{ opacity: .5; }
.op-060{ opacity: .6; }
.op-100{ opacity: 1; }

/* 外边距 */
/* 上(百分比) */
.mt-p20{ margin-top: 20%; }
.mt-p30{ margin-top: 30%; }
.mt-p40{ margin-top: 40%; }
.mt-p50{ margin-top: 50%; }

/* 上下左右（都相同） */
.m-0{ margin: 0; }
.m-24{ margin: 24rpx; }

/* 上右下左（上下、左右相同） */
.mtb-00-mlr-12{ margin: 0 12rpx; }
.mtb-00-mlr-20{ margin: 0 20rpx; }
.mtb-00-mlr-24{ margin: 0 24rpx; }
.mtb-00-mlr-28{ margin: 0 28rpx; }
.mtb-00-mlr-32{ margin: 0 32rpx; }
.mtb-00-mlr-38{ margin: 0 38rpx; }
.mtb-00-mlr-auto{ margin: 0 auto; }
.mtb-16-mlr-12{ margin: 16rpx 12rpx; }
.mtb-20-mlr-00{ margin: 20rpx 0; }
.mtb-20-mlr-24{ margin: 20rpx 24rpx; }
.mtb-00-mlr-44{ margin: 0 44rpx; }
.mtb-24-mlr-auto{ margin: 24rpx auto; }

/* 上(rpx) */
.mt-n-290{ margin-top: -290rpx; }
.mt-n-88{ margin-top: -88rpx; }
.mt-n-80{ margin-top: -80rpx; }
.mt-n-70{ margin-top: -70rpx; }
.mt-n-54{ margin-top: -54rpx; }
.mt-n-48{ margin-top: -48rpx; }
.mt-n-40{ margin-top: -40rpx; }
.mt-n-30{ margin-top: -30rpx; }
.mt-n-28{ margin-top: -28rpx; }
.mt-n-20{ margin-top: -20rpx; }
.mt-n-16{ margin-top: -16rpx; }
.mt-n-14{ margin-top: -14rpx; }
.mt-n-12{ margin-top: -12rpx; }
.mt-n-10{ margin-top: -10rpx; }
.mt-n-08{ margin-top: -8rpx; }
.mt-n-02{ margin-top: -2rpx; }
.mt-02{ margin-top: 2rpx; }
.mt-04{ margin-top: 4rpx; }
.mt-06{ margin-top: 6rpx; }
.mt-08{ margin-top: 8rpx; }
.mt-10{ margin-top: 10rpx; }
.mt-12{ margin-top: 12rpx; }
.mt-14{ margin-top: 14rpx; }
.mt-16{ margin-top: 16rpx; }
.mt-18{ margin-top: 18rpx; }
.mt-20{ margin-top: 20rpx; }
.mt-22{ margin-top: 22rpx; }
.mt-24{ margin-top: 24rpx; }
.mt-26{ margin-top: 26rpx; }
.mt-28{ margin-top: 28rpx; }
.mt-30{ margin-top: 30rpx; }
.mt-32{ margin-top: 32rpx; }
.mt-34{ margin-top: 34rpx; }
.mt-36{ margin-top: 36rpx; }
.mt-38{ margin-top: 38rpx; }
.mt-40{ margin-top: 40rpx; }
.mt-42{ margin-top: 42rpx; }
.mt-44{ margin-top: 44rpx; }
.mt-48{ margin-top: 48rpx; }
.mt-50{ margin-top: 50rpx; }
.mt-52{ margin-top: 52rpx; }
.mt-56{ margin-top: 56rpx; }
.mt-58{ margin-top: 58rpx; }
.mt-60{ margin-top: 60rpx; }
.mt-62{ margin-top: 62rpx; }
.mt-64{ margin-top: 64rpx; }
.mt-68{ margin-top: 68rpx; }
.mt-70{ margin-top: 70rpx; }
.mt-72{ margin-top: 72rpx; }
.mt-74{ margin-top: 74rpx; }
.mt-76{ margin-top: 76rpx; }
.mt-80{ margin-top: 80rpx; }
.mt-84{ margin-top: 84rpx; }
.mt-86{ margin-top: 86rpx; }
.mt-90{ margin-top: 90rpx; }
.mt-92{ margin-top: 92rpx; }
.mt-94{ margin-top: 94rpx; }
.mt-96{ margin-top: 96rpx; }
.mt-100{ margin-top: 100rpx; }
.mt-102{ margin-top: 102rpx; }
.mt-106{ margin-top: 106rpx; }
.mt-108{ margin-top: 108rpx; }
.mt-110{ margin-top: 110rpx; }
.mt-118{ margin-top: 118rpx; }
.mt-120{ margin-top: 120rpx; }
.mt-124{ margin-top: 124rpx; }
.mt-130{ margin-top: 130rpx; }
.mt-136{ margin-top: 136rpx; }
.mt-152{ margin-top: 152rpx; }
.mt-158{ margin-top: 158rpx; }
.mt-160{ margin-top: 160rpx; }
.mt-176{ margin-top: 176rpx; }
.mt-188{ margin-top: 188rpx; }
.mt-196{ margin-top: 196rpx; }
.mt-230{ margin-top: 230rpx; }
.mt-260{ margin-top: 260rpx; }
.mt-304{ margin-top: 304rpx; }
.mt-348{ margin-top: 348rpx; }
.mt-368{ margin-top: 368rpx; }

/* 右 */
.mr-n-20{ margin-right: -20rpx; }
.mr-0{ margin-right: 0; }
.mr-02{ margin-right: 2rpx; }
.mr-04{ margin-right: 4rpx; }
.mr-06{ margin-right: 6rpx; }
.mr-08{ margin-right: 8rpx; }
.mr-10{ margin-right: 10rpx; }
.mr-12{ margin-right: 12rpx; }
.mr-14{ margin-right: 14rpx; }
.mr-16{ margin-right: 16rpx; }
.mr-18{ margin-right: 18rpx; }
.mr-20{ margin-right: 20rpx; }
.mr-22{ margin-right: 22rpx; }
.mr-24{ margin-right: 24rpx; }
.mr-26{ margin-right: 26rpx; }
.mr-28{ margin-right: 28rpx; }
.mr-30{ margin-right: 30rpx; }
.mr-32{ margin-right: 32rpx; }
.mr-34{ margin-right: 34rpx; }
.mr-36{ margin-right: 36rpx; }
.mr-38{ margin-right: 38rpx; }
.mr-40{ margin-right: 40rpx; }
.mr-46{ margin-right: 46rpx; }
.mr-48{ margin-right: 48rpx; }
.mr-50{ margin-right: 50rpx; }
.mr-52{ margin-right: 52rpx; }
.mr-54{ margin-right: 54rpx; }
.mr-56{ margin-right: 56rpx; }
.mr-60{ margin-right: 60rpx; }
.mr-62{ margin-right: 62rpx; }
.mr-72{ margin-right: 72rpx; }
.mr-84{ margin-right: 84rpx; }
.mr-94{ margin-right: 94rpx; }
.mr-100{ margin-right: 100rpx; }
.mr-104{ margin-right: 104rpx; }

/* 下 */
.mb-n-16{ margin-bottom: -16rpx; }
.mb-02{ margin-bottom: 2rpx; }
.mb-04{ margin-bottom: 4rpx; }
.mb-06{ margin-bottom: 6rpx; }
.mb-08{ margin-bottom: 8rpx; }
.mb-10{ margin-bottom: 10rpx; }
.mb-12{ margin-bottom: 12rpx; }
.mb-14{ margin-bottom: 14rpx; }
.mb-16{ margin-bottom: 16rpx; }
.mb-18{ margin-bottom: 18rpx; }
.mb-20{ margin-bottom: 20rpx; }
.mb-24{ margin-bottom: 24rpx; }
.mb-26{ margin-bottom: 26rpx; }
.mb-28{ margin-bottom: 28rpx; }
.mb-30{ margin-bottom: 30rpx; }
.mb-32{ margin-bottom: 32rpx; }
.mb-34{ margin-bottom: 34rpx; }
.mb-36{ margin-bottom: 36rpx; }
.mb-40{ margin-bottom: 40rpx; }
.mb-46{ margin-bottom: 46rpx; }
.mb-62{ margin-bottom: 62rpx; }
.mb-66{ margin-bottom: 66rpx; }
.mb-74{ margin-bottom: 74rpx; }
.mb-76{ margin-bottom: 76rpx; }
.mb-90{ margin-bottom: 90rpx; }
.mb-96{ margin-bottom: 96rpx; }
.mb-100{ margin-bottom: 100rpx; }
.mb-108{ margin-bottom: 108rpx; }
.mb-120{ margin-bottom: 120rpx; }
.mb-124{ margin-bottom: 124rpx; }
.mb-175{ margin-bottom: 175rpx; }

/* 左 */
.ml-n-28{ margin-left: -28rpx;}
.ml-n-24{ margin-left: -24rpx;}
.ml-n-20{ margin-left: -20rpx;}
.ml-n-14{ margin-left: -14rpx;}
.ml-n-10{ margin-left: -10rpx;}
.ml-n-06{ margin-left: -6rpx;}
.ml-02{ margin-left: 2rpx;}
.ml-04{ margin-left: 4rpx;}
.ml-06{ margin-left: 6rpx;}
.ml-08{ margin-left: 8rpx;}
.ml-10{ margin-left: 10rpx; }
.ml-12{ margin-left: 12rpx; }
.ml-14{ margin-left: 14rpx; }
.ml-16{ margin-left: 16rpx; }
.ml-18{ margin-left: 18rpx; }
.ml-20{ margin-left: 20rpx; }
.ml-22{ margin-left: 22rpx; }
.ml-24{ margin-left: 24rpx; }
.ml-26{ margin-left: 26rpx; }
.ml-28{ margin-left: 28rpx; }
.ml-30{ margin-left: 30rpx; }
.ml-32{ margin-left: 32rpx; }
.ml-34{ margin-left: 34rpx; }
.ml-36{ margin-left: 36rpx; }
.ml-38{ margin-left: 38rpx; }
.ml-40{ margin-left: 40rpx; }
.ml-44{ margin-left: 44rpx; }
.ml-46{ margin-left: 46rpx; }
.ml-48{ margin-left: 48rpx; }
.ml-50{ margin-left: 50rpx; }
.ml-52{ margin-left: 52rpx; }
.ml-54{ margin-left: 54rpx; }
.ml-56{ margin-left: 56rpx; }
.ml-58{ margin-left: 58rpx; }
.ml-60{ margin-left: 60rpx; }
.ml-62{ margin-left: 62rpx; }
.ml-64{ margin-left: 64rpx; }
.ml-70{ margin-left: 70rpx; }
.ml-72{ margin-left: 72rpx; }
.ml-74{ margin-left: 74rpx; }
.ml-76{ margin-left: 76rpx; }
.ml-78{ margin-left: 78rpx; }
.ml-80{ margin-left: 80rpx; }
.ml-82{ margin-left: 82rpx; }
.ml-86{ margin-left: 86rpx; }
.ml-90{ margin-left: 90rpx; }
.ml-94{ margin-left: 94rpx; }
.ml-134{ margin-left: 134rpx; }
.ml-100{ margin-left: 100rpx; }
.ml-108{ margin-left: 108rpx; }
.ml-124{ margin-left: 124rpx; }
.ml-200{ margin-left: 200rpx; }
.ml-190{ margin-left: 190rpx; }
/* 列表第一个元素上外边距 */
.mt-nth-child1-00 > view:nth-child(1) { margin-top: 0; }
.mt-nth-child1-24 > view:nth-child(1) { margin-top: 24rpx; }

/* 列表第一个元素左外边距 */
.ml-nth-child1-00 > view:nth-child(1) { margin-left: 0; }
.ml-nth-child1-24 > view:nth-child(1) { margin-left: 24rpx; }
.ml-nth-child1-40 > view:nth-child(1) { margin-left: 40rpx; }

/* 列表最后一个view组件右外边距 */
.mr-last-nth-child1-00 > view:nth-last-child(1) { margin-right: 0; }
.mr-last-nth-child1-24 > view:nth-last-child(1) { margin-right: 24rpx; }
.mr-last-nth-child1-40 > view:nth-last-child(1) { margin-right: 40rpx; }


/* 内边距 */
/* 苹果安全适配区域 */
.p-b-safe-area {
	padding-bottom: 0;  
	padding-bottom: constant(safe-area-inset-bottom);  
	padding-bottom: env(safe-area-inset-bottom);  
} 

/* 上右下左 */
.p-0{ padding: 0; }
.p-02{ padding: 2rpx; }
.p-04{ padding: 4rpx; }
.p-06{ padding: 6rpx; }
.p-08{ padding: 8rpx; }
.p-10{ padding: 10rpx; }
.p-12{ padding: 12rpx; }
.p-16{ padding: 16rpx; }
.p-20{ padding: 20rpx; }
.p-24{ padding: 24rpx; }
.p-26{ padding: 26rpx; }
.p-32{ padding: 32rpx; }
.p-40{ padding: 40rpx; }
.p-60{ padding: 60rpx; }
.p-84{ padding: 84rpx; }

/* 上右下左（上下、左右相同） */
.ptb-00-plr-02{ padding: 0 2rpx; }
.ptb-00-plr-04{ padding: 0 4rpx; }
.ptb-00-plr-06{ padding: 0 6rpx; }
.ptb-00-plr-08{ padding: 0 8rpx; }
.ptb-00-plr-10{ padding: 0 10rpx; }
.ptb-00-plr-12{ padding: 0 12rpx; }
.ptb-00-plr-14{ padding: 0 14rpx; }
.ptb-00-plr-16{ padding: 0 16rpx; }
.ptb-00-plr-20{ padding: 0 20rpx; }
.ptb-00-plr-24{ padding: 0 24rpx; }
.ptb-00-plr-26{ padding: 0 26rpx; }
.ptb-00-plr-28{ padding: 0 28rpx; }
.ptb-00-plr-30{ padding: 0 30rpx; }
.ptb-00-plr-32{ padding: 0 32rpx; }
.ptb-00-plr-34{ padding: 0 34rpx; }
.ptb-00-plr-36{ padding: 0 36rpx; }
.ptb-00-plr-38{ padding: 0 38rpx; }
.ptb-00-plr-40{ padding: 0 40rpx; }
.ptb-00-plr-42{ padding: 0 42rpx; }
.ptb-00-plr-46{ padding: 0 46rpx; }
.ptb-00-plr-48{ padding: 0 48rpx; }
.ptb-00-plr-50{ padding: 0 50rpx; }
.ptb-00-plr-52{ padding: 0 52rpx; }
.ptb-00-plr-56{ padding: 0 56rpx; }
.ptb-00-plr-60{ padding: 0 60rpx; }
.ptb-00-plr-62{ padding: 0 62rpx; }
.ptb-00-plr-70{ padding: 0 70rpx; }
.ptb-00-plr-72{ padding: 0 72rpx; }
.ptb-00-plr-74{ padding: 0 74rpx; }
.ptb-00-plr-76{ padding: 0 76rpx; }
.ptb-00-plr-84{ padding: 0 84rpx; }
.ptb-00-plr-92{ padding: 0 92rpx; }
.ptb-00-plr-98{ padding: 0 98rpx; }
.ptb-01-plr-12{ padding: 1rpx 12rpx; }
.ptb-01-plr-14{ padding: 1rpx 14rpx; }
.ptb-02-plr-04{ padding: 2rpx 4rpx; }
.ptb-02-plr-06{ padding: 2rpx 6rpx; }
.ptb-02-plr-08{ padding: 2rpx 8rpx; }
.ptb-02-plr-10{ padding: 2rpx 10rpx; }
.ptb-02-plr-12{ padding: 2rpx 12rpx; }
.ptb-02-plr-16{ padding: 2rpx 16rpx; }
.ptb-02-plr-20{ padding: 2rpx 20rpx; }
.ptb-02-plr-22{ padding: 2rpx 22rpx; }
.ptb-02-plr-24{ padding: 2rpx 24rpx; }
.ptb-04-plr-00{ padding: 4rpx 0; }
.ptb-04-plr-08{ padding: 4rpx 8rpx; }
.ptb-04-plr-12{ padding: 4rpx 12rpx; }
.ptb-04-plr-16{ padding: 4rpx 16rpx; }
.ptb-04-plr-18{ padding: 4rpx 18rpx; }
.ptb-04-plr-24{ padding: 4rpx 24rpx; }
.ptb-04-plr-32{ padding: 4rpx 32rpx; }
.ptb-04-plr-40{ padding: 4rpx 40rpx; }
.ptb-04-plr-42{ padding: 4rpx 42rpx; }
.ptb-06-plr-00{ padding: 6rpx 0; }
.ptb-06-plr-12{ padding: 6rpx 12rpx; }
.ptb-06-plr-16{ padding: 6rpx 16rpx; }
.ptb-06-plr-20{ padding: 6rpx 20rpx; }
.ptb-06-plr-22{ padding: 6rpx 22rpx; }
.ptb-06-plr-24{ padding: 6rpx 24rpx; }
.ptb-06-plr-34{ padding: 6rpx 34rpx; }
.ptb-06-plr-44{ padding: 6rpx 44rpx; }
.ptb-08-plr-20{ padding: 8rpx 20rpx; }
.ptb-10-plr-14{ padding: 10rpx 14rpx; }
.ptb-10-plr-20{ padding: 10rpx 20rpx; }
.ptb-10-plr-24{ padding: 10rpx 24rpx; }
.ptb-12-plr-24{ padding: 12rpx 24rpx; }
.ptb-14-plr-00{ padding: 14rpx 0; }
.ptb-14-plr-20{ padding: 14rpx 20rpx; }
.ptb-14-plr-24{ padding: 14rpx 24rpx; }
.ptb-16-plr-00{ padding: 16rpx 0; }
.ptb-16-plr-12{ padding: 16rpx 12rpx; }
.ptb-16-plr-24{ padding: 16rpx 24rpx; }
.ptb-16-plr-32{ padding: 16rpx 32rpx; }
.ptb-16-plr-40{ padding: 16rpx 40rpx; }
.ptb-18-plr-16{ padding: 18rpx 16rpx; }
.ptb-18-plr-24{ padding: 18rpx 24rpx; }
.ptb-20-plr-00{ padding: 20rpx 0; }
.ptb-20-plr-16{ padding: 20rpx 16rpx; }
.ptb-20-plr-24{ padding: 20rpx 24rpx; }
.ptb-20-plr-44{ padding: 20rpx 44rpx; }
.ptb-22-plr-24{ padding: 22rpx 24rpx; }
.ptb-24-plr-00{ padding: 24rpx 0; }
.ptb-24-plr-16{ padding: 24rpx 16rpx; }
.ptb-24-plr-20{ padding: 24rpx 20rpx; }
.ptb-24-plr-32{ padding: 24rpx 32rpx; }
.ptb-24-plr-56{ padding: 24rpx 56rpx; }
.ptb-28-plr-00{ padding: 28rpx 0; }
.ptb-28-plr-20{ padding: 28rpx 20rpx; }
.ptb-28-plr-24{ padding: 28rpx 24rpx; }
.ptb-28-plr-32{ padding: 28rpx 32rpx; }
.ptb-30-plr-00{ padding: 30rpx 0; }
.ptb-30-plr-20{ padding: 30rpx 20rpx; }
.ptb-32-plr-00{ padding: 32rpx 0; }
.ptb-30-plr-24{ padding: 30rpx 24rpx; }
.ptb-30-plr-32{ padding: 30rpx 32rpx; }
.ptb-32-plr-24{ padding: 32rpx 24rpx; }
.ptb-32-plr-20{ padding: 32rpx 20rpx; }
.ptb-32-plr-30{ padding: 32rpx 30rpx; }
.ptb-32-plr-44{ padding: 32rpx 44rpx; }
.ptb-36-plr-00{ padding: 36rpx 0; }
.ptb-40-plr-00{ padding: 40rpx 0; }
.ptb-40-plr-24{ padding: 40rpx 24rpx; }
.ptb-40-plr-60{ padding: 40rpx 60rpx; }
.ptb-44-plr-00{ padding: 44rpx 0; }
.ptb-48-plr-00{ padding: 48rpx 0; }
.ptb-48-plr-58{ padding: 48rpx 58rpx; }
.ptb-48-plr-80{ padding: 48rpx 80rpx; }
.ptb-52-plr-00{ padding: 52rpx 0; }
.ptb-56-plr-32{ padding: 56rpx 32rpx; }
.ptb-60-plr-00{ padding: 60rpx 0; }
.ptb-66-plr-00{ padding: 66rpx 0; }

/* 上 */
.pt-00{ padding-top: 0; }
.pt-02{ padding-top: 2rpx; }
.pt-04{ padding-top: 4rpx; }
.pt-06{ padding-top: 6rpx; }
.pt-08{ padding-top: 8rpx; }
.pt-10{ padding-top: 10rpx; }
.pt-12{ padding-top: 12rpx; }
.pt-14{ padding-top: 14rpx; }
.pt-16{ padding-top: 16rpx; }
.pt-18{ padding-top: 18rpx; }
.pt-20{ padding-top: 20rpx; }
.pt-22{ padding-top: 22rpx; }
.pt-24{ padding-top: 24rpx; }
.pt-26{ padding-top: 26rpx; }
.pt-28{ padding-top: 28rpx; }
.pt-30{ padding-top: 30rpx; }
.pt-32{ padding-top: 32rpx; }
.pt-34{ padding-top: 34rpx; }
.pt-36{ padding-top: 36rpx; }
.pt-38{ padding-top: 38rpx; }
.pt-40{ padding-top: 40rpx; }
.pt-42{ padding-top: 42rpx; }
.pt-44{ padding-top: 44rpx; }
.pt-46{ padding-top: 46rpx; }
.pt-48{ padding-top: 48rpx; }
.pt-50{ padding-top: 50rpx; }
.pt-52{ padding-top: 52rpx; }
.pt-60{ padding-top: 60rpx; }
.pt-64{ padding-top: 64rpx; }
.pt-70{ padding-top: 70rpx; }
.pt-80{ padding-top: 80rpx; }
.pt-84{ padding-top: 84rpx; }
.pt-86{ padding-top: 86rpx; }
.pt-88{ padding-top: 88rpx; }
.pt-92{ padding-top: 92rpx; }
.pt-98{ padding-top: 98rpx; }
.pt-100{ padding-top: 100rpx; }
.pt-108{ padding-top: 108rpx; }
.pt-120{ padding-top: 120rpx; }
.pt-140{ padding-top: 140rpx; }
.pt-154{ padding-top: 154rpx; }
.pt-170{ padding-top: 170rpx; }
.pt-198{ padding-top: 198rpx; }
.pt-200{ padding-top: 200rpx; }
.pt-238{ padding-top: 238rpx; }
.pt-256{ padding-top: 256rpx; }
.pt-270{ padding-top: 270rpx; }
.pt-230{ padding-top: 230rpx; }
.pt-468{ padding-top: 468rpx; }
.pt-530{ padding-top: 530rpx; }

/* 右 */
.pr-04{ padding-right: 4rpx; } 
.pr-06{ padding-right: 6rpx; }
.pr-08{ padding-right: 8rpx; }
.pr-10{ padding-right: 10rpx; }
.pr-12{ padding-right: 12rpx; }
.pr-14{ padding-right: 14rpx; }
.pr-16{ padding-right: 16rpx; }
.pr-18{ padding-right: 18rpx; }
.pr-20{ padding-right: 20rpx; }
.pr-22{ padding-right: 22rpx; }
.pr-24{ padding-right: 24rpx; }
.pr-26{ padding-right: 26rpx; }
.pr-28{ padding-right: 28rpx; }
.pr-30{ padding-right: 30rpx; }
.pr-32{ padding-right: 32rpx; }
.pr-34{ padding-right: 34rpx; }
.pr-36{ padding-right: 36rpx; }
.pr-40{ padding-right: 40rpx; }
.pr-44{ padding-right: 44rpx; }
.pr-46{ padding-right: 46rpx; }
.pr-48{ padding-right: 48rpx; }
.pr-52{ padding-right: 52rpx; }
.pr-68{ padding-right: 68rpx; }
.pr-72{ padding-right: 72rpx; }
.pr-80{ padding-right: 80rpx; }
.pr-84{ padding-right: 84rpx; }

/* 下 */
.pb-0{ padding-bottom: 0; }
.pb-04{ padding-bottom: 4rpx; }
.pb-06{ padding-bottom: 6rpx; }
.pb-08{ padding-bottom: 8rpx; }
.pb-10{ padding-bottom: 10rpx; }
.pb-12{ padding-bottom: 12rpx; }
.pb-14{ padding-bottom: 14rpx; }
.pb-16{ padding-bottom: 16rpx; }
.pb-18{ padding-bottom: 18rpx; }
.pb-20{ padding-bottom: 20rpx; }
.pb-22{ padding-bottom: 22rpx; }
.pb-24{ padding-bottom: 24rpx; }
.pb-26{ padding-bottom: 26rpx; }
.pb-28{ padding-bottom: 28rpx; }
.pb-30{ padding-bottom: 30rpx; }
.pb-32{ padding-bottom: 32rpx; }
.pb-34{ padding-bottom: 34rpx; }
.pb-36{ padding-bottom: 36rpx; }
.pb-38{ padding-bottom: 38rpx; }
.pb-40{ padding-bottom: 40rpx; }
.pb-42{ padding-bottom: 42rpx; }
.pb-44{ padding-bottom: 44rpx; }
.pb-46{ padding-bottom: 46rpx; }
.pb-48{ padding-bottom: 48rpx; }
.pb-58{ padding-bottom: 58rpx; }
.pb-60{ padding-bottom: 60rpx; }
.pb-62{ padding-bottom: 62rpx; }
.pb-64{ padding-bottom: 64rpx; }
.pb-70{ padding-bottom: 70rpx; }
.pb-72{ padding-bottom: 72rpx; }
.pb-80{ padding-bottom: 80rpx; }
.pb-100{ padding-bottom: 100rpx; }
.pb-102{ padding-bottom: 102rpx; }
.pb-104{ padding-bottom: 104rpx; }
.pb-110{ padding-bottom: 110rpx; }
.pb-120{ padding-bottom: 120rpx; }
.pb-124{ padding-bottom: 124rpx; }
.pb-128{ padding-bottom: 128rpx; }
.pb-130{ padding-bottom: 130rpx; }
.pb-134{ padding-bottom: 134rpx; }
.pb-138{ padding-bottom: 138rpx; }
.pb-140{ padding-bottom: 140rpx; }
.pb-148{ padding-bottom: 148rpx; }
.pb-154{ padding-bottom: 154rpx; }
.pb-156{ padding-bottom: 156rpx; }
.pb-180{ padding-bottom: 180rpx; }
.pb-200{ padding-bottom: 200rpx; }
.pb-208{ padding-bottom: 208rpx; }
.pb-228{ padding-bottom: 228rpx; }
.pb-230{ padding-bottom: 230rpx; }
.pb-256{ padding-bottom: 256rpx; }
.pb-270{ padding-bottom: 270rpx; }
.pb-276{ padding-bottom: 276rpx; }
.pb-360{ padding-bottom: 360rpx; }

/* 左 */
.pl-04{ padding-left: 4rpx; }
.pl-08{ padding-left: 8rpx; }
.pl-10{ padding-left: 10rpx; }
.pl-12{ padding-left: 12rpx; }
.pl-14{ padding-left: 14rpx; }
.pl-16{ padding-left: 16rpx; }
.pl-18{ padding-left: 18rpx; }
.pl-20{ padding-left: 20rpx; }
.pl-22{ padding-left: 22rpx; }
.pl-24{ padding-left: 24rpx; }
.pl-26{ padding-left: 26rpx; }
.pl-28{ padding-left: 28rpx; }
.pl-30{ padding-left: 30rpx; }
.pl-32{ padding-left: 32rpx; }
.pl-34{ padding-left: 34rpx; }
.pl-36{ padding-left: 36rpx; }
.pl-40{ padding-left: 40rpx; }
.pl-46{ padding-left: 46rpx; }
.pl-48{ padding-left: 48rpx; }
.pl-50{ padding-left: 50rpx; }
.pl-52{ padding-left: 52rpx; }
.pl-54{ padding-left: 54rpx; }
.pl-66{ padding-left: 66rpx; }
.pl-68{ padding-left: 68rpx; }
.pl-70{ padding-left: 70rpx; }
.pl-74{ padding-left: 74rpx; }
.pl-100{ padding-left: 100rpx; }
.pl-106{ padding-left: 106rpx; }
.pl-110{ padding-left: 110rpx; }
.pl-115{ padding-left: 115rpx; }
.pl-118{ padding-left: 118rpx; }
.pb-116{ padding-bottom: 116rpx; }
.pl-130{ padding-left: 130rpx; }
.pl-132{ padding-left: 132rpx; }


/* 字体排布位置（水平） */
.text-center{ text-align: center;}
.text-left{ text-align: left; }
.text-right{ text-align: right; }

/* 文字排布位置（垂直） */
.v-a-m { vertical-align: middle  }

/* 文字排列方向 */
.text-v-l{ writing-mode: vertical-lr; }

/* 文字中间划线 */
.text-dec-l-t{ text-decoration: line-through; }
.text-dec-u{ text-decoration: underline; }

/* 文字类型 */
.text-style-italic { font-style: italic; }

/* 字体大小 */
.font-12{ font-size: 12rpx; }
.font-14{ font-size: 14rpx; }
.font-16{ font-size: 16rpx; }
.font-18{ font-size: 18rpx; }
.font-20{ font-size: 20rpx; }
.font-22{ font-size: 22rpx; }
.font-24{ font-size: 24rpx; }
.font-26{ font-size: 26rpx; }
.font-28{ font-size: 28rpx; }
.font-30{ font-size: 30rpx; }
.font-32{ font-size: 32rpx; }
.font-34{ font-size: 34rpx; }
.font-36{ font-size: 36rpx; }
.font-38{ font-size: 38rpx; }
.font-40{ font-size: 40rpx; }
.font-42{ font-size: 42rpx; }
.font-44{ font-size: 44rpx; }
.font-48{ font-size: 48rpx; }
.font-52{ font-size: 52rpx; }
.font-54{ font-size: 54rpx; }
.font-56{ font-size: 56rpx; }
.font-58{ font-size: 58rpx; }
.font-60{ font-size: 60rpx; }
.font-64{ font-size: 64rpx; }
.font-68{ font-size: 68rpx; }
.font-70{ font-size: 70rpx; }
.font-72{ font-size: 72rpx; }
.font-76{ font-size: 76rpx; }
.font-84{ font-size: 84rpx; }
.font-86{ font-size: 86rpx; }
.font-88{ font-size: 88rpx; }
.font-90{ font-size: 90rpx; }
.font-180{ font-size: 180rpx; }
.font-200{ font-size: 200rpx; }
.font-240{ font-size: 240rpx; }

/* 字体颜色 */
.text-0{ color: #000 }
.text-3{ color: #333; }
.text-4{ color: #444; }
.text-6{ color: #666; }
.text-9{ color: #999; }
.text-5D5D5D{ color:#5D5D5D}
.text-transp{ color: transparent; }
.text-ffffff{ color: #FFFFFF; }
.text-979797{ color: #979797; }
.text-2e7bff{ color: #2E7BFF; }
.text-e80404{ color: #E80404; }
.text-bbbbbb{ color: #BBBBBB; }
.text-c31f1e{ color: #C31F1E; }
.text-f24b4b{ color: #F24B4B; }
.text-ff8f09{ color: #FF8F09; }
.text-c6c5c5{ color: #C6C5C5; }
.text-8f4c2a{ color: #8F4C2A; }
.text-ba835e{ color: #BA835E; }
.text-f15d22{ color: #F15D22; }
.text-e81710{ color: #E81710; }
.text-cda241{ color: #CDA241; }
.text-ddba65{ color: #DDBA65; }
.text-ff0013{ color: #FF0013; }
.text-bb6000{ color: #BB6000; }
.text-ba4c00{ color: #BA4C00; }
.text-c55d16{ color: #C55D16; }
.text-bb6d1e{ color: #BB6D1E; }
.text-e7a163{ color: #E7A163; }
.text-cc7a03{ color: #CC7A03; }
.text-ffdeb1{ color: #FFDEB1; }
.text-b46f3a{ color: #B46F3A; }
.text-103d23{ color: #103D23; }
.text-824729{ color: #824729; }
.text-153e68{ color: #153E68; }
.text-422684{ color: #422684; }
.text-be0000{ color: #BE0000; }
.text-7a5400{ color: #7A5400; }
.text-2b8cf7{ color: #2B8CF7; }
.text-ff6f6f{ color: #FF6F6F; }
.text-e04040{ color: #E04040; }
.text-d79c36{ color: #D79C36; }
.text-d2d2d2{ color: #D2D2D2; }
.text-d8d8d8{ color: #D8D8D8; }
.text-f35e56{ color: #F35E56; }
.text-757575{ color: #757575; }
.text-ffb4b4{ color: #FFB4B4; }
.text-a9c9ff{ color: #A9C9FF; }
.text-fe0000{ color: #FE0000; }
.text-e0e0e0{ color: #E0E0E0; }
.text-2d2d2d{ color: #2D2D2D; }
.text-f8a233{ color: #F8A233; }
.text-f4cfd1{ color: #F4CFD1; }
.text-d22628{ color: #D22628; }
.text-a54504{ color: #A54504; }
.text-fbf1ea{ color: #FBF1EA; }
.text-ffd8ab{ color: #FFD8AB; }
.text-fff5e6{ color: #FFF5E6; }
.text-c87b47{ color: #C87B47; }
.text-c1721e{ color: #C1721E; }
.text-ff9127{ color: #FF9127; }
.text-ed2317{ color: #ED2317; }
.text-c9c9c9{ color: #C9C9C9; }
.text-912b00{ color: #912B00; }
.text-e77800{ color: #E77800; }
.text-ffb7b7{ color: #FFB7B7; }
.text-815dda{ color: #815DDA; }
.text-825dda{ color: #825DDA; }
.text-ab8cf4{ color: #AB8CF4; }
.text-f6780e{ color: #F6780E; }
.text-fb933b{ color: #FB933B; }
.text-aaaaaa{ color: #AAAAAA; }
.text-de6a1b{ color: #DE6A1B; }
.text-f48b31{ color: #F48B31; }
.text-d4d4d4{ color: #D4D4D4; }
.text-dddddd{ color: #DDDDDD; }
.text-cfcfcf{ color: #CFCFCF; }
.text-8e591f{ color: #8E591F; }
.text-c87924{ color: #C87924; }
.text-ffc900{ color: #FFC900; }
.text-ffbbbb{ color: #FFBBBB; }
.text-c27807{ color: #C27807; }
.text-ffd6d3{ color: #FFD6D3; }
.text-ffd23e{ color: #FFD23E; }
.text-d2773f{ color: #D2773F; }
.text-e5e5e5{ color: #E5E5E5; }
.text-fb9f22{ color: #FB9F22; }
.text-99845f{ color: #99845F; }
.text-c59053{ color: #C59053; }
.text-c3c3c3{ color: #C3C3C3; }
.text-ca101a{ color: #CA101A; }
.text-d87f19{ color: #D87F19; }
.text-ff253d{ color: #FF253D; }
.text-cb7700{ color: #CB7700; }
.text-734cd2{ color: #734CD2; }
.text-83a6cc{ color: #83A6CC; }
.text-e00701{ color: #E00701; }
.text-ffe9cd{ color: #FFE9CD; }
.text-ffe0d0{ color: #ffe0d0; }
.text-f44530{ color: #F44530; }
.text-fe3637{ color: #FE3637; }
.text-954538{ color: #954538; }
.text-999999{ color: #999999; }
.text-e79a31{ color: #E79A31; }
.text-ff4444{ color: #FF4444; }
.text-cc3d2a{ color: #CC3D2A; }
.text-ca4b3d{ color: #CA4B3D; }
.text-fca72e{ color: #FCA72E; }
.text-9e632a{ color: #9E632A; }
.text-f3a549{ color: #f3a549; }
.text-b68757{ color: #B68757; }
.text-d76a00{ color: #D76A00; }
.text-d60d0d{ color: #D60D0D; }
.text-ffd7a9{ color: #FFD7A9; }
.text-f9d5ab{ color: #F9D5AB; }
.text-ff9500{ color: #FF9500; }
.text-a7a7a7{ color: #A7A7A7; }
.text-cecdcd{ color: #CECDCD; }
.text-8247e4{ color: #8247e4; }

/* 字体样式 */
.font-wei{ font-weight: bold; }
.font-wei-500{ font-weight: 500; }
.font-wei-450{ font-weight: 450; }
.font-wei-600{ font-weight: 600; }
.font-wei-700{ font-weight: 700; }

/* 行高rpx */
.l-h-18{ line-height: 18rpx; }
.l-h-22{ line-height: 22rpx; }
.l-h-26{ line-height: 26rpx; }
.l-h-28{ line-height: 28rpx; }
.l-h-30{ line-height: 30rpx; }
.l-h-32{ line-height: 32rpx; }
.l-h-34{ line-height: 34rpx; }
.l-h-36{ line-height: 36rpx; }
.l-h-38{ line-height: 38rpx; }
.l-h-40{ line-height: 40rpx; }
.l-h-42{ line-height: 42rpx; }
.l-h-44{ line-height: 44rpx; }
.l-h-46{ line-height: 46rpx; }
.l-h-48{ line-height: 48rpx; }
.l-h-50{ line-height: 50rpx; }
.l-h-52{ line-height: 52rpx; }
.l-h-54{ line-height: 54rpx; }
.l-h-56{ line-height: 56rpx; }
.l-h-60{ line-height: 60rpx; }
.l-h-64{ line-height: 64rpx; }
.l-h-66{ line-height: 66rpx; }
.l-h-72{ line-height: 72rpx; }
.l-h-80{ line-height: 80rpx; }
.l-h-84{ line-height: 84rpx; }
.l-h-96{ line-height: 96rpx; }
.l-h-240{ line-height: 240rpx; }

/* 行高倍数 */
.l-h-mul-10{ line-height: 1; }
.l-h-mul-18{ line-height: 1.8; }

/* 文字溢出样式 */
.t-o-ell{ text-overflow: ellipsis; }

/* 文字是否换行 */
.w-s-now{ white-space: nowrap; }
.w-s-pw{ white-space: pre-wrap; }

/* 英文换行 */
.w-b-b-a{ word-break: break-all; }
.w-b-b-w{ word-break: break-word; }

/* 文字溢出省略 */
/* 方法1（默认以单词分割） */
.text-hidden-1{display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1; overflow: hidden; word-break: break-word;}
.text-hidden-2{display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 2; overflow: hidden; word-break: break-word;}
.text-hidden-3{display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 3; overflow: hidden; word-break: break-word;}
.text-hidden-4{display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 4; overflow: hidden; word-break: break-word;}
.text-hidden-5{display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 5; overflow: hidden; word-break: break-word;}

/* 方法2（默认不截断单词） */
.text-hidden-break-all-1 { display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1; overflow: hidden; word-break: break-all; }

.text-hidden {
	overflow: hidden;
	text-overflow: ellipsis;
  white-space: nowrap;
}

.flex-sb-c {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.flex-c-c {
	display: flex;
	justify-content: center;
	align-items: center;
}

.bg-transparent-popup .u-mode-center-box {
	background-color: transparent !important;
}

.overflow-visible-popup .u-mode-center-box {
	overflow: visible !important;
}

.overflow-visible-popup .uni-scroll-view {
	overflow: visible !important;
}

/* 文字间距 */
.l-s-n-30 { letter-spacing: -30rpx; } 
.l-s-n-2 { letter-spacing: -2rpx; } 

/* 文字阴影 */
.t-sh-080000-b33216 { text-shadow: 8rpx 0 0 #B33216; }

