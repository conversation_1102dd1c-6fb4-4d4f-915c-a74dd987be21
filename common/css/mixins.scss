@mixin globalFontFamily() {
	font-family: OpenSans, apple-system, BlinkMacSystemFont, "Helvetica Neue",
			Helvetica, Segoe UI, Arial, Roboto, "PingFang SC", "Hiragino Sans GB",
			"Microsoft Yahei", sans-serif;
}

@mixin font($weight: 400, $size: 0.12rem, $color: #000) {
	@include globalFontFamily();
	font-weight: $weight;
	font-size: $size;
	color: $color;
}

@mixin flex-row($j: center, $a: center) {
	display: flex;
	justify-content: $j;
	align-items: $a;
}

@mixin flex-col($j: center, $a: center) {
	@include flex-row($j, $a);
	flex-direction: column;
}

@mixin size($w: null, $h: $w) {
  width: $w;
  height: $h;
}

@mixin line($line: 1) {
  overflow: hidden;
  text-overflow: ellipsis;

  @if ($line<=1) {
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $line;
    -webkit-box-orient: vertical;
  }
}
